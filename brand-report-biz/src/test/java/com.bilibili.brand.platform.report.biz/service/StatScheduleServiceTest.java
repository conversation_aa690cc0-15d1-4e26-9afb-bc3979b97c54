package com.bilibili.brand.platform.report.biz.service;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.platform.report.api.dto.StatScheduleDto;
import com.bilibili.brand.platform.report.biz.AbstractMockitoTest;
import com.bilibili.brand.platform.charging.dao.AdStatScheduleDayDao;
import com.bilibili.brand.platform.charging.po.AdStatScheduleDayPo;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * StatScheduleService Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>���� 14, 2019</pre>
 */
public class StatScheduleServiceTest extends AbstractMockitoTest {

    @Mock
    private AdStatScheduleDayDao adStatScheduleDayDao;
    @InjectMocks
    private StatScheduleService statScheduleService;

    @Before
    public void before() throws Exception {
        when(adStatScheduleDayDao.selectByExample(any())).thenReturn(Collections.singletonList(BeanTestUtils.generateInstance(AdStatScheduleDayPo.class)));
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType)
     */
    @Test
    public void testGetByAccountIdGroupByTime() throws Exception {
        List<StatScheduleDto> result = statScheduleService.getByAccountIdGroupByTime(1, Utils.getToday(), Utils.getToday(), SalesType.GD.getCode());
        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getAccountId());
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getShowCount());
    }

    /**
     * Method: getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType)
     */
    @Test
    public void testGetByAccountIdGroupByDay() throws Exception {
        List<StatScheduleDto> result = statScheduleService.getByAccountIdGroupByDay(1, Utils.getToday(), Utils.getToday(), SalesType.GD.getCode());
        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getAccountId());
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getShowCount());
    }

    /**
     * Method: getInScheduleIdsGroupByTime(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime)
     */
    @Test
    public void testGetInScheduleIdsGroupByTime() throws Exception {
        List<StatScheduleDto> result = statScheduleService.getInScheduleIdsGroupByTime(Arrays.asList(1), Utils.getToday(), Utils.getToday());
        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getAccountId());
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getShowCount());
    }

    /**
     * Method: getMapInScheduleIdsGroupByTime(List<Integer> scheduleIds)
     */
    @Test
    public void testGetMapInScheduleIdsGroupByTime() throws Exception {
        Map<Integer, StatScheduleDto> result = statScheduleService.getMapInScheduleIdsGroupByTime(Arrays.asList(1));
        Assert.assertNotNull(result);
        this.print(result);
        Assert.assertEquals(Collections.singleton(1), result.keySet());
    }

    /**
     * Method: getMapInScheduleIdsGroupByDay(List<Integer> scheduleIds)
     */
    @Test
    public void testGetMapInScheduleIdsGroupByDay() throws Exception {
        Map<Integer, List<StatScheduleDto>> result = statScheduleService.getMapInScheduleIdsGroupByDay(Arrays.asList(1));
        Assert.assertNotNull(result);
        this.print(result);
        Assert.assertEquals(Collections.singleton(1), result.keySet());
    }

    /**
     * Method: getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType)
     */
    @Test
    public void testGetByAccountIdGroupByMonth() throws Exception {
        List<StatScheduleDto> result = statScheduleService.getByAccountIdGroupByMonth(1, Utils.getToday(), Utils.getToday(), SalesType.GD.getCode());
        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getAccountId());
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getShowCount());
    }

    /**
     * Method: getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType)
     */
    @Test
    public void testGetByAccountIdGroupByWeek() throws Exception {
        List<StatScheduleDto> result = statScheduleService.getByAccountIdGroupByWeek(1, Utils.getToday(), Utils.getToday(), SalesType.GD.getCode());
        Assert.assertNotNull(result);
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getAccountId());
        Assert.assertEquals(Integer.valueOf(1), result.get(0).getShowCount());
    }

    /**
     * Method: getGroupByKey(StatPo statPojo)
     */
    @Test
    public void testGetGroupByKeyStatPojo() throws Exception {
        //TODO: Test goes here...
    }

} 
