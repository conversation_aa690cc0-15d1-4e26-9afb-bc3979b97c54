package com.bilibili.brand.platform.report.biz.service;

import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.platform.report.biz.AbstractMockitoTest;
import com.bilibili.cpt.platform.report.biz.dao.ReportDayCptCreativeDao;
import com.bilibili.cpt.platform.report.biz.po.ReportDayCptCreativePo;
import com.bilibili.cpt.platform.report.biz.service.CptReportDayService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Collections;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.when;

/**
 * CptReportDayService Tester.
 *
 * <AUTHOR> name>
 * @version 1.0
 * @since <pre>���� 15, 2019</pre>
 */
public class CptReportDayServiceTest extends AbstractMockitoTest {

    @Mock
    private ReportDayCptCreativeDao reportDayCptCreativeDao;
    @InjectMocks
    private CptReportDayService cptReportDayService;

    @Before
    public void before() throws Exception {
        when(reportDayCptCreativeDao.countByExample(any())).thenReturn(1L);
        when(reportDayCptCreativeDao.selectByExample(any())).thenReturn(Collections.singletonList(BeanTestUtils.generateInstance(ReportDayCptCreativePo.class)));
    }

    @After
    public void after() throws Exception {
    }

    /**
     * Method: getCptReportDayList(Operator operator, QueryCptReportParamDto queryCptReportParamDto, Integer page, Integer size)
     */
    @Test
    public void testGetCptReportDayList() throws Exception {
    }

    /**
     * Method: exportCptReportDayDtoList(Operator operator, QueryCptReportParamDto queryCptReportParamDto)
     */
    @Test
    public void testExportCptReportDayDtoList() throws Exception {
    }

    /**
     * Method: getCptReportListByPage(Operator operator, QueryCptReportParamDto queryCptReportParamDto, String orderBy, Integer page, Integer size)
     */
    @Test
    public void testGetCptReportListByPage() throws Exception {
        //TODO: Test goes here...
    /*
    try {
       Method method = CptReportDayService.getClass().getMethod("getCptReportListByPage", Operator.class, QueryCptReportParamDto.class, String.class, Integer.class, Integer.class);
       method.setAccessible(true);
       method.invoke(<Object>, <Parameters>);
    } catch(NoSuchMethodException e) {
    } catch(IllegalAccessException e) {
    } catch(InvocationTargetException e) {
    }
    */
    }

    /**
     * Method: getCptReportDayDtos(Operator operator, ReportDayCptCreativePoExample example)
     */
    @Test
    public void testGetCptReportDayDtos() throws Exception {
        //TODO: Test goes here...
    /*
    try {
       Method method = CptReportDayService.getClass().getMethod("getCptReportDayDtos", Operator.class, ReportDayCptCreativePoExample.class);
       method.setAccessible(true);
       method.invoke(<Object>, <Parameters>);
    } catch(NoSuchMethodException e) {
    } catch(IllegalAccessException e) {
    } catch(InvocationTargetException e) {
    }
    */
    }

    /**
     * Method: cptReportDayPos2Dtos(List<ReportDayCptCreativePo> cptReportDayPos)
     */
    @Test
    public void testCptReportDayPos2Dtos() throws Exception {
        //TODO: Test goes here...
    /*
    try {
       Method method = CptReportDayService.getClass().getMethod("cptReportDayPos2Dtos", List<ReportDayCptCreativePo>.class);
       method.setAccessible(true);
       method.invoke(<Object>, <Parameters>);
    } catch(NoSuchMethodException e) {
    } catch(IllegalAccessException e) {
    } catch(InvocationTargetException e) {
    }
    */
    }

    /**
     * Method: buildQueryExample(Operator operator, QueryCptReportParamDto param, String ordeyBy)
     */
    @Test
    public void testBuildQueryExample() throws Exception {
        //TODO: Test goes here...
    /*
    try {
       Method method = CptReportDayService.getClass().getMethod("buildQueryExample", Operator.class, QueryCptReportParamDto.class, String.class);
       method.setAccessible(true);
       method.invoke(<Object>, <Parameters>);
    } catch(NoSuchMethodException e) {
    } catch(IllegalAccessException e) {
    } catch(InvocationTargetException e) {
    }
    */
    }

} 
