package com.bilibili.brand.platform.report.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.platform.report.api.dto.StatOrderDto;
import com.bilibili.brand.platform.report.api.service.IStatOrderService;
import com.bilibili.brand.platform.charging.dao.AdStatOrderDayDao;
import com.bilibili.brand.platform.charging.po.AdStatOrderDayPoExample;
import com.bilibili.brand.platform.report.biz.po.StatPo;
import com.bilibili.brand.platform.report.biz.po.StatPojo;
import com.bilibili.cpt.platform.common.BeanCopyUtil;
import com.bilibili.cpt.platform.common.GROUP_TYPE;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2016/12/9.
 */
@Service
public class StatOrderService extends StatBaseService implements IStatOrderService {
    @Autowired
    private AdStatOrderDayDao statOrderDao;

    @Override
    public List<StatOrderDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DEFAULT, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DAY, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.WEEK, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.MONTH, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getInOrderIdsGroupByTime(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        super.validateParam(orderIds, fromTime, toTime);
        List<StatPo> statPos = getPosInOrderIdsGroupByDay(orderIds, fromTime, toTime);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DEFAULT, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public Map<Integer, StatOrderDto> getMapInOrderIds(List<Integer> orderIds) throws ServiceException {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyMap();
        }

        List<StatPo> statPos = getPosInOrderIdsGroupByDay(orderIds, null, Utils.getEndSecondOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)));
        // Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)这个参数不要随便改 取昨天之前的时间算排期状态 今天的数据有反欺诈
        if (null == statPos) {
            statPos = new ArrayList<>();
        }

        if (CollectionUtils.isEmpty(statPos)) {
            return Collections.emptyMap();
        }
        Collections.sort(statPos, (o1, o2) -> o1.getGroupTime().compareTo(o2.getGroupTime()));
        List<StatOrderDto> statOrderDtos = convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DEFAULT, GROUP_KEY.ORDER_ID, statPos.get(0).getGroupTime(), Utils.getNow()));
        Map<Integer, StatOrderDto> map = new HashMap<>(statOrderDtos.size());
        for (StatOrderDto dto : statOrderDtos) {
            map.put(dto.getOrderId(), dto);
        }
        return map;
    }

    @Override
    public List<StatOrderDto> getInAccountIdsGroupByTime(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.DEFAULT, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getInAccountIdsGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.DAY, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getInAccountIdsGroupByWeek(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.WEEK, GROUP_KEY.ORDER_ID, fromTime, toTime));

    }

    @Override
    public List<StatOrderDto> getInAccountIdsGroupByMonth(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.MONTH, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    private List<StatOrderDto> convertPojoToDto(List<StatPojo> statPojos) {
        if (CollectionUtils.isEmpty(statPojos)) {
            return Collections.emptyList();
        }
        List<StatOrderDto> result = new ArrayList<>(statPojos.size());
        result.addAll(statPojos.stream().map(po -> StatOrderDto.builder()
                .accountId(po.getAccountId())
                .orderId(po.getOrderId())
                .showAccount(po.getShowAccount())
                .clickCount(po.getClickCount())
                .cost(po.getCost())
                .clickRate(po.getClickRate())
                .averageCostPerThousand(po.getAverageCostPerThousand())
                .costPerClick(po.getCostPerClick())
                .date(po.getDate()).build()).collect(Collectors.toList()));
        return result;
    }


    @Override
    public List<StatOrderDto> getInOrderIdsGroupByDay(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        super.validateParam(orderIds, fromTime, toTime);
        List<StatPo> statPos = getPosInOrderIdsGroupByDay(orderIds, fromTime, toTime);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }

        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DAY, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getInOrderIdsGroupByWeek(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        super.validateParam(orderIds, fromTime, toTime);
        List<StatPo> statPos = getPosInOrderIdsGroupByDay(orderIds, fromTime, toTime);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.WEEK, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getByAccountIdGroupByTimeAndGroupType(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, GROUP_TYPE group_type, List<Integer> orderIdList) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosByAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode()), orderIdList), group_type, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    @Override
    public List<StatOrderDto> getInOrderIdsGroupByMonth(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        super.validateParam(orderIds, fromTime, toTime);
        List<StatPo> statPos = getPosInOrderIdsGroupByDay(orderIds, fromTime, toTime);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.MONTH, GROUP_KEY.ORDER_ID, fromTime, toTime));
    }

    private List<StatPo> getPosByAccountIdGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) {
        AdStatOrderDayPoExample example = new AdStatOrderDayPoExample();
        AdStatOrderDayPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        if (!CollectionUtils.isEmpty(salesTypes)) {
            criteria.andSalesTypeIn(salesTypes);
        }
        return BeanCopyUtil.transform(statOrderDao.selectByExample(example), StatPo.class);
    }

    private List<StatPo> getPosByAccountIdGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime,
                                                     List<Integer> salesTypes, List<Integer> orderIdList) {
        AdStatOrderDayPoExample example = new AdStatOrderDayPoExample();
        AdStatOrderDayPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        if (!CollectionUtils.isEmpty(salesTypes)) {
            criteria.andSalesTypeIn(salesTypes);
        }
        if (!CollectionUtils.isEmpty(orderIdList)) {
            criteria.andOrderIdIn(orderIdList);
        }
        return BeanCopyUtil.transform(statOrderDao.selectByExample(example), StatPo.class);
    }

    private List<StatPo> getPosInOrderIdsGroupByDay(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) {
        AdStatOrderDayPoExample example = new AdStatOrderDayPoExample();
        AdStatOrderDayPoExample.Criteria criteria = example.or().andOrderIdIn(orderIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        return BeanCopyUtil.transform(statOrderDao.selectByExample(example), StatPo.class);
    }
}
