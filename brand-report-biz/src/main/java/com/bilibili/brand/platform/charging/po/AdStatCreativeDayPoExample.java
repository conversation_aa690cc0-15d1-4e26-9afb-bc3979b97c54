package com.bilibili.brand.platform.charging.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class AdStatCreativeDayPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdStatCreativeDayPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(Integer value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(Integer value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(Integer value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(Integer value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<Integer> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<Integer> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Long value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Long value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Long value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Long value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Long value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Long> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Long> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Long value1, Long value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Long value1, Long value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNull() {
            addCriterion("sales_type is null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNotNull() {
            addCriterion("sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeEqualTo(Integer value) {
            addCriterion("sales_type =", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotEqualTo(Integer value) {
            addCriterion("sales_type <>", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThan(Integer value) {
            addCriterion("sales_type >", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_type >=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThan(Integer value) {
            addCriterion("sales_type <", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sales_type <=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIn(List<Integer> values) {
            addCriterion("sales_type in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotIn(List<Integer> values) {
            addCriterion("sales_type not in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("sales_type between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_type not between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andGroupTimeIsNull() {
            addCriterion("group_time is null");
            return (Criteria) this;
        }

        public Criteria andGroupTimeIsNotNull() {
            addCriterion("group_time is not null");
            return (Criteria) this;
        }

        public Criteria andGroupTimeEqualTo(Timestamp value) {
            addCriterion("group_time =", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeNotEqualTo(Timestamp value) {
            addCriterion("group_time <>", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeGreaterThan(Timestamp value) {
            addCriterion("group_time >", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("group_time >=", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeLessThan(Timestamp value) {
            addCriterion("group_time <", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("group_time <=", value, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeIn(List<Timestamp> values) {
            addCriterion("group_time in", values, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeNotIn(List<Timestamp> values) {
            addCriterion("group_time not in", values, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("group_time between", value1, value2, "groupTime");
            return (Criteria) this;
        }

        public Criteria andGroupTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("group_time not between", value1, value2, "groupTime");
            return (Criteria) this;
        }

        public Criteria andShowCountIsNull() {
            addCriterion("show_count is null");
            return (Criteria) this;
        }

        public Criteria andShowCountIsNotNull() {
            addCriterion("show_count is not null");
            return (Criteria) this;
        }

        public Criteria andShowCountEqualTo(Integer value) {
            addCriterion("show_count =", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountNotEqualTo(Integer value) {
            addCriterion("show_count <>", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountGreaterThan(Integer value) {
            addCriterion("show_count >", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_count >=", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountLessThan(Integer value) {
            addCriterion("show_count <", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountLessThanOrEqualTo(Integer value) {
            addCriterion("show_count <=", value, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountIn(List<Integer> values) {
            addCriterion("show_count in", values, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountNotIn(List<Integer> values) {
            addCriterion("show_count not in", values, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountBetween(Integer value1, Integer value2) {
            addCriterion("show_count between", value1, value2, "showCount");
            return (Criteria) this;
        }

        public Criteria andShowCountNotBetween(Integer value1, Integer value2) {
            addCriterion("show_count not between", value1, value2, "showCount");
            return (Criteria) this;
        }

        public Criteria andClickCountIsNull() {
            addCriterion("click_count is null");
            return (Criteria) this;
        }

        public Criteria andClickCountIsNotNull() {
            addCriterion("click_count is not null");
            return (Criteria) this;
        }

        public Criteria andClickCountEqualTo(Integer value) {
            addCriterion("click_count =", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountNotEqualTo(Integer value) {
            addCriterion("click_count <>", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountGreaterThan(Integer value) {
            addCriterion("click_count >", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("click_count >=", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountLessThan(Integer value) {
            addCriterion("click_count <", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountLessThanOrEqualTo(Integer value) {
            addCriterion("click_count <=", value, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountIn(List<Integer> values) {
            addCriterion("click_count in", values, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountNotIn(List<Integer> values) {
            addCriterion("click_count not in", values, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountBetween(Integer value1, Integer value2) {
            addCriterion("click_count between", value1, value2, "clickCount");
            return (Criteria) this;
        }

        public Criteria andClickCountNotBetween(Integer value1, Integer value2) {
            addCriterion("click_count not between", value1, value2, "clickCount");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliIsNull() {
            addCriterion("charged_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliIsNotNull() {
            addCriterion("charged_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliEqualTo(Long value) {
            addCriterion("charged_cost_milli =", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliNotEqualTo(Long value) {
            addCriterion("charged_cost_milli <>", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliGreaterThan(Long value) {
            addCriterion("charged_cost_milli >", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("charged_cost_milli >=", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliLessThan(Long value) {
            addCriterion("charged_cost_milli <", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("charged_cost_milli <=", value, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliIn(List<Long> values) {
            addCriterion("charged_cost_milli in", values, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliNotIn(List<Long> values) {
            addCriterion("charged_cost_milli not in", values, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliBetween(Long value1, Long value2) {
            addCriterion("charged_cost_milli between", value1, value2, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andChargedCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("charged_cost_milli not between", value1, value2, "chargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliIsNull() {
            addCriterion("uncharged_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliIsNotNull() {
            addCriterion("uncharged_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliEqualTo(Long value) {
            addCriterion("uncharged_cost_milli =", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliNotEqualTo(Long value) {
            addCriterion("uncharged_cost_milli <>", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliGreaterThan(Long value) {
            addCriterion("uncharged_cost_milli >", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("uncharged_cost_milli >=", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliLessThan(Long value) {
            addCriterion("uncharged_cost_milli <", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("uncharged_cost_milli <=", value, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliIn(List<Long> values) {
            addCriterion("uncharged_cost_milli in", values, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliNotIn(List<Long> values) {
            addCriterion("uncharged_cost_milli not in", values, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliBetween(Long value1, Long value2) {
            addCriterion("uncharged_cost_milli between", value1, value2, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andUnchargedCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("uncharged_cost_milli not between", value1, value2, "unchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcShowCountIsNull() {
            addCriterion("ac_show_count is null");
            return (Criteria) this;
        }

        public Criteria andAcShowCountIsNotNull() {
            addCriterion("ac_show_count is not null");
            return (Criteria) this;
        }

        public Criteria andAcShowCountEqualTo(Integer value) {
            addCriterion("ac_show_count =", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountNotEqualTo(Integer value) {
            addCriterion("ac_show_count <>", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountGreaterThan(Integer value) {
            addCriterion("ac_show_count >", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ac_show_count >=", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountLessThan(Integer value) {
            addCriterion("ac_show_count <", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountLessThanOrEqualTo(Integer value) {
            addCriterion("ac_show_count <=", value, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountIn(List<Integer> values) {
            addCriterion("ac_show_count in", values, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountNotIn(List<Integer> values) {
            addCriterion("ac_show_count not in", values, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountBetween(Integer value1, Integer value2) {
            addCriterion("ac_show_count between", value1, value2, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcShowCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ac_show_count not between", value1, value2, "acShowCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountIsNull() {
            addCriterion("ac_click_count is null");
            return (Criteria) this;
        }

        public Criteria andAcClickCountIsNotNull() {
            addCriterion("ac_click_count is not null");
            return (Criteria) this;
        }

        public Criteria andAcClickCountEqualTo(Integer value) {
            addCriterion("ac_click_count =", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountNotEqualTo(Integer value) {
            addCriterion("ac_click_count <>", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountGreaterThan(Integer value) {
            addCriterion("ac_click_count >", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("ac_click_count >=", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountLessThan(Integer value) {
            addCriterion("ac_click_count <", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountLessThanOrEqualTo(Integer value) {
            addCriterion("ac_click_count <=", value, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountIn(List<Integer> values) {
            addCriterion("ac_click_count in", values, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountNotIn(List<Integer> values) {
            addCriterion("ac_click_count not in", values, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountBetween(Integer value1, Integer value2) {
            addCriterion("ac_click_count between", value1, value2, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcClickCountNotBetween(Integer value1, Integer value2) {
            addCriterion("ac_click_count not between", value1, value2, "acClickCount");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliIsNull() {
            addCriterion("ac_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliIsNotNull() {
            addCriterion("ac_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliEqualTo(Long value) {
            addCriterion("ac_cost_milli =", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliNotEqualTo(Long value) {
            addCriterion("ac_cost_milli <>", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliGreaterThan(Long value) {
            addCriterion("ac_cost_milli >", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("ac_cost_milli >=", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliLessThan(Long value) {
            addCriterion("ac_cost_milli <", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("ac_cost_milli <=", value, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliIn(List<Long> values) {
            addCriterion("ac_cost_milli in", values, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliNotIn(List<Long> values) {
            addCriterion("ac_cost_milli not in", values, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliBetween(Long value1, Long value2) {
            addCriterion("ac_cost_milli between", value1, value2, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("ac_cost_milli not between", value1, value2, "acCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourShowCountIsNull() {
            addCriterion("hour_show_count is null");
            return (Criteria) this;
        }

        public Criteria andHourShowCountIsNotNull() {
            addCriterion("hour_show_count is not null");
            return (Criteria) this;
        }

        public Criteria andHourShowCountEqualTo(Integer value) {
            addCriterion("hour_show_count =", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountNotEqualTo(Integer value) {
            addCriterion("hour_show_count <>", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountGreaterThan(Integer value) {
            addCriterion("hour_show_count >", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("hour_show_count >=", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountLessThan(Integer value) {
            addCriterion("hour_show_count <", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountLessThanOrEqualTo(Integer value) {
            addCriterion("hour_show_count <=", value, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountIn(List<Integer> values) {
            addCriterion("hour_show_count in", values, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountNotIn(List<Integer> values) {
            addCriterion("hour_show_count not in", values, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountBetween(Integer value1, Integer value2) {
            addCriterion("hour_show_count between", value1, value2, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourShowCountNotBetween(Integer value1, Integer value2) {
            addCriterion("hour_show_count not between", value1, value2, "hourShowCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountIsNull() {
            addCriterion("hour_click_count is null");
            return (Criteria) this;
        }

        public Criteria andHourClickCountIsNotNull() {
            addCriterion("hour_click_count is not null");
            return (Criteria) this;
        }

        public Criteria andHourClickCountEqualTo(Integer value) {
            addCriterion("hour_click_count =", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountNotEqualTo(Integer value) {
            addCriterion("hour_click_count <>", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountGreaterThan(Integer value) {
            addCriterion("hour_click_count >", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("hour_click_count >=", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountLessThan(Integer value) {
            addCriterion("hour_click_count <", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountLessThanOrEqualTo(Integer value) {
            addCriterion("hour_click_count <=", value, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountIn(List<Integer> values) {
            addCriterion("hour_click_count in", values, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountNotIn(List<Integer> values) {
            addCriterion("hour_click_count not in", values, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountBetween(Integer value1, Integer value2) {
            addCriterion("hour_click_count between", value1, value2, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourClickCountNotBetween(Integer value1, Integer value2) {
            addCriterion("hour_click_count not between", value1, value2, "hourClickCount");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliIsNull() {
            addCriterion("hour_charged_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliIsNotNull() {
            addCriterion("hour_charged_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliEqualTo(Long value) {
            addCriterion("hour_charged_cost_milli =", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliNotEqualTo(Long value) {
            addCriterion("hour_charged_cost_milli <>", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliGreaterThan(Long value) {
            addCriterion("hour_charged_cost_milli >", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("hour_charged_cost_milli >=", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliLessThan(Long value) {
            addCriterion("hour_charged_cost_milli <", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("hour_charged_cost_milli <=", value, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliIn(List<Long> values) {
            addCriterion("hour_charged_cost_milli in", values, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliNotIn(List<Long> values) {
            addCriterion("hour_charged_cost_milli not in", values, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliBetween(Long value1, Long value2) {
            addCriterion("hour_charged_cost_milli between", value1, value2, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourChargedCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("hour_charged_cost_milli not between", value1, value2, "hourChargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourIsNull() {
            addCriterion("latest_group_hour is null");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourIsNotNull() {
            addCriterion("latest_group_hour is not null");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourEqualTo(Timestamp value) {
            addCriterion("latest_group_hour =", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourNotEqualTo(Timestamp value) {
            addCriterion("latest_group_hour <>", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourGreaterThan(Timestamp value) {
            addCriterion("latest_group_hour >", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("latest_group_hour >=", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourLessThan(Timestamp value) {
            addCriterion("latest_group_hour <", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourLessThanOrEqualTo(Timestamp value) {
            addCriterion("latest_group_hour <=", value, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourIn(List<Timestamp> values) {
            addCriterion("latest_group_hour in", values, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourNotIn(List<Timestamp> values) {
            addCriterion("latest_group_hour not in", values, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourBetween(Timestamp value1, Timestamp value2) {
            addCriterion("latest_group_hour between", value1, value2, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andLatestGroupHourNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("latest_group_hour not between", value1, value2, "latestGroupHour");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliIsNull() {
            addCriterion("hour_uncharged_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliIsNotNull() {
            addCriterion("hour_uncharged_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliEqualTo(Long value) {
            addCriterion("hour_uncharged_cost_milli =", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliNotEqualTo(Long value) {
            addCriterion("hour_uncharged_cost_milli <>", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliGreaterThan(Long value) {
            addCriterion("hour_uncharged_cost_milli >", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("hour_uncharged_cost_milli >=", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliLessThan(Long value) {
            addCriterion("hour_uncharged_cost_milli <", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("hour_uncharged_cost_milli <=", value, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliIn(List<Long> values) {
            addCriterion("hour_uncharged_cost_milli in", values, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliNotIn(List<Long> values) {
            addCriterion("hour_uncharged_cost_milli not in", values, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliBetween(Long value1, Long value2) {
            addCriterion("hour_uncharged_cost_milli between", value1, value2, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourUnchargedCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("hour_uncharged_cost_milli not between", value1, value2, "hourUnchargedCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountIsNull() {
            addCriterion("hour_ac_show_count is null");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountIsNotNull() {
            addCriterion("hour_ac_show_count is not null");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountEqualTo(Integer value) {
            addCriterion("hour_ac_show_count =", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountNotEqualTo(Integer value) {
            addCriterion("hour_ac_show_count <>", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountGreaterThan(Integer value) {
            addCriterion("hour_ac_show_count >", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("hour_ac_show_count >=", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountLessThan(Integer value) {
            addCriterion("hour_ac_show_count <", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountLessThanOrEqualTo(Integer value) {
            addCriterion("hour_ac_show_count <=", value, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountIn(List<Integer> values) {
            addCriterion("hour_ac_show_count in", values, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountNotIn(List<Integer> values) {
            addCriterion("hour_ac_show_count not in", values, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountBetween(Integer value1, Integer value2) {
            addCriterion("hour_ac_show_count between", value1, value2, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcShowCountNotBetween(Integer value1, Integer value2) {
            addCriterion("hour_ac_show_count not between", value1, value2, "hourAcShowCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountIsNull() {
            addCriterion("hour_ac_click_count is null");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountIsNotNull() {
            addCriterion("hour_ac_click_count is not null");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountEqualTo(Integer value) {
            addCriterion("hour_ac_click_count =", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountNotEqualTo(Integer value) {
            addCriterion("hour_ac_click_count <>", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountGreaterThan(Integer value) {
            addCriterion("hour_ac_click_count >", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("hour_ac_click_count >=", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountLessThan(Integer value) {
            addCriterion("hour_ac_click_count <", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountLessThanOrEqualTo(Integer value) {
            addCriterion("hour_ac_click_count <=", value, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountIn(List<Integer> values) {
            addCriterion("hour_ac_click_count in", values, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountNotIn(List<Integer> values) {
            addCriterion("hour_ac_click_count not in", values, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountBetween(Integer value1, Integer value2) {
            addCriterion("hour_ac_click_count between", value1, value2, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcClickCountNotBetween(Integer value1, Integer value2) {
            addCriterion("hour_ac_click_count not between", value1, value2, "hourAcClickCount");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliIsNull() {
            addCriterion("hour_ac_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliIsNotNull() {
            addCriterion("hour_ac_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliEqualTo(Long value) {
            addCriterion("hour_ac_cost_milli =", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliNotEqualTo(Long value) {
            addCriterion("hour_ac_cost_milli <>", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliGreaterThan(Long value) {
            addCriterion("hour_ac_cost_milli >", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("hour_ac_cost_milli >=", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliLessThan(Long value) {
            addCriterion("hour_ac_cost_milli <", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("hour_ac_cost_milli <=", value, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliIn(List<Long> values) {
            addCriterion("hour_ac_cost_milli in", values, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliNotIn(List<Long> values) {
            addCriterion("hour_ac_cost_milli not in", values, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliBetween(Long value1, Long value2) {
            addCriterion("hour_ac_cost_milli between", value1, value2, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("hour_ac_cost_milli not between", value1, value2, "hourAcCostMilli");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNull() {
            addCriterion("promotion_purpose_type is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNotNull() {
            addCriterion("promotion_purpose_type is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeEqualTo(Integer value) {
            addCriterion("promotion_purpose_type =", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <>", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThan(Integer value) {
            addCriterion("promotion_purpose_type >", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type >=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThan(Integer value) {
            addCriterion("promotion_purpose_type <", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIn(List<Integer> values) {
            addCriterion("promotion_purpose_type in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotIn(List<Integer> values) {
            addCriterion("promotion_purpose_type not in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type not between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliIsNull() {
            addCriterion("bid_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliIsNotNull() {
            addCriterion("bid_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliEqualTo(Long value) {
            addCriterion("bid_cost_milli =", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliNotEqualTo(Long value) {
            addCriterion("bid_cost_milli <>", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliGreaterThan(Long value) {
            addCriterion("bid_cost_milli >", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("bid_cost_milli >=", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliLessThan(Long value) {
            addCriterion("bid_cost_milli <", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("bid_cost_milli <=", value, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliIn(List<Long> values) {
            addCriterion("bid_cost_milli in", values, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliNotIn(List<Long> values) {
            addCriterion("bid_cost_milli not in", values, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliBetween(Long value1, Long value2) {
            addCriterion("bid_cost_milli between", value1, value2, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andBidCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("bid_cost_milli not between", value1, value2, "bidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliIsNull() {
            addCriterion("hour_bid_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliIsNotNull() {
            addCriterion("hour_bid_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliEqualTo(Long value) {
            addCriterion("hour_bid_cost_milli =", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliNotEqualTo(Long value) {
            addCriterion("hour_bid_cost_milli <>", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliGreaterThan(Long value) {
            addCriterion("hour_bid_cost_milli >", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("hour_bid_cost_milli >=", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliLessThan(Long value) {
            addCriterion("hour_bid_cost_milli <", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("hour_bid_cost_milli <=", value, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliIn(List<Long> values) {
            addCriterion("hour_bid_cost_milli in", values, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliNotIn(List<Long> values) {
            addCriterion("hour_bid_cost_milli not in", values, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliBetween(Long value1, Long value2) {
            addCriterion("hour_bid_cost_milli between", value1, value2, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourBidCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("hour_bid_cost_milli not between", value1, value2, "hourBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliIsNull() {
            addCriterion("ac_bid_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliIsNotNull() {
            addCriterion("ac_bid_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliEqualTo(Long value) {
            addCriterion("ac_bid_cost_milli =", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliNotEqualTo(Long value) {
            addCriterion("ac_bid_cost_milli <>", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliGreaterThan(Long value) {
            addCriterion("ac_bid_cost_milli >", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("ac_bid_cost_milli >=", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliLessThan(Long value) {
            addCriterion("ac_bid_cost_milli <", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("ac_bid_cost_milli <=", value, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliIn(List<Long> values) {
            addCriterion("ac_bid_cost_milli in", values, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliNotIn(List<Long> values) {
            addCriterion("ac_bid_cost_milli not in", values, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliBetween(Long value1, Long value2) {
            addCriterion("ac_bid_cost_milli between", value1, value2, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andAcBidCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("ac_bid_cost_milli not between", value1, value2, "acBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliIsNull() {
            addCriterion("hour_ac_bid_cost_milli is null");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliIsNotNull() {
            addCriterion("hour_ac_bid_cost_milli is not null");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliEqualTo(Long value) {
            addCriterion("hour_ac_bid_cost_milli =", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliNotEqualTo(Long value) {
            addCriterion("hour_ac_bid_cost_milli <>", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliGreaterThan(Long value) {
            addCriterion("hour_ac_bid_cost_milli >", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliGreaterThanOrEqualTo(Long value) {
            addCriterion("hour_ac_bid_cost_milli >=", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliLessThan(Long value) {
            addCriterion("hour_ac_bid_cost_milli <", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliLessThanOrEqualTo(Long value) {
            addCriterion("hour_ac_bid_cost_milli <=", value, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliIn(List<Long> values) {
            addCriterion("hour_ac_bid_cost_milli in", values, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliNotIn(List<Long> values) {
            addCriterion("hour_ac_bid_cost_milli not in", values, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliBetween(Long value1, Long value2) {
            addCriterion("hour_ac_bid_cost_milli between", value1, value2, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andHourAcBidCostMilliNotBetween(Long value1, Long value2) {
            addCriterion("hour_ac_bid_cost_milli not between", value1, value2, "hourAcBidCostMilli");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNull() {
            addCriterion("schedule_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIsNotNull() {
            addCriterion("schedule_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleIdEqualTo(Long value) {
            addCriterion("schedule_id =", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotEqualTo(Long value) {
            addCriterion("schedule_id <>", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThan(Long value) {
            addCriterion("schedule_id >", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("schedule_id >=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThan(Long value) {
            addCriterion("schedule_id <", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdLessThanOrEqualTo(Long value) {
            addCriterion("schedule_id <=", value, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdIn(List<Long> values) {
            addCriterion("schedule_id in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotIn(List<Long> values) {
            addCriterion("schedule_id not in", values, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdBetween(Long value1, Long value2) {
            addCriterion("schedule_id between", value1, value2, "scheduleId");
            return (Criteria) this;
        }

        public Criteria andScheduleIdNotBetween(Long value1, Long value2) {
            addCriterion("schedule_id not between", value1, value2, "scheduleId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}