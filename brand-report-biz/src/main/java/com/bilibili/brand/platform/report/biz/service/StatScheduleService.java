package com.bilibili.brand.platform.report.biz.service;

import com.bilibili.adp.common.bean.CommonContants;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.platform.report.api.dto.StatScheduleDto;
import com.bilibili.brand.platform.report.api.service.IStatScheduleService;
import com.bilibili.brand.platform.charging.dao.AdStatScheduleDayDao;
import com.bilibili.brand.platform.charging.po.AdStatScheduleDayPo;
import com.bilibili.brand.platform.charging.po.AdStatScheduleDayPoExample;
import com.bilibili.brand.platform.report.biz.po.StatPo;
import com.bilibili.brand.platform.report.biz.po.StatPojo;
import com.bilibili.cpt.platform.common.BeanCopyUtil;
import com.bilibili.cpt.platform.common.GROUP_TYPE;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 16/9/26.
 */
@Service
public class StatScheduleService extends StatBaseService implements IStatScheduleService {
    @Autowired
    private AdStatScheduleDayDao adStatScheduleDayDao;

    private List<StatScheduleDto> convertPojoToDto(List<StatPojo> statPojos) {
        if (CollectionUtils.isEmpty(statPojos)) {
            return Collections.emptyList();
        }
        List<StatScheduleDto> result = new ArrayList<>(statPojos.size());
        result.addAll(statPojos.stream().map(po -> StatScheduleDto.builder()
                        .accountId(po.getAccountId())
                        .orderId(po.getOrderId())
                        .scheduleId(po.getScheduleId())
                        .showCount(po.getShowAccount())
                        .clickCount(po.getClickCount())
                        .cost(po.getCost())
                        .clickRate(po.getClickRate())
                        .averageCostPerThousand(po.getAverageCostPerThousand())
                        .costPerClick(po.getCostPerClick())
                        .date(po.getDate())
                        .salesType(po.getSalesType())
                        .build())
                .collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<StatScheduleDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DEFAULT, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public List<StatScheduleDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DAY, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public List<StatScheduleDto> getInScheduleIdsGroupByTime(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime) {
        super.validateParam(scheduleIds, fromTime, toTime);
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds, fromTime, toTime, null);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DEFAULT, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public Map<Integer, StatScheduleDto> getMapInScheduleIdsGroupByTime(List<Integer> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyMap();
        }
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds, null, Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)), null);
        // Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)这个参数不要随便改 取昨天之前的时间算排期状态 今天的数据有反欺诈
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(statPos)) {
            return Collections.emptyMap();
        }
        Collections.sort(statPos, (o1, o2) -> o1.getGroupTime().compareTo(o2.getGroupTime()));
        List<StatScheduleDto> statCampaignDtos = convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DEFAULT, GROUP_KEY.SCHEDULE_ID, statPos.get(0).getGroupTime(), Utils.getNow()));
        Map<Integer, StatScheduleDto> map = new HashMap<>(statCampaignDtos.size());
        for (StatScheduleDto dto : statCampaignDtos) {
            map.put(dto.getScheduleId(), dto);
        }
        return map;
    }

    @Override
    public Map<Integer, List<StatScheduleDto>> getMapInScheduleIdsGroupByDay(List<Integer> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyMap();
        }
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds, null, Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)),null);
        // Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)这个参数不要随便改 取昨天之前的时间算排期状态 今天的数据有反欺诈
        if (CollectionUtils.isEmpty(statPos)) {
            return Collections.EMPTY_MAP;
        }

        statPos.sort(Comparator.comparing(StatPo::getGroupTime));
        List<StatScheduleDto> list = convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DAY, GROUP_KEY.SCHEDULE_ID,
                statPos.get(0).getGroupTime(), Utils.getNow()));

        return list.stream()
                .collect(Collectors.groupingBy(StatScheduleDto::getScheduleId,
                        Collectors.mapping(Function.identity(), Collectors.toList())));
    }


    @Override
    public List<StatScheduleDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.MONTH, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public List<StatScheduleDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.WEEK, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public List<StatScheduleDto> getInScheduleIdsGroupByDay(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        super.validateParam(scheduleIds, fromTime, toTime);
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds, fromTime, toTime, null);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DAY, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    @Override
    public Map<Integer, List<StatScheduleDto>> getMapInScheduleIdsGroupByDay(List<Integer> scheduleIds, Integer salesType) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyMap();
        }
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds,
                null, Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)), salesType);
        // Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)这个参数不要随便改 取昨天之前的时间算排期状态 今天的数据有反欺诈
        if (CollectionUtils.isEmpty(statPos)) {
            return Collections.EMPTY_MAP;
        }

        statPos.sort(Comparator.comparing(StatPo::getGroupTime));
        List<StatScheduleDto> list = convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DAY, GROUP_KEY.SCHEDULE_ID,
                statPos.get(0).getGroupTime(), Utils.getNow()));

        return list.stream()
                .collect(Collectors.groupingBy(StatScheduleDto::getScheduleId,
                        Collectors.mapping(Function.identity(), Collectors.toList())));
    }

    @Override
    public List<StatScheduleDto> getInScheduleIdsGroupByTime(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime, Integer salesType) {
        super.validateParam(scheduleIds, fromTime, toTime);
        List<StatPo> statPos = getBatchInScheduleIdsGroupByDay(scheduleIds, fromTime, toTime, salesType);
        if (null == statPos) {
            statPos = new ArrayList<>();
        }
        return convertPojoToDto(super.groupBy(statPos, GROUP_TYPE.DEFAULT, GROUP_KEY.SCHEDULE_ID, fromTime, toTime));
    }

    private List<StatPo> getBatchStatPoInAccountIdsGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) {
        return CollectionHelper.callInBatches(accountIds, CommonContants.BATCH_SIZE,
                ids -> this.getPosInAccountIdGroupByDay(ids, fromTime, toTime, salesTypes));

    }

    private List<StatPo> getBatchInScheduleIdsGroupByDay(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime, Integer salesType) {
        return CollectionHelper.callInBatches(scheduleIds, CommonContants.BATCH_SIZE,
                ids -> getPosInScheduleIdsGroupByDay(ids, fromTime, toTime, salesType));

    }

    private List<StatPo> getPosInScheduleIdsGroupByDay(List<Integer> scheduleIds, Timestamp fromTime, Timestamp toTime, Integer salesType) {
        AdStatScheduleDayPoExample example = new AdStatScheduleDayPoExample();
        AdStatScheduleDayPoExample.Criteria criteria = example.or().andScheduleIdIn(scheduleIds);
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        if (null != salesType) {
            criteria.andSalesTypeEqualTo(salesType);
        }
        List<AdStatScheduleDayPo> pos = adStatScheduleDayDao.selectByExample(example);
        return BeanCopyUtil.transform(pos, StatPo.class);
    }


    private List<StatPo> getPosInAccountIdGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) {
        AdStatScheduleDayPoExample example = new AdStatScheduleDayPoExample();
        AdStatScheduleDayPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        if (!CollectionUtils.isEmpty(salesTypes)) {
            criteria.andSalesTypeIn(salesTypes);
        }
        return BeanCopyUtil.transform(adStatScheduleDayDao.selectByExample(example), StatPo.class);
    }

}
