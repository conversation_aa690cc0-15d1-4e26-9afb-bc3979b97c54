package com.bilibili.brand.platform.report.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by fanwen<PERSON> on 2018/4/16.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaStatPojo {
    private Long showAccount;
    private Integer clickCount;
    private BigDecimal cost;
    private BigDecimal unchargedCost;
    private String date;
    private BigDecimal clickRate;
    private BigDecimal costPerClick;
    private BigDecimal averageCostPerThousand;
    private Integer promotionType;

    private Integer accountId;
    private Integer orderId;
    private Integer campaignId;
    private Integer unitId;
    private Integer creativeId;
    private Integer screenId;
    private Integer sourceId;
    private Integer salesType;
}
