package com.bilibili.brand.platform.charging.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdStatCreativeDayPo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 账户ID
     */
    private Integer accountId;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 计划ID
     */
    private Integer campaignId;

    /**
     * 单元ID
     */
    private Long unitId;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 售卖类型
     */
    private Integer salesType;

    /**
     * 时间序列-日期
     */
    private Timestamp groupTime;

    /**
     * 曝光数量
     */
    private Long showCount;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * 应扣费计费（单位：毫分）
     */
    private Long chargedCostMilli;

    /**
     * 未扣费计费（单位：毫分）
     */
    private Long unchargedCostMilli;

    /**
     * 反作弊曝光量
     */
    private Integer acShowCount;

    /**
     * 反作弊点击次数
     */
    private Integer acClickCount;

    /**
     * 反作弊计费（单位：毫分）
     */
    private Long acCostMilli;

    /**
     * 最新小时曝光数量, 仅实时
     */
    private Integer hourShowCount;

    /**
     * 最新小时点击次数，仅实时
     */
    private Integer hourClickCount;

    /**
     * 最新小时应扣费计费（单位：毫分）
     */
    private Long hourChargedCostMilli;

    /**
     * 最新小时时间序列
     */
    private Timestamp latestGroupHour;

    /**
     * 记录版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 最新小时未扣费计费（单位：毫分）
     */
    private Long hourUnchargedCostMilli;

    /**
     * 最新小时反作弊曝光数量, 仅实时
     */
    private Integer hourAcShowCount;

    /**
     * 最新小时反作弊点击次数，仅实时
     */
    private Integer hourAcClickCount;

    /**
     * 最新小时反作弊计费（单位：毫分）
     */
    private Long hourAcCostMilli;

    /**
     * 推广目的
     */
    private Integer promotionPurposeType;

    /**
     * 累计出价 单位（毫分）
     */
    private Long bidCostMilli;

    /**
     * 最新小时累计出价 单位（毫分）
     */
    private Long hourBidCostMilli;

    /**
     * 反作弊累计出价 单位（毫分）
     */
    private Long acBidCostMilli;

    /**
     * 最新小时反作弊累计出价 单位（毫分）
     */
    private Long hourAcBidCostMilli;

    /**
     * 品牌排期ID
     */
    private Long scheduleId;

    /**
     * 时间序列-聚合后的日期结果，以逗号分隔
     */
    private String groupTimes;

    private static final long serialVersionUID = 1L;
}