package com.bilibili.brand.platform.report.biz.service;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.platform.report.api.dto.StatAccountDto;
import com.bilibili.brand.platform.report.api.service.IStatAccountService;
import com.bilibili.brand.platform.charging.dao.AdStatAccountDayDao;
import com.bilibili.brand.platform.charging.po.AdStatAccountDayPoExample;
import com.bilibili.cpt.platform.common.BeanCopyUtil;
import com.bilibili.cpt.platform.common.GROUP_TYPE;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.platform.report.biz.po.StatPo;
import com.bilibili.brand.platform.report.biz.po.StatPojo;

/**
 * Created by fanwenbin on 16/9/26.
 */
@Service
public class StatAccountService extends StatBaseService implements IStatAccountService {
    @Autowired
    private AdStatAccountDayDao statAccountDao;

    private List<StatAccountDto> convertPojoToDto(List<StatPojo> statPojos) {
        if (CollectionUtils.isEmpty(statPojos)) {
            return Collections.emptyList();
        }
        List<StatAccountDto> result = new ArrayList<>(statPojos.size());
        result.addAll(statPojos.stream().map(po -> StatAccountDto.builder()
                .salesType(po.getSalesType())
                .accountId(po.getAccountId())
                .showAccount(po.getShowAccount())
                .clickCount(po.getClickCount())
                .cost(po.getCost())
                .clickRate(po.getClickRate())
                .averageCostPerThousand(po.getAverageCostPerThousand())
                .costPerClick(po.getCostPerClick())
                .date(po.getDate()).build()).collect(Collectors.toList()));
        return result;
    }

    @Override
    public List<StatAccountDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DEFAULT, GROUP_KEY.ACCOUNT_ID, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.DAY, GROUP_KEY.ACCOUNT_ID, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.WEEK, GROUP_KEY.ACCOUNT_ID, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        super.validateParam(accountId, fromTime, toTime);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(Lists.newArrayList(accountId), fromTime, toTime, Lists.newArrayList(SalesType.getByCode(salesType).getCode())), GROUP_TYPE.MONTH, GROUP_KEY.ACCOUNT_ID, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getInAccountIdsGroupByTime(List<Integer> accountIds,
                                                           Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes)
            throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.DEFAULT, GROUP_KEY.ACCOUNT_ID, GROUP_KEY.SALES_TYPE, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getInAccountIdsGroupByDay(List<Integer> accountIds,
                                                          Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes)
            throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.DAY, GROUP_KEY.ACCOUNT_ID, GROUP_KEY.SALES_TYPE, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getInAccountIdsGroupByWeek(List<Integer> accountIds,
                                                           Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes)
            throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.WEEK, GROUP_KEY.ACCOUNT_ID, GROUP_KEY.SALES_TYPE, fromTime, toTime));
    }

    @Override
    public List<StatAccountDto> getInAccountIdsGroupByMonth(List<Integer> accountIds,
                                                            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes)
            throws ServiceException {
        super.validateParam(fromTime, toTime, salesTypes);
        return convertPojoToDto(super.groupBy(getPosInAccountIdGroupByDay(accountIds, fromTime, toTime, salesTypes), GROUP_TYPE.MONTH, GROUP_KEY.ACCOUNT_ID, GROUP_KEY.SALES_TYPE, fromTime, toTime));
    }


    private List<StatPo> getPosInAccountIdGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) {
        AdStatAccountDayPoExample example = new AdStatAccountDayPoExample();
        AdStatAccountDayPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        if (null != fromTime) {
            criteria.andGroupTimeGreaterThanOrEqualTo(fromTime);
        }
        if (null != toTime) {
            criteria.andGroupTimeLessThanOrEqualTo(toTime);
        }
        if (!CollectionUtils.isEmpty(salesTypes)) {
            criteria.andSalesTypeIn(salesTypes);
        }

        return BeanCopyUtil.transform(statAccountDao.selectByExample(example), StatPo.class);
    }
}
