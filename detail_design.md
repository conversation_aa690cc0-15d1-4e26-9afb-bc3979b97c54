# 【品牌广告】TOP系列新样式过渡动效升级 - 详细技术设计

## 1. 需求概述

基于ai-brand.md和endtech.md的需求，实现TOP系列广告的过渡动效升级功能，包括排期层新增过渡形式字段、创意层新增过渡视频上传功能等。

## 2. 技术架构分析

### 2.1 现有代码结构
- **排期层**：TopViewPlusScheduleController、TopViewPlusScheduleVo、GdTopViewSchedulePo
- **创意层**：TopViewCreativeController、NewExternalTopViewDto、UpdateExternalTopViewDto
- **播放形式枚举**：SsaVideoPlayModeEnum（浮窗彩蛋视频=3，自动续播=4）
- **IP视频服务**：IPVideoService、IIPVideoService

### 2.2 核心改动点
1. 数据库表结构变更
2. 排期层VO/DTO/PO增加过渡形式字段
3. 创意层VO/DTO增加过渡视频字段
4. 校验逻辑实现
5. IP视频检索功能增强

## 3. 详细实现方案

### 3.1 数据库层改动

#### 3.1.1 gd_top_view_schedule表新增字段
```sql
ALTER TABLE `gd_top_view_schedule`
    ADD COLUMN `transition_mode` TINYINT(4) NOT NULL DEFAULT 0
    COMMENT '过渡形式：0-普通过渡 1-自定义过渡';
```

#### 3.1.2 ssa_splash_screen_video表枚举值扩展
需要为过渡视频新增video_type枚举值（具体值待确认）

### 3.2 排期层改动

#### 3.2.1 VO层改动
**文件**: `brand-portal/src/main/java/com/bilibili/adp/brand/portal/webapi/schedule/vo/TopViewPlusScheduleVo.java`

新增字段：
```java
@ApiModelProperty("过渡形式：0-普通过渡 1-自定义过渡")
private Integer transitionMode;
```

#### 3.2.2 PO层改动
**文件**: `brand-biz/src/main/java/com/bilibili/brand/biz/schedule/po/GdTopViewSchedulePo.java`

新增字段：
```java
/**
 * 过渡形式：0-普通过渡 1-自定义过渡
 */
private Integer transitionMode;
```

#### 3.2.3 DTO层改动
**文件**: `brand-api/src/main/java/com/bilibili/brand/api/schedule/dto/GdTopViewScheduleDto.java`

新增字段：
```java
/**
 * 过渡形式：0-普通过渡 1-自定义过渡
 */
private Integer transitionMode;
```

#### 3.2.4 校验逻辑实现
**文件**: `brand-biz/src/main/java/com/bilibili/ssa/platform/biz/service/schedule/TopViewPlusScheduleValidator.java`

在validate方法中新增校验：
```java
// 校验过渡形式与播放形式的关联
if (Objects.equals(scheduleBo.getTransitionMode(), 1)) { // 自定义过渡
    Integer playMode = scheduleBo.getSsaVideoPlayMode();
    Assert.isTrue(!Objects.equals(playMode, SsaVideoPlayModeEnum.EASTER_EGG.getCode()) 
                  && !Objects.equals(playMode, SsaVideoPlayModeEnum.AUTO_CONTINUE_PLAY.getCode()),
                  "播放形式选择【浮窗彩蛋视频】或【自动续播】时，过渡形式不可选择【自定义过渡】");
}
```

### 3.3 创意层改动

#### 3.3.1 VO层改动
**文件**: `brand-portal/src/main/java/com/bilibili/adp/brand/portal/webapi/splash_screen/vo/top_view/NewExternalTopViewVo.java`

新增字段：
```java
@ApiModelProperty("过渡视频")
private TransitionVideoVo transitionVideo;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public static class TransitionVideoVo {
    @ApiModelProperty("视频URL")
    private String url;
    
    @ApiModelProperty("业务ID")
    private Integer bizId;
}
```

#### 3.3.2 DTO层改动
**文件**: `brand-api/src/main/java/com/bilibili/brand/api/creative/dto/NewExternalTopViewDto.java`

新增字段：
```java
/**
 * 过渡视频
 */
private TransitionVideoDto transitionVideo;
```

新增DTO类：
```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransitionVideoDto {
    private String url;
    private Integer bizId;
}
```

#### 3.3.3 过渡视频校验逻辑
**文件**: `brand-biz/src/main/java/com/bilibili/brand/biz/creative/service/TopViewCreativeValidator.java`

新增校验方法：
```java
private void validateTransitionVideo(TransitionVideoDto transitionVideo) {
    if (transitionVideo == null || transitionVideo.getBizId() == null) {
        return;
    }
    
    IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(transitionVideo.getBizId());
    Assert.notNull(ipVideoBo, "过渡视频不存在");
    
    // 校验时长：必须小于1s (1000ms)
    Assert.isTrue(ipVideoBo.getDuration() < 1000, 
                  "过渡视频时长必须小于1s，当前时长：" + ipVideoBo.getDuration() + "ms");
    
    // 校验大小：必须小于3MB
    long maxSize = 3 * 1024 * 1024; // 3MB
    Assert.isTrue(ipVideoBo.getSize() <= maxSize,
                  "过渡视频大小必须小于3MB，当前大小：" + (ipVideoBo.getSize() / 1024 / 1024) + "MB");
}
```

### 3.4 IP视频检索功能增强

#### 3.4.1 接口参数扩展
**文件**: `brand-api/src/main/java/com/bilibili/brand/api/material/IIPVideoService.java`

修改方法签名，新增maxDuration参数：
```java
PageResult<IPVideoBo> getIPVideoBoList(String name, Integer page, Integer size, 
                                       Integer dealStatus, Integer width, Integer height, 
                                       List<Integer> videoTypeList, Long maxSize, 
                                       Long maxDuration, boolean strictMatch);
```

#### 3.4.2 实现类修改
**文件**: `brand-biz/src/main/java/com/bilibili/brand/biz/material/IPVideoService.java`

在getIPVideoBoList方法中新增时长过滤：
```java
if (maxDuration != null) {
    criteria.andDurationLessThanOrEqualTo(maxDuration);
}
```

#### 3.4.3 Controller层调用
**文件**: `brand-portal/src/main/java/com/bilibili/adp/brand/portal/webapi/resource/ResourceController.java`

在IP视频检索接口中，默认检索1s以内的视频：
```java
@RequestMapping(value = "/videos", method = RequestMethod.GET)
public Response<PageResult<IPVideoBo>> getIPVideos(
        @RequestParam(value = "max_duration", required = false, defaultValue = "1000") Long maxDuration,
        // ... 其他参数
) {
    return Response.SUCCESS(ipVideoService.getIPVideoBoList(
        name, page, size, dealStatus, width, height, 
        videoTypeList, maxSize, maxDuration, strictMatch));
}
```

### 3.5 个性化引导升级

#### 3.5.1 图片上传校验修改
**文件**: 图片上传相关的校验逻辑

支持两种尺寸：
- 300*300（现状，<=500k）
- 700*252（新增，<=2MB）

校验逻辑：
```java
private void validateGuideImage(String imageUrl, Integer width, Integer height, Long size) {
    if ((width == 300 && height == 300 && size <= 500 * 1024) ||
        (width == 700 && height == 252 && size <= 2 * 1024 * 1024)) {
        // 校验通过
        return;
    }
    throw new IllegalArgumentException("个性化引导图片尺寸必须为300px*300px(<=500k)或700px*252px(<=2MB)");
}
```

### 3.6 版本控制

#### 3.6.1 版本控制逻辑
**文件**: `brand-biz/src/main/java/com/bilibili/brand/biz/creative/convert/TopViewConvert.java`

在getDefaultVersionPlatformMap方法中新增版本控制：
```java
// 针对过渡视频功能的版本控制
if (isTransitionVideoEnabled) {
    versionMap.put(PlatformType.ANDROID.getCode(), 8470001);
    versionMap.put(PlatformType.IPHONE.getCode(), 84700001);
}
```

### 3.7 批量创意排期结构比较

#### 3.7.1 排期结构比较字段扩展
**文件**: 批量创意相关的比较逻辑

在排期结构比较中新增transition_mode字段：
```java
// 现有比较字段基础上新增
"topView_info#transition_mode"
```

### 3.8 审核后台改动

#### 3.8.1 审核列表外露过渡视频
需要在审核列表中展示过渡视频信息，具体实现待审核后台模块确认。

### 3.9 售卖监控改动

#### 3.9.1 SKU底表支持
**文件**: 
- `com.bilibili.brand.job.common.PushCreativeInfoSdwJob`
- `com.bilibili.brand.job.common.SyncBrandCreativeProductSkuJob`

需要在SKU相关逻辑中支持新样式。

## 4. 接口改动清单

### 4.1 排期相关接口

#### 4.1.1 新建排期
- **Topview GD**: `POST /web_api/v1/schedules/top_view/plus/gd/add`
- **Topview CPT**: `POST /web_api/v1/schedules/top_view/plus/cpt/add`

请求参数新增：
```json
{
  "transition_mode": 1 // 过渡形式：0-普通过渡 1-自定义过渡
}
```

#### 4.1.2 编辑排期
- **Topview GD**: `PUT /web_api/v1/schedules/top_view/plus/gd/update`
- **Topview CPT**: `PUT /web_api/v1/schedules/top_view/plus/cpt/update`

#### 4.1.3 排期详情
- `GET /web_api/v1/schedules/{schedule_id}`

响应新增：
```json
{
  "transition_mode": 1 // 过渡形式：0-普通过渡 1-自定义过渡
}
```

### 4.2 创意相关接口

#### 4.2.1 新建创意
- `POST /web_api/v1/creative/top_view/external`

请求参数新增：
```json
{
  "transition_video": {
    "url": "http://www.bilibili.com/1111111.mp4",
    "biz_id": 12345
  }
}
```

#### 4.2.2 编辑创意
- `PUT /web_api/v1/creative/top_view/external`

#### 4.2.3 创意详情
- `GET /web_api/v1/creative/top_view/{top_view_id}`

响应新增：
```json
{
  "transition_video": {
    "upos_url": "http://www.bilibili.com/1111111.mp4",
    "xcode_md5": "aaaaaaaa",
    "xcode_height": 100,
    "xcode_width": 200,
    "id": 1,
    "biz_id": 12345,
    "status": 1
  }
}
```

#### 4.2.4 IP视频检索
- `GET /web_api/v1/resource/videos`

新增参数：
```
max_duration: 1000 // 最大时长(ms)，默认1000ms
```

## 5. 开发优先级

### 5.1 第一阶段（核心功能）
1. 数据库表结构变更
2. 排期层过渡形式字段新增
3. 排期层校验逻辑实现
4. 创意层过渡视频字段新增
5. 过渡视频校验逻辑

### 5.2 第二阶段（增强功能）
1. IP视频检索功能增强
2. 个性化引导升级
3. 版本控制逻辑
4. 批量创意支持

### 5.3 第三阶段（配套功能）
1. 审核后台改动
2. 售卖监控改动
3. 测试和优化

## 6. 风险点和注意事项

### 6.1 数据兼容性
- 存量排期的transition_mode字段默认值为0（普通过渡）
- 需要确保向后兼容

### 6.2 校验逻辑
- 过渡形式与播放形式的关联校验需要在前后端都实现
- 过渡视频的时长和大小校验需要准确

### 6.3 版本控制
- 需要确认Android和iOS的具体版本号
- 版本控制逻辑需要与客户端对齐

### 6.4 性能考虑
- IP视频检索增加时长过滤可能影响查询性能
- 需要考虑数据库索引优化

## 7. 测试计划

### 7.1 单元测试
- 校验逻辑的单元测试
- 数据转换逻辑的单元测试

### 7.2 集成测试
- 排期创建和编辑的完整流程测试
- 创意创建和编辑的完整流程测试

### 7.3 回归测试
- 确保现有功能不受影响
- 存量数据的兼容性测试
