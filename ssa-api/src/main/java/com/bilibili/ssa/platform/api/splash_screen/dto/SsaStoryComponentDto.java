package com.bilibili.ssa.platform.api.splash_screen.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 由于包依赖问题，copy已有类 ComponentDto
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaStoryComponentDto implements Serializable {

    private static final long serialVersionUID = 2736546689552807698L;
    /**
     * 组件id
     */
    private Long componentId;

    /**
     * 组件名称
     */
    private String componentName;

    /**
     * 组件类型，0：图片，默认0
     */
    private Integer componentType;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 图片md5
     */
    private String imageMd5;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 组件跳转链接
     */
    private String jumpUrl;

    /**
     * 组件唤起链接
     */
    private String schemaUrl;

    /**
     * 组件标链接，如果为空则表示无标
     */
    private String markUrl;
}
