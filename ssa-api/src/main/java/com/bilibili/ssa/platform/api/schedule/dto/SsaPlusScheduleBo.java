package com.bilibili.ssa.platform.api.schedule.dto;

import com.bilibili.adp.common.annotation.DatabaseColumnName;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.enums.TopViewVideoEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaPlusScheduleBo implements Serializable {

    private static final long serialVersionUID = -6310389993945575741L;

    private Integer scheduleId;

    private String name;

    private Integer orderId;

    private Timestamp beginTime;

    private Timestamp endTime;

    private Timestamp launchDate;

    private Integer rotationNum;

    private Integer resType;

    private Integer adType;

    /**
     * 闪屏全半屏样式 1-全屏 2-半屏
     */
    private Integer screenStyle;
    /**
     * 闪屏展示样式 1-全屏图片 2-半屏图片 3-半屏横屏视频 4-半屏竖屏视频 5-全屏竖屏视频
     */
    private Integer showStyle;

    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    private Integer clickArea;

    //交互方式 0-点击交互 1-滑动交互
    private Integer interactStyle;

    /**
     * 跳转模块样式 0-引导说明 1-引导说明+标题
     */
    private Integer jumpAreaStyle;

    /**
     * 跳转模块动效 0-无动效 1-跳动动效
     */
    private Integer jumpAreaEffect;

    //按钮类型 0-点击交互 1-滑动交互 2-选择交互
    private Integer buttonStyle;

    @DatabaseColumnName("人群包")
    private List<Integer> crowdPackIds;

    @DatabaseColumnName("排除的人群包")
    private List<Integer> excludeCrowdPackIds;
    /**
     * 外部价,单位(分)
     */
    private BigDecimal externalPrice;

    /**
     * 内部价,单位(分)
     */
    private BigDecimal internalPrice;

    /**
     * 周期id
     */
    private Integer cycleId;

    /**
     * 目标展现量
     */
    private Integer totalImpression;

    //用于vo转Bo时接收对象
    private List<SplitDaysImpressBo> impressBos;

    //用于实际创建时的逻辑处理
    private SplitDaysImpressBo impressBo;

    private List<TargetRule> targetRules;

    private SsaScheduleTargetDto targetDto;

    private List<Integer> sourceIds;

    private Integer hour;

    private boolean isTodaySchedule;

    private Integer frequencyLimit;

    private Integer salesType;

    //topview
    /**
     * 推广目的 2-落地页 4-应用下载
     */
    private Integer promotionPurposeType;

    /**
     * APP包ID
     */
    private List<Integer> appPackageIdList;

    /**
     * 各端APP包ID Map<platformId, appPackageId>
     */
    private Map<Integer, Integer> platformAppPackageMap;

    /**
     * 唤起外部APP：0-无须唤起 1-需要唤起
     */
    private Integer needWakeApp;

    /**
     * 首焦媒体类型 1-视频 2-图文
     * {@link com.bilibili.ssa.platform.common.enums.BannerShowType}
     */
    private Integer hfAdType;

    /**
     * 业务方id
     */
    private Integer businessSideId;

    //处理序列号
    private Long dealSeq;

    private Integer areaGroupId;

    private Operator operator;

    private Integer orderProduct;

    /**
     * adx deal Id
     */
    private Long dealId;

    /**
     * adx deal id组
     */
    private Long dealGroupId;

    //是否投放内链
    private Boolean launchInnerJump;

    private Integer displayMode;


    /**
     * 闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频 3.浮窗彩蛋视频
     *
     * @see com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum
     */
    private Integer ssaVideoPlayMode;


    /**
     * 预订比例，半轮：500，一轮：1000，千分比
     */
    private Integer bookingRatio;

    /**
     * dsp的资源位ID
     */
    private String bidderSourceId;


    /**
     * topview视频类型
     *
     * @see TopViewVideoEnum
     */
    private Integer topViewVideoType;

    //单价（单位：分）
    private BigDecimal unitPrice;

    /**
     * 是否启用空间
     */
    private Integer openEnterpriseSpace;

    /**
     * 启用空间的mid
     */
    private Long mid;

    /**
     * 预订模式，0-按天，1-分时
     *
     * @see com.bilibili.brand.api.common.enums.SsaCptOrderModeEnum
     */
    private Integer orderMode;

    private Integer tempScheduleId;

    /**
     * 闪屏联动，0：无（常规闪屏），1：默认搜索词
     * PS:
     * （1）即使在打开的情况下，searchKeyword也可能不传，因为闪屏排期往往预占，而搜索cpt排期不需要，这就可能需要闪屏排期比搜索cpt排期前置，
     * 顾允许创建时在打开开关的情况下searchKeyword为空，后续他们可以在编辑排期中关联搜索cpt排期
     * （2）在（1）的前提下，linkageType透传就很必要了，因为会涉及到刊例价的计算
     */
    private Integer linkageType;

    /**
     * 搜索词
     */
    private SsaSearchKeywordDto searchKeyword;

    /**
     * 是否是topView
     */
    private boolean isTopView;

    private Integer wakeAppType;

    //需要锁的库存
    private Integer lockInventory;

    //goblin forceUpdate
    private boolean forceUpdate;

    /**
     * @see com.bilibili.brand.api.common.enums.SsaEffectiveType
     */
    private Integer effectiveType;

    /**
     * 过渡形式：0-普通过渡 1-自定义过渡
     */
    private Integer transitionMode;
}
