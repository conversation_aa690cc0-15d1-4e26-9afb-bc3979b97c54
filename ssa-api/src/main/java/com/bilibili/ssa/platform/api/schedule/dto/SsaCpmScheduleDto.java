/** 
* <AUTHOR> 
* @date  2018年5月23日
*/ 

package com.bilibili.ssa.platform.api.schedule.dto;

import com.bilibili.ssa.platform.common.enums.SsaSecondPageShowStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaCpmScheduleDto {

	private Integer gdScheduleId;

	private Integer orderId;

	private String name;

	private Integer cycleId;

	private Integer resType;

	private Integer adType;

	private SsaScheduleTargetDto target;

	private Timestamp beginDate;

	private Timestamp endDate;

	private BigDecimal costPrice;

	private Integer totalImpression;

	/**
	 * 日最大目标展示量
	 */
	private Integer dayImpressionLimit;
	/**
	 * 闪屏全半屏样式 1-全屏 2-半屏
	 */
	private Integer screenStyle;
	/**
	 * 闪屏展示样式 1-全屏图片 2-半屏图片 3-半屏横屏视频 4-半屏竖屏视频 5-全屏竖屏视频
	 */
	private Integer showStyle;
	/**
	 * 素材点击区域 0-常规区域 1-全素材区域
	 */
	private Integer clickArea;

	//交互方式 0-点击交互 1-滑动交互
	private Integer interactStyle;

	/**
	 * 跳转模块样式 0-引导说明 1-引导说明+标题
	 */
	private Integer jumpAreaStyle;

	/**
	 * 跳转模块动效 0-无动效 1-跳动动效
	 */
	private Integer jumpAreaEffect;

	private Integer buttonStyle;

	//人群包（包含)
	private List<Integer> crowdPackIds;

	//人群包（排除）
	private List<Integer> excludeCrowdPackIds;

	//频次单元(1-日 2-周 3-月)
	private Integer frequencyUnit;

	//频次限制
	private Integer frequencyLimit;

	/**
	 * 售卖类型 44-闪屏cpm 41-闪屏cpt
	 */
	private Integer salesType;

	/**
	 * 预占轮数
	 */
	private Integer rotationNum;

	private Integer areaGroupId;

	//deal_id
	private Long dealId;

	/**
	 * deal id组
	 */
	private Long dealGroupId;

	/**
	 * 闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频
	 * @see com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum
	 */
	private Integer ssaVideoPlayMode;

	//dsp的资源位id，比如京东天宫资源位id
	private String bidderSourceId;
	//yyyy-MM-dd HH:mm:ss
	private Timestamp beginTime;
	//yyyy-MM-dd HH:mm:ss
	private Timestamp endTime;

	/**
	 * 合同账号id
	 */
	private Integer contractAccountId;

	private Integer wakeAppType;
}
