package com.bilibili.ssa.platform.api.splash_screen.dto;

import com.fastobject.diff.DiffLog;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 闪屏附加组件信息
 *
 * <AUTHOR>
 * @date 2022/8/29
 */
@Data
public class SsaAdditionalComponentsDto implements Serializable {

    private static final long serialVersionUID = -2689618409379954282L;

    private Integer splashScreenId;

    /**
     * 组件类型
     */
    @DiffLog(name = "组件类型")
    private List<Integer> componentTypes;

    /**
     * 闪屏倒计时组件
     */
    @DiffLog(name = "闪屏倒计时组件")
    private SsaCountDownComponentDto countDownComponent;

}
