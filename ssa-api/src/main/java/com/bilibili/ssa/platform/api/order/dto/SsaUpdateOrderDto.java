/** 
* <AUTHOR> 
* @date  2018年3月8日
*/ 

package com.bilibili.ssa.platform.api.order.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaUpdateOrderDto implements Serializable {

	private static final long serialVersionUID = 8589256417116551799L;
	
	private Integer id;
	private String orderName;
	private List<SsaScheduleDto> schedules;
	private String projectName;
	private String projectDesc;
	private List<SsaOrderAttachmentDto> attachments;
	private Integer status;
	private String rejectReason;
	private Integer salesType;
	private Timestamp startDate;
	private Timestamp endDate;
	private Integer sourceType;
}
