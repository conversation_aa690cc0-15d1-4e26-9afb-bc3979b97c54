/** 
* <AUTHOR> 
* @date  2018年3月19日
*/ 

package com.bilibili.ssa.platform.soa;

import com.bilibili.ssa.platform.api.order.dto.SsaOrderDto;

import java.util.List;
import java.util.Map;

public interface ISoaSsaOrderService {

	SsaOrderDto load(Integer id);

	Map<Integer, String> getSsaOrderId2NameMapInSsaOrderIds(List<Integer> ids);

	List<SsaOrderDto> queryByCrmOrderId(List<Integer> crmOrderIds);
}
