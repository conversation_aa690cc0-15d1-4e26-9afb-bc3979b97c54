package com.bilibili.ssa.platform.api.splash_screen.service;

import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenCustomizedDTO;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenJumpDTO;

import java.util.List;
import java.util.Map;

public interface ISsaSplashScreenCustomUrlService {

    void batchSaveCustomizedImpUrl(Integer splashScreenId, List<String> customizedUrlList);

    void batchSaveCustomizedClickUrl(Integer splashScreenId, List<String> customizedUrlList);

    void batchUpdateCustomizedImpUrl(Integer splashScreenId, List<String> customizedUrlList);

    void batchUpdateCustomizedClickUrl(Integer splashScreenId, List<String> customizedUrlList);

    void batchSave(Integer splashScreenId, List<SplashScreenCustomizedDTO> ssaCustomizedDTOS);

    List<String> getCustomizedImpUrlList(Integer splashScreenId);

    List<String> getCustomizedClickUrlList(Integer splashScreenId);

    /*
     * 返回闪屏id+包含的曝光度监控链接
     */
    Map<Integer, List<String>> getCustomizedImpUrlListMap(List<Integer> splashScreenIdList);

    /*
     * 返回闪屏id+包含的点击监控链接
     */
    Map<Integer, List<String>> getCustomizedClickUrlListMap(List<Integer> splashScreenIdList);

    /*
     * 返回平台id+平台下包含的监控链接类型-监控链接
     */
    Map<Integer, Map<Integer,List<String>>> getUrlPlatformMap(Integer splashScreenId);
}
