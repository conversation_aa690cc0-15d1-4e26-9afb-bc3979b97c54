package com.bilibili.ssa.platform.api.splash_screen.dto.second_page;

import com.fastobject.diff.DiffLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 闪屏第二屏按钮信息
 * <AUTHOR>
 * @date 2022/12/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaSecondPageButtonDto implements Serializable {

    private static final long serialVersionUID = 6766755163038991531L;

    /**
     * 按钮文案
     */
    @DiffLog(name = "按钮文案")
    private String buttonText;

    /**
     * 动效url
     */
    @DiffLog(name = "动效url")
    private String lottieUrl;

    /**
     * 动效MD5
     */
    private String lottieMd5;

    /**
     * 降级文案，存在动效按钮文案上
     */
    @DiffLog(name = "降级文案")
    private String downgradeButtonText;

}
