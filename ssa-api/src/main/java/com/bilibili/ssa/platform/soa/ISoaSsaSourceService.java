package com.bilibili.ssa.platform.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.location.dto.SourceConfigDto;
import com.bilibili.ssa.platform.api.location.dto.SsaGdPriceDTO;
import com.bilibili.ssa.platform.api.location.dto.SsaSourceAllInfoDto;
import com.bilibili.ssa.platform.api.location.dto.UpdateSsaSourceConfigDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017年6月14日
 */
public interface ISoaSsaSourceService {

    void updateSourceConfig(Operator operator, UpdateSsaSourceConfigDto updateSourceConfigDto);

    List<SsaSourceAllInfoDto> getSourcesByCycleId(Integer cycleId, Integer salesType);

	void batchUpdateSourceConfigByCycleId(Operator operator, Integer cycleId, UpdateSsaSourceConfigDto updateSourceConfigDto);

    Map<Integer, List<SourceConfigDto>> getMapSourceConfigDtoByCycleIds(List<Integer> cycleIds);

    List<SsaGdPriceDTO> getSsaGdPriceByCycleId(Integer cycleId, Integer OrderProduct);

    void updateSsaGdPrice(Operator operator, SsaGdPriceDTO priceDTO);
}
