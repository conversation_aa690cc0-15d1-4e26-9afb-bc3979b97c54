package com.bilibili.ssa.platform.api.location.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaCycleDto implements Serializable{
    private static final long serialVersionUID = 4772107021480045582L;
    private Integer id;

    @ApiModelProperty("继承周期id")
    private Integer parentCycleId;

    private String name;

    private Timestamp beginTime;

    private Timestamp endTime;

    private Integer status;

    private Integer adType;

    private Integer orderProduct;

    private Integer displayMode;

    private Timestamp executionTime;
}
