/**
 * <AUTHOR>
 * @date 2018年3月6日
 */

package com.bilibili.ssa.platform.api.schedule.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmQueryScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import javafx.util.Pair;

import java.sql.Timestamp;
import java.util.List;

/**
 * 已迁移至brand-api
 */
@Deprecated
public interface ISsaPDScheduleService {

    void createSchedule(SsaCpmScheduleDto scheduleDto, Operator operator) throws ServiceException;

    void updateSchedule(SsaCpmScheduleDto scheduleDto, Operator operator) throws ServiceException;

    SsaCpmScheduleDto getCpmScheduleByScheduleId(Integer scheduleId);

    Pair<Timestamp, Timestamp> getAvailableDate(Integer scheduleId);

    List<SsaCpmScheduleDto> querySSaCpmSchedule(SsaCpmQueryScheduleDto query);

    void deleteSchedule(Integer scheduleId, Operator operator) throws ServiceException;

}
