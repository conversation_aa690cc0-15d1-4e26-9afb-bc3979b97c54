package com.bilibili.ssa.platform.api.schedule.dto.ott;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 19:41
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SsaOttPdScheduleDetailBo extends SsaOttBaseScheduleBo {

    private Integer scheduleId;

    private String orderName;

    private int inventories;

    private List<GdInventoryDetailBo> inventoryDetails;

    private List<TargetRule> targets;

    private BigDecimal totalPrice;

    private Long dealGroupId;

    private String bidderSourceId;
}
