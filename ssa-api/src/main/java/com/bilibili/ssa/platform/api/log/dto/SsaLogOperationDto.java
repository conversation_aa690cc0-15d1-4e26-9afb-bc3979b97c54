package com.bilibili.ssa.platform.api.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaLogOperationDto implements Serializable{
    private static final long serialVersionUID = 3053950081395297363L;
    private Long id;
    private Integer objId;
    private String objFlag;
    private String operateType;
    private String operatorUsername;
    private String ctime;
    private String value;
}
