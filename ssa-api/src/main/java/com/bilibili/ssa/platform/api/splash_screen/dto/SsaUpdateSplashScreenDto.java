package com.bilibili.ssa.platform.api.splash_screen.dto;

import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.second_page.SsaSecondPageInfoDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.second_page.SsaSecondPageInteractEggDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaUpdateSplashScreenDto {

    private Integer id;

    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 闪屏标题
     */
    private String title;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 时间定向
     */
    private Integer timeTarget;

    /**
     * 素材加密
     */
    private Integer encryption;

    /**
     * 闪屏排期
     */
    private List<SsaNewScheduleSplashScreenMappingDto> ssaScheduleMappingDtos;

    /**
     * 展示样式: 1-全屏 2-半屏
     */
    private Integer showStyle;

    /**
     * 是否可跳过: 0-否 1-是
     */
    private Integer isSkip;

    /**
     * 下发时间: 1-过审下发 2-排期开始前2小时下发
     */
    private Integer issuedTime;

    /**
     * 闪屏文案
     */
    private String copyWriting;

    /**
     * 基础图片
     */
    private List<SsaSplashScreenBaseImageDto> ssaBaseImageDtos;


    /**
     * 闪屏视频
     */
    private SsaUpdateSplashScreenVideoDto videoDto;

    /**
     * 彩蛋视频
     */
    private SsaUpdateSplashScreenVideoDto eggVideoDto;

    /**
     * 广告角标(1:广告，2:推广)
     */
    private Integer cmMark;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否使用默认版本号: true-是 false-否
     */
    private Boolean useDefaultVersion;

    /**
     * 闪屏版本控制
     */

    private  List<SsaSplashScreenVersionControlDto> ssaVersionControlDtos;


    private List<Integer> appPackageIds;

    /**
     * 分享开关 0-关闭 1-打开
     */
    private Integer shareState;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享副标题
     */
    private String shareSubTitle;

    /**
     * 分享图片URL
     */
    private String shareImageUrl;

    /**
     * 分享图片hash
     */
    private String shareImageHash;

    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    private Integer clickArea;

    /**
     * 闪屏跳转模型列表
     */
    private List<SplashScreenJumpDTO> splashScreenJumpDTOS;

    /**
     * 闪屏监控模型列表
     */
    private List<SplashScreenCustomizedDTO> ssaCustomizedDTOS;

    /**
     * 分端开关 0-分端 1-不分端
     */
    private Integer platformSwitch;

    //跳转类型
    private Integer jumpType;

    /**
     * 交互方式 0-点击交互 1-手势交互
     */
    private Integer interactStyle;

    private List<SplashScreenDynamicButtonBO> buttonBOS;

    //是否支持自定义引导文案 0-否 1-是
    private Integer supportCustomGuide;

    private Integer orderProduct;

    //选择式按钮纯文字按钮唤起文案
    private String extraSchemeCopywriting;

    //3.选择式按钮纯文字按钮跳转文案")
    private String extraGuideInstructions;

    //按钮背景颜色样式:0-黑色 1-白色
    private Integer buttonBgColorStyle;

    //互动闪屏是否支持按钮
    private Boolean isSupportButtonToInteract;

    //商业标id
    private Integer busMarkId;

    //闪屏按钮文字颜色 0-默认 1-黑色
    private Integer textColorStyle;

    private SsaAdditionalComponentsDto additionalComponents;

    private SsaSecondPageInfoDto secondPageInfo;

    /**
     * 闪屏story创意信息
     */
    private SsaArchiveMaterialDto ssaArchiveMaterial;

    //产品型号
    private ProductLabelDto productLabel;

    //小程序
    private MiniProgramDto miniProgram;

    private String interactInstructions;

    /**
     * com.bilibili.ssa.platform.biz.enumerate.BalloonLineColorType
     * 气球线的颜色
     */
    private Integer lineColorType;

    private SsaSplashScreenMiddlePageDto middlePage;

    // 是否启用直播间预约
    private Boolean isEnableLiveBooking;

    // 直播预约id
    private Long liveBookingId;

    private SsaSecondPageInteractEggDto interactEgg;

}