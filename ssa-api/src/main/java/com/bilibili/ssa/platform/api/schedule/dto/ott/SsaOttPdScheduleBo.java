package com.bilibili.ssa.platform.api.schedule.dto.ott;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/13 17:55
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SsaOttPdScheduleBo extends SsaOttBaseScheduleBo {

    private List<TargetRule> targets;

    private List<GdInventoryDetailBo> reserveInventoryDetails;

    //没有实际意义，主要防止adx_order主键冲突
    private Long dealId;

    private Long dealGroupId;

    private String bidderSourceId;

    private Integer scheduleId;
    //总展示量
    private Integer inventories;

}
