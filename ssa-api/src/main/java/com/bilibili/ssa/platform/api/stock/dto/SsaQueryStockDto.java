/** 
* <AUTHOR> 
* @date  2018年5月25日
*/ 

package com.bilibili.ssa.platform.api.stock.dto;

import java.sql.Timestamp;
import java.util.List;
import java.util.TreeSet;

import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaQueryStockDto {
	private Integer sourceGroupId;
	private SsaScheduleTargetDto target;
	private TreeSet<Timestamp> dates;
	private List<Integer> sourceIds;
}
