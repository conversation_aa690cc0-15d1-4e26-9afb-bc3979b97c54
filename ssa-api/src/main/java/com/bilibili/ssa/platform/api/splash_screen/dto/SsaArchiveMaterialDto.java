package com.bilibili.ssa.platform.api.splash_screen.dto;

import com.fastobject.diff.DiffLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaArchiveMaterialDto implements Serializable {
    private static final long serialVersionUID = -6939363667376621753L;

    /**
     * 闪屏story创意信息
     */
    @DiffLog(name = "闪屏story创意信息")
    private SsaStoryCreativeDto ssaStoryCreative;

    /**
     * 闪屏稿件转码后id
     */
    private Integer archiveTranscodingId;

    /**
     * 稿件id
     */
    @DiffLog(name = "稿件id")
    private Long aid;
}
