package com.bilibili.ssa.platform.api.location.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by cuihaichuan on 2017/10/09.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSsaCycleDto implements Serializable {
    private static final long serialVersionUID = -1002473561012089794L;

    private Integer id;

    private String name;

    private Timestamp beginTime;

    private Timestamp endTime;

    @ApiModelProperty("广告类型")
    private Integer ad_type;

    @ApiModelProperty("继承周期id")
    private Integer parentCycleId;

    private Integer orderProduct;

    private Timestamp executionTime;

}
