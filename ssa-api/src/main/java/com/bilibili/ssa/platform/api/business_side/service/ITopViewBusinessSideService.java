package com.bilibili.ssa.platform.api.business_side.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.business_side.dto.TopViewBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.dto.UpdateTopViewBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.location.dto.TopViewRotationDto;

import java.util.List;

public interface ITopViewBusinessSideService {


    Integer saveBusinessSideLimit(Operator operator, UpdateTopViewBusinessSideRotationConfigDto dto);

    List<TopViewBusinessSideRotationConfigDto> getBusinessSideLimitByConfigId(Integer sourceConfigId);

	TopViewBusinessSideRotationConfigDto getBusinessSideLimitById(Integer id);
	TopViewBusinessSideRotationConfigDto getBusinessSideLimitByBusinessSideIdAndSourceConfigId(Integer accountId, Integer sourceConfigId);
    TopViewRotationDto getRotationLimitByCycleId(Integer cycleId);

    TopViewBusinessSideRotationConfigDto getByCycleIdAndTopViewTypeAndAccountId(Integer cycleId, Integer topViewType, Integer accountId);
}
