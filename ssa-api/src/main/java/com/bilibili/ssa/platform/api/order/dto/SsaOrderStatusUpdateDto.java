/** 
* <AUTHOR> 
* @date  2018年3月21日
*/ 

package com.bilibili.ssa.platform.api.order.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaOrderStatusUpdateDto {
	private Integer id;
	private List<Integer> ids;
	private Integer status;
	private String rejectReason;
}
