package com.bilibili.ssa.platform.api.business_side.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.business_side.dto.SsaUserDto;

import java.util.List;
import java.util.Map;

public interface ISsaUserService {
    SsaUserDto getUserByName(String name);
    SsaUserDto getUserById(Integer userId);

    public void updateUserName(Operator operator, Integer userId, String userName);
    public List<SsaUserDto> getBindedUsersByBusinessSideId(Integer businessSideId);
    public Integer save(Operator operator, String name);
    public Map<Integer, List<SsaUserDto>> getBusinessSideUserMapInBusinessSideIds(List<Integer> businessSideIds);

}
