/** 
* <AUTHOR> 
* @date  2018年5月23日
*/

package com.bilibili.ssa.platform.api.schedule.group.dto;

import java.sql.Timestamp;
import java.util.List;

import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaScheduleGroupDto {
	private Integer id;
	private Integer orderId;
	private String name;
	private Integer costPrice;
	private SsaScheduleTargetDto target;
	private Integer frequencyUnit;
	private Integer frequencyLimit;
	private Timestamp startDate;
	private Timestamp endDate;
	private Integer totalImpression;
	private Integer salesType;
	private Integer sourceGroupId;
	private Integer status;
	List<SsaScheduleDto> schedules;
}
