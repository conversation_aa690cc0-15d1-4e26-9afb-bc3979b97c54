package com.bilibili.ssa.platform.api.splash_screen.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaUposVideoDto implements Serializable {

    private static final long serialVersionUID = -8833220732579743477L;
    /**
     * 自增id
     */
    private Integer id;

    /**
     * BIZ_ID
     */
    private Integer bizId;

    /**
     * 转码前的截图
     */
    private String cover;

    /**
     * 转码前视频url
     */
    private String uposUrl;

    /**
     * 转码前的宽度
     */
    private Integer width;

    /**
     * 转码后的高度
     */
    private Integer height;

    /**
     * 转码前的MD5
     */
    private String md5;

    /**
     * 转码前的大小(单位B)
     */
    private Integer size;

    /**
     * 转码前时长(单位毫秒）
     */
    private Integer duration;

    /**
     * 视频名称
     */
    private String fileName;

    /**
     * 转码后视频URL
     */
    private String xcodeUposUrl;

    /**
     * 转码后视频MD5
     */
    private String xcodeMd5;

    /**
     * 转码后视频宽度
     */
    private Integer xcodeWidth;

    /**
     * 转码后视频高度
     */
    private Integer xcodeHeight;

    /**
     * 转码后的大小(单位B)
     */
    private Integer xcodeSize;

    /**
     * 转码后的时长(单位毫秒)
     */
    private Integer xcodeDuration;

    /**
     * 视频状态0：转码中  1：转码成功  2：转码失败  3:视频不合法
     */
    private Integer status;

    /**
     * 权限认证
     */
    private String uposAuth;

    private List<String> videoCoverUrls;

    /**
     * 清晰度类型: 0-原始清晰度 1-480P 2-1080P
     */
    private Integer definitionType;
}