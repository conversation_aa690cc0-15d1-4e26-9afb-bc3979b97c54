package com.bilibili.ssa.platform.soa;

import com.bilibili.ssa.platform.api.splash_screen.dto.SsaUposVideoDto;

import java.util.List;
import java.util.Map;

public interface ISoaUposVideoService {
    int getUposVideoSequence();

    SsaUposVideoDto getUposVideoByBizId(Integer bizId);

    List<SsaUposVideoDto> getUposVideosInBizIds(List<Integer> bizIds);

    Map<Integer, SsaUposVideoDto> getBizId2UposVideoMapInBizIds(List<Integer> bizIds);

    Map<Integer, SsaUposVideoDto> getMgkDefinition2UposVideoWithCoverMapByBizId(Integer bizId);

    Map<Integer, List<SsaUposVideoDto>> getMgkBizId2UposVideoWithCoverMapInBizIds(List<Integer> bizIds);
}
