package com.bilibili.ssa.platform.api.splash_screen.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaNewSplashScreenVersionControlDto {

    /**
     * 闪屏ID
     */
    private Integer splashScreenId;

    /**
     * 开始版本号
     */
    private Integer startVersion;

    /**
     * 截止版本号
     */
    private Integer endVersion;

    /**
     * 平台ID: 1-iPhone 2-Android 3-iPad
     */
    private Integer platformId;

}