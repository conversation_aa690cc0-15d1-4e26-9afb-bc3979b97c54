package com.bilibili.ssa.platform.api.splash_screen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaStoryJumpInfoDto implements Serializable {

    private static final long serialVersionUID = -7144346092234328592L;
    @ApiModelProperty("跳转类型")
    private Integer jumpType;

    @ApiModelProperty("是否分端")
    private Boolean differentiatePlatform;

    @ApiModelProperty("跳转链接信息")
    private List<SsaStoryJumpUrlDto> urls;
}
