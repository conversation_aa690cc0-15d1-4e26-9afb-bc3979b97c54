package com.bilibili.ssa.platform.api.business_side.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTopViewBusinessSideRotationConfigDto implements Serializable{
    private static final long serialVersionUID = -8356965946363883553L;
    private Integer topViewConfigId;
    private Integer accountId;
    private Integer conRotationLimit;
    private Integer busRotationLimit;
}
