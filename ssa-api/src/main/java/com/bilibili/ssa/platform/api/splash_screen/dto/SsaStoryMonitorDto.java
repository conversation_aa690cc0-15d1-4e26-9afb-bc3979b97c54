package com.bilibili.ssa.platform.api.splash_screen.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 闪屏story创意监控信息
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
@Data
public class SsaStoryMonitorDto implements Serializable {

    private static final long serialVersionUID = -6908016334462344292L;
    /**
     * @see com.bilibili.brand.api.common.enums.MonitorType
     */
    private Integer type;

    private List<String> urls;
}
