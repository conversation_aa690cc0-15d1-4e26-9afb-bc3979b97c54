/** 
* <AUTHOR> 
* @date  2018年3月15日
*/ 

package com.bilibili.ssa.platform.api.schedule.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DayRotationInfoDto implements Serializable {

	private static final long serialVersionUID = -3834249478509405108L;
	
	private Integer usedRotationNum;
	private Integer totalRotationNum;
	private Integer remainedRotationNum;
}
