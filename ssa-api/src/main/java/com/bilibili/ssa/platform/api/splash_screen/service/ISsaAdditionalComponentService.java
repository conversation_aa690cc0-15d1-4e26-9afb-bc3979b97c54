package com.bilibili.ssa.platform.api.splash_screen.service;

import com.bilibili.ssa.platform.api.splash_screen.dto.SsaAdditionalComponentsDto;

import java.util.List;

/**
 * 闪屏
 * <AUTHOR>
 * @date 2022/8/29
 */
public interface ISsaAdditionalComponentService {

    SsaAdditionalComponentsDto queryAdditionalComponent(Integer splashScreenId);


    List<SsaAdditionalComponentsDto> queryAdditionalComponentMaps(List<Integer> splashScreenId);
}
