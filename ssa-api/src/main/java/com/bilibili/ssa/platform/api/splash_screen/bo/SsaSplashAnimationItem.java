package com.bilibili.ssa.platform.api.splash_screen.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/7
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SsaSplashAnimationItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer type;
    private Integer delay;
    private Integer duration;
    private SsaSplashAnimationParam param;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SsaSplashAnimationParam {
        private SsaSplashAnimationPosition start;
        private SsaSplashAnimationPosition end;
        private SsaSplashAnimationPosition control1;
        private SsaSplashAnimationPosition control2;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SsaSplashAnimationPosition {
        private BigDecimal x;
        private BigDecimal y;
    }
}
