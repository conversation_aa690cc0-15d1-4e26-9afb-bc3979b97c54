package com.bilibili.ssa.platform.api.schedule.dto.ott;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SsaOttGdScheduleDetailBo extends SsaOttBaseScheduleBo {

    private Integer scheduleId;

    private String orderName;

    private GdInventoryDetailBo inventoryDetail;

    private List<TargetRule> targets;

    private BigDecimal totalPrice;

    private List<Integer> crowdPackIds;

    private List<Integer> excludeCrowdPackIds;

    private Integer orderProduct;

    private Long dealGroupId;

    private String bidderSourceId;
}
