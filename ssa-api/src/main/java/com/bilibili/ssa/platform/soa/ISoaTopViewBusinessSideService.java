package com.bilibili.ssa.platform.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.business_side.dto.SsaBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.dto.TopViewBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.dto.UpdateBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.dto.UpdateTopViewBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.location.dto.TopViewRotationDto;

import java.util.List;
import java.util.Map;

public interface ISoaTopViewBusinessSideService {

    @Deprecated
    Integer saveBusinessSideLimit(Operator operator, UpdateTopViewBusinessSideRotationConfigDto dto);
    List<TopViewBusinessSideRotationConfigDto> getBusinessSideLimitByConfigId(Integer sourceConfigId);
    TopViewBusinessSideRotationConfigDto getBusinessSideLimitById(Integer id);
    TopViewBusinessSideRotationConfigDto getBusinessSideLimitByBusinessSideIdAndSourceConfigId(Integer businessSideId,
                                                                                               Integer sourceConfigId);
    TopViewRotationDto getRotationLimitByCycleId(Integer cycleId);
}
