package com.bilibili.ssa.platform.api.splash_screen.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaIpVideoDto implements Serializable {

    @ApiModelProperty(value = "视频库ID", required = true)
    private Integer id;

    @ApiModelProperty("视频名称")
    private String name;

    @ApiModelProperty("视频封面")
    private String cover;

    @ApiModelProperty("视频链接")
    private String url;

    @ApiModelProperty("MD5")
    private String md5;

    @ApiModelProperty("视频宽度")
    private int width;

    @ApiModelProperty("视频高度")
    private int height;

    @ApiModelProperty("转码前视频链接")
    private String before_url;

    @ApiModelProperty("合成视频时长,单位毫秒")
    private Integer duration;

    @ApiModelProperty("闪屏视频类型,0、普通闪屏视频 1、彩蛋视频")
    private Integer videoType;

    @ApiModelProperty("闪屏ID")
    private Integer splashScreenId;
}
