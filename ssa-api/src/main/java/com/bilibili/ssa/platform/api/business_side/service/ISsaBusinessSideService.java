package com.bilibili.ssa.platform.api.business_side.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.business_side.dto.NewBusinessSideDto;
import com.bilibili.ssa.platform.api.business_side.dto.SsaBusinessSideBaseDto;
import com.bilibili.ssa.platform.api.business_side.dto.SsaBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.dto.UpdateBusinessSideRotationConfigDto;

import java.util.List;
import java.util.Map;

public interface ISsaBusinessSideService {

    @Deprecated
    Integer getSsaBusinessSideIdByAccountId(Integer accountId);
    Map<Integer, List<Long>> getAccountId2MidsMapInAccountIds(List<Integer> accountIds);
	void bindMids(Integer accountId, List<Long> mids);

    Integer saveBusinessSideLimit(Operator operator, UpdateBusinessSideRotationConfigDto dto);
    void deleteBusinessSideLimit(Operator operator, Integer limitId);
    List<SsaBusinessSideRotationConfigDto> getBusinessSideLimitBySourceConfigId(Integer sourceConfigId);

    SsaBusinessSideRotationConfigDto getBusinessSideLimitBySourceConfigId(Integer sourceConfigId, Integer accountId);
	SsaBusinessSideRotationConfigDto getBusinessSideLimitById(Integer id);
	SsaBusinessSideRotationConfigDto getBusinessSideLimitByBusinessSideIdAndSourceConfigId(Integer accountId, Integer sourceConfigId);


    @Deprecated
    Integer create(Operator operator, NewBusinessSideDto newBusinessSideDto);
    @Deprecated
    void delete(Operator operator, Integer businessSideId);

    List<SsaBusinessSideBaseDto> getBaseDtos();
}
