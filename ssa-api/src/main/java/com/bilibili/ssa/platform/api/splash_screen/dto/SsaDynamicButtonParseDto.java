package com.bilibili.ssa.platform.api.splash_screen.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/29 16:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaDynamicButtonParseDto implements Serializable {
    private static final long serialVersionUID = -3141591923839943152L;

    /**
     * 引导文案
     */
    private String guideInstructions;
    /**
     * 唤起文案
     */
    private String schemeCopyWriting;
    /**
     * 按钮
     */
    private List<SplashScreenDynamicButtonBO> buttons;
    /**
     * 这部分的跳转链接，其实是buttons中的splashScreenJumpDTOS的汇总
     *
     * @see #buttons
     */
    private List<SplashScreenJumpDTO> jumps;

}
