package com.bilibili.ssa.platform.api.business_side.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaUserDto implements Serializable{
    private static final long serialVersionUID = -2976936355137846928L;
    private Integer id;
    private String name;
}
