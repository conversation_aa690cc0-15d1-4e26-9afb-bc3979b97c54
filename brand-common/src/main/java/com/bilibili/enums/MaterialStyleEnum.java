package com.bilibili.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/5/27 16:00
 */
@Getter
public enum MaterialStyleEnum {
    UNKNOWN(-1, "未知"),
    IP_EGG(0, "IP浮层视频+全屏彩蛋"),
    LOTTERY(1, "抽卡"),
    ;
    private final Integer code;
    private final String desc;

    MaterialStyleEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MaterialStyleEnum getByCodeWithoutEx(Integer code) {
        for (MaterialStyleEnum value : MaterialStyleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static MaterialStyleEnum getByCode(Integer code) {
        for (MaterialStyleEnum value : MaterialStyleEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown code=" + code);
    }
}
