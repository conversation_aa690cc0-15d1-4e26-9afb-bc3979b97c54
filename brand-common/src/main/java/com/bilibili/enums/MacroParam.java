package com.bilibili.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@SuppressWarnings("all")
public enum MacroParam {

    BUVID("buvid", "__BUVID__", true),

    MID("mid", "__MID__", true),

    IMEI("imei", "__IMEI__", true),

    DUID("duid", "__DUID__", true),

    IDFA("idfa", "__IDFA__", true),

    ANDROID_ID("android_id", "__ANDROIDID__", true),

    OS("os", "__OS__", true),

    REQUEST_ID("request_id", "__REQUESTID__", true),

    SOURCE_ID("source_id", "__SOURCEID__", true),

    TRACK_ID("track_id", "__TRACKID__", true),

    CREATIVE_ID("creative_id", "__CREATIVEID__", true),

    ADTYPE("adtype", "__ADTYPE__", true);

    private final String key;

    private final String value;

    private final boolean isCommon;
}
