package com.bilibili.enums;

import lombok.Getter;

/**
 * 品牌起飞优化目标
 * <AUTHOR>
 * @date 2023/2/3
 */
@Getter
public enum  OcpxTargetEnum {

    NONE(0, "无"),
    VIDEO_PLAY(9,"稿件播放"),
    CLICK(41,"点击");


    private final int code;
    private final String desc;

    OcpxTargetEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OcpxTargetEnum getByCode(int code) {
        for (OcpxTargetEnum value : OcpxTargetEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
