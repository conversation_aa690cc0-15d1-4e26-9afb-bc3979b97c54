package com.bilibili.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum Gd3dModelBizSceneEnum {

    STORY_DROP_BOX(1, "story天降礼盒", Lists.newArrayList("box_model")),
    ;

    private final Integer code;

    private final String desc;

    private final List<String> supportModelTypeList;
}
