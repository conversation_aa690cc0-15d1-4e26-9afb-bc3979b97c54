package com.bilibili.enums;

import lombok.Getter;

/**
 * 刊例价价格加收类型
 *
 * <AUTHOR>
 * @date 2024/1/18 11:16
 */
@Getter
public enum PriceRaiseType {
    RATIO(1, "比例加收"),
    CONSTANT(2, "常量加收"),
    ;
    private Integer code;
    private String desc;

    PriceRaiseType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PriceRaiseType getByCode(Integer code) {
        for (PriceRaiseType type : PriceRaiseType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown price raise type:" + code);
    }
}
