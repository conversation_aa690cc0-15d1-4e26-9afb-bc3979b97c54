package com.bilibili.enums;

import lombok.Getter;

/**
 * 品牌稿件库处理状态
 *
 * <AUTHOR>
 * @date 2023/9/8 16:43
 */
@Getter
public enum BrandArchiveStatus {
    UNKNOWN(-1, "未知"),
    INIT(0, "待转码"),
    TRANSCODING(1, "转码中"),
    SUCCESS(2, "转码成功"),
    FAILED(3, "转码失败"),
    ;
    private Integer code;
    private String desc;

    BrandArchiveStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BrandArchiveStatus getByCodeWithoutEx(Integer code) {
        for (BrandArchiveStatus value : BrandArchiveStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static BrandArchiveStatus getByCode(Integer code) {
        for (BrandArchiveStatus value : BrandArchiveStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown code=" + code);
    }
}
