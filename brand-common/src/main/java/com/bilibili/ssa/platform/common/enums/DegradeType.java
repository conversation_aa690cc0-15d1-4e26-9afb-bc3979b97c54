package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 降级是否禁用滑动交互，放在含有特效的按钮上
 *
 * <AUTHOR>
 * @date 2022年2月16日
 */
@AllArgsConstructor
public enum DegradeType {
    DEFAULT(0, "默认降级"),
    OUTER(1, "外层素材去除动效"),
    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static DegradeType getByCode(int code) {
        for (DegradeType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code DegradeType " + code);
    }
}
