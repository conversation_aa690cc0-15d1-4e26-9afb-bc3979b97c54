package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年3月9日
 */
@AllArgsConstructor
public enum SsaSplashScreenImageStatus {

    SUCCESS(1, "成功"),
    FAILED(2, "失败");

    @Getter
    private Integer code;
    @Getter
    private String desc;


    public static SsaSplashScreenImageStatus getByCode(int code) {
        for (SsaSplashScreenImageStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code SsaSplashScreenImageStatus " + code);
    }


}
