package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年3月9日
 */
@AllArgsConstructor
public enum BookingRatio {


    HALF(500, "半轮闪屏"),
    FULL(1000, "满轮闪屏"),
    FIRST_BRUSH(2000, "首刷闪屏")
   ;
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static BookingRatio getByCode(int code) {
        for (BookingRatio bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code BookingRatio " + code);
    }
}
