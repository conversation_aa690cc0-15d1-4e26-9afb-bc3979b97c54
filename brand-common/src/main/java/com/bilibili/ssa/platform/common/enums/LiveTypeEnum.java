package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018/6/13
 **/
@AllArgsConstructor
public enum LiveTypeEnum {

    ACTIVITY(0, "活动直播"),
    E_SPORTS(1, "电竞直播");
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static LiveTypeEnum getByCode(int code) {
        for (LiveTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code IsSkipEnum " + code);
    }
}
