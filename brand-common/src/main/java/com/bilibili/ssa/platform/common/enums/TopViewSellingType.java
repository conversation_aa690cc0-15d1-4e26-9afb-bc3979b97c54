package com.bilibili.ssa.platform.common.enums;

public enum TopViewSellingType {
    COMMON(0, "常规TopView"),
    FIRST_BRUSH(1, "首刷TopView");

    private Integer code;
    private String desc;

    TopViewSellingType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static TopViewSellingType getByCode(int code) {
        for (TopViewSellingType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

    public static TopViewSellingType getByCodeWithValidation(int code) {
        for (TopViewSellingType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown TopViewSellingType code " + code);
    }
}
