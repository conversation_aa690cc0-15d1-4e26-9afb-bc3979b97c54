package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年3月9日
 */
@AllArgsConstructor
public enum ButtonBgColorStyle {


    BLACK(0, "#80000000", "#80000000"),
    WHITE(1, "#4DFFFFFF","#4DFFFFFF"),
   ;
    @Getter
    private final Integer code;
    @Getter
    private final String color;

    @Getter
    private final String nightColor;

    public static ButtonBgColorStyle getByCode(int code) {
        for (ButtonBgColorStyle bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        return ButtonBgColorStyle.BLACK;
    }
}
