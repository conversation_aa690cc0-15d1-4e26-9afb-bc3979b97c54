package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum UposProfileEnum {
	SSA_YB(1, "ssa/yb"),
    MGK_AV(2, "ssa/av"),
    SSA_BUP(3, "ssa/bup");
    @Getter
    private Integer code;
    @Getter
    private String profile;

    public static UposProfileEnum getByCode(int code) {
        for (UposProfileEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code UposProfileEnum " + code);
    }

    public static UposProfileEnum getByProfile(String profile) {
        for (UposProfileEnum bean : values()) {
            if (bean.getProfile().equals(profile)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown profile UposProfileEnum " + profile);
    }
}
