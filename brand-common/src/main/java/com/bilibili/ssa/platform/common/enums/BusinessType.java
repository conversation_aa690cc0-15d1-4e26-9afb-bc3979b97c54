package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022年2月16日
 */
@AllArgsConstructor
public enum BusinessType {
    ALL(0, "全部"),
    OUTER(1, "外广"),
    INNER(2, "内广");

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static BusinessType getByCode(int code) {
        for (BusinessType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code BusinessType " + code);
    }
}
