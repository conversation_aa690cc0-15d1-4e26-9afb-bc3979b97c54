package com.bilibili.ssa.platform.common.enums;

public enum SsaScreenStyle {
    // 闪屏全半屏样式
    FULL_SCREEN(1, "全屏"),
    HALF_SCREEN(2, "半屏");

    private final Integer code;
    private final String desc;

    SsaScreenStyle(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SsaScreenStyle getByCode(Integer code) {
        for (SsaScreenStyle style : values()) {
            if (style.getCode().equals(code)) {
                return style;
            }
        }
        return null;
    }

    public static SsaScreenStyle getByCodeWithValidation(Integer code) {
        for (SsaScreenStyle style : values()) {
            if (style.getCode().equals(code)) {
                return style;
            }
        }
        throw new IllegalArgumentException("unknown code SsaScreenStyle " + code);
    }
}
