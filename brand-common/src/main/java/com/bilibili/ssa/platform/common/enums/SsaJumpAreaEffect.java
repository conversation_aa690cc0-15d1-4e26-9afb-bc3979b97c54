package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018年5月24日
 */
@AllArgsConstructor
public enum SsaJumpAreaEffect {

    //跳转模块动效 0-无动效 1-跳动动效

	NO(0, "无动效"),
    LIGHT_EFFECT(1, "光效"),
    SPREAD_LINE(2, "扩散动效"),
    COLOR_REVERSE(3, "颜色翻转"),
    CUSTOM_EFFECT(4, "自定义动效"),
    SLIDE_EFFECT(5, "滑动动效"),
    WATER_WAVE(6, "水波动效"),
    SHARK(7, "摇一摇动效"),
    TWIST(8, "扭一扭动效"),

    /**
     * 由于滚动按钮产品不让和按钮类型放同一层级，所以前端只能通过这个字段去判断是否是扭一扭滚动按钮
     * 仅在vo层使用，到业务层以后转换为对应的按钮类型和无动效
     */
    TWIST_BRAND_SCROLL(9, "扭一扭品牌滚动特效（属于无动效）"),

    EGGS_LANDING(10, "空降彩蛋"),

    HEARTBEAT_BALLOONS(11, "惊喜气球"),
    LOTTIE_SCROLL(12, "lottie滚动"),
    ;
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static SsaJumpAreaEffect getByCode(Integer code) {
        for (SsaJumpAreaEffect bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code SsaJumpAreaEffect " + code);
    }

    public static SsaJumpAreaEffect getByCodeElse(Integer code, SsaJumpAreaEffect res) {
        for (SsaJumpAreaEffect bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return res;
    }
}
