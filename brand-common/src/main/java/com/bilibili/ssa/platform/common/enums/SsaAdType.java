package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018年5月24日
 */
@AllArgsConstructor
public enum SsaAdType {
    VIDEO(1, "视频"),
    IMAGE(2, "图片");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static SsaAdType getByCode(Integer code) {
        for (SsaAdType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code SsaAdType " + code);
    }

    public static SsaAdType getByCodeWithoutEx(Integer code) {
        for (SsaAdType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }

    public static boolean isImage(Integer adType) {
        return Objects.equals(IMAGE.getCode(), adType);
    }

    public static boolean isVideo(Integer adType) {
        return Objects.equals(VIDEO.getCode(), adType);
    }
}
