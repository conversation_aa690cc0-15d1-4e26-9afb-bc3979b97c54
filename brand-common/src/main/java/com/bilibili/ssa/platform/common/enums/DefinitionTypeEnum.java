package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum DefinitionTypeEnum {
	DEFAULT(0, 0),
    P480(1, 480),
    P1080(2, 1080);
    @Getter
    private Integer code;
    @Getter
    private Integer definition;

    public static DefinitionTypeEnum getByCode(int code) {
        for (DefinitionTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code DefinitionTypeEnum " + code);
    }

    public static DefinitionTypeEnum getByDefinition(int definition) {
        for (DefinitionTypeEnum bean : values()) {
            if (bean.getDefinition().equals(definition)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown definition DefinitionTypeEnum " + definition);
    }
}
