package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018年3月9日
 */
@AllArgsConstructor
public enum SsaSplashScreenStatus {

    TO_BE_FIRST_AUDIT(1, "待初核") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return INITIAL_AUDIT_CAN_TO.contains(toStatus);
        }
    },
    TO_BE_RE_AUDIT(2, "待复核") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return REVIEW_CAN_TO.contains(toStatus);
        }
    },
    AUDIT_REJECT(3, "已驳回") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return AUDIT_REJECT_CAN_TO.contains(toStatus);
        }
    },
    WAIT_ON_LINE(4, "待上线") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return WAIT_ON_LINE_CAN_TO.contains(toStatus);
        }
    },
    ON_LINE(5,"上线中") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return ON_LINE_CAN_TO.contains(toStatus);
        }
    },
    PAUSED(6, "已暂停") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return PAUSED_CAN_TO.contains(toStatus);
        }
    },
    COMPLETED(7, "已完成") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return COMPLETED_CAN_TO.contains(toStatus);
        }
    },
    DELETED(8, "已删除") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return false;
        }
    },
    VIDEO_DEAL(9, "视频处理中") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return false;
        }
    },
    VIDEO_DEAL_FAIL(10, "视频处理失败") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return false;
        }
    },
    TO_BE_THIRD_AUDIT(11, "待三审") {
        @Override
        public boolean validateToStatus(SsaSplashScreenStatus toStatus) {
            return THIRD_AUDIT_CAN_TO.contains(toStatus);
        }
    };

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public final static List<Integer> AUDIT_PASS_STATUS = Arrays.asList(
            WAIT_ON_LINE.getCode(),
            ON_LINE.getCode(),
            PAUSED.getCode(),
            COMPLETED.getCode()
    );

    public final static List<Integer> AUDIT_PASS_STATUS_ONLY_ONLINE = Arrays.asList(
            WAIT_ON_LINE.getCode(),
            ON_LINE.getCode()
    );


    public final static List<Integer> NOT_DELETED_STATUS = Arrays.asList(
            TO_BE_FIRST_AUDIT.getCode(),
            TO_BE_RE_AUDIT.getCode(),
            TO_BE_THIRD_AUDIT.getCode(),
            AUDIT_REJECT.getCode(),
            WAIT_ON_LINE.getCode(),
            ON_LINE.getCode(),
            PAUSED.getCode(),
            COMPLETED.getCode()
    );

    public final static List<Integer> CAN_TO_COMPLETED_STATUS = Arrays.asList(
            TO_BE_FIRST_AUDIT.getCode(),
            TO_BE_RE_AUDIT.getCode(),
            TO_BE_THIRD_AUDIT.getCode(),
            AUDIT_REJECT.getCode(),
            WAIT_ON_LINE.getCode(),
            ON_LINE.getCode(),
            PAUSED.getCode()
    );

    public final static List<Integer> CAN_NOT_MODIFY_STATUS = Arrays.asList(
            DELETED.getCode(),
            COMPLETED.getCode()
    );

    public final static List<Integer> ISSUED_TIME_RELEVANT_STATUS = Arrays.asList(
            WAIT_ON_LINE.getCode(),
            ON_LINE.getCode()
    );

    public final static List<Integer> NO_PREVIEW_STATUS_LIST = Arrays.asList(
            AUDIT_REJECT.getCode(),
            COMPLETED.getCode(),
            DELETED.getCode()
    );


    private final static EnumSet<SsaSplashScreenStatus> INITIAL_AUDIT_CAN_TO =
            EnumSet.of(AUDIT_REJECT, TO_BE_RE_AUDIT, COMPLETED, DELETED);

    private final static EnumSet<SsaSplashScreenStatus> REVIEW_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, AUDIT_REJECT, WAIT_ON_LINE, ON_LINE, COMPLETED, DELETED, TO_BE_THIRD_AUDIT);

    private final static EnumSet<SsaSplashScreenStatus> THIRD_AUDIT_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, AUDIT_REJECT, WAIT_ON_LINE, ON_LINE, COMPLETED, DELETED, TO_BE_RE_AUDIT);

    private final static EnumSet<SsaSplashScreenStatus> AUDIT_REJECT_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, TO_BE_RE_AUDIT, COMPLETED, DELETED, AUDIT_REJECT);

    private final static EnumSet<SsaSplashScreenStatus> WAIT_ON_LINE_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, AUDIT_REJECT, ON_LINE, PAUSED, COMPLETED, DELETED);

    private final static EnumSet<SsaSplashScreenStatus> ON_LINE_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, AUDIT_REJECT,PAUSED,WAIT_ON_LINE, COMPLETED, DELETED);

    private final static EnumSet<SsaSplashScreenStatus> PAUSED_CAN_TO =
            EnumSet.of(TO_BE_FIRST_AUDIT, AUDIT_REJECT, WAIT_ON_LINE, ON_LINE, COMPLETED, DELETED);

    private final static EnumSet<SsaSplashScreenStatus> COMPLETED_CAN_TO =
            EnumSet.of(DELETED);

    public static SsaSplashScreenStatus getByCode(int code) {
        for (SsaSplashScreenStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code SsaSplashScreenStatus " + code);
    }

    public static SsaSplashScreenStatus getByCodeWithoutEx(Integer code) {
        for (SsaSplashScreenStatus bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }

    public abstract boolean validateToStatus(SsaSplashScreenStatus toStatus);

}
