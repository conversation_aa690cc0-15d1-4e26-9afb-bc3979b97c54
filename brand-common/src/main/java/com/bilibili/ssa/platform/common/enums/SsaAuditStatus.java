package com.bilibili.ssa.platform.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2020年12月03日
 */
@AllArgsConstructor
public enum SsaAuditStatus {
	FIRST_AUDIT_PASS(0, "初审通过"),
    AUDIT_REJECT(1, "已驳回"),
    END_AUDIT_PASS(2, "复审通过"),
    THIRD_AUDIT_PASS(3, "三审通过"),
    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static SsaAuditStatus getByCode(Integer code) {
        for (SsaAuditStatus bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code SsaAuditStatus " + code);
    }
}
