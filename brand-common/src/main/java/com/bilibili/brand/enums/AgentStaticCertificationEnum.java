package com.bilibili.brand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/27
 **/

@Getter
@AllArgsConstructor
public enum AgentStaticCertificationEnum {

    EFFECT(1, "效果认证", "bilibili STAR 效果认证"),
    FIREWORKS(2, "花火认证", "bilibili STAR 花火认证"),
    ;

    private final Integer code;
    private final String name;
    private final String desc;

    public static AgentStaticCertificationEnum getByName(String name) {
        return Arrays.stream(values()).filter(t -> StringUtils.equals(t.getName(), name)).findFirst().orElse(null);
    }

    public static AgentStaticCertificationEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(t -> Objects.equals(t.getCode(), code)).findFirst().orElse(null);
    }

    public static Map<Integer, String> queryAll() {
        return Arrays.stream(values()).collect(Collectors.toMap(AgentStaticCertificationEnum::getCode, AgentStaticCertificationEnum::getDesc));
    }
}
