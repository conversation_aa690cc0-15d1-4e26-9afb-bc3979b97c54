package com.bilibili.brand.enums;

import com.bilibili.adp.common.util.Utils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-01-19 15:50:52
 * @description:
 **/

@Getter
@AllArgsConstructor
public enum AgentStaticQualificationAllEnum {

    CORE(1, "核心代理", AgentStaticQualificationEnum.CORE, null),
    INDUSTRY_GAME(2, "行业代理-游戏", AgentStaticQualificationEnum.INDUSTRY, AgentStaticQualificationSecondEnum.GAME),
//    INDUSTRY_TRIP(3, "行业代理-文旅", AgentStaticQualificationEnum.INDUSTRY, AgentStaticQualificationSecondEnum.TRIP),
//    INDUSTRY_CAR(4, "行业代理-汽车区域", AgentStaticQualificationEnum.INDUSTRY, AgentStaticQualificationSecondEnum.CAR),
//    INVESTMENT_AGENT(5, "招商代理", AgentStaticQualificationEnum.INVESTMENT_AGENT, null),
    AD(6, "广告合作代理", AgentStaticQualificationEnum.AD, null),
    SELL_GOOD(7, "带货服务商", AgentStaticQualificationEnum.SELL_GOOD, null),
//    BRAND(8, "品牌号服务商", AgentStaticQualificationEnum.BRAND, null),
//    SEARCH(9, "搜索代理", AgentStaticQualificationEnum.SEARCH, null),
//    UP_PLUS(10, "UP+代理", AgentStaticQualificationEnum.UP_PLUS, null),
    ;

    private final Integer code;
    private final String name;

    private final AgentStaticQualificationEnum firstEnum;

    private final AgentStaticQualificationSecondEnum secondEnum;

    public static AgentStaticQualificationAllEnum getByName(String name) {
        for (AgentStaticQualificationAllEnum bean : values()) {
            if (bean.getName().equals(name)) {
                return bean;
            }
        }
        return null;
    }

    public static AgentStaticQualificationAllEnum getByCode(Integer code) {
        for (AgentStaticQualificationAllEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }

    public static List<AgentStaticQualificationAllEnum> getByFirst(Integer firstCode) {
        if (!Utils.isPositive(firstCode)) {
            return Collections.emptyList();
        }
        List<AgentStaticQualificationAllEnum> result = Lists.newArrayList();
        for (AgentStaticQualificationAllEnum bean : values()) {
            if (bean.getFirstEnum().getCode().equals(firstCode)) {
                result.add(bean);
            }
        }
        return result;

    }

    public static List<AgentStaticQualificationAllEnum> getByEnum(Integer firstCode, Integer secondCode) {
        if (!Utils.isPositive(firstCode)) {
            return Lists.newArrayList();
        }
        for (AgentStaticQualificationAllEnum bean : values()) {
            if (bean.getFirstEnum().getCode().equals(firstCode)) {
                if (bean.getSecondEnum() == null) {
                    // 没有二级分类
                    return Lists.newArrayList(bean);
                } else if (!Utils.isPositive(secondCode)) {
                    // 有二级分类 但是没有传二级分类 查全部
                    return getByFirst(firstCode);
                } else if (bean.getSecondEnum().getCode().equals(secondCode)) {
                    return Lists.newArrayList(bean);
                }
            }
        }
        return Lists.newArrayList();
    }

}
