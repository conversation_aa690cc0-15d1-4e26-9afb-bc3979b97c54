package com.bilibili.brand.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/27
 **/
@Getter
@AllArgsConstructor
public enum AgentStaticCertificationTypeEnum {

    COMMON(0, "默认"),
    CONTENT(1, "内容型"),
    CREATIVE(2, "创策型"),
    GRASS(3, "种草型"),
    ;


    private final Integer code;
    private final String name;

    public static AgentStaticCertificationTypeEnum getByName(String name) {
        return Arrays.stream(values()).filter(t -> StringUtils.equals(t.getName(), name)).findFirst().orElse(null);
    }

    public static AgentStaticCertificationTypeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(t -> Objects.equals(t.getCode(), code)).findFirst().orElse(null);
    }

    public static Map<Integer, String> queryAll() {
        return Arrays.stream(values()).filter(e -> !Objects.equals(e, AgentStaticCertificationTypeEnum.COMMON)).collect(Collectors.toMap(AgentStaticCertificationTypeEnum::getCode, AgentStaticCertificationTypeEnum::getName));
    }
}
