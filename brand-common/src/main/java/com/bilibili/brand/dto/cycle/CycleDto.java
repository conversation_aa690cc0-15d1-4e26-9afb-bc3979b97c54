package com.bilibili.brand.dto.cycle;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/2/26 11:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CycleDto implements Serializable {
    private static final long serialVersionUID = 7931027521851259L;
    private Integer id;
    private String name;
}
