package com.bilibili.brand.dto.cycle;

import java.io.Serializable;

import com.bilibili.enums.PriceRaiseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 刊例价价格加收项
 * 暂时没用
 *
 * <AUTHOR>
 * @date 2024/1/18 11:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PriceRaiseItemDto implements Serializable {
    private static final long serialVersionUID = -4742332080620006623L;
    /**
     * 加收类型
     *
     * @see PriceRaiseType
     */
    private Integer type;
    /**
     * 加收值
     * 如果{@link #type}：
     * （1）等于{@link PriceRaiseType#RATIO}，则该字段是加收比例，比如加收30%，则value=30
     * （2）等于{@link PriceRaiseType#CONSTANT}，则该字段是加收绝对值，单位元，比如加收2w，则value=20000
     */
    private Integer value;
}
