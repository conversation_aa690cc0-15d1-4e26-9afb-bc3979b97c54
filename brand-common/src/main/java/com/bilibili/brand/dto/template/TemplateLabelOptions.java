package com.bilibili.brand.dto.template;

import com.bilibili.enums.TemplateLabel;
import com.google.common.collect.Lists;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/6 10:59
 */
public class TemplateLabelOptions implements Iterable<Map.Entry<String, Object>> {
    private final Map<String, Object> labelValueMap;

    private TemplateLabelOptions() {
        this.labelValueMap = new HashMap<>();
    }

    public Object getLabelValue(String labelKey) {
        return labelValueMap.get(labelKey);
    }

    public Object getLabelValue(TemplateLabel labelKey) {
        return labelValueMap.get(labelKey.getKey());
    }

    public List<String> getLabelKeys() {
        return Lists.newArrayList(labelValueMap.keySet());
    }

    public boolean isEmpty() {
        return labelValueMap.isEmpty();
    }

    public static TemplateOptionsBuilder newBuilder() {
        return new TemplateOptionsBuilder(new TemplateLabelOptions());
    }

    @NotNull
    @Override
    public Iterator<Map.Entry<String, Object>> iterator() {
        return this.labelValueMap.entrySet().iterator();
    }

    @Override
    public Spliterator<Map.Entry<String, Object>> spliterator() {
        return Iterable.super.spliterator();
    }


    public static class TemplateOptionsBuilder {
        private final TemplateLabelOptions templateLabelOptions;

        private TemplateOptionsBuilder(TemplateLabelOptions templateLabelOptions) {
            this.templateLabelOptions = templateLabelOptions;
        }

        public TemplateOptionsBuilder withOption(String labelKey, Object labelValue) {
            if (StringUtils.hasText(labelKey)) {
                //只有存在key才生效，屏蔽上游可能需要的判空逻辑，对调用方友好
                this.templateLabelOptions.labelValueMap.put(labelKey, labelValue);
            }
            return this;
        }

        public TemplateOptionsBuilder withOption(TemplateLabel label, Object labelValue) {
            this.templateLabelOptions.labelValueMap.put(label.getKey(), labelValue);
            return this;
        }

        public TemplateLabelOptions build() {
            return templateLabelOptions;
        }
    }
}
