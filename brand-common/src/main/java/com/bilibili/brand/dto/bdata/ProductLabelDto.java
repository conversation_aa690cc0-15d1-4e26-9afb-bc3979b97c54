package com.bilibili.brand.dto.bdata;

import com.fastobject.diff.DiffLog;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/27 21:17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductLabelDto implements Serializable {
    private static final long serialVersionUID = -6067864056318270734L;
    @DiffLog(name = "一级标签id")
    private Long firstProductLabelId;
    private String firstProductLabelName = "";
    @DiffLog(name = "二级标签id")
    private Long secondProductLabelId;
    private String secondProductLabelName = "";
}
