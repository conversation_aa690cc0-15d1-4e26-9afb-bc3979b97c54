package com.bilibili;

import com.bilibili.bvid.BVIDUtils;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by dailu<PERSON> on 2019/9/2 16:37
 *
 * <AUTHOR>
 */
public class CommonBvidUtils {

    public static final String bvIdPrefix= "BV1";
    public static String transferToBvId(Long avid) {
        if(avid ==null || avid<1){
            return "";
        }
        return BVIDUtils.avToBv(Long.valueOf(avid));
    }

    public static Long transferToAvid(String videoId){
        if(StringUtils.isEmpty(videoId)){
            return null;
        }
        if(StringUtils.isNumeric(videoId)){
            return  Long.valueOf(videoId);
        }
        if(videoId.toUpperCase().startsWith(bvIdPrefix)){
            return BVIDUtils.bvToAv(videoId);
        }
        return null;
    }
    public static String transferToBvIdUp(String videoId) {
        if(StringUtils.isEmpty(videoId)){
            return null;
        }
        long avid;
        if(StringUtils.isNumeric(videoId)){
            avid = Long.parseLong(videoId);
        }else {
            return videoId;
        }
        if(avid<1){
            return "";
        }
        return BVIDUtils.avToBv(avid);
    }

    public static String urlBvToAv(String jumpUrl) {
        if (StringUtils.isEmpty(jumpUrl)) {
            return "";
        }
        Pattern jumpUrlAvidPattern = Pattern.compile("^https?://((www|m).bilibili.com|b23.tv)(/video)?/(av|AV)(\\d+)/?");

        Matcher avidMatcher = jumpUrlAvidPattern.matcher(jumpUrl);
        if (avidMatcher.find()) {
            String avidPrefix = avidMatcher.group(4);
            String avidNum = avidMatcher.group(5);
            String bvid = BVIDUtils.avToBv(Long.valueOf(avidNum));
            String avid = avidPrefix + avidNum;
            jumpUrl = jumpUrl.replace(avid, bvid);
        }
        return jumpUrl;
    }
}
