package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否是Admin(0-否 1-是)
 * Created by <PERSON><PERSON><PERSON> on 2017/6/13.
 */
@AllArgsConstructor
public enum IsAdmin {
    NOT_ADMIN(0, "否"),
    ADMIN(1, "是");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static IsAdmin getByCode(int code) {
        for (IsAdmin bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code IsAdmin " + code);
    }
}
