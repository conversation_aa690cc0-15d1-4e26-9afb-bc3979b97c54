package com.bilibili.cpt.platform.common;



public enum VideoDealStatus {
    INIT(0, "处理中"),
    SUCCESS(1, "成功"),
    FAIL(2, "失败");

    private final Integer code;
    private final String name;


    VideoDealStatus(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static VideoDealStatus getByCode(int code) {

        for (VideoDealStatus auditStatus: values()) {
            if (auditStatus.code == code) {
                return auditStatus;
            }
        }
        
        return null;
    }

    public static VideoDealStatus getByCodeWithValidation(int code) {

        for (VideoDealStatus auditStatus: values()) {
            if (auditStatus.code == code) {
                return auditStatus;
            }
        }
        throw new IllegalArgumentException("unknown AuditStatus code " + code);
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
