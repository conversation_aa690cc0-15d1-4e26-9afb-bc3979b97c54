package com.bilibili.cpt.platform.common;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ResourceType {
    OTHER(0, "其他", null),
    BIG_CARD(1, "大卡", LocationType.BIG_CARD),
    SMALL_CARD(2, "小卡", LocationType.SMALL_CARD),
    UNDER_BOX_CARD(3, "框下", LocationType.UNDER_BOX),
    STORY_CARD(4, "story", LocationType.STORY),
    //ott-inline
    TV_CARD(5, "tv", LocationType.OTT_INLINE),

    PD(-1, "pd资源位", null),
    SSA(6, "闪屏", LocationType.SSA),
    PLAYER_DETAIL(7, "播放详情页-相关推荐", LocationType.RECOMMEND),
    TV_PAUSED(8, "TV-播放页-暂停广告", null),
    //动态暂时没用，后续会用到
    //CPT投了动态
    DYNAMIC(9, "动态", null),
    ;

    private final int code;
    private final String name;
    private final LocationType locationType;

    ResourceType(int code, String name, LocationType locationType) {
        this.code = code;
        this.name = name;
        this.locationType = locationType;
    }

    public static ResourceType getByCode(int code) {
        for (ResourceType auditStatus : values()) {
            if (auditStatus.code == code) {
                return auditStatus;
            }
        }

        return null;
    }

    public static ResourceType getByCodeWithValidation(Integer code) {

        for (ResourceType gdType : values()) {
            if (Objects.equals(gdType.code, code)) {
                return gdType;
            }
        }
        throw new IllegalArgumentException("unknown ResourceType code " + code);
    }

    public boolean isSupportNewStock(){
        return Objects.equals(locationType, LocationType.SMALL_CARD);
    }

}
