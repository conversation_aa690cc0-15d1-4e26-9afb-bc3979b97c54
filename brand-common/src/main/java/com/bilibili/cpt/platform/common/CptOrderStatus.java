package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 状态（0待审核 1正式 2删除）
 * Created by <PERSON><PERSON><PERSON> on 2017/6/12.
 */
@AllArgsConstructor
public enum CptOrderStatus {
    TO_BE_AUDIT(0, "待审核"),
    FORMAL(1, "正式"),
    DELETED(2, "删除"),
    EMPTY(3, "空订单");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static CptOrderStatus getByCode(int code) {
        for (CptOrderStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code CptOrderStatus " + code);
    }
}
