package com.bilibili.cpt.platform.common;

public enum ButtonStyle {
    SELECT(0, "文案选择式按钮"),
    CUSTOMIZE(1, "文案自定义式按钮");

    private final int code;
    private final String name;

    ButtonStyle(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static ButtonStyle getByCode(int code) {

        for (ButtonStyle style: values()) {
            if (style.code == code) {
                return style;
            }
        }
        
        return null;
    }

    public static ButtonStyle getByCodeWithValidation(int code) {

        for (ButtonStyle style: values()) {
            if (style.code == code) {
                return style;
            }
        }
        throw new IllegalArgumentException("unknown ButtonStyle code " + code);
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
