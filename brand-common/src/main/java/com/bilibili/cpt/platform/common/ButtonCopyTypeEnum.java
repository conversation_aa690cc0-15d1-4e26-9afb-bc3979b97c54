package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum ButtonCopyTypeEnum {
    JUMP_LINK(1, "跳转链接"),
    APP_DOWN_AWAKEN(2, "APP下载和唤醒"),
    LIVE_BOOKING(6, "直播预约"),
    GAME_DOWNLOAD(5, "游戏下载");
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public final static ButtonCopyTypeEnum getByCode(int code) {
        for (ButtonCopyTypeEnum type : ButtonCopyTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("unknown ButtonCopyTypeEnum code.");
    }



}
