/** 
* <AUTHOR> 
* @date  2017年10月17日
*/ 

package com.bilibili.cpt.platform.common;

import com.bilibili.ssa.platform.common.enums.DateGroupTypeEnum;
import lombok.Getter;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;

public enum GROUP_TYPE {
    WEEK {
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            calendar.setTimeInMillis(time);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            StringBuilder key = new StringBuilder();
            key.append(getDate(calendar.getTimeInMillis()));
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            key.append(" ~ ");
            key.append(getDate(calendar.getTimeInMillis()));
            return key.toString();
        }
    }, MONTH {
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            calendar.setTimeInMillis(time);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            StringBuilder key = new StringBuilder();
            key.append(getDate(calendar.getTimeInMillis()));
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            key.append(" ~ ");
            key.append(getDate(calendar.getTimeInMillis()));
            return key.toString();
        }
    }, DAY {
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            return getDate(time);
        }
    },
    DEFAULT {
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            if (null == fromTime || null == toTime) {
                return "";
            }
            return getDate(fromTime.getTime()) + " ~ " + getDate(toTime.getTime());
        }
    };
    @Getter
    private Integer code;

    public abstract String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime);
    
    private static String getDate(Long timeMiles) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(timeMiles);
    }

    public static GROUP_TYPE getByCode(Integer code) {
        switch (code) {
            case 1:
                return GROUP_TYPE.DAY;
            case 2:
                return GROUP_TYPE.WEEK;
            case 3:
                return GROUP_TYPE.MONTH;
            case 0:
                return GROUP_TYPE.DEFAULT;
            default:
                return GROUP_TYPE.DEFAULT;
        }
    }
}
