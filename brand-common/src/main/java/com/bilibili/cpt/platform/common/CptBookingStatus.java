package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-31
 **/
@AllArgsConstructor
public enum CptBookingStatus {
    //不在刊例时间范围内
    INVALID(-1, "无效的"),

    BOOKABLE(0, "可预约"),

    BOOKED(1, "已预约"),

    LOCKED(2, "已锁定"),

    BOOKED_SCHEDULED(3, "预约已排期"),

    LOCKED_SCHEDULED(4, "锁定已排期"),

    LESS_RESOURCE_TOP_VIEW(5, "不可预约"),

    PART_BOOKABLE(6, "部分可预约"),

    PART_BOOKED(7, "部分已预约"),

    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public final static List<Integer> BOOKED_STATUS_LIST = Arrays.asList(BOOKED.getCode(), LOCKED.getCode(),
            BOOKED_SCHEDULED.getCode(), LOCKED_SCHEDULED.getCode());

    public final static List<Integer> BOOKED_LOCKED_LIST = Arrays.asList(BOOKED.getCode(), LOCKED.getCode());

    public final static List<Integer> SCHEDULE_LOCKED_LIST = Arrays.asList(BOOKED_SCHEDULED.getCode(),
            LOCKED_SCHEDULED.getCode());

    public static CptBookingStatus getByCode(int code) {
        for (CptBookingStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code CptBookingStatus " + code);
    }


}
