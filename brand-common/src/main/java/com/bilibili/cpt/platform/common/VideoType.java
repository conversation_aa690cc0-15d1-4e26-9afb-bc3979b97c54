package com.bilibili.cpt.platform.common;

public enum VideoType {

    MGK_VIDEO(0, "mgk视频"),
    IP_VIDEO(1, "ip视频"),

    EGG_VIDEO(2, "彩蛋视频"),

    TOP_VIEW_3D_VIDEO(3, "topView3D破框视频"),
    ;

    private final Integer code;
    private final String name;

    VideoType(Integer code, String name) {

        this.code = code;
        this.name = name;
    }

    public static VideoType getByCode(int code) {

        for (VideoType flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        
        return null;
    }

    public static VideoType getByCodeWithValidation(int code) {

        for (VideoType flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        throw new IllegalArgumentException("unknown VideoType code " + code);
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
