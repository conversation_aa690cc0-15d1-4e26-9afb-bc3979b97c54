package com.bilibili.cpt.platform.common;

import com.google.common.collect.Lists;

import java.util.EnumSet;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年4月5日
 */
public enum LaunchStatus {
    START(1, "有效") {
        @Override
        public boolean isCanUpdate() {
            return true;
        }

        @Override
        public boolean isCanAudit() {
            return true;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return EnumSet.of(STOP, DELETE, FINISH, MODIFY_WAIT_OFF_LINE, OFFLINE).contains(toStatus);
        }
    },
    STOP(2, "暂停") {
        @Override
        public boolean isCanUpdate() {
            return true;
        }

        @Override
        public boolean isCanAudit() {
            return true;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return EnumSet.of(START, DELETE, FINISH, MODIFY_WAIT_OFF_LINE, OFFLINE).contains(toStatus);
        }
    },
    DELETE(3, "删除") {
        @Override
        public boolean isCanUpdate() {
            return false;
        }

        @Override
        public boolean isCanAudit() {
            return false;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return false;
        }
    },
    FINISH(4, "结束") {
        @Override
        public boolean isCanUpdate() {
            return false;
        }

        @Override
        public boolean isCanAudit() {
            return false;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return false;
        }
    },
    MODIFY_WAIT_OFF_LINE(5, "修改待下线") {
         @Override
         public boolean isCanUpdate() {
             return false;
         }

        @Override
        public boolean isCanAudit() {
            return false;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return EnumSet.of(DELETE, OFFLINE).contains(toStatus);
        }
    },
    OFFLINE(6, "已下线") {
         @Override
         public boolean isCanUpdate() {
             return false;
         }

        @Override
        public boolean isCanAudit() {
            return false;
        }

        @Override
        public boolean validateToStatus(LaunchStatus toStatus) {
            return false;
        }
    };

    private final int code;
    private final String name;


    public static final List<Integer> NO_PREVIEW_CREATIVE_STATUS_LIST = Lists.newArrayList(
            DELETE.getCode(),
            FINISH.getCode(),
            OFFLINE.getCode());

    public static final List<Integer> NO_DELETED_CREATIVE = Lists.newArrayList(
            START.getCode(),
            STOP.getCode(),
            MODIFY_WAIT_OFF_LINE.getCode(),
            FINISH.getCode());

    LaunchStatus(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static LaunchStatus getByCode(int code) {
        for (LaunchStatus status: values()) {
            if (status.code == code) {
                return status;
            }
        }

        throw new IllegalArgumentException("can not find LaunchStatus by code:" + code);
    }

    public int getCode() {
    return code;
    }

    public String getName() {
        return name;
    }

    public abstract boolean isCanUpdate();
    public abstract boolean isCanAudit();
    public abstract boolean validateToStatus(LaunchStatus toStatus);

 }
