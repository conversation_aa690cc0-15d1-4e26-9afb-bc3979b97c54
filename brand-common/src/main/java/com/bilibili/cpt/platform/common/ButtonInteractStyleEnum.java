package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2018年3月9日
 */
@AllArgsConstructor
public enum ButtonInteractStyleEnum {


    CLICK(0, "点击交互"),
    SLIDE(1, "滑动交互"),
    CLICK_SLIDE(2, "点击滑动交互"),
    WORD_WITHOUT_CLICK(3, "纯文字无交互"),
    SHARK(4, "摇一摇"),
    CLICK_SHARK(5, "点击摇一摇"),
    LOTTIE_WITHOUT_CLICK(6, "纯动效无交互"),
    TWIST(7, "扭一扭"),
    CLICK_TWIST(8, "点击扭一扭"),

    COUNT_DOWN(9, "倒计时组件"),

    TWIST_BRAND_SCROLL(10, "扭一扭品牌滚动"),

    HOLD_DOWN(11, "长按"),

    BRAND_CARD_TWIST(12, "品牌卡片扭一扭"),

    //暂时没启用
    BRAND_CARD_CLICK_TWIST(13, "品牌卡片点击扭一扭"),

    TOP_COUNT_DOWN(14, "顶部倒计时组件"),

    TWIST_EASTER_EGG_CLICKS(15, "扭一扭+彩蛋点击"),

    SLIDE_EASTER_EGG_CLICKS(16, "向上滑动+彩蛋点击"),

    TWIST_PRODUCT_CLICK(17, "扭一扭+商品按钮点击"),

    SLIDE_PRODUCT_CLICK(18, "向上滑动+商品按钮点击"),

    INTERACT_EGG_USER_NAME(19, "交互彩蛋+二屏用户名"),
    INTERACT_EGG_TEXT(20, "交互彩蛋+二屏文字）"),
    ;
    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static ButtonInteractStyleEnum getByCode(int code) {
        for (ButtonInteractStyleEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code InteractStyleEnum " + code);
    }

    //是否是组件
    public static boolean isComponent(Integer interactStyle) {
        return Objects.equals(COUNT_DOWN.getCode(), interactStyle)
                || Objects.equals(TOP_COUNT_DOWN.getCode(), interactStyle);
    }

    public static boolean isClickEgg(Integer interactStyle) {
        return Objects.equals(TWIST_EASTER_EGG_CLICKS.getCode(), interactStyle)
                || Objects.equals(SLIDE_EASTER_EGG_CLICKS.getCode(), interactStyle);
    }
}
