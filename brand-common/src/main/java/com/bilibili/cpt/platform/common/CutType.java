package com.bilibili.cpt.platform.common;

/*
 * 裁剪类型
 */
public enum CutType {

    DEFAULT(0, "按照自定义尺寸裁剪"),
    SHARE(1, "分享图片,按照原素材最短边1比1裁剪"),;

    private final Integer code;
    private final String name;

    CutType(Integer code, String name) {

        this.code = code;
        this.name = name;
    }

    public static CutType getByCode(int code) {

        for (CutType flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        
        return null;
    }

    public static CutType getByCodeWithValidation(int code) {

        for (CutType flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        throw new IllegalArgumentException("unknown CutType code " + code);
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
