package com.bilibili.cpt.platform.common;

public enum IsBookingDay {

    YES(0, "是"),
    NO(1, "否");

    private final Integer code;
    private final String name;

    IsBookingDay(Integer code, String name) {

        this.code = code;
        this.name = name;
    }

    public static IsBookingDay getByCode(int code) {

        for (IsBookingDay flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        
        return null;
    }

    public static IsBookingDay getByCodeWithValidation(int code) {

        for (IsBookingDay flag: values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        throw new IllegalArgumentException("unknown IsBookingDay code " + code);
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
