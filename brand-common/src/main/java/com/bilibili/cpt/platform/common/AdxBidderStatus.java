package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created by da<PERSON><PERSON><PERSON> on 2019/8/14 12:10
 *
 * <AUTHOR>
 */

@AllArgsConstructor
public enum AdxBidderStatus {
    VALID(1, "有效"),
    INVALID(2, "无效");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static AdxBidderStatus getByCode(int code) {
        for (AdxBidderStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code BidderStatus " + code);
    }
}


