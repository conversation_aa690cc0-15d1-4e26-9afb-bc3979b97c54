package com.bilibili.cpt.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * goblin的点位
 *
 * <AUTHOR>
 * @date 2025/3/11 20:52
 */
@Getter
@AllArgsConstructor
public enum LocationType {
    DEFAULT(0, "默认"),//没有实际意义
    SSA(1, "移动端闪屏"),
    BIG_CARD(2, "天马大卡"),
    SMALL_CARD(3, "天马小卡"),
    STORY(4, "story信息流"),
    OTT_SSA(5, "ott闪屏"),
    OTT_INLINE(6, "ott信息流"),
    UNDER_BOX(7, "播放详情页-框下"),
    RECOMMEND(8, "播放详情页-相关推荐");
    private final Integer code;
    private final String desc;
}
