package com.bilibili.utils;

import lombok.experimental.UtilityClass;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@UtilityClass
public class ArrayUtils {
    @SafeVarargs
    public <T> List<T> asList(T... a) {
        return Arrays.stream(a)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
