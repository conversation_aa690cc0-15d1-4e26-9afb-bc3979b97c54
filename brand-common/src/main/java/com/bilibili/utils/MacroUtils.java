package com.bilibili.utils;

import cn.hutool.core.net.url.UrlBuilder;
import com.bilibili.enums.MacroParam;

import java.util.Arrays;

public class MacroUtils {

    public static String appendCommonMacro(String baseUrl) {
        UrlBuilder urlBuilder = UrlBuilder.of(baseUrl);
        Arrays.stream(MacroParam.values())
                .filter(MacroParam::isCommon)
                .forEach(macroParam -> urlBuilder.addQuery(macroParam.getKey(), macroParam.getValue()));
        return urlBuilder.build();
    }

}
