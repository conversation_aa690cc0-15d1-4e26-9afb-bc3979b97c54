<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="soaOrderService" class="org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean">
        <property name="serviceUrl" value="http://cm.bilibili.co/pickup/api/service/soaOrderService"/>
        <property name="serviceInterface" value="com.bilibili.adpickup.soa.service.ISoaOrderService"/>
        <property name="httpInvokerRequestExecutor" ref="httpInvokerRequestExecutor"/>
    </bean>
</beans>