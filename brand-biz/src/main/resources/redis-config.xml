<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

<!--    <bean id="resourcePropertySource" class="org.springframework.core.io.support.ResourcePropertySource">-->
<!--        <constructor-arg name="name" value="spring-redis-template.properties"/>-->
<!--        <constructor-arg name="resource" value="classpath:/spring-redis-template.properties"/>-->
<!--    </bean>-->
<!--    -->
<!--    &lt;!&ndash;&lt;!&ndash; jedis 配置&ndash;&gt;&ndash;&gt;-->
<!--    <bean id="poolConfig" class="redis.clients.jedis.JedisPoolConfig" >-->
<!--        <property name="maxIdle" value="${spring.redis.maxIdle}" />-->
<!--        <property name="maxWaitMillis" value="${spring.redis.maxWaitMillis}" />-->
<!--        <property name="maxTotal" value="${spring.redis.maxTotal}"/>-->
<!--        <property name="minIdle" value="${spring.redis.minIdle}"/>-->
<!--    </bean >-->


<!--    &lt;!&ndash;&lt;!&ndash;redisCluster配置&ndash;&gt;&ndash;&gt;-->
<!--    <bean id="redisClusterConfiguration" class="org.springframework.data.redis.connection.RedisClusterConfiguration">-->
<!--        <constructor-arg name="propertySource" ref="resourcePropertySource"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash;&lt;!&ndash; redis服务器中心 &ndash;&gt;&ndash;&gt;-->
<!--    <bean id="connectionFactory"  class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory" >-->
<!--        <constructor-arg name="clusterConfig" ref="redisClusterConfiguration"/>-->
<!--        <constructor-arg name="poolConfig" ref="poolConfig"/>-->
<!--        <property name="timeout" value="${spring.redis.timeout}" ></property>-->
<!--    </bean >-->
<!--    <bean id="stringValueRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate" >-->
<!--        <property name="connectionFactory" ref="connectionFactory" />-->
<!--&lt;!&ndash;         &lt;!&ndash;如果不配置Serializer，那么存储的时候缺省使用String，如果用User类型存储，那么会提示错误User can't cast to String！！  &ndash;&gt; &ndash;&gt;-->
<!--        <property name="keySerializer" >-->
<!--            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />-->
<!--        </property>-->
<!--        <property name="valueSerializer" >-->
<!--            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer" />-->
<!--        </property>-->
<!--        <property name="hashKeySerializer">-->
<!--            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>-->
<!--        </property>-->
<!--        <property name="hashValueSerializer">-->
<!--            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer"/>-->
<!--        </property>-->
<!--    </bean>-->
<!--    -->
<!--    <bean id="integerValueRedisTemplate" class="org.springframework.data.redis.core.RedisTemplate" >-->
<!--        <property name="connectionFactory" ref="connectionFactory" />-->
<!--&lt;!&ndash;         &lt;!&ndash;如果不配置Serializer，那么存储的时候缺省使用String，如果用User类型存储，那么会提示错误User can't cast to String！！  &ndash;&gt; &ndash;&gt;-->
<!--        <property name="keySerializer" >-->
<!--            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer" />-->
<!--        </property>-->
<!--        <property name="valueSerializer" >-->
<!--            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer" />-->
<!--        </property>-->
<!--        <property name="hashKeySerializer">-->
<!--            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>-->
<!--        </property>-->
<!--        <property name="hashValueSerializer">-->
<!--            <bean class="org.springframework.data.redis.serializer.JdkSerializationRedisSerializer"/>-->
<!--        </property>-->
<!--    </bean>-->
</beans>
