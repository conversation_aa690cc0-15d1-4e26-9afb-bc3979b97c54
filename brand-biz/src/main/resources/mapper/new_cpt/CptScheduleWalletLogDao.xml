<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.CptScheduleWalletLogDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_side_id" jdbcType="INTEGER" property="businessSideId" />
    <result column="cpt_schedule_id" jdbcType="INTEGER" property="cptScheduleId" />
    <result column="cash" jdbcType="BIGINT" property="cash" />
    <result column="red_packet" jdbcType="BIGINT" property="redPacket" />
    <result column="schedule_date" jdbcType="DATE" property="scheduleDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="gd_schedule_id" jdbcType="INTEGER" property="gdScheduleId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_side_id, cpt_schedule_id, cash, red_packet, schedule_date, is_deleted, 
    ctime, mtime, gd_schedule_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cpt_schedule_wallet_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cpt_schedule_wallet_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from cpt_schedule_wallet_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPoExample">
    delete from cpt_schedule_wallet_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_schedule_wallet_log (business_side_id, cpt_schedule_id, cash, 
      red_packet, schedule_date, is_deleted, 
      ctime, mtime, gd_schedule_id
      )
    values (#{businessSideId,jdbcType=INTEGER}, #{cptScheduleId,jdbcType=INTEGER}, #{cash,jdbcType=BIGINT}, 
      #{redPacket,jdbcType=BIGINT}, #{scheduleDate,jdbcType=DATE}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{gdScheduleId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_schedule_wallet_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        business_side_id,
      </if>
      <if test="cptScheduleId != null">
        cpt_schedule_id,
      </if>
      <if test="cash != null">
        cash,
      </if>
      <if test="redPacket != null">
        red_packet,
      </if>
      <if test="scheduleDate != null">
        schedule_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="cptScheduleId != null">
        #{cptScheduleId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="scheduleDate != null">
        #{scheduleDate,jdbcType=DATE},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPoExample" resultType="java.lang.Long">
    select count(*) from cpt_schedule_wallet_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cpt_schedule_wallet_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessSideId != null">
        business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      </if>
      <if test="record.cptScheduleId != null">
        cpt_schedule_id = #{record.cptScheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.cash != null">
        cash = #{record.cash,jdbcType=BIGINT},
      </if>
      <if test="record.redPacket != null">
        red_packet = #{record.redPacket,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleDate != null">
        schedule_date = #{record.scheduleDate,jdbcType=DATE},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gdScheduleId != null">
        gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cpt_schedule_wallet_log
    set id = #{record.id,jdbcType=INTEGER},
      business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      cpt_schedule_id = #{record.cptScheduleId,jdbcType=INTEGER},
      cash = #{record.cash,jdbcType=BIGINT},
      red_packet = #{record.redPacket,jdbcType=BIGINT},
      schedule_date = #{record.scheduleDate,jdbcType=DATE},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    update cpt_schedule_wallet_log
    <set>
      <if test="businessSideId != null">
        business_side_id = #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="cptScheduleId != null">
        cpt_schedule_id = #{cptScheduleId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        cash = #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        red_packet = #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="scheduleDate != null">
        schedule_date = #{scheduleDate,jdbcType=DATE},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdScheduleId != null">
        gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    update cpt_schedule_wallet_log
    set business_side_id = #{businessSideId,jdbcType=INTEGER},
      cpt_schedule_id = #{cptScheduleId,jdbcType=INTEGER},
      cash = #{cash,jdbcType=BIGINT},
      red_packet = #{redPacket,jdbcType=BIGINT},
      schedule_date = #{scheduleDate,jdbcType=DATE},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_schedule_wallet_log (business_side_id, cpt_schedule_id, cash, 
      red_packet, schedule_date, is_deleted, 
      ctime, mtime, gd_schedule_id
      )
    values (#{businessSideId,jdbcType=INTEGER}, #{cptScheduleId,jdbcType=INTEGER}, #{cash,jdbcType=BIGINT}, 
      #{redPacket,jdbcType=BIGINT}, #{scheduleDate,jdbcType=DATE}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{gdScheduleId,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      business_side_id = values(business_side_id),
      cpt_schedule_id = values(cpt_schedule_id),
      cash = values(cash),
      red_packet = values(red_packet),
      schedule_date = values(schedule_date),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      gd_schedule_id = values(gd_schedule_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      cpt_schedule_wallet_log
      (business_side_id,cpt_schedule_id,cash,red_packet,schedule_date,is_deleted,ctime,mtime,gd_schedule_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.businessSideId,jdbcType=INTEGER},
        #{item.cptScheduleId,jdbcType=INTEGER},
        #{item.cash,jdbcType=BIGINT},
        #{item.redPacket,jdbcType=BIGINT},
        #{item.scheduleDate,jdbcType=DATE},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.gdScheduleId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      cpt_schedule_wallet_log
      (business_side_id,cpt_schedule_id,cash,red_packet,schedule_date,is_deleted,ctime,mtime,gd_schedule_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.businessSideId,jdbcType=INTEGER},
        #{item.cptScheduleId,jdbcType=INTEGER},
        #{item.cash,jdbcType=BIGINT},
        #{item.redPacket,jdbcType=BIGINT},
        #{item.scheduleDate,jdbcType=DATE},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.gdScheduleId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      business_side_id = values(business_side_id),
      cpt_schedule_id = values(cpt_schedule_id),
      cash = values(cash),
      red_packet = values(red_packet),
      schedule_date = values(schedule_date),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      gd_schedule_id = values(gd_schedule_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.cpt.platform.biz.po.CptScheduleWalletLogPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_schedule_wallet_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        business_side_id,
      </if>
      <if test="cptScheduleId != null">
        cpt_schedule_id,
      </if>
      <if test="cash != null">
        cash,
      </if>
      <if test="redPacket != null">
        red_packet,
      </if>
      <if test="scheduleDate != null">
        schedule_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="cptScheduleId != null">
        #{cptScheduleId,jdbcType=INTEGER},
      </if>
      <if test="cash != null">
        #{cash,jdbcType=BIGINT},
      </if>
      <if test="redPacket != null">
        #{redPacket,jdbcType=BIGINT},
      </if>
      <if test="scheduleDate != null">
        #{scheduleDate,jdbcType=DATE},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="businessSideId != null">
        business_side_id = values(business_side_id),
      </if>
      <if test="cptScheduleId != null">
        cpt_schedule_id = values(cpt_schedule_id),
      </if>
      <if test="cash != null">
        cash = values(cash),
      </if>
      <if test="redPacket != null">
        red_packet = values(red_packet),
      </if>
      <if test="scheduleDate != null">
        schedule_date = values(schedule_date),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="gdScheduleId != null">
        gd_schedule_id = values(gd_schedule_id),
      </if>
    </trim>
  </insert>
</mapper>