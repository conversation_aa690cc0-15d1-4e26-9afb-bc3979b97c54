<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.CptBusinessSideRedPacketLimitDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_side_id" jdbcType="INTEGER" property="businessSideId" />
    <result column="effective_date" jdbcType="DATE" property="effectiveDate" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_side_id, effective_date, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cpt_business_side_red_packet_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cpt_business_side_red_packet_limit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from cpt_business_side_red_packet_limit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPoExample">
    delete from cpt_business_side_red_packet_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_business_side_red_packet_limit (business_side_id, effective_date, ctime, 
      mtime, is_deleted)
    values (#{businessSideId,jdbcType=INTEGER}, #{effectiveDate,jdbcType=DATE}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_business_side_red_packet_limit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        business_side_id,
      </if>
      <if test="effectiveDate != null">
        effective_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessSideId != null">
        #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=DATE},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPoExample" resultType="java.lang.Long">
    select count(*) from cpt_business_side_red_packet_limit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cpt_business_side_red_packet_limit
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessSideId != null">
        business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      </if>
      <if test="record.effectiveDate != null">
        effective_date = #{record.effectiveDate,jdbcType=DATE},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cpt_business_side_red_packet_limit
    set id = #{record.id,jdbcType=INTEGER},
      business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      effective_date = #{record.effectiveDate,jdbcType=DATE},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPo">
    update cpt_business_side_red_packet_limit
    <set>
      <if test="businessSideId != null">
        business_side_id = #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="effectiveDate != null">
        effective_date = #{effectiveDate,jdbcType=DATE},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.cpt.platform.biz.po.CptBusinessSideRedPacketLimitPo">
    update cpt_business_side_red_packet_limit
    set business_side_id = #{businessSideId,jdbcType=INTEGER},
      effective_date = #{effectiveDate,jdbcType=DATE},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>