<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.CptSourceDayBookingLiveExtDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="platform_id" jdbcType="INTEGER" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="page_id" jdbcType="INTEGER" property="pageId" />
    <result column="page_name" jdbcType="VARCHAR" property="pageName" />
    <result column="resource_id" jdbcType="INTEGER" property="resourceId" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="group_date" jdbcType="DATE" property="groupDate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="business_side_id" jdbcType="INTEGER" property="businessSideId" />
    <result column="cpt_order_id" jdbcType="INTEGER" property="cptOrderId" />
    <result column="cpt_schedule_id" jdbcType="INTEGER" property="cptScheduleId" />
    <result column="cycle_id" jdbcType="INTEGER" property="cycleId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="is_booking_day" jdbcType="TINYINT" property="isBookingDay" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_id, platform_name, page_id, page_name, resource_id, resource_name, source_id, 
    source_name, group_date, status, business_side_id, cpt_order_id, cpt_schedule_id, 
    cycle_id, is_deleted, is_booking_day, ctime, mtime
  </sql>

  <select id="selectOneCycle" parameterType="com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePoExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from cpt_source_day_booking_live
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    group by source_id, group_date
  </select>
  <select id="selectMaxId"  parameterType="com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePoExample" resultType="java.lang.Integer">
    select max(id)
    from cpt_source_day_booking_live
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="selectMinId"  parameterType="com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePoExample" resultType="java.lang.Integer">
    select min(id)
    from cpt_source_day_booking_live
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
</mapper>