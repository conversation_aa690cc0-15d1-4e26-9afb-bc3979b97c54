<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.OgvBrandContentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="cid" jdbcType="BIGINT" property="cid" />
    <result column="ad_start_time" jdbcType="VARCHAR" property="adStartTime" />
    <result column="ad_end_time" jdbcType="VARCHAR" property="adEndTime" />
    <result column="brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <id column="ogv_brand_content_id" jdbcType="BIGINT" property="id" />
    <result column="ogv_brand_content_avid" jdbcType="BIGINT" property="avid" />
    <result column="ogv_brand_content_cid" jdbcType="BIGINT" property="cid" />
    <result column="ogv_brand_content_ad_start_time" jdbcType="VARCHAR" property="adStartTime" />
    <result column="ogv_brand_content_ad_end_time" jdbcType="VARCHAR" property="adEndTime" />
    <result column="ogv_brand_content_brand_id" jdbcType="BIGINT" property="brandId" />
    <result column="ogv_brand_content_brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="ogv_brand_content_status" jdbcType="TINYINT" property="status" />
    <result column="ogv_brand_content_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ogv_brand_content_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ogv_brand_content_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ogv_brand_content_id, ${alias}.avid as ogv_brand_content_avid, ${alias}.cid as ogv_brand_content_cid, 
    ${alias}.ad_start_time as ogv_brand_content_ad_start_time, ${alias}.ad_end_time as ogv_brand_content_ad_end_time, 
    ${alias}.brand_id as ogv_brand_content_brand_id, ${alias}.brand_name as ogv_brand_content_brand_name, 
    ${alias}.status as ogv_brand_content_status, ${alias}.is_deleted as ogv_brand_content_is_deleted, 
    ${alias}.ctime as ogv_brand_content_ctime, ${alias}.mtime as ogv_brand_content_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, avid, cid, ad_start_time, ad_end_time, brand_id, brand_name, status, is_deleted, 
    ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ogv_brand_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ogv_brand_content
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ogv_brand_content
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPoExample">
    delete from ogv_brand_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_brand_content (avid, cid, ad_start_time, 
      ad_end_time, brand_id, brand_name, 
      status, is_deleted, ctime, 
      mtime)
    values (#{avid,jdbcType=BIGINT}, #{cid,jdbcType=BIGINT}, #{adStartTime,jdbcType=VARCHAR}, 
      #{adEndTime,jdbcType=VARCHAR}, #{brandId,jdbcType=BIGINT}, #{brandName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_brand_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        avid,
      </if>
      <if test="cid != null">
        cid,
      </if>
      <if test="adStartTime != null">
        ad_start_time,
      </if>
      <if test="adEndTime != null">
        ad_end_time,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="cid != null">
        #{cid,jdbcType=BIGINT},
      </if>
      <if test="adStartTime != null">
        #{adStartTime,jdbcType=VARCHAR},
      </if>
      <if test="adEndTime != null">
        #{adEndTime,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPoExample" resultType="java.lang.Long">
    select count(*) from ogv_brand_content
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ogv_brand_content
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.cid != null">
        cid = #{record.cid,jdbcType=BIGINT},
      </if>
      <if test="record.adStartTime != null">
        ad_start_time = #{record.adStartTime,jdbcType=VARCHAR},
      </if>
      <if test="record.adEndTime != null">
        ad_end_time = #{record.adEndTime,jdbcType=VARCHAR},
      </if>
      <if test="record.brandId != null">
        brand_id = #{record.brandId,jdbcType=BIGINT},
      </if>
      <if test="record.brandName != null">
        brand_name = #{record.brandName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ogv_brand_content
    set id = #{record.id,jdbcType=BIGINT},
      avid = #{record.avid,jdbcType=BIGINT},
      cid = #{record.cid,jdbcType=BIGINT},
      ad_start_time = #{record.adStartTime,jdbcType=VARCHAR},
      ad_end_time = #{record.adEndTime,jdbcType=VARCHAR},
      brand_id = #{record.brandId,jdbcType=BIGINT},
      brand_name = #{record.brandName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    update ogv_brand_content
    <set>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="cid != null">
        cid = #{cid,jdbcType=BIGINT},
      </if>
      <if test="adStartTime != null">
        ad_start_time = #{adStartTime,jdbcType=VARCHAR},
      </if>
      <if test="adEndTime != null">
        ad_end_time = #{adEndTime,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        brand_id = #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        brand_name = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    update ogv_brand_content
    set avid = #{avid,jdbcType=BIGINT},
      cid = #{cid,jdbcType=BIGINT},
      ad_start_time = #{adStartTime,jdbcType=VARCHAR},
      ad_end_time = #{adEndTime,jdbcType=VARCHAR},
      brand_id = #{brandId,jdbcType=BIGINT},
      brand_name = #{brandName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_brand_content (avid, cid, ad_start_time, 
      ad_end_time, brand_id, brand_name, 
      status, is_deleted, ctime, 
      mtime)
    values (#{avid,jdbcType=BIGINT}, #{cid,jdbcType=BIGINT}, #{adStartTime,jdbcType=VARCHAR}, 
      #{adEndTime,jdbcType=VARCHAR}, #{brandId,jdbcType=BIGINT}, #{brandName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      avid = values(avid),
      cid = values(cid),
      ad_start_time = values(ad_start_time),
      ad_end_time = values(ad_end_time),
      brand_id = values(brand_id),
      brand_name = values(brand_name),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ogv_brand_content
      (avid,cid,ad_start_time,ad_end_time,brand_id,brand_name,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.avid,jdbcType=BIGINT},
        #{item.cid,jdbcType=BIGINT},
        #{item.adStartTime,jdbcType=VARCHAR},
        #{item.adEndTime,jdbcType=VARCHAR},
        #{item.brandId,jdbcType=BIGINT},
        #{item.brandName,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ogv_brand_content
      (avid,cid,ad_start_time,ad_end_time,brand_id,brand_name,status,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.avid,jdbcType=BIGINT},
        #{item.cid,jdbcType=BIGINT},
        #{item.adStartTime,jdbcType=VARCHAR},
        #{item.adEndTime,jdbcType=VARCHAR},
        #{item.brandId,jdbcType=BIGINT},
        #{item.brandName,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      avid = values(avid),
      cid = values(cid),
      ad_start_time = values(ad_start_time),
      ad_end_time = values(ad_end_time),
      brand_id = values(brand_id),
      brand_name = values(brand_name),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.cpt.platform.biz.po.OgvBrandContentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_brand_content
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        avid,
      </if>
      <if test="cid != null">
        cid,
      </if>
      <if test="adStartTime != null">
        ad_start_time,
      </if>
      <if test="adEndTime != null">
        ad_end_time,
      </if>
      <if test="brandId != null">
        brand_id,
      </if>
      <if test="brandName != null">
        brand_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="cid != null">
        #{cid,jdbcType=BIGINT},
      </if>
      <if test="adStartTime != null">
        #{adStartTime,jdbcType=VARCHAR},
      </if>
      <if test="adEndTime != null">
        #{adEndTime,jdbcType=VARCHAR},
      </if>
      <if test="brandId != null">
        #{brandId,jdbcType=BIGINT},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="cid != null">
        cid = values(cid),
      </if>
      <if test="adStartTime != null">
        ad_start_time = values(ad_start_time),
      </if>
      <if test="adEndTime != null">
        ad_end_time = values(ad_end_time),
      </if>
      <if test="brandId != null">
        brand_id = values(brand_id),
      </if>
      <if test="brandName != null">
        brand_name = values(brand_name),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>