<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.CptCrowdPackDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="gd_schedule_id" jdbcType="INTEGER" property="gdScheduleId" />
    <result column="crowd_pack_id" jdbcType="INTEGER" property="crowdPackId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="type" jdbcType="TINYINT" property="type" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, gd_schedule_id, crowd_pack_id, ctime, mtime, status, is_deleted, type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cpt_crowd_pack
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cpt_crowd_pack
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from cpt_crowd_pack
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPoExample">
    delete from cpt_crowd_pack
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_crowd_pack (gd_schedule_id, crowd_pack_id, ctime, 
      mtime, status, is_deleted, 
      type)
    values (#{gdScheduleId,jdbcType=INTEGER}, #{crowdPackId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_crowd_pack
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
      <if test="crowdPackId != null">
        crowd_pack_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="crowdPackId != null">
        #{crowdPackId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPoExample" resultType="java.lang.Long">
    select count(*) from cpt_crowd_pack
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update cpt_crowd_pack
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.gdScheduleId != null">
        gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.crowdPackId != null">
        crowd_pack_id = #{record.crowdPackId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update cpt_crowd_pack
    set id = #{record.id,jdbcType=INTEGER},
      gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER},
      crowd_pack_id = #{record.crowdPackId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      type = #{record.type,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    update cpt_crowd_pack
    <set>
      <if test="gdScheduleId != null">
        gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="crowdPackId != null">
        crowd_pack_id = #{crowdPackId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    update cpt_crowd_pack
    set gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER},
      crowd_pack_id = #{crowdPackId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_crowd_pack (gd_schedule_id, crowd_pack_id, ctime, 
      mtime, status, is_deleted, 
      type)
    values (#{gdScheduleId,jdbcType=INTEGER}, #{crowdPackId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      gd_schedule_id = values(gd_schedule_id),
      crowd_pack_id = values(crowd_pack_id),
      ctime = values(ctime),
      mtime = values(mtime),
      status = values(status),
      is_deleted = values(is_deleted),
      type = values(type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      cpt_crowd_pack
      (gd_schedule_id,crowd_pack_id,ctime,mtime,status,is_deleted,type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gdScheduleId,jdbcType=INTEGER},
        #{item.crowdPackId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      cpt_crowd_pack
      (gd_schedule_id,crowd_pack_id,ctime,mtime,status,is_deleted,type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gdScheduleId,jdbcType=INTEGER},
        #{item.crowdPackId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.type,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      gd_schedule_id = values(gd_schedule_id),
      crowd_pack_id = values(crowd_pack_id),
      ctime = values(ctime),
      mtime = values(mtime),
      status = values(status),
      is_deleted = values(is_deleted),
      type = values(type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.cpt.platform.biz.po.CptCrowdPackPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into cpt_crowd_pack
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
      <if test="crowdPackId != null">
        crowd_pack_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="type != null">
        type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="crowdPackId != null">
        #{crowdPackId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id = values(gd_schedule_id),
      </if>
      <if test="crowdPackId != null">
        crowd_pack_id = values(crowd_pack_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
    </trim>
  </insert>
</mapper>