<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.cpt.platform.biz.dao.CreativeDrawGestureDao">
  <resultMap id="BaseResultMap" type="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="guide_image" jdbcType="VARCHAR" property="guideImage" />
    <result column="guide_image_md5" jdbcType="VARCHAR" property="guideImageMd5" />
    <result column="width" jdbcType="REAL" property="width" />
    <result column="height" jdbcType="REAL" property="height" />
    <result column="coor_x" jdbcType="INTEGER" property="coorX" />
    <result column="coor_y" jdbcType="INTEGER" property="coorY" />
    <result column="draw_direction" jdbcType="TINYINT" property="drawDirection" />
    <result column="ios_start_angle" jdbcType="INTEGER" property="iosStartAngle" />
    <result column="ios_end_angle" jdbcType="INTEGER" property="iosEndAngle" />
    <result column="ios_draw_length" jdbcType="INTEGER" property="iosDrawLength" />
    <result column="android_start_angle" jdbcType="INTEGER" property="androidStartAngle" />
    <result column="android_end_angle" jdbcType="INTEGER" property="androidEndAngle" />
    <result column="android_draw_length" jdbcType="INTEGER" property="androidDrawLength" />
    <result column="dur_time" jdbcType="INTEGER" property="durTime" />
    <result column="active_times" jdbcType="VARCHAR" property="activeTimes" />
    <result column="action_type" jdbcType="TINYINT" property="actionType" />
    <result column="ext" jdbcType="VARCHAR" property="ext" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, guide_image, guide_image_md5, width, height, coor_x, coor_y, draw_direction, 
    ios_start_angle, ios_end_angle, ios_draw_length, android_start_angle, android_end_angle, 
    android_draw_length, dur_time, active_times, action_type, ext, is_deleted, ctime, 
    mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from creative_draw_gesture
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from creative_draw_gesture
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from creative_draw_gesture
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePoExample">
    delete from creative_draw_gesture
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_draw_gesture (creative_id, guide_image, guide_image_md5, 
      width, height, coor_x, coor_y, 
      draw_direction, ios_start_angle, ios_end_angle, 
      ios_draw_length, android_start_angle, android_end_angle, 
      android_draw_length, dur_time, active_times, 
      action_type, ext, is_deleted, 
      ctime, mtime)
    values (#{creativeId,jdbcType=BIGINT}, #{guideImage,jdbcType=VARCHAR}, #{guideImageMd5,jdbcType=VARCHAR}, 
      #{width,jdbcType=REAL}, #{height,jdbcType=REAL}, #{coorX,jdbcType=INTEGER}, #{coorY,jdbcType=INTEGER}, 
      #{drawDirection,jdbcType=TINYINT}, #{iosStartAngle,jdbcType=INTEGER}, #{iosEndAngle,jdbcType=INTEGER}, 
      #{iosDrawLength,jdbcType=INTEGER}, #{androidStartAngle,jdbcType=INTEGER}, #{androidEndAngle,jdbcType=INTEGER}, 
      #{androidDrawLength,jdbcType=INTEGER}, #{durTime,jdbcType=INTEGER}, #{activeTimes,jdbcType=VARCHAR}, 
      #{actionType,jdbcType=TINYINT}, #{ext,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_draw_gesture
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="guideImage != null">
        guide_image,
      </if>
      <if test="guideImageMd5 != null">
        guide_image_md5,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="coorX != null">
        coor_x,
      </if>
      <if test="coorY != null">
        coor_y,
      </if>
      <if test="drawDirection != null">
        draw_direction,
      </if>
      <if test="iosStartAngle != null">
        ios_start_angle,
      </if>
      <if test="iosEndAngle != null">
        ios_end_angle,
      </if>
      <if test="iosDrawLength != null">
        ios_draw_length,
      </if>
      <if test="androidStartAngle != null">
        android_start_angle,
      </if>
      <if test="androidEndAngle != null">
        android_end_angle,
      </if>
      <if test="androidDrawLength != null">
        android_draw_length,
      </if>
      <if test="durTime != null">
        dur_time,
      </if>
      <if test="activeTimes != null">
        active_times,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="guideImage != null">
        #{guideImage,jdbcType=VARCHAR},
      </if>
      <if test="guideImageMd5 != null">
        #{guideImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=REAL},
      </if>
      <if test="height != null">
        #{height,jdbcType=REAL},
      </if>
      <if test="coorX != null">
        #{coorX,jdbcType=INTEGER},
      </if>
      <if test="coorY != null">
        #{coorY,jdbcType=INTEGER},
      </if>
      <if test="drawDirection != null">
        #{drawDirection,jdbcType=TINYINT},
      </if>
      <if test="iosStartAngle != null">
        #{iosStartAngle,jdbcType=INTEGER},
      </if>
      <if test="iosEndAngle != null">
        #{iosEndAngle,jdbcType=INTEGER},
      </if>
      <if test="iosDrawLength != null">
        #{iosDrawLength,jdbcType=INTEGER},
      </if>
      <if test="androidStartAngle != null">
        #{androidStartAngle,jdbcType=INTEGER},
      </if>
      <if test="androidEndAngle != null">
        #{androidEndAngle,jdbcType=INTEGER},
      </if>
      <if test="androidDrawLength != null">
        #{androidDrawLength,jdbcType=INTEGER},
      </if>
      <if test="durTime != null">
        #{durTime,jdbcType=INTEGER},
      </if>
      <if test="activeTimes != null">
        #{activeTimes,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=TINYINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePoExample" resultType="java.lang.Long">
    select count(*) from creative_draw_gesture
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update creative_draw_gesture
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.guideImage != null">
        guide_image = #{record.guideImage,jdbcType=VARCHAR},
      </if>
      <if test="record.guideImageMd5 != null">
        guide_image_md5 = #{record.guideImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=REAL},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=REAL},
      </if>
      <if test="record.coorX != null">
        coor_x = #{record.coorX,jdbcType=INTEGER},
      </if>
      <if test="record.coorY != null">
        coor_y = #{record.coorY,jdbcType=INTEGER},
      </if>
      <if test="record.drawDirection != null">
        draw_direction = #{record.drawDirection,jdbcType=TINYINT},
      </if>
      <if test="record.iosStartAngle != null">
        ios_start_angle = #{record.iosStartAngle,jdbcType=INTEGER},
      </if>
      <if test="record.iosEndAngle != null">
        ios_end_angle = #{record.iosEndAngle,jdbcType=INTEGER},
      </if>
      <if test="record.iosDrawLength != null">
        ios_draw_length = #{record.iosDrawLength,jdbcType=INTEGER},
      </if>
      <if test="record.androidStartAngle != null">
        android_start_angle = #{record.androidStartAngle,jdbcType=INTEGER},
      </if>
      <if test="record.androidEndAngle != null">
        android_end_angle = #{record.androidEndAngle,jdbcType=INTEGER},
      </if>
      <if test="record.androidDrawLength != null">
        android_draw_length = #{record.androidDrawLength,jdbcType=INTEGER},
      </if>
      <if test="record.durTime != null">
        dur_time = #{record.durTime,jdbcType=INTEGER},
      </if>
      <if test="record.activeTimes != null">
        active_times = #{record.activeTimes,jdbcType=VARCHAR},
      </if>
      <if test="record.actionType != null">
        action_type = #{record.actionType,jdbcType=TINYINT},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update creative_draw_gesture
    set id = #{record.id,jdbcType=BIGINT},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      guide_image = #{record.guideImage,jdbcType=VARCHAR},
      guide_image_md5 = #{record.guideImageMd5,jdbcType=VARCHAR},
      width = #{record.width,jdbcType=REAL},
      height = #{record.height,jdbcType=REAL},
      coor_x = #{record.coorX,jdbcType=INTEGER},
      coor_y = #{record.coorY,jdbcType=INTEGER},
      draw_direction = #{record.drawDirection,jdbcType=TINYINT},
      ios_start_angle = #{record.iosStartAngle,jdbcType=INTEGER},
      ios_end_angle = #{record.iosEndAngle,jdbcType=INTEGER},
      ios_draw_length = #{record.iosDrawLength,jdbcType=INTEGER},
      android_start_angle = #{record.androidStartAngle,jdbcType=INTEGER},
      android_end_angle = #{record.androidEndAngle,jdbcType=INTEGER},
      android_draw_length = #{record.androidDrawLength,jdbcType=INTEGER},
      dur_time = #{record.durTime,jdbcType=INTEGER},
      active_times = #{record.activeTimes,jdbcType=VARCHAR},
      action_type = #{record.actionType,jdbcType=TINYINT},
      ext = #{record.ext,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    update creative_draw_gesture
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="guideImage != null">
        guide_image = #{guideImage,jdbcType=VARCHAR},
      </if>
      <if test="guideImageMd5 != null">
        guide_image_md5 = #{guideImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=REAL},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=REAL},
      </if>
      <if test="coorX != null">
        coor_x = #{coorX,jdbcType=INTEGER},
      </if>
      <if test="coorY != null">
        coor_y = #{coorY,jdbcType=INTEGER},
      </if>
      <if test="drawDirection != null">
        draw_direction = #{drawDirection,jdbcType=TINYINT},
      </if>
      <if test="iosStartAngle != null">
        ios_start_angle = #{iosStartAngle,jdbcType=INTEGER},
      </if>
      <if test="iosEndAngle != null">
        ios_end_angle = #{iosEndAngle,jdbcType=INTEGER},
      </if>
      <if test="iosDrawLength != null">
        ios_draw_length = #{iosDrawLength,jdbcType=INTEGER},
      </if>
      <if test="androidStartAngle != null">
        android_start_angle = #{androidStartAngle,jdbcType=INTEGER},
      </if>
      <if test="androidEndAngle != null">
        android_end_angle = #{androidEndAngle,jdbcType=INTEGER},
      </if>
      <if test="androidDrawLength != null">
        android_draw_length = #{androidDrawLength,jdbcType=INTEGER},
      </if>
      <if test="durTime != null">
        dur_time = #{durTime,jdbcType=INTEGER},
      </if>
      <if test="activeTimes != null">
        active_times = #{activeTimes,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null">
        action_type = #{actionType,jdbcType=TINYINT},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    update creative_draw_gesture
    set creative_id = #{creativeId,jdbcType=BIGINT},
      guide_image = #{guideImage,jdbcType=VARCHAR},
      guide_image_md5 = #{guideImageMd5,jdbcType=VARCHAR},
      width = #{width,jdbcType=REAL},
      height = #{height,jdbcType=REAL},
      coor_x = #{coorX,jdbcType=INTEGER},
      coor_y = #{coorY,jdbcType=INTEGER},
      draw_direction = #{drawDirection,jdbcType=TINYINT},
      ios_start_angle = #{iosStartAngle,jdbcType=INTEGER},
      ios_end_angle = #{iosEndAngle,jdbcType=INTEGER},
      ios_draw_length = #{iosDrawLength,jdbcType=INTEGER},
      android_start_angle = #{androidStartAngle,jdbcType=INTEGER},
      android_end_angle = #{androidEndAngle,jdbcType=INTEGER},
      android_draw_length = #{androidDrawLength,jdbcType=INTEGER},
      dur_time = #{durTime,jdbcType=INTEGER},
      active_times = #{activeTimes,jdbcType=VARCHAR},
      action_type = #{actionType,jdbcType=TINYINT},
      ext = #{ext,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_draw_gesture (creative_id, guide_image, guide_image_md5, 
      width, height, coor_x, coor_y, 
      draw_direction, ios_start_angle, ios_end_angle, 
      ios_draw_length, android_start_angle, android_end_angle, 
      android_draw_length, dur_time, active_times, 
      action_type, ext, is_deleted, 
      ctime, mtime)
    values (#{creativeId,jdbcType=BIGINT}, #{guideImage,jdbcType=VARCHAR}, #{guideImageMd5,jdbcType=VARCHAR}, 
      #{width,jdbcType=REAL}, #{height,jdbcType=REAL}, #{coorX,jdbcType=INTEGER}, #{coorY,jdbcType=INTEGER}, 
      #{drawDirection,jdbcType=TINYINT}, #{iosStartAngle,jdbcType=INTEGER}, #{iosEndAngle,jdbcType=INTEGER}, 
      #{iosDrawLength,jdbcType=INTEGER}, #{androidStartAngle,jdbcType=INTEGER}, #{androidEndAngle,jdbcType=INTEGER}, 
      #{androidDrawLength,jdbcType=INTEGER}, #{durTime,jdbcType=INTEGER}, #{activeTimes,jdbcType=VARCHAR}, 
      #{actionType,jdbcType=TINYINT}, #{ext,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      guide_image = values(guide_image),
      guide_image_md5 = values(guide_image_md5),
      width = values(width),
      height = values(height),
      coor_x = values(coor_x),
      coor_y = values(coor_y),
      draw_direction = values(draw_direction),
      ios_start_angle = values(ios_start_angle),
      ios_end_angle = values(ios_end_angle),
      ios_draw_length = values(ios_draw_length),
      android_start_angle = values(android_start_angle),
      android_end_angle = values(android_end_angle),
      android_draw_length = values(android_draw_length),
      dur_time = values(dur_time),
      active_times = values(active_times),
      action_type = values(action_type),
      ext = values(ext),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      creative_draw_gesture
      (creative_id,guide_image,guide_image_md5,width,height,coor_x,coor_y,draw_direction,ios_start_angle,ios_end_angle,ios_draw_length,android_start_angle,android_end_angle,android_draw_length,dur_time,active_times,action_type,ext,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.guideImage,jdbcType=VARCHAR},
        #{item.guideImageMd5,jdbcType=VARCHAR},
        #{item.width,jdbcType=REAL},
        #{item.height,jdbcType=REAL},
        #{item.coorX,jdbcType=INTEGER},
        #{item.coorY,jdbcType=INTEGER},
        #{item.drawDirection,jdbcType=TINYINT},
        #{item.iosStartAngle,jdbcType=INTEGER},
        #{item.iosEndAngle,jdbcType=INTEGER},
        #{item.iosDrawLength,jdbcType=INTEGER},
        #{item.androidStartAngle,jdbcType=INTEGER},
        #{item.androidEndAngle,jdbcType=INTEGER},
        #{item.androidDrawLength,jdbcType=INTEGER},
        #{item.durTime,jdbcType=INTEGER},
        #{item.activeTimes,jdbcType=VARCHAR},
        #{item.actionType,jdbcType=TINYINT},
        #{item.ext,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      creative_draw_gesture
      (creative_id,guide_image,guide_image_md5,width,height,coor_x,coor_y,draw_direction,ios_start_angle,ios_end_angle,ios_draw_length,android_start_angle,android_end_angle,android_draw_length,dur_time,active_times,action_type,ext,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.guideImage,jdbcType=VARCHAR},
        #{item.guideImageMd5,jdbcType=VARCHAR},
        #{item.width,jdbcType=REAL},
        #{item.height,jdbcType=REAL},
        #{item.coorX,jdbcType=INTEGER},
        #{item.coorY,jdbcType=INTEGER},
        #{item.drawDirection,jdbcType=TINYINT},
        #{item.iosStartAngle,jdbcType=INTEGER},
        #{item.iosEndAngle,jdbcType=INTEGER},
        #{item.iosDrawLength,jdbcType=INTEGER},
        #{item.androidStartAngle,jdbcType=INTEGER},
        #{item.androidEndAngle,jdbcType=INTEGER},
        #{item.androidDrawLength,jdbcType=INTEGER},
        #{item.durTime,jdbcType=INTEGER},
        #{item.activeTimes,jdbcType=VARCHAR},
        #{item.actionType,jdbcType=TINYINT},
        #{item.ext,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      guide_image = values(guide_image),
      guide_image_md5 = values(guide_image_md5),
      width = values(width),
      height = values(height),
      coor_x = values(coor_x),
      coor_y = values(coor_y),
      draw_direction = values(draw_direction),
      ios_start_angle = values(ios_start_angle),
      ios_end_angle = values(ios_end_angle),
      ios_draw_length = values(ios_draw_length),
      android_start_angle = values(android_start_angle),
      android_end_angle = values(android_end_angle),
      android_draw_length = values(android_draw_length),
      dur_time = values(dur_time),
      active_times = values(active_times),
      action_type = values(action_type),
      ext = values(ext),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_draw_gesture
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="guideImage != null">
        guide_image,
      </if>
      <if test="guideImageMd5 != null">
        guide_image_md5,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="coorX != null">
        coor_x,
      </if>
      <if test="coorY != null">
        coor_y,
      </if>
      <if test="drawDirection != null">
        draw_direction,
      </if>
      <if test="iosStartAngle != null">
        ios_start_angle,
      </if>
      <if test="iosEndAngle != null">
        ios_end_angle,
      </if>
      <if test="iosDrawLength != null">
        ios_draw_length,
      </if>
      <if test="androidStartAngle != null">
        android_start_angle,
      </if>
      <if test="androidEndAngle != null">
        android_end_angle,
      </if>
      <if test="androidDrawLength != null">
        android_draw_length,
      </if>
      <if test="durTime != null">
        dur_time,
      </if>
      <if test="activeTimes != null">
        active_times,
      </if>
      <if test="actionType != null">
        action_type,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="guideImage != null">
        #{guideImage,jdbcType=VARCHAR},
      </if>
      <if test="guideImageMd5 != null">
        #{guideImageMd5,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=REAL},
      </if>
      <if test="height != null">
        #{height,jdbcType=REAL},
      </if>
      <if test="coorX != null">
        #{coorX,jdbcType=INTEGER},
      </if>
      <if test="coorY != null">
        #{coorY,jdbcType=INTEGER},
      </if>
      <if test="drawDirection != null">
        #{drawDirection,jdbcType=TINYINT},
      </if>
      <if test="iosStartAngle != null">
        #{iosStartAngle,jdbcType=INTEGER},
      </if>
      <if test="iosEndAngle != null">
        #{iosEndAngle,jdbcType=INTEGER},
      </if>
      <if test="iosDrawLength != null">
        #{iosDrawLength,jdbcType=INTEGER},
      </if>
      <if test="androidStartAngle != null">
        #{androidStartAngle,jdbcType=INTEGER},
      </if>
      <if test="androidEndAngle != null">
        #{androidEndAngle,jdbcType=INTEGER},
      </if>
      <if test="androidDrawLength != null">
        #{androidDrawLength,jdbcType=INTEGER},
      </if>
      <if test="durTime != null">
        #{durTime,jdbcType=INTEGER},
      </if>
      <if test="activeTimes != null">
        #{activeTimes,jdbcType=VARCHAR},
      </if>
      <if test="actionType != null">
        #{actionType,jdbcType=TINYINT},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="guideImage != null">
        guide_image = values(guide_image),
      </if>
      <if test="guideImageMd5 != null">
        guide_image_md5 = values(guide_image_md5),
      </if>
      <if test="width != null">
        width = values(width),
      </if>
      <if test="height != null">
        height = values(height),
      </if>
      <if test="coorX != null">
        coor_x = values(coor_x),
      </if>
      <if test="coorY != null">
        coor_y = values(coor_y),
      </if>
      <if test="drawDirection != null">
        draw_direction = values(draw_direction),
      </if>
      <if test="iosStartAngle != null">
        ios_start_angle = values(ios_start_angle),
      </if>
      <if test="iosEndAngle != null">
        ios_end_angle = values(ios_end_angle),
      </if>
      <if test="iosDrawLength != null">
        ios_draw_length = values(ios_draw_length),
      </if>
      <if test="androidStartAngle != null">
        android_start_angle = values(android_start_angle),
      </if>
      <if test="androidEndAngle != null">
        android_end_angle = values(android_end_angle),
      </if>
      <if test="androidDrawLength != null">
        android_draw_length = values(android_draw_length),
      </if>
      <if test="durTime != null">
        dur_time = values(dur_time),
      </if>
      <if test="activeTimes != null">
        active_times = values(active_times),
      </if>
      <if test="actionType != null">
        action_type = values(action_type),
      </if>
      <if test="ext != null">
        ext = values(ext),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>