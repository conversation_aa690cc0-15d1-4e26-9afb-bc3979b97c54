<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.ext.ExtSplashScreenVersionControlDao">


  <insert id="batchInsert" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">

    insert into ssa_splash_screen_version_control
    (
     splash_screen_id,
     start_version,
     end_version,
     platform_id
     )
    values
    <foreach item="entity" index="index" collection="records" open="" separator="," close="">
    (
      #{entity.splashScreenId,jdbcType=INTEGER},
      #{entity.startVersion,jdbcType=INTEGER},
      #{entity.endVersion,jdbcType=INTEGER},
      #{entity.platformId,jdbcType=TINYINT}
      )
    </foreach>
      ON DUPLICATE KEY UPDATE
      start_version=VALUES(start_version),
      end_version=VALUES(end_version),
      is_deleted=0
  </insert>


</mapper>