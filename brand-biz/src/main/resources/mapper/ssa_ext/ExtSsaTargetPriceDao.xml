<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.ext.ExtSsaTargetPriceDao">
  
  <insert id="batchSave" parameterType="com.bilibili.ssa.platform.biz.po.SsaTargetPricePo">
    insert into ssa_target_price (source_config_id, target_type, area_type, price_config_id,
      price, percent)
    values 
    <foreach collection="pos" item="po" open="" separator="," close="">
    (#{po.sourceConfigId,jdbcType=INTEGER}, #{po.targetType,jdbcType=INTEGER}, #{po.areaType,jdbcType=INTEGER},
     #{po.priceConfigId,jdbcType=INTEGER}, #{po.price,jdbcType=INTEGER}, #{po.percent,jdbcType=INTEGER})
    </foreach>
    on duplicate key update is_deleted = 0, price = values(price), percent = values(percent)
  </insert>
  
</mapper>