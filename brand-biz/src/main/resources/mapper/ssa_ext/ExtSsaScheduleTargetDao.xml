<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.ext.ExtSsaScheduleTargetDao">
  
  <insert id="batchInsert" parameterType="com.bilibili.ssa.platform.biz.po.SsaScheduleTargetPo">
    insert into ssa_schedule_target (schedule_group_id, order_id, target_type, 
      target_item_ids)
    values 
    <foreach collection="pos" item="po" open="" separator="," close="">
        (#{po.scheduleGroupId,jdbcType=INTEGER}, #{po.orderId,jdbcType=INTEGER}, #{po.targetType,jdbcType=TINYINT}, 
      #{po.targetItemIds,jdbcType=VARCHAR})
    </foreach>
    on duplicate key update target_item_ids = values(target_item_ids), is_deleted = 0
  </insert>
  
</mapper>