<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.ext.ExtSplashScreenDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cycle_id" jdbcType="INTEGER" property="cycleId" />
    <result column="business_side_id" jdbcType="INTEGER" property="businessSideId" />
    <result column="ssa_order_id" jdbcType="INTEGER" property="ssaOrderId" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="copywriting" jdbcType="VARCHAR" property="copywriting" />
    <result column="cm_mark" jdbcType="TINYINT" property="cmMark" />
    <result column="business_side_type" jdbcType="TINYINT" property="businessSideType" />
    <result column="show_style" jdbcType="TINYINT" property="showStyle" />
    <result column="show_time" jdbcType="TINYINT" property="showTime" />
    <result column="is_skip" jdbcType="TINYINT" property="isSkip" />
    <result column="jump_type" jdbcType="TINYINT" property="jumpType" />
    <result column="jump_link" jdbcType="VARCHAR" property="jumpLink" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="customized_click_url" jdbcType="VARCHAR" property="customizedClickUrl" />
    <result column="issued_time" jdbcType="TINYINT" property="issuedTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="scheme_url" jdbcType="VARCHAR" property="schemeUrl" />
    <result column="scheme_copywriting" jdbcType="VARCHAR" property="schemeCopywriting" />
    <result column="android_app_package_name" jdbcType="VARCHAR" property="androidAppPackageName" />
    <result column="ios_app_package_name" jdbcType="VARCHAR" property="iosAppPackageName" />
    <result column="is_call_app" jdbcType="TINYINT" property="isCallApp" />
    <result column="sales_type" jdbcType="TINYINT" property="salesType" />
    <result column="schedule_group_id" jdbcType="INTEGER" property="scheduleGroupId" />
    <result column="source_type" jdbcType="TINYINT" property="sourceType" />
    <result column="customized_imp_url" jdbcType="VARCHAR" property="customizedImpUrl" />
  </resultMap>

  <update id="batchUpdateStatus" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo">
    <foreach collection="records" item="record" open="" separator=";" close="">
      UPDATE ssa_splash_screen
      SET status = #{record.status,jdbcType=TINYINT}
      WHERE id = #{record.id,jdbcType=INTEGER}
      AND status != #{record.status,jdbcType=TINYINT}
    </foreach>
  </update>
</mapper>