<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.ext.ExtSsaUserDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaUserPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <insert id="save" parameterType="com.bilibili.ssa.platform.biz.po.SsaUserPo">
    insert into ssa_user (name, status)
    values (#{name,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT})
    ON DUPLICATE KEY UPDATE status=#{status,jdbcType=TINYINT};
  </insert>
</mapper>