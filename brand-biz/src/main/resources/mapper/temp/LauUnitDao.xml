<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.temp.dao.LauUnitDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    <id column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_name" jdbcType="VARCHAR" property="unitName" />
    <result column="promotion_purpose_type" jdbcType="TINYINT" property="promotionPurposeType" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="slot_group" jdbcType="INTEGER" property="slotGroup" />
    <result column="launch_begin_date" jdbcType="VARCHAR" property="launchBeginDate" />
    <result column="launch_end_date" jdbcType="VARCHAR" property="launchEndDate" />
    <result column="launch_time" jdbcType="VARCHAR" property="launchTime" />
    <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
    <result column="budget_type" jdbcType="TINYINT" property="budgetType" />
    <result column="budget" jdbcType="BIGINT" property="budget" />
    <result column="frequency_unit" jdbcType="INTEGER" property="frequencyUnit" />
    <result column="frequency_limit" jdbcType="INTEGER" property="frequencyLimit" />
    <result column="creative_display_mode" jdbcType="TINYINT" property="creativeDisplayMode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="sales_type" jdbcType="INTEGER" property="salesType" />
    <result column="slot_id" jdbcType="INTEGER" property="slotId" />
    <result column="unit_status" jdbcType="TINYINT" property="unitStatus" />
    <result column="unit_status_mtime" jdbcType="TIMESTAMP" property="unitStatusMtime" />
    <result column="repeat_flag" jdbcType="VARCHAR" property="repeatFlag" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="app_package_id" jdbcType="INTEGER" property="appPackageId" />
    <result column="daily_budget_type" jdbcType="TINYINT" property="dailyBudgetType" />
    <result column="is_history" jdbcType="TINYINT" property="isHistory" />
    <result column="ocpc_target" jdbcType="TINYINT" property="ocpcTarget" />
    <result column="two_stage_bid" jdbcType="INTEGER" property="twoStageBid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    unit_id, account_id, campaign_id, unit_name, promotion_purpose_type, channel_id, 
    slot_group, launch_begin_date, launch_end_date, launch_time, cost_price, budget_type, 
    budget, frequency_unit, frequency_limit, creative_display_mode, status, is_deleted, 
    ctime, mtime, sales_type, slot_id, unit_status, unit_status_mtime, repeat_flag, tags, 
    app_package_id, daily_budget_type, is_history, ocpc_target, two_stage_bid
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit
    where unit_id = #{unitId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_unit
    where unit_id = #{unitId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPoExample">
    delete from lau_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    <selectKey keyProperty="unitId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit (account_id, campaign_id, unit_name, 
      promotion_purpose_type, channel_id, slot_group, 
      launch_begin_date, launch_end_date, launch_time, 
      cost_price, budget_type, budget, 
      frequency_unit, frequency_limit, creative_display_mode, 
      status, is_deleted, ctime, 
      mtime, sales_type, slot_id, 
      unit_status, unit_status_mtime, repeat_flag, 
      tags, app_package_id, daily_budget_type, 
      is_history, ocpc_target, two_stage_bid
      )
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitName,jdbcType=VARCHAR}, 
      #{promotionPurposeType,jdbcType=TINYINT}, #{channelId,jdbcType=INTEGER}, #{slotGroup,jdbcType=INTEGER}, 
      #{launchBeginDate,jdbcType=VARCHAR}, #{launchEndDate,jdbcType=VARCHAR}, #{launchTime,jdbcType=VARCHAR}, 
      #{costPrice,jdbcType=INTEGER}, #{budgetType,jdbcType=TINYINT}, #{budget,jdbcType=BIGINT}, 
      #{frequencyUnit,jdbcType=INTEGER}, #{frequencyLimit,jdbcType=INTEGER}, #{creativeDisplayMode,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{salesType,jdbcType=INTEGER}, #{slotId,jdbcType=INTEGER}, 
      #{unitStatus,jdbcType=TINYINT}, #{unitStatusMtime,jdbcType=TIMESTAMP}, #{repeatFlag,jdbcType=VARCHAR}, 
      #{tags,jdbcType=VARCHAR}, #{appPackageId,jdbcType=INTEGER}, #{dailyBudgetType,jdbcType=TINYINT}, 
      #{isHistory,jdbcType=TINYINT}, #{ocpcTarget,jdbcType=TINYINT}, #{twoStageBid,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    <selectKey keyProperty="unitId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitName != null">
        unit_name,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="slotGroup != null">
        slot_group,
      </if>
      <if test="launchBeginDate != null">
        launch_begin_date,
      </if>
      <if test="launchEndDate != null">
        launch_end_date,
      </if>
      <if test="launchTime != null">
        launch_time,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="budgetType != null">
        budget_type,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="frequencyUnit != null">
        frequency_unit,
      </if>
      <if test="frequencyLimit != null">
        frequency_limit,
      </if>
      <if test="creativeDisplayMode != null">
        creative_display_mode,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="slotId != null">
        slot_id,
      </if>
      <if test="unitStatus != null">
        unit_status,
      </if>
      <if test="unitStatusMtime != null">
        unit_status_mtime,
      </if>
      <if test="repeatFlag != null">
        repeat_flag,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
      <if test="dailyBudgetType != null">
        daily_budget_type,
      </if>
      <if test="isHistory != null">
        is_history,
      </if>
      <if test="ocpcTarget != null">
        ocpc_target,
      </if>
      <if test="twoStageBid != null">
        two_stage_bid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="slotGroup != null">
        #{slotGroup,jdbcType=INTEGER},
      </if>
      <if test="launchBeginDate != null">
        #{launchBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="launchEndDate != null">
        #{launchEndDate,jdbcType=VARCHAR},
      </if>
      <if test="launchTime != null">
        #{launchTime,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="budgetType != null">
        #{budgetType,jdbcType=TINYINT},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=BIGINT},
      </if>
      <if test="frequencyUnit != null">
        #{frequencyUnit,jdbcType=INTEGER},
      </if>
      <if test="frequencyLimit != null">
        #{frequencyLimit,jdbcType=INTEGER},
      </if>
      <if test="creativeDisplayMode != null">
        #{creativeDisplayMode,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="slotId != null">
        #{slotId,jdbcType=INTEGER},
      </if>
      <if test="unitStatus != null">
        #{unitStatus,jdbcType=TINYINT},
      </if>
      <if test="unitStatusMtime != null">
        #{unitStatusMtime,jdbcType=TIMESTAMP},
      </if>
      <if test="repeatFlag != null">
        #{repeatFlag,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="dailyBudgetType != null">
        #{dailyBudgetType,jdbcType=TINYINT},
      </if>
      <if test="isHistory != null">
        #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="ocpcTarget != null">
        #{ocpcTarget,jdbcType=TINYINT},
      </if>
      <if test="twoStageBid != null">
        #{twoStageBid,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPoExample" resultType="java.lang.Long">
    select count(*) from lau_unit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit
    <set>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitName != null">
        unit_name = #{record.unitName,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionPurposeType != null">
        promotion_purpose_type = #{record.promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.slotGroup != null">
        slot_group = #{record.slotGroup,jdbcType=INTEGER},
      </if>
      <if test="record.launchBeginDate != null">
        launch_begin_date = #{record.launchBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="record.launchEndDate != null">
        launch_end_date = #{record.launchEndDate,jdbcType=VARCHAR},
      </if>
      <if test="record.launchTime != null">
        launch_time = #{record.launchTime,jdbcType=VARCHAR},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=INTEGER},
      </if>
      <if test="record.budgetType != null">
        budget_type = #{record.budgetType,jdbcType=TINYINT},
      </if>
      <if test="record.budget != null">
        budget = #{record.budget,jdbcType=BIGINT},
      </if>
      <if test="record.frequencyUnit != null">
        frequency_unit = #{record.frequencyUnit,jdbcType=INTEGER},
      </if>
      <if test="record.frequencyLimit != null">
        frequency_limit = #{record.frequencyLimit,jdbcType=INTEGER},
      </if>
      <if test="record.creativeDisplayMode != null">
        creative_display_mode = #{record.creativeDisplayMode,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=INTEGER},
      </if>
      <if test="record.slotId != null">
        slot_id = #{record.slotId,jdbcType=INTEGER},
      </if>
      <if test="record.unitStatus != null">
        unit_status = #{record.unitStatus,jdbcType=TINYINT},
      </if>
      <if test="record.unitStatusMtime != null">
        unit_status_mtime = #{record.unitStatusMtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.repeatFlag != null">
        repeat_flag = #{record.repeatFlag,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.appPackageId != null">
        app_package_id = #{record.appPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.dailyBudgetType != null">
        daily_budget_type = #{record.dailyBudgetType,jdbcType=TINYINT},
      </if>
      <if test="record.isHistory != null">
        is_history = #{record.isHistory,jdbcType=TINYINT},
      </if>
      <if test="record.ocpcTarget != null">
        ocpc_target = #{record.ocpcTarget,jdbcType=TINYINT},
      </if>
      <if test="record.twoStageBid != null">
        two_stage_bid = #{record.twoStageBid,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit
    set unit_id = #{record.unitId,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_name = #{record.unitName,jdbcType=VARCHAR},
      promotion_purpose_type = #{record.promotionPurposeType,jdbcType=TINYINT},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      slot_group = #{record.slotGroup,jdbcType=INTEGER},
      launch_begin_date = #{record.launchBeginDate,jdbcType=VARCHAR},
      launch_end_date = #{record.launchEndDate,jdbcType=VARCHAR},
      launch_time = #{record.launchTime,jdbcType=VARCHAR},
      cost_price = #{record.costPrice,jdbcType=INTEGER},
      budget_type = #{record.budgetType,jdbcType=TINYINT},
      budget = #{record.budget,jdbcType=BIGINT},
      frequency_unit = #{record.frequencyUnit,jdbcType=INTEGER},
      frequency_limit = #{record.frequencyLimit,jdbcType=INTEGER},
      creative_display_mode = #{record.creativeDisplayMode,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      sales_type = #{record.salesType,jdbcType=INTEGER},
      slot_id = #{record.slotId,jdbcType=INTEGER},
      unit_status = #{record.unitStatus,jdbcType=TINYINT},
      unit_status_mtime = #{record.unitStatusMtime,jdbcType=TIMESTAMP},
      repeat_flag = #{record.repeatFlag,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=VARCHAR},
      app_package_id = #{record.appPackageId,jdbcType=INTEGER},
      daily_budget_type = #{record.dailyBudgetType,jdbcType=TINYINT},
      is_history = #{record.isHistory,jdbcType=TINYINT},
      ocpc_target = #{record.ocpcTarget,jdbcType=TINYINT},
      two_stage_bid = #{record.twoStageBid,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    update lau_unit
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        unit_name = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="slotGroup != null">
        slot_group = #{slotGroup,jdbcType=INTEGER},
      </if>
      <if test="launchBeginDate != null">
        launch_begin_date = #{launchBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="launchEndDate != null">
        launch_end_date = #{launchEndDate,jdbcType=VARCHAR},
      </if>
      <if test="launchTime != null">
        launch_time = #{launchTime,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="budgetType != null">
        budget_type = #{budgetType,jdbcType=TINYINT},
      </if>
      <if test="budget != null">
        budget = #{budget,jdbcType=BIGINT},
      </if>
      <if test="frequencyUnit != null">
        frequency_unit = #{frequencyUnit,jdbcType=INTEGER},
      </if>
      <if test="frequencyLimit != null">
        frequency_limit = #{frequencyLimit,jdbcType=INTEGER},
      </if>
      <if test="creativeDisplayMode != null">
        creative_display_mode = #{creativeDisplayMode,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=INTEGER},
      </if>
      <if test="slotId != null">
        slot_id = #{slotId,jdbcType=INTEGER},
      </if>
      <if test="unitStatus != null">
        unit_status = #{unitStatus,jdbcType=TINYINT},
      </if>
      <if test="unitStatusMtime != null">
        unit_status_mtime = #{unitStatusMtime,jdbcType=TIMESTAMP},
      </if>
      <if test="repeatFlag != null">
        repeat_flag = #{repeatFlag,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="appPackageId != null">
        app_package_id = #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="dailyBudgetType != null">
        daily_budget_type = #{dailyBudgetType,jdbcType=TINYINT},
      </if>
      <if test="isHistory != null">
        is_history = #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="ocpcTarget != null">
        ocpc_target = #{ocpcTarget,jdbcType=TINYINT},
      </if>
      <if test="twoStageBid != null">
        two_stage_bid = #{twoStageBid,jdbcType=INTEGER},
      </if>
    </set>
    where unit_id = #{unitId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    update lau_unit
    set account_id = #{accountId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_name = #{unitName,jdbcType=VARCHAR},
      promotion_purpose_type = #{promotionPurposeType,jdbcType=TINYINT},
      channel_id = #{channelId,jdbcType=INTEGER},
      slot_group = #{slotGroup,jdbcType=INTEGER},
      launch_begin_date = #{launchBeginDate,jdbcType=VARCHAR},
      launch_end_date = #{launchEndDate,jdbcType=VARCHAR},
      launch_time = #{launchTime,jdbcType=VARCHAR},
      cost_price = #{costPrice,jdbcType=INTEGER},
      budget_type = #{budgetType,jdbcType=TINYINT},
      budget = #{budget,jdbcType=BIGINT},
      frequency_unit = #{frequencyUnit,jdbcType=INTEGER},
      frequency_limit = #{frequencyLimit,jdbcType=INTEGER},
      creative_display_mode = #{creativeDisplayMode,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      sales_type = #{salesType,jdbcType=INTEGER},
      slot_id = #{slotId,jdbcType=INTEGER},
      unit_status = #{unitStatus,jdbcType=TINYINT},
      unit_status_mtime = #{unitStatusMtime,jdbcType=TIMESTAMP},
      repeat_flag = #{repeatFlag,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=VARCHAR},
      app_package_id = #{appPackageId,jdbcType=INTEGER},
      daily_budget_type = #{dailyBudgetType,jdbcType=TINYINT},
      is_history = #{isHistory,jdbcType=TINYINT},
      ocpc_target = #{ocpcTarget,jdbcType=TINYINT},
      two_stage_bid = #{twoStageBid,jdbcType=INTEGER}
    where unit_id = #{unitId,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    <selectKey keyProperty="unitId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit (account_id, campaign_id, unit_name, 
      promotion_purpose_type, channel_id, slot_group, 
      launch_begin_date, launch_end_date, launch_time, 
      cost_price, budget_type, budget, 
      frequency_unit, frequency_limit, creative_display_mode, 
      status, is_deleted, ctime, 
      mtime, sales_type, slot_id, 
      unit_status, unit_status_mtime, repeat_flag, 
      tags, app_package_id, daily_budget_type, 
      is_history, ocpc_target, two_stage_bid
      )
    values (#{accountId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{unitName,jdbcType=VARCHAR}, 
      #{promotionPurposeType,jdbcType=TINYINT}, #{channelId,jdbcType=INTEGER}, #{slotGroup,jdbcType=INTEGER}, 
      #{launchBeginDate,jdbcType=VARCHAR}, #{launchEndDate,jdbcType=VARCHAR}, #{launchTime,jdbcType=VARCHAR}, 
      #{costPrice,jdbcType=INTEGER}, #{budgetType,jdbcType=TINYINT}, #{budget,jdbcType=BIGINT}, 
      #{frequencyUnit,jdbcType=INTEGER}, #{frequencyLimit,jdbcType=INTEGER}, #{creativeDisplayMode,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{salesType,jdbcType=INTEGER}, #{slotId,jdbcType=INTEGER}, 
      #{unitStatus,jdbcType=TINYINT}, #{unitStatusMtime,jdbcType=TIMESTAMP}, #{repeatFlag,jdbcType=VARCHAR}, 
      #{tags,jdbcType=VARCHAR}, #{appPackageId,jdbcType=INTEGER}, #{dailyBudgetType,jdbcType=TINYINT}, 
      #{isHistory,jdbcType=TINYINT}, #{ocpcTarget,jdbcType=TINYINT}, #{twoStageBid,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_name = values(unit_name),
      promotion_purpose_type = values(promotion_purpose_type),
      channel_id = values(channel_id),
      slot_group = values(slot_group),
      launch_begin_date = values(launch_begin_date),
      launch_end_date = values(launch_end_date),
      launch_time = values(launch_time),
      cost_price = values(cost_price),
      budget_type = values(budget_type),
      budget = values(budget),
      frequency_unit = values(frequency_unit),
      frequency_limit = values(frequency_limit),
      creative_display_mode = values(creative_display_mode),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      sales_type = values(sales_type),
      slot_id = values(slot_id),
      unit_status = values(unit_status),
      unit_status_mtime = values(unit_status_mtime),
      repeat_flag = values(repeat_flag),
      tags = values(tags),
      app_package_id = values(app_package_id),
      daily_budget_type = values(daily_budget_type),
      is_history = values(is_history),
      ocpc_target = values(ocpc_target),
      two_stage_bid = values(two_stage_bid),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit
      (account_id,campaign_id,unit_name,promotion_purpose_type,channel_id,slot_group,launch_begin_date,launch_end_date,launch_time,cost_price,budget_type,budget,frequency_unit,frequency_limit,creative_display_mode,status,is_deleted,ctime,mtime,sales_type,slot_id,unit_status,unit_status_mtime,repeat_flag,tags,app_package_id,daily_budget_type,is_history,ocpc_target,two_stage_bid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitName,jdbcType=VARCHAR},
        #{item.promotionPurposeType,jdbcType=TINYINT},
        #{item.channelId,jdbcType=INTEGER},
        #{item.slotGroup,jdbcType=INTEGER},
        #{item.launchBeginDate,jdbcType=VARCHAR},
        #{item.launchEndDate,jdbcType=VARCHAR},
        #{item.launchTime,jdbcType=VARCHAR},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.budgetType,jdbcType=TINYINT},
        #{item.budget,jdbcType=BIGINT},
        #{item.frequencyUnit,jdbcType=INTEGER},
        #{item.frequencyLimit,jdbcType=INTEGER},
        #{item.creativeDisplayMode,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.salesType,jdbcType=INTEGER},
        #{item.slotId,jdbcType=INTEGER},
        #{item.unitStatus,jdbcType=TINYINT},
        #{item.unitStatusMtime,jdbcType=TIMESTAMP},
        #{item.repeatFlag,jdbcType=VARCHAR},
        #{item.tags,jdbcType=VARCHAR},
        #{item.appPackageId,jdbcType=INTEGER},
        #{item.dailyBudgetType,jdbcType=TINYINT},
        #{item.isHistory,jdbcType=TINYINT},
        #{item.ocpcTarget,jdbcType=TINYINT},
        #{item.twoStageBid,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit
      (account_id,campaign_id,unit_name,promotion_purpose_type,channel_id,slot_group,launch_begin_date,launch_end_date,launch_time,cost_price,budget_type,budget,frequency_unit,frequency_limit,creative_display_mode,status,is_deleted,ctime,mtime,sales_type,slot_id,unit_status,unit_status_mtime,repeat_flag,tags,app_package_id,daily_budget_type,is_history,ocpc_target,two_stage_bid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitName,jdbcType=VARCHAR},
        #{item.promotionPurposeType,jdbcType=TINYINT},
        #{item.channelId,jdbcType=INTEGER},
        #{item.slotGroup,jdbcType=INTEGER},
        #{item.launchBeginDate,jdbcType=VARCHAR},
        #{item.launchEndDate,jdbcType=VARCHAR},
        #{item.launchTime,jdbcType=VARCHAR},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.budgetType,jdbcType=TINYINT},
        #{item.budget,jdbcType=BIGINT},
        #{item.frequencyUnit,jdbcType=INTEGER},
        #{item.frequencyLimit,jdbcType=INTEGER},
        #{item.creativeDisplayMode,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.salesType,jdbcType=INTEGER},
        #{item.slotId,jdbcType=INTEGER},
        #{item.unitStatus,jdbcType=TINYINT},
        #{item.unitStatusMtime,jdbcType=TIMESTAMP},
        #{item.repeatFlag,jdbcType=VARCHAR},
        #{item.tags,jdbcType=VARCHAR},
        #{item.appPackageId,jdbcType=INTEGER},
        #{item.dailyBudgetType,jdbcType=TINYINT},
        #{item.isHistory,jdbcType=TINYINT},
        #{item.ocpcTarget,jdbcType=TINYINT},
        #{item.twoStageBid,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      campaign_id = values(campaign_id),
      unit_name = values(unit_name),
      promotion_purpose_type = values(promotion_purpose_type),
      channel_id = values(channel_id),
      slot_group = values(slot_group),
      launch_begin_date = values(launch_begin_date),
      launch_end_date = values(launch_end_date),
      launch_time = values(launch_time),
      cost_price = values(cost_price),
      budget_type = values(budget_type),
      budget = values(budget),
      frequency_unit = values(frequency_unit),
      frequency_limit = values(frequency_limit),
      creative_display_mode = values(creative_display_mode),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      sales_type = values(sales_type),
      slot_id = values(slot_id),
      unit_status = values(unit_status),
      unit_status_mtime = values(unit_status_mtime),
      repeat_flag = values(repeat_flag),
      tags = values(tags),
      app_package_id = values(app_package_id),
      daily_budget_type = values(daily_budget_type),
      is_history = values(is_history),
      ocpc_target = values(ocpc_target),
      two_stage_bid = values(two_stage_bid),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ssa.platform.biz.temp.po.LauUnitPo">
    <selectKey keyProperty="unitId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitName != null">
        unit_name,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="slotGroup != null">
        slot_group,
      </if>
      <if test="launchBeginDate != null">
        launch_begin_date,
      </if>
      <if test="launchEndDate != null">
        launch_end_date,
      </if>
      <if test="launchTime != null">
        launch_time,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="budgetType != null">
        budget_type,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="frequencyUnit != null">
        frequency_unit,
      </if>
      <if test="frequencyLimit != null">
        frequency_limit,
      </if>
      <if test="creativeDisplayMode != null">
        creative_display_mode,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="slotId != null">
        slot_id,
      </if>
      <if test="unitStatus != null">
        unit_status,
      </if>
      <if test="unitStatusMtime != null">
        unit_status_mtime,
      </if>
      <if test="repeatFlag != null">
        repeat_flag,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
      <if test="dailyBudgetType != null">
        daily_budget_type,
      </if>
      <if test="isHistory != null">
        is_history,
      </if>
      <if test="ocpcTarget != null">
        ocpc_target,
      </if>
      <if test="twoStageBid != null">
        two_stage_bid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="slotGroup != null">
        #{slotGroup,jdbcType=INTEGER},
      </if>
      <if test="launchBeginDate != null">
        #{launchBeginDate,jdbcType=VARCHAR},
      </if>
      <if test="launchEndDate != null">
        #{launchEndDate,jdbcType=VARCHAR},
      </if>
      <if test="launchTime != null">
        #{launchTime,jdbcType=VARCHAR},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="budgetType != null">
        #{budgetType,jdbcType=TINYINT},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=BIGINT},
      </if>
      <if test="frequencyUnit != null">
        #{frequencyUnit,jdbcType=INTEGER},
      </if>
      <if test="frequencyLimit != null">
        #{frequencyLimit,jdbcType=INTEGER},
      </if>
      <if test="creativeDisplayMode != null">
        #{creativeDisplayMode,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=INTEGER},
      </if>
      <if test="slotId != null">
        #{slotId,jdbcType=INTEGER},
      </if>
      <if test="unitStatus != null">
        #{unitStatus,jdbcType=TINYINT},
      </if>
      <if test="unitStatusMtime != null">
        #{unitStatusMtime,jdbcType=TIMESTAMP},
      </if>
      <if test="repeatFlag != null">
        #{repeatFlag,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="dailyBudgetType != null">
        #{dailyBudgetType,jdbcType=TINYINT},
      </if>
      <if test="isHistory != null">
        #{isHistory,jdbcType=TINYINT},
      </if>
      <if test="ocpcTarget != null">
        #{ocpcTarget,jdbcType=TINYINT},
      </if>
      <if test="twoStageBid != null">
        #{twoStageBid,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitName != null">
        unit_name = values(unit_name),
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = values(promotion_purpose_type),
      </if>
      <if test="channelId != null">
        channel_id = values(channel_id),
      </if>
      <if test="slotGroup != null">
        slot_group = values(slot_group),
      </if>
      <if test="launchBeginDate != null">
        launch_begin_date = values(launch_begin_date),
      </if>
      <if test="launchEndDate != null">
        launch_end_date = values(launch_end_date),
      </if>
      <if test="launchTime != null">
        launch_time = values(launch_time),
      </if>
      <if test="costPrice != null">
        cost_price = values(cost_price),
      </if>
      <if test="budgetType != null">
        budget_type = values(budget_type),
      </if>
      <if test="budget != null">
        budget = values(budget),
      </if>
      <if test="frequencyUnit != null">
        frequency_unit = values(frequency_unit),
      </if>
      <if test="frequencyLimit != null">
        frequency_limit = values(frequency_limit),
      </if>
      <if test="creativeDisplayMode != null">
        creative_display_mode = values(creative_display_mode),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="slotId != null">
        slot_id = values(slot_id),
      </if>
      <if test="unitStatus != null">
        unit_status = values(unit_status),
      </if>
      <if test="unitStatusMtime != null">
        unit_status_mtime = values(unit_status_mtime),
      </if>
      <if test="repeatFlag != null">
        repeat_flag = values(repeat_flag),
      </if>
      <if test="tags != null">
        tags = values(tags),
      </if>
      <if test="appPackageId != null">
        app_package_id = values(app_package_id),
      </if>
      <if test="dailyBudgetType != null">
        daily_budget_type = values(daily_budget_type),
      </if>
      <if test="isHistory != null">
        is_history = values(is_history),
      </if>
      <if test="ocpcTarget != null">
        ocpc_target = values(ocpc_target),
      </if>
      <if test="twoStageBid != null">
        two_stage_bid = values(two_stage_bid),
      </if>
    </trim>
  </insert>
</mapper>