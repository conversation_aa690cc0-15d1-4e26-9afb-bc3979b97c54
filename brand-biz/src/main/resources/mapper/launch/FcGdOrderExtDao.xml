<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.order.dao.FcGdOrderExtDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_amount" jdbcType="BIGINT" property="orderAmount" />
    <result column="order_cpm" jdbcType="INTEGER" property="orderCpm" />
    <result column="order_launch_time_type" jdbcType="TINYINT" property="orderLaunchTimeType" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="enable_cycle_frequency" jdbcType="BIT" property="enableCycleFrequency" />
    <result column="cycle_frequency_begin_time" jdbcType="TIMESTAMP" property="cycleFrequencyBeginTime" />
    <result column="cycle_frequency_end_time" jdbcType="TIMESTAMP" property="cycleFrequencyEndTime" />
    <result column="cycle_frequency_interval" jdbcType="INTEGER" property="cycleFrequencyInterval" />
    <result column="cycle_frequency_limit" jdbcType="INTEGER" property="cycleFrequencyLimit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_amount, order_cpm, order_launch_time_type, order_source, is_deleted, 
    ctime, mtime, enable_cycle_frequency, cycle_frequency_begin_time, cycle_frequency_end_time, 
    cycle_frequency_interval, cycle_frequency_limit
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fc_gd_order_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fc_gd_order_ext
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from fc_gd_order_ext
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPoExample">
    delete from fc_gd_order_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fc_gd_order_ext (order_id, order_amount, order_cpm, 
      order_launch_time_type, order_source, is_deleted, 
      ctime, mtime, enable_cycle_frequency, 
      cycle_frequency_begin_time, cycle_frequency_end_time, 
      cycle_frequency_interval, cycle_frequency_limit
      )
    values (#{orderId,jdbcType=INTEGER}, #{orderAmount,jdbcType=BIGINT}, #{orderCpm,jdbcType=INTEGER}, 
      #{orderLaunchTimeType,jdbcType=TINYINT}, #{orderSource,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{enableCycleFrequency,jdbcType=BIT}, 
      #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP}, #{cycleFrequencyEndTime,jdbcType=TIMESTAMP}, 
      #{cycleFrequencyInterval,jdbcType=INTEGER}, #{cycleFrequencyLimit,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fc_gd_order_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="orderCpm != null">
        order_cpm,
      </if>
      <if test="orderLaunchTimeType != null">
        order_launch_time_type,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="enableCycleFrequency != null">
        enable_cycle_frequency,
      </if>
      <if test="cycleFrequencyBeginTime != null">
        cycle_frequency_begin_time,
      </if>
      <if test="cycleFrequencyEndTime != null">
        cycle_frequency_end_time,
      </if>
      <if test="cycleFrequencyInterval != null">
        cycle_frequency_interval,
      </if>
      <if test="cycleFrequencyLimit != null">
        cycle_frequency_limit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="orderCpm != null">
        #{orderCpm,jdbcType=INTEGER},
      </if>
      <if test="orderLaunchTimeType != null">
        #{orderLaunchTimeType,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableCycleFrequency != null">
        #{enableCycleFrequency,jdbcType=BIT},
      </if>
      <if test="cycleFrequencyBeginTime != null">
        #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyEndTime != null">
        #{cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyInterval != null">
        #{cycleFrequencyInterval,jdbcType=INTEGER},
      </if>
      <if test="cycleFrequencyLimit != null">
        #{cycleFrequencyLimit,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPoExample" resultType="java.lang.Long">
    select count(*) from fc_gd_order_ext
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fc_gd_order_ext
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.orderAmount != null">
        order_amount = #{record.orderAmount,jdbcType=BIGINT},
      </if>
      <if test="record.orderCpm != null">
        order_cpm = #{record.orderCpm,jdbcType=INTEGER},
      </if>
      <if test="record.orderLaunchTimeType != null">
        order_launch_time_type = #{record.orderLaunchTimeType,jdbcType=TINYINT},
      </if>
      <if test="record.orderSource != null">
        order_source = #{record.orderSource,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.enableCycleFrequency != null">
        enable_cycle_frequency = #{record.enableCycleFrequency,jdbcType=BIT},
      </if>
      <if test="record.cycleFrequencyBeginTime != null">
        cycle_frequency_begin_time = #{record.cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cycleFrequencyEndTime != null">
        cycle_frequency_end_time = #{record.cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cycleFrequencyInterval != null">
        cycle_frequency_interval = #{record.cycleFrequencyInterval,jdbcType=INTEGER},
      </if>
      <if test="record.cycleFrequencyLimit != null">
        cycle_frequency_limit = #{record.cycleFrequencyLimit,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fc_gd_order_ext
    set id = #{record.id,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=INTEGER},
      order_amount = #{record.orderAmount,jdbcType=BIGINT},
      order_cpm = #{record.orderCpm,jdbcType=INTEGER},
      order_launch_time_type = #{record.orderLaunchTimeType,jdbcType=TINYINT},
      order_source = #{record.orderSource,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      enable_cycle_frequency = #{record.enableCycleFrequency,jdbcType=BIT},
      cycle_frequency_begin_time = #{record.cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      cycle_frequency_end_time = #{record.cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      cycle_frequency_interval = #{record.cycleFrequencyInterval,jdbcType=INTEGER},
      cycle_frequency_limit = #{record.cycleFrequencyLimit,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    update fc_gd_order_ext
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="orderCpm != null">
        order_cpm = #{orderCpm,jdbcType=INTEGER},
      </if>
      <if test="orderLaunchTimeType != null">
        order_launch_time_type = #{orderLaunchTimeType,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableCycleFrequency != null">
        enable_cycle_frequency = #{enableCycleFrequency,jdbcType=BIT},
      </if>
      <if test="cycleFrequencyBeginTime != null">
        cycle_frequency_begin_time = #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyEndTime != null">
        cycle_frequency_end_time = #{cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyInterval != null">
        cycle_frequency_interval = #{cycleFrequencyInterval,jdbcType=INTEGER},
      </if>
      <if test="cycleFrequencyLimit != null">
        cycle_frequency_limit = #{cycleFrequencyLimit,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    update fc_gd_order_ext
    set order_id = #{orderId,jdbcType=INTEGER},
      order_amount = #{orderAmount,jdbcType=BIGINT},
      order_cpm = #{orderCpm,jdbcType=INTEGER},
      order_launch_time_type = #{orderLaunchTimeType,jdbcType=TINYINT},
      order_source = #{orderSource,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      enable_cycle_frequency = #{enableCycleFrequency,jdbcType=BIT},
      cycle_frequency_begin_time = #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      cycle_frequency_end_time = #{cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      cycle_frequency_interval = #{cycleFrequencyInterval,jdbcType=INTEGER},
      cycle_frequency_limit = #{cycleFrequencyLimit,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fc_gd_order_ext (order_id, order_amount, order_cpm, 
      order_launch_time_type, order_source, is_deleted, 
      ctime, mtime, enable_cycle_frequency, 
      cycle_frequency_begin_time, cycle_frequency_end_time, 
      cycle_frequency_interval, cycle_frequency_limit
      )
    values (#{orderId,jdbcType=INTEGER}, #{orderAmount,jdbcType=BIGINT}, #{orderCpm,jdbcType=INTEGER}, 
      #{orderLaunchTimeType,jdbcType=TINYINT}, #{orderSource,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{enableCycleFrequency,jdbcType=BIT}, 
      #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP}, #{cycleFrequencyEndTime,jdbcType=TIMESTAMP}, 
      #{cycleFrequencyInterval,jdbcType=INTEGER}, #{cycleFrequencyLimit,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      order_amount = values(order_amount),
      order_cpm = values(order_cpm),
      order_launch_time_type = values(order_launch_time_type),
      order_source = values(order_source),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      enable_cycle_frequency = values(enable_cycle_frequency),
      cycle_frequency_begin_time = values(cycle_frequency_begin_time),
      cycle_frequency_end_time = values(cycle_frequency_end_time),
      cycle_frequency_interval = values(cycle_frequency_interval),
      cycle_frequency_limit = values(cycle_frequency_limit),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      fc_gd_order_ext
      (order_id,order_amount,order_cpm,order_launch_time_type,order_source,is_deleted,ctime,mtime,enable_cycle_frequency,cycle_frequency_begin_time,cycle_frequency_end_time,cycle_frequency_interval,cycle_frequency_limit)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=INTEGER},
        #{item.orderAmount,jdbcType=BIGINT},
        #{item.orderCpm,jdbcType=INTEGER},
        #{item.orderLaunchTimeType,jdbcType=TINYINT},
        #{item.orderSource,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.enableCycleFrequency,jdbcType=BIT},
        #{item.cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
        #{item.cycleFrequencyEndTime,jdbcType=TIMESTAMP},
        #{item.cycleFrequencyInterval,jdbcType=INTEGER},
        #{item.cycleFrequencyLimit,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      fc_gd_order_ext
      (order_id,order_amount,order_cpm,order_launch_time_type,order_source,is_deleted,ctime,mtime,enable_cycle_frequency,cycle_frequency_begin_time,cycle_frequency_end_time,cycle_frequency_interval,cycle_frequency_limit)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.orderId,jdbcType=INTEGER},
        #{item.orderAmount,jdbcType=BIGINT},
        #{item.orderCpm,jdbcType=INTEGER},
        #{item.orderLaunchTimeType,jdbcType=TINYINT},
        #{item.orderSource,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.enableCycleFrequency,jdbcType=BIT},
        #{item.cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
        #{item.cycleFrequencyEndTime,jdbcType=TIMESTAMP},
        #{item.cycleFrequencyInterval,jdbcType=INTEGER},
        #{item.cycleFrequencyLimit,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      order_id = values(order_id),
      order_amount = values(order_amount),
      order_cpm = values(order_cpm),
      order_launch_time_type = values(order_launch_time_type),
      order_source = values(order_source),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      enable_cycle_frequency = values(enable_cycle_frequency),
      cycle_frequency_begin_time = values(cycle_frequency_begin_time),
      cycle_frequency_end_time = values(cycle_frequency_end_time),
      cycle_frequency_interval = values(cycle_frequency_interval),
      cycle_frequency_limit = values(cycle_frequency_limit),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.order.po.FcGdOrderExtPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fc_gd_order_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="orderCpm != null">
        order_cpm,
      </if>
      <if test="orderLaunchTimeType != null">
        order_launch_time_type,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="enableCycleFrequency != null">
        enable_cycle_frequency,
      </if>
      <if test="cycleFrequencyBeginTime != null">
        cycle_frequency_begin_time,
      </if>
      <if test="cycleFrequencyEndTime != null">
        cycle_frequency_end_time,
      </if>
      <if test="cycleFrequencyInterval != null">
        cycle_frequency_interval,
      </if>
      <if test="cycleFrequencyLimit != null">
        cycle_frequency_limit,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=BIGINT},
      </if>
      <if test="orderCpm != null">
        #{orderCpm,jdbcType=INTEGER},
      </if>
      <if test="orderLaunchTimeType != null">
        #{orderLaunchTimeType,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="enableCycleFrequency != null">
        #{enableCycleFrequency,jdbcType=BIT},
      </if>
      <if test="cycleFrequencyBeginTime != null">
        #{cycleFrequencyBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyEndTime != null">
        #{cycleFrequencyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleFrequencyInterval != null">
        #{cycleFrequencyInterval,jdbcType=INTEGER},
      </if>
      <if test="cycleFrequencyLimit != null">
        #{cycleFrequencyLimit,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="orderAmount != null">
        order_amount = values(order_amount),
      </if>
      <if test="orderCpm != null">
        order_cpm = values(order_cpm),
      </if>
      <if test="orderLaunchTimeType != null">
        order_launch_time_type = values(order_launch_time_type),
      </if>
      <if test="orderSource != null">
        order_source = values(order_source),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="enableCycleFrequency != null">
        enable_cycle_frequency = values(enable_cycle_frequency),
      </if>
      <if test="cycleFrequencyBeginTime != null">
        cycle_frequency_begin_time = values(cycle_frequency_begin_time),
      </if>
      <if test="cycleFrequencyEndTime != null">
        cycle_frequency_end_time = values(cycle_frequency_end_time),
      </if>
      <if test="cycleFrequencyInterval != null">
        cycle_frequency_interval = values(cycle_frequency_interval),
      </if>
      <if test="cycleFrequencyLimit != null">
        cycle_frequency_limit = values(cycle_frequency_limit),
      </if>
    </trim>
  </insert>
</mapper>