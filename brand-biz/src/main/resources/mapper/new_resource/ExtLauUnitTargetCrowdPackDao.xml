<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.resource.res_dao.ExtLauUnitTargetCrowdPackDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.resource.pojo.LauUnitTargetCrowdPackPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="crowd_pack_id" jdbcType="INTEGER" property="crowdPackId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <insert id="batchSave" parameterType="list">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_target_crowd_pack (unit_id, crowd_pack_id, type)
    values 
    <foreach item="entity" index="index" collection="recordList" open="" separator="," close="">
    (#{entity.unitId,jdbcType=INTEGER}, #{entity.crowdPackId,jdbcType=INTEGER}, 
    #{entity.type,jdbcType=TINYINT})
    </foreach>
    ON DUPLICATE KEY UPDATE is_deleted=0, type = values(type);
  </insert>
</mapper>