<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.resource.res_dao.BrandBusinessAtmosphereDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_id" jdbcType="BIGINT" property="contentId" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="first_business_type" jdbcType="INTEGER" property="firstBusinessType" />
    <result column="second_business_type" jdbcType="INTEGER" property="secondBusinessType" />
    <result column="season_begin_time" jdbcType="TIMESTAMP" property="seasonBeginTime" />
    <result column="ogv_control_priority" jdbcType="INTEGER" property="ogvControlPriority" />
    <result column="contract_number" jdbcType="BIGINT" property="contractNumber" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, content_id, content_type, first_business_type, second_business_type, season_begin_time, 
    ogv_control_priority, contract_number, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from brand_business_atmosphere
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from brand_business_atmosphere
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from brand_business_atmosphere
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePoExample">
    delete from brand_business_atmosphere
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into brand_business_atmosphere (content_id, content_type, first_business_type, 
      second_business_type, season_begin_time, 
      ogv_control_priority, contract_number, ctime, 
      mtime)
    values (#{contentId,jdbcType=BIGINT}, #{contentType,jdbcType=INTEGER}, #{firstBusinessType,jdbcType=INTEGER}, 
      #{secondBusinessType,jdbcType=INTEGER}, #{seasonBeginTime,jdbcType=TIMESTAMP}, 
      #{ogvControlPriority,jdbcType=INTEGER}, #{contractNumber,jdbcType=BIGINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into brand_business_atmosphere
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        content_id,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="firstBusinessType != null">
        first_business_type,
      </if>
      <if test="secondBusinessType != null">
        second_business_type,
      </if>
      <if test="seasonBeginTime != null">
        season_begin_time,
      </if>
      <if test="ogvControlPriority != null">
        ogv_control_priority,
      </if>
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        #{contentId,jdbcType=BIGINT},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="firstBusinessType != null">
        #{firstBusinessType,jdbcType=INTEGER},
      </if>
      <if test="secondBusinessType != null">
        #{secondBusinessType,jdbcType=INTEGER},
      </if>
      <if test="seasonBeginTime != null">
        #{seasonBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ogvControlPriority != null">
        #{ogvControlPriority,jdbcType=INTEGER},
      </if>
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePoExample" resultType="java.lang.Long">
    select count(*) from brand_business_atmosphere
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update brand_business_atmosphere
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.contentId != null">
        content_id = #{record.contentId,jdbcType=BIGINT},
      </if>
      <if test="record.contentType != null">
        content_type = #{record.contentType,jdbcType=INTEGER},
      </if>
      <if test="record.firstBusinessType != null">
        first_business_type = #{record.firstBusinessType,jdbcType=INTEGER},
      </if>
      <if test="record.secondBusinessType != null">
        second_business_type = #{record.secondBusinessType,jdbcType=INTEGER},
      </if>
      <if test="record.seasonBeginTime != null">
        season_begin_time = #{record.seasonBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ogvControlPriority != null">
        ogv_control_priority = #{record.ogvControlPriority,jdbcType=INTEGER},
      </if>
      <if test="record.contractNumber != null">
        contract_number = #{record.contractNumber,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update brand_business_atmosphere
    set id = #{record.id,jdbcType=BIGINT},
      content_id = #{record.contentId,jdbcType=BIGINT},
      content_type = #{record.contentType,jdbcType=INTEGER},
      first_business_type = #{record.firstBusinessType,jdbcType=INTEGER},
      second_business_type = #{record.secondBusinessType,jdbcType=INTEGER},
      season_begin_time = #{record.seasonBeginTime,jdbcType=TIMESTAMP},
      ogv_control_priority = #{record.ogvControlPriority,jdbcType=INTEGER},
      contract_number = #{record.contractNumber,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    update brand_business_atmosphere
    <set>
      <if test="contentId != null">
        content_id = #{contentId,jdbcType=BIGINT},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=INTEGER},
      </if>
      <if test="firstBusinessType != null">
        first_business_type = #{firstBusinessType,jdbcType=INTEGER},
      </if>
      <if test="secondBusinessType != null">
        second_business_type = #{secondBusinessType,jdbcType=INTEGER},
      </if>
      <if test="seasonBeginTime != null">
        season_begin_time = #{seasonBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ogvControlPriority != null">
        ogv_control_priority = #{ogvControlPriority,jdbcType=INTEGER},
      </if>
      <if test="contractNumber != null">
        contract_number = #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    update brand_business_atmosphere
    set content_id = #{contentId,jdbcType=BIGINT},
      content_type = #{contentType,jdbcType=INTEGER},
      first_business_type = #{firstBusinessType,jdbcType=INTEGER},
      second_business_type = #{secondBusinessType,jdbcType=INTEGER},
      season_begin_time = #{seasonBeginTime,jdbcType=TIMESTAMP},
      ogv_control_priority = #{ogvControlPriority,jdbcType=INTEGER},
      contract_number = #{contractNumber,jdbcType=BIGINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into brand_business_atmosphere (content_id, content_type, first_business_type, 
      second_business_type, season_begin_time, 
      ogv_control_priority, contract_number, ctime, 
      mtime)
    values (#{contentId,jdbcType=BIGINT}, #{contentType,jdbcType=INTEGER}, #{firstBusinessType,jdbcType=INTEGER}, 
      #{secondBusinessType,jdbcType=INTEGER}, #{seasonBeginTime,jdbcType=TIMESTAMP}, 
      #{ogvControlPriority,jdbcType=INTEGER}, #{contractNumber,jdbcType=BIGINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      content_id = values(content_id),
      content_type = values(content_type),
      first_business_type = values(first_business_type),
      second_business_type = values(second_business_type),
      season_begin_time = values(season_begin_time),
      ogv_control_priority = values(ogv_control_priority),
      contract_number = values(contract_number),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      brand_business_atmosphere
      (content_id,content_type,first_business_type,second_business_type,season_begin_time,ogv_control_priority,contract_number,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.contentId,jdbcType=BIGINT},
        #{item.contentType,jdbcType=INTEGER},
        #{item.firstBusinessType,jdbcType=INTEGER},
        #{item.secondBusinessType,jdbcType=INTEGER},
        #{item.seasonBeginTime,jdbcType=TIMESTAMP},
        #{item.ogvControlPriority,jdbcType=INTEGER},
        #{item.contractNumber,jdbcType=BIGINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      brand_business_atmosphere
      (content_id,content_type,first_business_type,second_business_type,season_begin_time,ogv_control_priority,contract_number,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.contentId,jdbcType=BIGINT},
        #{item.contentType,jdbcType=INTEGER},
        #{item.firstBusinessType,jdbcType=INTEGER},
        #{item.secondBusinessType,jdbcType=INTEGER},
        #{item.seasonBeginTime,jdbcType=TIMESTAMP},
        #{item.ogvControlPriority,jdbcType=INTEGER},
        #{item.contractNumber,jdbcType=BIGINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      content_id = values(content_id),
      content_type = values(content_type),
      first_business_type = values(first_business_type),
      second_business_type = values(second_business_type),
      season_begin_time = values(season_begin_time),
      ogv_control_priority = values(ogv_control_priority),
      contract_number = values(contract_number),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into brand_business_atmosphere
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        content_id,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="firstBusinessType != null">
        first_business_type,
      </if>
      <if test="secondBusinessType != null">
        second_business_type,
      </if>
      <if test="seasonBeginTime != null">
        season_begin_time,
      </if>
      <if test="ogvControlPriority != null">
        ogv_control_priority,
      </if>
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contentId != null">
        #{contentId,jdbcType=BIGINT},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=INTEGER},
      </if>
      <if test="firstBusinessType != null">
        #{firstBusinessType,jdbcType=INTEGER},
      </if>
      <if test="secondBusinessType != null">
        #{secondBusinessType,jdbcType=INTEGER},
      </if>
      <if test="seasonBeginTime != null">
        #{seasonBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ogvControlPriority != null">
        #{ogvControlPriority,jdbcType=INTEGER},
      </if>
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="contentId != null">
        content_id = values(content_id),
      </if>
      <if test="contentType != null">
        content_type = values(content_type),
      </if>
      <if test="firstBusinessType != null">
        first_business_type = values(first_business_type),
      </if>
      <if test="secondBusinessType != null">
        second_business_type = values(second_business_type),
      </if>
      <if test="seasonBeginTime != null">
        season_begin_time = values(season_begin_time),
      </if>
      <if test="ogvControlPriority != null">
        ogv_control_priority = values(ogv_control_priority),
      </if>
      <if test="contractNumber != null">
        contract_number = values(contract_number),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>