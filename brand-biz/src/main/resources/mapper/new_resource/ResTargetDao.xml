<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.resource.res_dao.ResTargetDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.resource.pojo.ResTargetPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="is_input" jdbcType="TINYINT" property="isInput" />
    <result column="is_multi_group_selected" jdbcType="TINYINT" property="isMultiGroupSelected" />
    <result column="has_mapping" jdbcType="TINYINT" property="hasMapping" />
    <result column="mapping_type" jdbcType="TINYINT" property="mappingType" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, type, name, is_input, is_multi_group_selected, has_mapping, mapping_type, sort_order, 
    status, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from res_target
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from res_target
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPoExample">
    delete from res_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target (id, type, name, 
      is_input, is_multi_group_selected, has_mapping, 
      mapping_type, sort_order, status, 
      is_deleted, ctime, mtime
      )
    values (#{id,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{isInput,jdbcType=TINYINT}, #{isMultiGroupSelected,jdbcType=TINYINT}, #{hasMapping,jdbcType=TINYINT}, 
      #{mappingType,jdbcType=TINYINT}, #{sortOrder,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="isInput != null">
        is_input,
      </if>
      <if test="isMultiGroupSelected != null">
        is_multi_group_selected,
      </if>
      <if test="hasMapping != null">
        has_mapping,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="isInput != null">
        #{isInput,jdbcType=TINYINT},
      </if>
      <if test="isMultiGroupSelected != null">
        #{isMultiGroupSelected,jdbcType=TINYINT},
      </if>
      <if test="hasMapping != null">
        #{hasMapping,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPoExample" resultType="java.lang.Long">
    select count(*) from res_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_target
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.isInput != null">
        is_input = #{record.isInput,jdbcType=TINYINT},
      </if>
      <if test="record.isMultiGroupSelected != null">
        is_multi_group_selected = #{record.isMultiGroupSelected,jdbcType=TINYINT},
      </if>
      <if test="record.hasMapping != null">
        has_mapping = #{record.hasMapping,jdbcType=TINYINT},
      </if>
      <if test="record.mappingType != null">
        mapping_type = #{record.mappingType,jdbcType=TINYINT},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_target
    set id = #{record.id,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      is_input = #{record.isInput,jdbcType=TINYINT},
      is_multi_group_selected = #{record.isMultiGroupSelected,jdbcType=TINYINT},
      has_mapping = #{record.hasMapping,jdbcType=TINYINT},
      mapping_type = #{record.mappingType,jdbcType=TINYINT},
      sort_order = #{record.sortOrder,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPo">
    update res_target
    <set>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="isInput != null">
        is_input = #{isInput,jdbcType=TINYINT},
      </if>
      <if test="isMultiGroupSelected != null">
        is_multi_group_selected = #{isMultiGroupSelected,jdbcType=TINYINT},
      </if>
      <if test="hasMapping != null">
        has_mapping = #{hasMapping,jdbcType=TINYINT},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=TINYINT},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.resource.pojo.ResTargetPo">
    update res_target
    set type = #{type,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      is_input = #{isInput,jdbcType=TINYINT},
      is_multi_group_selected = #{isMultiGroupSelected,jdbcType=TINYINT},
      has_mapping = #{hasMapping,jdbcType=TINYINT},
      mapping_type = #{mappingType,jdbcType=TINYINT},
      sort_order = #{sortOrder,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>