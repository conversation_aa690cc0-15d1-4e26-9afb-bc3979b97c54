<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.order.adxdao.AdxStatOrderDayDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="bidder_id" jdbcType="INTEGER" property="bidderId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="group_time" jdbcType="TIMESTAMP" property="groupTime" />
    <result column="show_count" jdbcType="INTEGER" property="showCount" />
    <result column="click_count" jdbcType="INTEGER" property="clickCount" />
    <result column="charged_cost_milli" jdbcType="BIGINT" property="chargedCostMilli" />
    <result column="bid_cost_milli" jdbcType="BIGINT" property="bidCostMilli" />
    <result column="ac_show_count" jdbcType="INTEGER" property="acShowCount" />
    <result column="ac_click_count" jdbcType="INTEGER" property="acClickCount" />
    <result column="ac_cost_milli" jdbcType="BIGINT" property="acCostMilli" />
    <result column="ac_bid_cost_milli" jdbcType="BIGINT" property="acBidCostMilli" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="hour_show_count" jdbcType="INTEGER" property="hourShowCount" />
    <result column="hour_click_count" jdbcType="INTEGER" property="hourClickCount" />
    <result column="hour_charged_cost_milli" jdbcType="BIGINT" property="hourChargedCostMilli" />
    <result column="latest_group_hour" jdbcType="TIMESTAMP" property="latestGroupHour" />
    <result column="hour_uncharged_cost_milli" jdbcType="BIGINT" property="hourUnchargedCostMilli" />
    <result column="hour_ac_show_count" jdbcType="INTEGER" property="hourAcShowCount" />
    <result column="hour_ac_click_count" jdbcType="INTEGER" property="hourAcClickCount" />
    <result column="hour_ac_cost_milli" jdbcType="BIGINT" property="hourAcCostMilli" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bidder_id, order_id, group_time, show_count, click_count, charged_cost_milli, 
    bid_cost_milli, ac_show_count, ac_click_count, ac_cost_milli, ac_bid_cost_milli, 
    version, ctime, mtime, is_deleted, hour_show_count, hour_click_count, hour_charged_cost_milli, 
    latest_group_hour, hour_uncharged_cost_milli, hour_ac_show_count, hour_ac_click_count, 
    hour_ac_cost_milli
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adx_stat_order_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from adx_stat_order_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from adx_stat_order_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPoExample">
    delete from adx_stat_order_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into adx_stat_order_day (bidder_id, order_id, group_time, 
      show_count, click_count, charged_cost_milli, 
      bid_cost_milli, ac_show_count, ac_click_count, 
      ac_cost_milli, ac_bid_cost_milli, version, 
      ctime, mtime, is_deleted, 
      hour_show_count, hour_click_count, hour_charged_cost_milli, 
      latest_group_hour, hour_uncharged_cost_milli, 
      hour_ac_show_count, hour_ac_click_count, hour_ac_cost_milli
      )
    values (#{bidderId,jdbcType=INTEGER}, #{orderId,jdbcType=BIGINT}, #{groupTime,jdbcType=TIMESTAMP}, 
      #{showCount,jdbcType=INTEGER}, #{clickCount,jdbcType=INTEGER}, #{chargedCostMilli,jdbcType=BIGINT}, 
      #{bidCostMilli,jdbcType=BIGINT}, #{acShowCount,jdbcType=INTEGER}, #{acClickCount,jdbcType=INTEGER}, 
      #{acCostMilli,jdbcType=BIGINT}, #{acBidCostMilli,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{hourShowCount,jdbcType=INTEGER}, #{hourClickCount,jdbcType=INTEGER}, #{hourChargedCostMilli,jdbcType=BIGINT}, 
      #{latestGroupHour,jdbcType=TIMESTAMP}, #{hourUnchargedCostMilli,jdbcType=BIGINT}, 
      #{hourAcShowCount,jdbcType=INTEGER}, #{hourAcClickCount,jdbcType=INTEGER}, #{hourAcCostMilli,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into adx_stat_order_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bidderId != null">
        bidder_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="groupTime != null">
        group_time,
      </if>
      <if test="showCount != null">
        show_count,
      </if>
      <if test="clickCount != null">
        click_count,
      </if>
      <if test="chargedCostMilli != null">
        charged_cost_milli,
      </if>
      <if test="bidCostMilli != null">
        bid_cost_milli,
      </if>
      <if test="acShowCount != null">
        ac_show_count,
      </if>
      <if test="acClickCount != null">
        ac_click_count,
      </if>
      <if test="acCostMilli != null">
        ac_cost_milli,
      </if>
      <if test="acBidCostMilli != null">
        ac_bid_cost_milli,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="hourShowCount != null">
        hour_show_count,
      </if>
      <if test="hourClickCount != null">
        hour_click_count,
      </if>
      <if test="hourChargedCostMilli != null">
        hour_charged_cost_milli,
      </if>
      <if test="latestGroupHour != null">
        latest_group_hour,
      </if>
      <if test="hourUnchargedCostMilli != null">
        hour_uncharged_cost_milli,
      </if>
      <if test="hourAcShowCount != null">
        hour_ac_show_count,
      </if>
      <if test="hourAcClickCount != null">
        hour_ac_click_count,
      </if>
      <if test="hourAcCostMilli != null">
        hour_ac_cost_milli,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bidderId != null">
        #{bidderId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="groupTime != null">
        #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showCount != null">
        #{showCount,jdbcType=INTEGER},
      </if>
      <if test="clickCount != null">
        #{clickCount,jdbcType=INTEGER},
      </if>
      <if test="chargedCostMilli != null">
        #{chargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="bidCostMilli != null">
        #{bidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acShowCount != null">
        #{acShowCount,jdbcType=INTEGER},
      </if>
      <if test="acClickCount != null">
        #{acClickCount,jdbcType=INTEGER},
      </if>
      <if test="acCostMilli != null">
        #{acCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acBidCostMilli != null">
        #{acBidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="hourShowCount != null">
        #{hourShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourClickCount != null">
        #{hourClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourChargedCostMilli != null">
        #{hourChargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="latestGroupHour != null">
        #{latestGroupHour,jdbcType=TIMESTAMP},
      </if>
      <if test="hourUnchargedCostMilli != null">
        #{hourUnchargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="hourAcShowCount != null">
        #{hourAcShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcClickCount != null">
        #{hourAcClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcCostMilli != null">
        #{hourAcCostMilli,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPoExample" resultType="java.lang.Long">
    select count(*) from adx_stat_order_day
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update adx_stat_order_day
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bidderId != null">
        bidder_id = #{record.bidderId,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.groupTime != null">
        group_time = #{record.groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.showCount != null">
        show_count = #{record.showCount,jdbcType=INTEGER},
      </if>
      <if test="record.clickCount != null">
        click_count = #{record.clickCount,jdbcType=INTEGER},
      </if>
      <if test="record.chargedCostMilli != null">
        charged_cost_milli = #{record.chargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.bidCostMilli != null">
        bid_cost_milli = #{record.bidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.acShowCount != null">
        ac_show_count = #{record.acShowCount,jdbcType=INTEGER},
      </if>
      <if test="record.acClickCount != null">
        ac_click_count = #{record.acClickCount,jdbcType=INTEGER},
      </if>
      <if test="record.acCostMilli != null">
        ac_cost_milli = #{record.acCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.acBidCostMilli != null">
        ac_bid_cost_milli = #{record.acBidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.hourShowCount != null">
        hour_show_count = #{record.hourShowCount,jdbcType=INTEGER},
      </if>
      <if test="record.hourClickCount != null">
        hour_click_count = #{record.hourClickCount,jdbcType=INTEGER},
      </if>
      <if test="record.hourChargedCostMilli != null">
        hour_charged_cost_milli = #{record.hourChargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.latestGroupHour != null">
        latest_group_hour = #{record.latestGroupHour,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hourUnchargedCostMilli != null">
        hour_uncharged_cost_milli = #{record.hourUnchargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="record.hourAcShowCount != null">
        hour_ac_show_count = #{record.hourAcShowCount,jdbcType=INTEGER},
      </if>
      <if test="record.hourAcClickCount != null">
        hour_ac_click_count = #{record.hourAcClickCount,jdbcType=INTEGER},
      </if>
      <if test="record.hourAcCostMilli != null">
        hour_ac_cost_milli = #{record.hourAcCostMilli,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update adx_stat_order_day
    set id = #{record.id,jdbcType=BIGINT},
      bidder_id = #{record.bidderId,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=BIGINT},
      group_time = #{record.groupTime,jdbcType=TIMESTAMP},
      show_count = #{record.showCount,jdbcType=INTEGER},
      click_count = #{record.clickCount,jdbcType=INTEGER},
      charged_cost_milli = #{record.chargedCostMilli,jdbcType=BIGINT},
      bid_cost_milli = #{record.bidCostMilli,jdbcType=BIGINT},
      ac_show_count = #{record.acShowCount,jdbcType=INTEGER},
      ac_click_count = #{record.acClickCount,jdbcType=INTEGER},
      ac_cost_milli = #{record.acCostMilli,jdbcType=BIGINT},
      ac_bid_cost_milli = #{record.acBidCostMilli,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      hour_show_count = #{record.hourShowCount,jdbcType=INTEGER},
      hour_click_count = #{record.hourClickCount,jdbcType=INTEGER},
      hour_charged_cost_milli = #{record.hourChargedCostMilli,jdbcType=BIGINT},
      latest_group_hour = #{record.latestGroupHour,jdbcType=TIMESTAMP},
      hour_uncharged_cost_milli = #{record.hourUnchargedCostMilli,jdbcType=BIGINT},
      hour_ac_show_count = #{record.hourAcShowCount,jdbcType=INTEGER},
      hour_ac_click_count = #{record.hourAcClickCount,jdbcType=INTEGER},
      hour_ac_cost_milli = #{record.hourAcCostMilli,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    update adx_stat_order_day
    <set>
      <if test="bidderId != null">
        bidder_id = #{bidderId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="groupTime != null">
        group_time = #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showCount != null">
        show_count = #{showCount,jdbcType=INTEGER},
      </if>
      <if test="clickCount != null">
        click_count = #{clickCount,jdbcType=INTEGER},
      </if>
      <if test="chargedCostMilli != null">
        charged_cost_milli = #{chargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="bidCostMilli != null">
        bid_cost_milli = #{bidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acShowCount != null">
        ac_show_count = #{acShowCount,jdbcType=INTEGER},
      </if>
      <if test="acClickCount != null">
        ac_click_count = #{acClickCount,jdbcType=INTEGER},
      </if>
      <if test="acCostMilli != null">
        ac_cost_milli = #{acCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acBidCostMilli != null">
        ac_bid_cost_milli = #{acBidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="hourShowCount != null">
        hour_show_count = #{hourShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourClickCount != null">
        hour_click_count = #{hourClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourChargedCostMilli != null">
        hour_charged_cost_milli = #{hourChargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="latestGroupHour != null">
        latest_group_hour = #{latestGroupHour,jdbcType=TIMESTAMP},
      </if>
      <if test="hourUnchargedCostMilli != null">
        hour_uncharged_cost_milli = #{hourUnchargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="hourAcShowCount != null">
        hour_ac_show_count = #{hourAcShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcClickCount != null">
        hour_ac_click_count = #{hourAcClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcCostMilli != null">
        hour_ac_cost_milli = #{hourAcCostMilli,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    update adx_stat_order_day
    set bidder_id = #{bidderId,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=BIGINT},
      group_time = #{groupTime,jdbcType=TIMESTAMP},
      show_count = #{showCount,jdbcType=INTEGER},
      click_count = #{clickCount,jdbcType=INTEGER},
      charged_cost_milli = #{chargedCostMilli,jdbcType=BIGINT},
      bid_cost_milli = #{bidCostMilli,jdbcType=BIGINT},
      ac_show_count = #{acShowCount,jdbcType=INTEGER},
      ac_click_count = #{acClickCount,jdbcType=INTEGER},
      ac_cost_milli = #{acCostMilli,jdbcType=BIGINT},
      ac_bid_cost_milli = #{acBidCostMilli,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      hour_show_count = #{hourShowCount,jdbcType=INTEGER},
      hour_click_count = #{hourClickCount,jdbcType=INTEGER},
      hour_charged_cost_milli = #{hourChargedCostMilli,jdbcType=BIGINT},
      latest_group_hour = #{latestGroupHour,jdbcType=TIMESTAMP},
      hour_uncharged_cost_milli = #{hourUnchargedCostMilli,jdbcType=BIGINT},
      hour_ac_show_count = #{hourAcShowCount,jdbcType=INTEGER},
      hour_ac_click_count = #{hourAcClickCount,jdbcType=INTEGER},
      hour_ac_cost_milli = #{hourAcCostMilli,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into adx_stat_order_day (bidder_id, order_id, group_time, 
      show_count, click_count, charged_cost_milli, 
      bid_cost_milli, ac_show_count, ac_click_count, 
      ac_cost_milli, ac_bid_cost_milli, version, 
      ctime, mtime, is_deleted, 
      hour_show_count, hour_click_count, hour_charged_cost_milli, 
      latest_group_hour, hour_uncharged_cost_milli, 
      hour_ac_show_count, hour_ac_click_count, hour_ac_cost_milli
      )
    values (#{bidderId,jdbcType=INTEGER}, #{orderId,jdbcType=BIGINT}, #{groupTime,jdbcType=TIMESTAMP}, 
      #{showCount,jdbcType=INTEGER}, #{clickCount,jdbcType=INTEGER}, #{chargedCostMilli,jdbcType=BIGINT}, 
      #{bidCostMilli,jdbcType=BIGINT}, #{acShowCount,jdbcType=INTEGER}, #{acClickCount,jdbcType=INTEGER}, 
      #{acCostMilli,jdbcType=BIGINT}, #{acBidCostMilli,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{hourShowCount,jdbcType=INTEGER}, #{hourClickCount,jdbcType=INTEGER}, #{hourChargedCostMilli,jdbcType=BIGINT}, 
      #{latestGroupHour,jdbcType=TIMESTAMP}, #{hourUnchargedCostMilli,jdbcType=BIGINT}, 
      #{hourAcShowCount,jdbcType=INTEGER}, #{hourAcClickCount,jdbcType=INTEGER}, #{hourAcCostMilli,jdbcType=BIGINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      bidder_id = values(bidder_id),
      order_id = values(order_id),
      group_time = values(group_time),
      show_count = values(show_count),
      click_count = values(click_count),
      charged_cost_milli = values(charged_cost_milli),
      bid_cost_milli = values(bid_cost_milli),
      ac_show_count = values(ac_show_count),
      ac_click_count = values(ac_click_count),
      ac_cost_milli = values(ac_cost_milli),
      ac_bid_cost_milli = values(ac_bid_cost_milli),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      hour_show_count = values(hour_show_count),
      hour_click_count = values(hour_click_count),
      hour_charged_cost_milli = values(hour_charged_cost_milli),
      latest_group_hour = values(latest_group_hour),
      hour_uncharged_cost_milli = values(hour_uncharged_cost_milli),
      hour_ac_show_count = values(hour_ac_show_count),
      hour_ac_click_count = values(hour_ac_click_count),
      hour_ac_cost_milli = values(hour_ac_cost_milli),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      adx_stat_order_day
      (bidder_id,order_id,group_time,show_count,click_count,charged_cost_milli,bid_cost_milli,ac_show_count,ac_click_count,ac_cost_milli,ac_bid_cost_milli,version,ctime,mtime,is_deleted,hour_show_count,hour_click_count,hour_charged_cost_milli,latest_group_hour,hour_uncharged_cost_milli,hour_ac_show_count,hour_ac_click_count,hour_ac_cost_milli)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bidderId,jdbcType=INTEGER},
        #{item.orderId,jdbcType=BIGINT},
        #{item.groupTime,jdbcType=TIMESTAMP},
        #{item.showCount,jdbcType=INTEGER},
        #{item.clickCount,jdbcType=INTEGER},
        #{item.chargedCostMilli,jdbcType=BIGINT},
        #{item.bidCostMilli,jdbcType=BIGINT},
        #{item.acShowCount,jdbcType=INTEGER},
        #{item.acClickCount,jdbcType=INTEGER},
        #{item.acCostMilli,jdbcType=BIGINT},
        #{item.acBidCostMilli,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.hourShowCount,jdbcType=INTEGER},
        #{item.hourClickCount,jdbcType=INTEGER},
        #{item.hourChargedCostMilli,jdbcType=BIGINT},
        #{item.latestGroupHour,jdbcType=TIMESTAMP},
        #{item.hourUnchargedCostMilli,jdbcType=BIGINT},
        #{item.hourAcShowCount,jdbcType=INTEGER},
        #{item.hourAcClickCount,jdbcType=INTEGER},
        #{item.hourAcCostMilli,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      adx_stat_order_day
      (bidder_id,order_id,group_time,show_count,click_count,charged_cost_milli,bid_cost_milli,ac_show_count,ac_click_count,ac_cost_milli,ac_bid_cost_milli,version,ctime,mtime,is_deleted,hour_show_count,hour_click_count,hour_charged_cost_milli,latest_group_hour,hour_uncharged_cost_milli,hour_ac_show_count,hour_ac_click_count,hour_ac_cost_milli)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bidderId,jdbcType=INTEGER},
        #{item.orderId,jdbcType=BIGINT},
        #{item.groupTime,jdbcType=TIMESTAMP},
        #{item.showCount,jdbcType=INTEGER},
        #{item.clickCount,jdbcType=INTEGER},
        #{item.chargedCostMilli,jdbcType=BIGINT},
        #{item.bidCostMilli,jdbcType=BIGINT},
        #{item.acShowCount,jdbcType=INTEGER},
        #{item.acClickCount,jdbcType=INTEGER},
        #{item.acCostMilli,jdbcType=BIGINT},
        #{item.acBidCostMilli,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.hourShowCount,jdbcType=INTEGER},
        #{item.hourClickCount,jdbcType=INTEGER},
        #{item.hourChargedCostMilli,jdbcType=BIGINT},
        #{item.latestGroupHour,jdbcType=TIMESTAMP},
        #{item.hourUnchargedCostMilli,jdbcType=BIGINT},
        #{item.hourAcShowCount,jdbcType=INTEGER},
        #{item.hourAcClickCount,jdbcType=INTEGER},
        #{item.hourAcCostMilli,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      bidder_id = values(bidder_id),
      order_id = values(order_id),
      group_time = values(group_time),
      show_count = values(show_count),
      click_count = values(click_count),
      charged_cost_milli = values(charged_cost_milli),
      bid_cost_milli = values(bid_cost_milli),
      ac_show_count = values(ac_show_count),
      ac_click_count = values(ac_click_count),
      ac_cost_milli = values(ac_cost_milli),
      ac_bid_cost_milli = values(ac_bid_cost_milli),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      hour_show_count = values(hour_show_count),
      hour_click_count = values(hour_click_count),
      hour_charged_cost_milli = values(hour_charged_cost_milli),
      latest_group_hour = values(latest_group_hour),
      hour_uncharged_cost_milli = values(hour_uncharged_cost_milli),
      hour_ac_show_count = values(hour_ac_show_count),
      hour_ac_click_count = values(hour_ac_click_count),
      hour_ac_cost_milli = values(hour_ac_cost_milli),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.order.po.AdxStatOrderDayPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into adx_stat_order_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bidderId != null">
        bidder_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="groupTime != null">
        group_time,
      </if>
      <if test="showCount != null">
        show_count,
      </if>
      <if test="clickCount != null">
        click_count,
      </if>
      <if test="chargedCostMilli != null">
        charged_cost_milli,
      </if>
      <if test="bidCostMilli != null">
        bid_cost_milli,
      </if>
      <if test="acShowCount != null">
        ac_show_count,
      </if>
      <if test="acClickCount != null">
        ac_click_count,
      </if>
      <if test="acCostMilli != null">
        ac_cost_milli,
      </if>
      <if test="acBidCostMilli != null">
        ac_bid_cost_milli,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="hourShowCount != null">
        hour_show_count,
      </if>
      <if test="hourClickCount != null">
        hour_click_count,
      </if>
      <if test="hourChargedCostMilli != null">
        hour_charged_cost_milli,
      </if>
      <if test="latestGroupHour != null">
        latest_group_hour,
      </if>
      <if test="hourUnchargedCostMilli != null">
        hour_uncharged_cost_milli,
      </if>
      <if test="hourAcShowCount != null">
        hour_ac_show_count,
      </if>
      <if test="hourAcClickCount != null">
        hour_ac_click_count,
      </if>
      <if test="hourAcCostMilli != null">
        hour_ac_cost_milli,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bidderId != null">
        #{bidderId,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="groupTime != null">
        #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="showCount != null">
        #{showCount,jdbcType=INTEGER},
      </if>
      <if test="clickCount != null">
        #{clickCount,jdbcType=INTEGER},
      </if>
      <if test="chargedCostMilli != null">
        #{chargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="bidCostMilli != null">
        #{bidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acShowCount != null">
        #{acShowCount,jdbcType=INTEGER},
      </if>
      <if test="acClickCount != null">
        #{acClickCount,jdbcType=INTEGER},
      </if>
      <if test="acCostMilli != null">
        #{acCostMilli,jdbcType=BIGINT},
      </if>
      <if test="acBidCostMilli != null">
        #{acBidCostMilli,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="hourShowCount != null">
        #{hourShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourClickCount != null">
        #{hourClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourChargedCostMilli != null">
        #{hourChargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="latestGroupHour != null">
        #{latestGroupHour,jdbcType=TIMESTAMP},
      </if>
      <if test="hourUnchargedCostMilli != null">
        #{hourUnchargedCostMilli,jdbcType=BIGINT},
      </if>
      <if test="hourAcShowCount != null">
        #{hourAcShowCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcClickCount != null">
        #{hourAcClickCount,jdbcType=INTEGER},
      </if>
      <if test="hourAcCostMilli != null">
        #{hourAcCostMilli,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="bidderId != null">
        bidder_id = values(bidder_id),
      </if>
      <if test="orderId != null">
        order_id = values(order_id),
      </if>
      <if test="groupTime != null">
        group_time = values(group_time),
      </if>
      <if test="showCount != null">
        show_count = values(show_count),
      </if>
      <if test="clickCount != null">
        click_count = values(click_count),
      </if>
      <if test="chargedCostMilli != null">
        charged_cost_milli = values(charged_cost_milli),
      </if>
      <if test="bidCostMilli != null">
        bid_cost_milli = values(bid_cost_milli),
      </if>
      <if test="acShowCount != null">
        ac_show_count = values(ac_show_count),
      </if>
      <if test="acClickCount != null">
        ac_click_count = values(ac_click_count),
      </if>
      <if test="acCostMilli != null">
        ac_cost_milli = values(ac_cost_milli),
      </if>
      <if test="acBidCostMilli != null">
        ac_bid_cost_milli = values(ac_bid_cost_milli),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="hourShowCount != null">
        hour_show_count = values(hour_show_count),
      </if>
      <if test="hourClickCount != null">
        hour_click_count = values(hour_click_count),
      </if>
      <if test="hourChargedCostMilli != null">
        hour_charged_cost_milli = values(hour_charged_cost_milli),
      </if>
      <if test="latestGroupHour != null">
        latest_group_hour = values(latest_group_hour),
      </if>
      <if test="hourUnchargedCostMilli != null">
        hour_uncharged_cost_milli = values(hour_uncharged_cost_milli),
      </if>
      <if test="hourAcShowCount != null">
        hour_ac_show_count = values(hour_ac_show_count),
      </if>
      <if test="hourAcClickCount != null">
        hour_ac_click_count = values(hour_ac_click_count),
      </if>
      <if test="hourAcCostMilli != null">
        hour_ac_cost_milli = values(hour_ac_cost_milli),
      </if>
    </trim>
  </insert>
</mapper>