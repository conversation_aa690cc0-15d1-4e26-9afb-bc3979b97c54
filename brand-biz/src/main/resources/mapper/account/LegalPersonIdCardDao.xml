<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.brand.biz.account.dao.LegalPersonIdCardDao">
	<resultMap id="LegalPersonIdCardResult" type="com.bilibili.brand.biz.account.po.LegalPersonIdCardPo">
        <id property="id" column="id" />
        <result property="accountId" column="account_id" />
        <result property="idCardUrl" column="idcard_url" />
        <result property="type" column="type" />
        <result property="isDeleted" column="is_deleted" />
        <result property="addTime" column="ctime" />
        <result property="updateTime" column="mtime" />
    </resultMap>
    
     <sql id="tbl_name">
        acc_legal_person_idcard
    </sql>

    <sql id="select_sql">
        select `id`, `account_id`, `idcard_url`, `type` from <include refid="tbl_name"/>
    </sql>

	<select id="getByAccountId" parameterType="map" resultMap="LegalPersonIdCardResult">
        <include refid="select_sql"/>
        WHERE
        account_id = #{accountId}
        and is_deleted=0
    </select>
	
    <select id="getByAccountIds" parameterType="map" resultMap="LegalPersonIdCardResult">
        <include refid="select_sql"/>
        WHERE
        account_id IN 
        <foreach item="accountId" index="index" collection="accountIds"
	                 open="(" separator="," close=")">
            #{accountId}
        </foreach>
        and is_deleted=0
    </select>
    
    <delete id="deleteByAccountId" parameterType="map">
    	update <include refid="tbl_name" /> set is_deleted=1 where account_id=#{accountId};
    </delete>
    
    <insert id="batchSave" parameterType="list">

        insert into <include refid="tbl_name" />
        (`account_id`, `idcard_url`, `type`)
        values
        <foreach item="entity" index="index" collection="poList"
	                 open="" separator="," close="">
            (#{entity.accountId},
            #{entity.idCardUrl},
            #{entity.type})
        </foreach>
        
    </insert>
    
</mapper>