<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.brand.biz.account.dao.AccountBiliUserDao">
    <resultMap id="resultMap" type="com.bilibili.brand.biz.account.po.AccountBiliUserPo">
        <result column="account_id" property="accountId" jdbcType="INTEGER"/>
        <result column="bili_username" property="biliUsername" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="all_columns">
        account_id,bili_username
    </sql>

    <insert id="insert" parameterType="com.bilibili.brand.biz.account.po.AccountBiliUserPo"
            useGeneratedKeys="true" keyProperty="accountId">
        INSERT INTO
        acc_account_bili_user(account_id,`bili_username`,`is_deleted`)
        VALUES
        (
        #{accountId},
        #{biliUsername},
        0
        )
    </insert>

    <select id="getListByBiliUsernameAndAccountId" resultType="java.lang.Integer">
        SELECT <include refid="all_columns"/> FROM acc_account_bili_user WHERE bili_username=#{bili_username} AND account_id=#{account_id} AND is_deleted=0
    </select>
    <select id="getAccountIdsByBiliUsername" resultType="java.lang.Integer" parameterType="java.lang.String">
        SELECT account_id FROM acc_account_bili_user WHERE bili_username=#{bili_username} AND is_deleted=0
    </select>

    <update id="delete">
        UPDATE acc_account_bili_user SET is_deleted=1 WHERE account_id=#{account_id} AND is_deleted=0
    </update>

    <select id="getListByAccountIds" resultMap="resultMap" parameterType="java.lang.String">
        SELECT
        <include refid="all_columns"/>
        FROM acc_account_bili_user
        WHERE
        account_id IN
        <foreach collection="account_ids" item="account_id" index="index" open="("
                 separator="," close=")">
            #{account_id}
        </foreach>
        and is_deleted=0
    </select>

</mapper>