<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.SsaSplashScreenVersionControlDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="splash_screen_id" jdbcType="INTEGER" property="splashScreenId" />
    <result column="start_version" jdbcType="INTEGER" property="startVersion" />
    <result column="end_version" jdbcType="INTEGER" property="endVersion" />
    <result column="platform_id" jdbcType="TINYINT" property="platformId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, splash_screen_id, start_version, end_version, platform_id, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ssa_splash_screen_version_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ssa_splash_screen_version_control
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ssa_splash_screen_version_control
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPoExample">
    delete from ssa_splash_screen_version_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_version_control (splash_screen_id, start_version, end_version, 
      platform_id, ctime, mtime, 
      is_deleted)
    values (#{splashScreenId,jdbcType=INTEGER}, #{startVersion,jdbcType=INTEGER}, #{endVersion,jdbcType=INTEGER}, 
      #{platformId,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_version_control
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        splash_screen_id,
      </if>
      <if test="startVersion != null">
        start_version,
      </if>
      <if test="endVersion != null">
        end_version,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="startVersion != null">
        #{startVersion,jdbcType=INTEGER},
      </if>
      <if test="endVersion != null">
        #{endVersion,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPoExample" resultType="java.lang.Long">
    select count(*) from ssa_splash_screen_version_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ssa_splash_screen_version_control
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.splashScreenId != null">
        splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="record.startVersion != null">
        start_version = #{record.startVersion,jdbcType=INTEGER},
      </if>
      <if test="record.endVersion != null">
        end_version = #{record.endVersion,jdbcType=INTEGER},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ssa_splash_screen_version_control
    set id = #{record.id,jdbcType=INTEGER},
      splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      start_version = #{record.startVersion,jdbcType=INTEGER},
      end_version = #{record.endVersion,jdbcType=INTEGER},
      platform_id = #{record.platformId,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">
    update ssa_splash_screen_version_control
    <set>
      <if test="splashScreenId != null">
        splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="startVersion != null">
        start_version = #{startVersion,jdbcType=INTEGER},
      </if>
      <if test="endVersion != null">
        end_version = #{endVersion,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVersionControlPo">
    update ssa_splash_screen_version_control
    set splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      start_version = #{startVersion,jdbcType=INTEGER},
      end_version = #{endVersion,jdbcType=INTEGER},
      platform_id = #{platformId,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>