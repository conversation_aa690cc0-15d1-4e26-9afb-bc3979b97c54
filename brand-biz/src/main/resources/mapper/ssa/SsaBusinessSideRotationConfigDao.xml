<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.SsaBusinessSideRotationConfigDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="source_config_id" jdbcType="INTEGER" property="sourceConfigId" />
    <result column="business_side_id" jdbcType="INTEGER" property="businessSideId" />
    <result column="con_rotation_limit" jdbcType="TINYINT" property="conRotationLimit" />
    <result column="bus_rotation_limit" jdbcType="TINYINT" property="busRotationLimit" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_account_id" jdbcType="INTEGER" property="crmAccountId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_config_id, business_side_id, con_rotation_limit, bus_rotation_limit, status, 
    is_deleted, ctime, mtime, crm_account_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ssa_business_side_rotation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ssa_business_side_rotation_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ssa_business_side_rotation_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPoExample">
    delete from ssa_business_side_rotation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_business_side_rotation_config (source_config_id, business_side_id, con_rotation_limit, 
      bus_rotation_limit, status, is_deleted, 
      ctime, mtime, crm_account_id
      )
    values (#{sourceConfigId,jdbcType=INTEGER}, #{businessSideId,jdbcType=INTEGER}, #{conRotationLimit,jdbcType=TINYINT}, 
      #{busRotationLimit,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{crmAccountId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_business_side_rotation_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceConfigId != null">
        source_config_id,
      </if>
      <if test="businessSideId != null">
        business_side_id,
      </if>
      <if test="conRotationLimit != null">
        con_rotation_limit,
      </if>
      <if test="busRotationLimit != null">
        bus_rotation_limit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="crmAccountId != null">
        crm_account_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sourceConfigId != null">
        #{sourceConfigId,jdbcType=INTEGER},
      </if>
      <if test="businessSideId != null">
        #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="conRotationLimit != null">
        #{conRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="busRotationLimit != null">
        #{busRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="crmAccountId != null">
        #{crmAccountId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPoExample" resultType="java.lang.Long">
    select count(*) from ssa_business_side_rotation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ssa_business_side_rotation_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sourceConfigId != null">
        source_config_id = #{record.sourceConfigId,jdbcType=INTEGER},
      </if>
      <if test="record.businessSideId != null">
        business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      </if>
      <if test="record.conRotationLimit != null">
        con_rotation_limit = #{record.conRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="record.busRotationLimit != null">
        bus_rotation_limit = #{record.busRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.crmAccountId != null">
        crm_account_id = #{record.crmAccountId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ssa_business_side_rotation_config
    set id = #{record.id,jdbcType=INTEGER},
      source_config_id = #{record.sourceConfigId,jdbcType=INTEGER},
      business_side_id = #{record.businessSideId,jdbcType=INTEGER},
      con_rotation_limit = #{record.conRotationLimit,jdbcType=TINYINT},
      bus_rotation_limit = #{record.busRotationLimit,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      crm_account_id = #{record.crmAccountId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    update ssa_business_side_rotation_config
    <set>
      <if test="sourceConfigId != null">
        source_config_id = #{sourceConfigId,jdbcType=INTEGER},
      </if>
      <if test="businessSideId != null">
        business_side_id = #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="conRotationLimit != null">
        con_rotation_limit = #{conRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="busRotationLimit != null">
        bus_rotation_limit = #{busRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="crmAccountId != null">
        crm_account_id = #{crmAccountId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    update ssa_business_side_rotation_config
    set source_config_id = #{sourceConfigId,jdbcType=INTEGER},
      business_side_id = #{businessSideId,jdbcType=INTEGER},
      con_rotation_limit = #{conRotationLimit,jdbcType=TINYINT},
      bus_rotation_limit = #{busRotationLimit,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      crm_account_id = #{crmAccountId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_business_side_rotation_config (source_config_id, business_side_id, con_rotation_limit, 
      bus_rotation_limit, status, is_deleted, 
      ctime, mtime, crm_account_id
      )
    values (#{sourceConfigId,jdbcType=INTEGER}, #{businessSideId,jdbcType=INTEGER}, #{conRotationLimit,jdbcType=TINYINT}, 
      #{busRotationLimit,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{crmAccountId,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      source_config_id = values(source_config_id),
      business_side_id = values(business_side_id),
      con_rotation_limit = values(con_rotation_limit),
      bus_rotation_limit = values(bus_rotation_limit),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      crm_account_id = values(crm_account_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ssa_business_side_rotation_config
      (source_config_id,business_side_id,con_rotation_limit,bus_rotation_limit,status,is_deleted,ctime,mtime,crm_account_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.sourceConfigId,jdbcType=INTEGER},
        #{item.businessSideId,jdbcType=INTEGER},
        #{item.conRotationLimit,jdbcType=TINYINT},
        #{item.busRotationLimit,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.crmAccountId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ssa_business_side_rotation_config
      (source_config_id,business_side_id,con_rotation_limit,bus_rotation_limit,status,is_deleted,ctime,mtime,crm_account_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.sourceConfigId,jdbcType=INTEGER},
        #{item.businessSideId,jdbcType=INTEGER},
        #{item.conRotationLimit,jdbcType=TINYINT},
        #{item.busRotationLimit,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.crmAccountId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      source_config_id = values(source_config_id),
      business_side_id = values(business_side_id),
      con_rotation_limit = values(con_rotation_limit),
      bus_rotation_limit = values(bus_rotation_limit),
      status = values(status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      crm_account_id = values(crm_account_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_business_side_rotation_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sourceConfigId != null">
        source_config_id,
      </if>
      <if test="businessSideId != null">
        business_side_id,
      </if>
      <if test="conRotationLimit != null">
        con_rotation_limit,
      </if>
      <if test="busRotationLimit != null">
        bus_rotation_limit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="crmAccountId != null">
        crm_account_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sourceConfigId != null">
        #{sourceConfigId,jdbcType=INTEGER},
      </if>
      <if test="businessSideId != null">
        #{businessSideId,jdbcType=INTEGER},
      </if>
      <if test="conRotationLimit != null">
        #{conRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="busRotationLimit != null">
        #{busRotationLimit,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="crmAccountId != null">
        #{crmAccountId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="sourceConfigId != null">
        source_config_id = values(source_config_id),
      </if>
      <if test="businessSideId != null">
        business_side_id = values(business_side_id),
      </if>
      <if test="conRotationLimit != null">
        con_rotation_limit = values(con_rotation_limit),
      </if>
      <if test="busRotationLimit != null">
        bus_rotation_limit = values(bus_rotation_limit),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="crmAccountId != null">
        crm_account_id = values(crm_account_id),
      </if>
    </trim>
  </insert>
</mapper>