<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.SsaImageRuleCopyDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="platform_id" jdbcType="TINYINT" property="platformId" />
    <result column="base_image_type" jdbcType="TINYINT" property="baseImageType" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="width" jdbcType="INTEGER" property="width" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="order_product" jdbcType="INTEGER" property="orderProduct" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, platform_id, base_image_type, rule_name, width, height, is_deleted, ctime, mtime, 
    order_product
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ssa_image_rule_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ssa_image_rule_copy
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ssa_image_rule_copy
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPoExample">
    delete from ssa_image_rule_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_image_rule_copy (platform_id, base_image_type, rule_name, 
      width, height, is_deleted, 
      ctime, mtime, order_product
      )
    values (#{platformId,jdbcType=TINYINT}, #{baseImageType,jdbcType=TINYINT}, #{ruleName,jdbcType=VARCHAR}, 
      #{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{orderProduct,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_image_rule_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="baseImageType != null">
        base_image_type,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="orderProduct != null">
        order_product,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        #{platformId,jdbcType=TINYINT},
      </if>
      <if test="baseImageType != null">
        #{baseImageType,jdbcType=TINYINT},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderProduct != null">
        #{orderProduct,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPoExample" resultType="java.lang.Long">
    select count(*) from ssa_image_rule_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ssa_image_rule_copy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=TINYINT},
      </if>
      <if test="record.baseImageType != null">
        base_image_type = #{record.baseImageType,jdbcType=TINYINT},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.width != null">
        width = #{record.width,jdbcType=INTEGER},
      </if>
      <if test="record.height != null">
        height = #{record.height,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderProduct != null">
        order_product = #{record.orderProduct,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ssa_image_rule_copy
    set id = #{record.id,jdbcType=INTEGER},
      platform_id = #{record.platformId,jdbcType=TINYINT},
      base_image_type = #{record.baseImageType,jdbcType=TINYINT},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      width = #{record.width,jdbcType=INTEGER},
      height = #{record.height,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      order_product = #{record.orderProduct,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    update ssa_image_rule_copy
    <set>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=TINYINT},
      </if>
      <if test="baseImageType != null">
        base_image_type = #{baseImageType,jdbcType=TINYINT},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        width = #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        height = #{height,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderProduct != null">
        order_product = #{orderProduct,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    update ssa_image_rule_copy
    set platform_id = #{platformId,jdbcType=TINYINT},
      base_image_type = #{baseImageType,jdbcType=TINYINT},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      width = #{width,jdbcType=INTEGER},
      height = #{height,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      order_product = #{orderProduct,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_image_rule_copy (platform_id, base_image_type, rule_name, 
      width, height, is_deleted, 
      ctime, mtime, order_product
      )
    values (#{platformId,jdbcType=TINYINT}, #{baseImageType,jdbcType=TINYINT}, #{ruleName,jdbcType=VARCHAR}, 
      #{width,jdbcType=INTEGER}, #{height,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{orderProduct,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      platform_id = values(platform_id),
      base_image_type = values(base_image_type),
      rule_name = values(rule_name),
      width = values(width),
      height = values(height),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      order_product = values(order_product),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ssa_image_rule_copy
      (platform_id,base_image_type,rule_name,width,height,is_deleted,ctime,mtime,order_product)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.platformId,jdbcType=TINYINT},
        #{item.baseImageType,jdbcType=TINYINT},
        #{item.ruleName,jdbcType=VARCHAR},
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.orderProduct,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ssa_image_rule_copy
      (platform_id,base_image_type,rule_name,width,height,is_deleted,ctime,mtime,order_product)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.platformId,jdbcType=TINYINT},
        #{item.baseImageType,jdbcType=TINYINT},
        #{item.ruleName,jdbcType=VARCHAR},
        #{item.width,jdbcType=INTEGER},
        #{item.height,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.orderProduct,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      platform_id = values(platform_id),
      base_image_type = values(base_image_type),
      rule_name = values(rule_name),
      width = values(width),
      height = values(height),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      order_product = values(order_product),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaImageRuleCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_image_rule_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="baseImageType != null">
        base_image_type,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="width != null">
        width,
      </if>
      <if test="height != null">
        height,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="orderProduct != null">
        order_product,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="platformId != null">
        #{platformId,jdbcType=TINYINT},
      </if>
      <if test="baseImageType != null">
        #{baseImageType,jdbcType=TINYINT},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="width != null">
        #{width,jdbcType=INTEGER},
      </if>
      <if test="height != null">
        #{height,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderProduct != null">
        #{orderProduct,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="platformId != null">
        platform_id = values(platform_id),
      </if>
      <if test="baseImageType != null">
        base_image_type = values(base_image_type),
      </if>
      <if test="ruleName != null">
        rule_name = values(rule_name),
      </if>
      <if test="width != null">
        width = values(width),
      </if>
      <if test="height != null">
        height = values(height),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="orderProduct != null">
        order_product = values(order_product),
      </if>
    </trim>
  </insert>
</mapper>