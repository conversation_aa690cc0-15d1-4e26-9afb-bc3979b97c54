<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.SsaSplashScreenJumpInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="splash_screen_id" jdbcType="INTEGER" property="splashScreenId" />
    <result column="platform_id" jdbcType="TINYINT" property="platformId" />
    <result column="jump_type" jdbcType="TINYINT" property="jumpType" />
    <result column="jump_link" jdbcType="VARCHAR" property="jumpLink" />
    <result column="is_call_app" jdbcType="TINYINT" property="isCallApp" />
    <result column="scheme_url" jdbcType="VARCHAR" property="schemeUrl" />
    <result column="universal_app" jdbcType="VARCHAR" property="universalApp" />
    <result column="scheme_copywriting" jdbcType="VARCHAR" property="schemeCopywriting" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="is_with_wake_up_bar" jdbcType="TINYINT" property="isWithWakeUpBar" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="interact_link" jdbcType="VARCHAR" property="interactLink" />
    <result column="interact_link_type" jdbcType="TINYINT" property="interactLinkType" />
    <result column="seq" jdbcType="TINYINT" property="seq" />
    <result column="button_id" jdbcType="INTEGER" property="buttonId" />
    <result column="scheme_copywriting_new" jdbcType="VARCHAR" property="schemeCopywritingNew" />
    <result column="live_type" jdbcType="TINYINT" property="liveType" />
    <result column="season_id" jdbcType="BIGINT" property="seasonId" />
    <result column="ep_id" jdbcType="BIGINT" property="epId" />
    <result column="mgk_video_id" jdbcType="BIGINT" property="mgkVideoId" />
    <result column="user_cancel_jump_link" jdbcType="VARCHAR" property="userCancelJumpLink" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, splash_screen_id, platform_id, jump_type, jump_link, is_call_app, scheme_url, 
    universal_app, scheme_copywriting, package_name, is_with_wake_up_bar, is_deleted, 
    ctime, mtime, interact_link, interact_link_type, seq, button_id, scheme_copywriting_new, 
    live_type, season_id, ep_id, mgk_video_id, user_cancel_jump_link
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ssa_splash_screen_jump_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ssa_splash_screen_jump_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ssa_splash_screen_jump_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPoExample">
    delete from ssa_splash_screen_jump_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_jump_info (splash_screen_id, platform_id, jump_type, 
      jump_link, is_call_app, scheme_url, 
      universal_app, scheme_copywriting, package_name, 
      is_with_wake_up_bar, is_deleted, ctime, 
      mtime, interact_link, interact_link_type, 
      seq, button_id, scheme_copywriting_new, 
      live_type, season_id, ep_id, 
      mgk_video_id, user_cancel_jump_link)
    values (#{splashScreenId,jdbcType=INTEGER}, #{platformId,jdbcType=TINYINT}, #{jumpType,jdbcType=TINYINT}, 
      #{jumpLink,jdbcType=VARCHAR}, #{isCallApp,jdbcType=TINYINT}, #{schemeUrl,jdbcType=VARCHAR}, 
      #{universalApp,jdbcType=VARCHAR}, #{schemeCopywriting,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, 
      #{isWithWakeUpBar,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{interactLink,jdbcType=VARCHAR}, #{interactLinkType,jdbcType=TINYINT}, 
      #{seq,jdbcType=TINYINT}, #{buttonId,jdbcType=INTEGER}, #{schemeCopywritingNew,jdbcType=VARCHAR}, 
      #{liveType,jdbcType=TINYINT}, #{seasonId,jdbcType=BIGINT}, #{epId,jdbcType=BIGINT}, 
      #{mgkVideoId,jdbcType=BIGINT}, #{userCancelJumpLink,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_jump_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        splash_screen_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="jumpLink != null">
        jump_link,
      </if>
      <if test="isCallApp != null">
        is_call_app,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="universalApp != null">
        universal_app,
      </if>
      <if test="schemeCopywriting != null">
        scheme_copywriting,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="isWithWakeUpBar != null">
        is_with_wake_up_bar,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="interactLink != null">
        interact_link,
      </if>
      <if test="interactLinkType != null">
        interact_link_type,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="buttonId != null">
        button_id,
      </if>
      <if test="schemeCopywritingNew != null">
        scheme_copywriting_new,
      </if>
      <if test="liveType != null">
        live_type,
      </if>
      <if test="seasonId != null">
        season_id,
      </if>
      <if test="epId != null">
        ep_id,
      </if>
      <if test="mgkVideoId != null">
        mgk_video_id,
      </if>
      <if test="userCancelJumpLink != null">
        user_cancel_jump_link,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=TINYINT},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="jumpLink != null">
        #{jumpLink,jdbcType=VARCHAR},
      </if>
      <if test="isCallApp != null">
        #{isCallApp,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="universalApp != null">
        #{universalApp,jdbcType=VARCHAR},
      </if>
      <if test="schemeCopywriting != null">
        #{schemeCopywriting,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="isWithWakeUpBar != null">
        #{isWithWakeUpBar,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="interactLink != null">
        #{interactLink,jdbcType=VARCHAR},
      </if>
      <if test="interactLinkType != null">
        #{interactLinkType,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="buttonId != null">
        #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="schemeCopywritingNew != null">
        #{schemeCopywritingNew,jdbcType=VARCHAR},
      </if>
      <if test="liveType != null">
        #{liveType,jdbcType=TINYINT},
      </if>
      <if test="seasonId != null">
        #{seasonId,jdbcType=BIGINT},
      </if>
      <if test="epId != null">
        #{epId,jdbcType=BIGINT},
      </if>
      <if test="mgkVideoId != null">
        #{mgkVideoId,jdbcType=BIGINT},
      </if>
      <if test="userCancelJumpLink != null">
        #{userCancelJumpLink,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPoExample" resultType="java.lang.Long">
    select count(*) from ssa_splash_screen_jump_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ssa_splash_screen_jump_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.splashScreenId != null">
        splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=TINYINT},
      </if>
      <if test="record.jumpType != null">
        jump_type = #{record.jumpType,jdbcType=TINYINT},
      </if>
      <if test="record.jumpLink != null">
        jump_link = #{record.jumpLink,jdbcType=VARCHAR},
      </if>
      <if test="record.isCallApp != null">
        is_call_app = #{record.isCallApp,jdbcType=TINYINT},
      </if>
      <if test="record.schemeUrl != null">
        scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.universalApp != null">
        universal_app = #{record.universalApp,jdbcType=VARCHAR},
      </if>
      <if test="record.schemeCopywriting != null">
        scheme_copywriting = #{record.schemeCopywriting,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.isWithWakeUpBar != null">
        is_with_wake_up_bar = #{record.isWithWakeUpBar,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.interactLink != null">
        interact_link = #{record.interactLink,jdbcType=VARCHAR},
      </if>
      <if test="record.interactLinkType != null">
        interact_link_type = #{record.interactLinkType,jdbcType=TINYINT},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=TINYINT},
      </if>
      <if test="record.buttonId != null">
        button_id = #{record.buttonId,jdbcType=INTEGER},
      </if>
      <if test="record.schemeCopywritingNew != null">
        scheme_copywriting_new = #{record.schemeCopywritingNew,jdbcType=VARCHAR},
      </if>
      <if test="record.liveType != null">
        live_type = #{record.liveType,jdbcType=TINYINT},
      </if>
      <if test="record.seasonId != null">
        season_id = #{record.seasonId,jdbcType=BIGINT},
      </if>
      <if test="record.epId != null">
        ep_id = #{record.epId,jdbcType=BIGINT},
      </if>
      <if test="record.mgkVideoId != null">
        mgk_video_id = #{record.mgkVideoId,jdbcType=BIGINT},
      </if>
      <if test="record.userCancelJumpLink != null">
        user_cancel_jump_link = #{record.userCancelJumpLink,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ssa_splash_screen_jump_info
    set id = #{record.id,jdbcType=INTEGER},
      splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      platform_id = #{record.platformId,jdbcType=TINYINT},
      jump_type = #{record.jumpType,jdbcType=TINYINT},
      jump_link = #{record.jumpLink,jdbcType=VARCHAR},
      is_call_app = #{record.isCallApp,jdbcType=TINYINT},
      scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      universal_app = #{record.universalApp,jdbcType=VARCHAR},
      scheme_copywriting = #{record.schemeCopywriting,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      is_with_wake_up_bar = #{record.isWithWakeUpBar,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      interact_link = #{record.interactLink,jdbcType=VARCHAR},
      interact_link_type = #{record.interactLinkType,jdbcType=TINYINT},
      seq = #{record.seq,jdbcType=TINYINT},
      button_id = #{record.buttonId,jdbcType=INTEGER},
      scheme_copywriting_new = #{record.schemeCopywritingNew,jdbcType=VARCHAR},
      live_type = #{record.liveType,jdbcType=TINYINT},
      season_id = #{record.seasonId,jdbcType=BIGINT},
      ep_id = #{record.epId,jdbcType=BIGINT},
      mgk_video_id = #{record.mgkVideoId,jdbcType=BIGINT},
      user_cancel_jump_link = #{record.userCancelJumpLink,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    update ssa_splash_screen_jump_info
    <set>
      <if test="splashScreenId != null">
        splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=TINYINT},
      </if>
      <if test="jumpType != null">
        jump_type = #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="jumpLink != null">
        jump_link = #{jumpLink,jdbcType=VARCHAR},
      </if>
      <if test="isCallApp != null">
        is_call_app = #{isCallApp,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="universalApp != null">
        universal_app = #{universalApp,jdbcType=VARCHAR},
      </if>
      <if test="schemeCopywriting != null">
        scheme_copywriting = #{schemeCopywriting,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="isWithWakeUpBar != null">
        is_with_wake_up_bar = #{isWithWakeUpBar,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="interactLink != null">
        interact_link = #{interactLink,jdbcType=VARCHAR},
      </if>
      <if test="interactLinkType != null">
        interact_link_type = #{interactLinkType,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=TINYINT},
      </if>
      <if test="buttonId != null">
        button_id = #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="schemeCopywritingNew != null">
        scheme_copywriting_new = #{schemeCopywritingNew,jdbcType=VARCHAR},
      </if>
      <if test="liveType != null">
        live_type = #{liveType,jdbcType=TINYINT},
      </if>
      <if test="seasonId != null">
        season_id = #{seasonId,jdbcType=BIGINT},
      </if>
      <if test="epId != null">
        ep_id = #{epId,jdbcType=BIGINT},
      </if>
      <if test="mgkVideoId != null">
        mgk_video_id = #{mgkVideoId,jdbcType=BIGINT},
      </if>
      <if test="userCancelJumpLink != null">
        user_cancel_jump_link = #{userCancelJumpLink,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    update ssa_splash_screen_jump_info
    set splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      platform_id = #{platformId,jdbcType=TINYINT},
      jump_type = #{jumpType,jdbcType=TINYINT},
      jump_link = #{jumpLink,jdbcType=VARCHAR},
      is_call_app = #{isCallApp,jdbcType=TINYINT},
      scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      universal_app = #{universalApp,jdbcType=VARCHAR},
      scheme_copywriting = #{schemeCopywriting,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      is_with_wake_up_bar = #{isWithWakeUpBar,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      interact_link = #{interactLink,jdbcType=VARCHAR},
      interact_link_type = #{interactLinkType,jdbcType=TINYINT},
      seq = #{seq,jdbcType=TINYINT},
      button_id = #{buttonId,jdbcType=INTEGER},
      scheme_copywriting_new = #{schemeCopywritingNew,jdbcType=VARCHAR},
      live_type = #{liveType,jdbcType=TINYINT},
      season_id = #{seasonId,jdbcType=BIGINT},
      ep_id = #{epId,jdbcType=BIGINT},
      mgk_video_id = #{mgkVideoId,jdbcType=BIGINT},
      user_cancel_jump_link = #{userCancelJumpLink,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_jump_info (splash_screen_id, platform_id, jump_type, 
      jump_link, is_call_app, scheme_url, 
      universal_app, scheme_copywriting, package_name, 
      is_with_wake_up_bar, is_deleted, ctime, 
      mtime, interact_link, interact_link_type, 
      seq, button_id, scheme_copywriting_new, 
      live_type, season_id, ep_id, 
      mgk_video_id, user_cancel_jump_link)
    values (#{splashScreenId,jdbcType=INTEGER}, #{platformId,jdbcType=TINYINT}, #{jumpType,jdbcType=TINYINT}, 
      #{jumpLink,jdbcType=VARCHAR}, #{isCallApp,jdbcType=TINYINT}, #{schemeUrl,jdbcType=VARCHAR}, 
      #{universalApp,jdbcType=VARCHAR}, #{schemeCopywriting,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, 
      #{isWithWakeUpBar,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{interactLink,jdbcType=VARCHAR}, #{interactLinkType,jdbcType=TINYINT}, 
      #{seq,jdbcType=TINYINT}, #{buttonId,jdbcType=INTEGER}, #{schemeCopywritingNew,jdbcType=VARCHAR}, 
      #{liveType,jdbcType=TINYINT}, #{seasonId,jdbcType=BIGINT}, #{epId,jdbcType=BIGINT}, 
      #{mgkVideoId,jdbcType=BIGINT}, #{userCancelJumpLink,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      splash_screen_id = values(splash_screen_id),
      platform_id = values(platform_id),
      jump_type = values(jump_type),
      jump_link = values(jump_link),
      is_call_app = values(is_call_app),
      scheme_url = values(scheme_url),
      universal_app = values(universal_app),
      scheme_copywriting = values(scheme_copywriting),
      package_name = values(package_name),
      is_with_wake_up_bar = values(is_with_wake_up_bar),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      interact_link = values(interact_link),
      interact_link_type = values(interact_link_type),
      seq = values(seq),
      button_id = values(button_id),
      scheme_copywriting_new = values(scheme_copywriting_new),
      live_type = values(live_type),
      season_id = values(season_id),
      ep_id = values(ep_id),
      mgk_video_id = values(mgk_video_id),
      user_cancel_jump_link = values(user_cancel_jump_link),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ssa_splash_screen_jump_info
      (splash_screen_id,platform_id,jump_type,jump_link,is_call_app,scheme_url,universal_app,scheme_copywriting,package_name,is_with_wake_up_bar,is_deleted,ctime,mtime,interact_link,interact_link_type,seq,button_id,scheme_copywriting_new,live_type,season_id,ep_id,mgk_video_id,user_cancel_jump_link)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.splashScreenId,jdbcType=INTEGER},
        #{item.platformId,jdbcType=TINYINT},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.jumpLink,jdbcType=VARCHAR},
        #{item.isCallApp,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.universalApp,jdbcType=VARCHAR},
        #{item.schemeCopywriting,jdbcType=VARCHAR},
        #{item.packageName,jdbcType=VARCHAR},
        #{item.isWithWakeUpBar,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.interactLink,jdbcType=VARCHAR},
        #{item.interactLinkType,jdbcType=TINYINT},
        #{item.seq,jdbcType=TINYINT},
        #{item.buttonId,jdbcType=INTEGER},
        #{item.schemeCopywritingNew,jdbcType=VARCHAR},
        #{item.liveType,jdbcType=TINYINT},
        #{item.seasonId,jdbcType=BIGINT},
        #{item.epId,jdbcType=BIGINT},
        #{item.mgkVideoId,jdbcType=BIGINT},
        #{item.userCancelJumpLink,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ssa_splash_screen_jump_info
      (splash_screen_id,platform_id,jump_type,jump_link,is_call_app,scheme_url,universal_app,scheme_copywriting,package_name,is_with_wake_up_bar,is_deleted,ctime,mtime,interact_link,interact_link_type,seq,button_id,scheme_copywriting_new,live_type,season_id,ep_id,mgk_video_id,user_cancel_jump_link)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.splashScreenId,jdbcType=INTEGER},
        #{item.platformId,jdbcType=TINYINT},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.jumpLink,jdbcType=VARCHAR},
        #{item.isCallApp,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.universalApp,jdbcType=VARCHAR},
        #{item.schemeCopywriting,jdbcType=VARCHAR},
        #{item.packageName,jdbcType=VARCHAR},
        #{item.isWithWakeUpBar,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.interactLink,jdbcType=VARCHAR},
        #{item.interactLinkType,jdbcType=TINYINT},
        #{item.seq,jdbcType=TINYINT},
        #{item.buttonId,jdbcType=INTEGER},
        #{item.schemeCopywritingNew,jdbcType=VARCHAR},
        #{item.liveType,jdbcType=TINYINT},
        #{item.seasonId,jdbcType=BIGINT},
        #{item.epId,jdbcType=BIGINT},
        #{item.mgkVideoId,jdbcType=BIGINT},
        #{item.userCancelJumpLink,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      splash_screen_id = values(splash_screen_id),
      platform_id = values(platform_id),
      jump_type = values(jump_type),
      jump_link = values(jump_link),
      is_call_app = values(is_call_app),
      scheme_url = values(scheme_url),
      universal_app = values(universal_app),
      scheme_copywriting = values(scheme_copywriting),
      package_name = values(package_name),
      is_with_wake_up_bar = values(is_with_wake_up_bar),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      interact_link = values(interact_link),
      interact_link_type = values(interact_link_type),
      seq = values(seq),
      button_id = values(button_id),
      scheme_copywriting_new = values(scheme_copywriting_new),
      live_type = values(live_type),
      season_id = values(season_id),
      ep_id = values(ep_id),
      mgk_video_id = values(mgk_video_id),
      user_cancel_jump_link = values(user_cancel_jump_link),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ssa_splash_screen_jump_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        splash_screen_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="jumpLink != null">
        jump_link,
      </if>
      <if test="isCallApp != null">
        is_call_app,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="universalApp != null">
        universal_app,
      </if>
      <if test="schemeCopywriting != null">
        scheme_copywriting,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="isWithWakeUpBar != null">
        is_with_wake_up_bar,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="interactLink != null">
        interact_link,
      </if>
      <if test="interactLinkType != null">
        interact_link_type,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="buttonId != null">
        button_id,
      </if>
      <if test="schemeCopywritingNew != null">
        scheme_copywriting_new,
      </if>
      <if test="liveType != null">
        live_type,
      </if>
      <if test="seasonId != null">
        season_id,
      </if>
      <if test="epId != null">
        ep_id,
      </if>
      <if test="mgkVideoId != null">
        mgk_video_id,
      </if>
      <if test="userCancelJumpLink != null">
        user_cancel_jump_link,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="splashScreenId != null">
        #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=TINYINT},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="jumpLink != null">
        #{jumpLink,jdbcType=VARCHAR},
      </if>
      <if test="isCallApp != null">
        #{isCallApp,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="universalApp != null">
        #{universalApp,jdbcType=VARCHAR},
      </if>
      <if test="schemeCopywriting != null">
        #{schemeCopywriting,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="isWithWakeUpBar != null">
        #{isWithWakeUpBar,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="interactLink != null">
        #{interactLink,jdbcType=VARCHAR},
      </if>
      <if test="interactLinkType != null">
        #{interactLinkType,jdbcType=TINYINT},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=TINYINT},
      </if>
      <if test="buttonId != null">
        #{buttonId,jdbcType=INTEGER},
      </if>
      <if test="schemeCopywritingNew != null">
        #{schemeCopywritingNew,jdbcType=VARCHAR},
      </if>
      <if test="liveType != null">
        #{liveType,jdbcType=TINYINT},
      </if>
      <if test="seasonId != null">
        #{seasonId,jdbcType=BIGINT},
      </if>
      <if test="epId != null">
        #{epId,jdbcType=BIGINT},
      </if>
      <if test="mgkVideoId != null">
        #{mgkVideoId,jdbcType=BIGINT},
      </if>
      <if test="userCancelJumpLink != null">
        #{userCancelJumpLink,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="splashScreenId != null">
        splash_screen_id = values(splash_screen_id),
      </if>
      <if test="platformId != null">
        platform_id = values(platform_id),
      </if>
      <if test="jumpType != null">
        jump_type = values(jump_type),
      </if>
      <if test="jumpLink != null">
        jump_link = values(jump_link),
      </if>
      <if test="isCallApp != null">
        is_call_app = values(is_call_app),
      </if>
      <if test="schemeUrl != null">
        scheme_url = values(scheme_url),
      </if>
      <if test="universalApp != null">
        universal_app = values(universal_app),
      </if>
      <if test="schemeCopywriting != null">
        scheme_copywriting = values(scheme_copywriting),
      </if>
      <if test="packageName != null">
        package_name = values(package_name),
      </if>
      <if test="isWithWakeUpBar != null">
        is_with_wake_up_bar = values(is_with_wake_up_bar),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="interactLink != null">
        interact_link = values(interact_link),
      </if>
      <if test="interactLinkType != null">
        interact_link_type = values(interact_link_type),
      </if>
      <if test="seq != null">
        seq = values(seq),
      </if>
      <if test="buttonId != null">
        button_id = values(button_id),
      </if>
      <if test="schemeCopywritingNew != null">
        scheme_copywriting_new = values(scheme_copywriting_new),
      </if>
      <if test="liveType != null">
        live_type = values(live_type),
      </if>
      <if test="seasonId != null">
        season_id = values(season_id),
      </if>
      <if test="epId != null">
        ep_id = values(ep_id),
      </if>
      <if test="mgkVideoId != null">
        mgk_video_id = values(mgk_video_id),
      </if>
      <if test="userCancelJumpLink != null">
        user_cancel_jump_link = values(user_cancel_jump_link),
      </if>
    </trim>
  </insert>
</mapper>