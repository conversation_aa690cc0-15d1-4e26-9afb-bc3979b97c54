<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.ssa.platform.biz.dao.SsaSplashScreenVideoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="splash_screen_id" jdbcType="INTEGER" property="splashScreenId" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="upos_url" jdbcType="VARCHAR" property="uposUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="xcode_upos_url" jdbcType="VARCHAR" property="xcodeUposUrl" />
    <result column="xcode_md5" jdbcType="VARCHAR" property="xcodeMd5" />
    <result column="xcode_width" jdbcType="INTEGER" property="xcodeWidth" />
    <result column="xcode_height" jdbcType="INTEGER" property="xcodeHeight" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="upos_auth" jdbcType="VARCHAR" property="uposAuth" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="xcode_duration" jdbcType="INTEGER" property="xcodeDuration" />
    <result column="video_type" jdbcType="TINYINT" property="videoType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, splash_screen_id, biz_id, upos_url, file_name, xcode_upos_url, xcode_md5, xcode_width, 
    xcode_height, status, upos_auth, is_deleted, ctime, mtime, xcode_duration, video_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ssa_splash_screen_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ssa_splash_screen_video
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ssa_splash_screen_video
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPoExample">
    delete from ssa_splash_screen_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    insert into ssa_splash_screen_video (id, splash_screen_id, biz_id, 
      upos_url, file_name, xcode_upos_url, 
      xcode_md5, xcode_width, xcode_height, 
      status, upos_auth, is_deleted, 
      ctime, mtime, xcode_duration, 
      video_type)
    values (#{id,jdbcType=INTEGER}, #{splashScreenId,jdbcType=INTEGER}, #{bizId,jdbcType=INTEGER}, 
      #{uposUrl,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{xcodeUposUrl,jdbcType=VARCHAR}, 
      #{xcodeMd5,jdbcType=VARCHAR}, #{xcodeWidth,jdbcType=INTEGER}, #{xcodeHeight,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{uposAuth,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{xcodeDuration,jdbcType=INTEGER}, 
      #{videoType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    insert into ssa_splash_screen_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="splashScreenId != null">
        splash_screen_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="uposUrl != null">
        upos_url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="xcodeUposUrl != null">
        xcode_upos_url,
      </if>
      <if test="xcodeMd5 != null">
        xcode_md5,
      </if>
      <if test="xcodeWidth != null">
        xcode_width,
      </if>
      <if test="xcodeHeight != null">
        xcode_height,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="uposAuth != null">
        upos_auth,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="xcodeDuration != null">
        xcode_duration,
      </if>
      <if test="videoType != null">
        video_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="splashScreenId != null">
        #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="uposUrl != null">
        #{uposUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="xcodeUposUrl != null">
        #{xcodeUposUrl,jdbcType=VARCHAR},
      </if>
      <if test="xcodeMd5 != null">
        #{xcodeMd5,jdbcType=VARCHAR},
      </if>
      <if test="xcodeWidth != null">
        #{xcodeWidth,jdbcType=INTEGER},
      </if>
      <if test="xcodeHeight != null">
        #{xcodeHeight,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="uposAuth != null">
        #{uposAuth,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="xcodeDuration != null">
        #{xcodeDuration,jdbcType=INTEGER},
      </if>
      <if test="videoType != null">
        #{videoType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPoExample" resultType="java.lang.Long">
    select count(*) from ssa_splash_screen_video
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ssa_splash_screen_video
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.splashScreenId != null">
        splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="record.bizId != null">
        biz_id = #{record.bizId,jdbcType=INTEGER},
      </if>
      <if test="record.uposUrl != null">
        upos_url = #{record.uposUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.xcodeUposUrl != null">
        xcode_upos_url = #{record.xcodeUposUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.xcodeMd5 != null">
        xcode_md5 = #{record.xcodeMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.xcodeWidth != null">
        xcode_width = #{record.xcodeWidth,jdbcType=INTEGER},
      </if>
      <if test="record.xcodeHeight != null">
        xcode_height = #{record.xcodeHeight,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.uposAuth != null">
        upos_auth = #{record.uposAuth,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.xcodeDuration != null">
        xcode_duration = #{record.xcodeDuration,jdbcType=INTEGER},
      </if>
      <if test="record.videoType != null">
        video_type = #{record.videoType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ssa_splash_screen_video
    set id = #{record.id,jdbcType=INTEGER},
      splash_screen_id = #{record.splashScreenId,jdbcType=INTEGER},
      biz_id = #{record.bizId,jdbcType=INTEGER},
      upos_url = #{record.uposUrl,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      xcode_upos_url = #{record.xcodeUposUrl,jdbcType=VARCHAR},
      xcode_md5 = #{record.xcodeMd5,jdbcType=VARCHAR},
      xcode_width = #{record.xcodeWidth,jdbcType=INTEGER},
      xcode_height = #{record.xcodeHeight,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      upos_auth = #{record.uposAuth,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      xcode_duration = #{record.xcodeDuration,jdbcType=INTEGER},
      video_type = #{record.videoType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    update ssa_splash_screen_video
    <set>
      <if test="splashScreenId != null">
        splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="uposUrl != null">
        upos_url = #{uposUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="xcodeUposUrl != null">
        xcode_upos_url = #{xcodeUposUrl,jdbcType=VARCHAR},
      </if>
      <if test="xcodeMd5 != null">
        xcode_md5 = #{xcodeMd5,jdbcType=VARCHAR},
      </if>
      <if test="xcodeWidth != null">
        xcode_width = #{xcodeWidth,jdbcType=INTEGER},
      </if>
      <if test="xcodeHeight != null">
        xcode_height = #{xcodeHeight,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="uposAuth != null">
        upos_auth = #{uposAuth,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="xcodeDuration != null">
        xcode_duration = #{xcodeDuration,jdbcType=INTEGER},
      </if>
      <if test="videoType != null">
        video_type = #{videoType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    update ssa_splash_screen_video
    set splash_screen_id = #{splashScreenId,jdbcType=INTEGER},
      biz_id = #{bizId,jdbcType=INTEGER},
      upos_url = #{uposUrl,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      xcode_upos_url = #{xcodeUposUrl,jdbcType=VARCHAR},
      xcode_md5 = #{xcodeMd5,jdbcType=VARCHAR},
      xcode_width = #{xcodeWidth,jdbcType=INTEGER},
      xcode_height = #{xcodeHeight,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      upos_auth = #{uposAuth,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      xcode_duration = #{xcodeDuration,jdbcType=INTEGER},
      video_type = #{videoType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    insert into ssa_splash_screen_video (id, splash_screen_id, biz_id, 
      upos_url, file_name, xcode_upos_url, 
      xcode_md5, xcode_width, xcode_height, 
      status, upos_auth, is_deleted, 
      ctime, mtime, xcode_duration, 
      video_type)
    values (#{id,jdbcType=INTEGER}, #{splashScreenId,jdbcType=INTEGER}, #{bizId,jdbcType=INTEGER}, 
      #{uposUrl,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{xcodeUposUrl,jdbcType=VARCHAR}, 
      #{xcodeMd5,jdbcType=VARCHAR}, #{xcodeWidth,jdbcType=INTEGER}, #{xcodeHeight,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{uposAuth,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{xcodeDuration,jdbcType=INTEGER}, 
      #{videoType,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      splash_screen_id = values(splash_screen_id),
      biz_id = values(biz_id),
      upos_url = values(upos_url),
      file_name = values(file_name),
      xcode_upos_url = values(xcode_upos_url),
      xcode_md5 = values(xcode_md5),
      xcode_width = values(xcode_width),
      xcode_height = values(xcode_height),
      status = values(status),
      upos_auth = values(upos_auth),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      xcode_duration = values(xcode_duration),
      video_type = values(video_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ssa_splash_screen_video
      (splash_screen_id,biz_id,upos_url,file_name,xcode_upos_url,xcode_md5,xcode_width,xcode_height,status,upos_auth,is_deleted,ctime,mtime,xcode_duration,video_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.splashScreenId,jdbcType=INTEGER},
        #{item.bizId,jdbcType=INTEGER},
        #{item.uposUrl,jdbcType=VARCHAR},
        #{item.fileName,jdbcType=VARCHAR},
        #{item.xcodeUposUrl,jdbcType=VARCHAR},
        #{item.xcodeMd5,jdbcType=VARCHAR},
        #{item.xcodeWidth,jdbcType=INTEGER},
        #{item.xcodeHeight,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.uposAuth,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.xcodeDuration,jdbcType=INTEGER},
        #{item.videoType,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ssa_splash_screen_video
      (splash_screen_id,biz_id,upos_url,file_name,xcode_upos_url,xcode_md5,xcode_width,xcode_height,status,upos_auth,is_deleted,ctime,mtime,xcode_duration,video_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.splashScreenId,jdbcType=INTEGER},
        #{item.bizId,jdbcType=INTEGER},
        #{item.uposUrl,jdbcType=VARCHAR},
        #{item.fileName,jdbcType=VARCHAR},
        #{item.xcodeUposUrl,jdbcType=VARCHAR},
        #{item.xcodeMd5,jdbcType=VARCHAR},
        #{item.xcodeWidth,jdbcType=INTEGER},
        #{item.xcodeHeight,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.uposAuth,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.xcodeDuration,jdbcType=INTEGER},
        #{item.videoType,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      splash_screen_id = values(splash_screen_id),
      biz_id = values(biz_id),
      upos_url = values(upos_url),
      file_name = values(file_name),
      xcode_upos_url = values(xcode_upos_url),
      xcode_md5 = values(xcode_md5),
      xcode_width = values(xcode_width),
      xcode_height = values(xcode_height),
      status = values(status),
      upos_auth = values(upos_auth),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      xcode_duration = values(xcode_duration),
      video_type = values(video_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.ssa.platform.biz.po.SsaSplashScreenVideoPo">
    insert into ssa_splash_screen_video
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="splashScreenId != null">
        splash_screen_id,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="uposUrl != null">
        upos_url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="xcodeUposUrl != null">
        xcode_upos_url,
      </if>
      <if test="xcodeMd5 != null">
        xcode_md5,
      </if>
      <if test="xcodeWidth != null">
        xcode_width,
      </if>
      <if test="xcodeHeight != null">
        xcode_height,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="uposAuth != null">
        upos_auth,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="xcodeDuration != null">
        xcode_duration,
      </if>
      <if test="videoType != null">
        video_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="splashScreenId != null">
        #{splashScreenId,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="uposUrl != null">
        #{uposUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="xcodeUposUrl != null">
        #{xcodeUposUrl,jdbcType=VARCHAR},
      </if>
      <if test="xcodeMd5 != null">
        #{xcodeMd5,jdbcType=VARCHAR},
      </if>
      <if test="xcodeWidth != null">
        #{xcodeWidth,jdbcType=INTEGER},
      </if>
      <if test="xcodeHeight != null">
        #{xcodeHeight,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="uposAuth != null">
        #{uposAuth,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="xcodeDuration != null">
        #{xcodeDuration,jdbcType=INTEGER},
      </if>
      <if test="videoType != null">
        #{videoType,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="splashScreenId != null">
        splash_screen_id = values(splash_screen_id),
      </if>
      <if test="bizId != null">
        biz_id = values(biz_id),
      </if>
      <if test="uposUrl != null">
        upos_url = values(upos_url),
      </if>
      <if test="fileName != null">
        file_name = values(file_name),
      </if>
      <if test="xcodeUposUrl != null">
        xcode_upos_url = values(xcode_upos_url),
      </if>
      <if test="xcodeMd5 != null">
        xcode_md5 = values(xcode_md5),
      </if>
      <if test="xcodeWidth != null">
        xcode_width = values(xcode_width),
      </if>
      <if test="xcodeHeight != null">
        xcode_height = values(xcode_height),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="uposAuth != null">
        upos_auth = values(upos_auth),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="xcodeDuration != null">
        xcode_duration = values(xcode_duration),
      </if>
      <if test="videoType != null">
        video_type = values(video_type),
      </if>
    </trim>
  </insert>
</mapper>