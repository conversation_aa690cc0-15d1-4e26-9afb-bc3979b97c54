<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.resource.dao.TargetItemDao">

    <resultMap id="TargetItemResult" type="com.bilibili.brand.biz.resource.po.TargetItemPo">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="subType" column="sub_type"/>
        <result property="name" column="name"/>
        <result property="parentId" column="parent_id"/>
        <result property="mappingContent" column="mapping_content"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="addTime" column="ctime"/>
        <result property="updateTime" column="mtime"/>
    </resultMap>

    <sql id="tbl_name">
        res_target_item
    </sql>

    <sql id="select_sql">
        select id, type, sub_type, name, parent_id, mapping_content, sort_order, status, is_deleted, ctime, mtime from
        <include refid="tbl_name"/>
    </sql>

    <sql id="where_sql">
        <where>
            <if test="query.idList != null">
                <if test="query.idList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="query.idList.size() == 1">
                    AND id =
                    <foreach collection="query.idList" index="" open="" close="" separator="" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.idList.size() > 1">
                    AND id IN
                    <foreach collection="query.idList" index="" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.typeList != null">
                <if test="query.typeList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="query.typeList.size() == 1">
                    AND type =
                    <foreach collection="query.typeList" index="" open="" close="" separator="" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.typeList.size() > 1">
                    AND type IN
                    <foreach collection="query.typeList" index="" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.subTypeList != null">
                <if test="query.subTypeList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="query.subTypeList.size() == 1">
                    AND sub_type =
                    <foreach collection="query.subTypeList" index="" open="" close="" separator="" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.subTypeList.size() > 1">
                    AND sub_type IN
                    <foreach collection="query.subTypeList" index="" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.parentIdList != null">
                <if test="query.parentIdList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="query.parentIdList.size() == 1">
                    AND parent_id =
                    <foreach collection="query.parentIdList" index="" open="" close="" separator="" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.parentIdList.size() > 1">
                    AND parent_id IN
                    <foreach collection="query.parentIdList" index="" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </if>

            <if test="query.statusList != null">
                <if test="query.statusList.size() == 0">
                    AND 1 = 0
                </if>
                <if test="query.statusList.size() == 1">
                    AND status =
                    <foreach collection="query.statusList" index="" open="" close="" separator="" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.statusList.size() > 1">
                    AND status IN
                    <foreach collection="query.statusList" index="" open="(" close=")" separator="," item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.isDeleted != null">
                AND is_deleted = #{query.isDeleted}
            </if>
        </where>
    </sql>

    <insert id="insert" parameterType="com.bilibili.brand.biz.resource.po.TargetItemPo" useGeneratedKeys="true"
            keyProperty="entity.id">

        insert into
        <include refid="tbl_name"/>
        (
        `type`,
        `name`,
        sub_type,
        parent_id,
        mapping_content,
        sort_order,
        status
        )
        values
        (
        #{entity.type},
        #{entity.name},
        #{entity.subType},
        #{entity.parentId},
        #{entity.mappingContent},
        #{entity.sortOrder},
        #{entity.status}
        )

    </insert>

    <select id="load" parameterType="map" resultMap="TargetItemResult">

        <include refid="select_sql"/>
        where id = #{id}
        <if test="deleted != null">
            AND is_deleted = #{deleted}
        </if>
    </select>

    <select id="queryByType" parameterType="map" resultMap="TargetItemResult">

        <include refid="select_sql"/>
        where type = #{type} AND is_deleted=0 order by sort_order asc

    </select>
    
    <select id="getTargetItemsByIds" parameterType="map" resultMap="TargetItemResult">

        <include refid="select_sql"/>
        where is_deleted=0 
        <if test="areaIds!=null and areaIds.size() > 0">
              AND id IN
              <foreach collection="areaIds" index="" open="(" close=")" separator="," item="id">
                  #{id}
              </foreach>
        </if>
        order by sort_order asc

    </select>
    
    <select id="query" parameterType="map" resultMap="TargetItemResult">
        <include refid="select_sql"/>
        <include refid="where_sql"/>
    </select>

    <update id="update">
        UPDATE
        <include refid="tbl_name"/>
        SET
        <trim suffixOverrides=",">
            <if test="name!=null">
                `name`=#{name},
            </if>
            <if test="parentId!=null">
                `parent_id`=#{parentId},
            </if>
            <if test="subType!=null">
                `sub_type`=#{subType},
            </if>
            <if test="mappingContent!=null">
                `mapping_content`=#{mappingContent},
            </if>
            <if test="sortOrder!=null">
                `sort_order`=#{sortOrder},
            </if>
            <if test="status!=null">
                status=#{status}
            </if>
        </trim>
        WHERE
        `id`=#{id}
    </update>
</mapper>
