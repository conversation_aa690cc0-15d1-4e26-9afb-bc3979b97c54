<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.account.dao.CrmContractDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.account.po.CrmContractPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contract_number" jdbcType="BIGINT" property="contractNumber" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="legal_contract_id" jdbcType="VARCHAR" property="legalContractId" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="archive_status" jdbcType="TINYINT" property="archiveStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="amount" jdbcType="BIGINT" property="amount" />
    <result column="amount_remark" jdbcType="VARCHAR" property="amountRemark" />
    <result column="review_remark" jdbcType="VARCHAR" property="reviewRemark" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="finance_title_agent_id" jdbcType="INTEGER" property="financeTitleAgentId" />
    <result column="distribution" jdbcType="INTEGER" property="distribution" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="quota_id" jdbcType="INTEGER" property="quotaId" />
    <result column="deduct_quota" jdbcType="BIGINT" property="deductQuota" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="order_begin_time" jdbcType="TIMESTAMP" property="orderBeginTime" />
    <result column="order_end_time" jdbcType="TIMESTAMP" property="orderEndTime" />
    <result column="promotion_policy" jdbcType="TINYINT" property="promotionPolicy" />
    <result column="group_id" jdbcType="INTEGER" property="groupId" />
    <result column="product_line_id" jdbcType="INTEGER" property="productLineId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="execute_id" jdbcType="INTEGER" property="executeId" />
    <result column="bus_status" jdbcType="TINYINT" property="busStatus" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="old_agent_id" jdbcType="INTEGER" property="oldAgentId" />
    <result column="bill_amount" jdbcType="BIGINT" property="billAmount" />
    <result column="total_claim_amount" jdbcType="BIGINT" property="totalClaimAmount" />
    <result column="init_deducted_amount" jdbcType="BIGINT" property="initDeductedAmount" />
    <result column="is_oa_open_bill" jdbcType="TINYINT" property="isOaOpenBill" />
    <result column="is_deduct_completed" jdbcType="TINYINT" property="isDeductCompleted" />
    <result column="offline_bill_amount" jdbcType="BIGINT" property="offlineBillAmount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contract_number, account_id, name, type, agent_id, legal_contract_id, begin_time, 
    end_time, archive_status, status, remark, amount, amount_remark, review_remark, version, 
    ctime, mtime, is_deleted, creator, finance_title_agent_id, distribution, discount, 
    quota_id, deduct_quota, project_name, order_begin_time, order_end_time, promotion_policy, 
    group_id, product_line_id, product_id, execute_id, bus_status, audit_status, old_agent_id, 
    bill_amount, total_claim_amount, init_deducted_amount, is_oa_open_bill, is_deduct_completed, 
    offline_bill_amount
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.account.po.CrmContractPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_contract
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_contract
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.account.po.CrmContractPoExample">
    delete from crm_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_contract (contract_number, account_id, name, 
      type, agent_id, legal_contract_id, 
      begin_time, end_time, archive_status, 
      status, remark, amount, 
      amount_remark, review_remark, version, 
      ctime, mtime, is_deleted, 
      creator, finance_title_agent_id, distribution, 
      discount, quota_id, deduct_quota, 
      project_name, order_begin_time, order_end_time, 
      promotion_policy, group_id, product_line_id, 
      product_id, execute_id, bus_status, 
      audit_status, old_agent_id, bill_amount, 
      total_claim_amount, init_deducted_amount, is_oa_open_bill, 
      is_deduct_completed, offline_bill_amount)
    values (#{contractNumber,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{agentId,jdbcType=INTEGER}, #{legalContractId,jdbcType=VARCHAR}, 
      #{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{archiveStatus,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, 
      #{amountRemark,jdbcType=VARCHAR}, #{reviewRemark,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{creator,jdbcType=VARCHAR}, #{financeTitleAgentId,jdbcType=INTEGER}, #{distribution,jdbcType=INTEGER}, 
      #{discount,jdbcType=DECIMAL}, #{quotaId,jdbcType=INTEGER}, #{deductQuota,jdbcType=BIGINT}, 
      #{projectName,jdbcType=VARCHAR}, #{orderBeginTime,jdbcType=TIMESTAMP}, #{orderEndTime,jdbcType=TIMESTAMP}, 
      #{promotionPolicy,jdbcType=TINYINT}, #{groupId,jdbcType=INTEGER}, #{productLineId,jdbcType=INTEGER}, 
      #{productId,jdbcType=INTEGER}, #{executeId,jdbcType=INTEGER}, #{busStatus,jdbcType=TINYINT}, 
      #{auditStatus,jdbcType=TINYINT}, #{oldAgentId,jdbcType=INTEGER}, #{billAmount,jdbcType=BIGINT}, 
      #{totalClaimAmount,jdbcType=BIGINT}, #{initDeductedAmount,jdbcType=BIGINT}, #{isOaOpenBill,jdbcType=TINYINT}, 
      #{isDeductCompleted,jdbcType=TINYINT}, #{offlineBillAmount,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="legalContractId != null">
        legal_contract_id,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="archiveStatus != null">
        archive_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="amountRemark != null">
        amount_remark,
      </if>
      <if test="reviewRemark != null">
        review_remark,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="financeTitleAgentId != null">
        finance_title_agent_id,
      </if>
      <if test="distribution != null">
        distribution,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="deductQuota != null">
        deduct_quota,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="orderBeginTime != null">
        order_begin_time,
      </if>
      <if test="orderEndTime != null">
        order_end_time,
      </if>
      <if test="promotionPolicy != null">
        promotion_policy,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="productLineId != null">
        product_line_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="executeId != null">
        execute_id,
      </if>
      <if test="busStatus != null">
        bus_status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="oldAgentId != null">
        old_agent_id,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="totalClaimAmount != null">
        total_claim_amount,
      </if>
      <if test="initDeductedAmount != null">
        init_deducted_amount,
      </if>
      <if test="isOaOpenBill != null">
        is_oa_open_bill,
      </if>
      <if test="isDeductCompleted != null">
        is_deduct_completed,
      </if>
      <if test="offlineBillAmount != null">
        offline_bill_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="legalContractId != null">
        #{legalContractId,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="archiveStatus != null">
        #{archiveStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="amountRemark != null">
        #{amountRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null">
        #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="financeTitleAgentId != null">
        #{financeTitleAgentId,jdbcType=INTEGER},
      </if>
      <if test="distribution != null">
        #{distribution,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=INTEGER},
      </if>
      <if test="deductQuota != null">
        #{deductQuota,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="orderBeginTime != null">
        #{orderBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promotionPolicy != null">
        #{promotionPolicy,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="executeId != null">
        #{executeId,jdbcType=INTEGER},
      </if>
      <if test="busStatus != null">
        #{busStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="oldAgentId != null">
        #{oldAgentId,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=BIGINT},
      </if>
      <if test="totalClaimAmount != null">
        #{totalClaimAmount,jdbcType=BIGINT},
      </if>
      <if test="initDeductedAmount != null">
        #{initDeductedAmount,jdbcType=BIGINT},
      </if>
      <if test="isOaOpenBill != null">
        #{isOaOpenBill,jdbcType=TINYINT},
      </if>
      <if test="isDeductCompleted != null">
        #{isDeductCompleted,jdbcType=TINYINT},
      </if>
      <if test="offlineBillAmount != null">
        #{offlineBillAmount,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.account.po.CrmContractPoExample" resultType="java.lang.Long">
    select count(*) from crm_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_contract
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.contractNumber != null">
        contract_number = #{record.contractNumber,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.agentId != null">
        agent_id = #{record.agentId,jdbcType=INTEGER},
      </if>
      <if test="record.legalContractId != null">
        legal_contract_id = #{record.legalContractId,jdbcType=VARCHAR},
      </if>
      <if test="record.beginTime != null">
        begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.archiveStatus != null">
        archive_status = #{record.archiveStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=BIGINT},
      </if>
      <if test="record.amountRemark != null">
        amount_remark = #{record.amountRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.reviewRemark != null">
        review_remark = #{record.reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.financeTitleAgentId != null">
        finance_title_agent_id = #{record.financeTitleAgentId,jdbcType=INTEGER},
      </if>
      <if test="record.distribution != null">
        distribution = #{record.distribution,jdbcType=INTEGER},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=DECIMAL},
      </if>
      <if test="record.quotaId != null">
        quota_id = #{record.quotaId,jdbcType=INTEGER},
      </if>
      <if test="record.deductQuota != null">
        deduct_quota = #{record.deductQuota,jdbcType=BIGINT},
      </if>
      <if test="record.projectName != null">
        project_name = #{record.projectName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderBeginTime != null">
        order_begin_time = #{record.orderBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderEndTime != null">
        order_end_time = #{record.orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.promotionPolicy != null">
        promotion_policy = #{record.promotionPolicy,jdbcType=TINYINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=INTEGER},
      </if>
      <if test="record.productLineId != null">
        product_line_id = #{record.productLineId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.executeId != null">
        execute_id = #{record.executeId,jdbcType=INTEGER},
      </if>
      <if test="record.busStatus != null">
        bus_status = #{record.busStatus,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.oldAgentId != null">
        old_agent_id = #{record.oldAgentId,jdbcType=INTEGER},
      </if>
      <if test="record.billAmount != null">
        bill_amount = #{record.billAmount,jdbcType=BIGINT},
      </if>
      <if test="record.totalClaimAmount != null">
        total_claim_amount = #{record.totalClaimAmount,jdbcType=BIGINT},
      </if>
      <if test="record.initDeductedAmount != null">
        init_deducted_amount = #{record.initDeductedAmount,jdbcType=BIGINT},
      </if>
      <if test="record.isOaOpenBill != null">
        is_oa_open_bill = #{record.isOaOpenBill,jdbcType=TINYINT},
      </if>
      <if test="record.isDeductCompleted != null">
        is_deduct_completed = #{record.isDeductCompleted,jdbcType=TINYINT},
      </if>
      <if test="record.offlineBillAmount != null">
        offline_bill_amount = #{record.offlineBillAmount,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_contract
    set id = #{record.id,jdbcType=INTEGER},
      contract_number = #{record.contractNumber,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      agent_id = #{record.agentId,jdbcType=INTEGER},
      legal_contract_id = #{record.legalContractId,jdbcType=VARCHAR},
      begin_time = #{record.beginTime,jdbcType=TIMESTAMP},
      end_time = #{record.endTime,jdbcType=TIMESTAMP},
      archive_status = #{record.archiveStatus,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      amount = #{record.amount,jdbcType=BIGINT},
      amount_remark = #{record.amountRemark,jdbcType=VARCHAR},
      review_remark = #{record.reviewRemark,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      finance_title_agent_id = #{record.financeTitleAgentId,jdbcType=INTEGER},
      distribution = #{record.distribution,jdbcType=INTEGER},
      discount = #{record.discount,jdbcType=DECIMAL},
      quota_id = #{record.quotaId,jdbcType=INTEGER},
      deduct_quota = #{record.deductQuota,jdbcType=BIGINT},
      project_name = #{record.projectName,jdbcType=VARCHAR},
      order_begin_time = #{record.orderBeginTime,jdbcType=TIMESTAMP},
      order_end_time = #{record.orderEndTime,jdbcType=TIMESTAMP},
      promotion_policy = #{record.promotionPolicy,jdbcType=TINYINT},
      group_id = #{record.groupId,jdbcType=INTEGER},
      product_line_id = #{record.productLineId,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=INTEGER},
      execute_id = #{record.executeId,jdbcType=INTEGER},
      bus_status = #{record.busStatus,jdbcType=TINYINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      old_agent_id = #{record.oldAgentId,jdbcType=INTEGER},
      bill_amount = #{record.billAmount,jdbcType=BIGINT},
      total_claim_amount = #{record.totalClaimAmount,jdbcType=BIGINT},
      init_deducted_amount = #{record.initDeductedAmount,jdbcType=BIGINT},
      is_oa_open_bill = #{record.isOaOpenBill,jdbcType=TINYINT},
      is_deduct_completed = #{record.isDeductCompleted,jdbcType=TINYINT},
      offline_bill_amount = #{record.offlineBillAmount,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    update crm_contract
    <set>
      <if test="contractNumber != null">
        contract_number = #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=INTEGER},
      </if>
      <if test="legalContractId != null">
        legal_contract_id = #{legalContractId,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="archiveStatus != null">
        archive_status = #{archiveStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=BIGINT},
      </if>
      <if test="amountRemark != null">
        amount_remark = #{amountRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null">
        review_remark = #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="financeTitleAgentId != null">
        finance_title_agent_id = #{financeTitleAgentId,jdbcType=INTEGER},
      </if>
      <if test="distribution != null">
        distribution = #{distribution,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=DECIMAL},
      </if>
      <if test="quotaId != null">
        quota_id = #{quotaId,jdbcType=INTEGER},
      </if>
      <if test="deductQuota != null">
        deduct_quota = #{deductQuota,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        project_name = #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="orderBeginTime != null">
        order_begin_time = #{orderBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promotionPolicy != null">
        promotion_policy = #{promotionPolicy,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        product_line_id = #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="executeId != null">
        execute_id = #{executeId,jdbcType=INTEGER},
      </if>
      <if test="busStatus != null">
        bus_status = #{busStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="oldAgentId != null">
        old_agent_id = #{oldAgentId,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=BIGINT},
      </if>
      <if test="totalClaimAmount != null">
        total_claim_amount = #{totalClaimAmount,jdbcType=BIGINT},
      </if>
      <if test="initDeductedAmount != null">
        init_deducted_amount = #{initDeductedAmount,jdbcType=BIGINT},
      </if>
      <if test="isOaOpenBill != null">
        is_oa_open_bill = #{isOaOpenBill,jdbcType=TINYINT},
      </if>
      <if test="isDeductCompleted != null">
        is_deduct_completed = #{isDeductCompleted,jdbcType=TINYINT},
      </if>
      <if test="offlineBillAmount != null">
        offline_bill_amount = #{offlineBillAmount,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    update crm_contract
    set contract_number = #{contractNumber,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      agent_id = #{agentId,jdbcType=INTEGER},
      legal_contract_id = #{legalContractId,jdbcType=VARCHAR},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      archive_status = #{archiveStatus,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=BIGINT},
      amount_remark = #{amountRemark,jdbcType=VARCHAR},
      review_remark = #{reviewRemark,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      finance_title_agent_id = #{financeTitleAgentId,jdbcType=INTEGER},
      distribution = #{distribution,jdbcType=INTEGER},
      discount = #{discount,jdbcType=DECIMAL},
      quota_id = #{quotaId,jdbcType=INTEGER},
      deduct_quota = #{deductQuota,jdbcType=BIGINT},
      project_name = #{projectName,jdbcType=VARCHAR},
      order_begin_time = #{orderBeginTime,jdbcType=TIMESTAMP},
      order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      promotion_policy = #{promotionPolicy,jdbcType=TINYINT},
      group_id = #{groupId,jdbcType=INTEGER},
      product_line_id = #{productLineId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      execute_id = #{executeId,jdbcType=INTEGER},
      bus_status = #{busStatus,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      old_agent_id = #{oldAgentId,jdbcType=INTEGER},
      bill_amount = #{billAmount,jdbcType=BIGINT},
      total_claim_amount = #{totalClaimAmount,jdbcType=BIGINT},
      init_deducted_amount = #{initDeductedAmount,jdbcType=BIGINT},
      is_oa_open_bill = #{isOaOpenBill,jdbcType=TINYINT},
      is_deduct_completed = #{isDeductCompleted,jdbcType=TINYINT},
      offline_bill_amount = #{offlineBillAmount,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_contract (contract_number, account_id, name, 
      type, agent_id, legal_contract_id, 
      begin_time, end_time, archive_status, 
      status, remark, amount, 
      amount_remark, review_remark, version, 
      ctime, mtime, is_deleted, 
      creator, finance_title_agent_id, distribution, 
      discount, quota_id, deduct_quota, 
      project_name, order_begin_time, order_end_time, 
      promotion_policy, group_id, product_line_id, 
      product_id, execute_id, bus_status, 
      audit_status, old_agent_id, bill_amount, 
      total_claim_amount, init_deducted_amount, is_oa_open_bill, 
      is_deduct_completed, offline_bill_amount)
    values (#{contractNumber,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{agentId,jdbcType=INTEGER}, #{legalContractId,jdbcType=VARCHAR}, 
      #{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{archiveStatus,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, #{amount,jdbcType=BIGINT}, 
      #{amountRemark,jdbcType=VARCHAR}, #{reviewRemark,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{creator,jdbcType=VARCHAR}, #{financeTitleAgentId,jdbcType=INTEGER}, #{distribution,jdbcType=INTEGER}, 
      #{discount,jdbcType=DECIMAL}, #{quotaId,jdbcType=INTEGER}, #{deductQuota,jdbcType=BIGINT}, 
      #{projectName,jdbcType=VARCHAR}, #{orderBeginTime,jdbcType=TIMESTAMP}, #{orderEndTime,jdbcType=TIMESTAMP}, 
      #{promotionPolicy,jdbcType=TINYINT}, #{groupId,jdbcType=INTEGER}, #{productLineId,jdbcType=INTEGER}, 
      #{productId,jdbcType=INTEGER}, #{executeId,jdbcType=INTEGER}, #{busStatus,jdbcType=TINYINT}, 
      #{auditStatus,jdbcType=TINYINT}, #{oldAgentId,jdbcType=INTEGER}, #{billAmount,jdbcType=BIGINT}, 
      #{totalClaimAmount,jdbcType=BIGINT}, #{initDeductedAmount,jdbcType=BIGINT}, #{isOaOpenBill,jdbcType=TINYINT}, 
      #{isDeductCompleted,jdbcType=TINYINT}, #{offlineBillAmount,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      contract_number = values(contract_number),
      account_id = values(account_id),
      name = values(name),
      type = values(type),
      agent_id = values(agent_id),
      legal_contract_id = values(legal_contract_id),
      begin_time = values(begin_time),
      end_time = values(end_time),
      archive_status = values(archive_status),
      status = values(status),
      remark = values(remark),
      amount = values(amount),
      amount_remark = values(amount_remark),
      review_remark = values(review_remark),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      creator = values(creator),
      finance_title_agent_id = values(finance_title_agent_id),
      distribution = values(distribution),
      discount = values(discount),
      quota_id = values(quota_id),
      deduct_quota = values(deduct_quota),
      project_name = values(project_name),
      order_begin_time = values(order_begin_time),
      order_end_time = values(order_end_time),
      promotion_policy = values(promotion_policy),
      group_id = values(group_id),
      product_line_id = values(product_line_id),
      product_id = values(product_id),
      execute_id = values(execute_id),
      bus_status = values(bus_status),
      audit_status = values(audit_status),
      old_agent_id = values(old_agent_id),
      bill_amount = values(bill_amount),
      total_claim_amount = values(total_claim_amount),
      init_deducted_amount = values(init_deducted_amount),
      is_oa_open_bill = values(is_oa_open_bill),
      is_deduct_completed = values(is_deduct_completed),
      offline_bill_amount = values(offline_bill_amount),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_contract
      (contract_number,account_id,name,type,agent_id,legal_contract_id,begin_time,end_time,archive_status,status,remark,amount,amount_remark,review_remark,version,ctime,mtime,is_deleted,creator,finance_title_agent_id,distribution,discount,quota_id,deduct_quota,project_name,order_begin_time,order_end_time,promotion_policy,group_id,product_line_id,product_id,execute_id,bus_status,audit_status,old_agent_id,bill_amount,total_claim_amount,init_deducted_amount,is_oa_open_bill,is_deduct_completed,offline_bill_amount)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.contractNumber,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.type,jdbcType=TINYINT},
        #{item.agentId,jdbcType=INTEGER},
        #{item.legalContractId,jdbcType=VARCHAR},
        #{item.beginTime,jdbcType=TIMESTAMP},
        #{item.endTime,jdbcType=TIMESTAMP},
        #{item.archiveStatus,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.amount,jdbcType=BIGINT},
        #{item.amountRemark,jdbcType=VARCHAR},
        #{item.reviewRemark,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.creator,jdbcType=VARCHAR},
        #{item.financeTitleAgentId,jdbcType=INTEGER},
        #{item.distribution,jdbcType=INTEGER},
        #{item.discount,jdbcType=DECIMAL},
        #{item.quotaId,jdbcType=INTEGER},
        #{item.deductQuota,jdbcType=BIGINT},
        #{item.projectName,jdbcType=VARCHAR},
        #{item.orderBeginTime,jdbcType=TIMESTAMP},
        #{item.orderEndTime,jdbcType=TIMESTAMP},
        #{item.promotionPolicy,jdbcType=TINYINT},
        #{item.groupId,jdbcType=INTEGER},
        #{item.productLineId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.executeId,jdbcType=INTEGER},
        #{item.busStatus,jdbcType=TINYINT},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.oldAgentId,jdbcType=INTEGER},
        #{item.billAmount,jdbcType=BIGINT},
        #{item.totalClaimAmount,jdbcType=BIGINT},
        #{item.initDeductedAmount,jdbcType=BIGINT},
        #{item.isOaOpenBill,jdbcType=TINYINT},
        #{item.isDeductCompleted,jdbcType=TINYINT},
        #{item.offlineBillAmount,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_contract
      (contract_number,account_id,name,type,agent_id,legal_contract_id,begin_time,end_time,archive_status,status,remark,amount,amount_remark,review_remark,version,ctime,mtime,is_deleted,creator,finance_title_agent_id,distribution,discount,quota_id,deduct_quota,project_name,order_begin_time,order_end_time,promotion_policy,group_id,product_line_id,product_id,execute_id,bus_status,audit_status,old_agent_id,bill_amount,total_claim_amount,init_deducted_amount,is_oa_open_bill,is_deduct_completed,offline_bill_amount)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.contractNumber,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.type,jdbcType=TINYINT},
        #{item.agentId,jdbcType=INTEGER},
        #{item.legalContractId,jdbcType=VARCHAR},
        #{item.beginTime,jdbcType=TIMESTAMP},
        #{item.endTime,jdbcType=TIMESTAMP},
        #{item.archiveStatus,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.amount,jdbcType=BIGINT},
        #{item.amountRemark,jdbcType=VARCHAR},
        #{item.reviewRemark,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.creator,jdbcType=VARCHAR},
        #{item.financeTitleAgentId,jdbcType=INTEGER},
        #{item.distribution,jdbcType=INTEGER},
        #{item.discount,jdbcType=DECIMAL},
        #{item.quotaId,jdbcType=INTEGER},
        #{item.deductQuota,jdbcType=BIGINT},
        #{item.projectName,jdbcType=VARCHAR},
        #{item.orderBeginTime,jdbcType=TIMESTAMP},
        #{item.orderEndTime,jdbcType=TIMESTAMP},
        #{item.promotionPolicy,jdbcType=TINYINT},
        #{item.groupId,jdbcType=INTEGER},
        #{item.productLineId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.executeId,jdbcType=INTEGER},
        #{item.busStatus,jdbcType=TINYINT},
        #{item.auditStatus,jdbcType=TINYINT},
        #{item.oldAgentId,jdbcType=INTEGER},
        #{item.billAmount,jdbcType=BIGINT},
        #{item.totalClaimAmount,jdbcType=BIGINT},
        #{item.initDeductedAmount,jdbcType=BIGINT},
        #{item.isOaOpenBill,jdbcType=TINYINT},
        #{item.isDeductCompleted,jdbcType=TINYINT},
        #{item.offlineBillAmount,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      contract_number = values(contract_number),
      account_id = values(account_id),
      name = values(name),
      type = values(type),
      agent_id = values(agent_id),
      legal_contract_id = values(legal_contract_id),
      begin_time = values(begin_time),
      end_time = values(end_time),
      archive_status = values(archive_status),
      status = values(status),
      remark = values(remark),
      amount = values(amount),
      amount_remark = values(amount_remark),
      review_remark = values(review_remark),
      version = values(version),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      creator = values(creator),
      finance_title_agent_id = values(finance_title_agent_id),
      distribution = values(distribution),
      discount = values(discount),
      quota_id = values(quota_id),
      deduct_quota = values(deduct_quota),
      project_name = values(project_name),
      order_begin_time = values(order_begin_time),
      order_end_time = values(order_end_time),
      promotion_policy = values(promotion_policy),
      group_id = values(group_id),
      product_line_id = values(product_line_id),
      product_id = values(product_id),
      execute_id = values(execute_id),
      bus_status = values(bus_status),
      audit_status = values(audit_status),
      old_agent_id = values(old_agent_id),
      bill_amount = values(bill_amount),
      total_claim_amount = values(total_claim_amount),
      init_deducted_amount = values(init_deducted_amount),
      is_oa_open_bill = values(is_oa_open_bill),
      is_deduct_completed = values(is_deduct_completed),
      offline_bill_amount = values(offline_bill_amount),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.account.po.CrmContractPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractNumber != null">
        contract_number,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="legalContractId != null">
        legal_contract_id,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="archiveStatus != null">
        archive_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="amountRemark != null">
        amount_remark,
      </if>
      <if test="reviewRemark != null">
        review_remark,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="financeTitleAgentId != null">
        finance_title_agent_id,
      </if>
      <if test="distribution != null">
        distribution,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="quotaId != null">
        quota_id,
      </if>
      <if test="deductQuota != null">
        deduct_quota,
      </if>
      <if test="projectName != null">
        project_name,
      </if>
      <if test="orderBeginTime != null">
        order_begin_time,
      </if>
      <if test="orderEndTime != null">
        order_end_time,
      </if>
      <if test="promotionPolicy != null">
        promotion_policy,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="productLineId != null">
        product_line_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="executeId != null">
        execute_id,
      </if>
      <if test="busStatus != null">
        bus_status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="oldAgentId != null">
        old_agent_id,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="totalClaimAmount != null">
        total_claim_amount,
      </if>
      <if test="initDeductedAmount != null">
        init_deducted_amount,
      </if>
      <if test="isOaOpenBill != null">
        is_oa_open_bill,
      </if>
      <if test="isDeductCompleted != null">
        is_deduct_completed,
      </if>
      <if test="offlineBillAmount != null">
        offline_bill_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractNumber != null">
        #{contractNumber,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="legalContractId != null">
        #{legalContractId,jdbcType=VARCHAR},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="archiveStatus != null">
        #{archiveStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=BIGINT},
      </if>
      <if test="amountRemark != null">
        #{amountRemark,jdbcType=VARCHAR},
      </if>
      <if test="reviewRemark != null">
        #{reviewRemark,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="financeTitleAgentId != null">
        #{financeTitleAgentId,jdbcType=INTEGER},
      </if>
      <if test="distribution != null">
        #{distribution,jdbcType=INTEGER},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=DECIMAL},
      </if>
      <if test="quotaId != null">
        #{quotaId,jdbcType=INTEGER},
      </if>
      <if test="deductQuota != null">
        #{deductQuota,jdbcType=BIGINT},
      </if>
      <if test="projectName != null">
        #{projectName,jdbcType=VARCHAR},
      </if>
      <if test="orderBeginTime != null">
        #{orderBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="promotionPolicy != null">
        #{promotionPolicy,jdbcType=TINYINT},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=INTEGER},
      </if>
      <if test="productLineId != null">
        #{productLineId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="executeId != null">
        #{executeId,jdbcType=INTEGER},
      </if>
      <if test="busStatus != null">
        #{busStatus,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="oldAgentId != null">
        #{oldAgentId,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=BIGINT},
      </if>
      <if test="totalClaimAmount != null">
        #{totalClaimAmount,jdbcType=BIGINT},
      </if>
      <if test="initDeductedAmount != null">
        #{initDeductedAmount,jdbcType=BIGINT},
      </if>
      <if test="isOaOpenBill != null">
        #{isOaOpenBill,jdbcType=TINYINT},
      </if>
      <if test="isDeductCompleted != null">
        #{isDeductCompleted,jdbcType=TINYINT},
      </if>
      <if test="offlineBillAmount != null">
        #{offlineBillAmount,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="contractNumber != null">
        contract_number = values(contract_number),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="agentId != null">
        agent_id = values(agent_id),
      </if>
      <if test="legalContractId != null">
        legal_contract_id = values(legal_contract_id),
      </if>
      <if test="beginTime != null">
        begin_time = values(begin_time),
      </if>
      <if test="endTime != null">
        end_time = values(end_time),
      </if>
      <if test="archiveStatus != null">
        archive_status = values(archive_status),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="amount != null">
        amount = values(amount),
      </if>
      <if test="amountRemark != null">
        amount_remark = values(amount_remark),
      </if>
      <if test="reviewRemark != null">
        review_remark = values(review_remark),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="creator != null">
        creator = values(creator),
      </if>
      <if test="financeTitleAgentId != null">
        finance_title_agent_id = values(finance_title_agent_id),
      </if>
      <if test="distribution != null">
        distribution = values(distribution),
      </if>
      <if test="discount != null">
        discount = values(discount),
      </if>
      <if test="quotaId != null">
        quota_id = values(quota_id),
      </if>
      <if test="deductQuota != null">
        deduct_quota = values(deduct_quota),
      </if>
      <if test="projectName != null">
        project_name = values(project_name),
      </if>
      <if test="orderBeginTime != null">
        order_begin_time = values(order_begin_time),
      </if>
      <if test="orderEndTime != null">
        order_end_time = values(order_end_time),
      </if>
      <if test="promotionPolicy != null">
        promotion_policy = values(promotion_policy),
      </if>
      <if test="groupId != null">
        group_id = values(group_id),
      </if>
      <if test="productLineId != null">
        product_line_id = values(product_line_id),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="executeId != null">
        execute_id = values(execute_id),
      </if>
      <if test="busStatus != null">
        bus_status = values(bus_status),
      </if>
      <if test="auditStatus != null">
        audit_status = values(audit_status),
      </if>
      <if test="oldAgentId != null">
        old_agent_id = values(old_agent_id),
      </if>
      <if test="billAmount != null">
        bill_amount = values(bill_amount),
      </if>
      <if test="totalClaimAmount != null">
        total_claim_amount = values(total_claim_amount),
      </if>
      <if test="initDeductedAmount != null">
        init_deducted_amount = values(init_deducted_amount),
      </if>
      <if test="isOaOpenBill != null">
        is_oa_open_bill = values(is_oa_open_bill),
      </if>
      <if test="isDeductCompleted != null">
        is_deduct_completed = values(is_deduct_completed),
      </if>
      <if test="offlineBillAmount != null">
        offline_bill_amount = values(offline_bill_amount),
      </if>
    </trim>
  </insert>
</mapper>