<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.creative.dao.GdCreativeDynamicDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="forward_state" jdbcType="TINYINT" property="forwardState" />
    <result column="reply_state" jdbcType="TINYINT" property="replyState" />
    <result column="reply_monitor" jdbcType="TINYINT" property="replyMonitor" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="dynamic_id" jdbcType="BIGINT" property="dynamicId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="launch_type" jdbcType="TINYINT" property="launchType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, forward_state, reply_state, reply_monitor, is_deleted, ctime, mtime, 
    mid, dynamic_id, title, launch_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_creative_dynamic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_creative_dynamic
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_creative_dynamic
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPoExample">
    delete from gd_creative_dynamic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_dynamic (creative_id, forward_state, reply_state, 
      reply_monitor, is_deleted, ctime, 
      mtime, mid, dynamic_id, 
      title, launch_type)
    values (#{creativeId,jdbcType=BIGINT}, #{forwardState,jdbcType=TINYINT}, #{replyState,jdbcType=TINYINT}, 
      #{replyMonitor,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{mid,jdbcType=BIGINT}, #{dynamicId,jdbcType=BIGINT}, 
      #{title,jdbcType=VARCHAR}, #{launchType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_dynamic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="forwardState != null">
        forward_state,
      </if>
      <if test="replyState != null">
        reply_state,
      </if>
      <if test="replyMonitor != null">
        reply_monitor,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="launchType != null">
        launch_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="forwardState != null">
        #{forwardState,jdbcType=TINYINT},
      </if>
      <if test="replyState != null">
        #{replyState,jdbcType=TINYINT},
      </if>
      <if test="replyMonitor != null">
        #{replyMonitor,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        #{launchType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPoExample" resultType="java.lang.Long">
    select count(*) from gd_creative_dynamic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_creative_dynamic
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.forwardState != null">
        forward_state = #{record.forwardState,jdbcType=TINYINT},
      </if>
      <if test="record.replyState != null">
        reply_state = #{record.replyState,jdbcType=TINYINT},
      </if>
      <if test="record.replyMonitor != null">
        reply_monitor = #{record.replyMonitor,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.dynamicId != null">
        dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.launchType != null">
        launch_type = #{record.launchType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_creative_dynamic
    set id = #{record.id,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      forward_state = #{record.forwardState,jdbcType=TINYINT},
      reply_state = #{record.replyState,jdbcType=TINYINT},
      reply_monitor = #{record.replyMonitor,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      mid = #{record.mid,jdbcType=BIGINT},
      dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      launch_type = #{record.launchType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    update gd_creative_dynamic
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="forwardState != null">
        forward_state = #{forwardState,jdbcType=TINYINT},
      </if>
      <if test="replyState != null">
        reply_state = #{replyState,jdbcType=TINYINT},
      </if>
      <if test="replyMonitor != null">
        reply_monitor = #{replyMonitor,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="dynamicId != null">
        dynamic_id = #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        launch_type = #{launchType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    update gd_creative_dynamic
    set creative_id = #{creativeId,jdbcType=BIGINT},
      forward_state = #{forwardState,jdbcType=TINYINT},
      reply_state = #{replyState,jdbcType=TINYINT},
      reply_monitor = #{replyMonitor,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      mid = #{mid,jdbcType=BIGINT},
      dynamic_id = #{dynamicId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      launch_type = #{launchType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_dynamic (creative_id, forward_state, reply_state, 
      reply_monitor, is_deleted, ctime, 
      mtime, mid, dynamic_id, 
      title, launch_type)
    values (#{creativeId,jdbcType=BIGINT}, #{forwardState,jdbcType=TINYINT}, #{replyState,jdbcType=TINYINT}, 
      #{replyMonitor,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{mid,jdbcType=BIGINT}, #{dynamicId,jdbcType=BIGINT}, 
      #{title,jdbcType=VARCHAR}, #{launchType,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      forward_state = values(forward_state),
      reply_state = values(reply_state),
      reply_monitor = values(reply_monitor),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      mid = values(mid),
      dynamic_id = values(dynamic_id),
      title = values(title),
      launch_type = values(launch_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_creative_dynamic
      (creative_id,forward_state,reply_state,reply_monitor,is_deleted,ctime,mtime,mid,dynamic_id,title,launch_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.forwardState,jdbcType=TINYINT},
        #{item.replyState,jdbcType=TINYINT},
        #{item.replyMonitor,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.mid,jdbcType=BIGINT},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.title,jdbcType=VARCHAR},
        #{item.launchType,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_creative_dynamic
      (creative_id,forward_state,reply_state,reply_monitor,is_deleted,ctime,mtime,mid,dynamic_id,title,launch_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.forwardState,jdbcType=TINYINT},
        #{item.replyState,jdbcType=TINYINT},
        #{item.replyMonitor,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.mid,jdbcType=BIGINT},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.title,jdbcType=VARCHAR},
        #{item.launchType,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      forward_state = values(forward_state),
      reply_state = values(reply_state),
      reply_monitor = values(reply_monitor),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      mid = values(mid),
      dynamic_id = values(dynamic_id),
      title = values(title),
      launch_type = values(launch_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeDynamicPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_dynamic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="forwardState != null">
        forward_state,
      </if>
      <if test="replyState != null">
        reply_state,
      </if>
      <if test="replyMonitor != null">
        reply_monitor,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="launchType != null">
        launch_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="forwardState != null">
        #{forwardState,jdbcType=TINYINT},
      </if>
      <if test="replyState != null">
        #{replyState,jdbcType=TINYINT},
      </if>
      <if test="replyMonitor != null">
        #{replyMonitor,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        #{launchType,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="forwardState != null">
        forward_state = values(forward_state),
      </if>
      <if test="replyState != null">
        reply_state = values(reply_state),
      </if>
      <if test="replyMonitor != null">
        reply_monitor = values(reply_monitor),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="dynamicId != null">
        dynamic_id = values(dynamic_id),
      </if>
      <if test="title != null">
        title = values(title),
      </if>
      <if test="launchType != null">
        launch_type = values(launch_type),
      </if>
    </trim>
  </insert>
</mapper>