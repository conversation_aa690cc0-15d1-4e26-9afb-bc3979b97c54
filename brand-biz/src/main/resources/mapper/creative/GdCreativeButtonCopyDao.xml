<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.creative.dao.GdCreativeButtonCopyDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="button_copy_id" jdbcType="INTEGER" property="buttonCopyId" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="customized_url" jdbcType="VARCHAR" property="customizedUrl" />
    <result column="button_type" jdbcType="TINYINT" property="buttonType" />
    <result column="extend_url" jdbcType="VARCHAR" property="extendUrl" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="image_id" jdbcType="INTEGER" property="imageId" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="jump_type" jdbcType="TINYINT" property="jumpType" />
    <result column="scheme_url" jdbcType="VARCHAR" property="schemeUrl" />
    <result column="button_name" jdbcType="VARCHAR" property="buttonName" />
    <result column="category" jdbcType="INTEGER" property="category" />
    <result column="bound_id" jdbcType="BIGINT" property="boundId" />
    <result column="game_base_id" jdbcType="INTEGER" property="gameBaseId" />
    <result column="trackadf" jdbcType="VARCHAR" property="trackadf" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, button_copy_id, jump_url, customized_url, button_type, extend_url, 
    is_deleted, ctime, mtime, image_id, seq, jump_type, scheme_url, button_name, category, 
    bound_id, game_base_id, trackadf
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_creative_button_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_creative_button_copy
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_creative_button_copy
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPoExample">
    delete from gd_creative_button_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_button_copy (creative_id, button_copy_id, jump_url, 
      customized_url, button_type, extend_url, 
      is_deleted, ctime, mtime, 
      image_id, seq, jump_type, 
      scheme_url, button_name, category, 
      bound_id, game_base_id, trackadf
      )
    values (#{creativeId,jdbcType=BIGINT}, #{buttonCopyId,jdbcType=INTEGER}, #{jumpUrl,jdbcType=VARCHAR}, 
      #{customizedUrl,jdbcType=VARCHAR}, #{buttonType,jdbcType=TINYINT}, #{extendUrl,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{imageId,jdbcType=INTEGER}, #{seq,jdbcType=INTEGER}, #{jumpType,jdbcType=TINYINT}, 
      #{schemeUrl,jdbcType=VARCHAR}, #{buttonName,jdbcType=VARCHAR}, #{category,jdbcType=INTEGER}, 
      #{boundId,jdbcType=BIGINT}, #{gameBaseId,jdbcType=INTEGER}, #{trackadf,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_button_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="buttonCopyId != null">
        button_copy_id,
      </if>
      <if test="jumpUrl != null">
        jump_url,
      </if>
      <if test="customizedUrl != null">
        customized_url,
      </if>
      <if test="buttonType != null">
        button_type,
      </if>
      <if test="extendUrl != null">
        extend_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="buttonName != null">
        button_name,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="boundId != null">
        bound_id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="trackadf != null">
        trackadf,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="buttonCopyId != null">
        #{buttonCopyId,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedUrl != null">
        #{customizedUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonType != null">
        #{buttonType,jdbcType=TINYINT},
      </if>
      <if test="extendUrl != null">
        #{extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=INTEGER},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonName != null">
        #{buttonName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=INTEGER},
      </if>
      <if test="boundId != null">
        #{boundId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="trackadf != null">
        #{trackadf,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPoExample" resultType="java.lang.Long">
    select count(*) from gd_creative_button_copy
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_creative_button_copy
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.buttonCopyId != null">
        button_copy_id = #{record.buttonCopyId,jdbcType=INTEGER},
      </if>
      <if test="record.jumpUrl != null">
        jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedUrl != null">
        customized_url = #{record.customizedUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.buttonType != null">
        button_type = #{record.buttonType,jdbcType=TINYINT},
      </if>
      <if test="record.extendUrl != null">
        extend_url = #{record.extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.imageId != null">
        image_id = #{record.imageId,jdbcType=INTEGER},
      </if>
      <if test="record.seq != null">
        seq = #{record.seq,jdbcType=INTEGER},
      </if>
      <if test="record.jumpType != null">
        jump_type = #{record.jumpType,jdbcType=TINYINT},
      </if>
      <if test="record.schemeUrl != null">
        scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.buttonName != null">
        button_name = #{record.buttonName,jdbcType=VARCHAR},
      </if>
      <if test="record.category != null">
        category = #{record.category,jdbcType=INTEGER},
      </if>
      <if test="record.boundId != null">
        bound_id = #{record.boundId,jdbcType=BIGINT},
      </if>
      <if test="record.gameBaseId != null">
        game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="record.trackadf != null">
        trackadf = #{record.trackadf,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_creative_button_copy
    set id = #{record.id,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      button_copy_id = #{record.buttonCopyId,jdbcType=INTEGER},
      jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      customized_url = #{record.customizedUrl,jdbcType=VARCHAR},
      button_type = #{record.buttonType,jdbcType=TINYINT},
      extend_url = #{record.extendUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      image_id = #{record.imageId,jdbcType=INTEGER},
      seq = #{record.seq,jdbcType=INTEGER},
      jump_type = #{record.jumpType,jdbcType=TINYINT},
      scheme_url = #{record.schemeUrl,jdbcType=VARCHAR},
      button_name = #{record.buttonName,jdbcType=VARCHAR},
      category = #{record.category,jdbcType=INTEGER},
      bound_id = #{record.boundId,jdbcType=BIGINT},
      game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      trackadf = #{record.trackadf,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    update gd_creative_button_copy
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="buttonCopyId != null">
        button_copy_id = #{buttonCopyId,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        jump_url = #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedUrl != null">
        customized_url = #{customizedUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonType != null">
        button_type = #{buttonType,jdbcType=TINYINT},
      </if>
      <if test="extendUrl != null">
        extend_url = #{extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        image_id = #{imageId,jdbcType=INTEGER},
      </if>
      <if test="seq != null">
        seq = #{seq,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        jump_type = #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonName != null">
        button_name = #{buttonName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=INTEGER},
      </if>
      <if test="boundId != null">
        bound_id = #{boundId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        game_base_id = #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="trackadf != null">
        trackadf = #{trackadf,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    update gd_creative_button_copy
    set creative_id = #{creativeId,jdbcType=BIGINT},
      button_copy_id = #{buttonCopyId,jdbcType=INTEGER},
      jump_url = #{jumpUrl,jdbcType=VARCHAR},
      customized_url = #{customizedUrl,jdbcType=VARCHAR},
      button_type = #{buttonType,jdbcType=TINYINT},
      extend_url = #{extendUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      image_id = #{imageId,jdbcType=INTEGER},
      seq = #{seq,jdbcType=INTEGER},
      jump_type = #{jumpType,jdbcType=TINYINT},
      scheme_url = #{schemeUrl,jdbcType=VARCHAR},
      button_name = #{buttonName,jdbcType=VARCHAR},
      category = #{category,jdbcType=INTEGER},
      bound_id = #{boundId,jdbcType=BIGINT},
      game_base_id = #{gameBaseId,jdbcType=INTEGER},
      trackadf = #{trackadf,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_button_copy (creative_id, button_copy_id, jump_url, 
      customized_url, button_type, extend_url, 
      is_deleted, ctime, mtime, 
      image_id, seq, jump_type, 
      scheme_url, button_name, category, 
      bound_id, game_base_id, trackadf
      )
    values (#{creativeId,jdbcType=BIGINT}, #{buttonCopyId,jdbcType=INTEGER}, #{jumpUrl,jdbcType=VARCHAR}, 
      #{customizedUrl,jdbcType=VARCHAR}, #{buttonType,jdbcType=TINYINT}, #{extendUrl,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{imageId,jdbcType=INTEGER}, #{seq,jdbcType=INTEGER}, #{jumpType,jdbcType=TINYINT}, 
      #{schemeUrl,jdbcType=VARCHAR}, #{buttonName,jdbcType=VARCHAR}, #{category,jdbcType=INTEGER}, 
      #{boundId,jdbcType=BIGINT}, #{gameBaseId,jdbcType=INTEGER}, #{trackadf,jdbcType=VARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      button_copy_id = values(button_copy_id),
      jump_url = values(jump_url),
      customized_url = values(customized_url),
      button_type = values(button_type),
      extend_url = values(extend_url),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      image_id = values(image_id),
      seq = values(seq),
      jump_type = values(jump_type),
      scheme_url = values(scheme_url),
      button_name = values(button_name),
      category = values(category),
      bound_id = values(bound_id),
      game_base_id = values(game_base_id),
      trackadf = values(trackadf),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_creative_button_copy
      (creative_id,button_copy_id,jump_url,customized_url,button_type,extend_url,is_deleted,ctime,mtime,image_id,seq,jump_type,scheme_url,button_name,category,bound_id,game_base_id,trackadf)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.buttonCopyId,jdbcType=INTEGER},
        #{item.jumpUrl,jdbcType=VARCHAR},
        #{item.customizedUrl,jdbcType=VARCHAR},
        #{item.buttonType,jdbcType=TINYINT},
        #{item.extendUrl,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.imageId,jdbcType=INTEGER},
        #{item.seq,jdbcType=INTEGER},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.buttonName,jdbcType=VARCHAR},
        #{item.category,jdbcType=INTEGER},
        #{item.boundId,jdbcType=BIGINT},
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.trackadf,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_creative_button_copy
      (creative_id,button_copy_id,jump_url,customized_url,button_type,extend_url,is_deleted,ctime,mtime,image_id,seq,jump_type,scheme_url,button_name,category,bound_id,game_base_id,trackadf)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=BIGINT},
        #{item.buttonCopyId,jdbcType=INTEGER},
        #{item.jumpUrl,jdbcType=VARCHAR},
        #{item.customizedUrl,jdbcType=VARCHAR},
        #{item.buttonType,jdbcType=TINYINT},
        #{item.extendUrl,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.imageId,jdbcType=INTEGER},
        #{item.seq,jdbcType=INTEGER},
        #{item.jumpType,jdbcType=TINYINT},
        #{item.schemeUrl,jdbcType=VARCHAR},
        #{item.buttonName,jdbcType=VARCHAR},
        #{item.category,jdbcType=INTEGER},
        #{item.boundId,jdbcType=BIGINT},
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.trackadf,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      button_copy_id = values(button_copy_id),
      jump_url = values(jump_url),
      customized_url = values(customized_url),
      button_type = values(button_type),
      extend_url = values(extend_url),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      image_id = values(image_id),
      seq = values(seq),
      jump_type = values(jump_type),
      scheme_url = values(scheme_url),
      button_name = values(button_name),
      category = values(category),
      bound_id = values(bound_id),
      game_base_id = values(game_base_id),
      trackadf = values(trackadf),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_creative_button_copy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="buttonCopyId != null">
        button_copy_id,
      </if>
      <if test="jumpUrl != null">
        jump_url,
      </if>
      <if test="customizedUrl != null">
        customized_url,
      </if>
      <if test="buttonType != null">
        button_type,
      </if>
      <if test="extendUrl != null">
        extend_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="seq != null">
        seq,
      </if>
      <if test="jumpType != null">
        jump_type,
      </if>
      <if test="schemeUrl != null">
        scheme_url,
      </if>
      <if test="buttonName != null">
        button_name,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="boundId != null">
        bound_id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="trackadf != null">
        trackadf,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="buttonCopyId != null">
        #{buttonCopyId,jdbcType=INTEGER},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedUrl != null">
        #{customizedUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonType != null">
        #{buttonType,jdbcType=TINYINT},
      </if>
      <if test="extendUrl != null">
        #{extendUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=INTEGER},
      </if>
      <if test="seq != null">
        #{seq,jdbcType=INTEGER},
      </if>
      <if test="jumpType != null">
        #{jumpType,jdbcType=TINYINT},
      </if>
      <if test="schemeUrl != null">
        #{schemeUrl,jdbcType=VARCHAR},
      </if>
      <if test="buttonName != null">
        #{buttonName,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=INTEGER},
      </if>
      <if test="boundId != null">
        #{boundId,jdbcType=BIGINT},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="trackadf != null">
        #{trackadf,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="buttonCopyId != null">
        button_copy_id = values(button_copy_id),
      </if>
      <if test="jumpUrl != null">
        jump_url = values(jump_url),
      </if>
      <if test="customizedUrl != null">
        customized_url = values(customized_url),
      </if>
      <if test="buttonType != null">
        button_type = values(button_type),
      </if>
      <if test="extendUrl != null">
        extend_url = values(extend_url),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="imageId != null">
        image_id = values(image_id),
      </if>
      <if test="seq != null">
        seq = values(seq),
      </if>
      <if test="jumpType != null">
        jump_type = values(jump_type),
      </if>
      <if test="schemeUrl != null">
        scheme_url = values(scheme_url),
      </if>
      <if test="buttonName != null">
        button_name = values(button_name),
      </if>
      <if test="category != null">
        category = values(category),
      </if>
      <if test="boundId != null">
        bound_id = values(bound_id),
      </if>
      <if test="gameBaseId != null">
        game_base_id = values(game_base_id),
      </if>
      <if test="trackadf != null">
        trackadf = values(trackadf),
      </if>
    </trim>
  </insert>
</mapper>