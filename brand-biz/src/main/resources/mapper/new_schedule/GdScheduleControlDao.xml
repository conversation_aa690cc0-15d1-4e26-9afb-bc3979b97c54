<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.GdScheduleControlDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="schedule_id" jdbcType="INTEGER" property="scheduleId" />
    <result column="speed_ratio" jdbcType="REAL" property="speedRatio" />
    <result column="weight_ratio" jdbcType="REAL" property="weightRatio" />
    <result column="expect_completion_ratio" jdbcType="REAL" property="expectCompletionRatio" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, schedule_id, speed_ratio, weight_ratio, expect_completion_ratio, is_deleted, 
    ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_schedule_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_schedule_control
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gd_schedule_control
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPoExample">
    delete from gd_schedule_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_control (schedule_id, speed_ratio, weight_ratio, 
      expect_completion_ratio, is_deleted, ctime, 
      mtime)
    values (#{scheduleId,jdbcType=INTEGER}, #{speedRatio,jdbcType=REAL}, #{weightRatio,jdbcType=REAL}, 
      #{expectCompletionRatio,jdbcType=REAL}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_control
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="speedRatio != null">
        speed_ratio,
      </if>
      <if test="weightRatio != null">
        weight_ratio,
      </if>
      <if test="expectCompletionRatio != null">
        expect_completion_ratio,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="speedRatio != null">
        #{speedRatio,jdbcType=REAL},
      </if>
      <if test="weightRatio != null">
        #{weightRatio,jdbcType=REAL},
      </if>
      <if test="expectCompletionRatio != null">
        #{expectCompletionRatio,jdbcType=REAL},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPoExample" resultType="java.lang.Long">
    select count(*) from gd_schedule_control
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_schedule_control
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.scheduleId != null">
        schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.speedRatio != null">
        speed_ratio = #{record.speedRatio,jdbcType=REAL},
      </if>
      <if test="record.weightRatio != null">
        weight_ratio = #{record.weightRatio,jdbcType=REAL},
      </if>
      <if test="record.expectCompletionRatio != null">
        expect_completion_ratio = #{record.expectCompletionRatio,jdbcType=REAL},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_schedule_control
    set id = #{record.id,jdbcType=BIGINT},
      schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      speed_ratio = #{record.speedRatio,jdbcType=REAL},
      weight_ratio = #{record.weightRatio,jdbcType=REAL},
      expect_completion_ratio = #{record.expectCompletionRatio,jdbcType=REAL},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    update gd_schedule_control
    <set>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="speedRatio != null">
        speed_ratio = #{speedRatio,jdbcType=REAL},
      </if>
      <if test="weightRatio != null">
        weight_ratio = #{weightRatio,jdbcType=REAL},
      </if>
      <if test="expectCompletionRatio != null">
        expect_completion_ratio = #{expectCompletionRatio,jdbcType=REAL},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    update gd_schedule_control
    set schedule_id = #{scheduleId,jdbcType=INTEGER},
      speed_ratio = #{speedRatio,jdbcType=REAL},
      weight_ratio = #{weightRatio,jdbcType=REAL},
      expect_completion_ratio = #{expectCompletionRatio,jdbcType=REAL},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_control (schedule_id, speed_ratio, weight_ratio, 
      expect_completion_ratio, is_deleted, ctime, 
      mtime)
    values (#{scheduleId,jdbcType=INTEGER}, #{speedRatio,jdbcType=REAL}, #{weightRatio,jdbcType=REAL}, 
      #{expectCompletionRatio,jdbcType=REAL}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      schedule_id = values(schedule_id),
      speed_ratio = values(speed_ratio),
      weight_ratio = values(weight_ratio),
      expect_completion_ratio = values(expect_completion_ratio),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_control
      (schedule_id,speed_ratio,weight_ratio,expect_completion_ratio,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.speedRatio,jdbcType=REAL},
        #{item.weightRatio,jdbcType=REAL},
        #{item.expectCompletionRatio,jdbcType=REAL},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_control
      (schedule_id,speed_ratio,weight_ratio,expect_completion_ratio,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.speedRatio,jdbcType=REAL},
        #{item.weightRatio,jdbcType=REAL},
        #{item.expectCompletionRatio,jdbcType=REAL},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      schedule_id = values(schedule_id),
      speed_ratio = values(speed_ratio),
      weight_ratio = values(weight_ratio),
      expect_completion_ratio = values(expect_completion_ratio),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleControlPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_control
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="speedRatio != null">
        speed_ratio,
      </if>
      <if test="weightRatio != null">
        weight_ratio,
      </if>
      <if test="expectCompletionRatio != null">
        expect_completion_ratio,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="speedRatio != null">
        #{speedRatio,jdbcType=REAL},
      </if>
      <if test="weightRatio != null">
        #{weightRatio,jdbcType=REAL},
      </if>
      <if test="expectCompletionRatio != null">
        #{expectCompletionRatio,jdbcType=REAL},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="scheduleId != null">
        schedule_id = values(schedule_id),
      </if>
      <if test="speedRatio != null">
        speed_ratio = values(speed_ratio),
      </if>
      <if test="weightRatio != null">
        weight_ratio = values(weight_ratio),
      </if>
      <if test="expectCompletionRatio != null">
        expect_completion_ratio = values(expect_completion_ratio),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>