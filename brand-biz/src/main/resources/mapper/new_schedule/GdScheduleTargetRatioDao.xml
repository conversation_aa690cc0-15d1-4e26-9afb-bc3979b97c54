<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.GdScheduleTargetRatioDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="target_key_id" jdbcType="INTEGER" property="targetKeyId" />
    <result column="ratio" jdbcType="INTEGER" property="ratio" />
    <result column="launch_day" jdbcType="TIMESTAMP" property="launchDay" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="schedule_id" jdbcType="INTEGER" property="scheduleId" />
    <result column="inner_target_key_id" jdbcType="INTEGER" property="innerTargetKeyId" />
    <result column="show_count" jdbcType="INTEGER" property="showCount" />
    <result column="is_gd_plus" jdbcType="TINYINT" property="isGdPlus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, plan_id, source, target_key_id, ratio, launch_day, is_deleted, ctime, mtime, 
    schedule_id, inner_target_key_id, show_count, is_gd_plus
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_schedule_target_ratio
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_schedule_target_ratio
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_schedule_target_ratio
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPoExample">
    delete from gd_schedule_target_ratio
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_target_ratio (plan_id, source, target_key_id, 
      ratio, launch_day, is_deleted, 
      ctime, mtime, schedule_id, 
      inner_target_key_id, show_count, is_gd_plus
      )
    values (#{planId,jdbcType=INTEGER}, #{source,jdbcType=INTEGER}, #{targetKeyId,jdbcType=INTEGER}, 
      #{ratio,jdbcType=INTEGER}, #{launchDay,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{scheduleId,jdbcType=INTEGER}, 
      #{innerTargetKeyId,jdbcType=INTEGER}, #{showCount,jdbcType=INTEGER}, #{isGdPlus,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_target_ratio
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        plan_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="targetKeyId != null">
        target_key_id,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="launchDay != null">
        launch_day,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="innerTargetKeyId != null">
        inner_target_key_id,
      </if>
      <if test="showCount != null">
        show_count,
      </if>
      <if test="isGdPlus != null">
        is_gd_plus,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        #{planId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="targetKeyId != null">
        #{targetKeyId,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=INTEGER},
      </if>
      <if test="launchDay != null">
        #{launchDay,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="innerTargetKeyId != null">
        #{innerTargetKeyId,jdbcType=INTEGER},
      </if>
      <if test="showCount != null">
        #{showCount,jdbcType=INTEGER},
      </if>
      <if test="isGdPlus != null">
        #{isGdPlus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPoExample" resultType="java.lang.Long">
    select count(*) from gd_schedule_target_ratio
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_schedule_target_ratio
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.planId != null">
        plan_id = #{record.planId,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.targetKeyId != null">
        target_key_id = #{record.targetKeyId,jdbcType=INTEGER},
      </if>
      <if test="record.ratio != null">
        ratio = #{record.ratio,jdbcType=INTEGER},
      </if>
      <if test="record.launchDay != null">
        launch_day = #{record.launchDay,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scheduleId != null">
        schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.innerTargetKeyId != null">
        inner_target_key_id = #{record.innerTargetKeyId,jdbcType=INTEGER},
      </if>
      <if test="record.showCount != null">
        show_count = #{record.showCount,jdbcType=INTEGER},
      </if>
      <if test="record.isGdPlus != null">
        is_gd_plus = #{record.isGdPlus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_schedule_target_ratio
    set id = #{record.id,jdbcType=INTEGER},
      plan_id = #{record.planId,jdbcType=INTEGER},
      source = #{record.source,jdbcType=INTEGER},
      target_key_id = #{record.targetKeyId,jdbcType=INTEGER},
      ratio = #{record.ratio,jdbcType=INTEGER},
      launch_day = #{record.launchDay,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      inner_target_key_id = #{record.innerTargetKeyId,jdbcType=INTEGER},
      show_count = #{record.showCount,jdbcType=INTEGER},
      is_gd_plus = #{record.isGdPlus,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    update gd_schedule_target_ratio
    <set>
      <if test="planId != null">
        plan_id = #{planId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="targetKeyId != null">
        target_key_id = #{targetKeyId,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        ratio = #{ratio,jdbcType=INTEGER},
      </if>
      <if test="launchDay != null">
        launch_day = #{launchDay,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="innerTargetKeyId != null">
        inner_target_key_id = #{innerTargetKeyId,jdbcType=INTEGER},
      </if>
      <if test="showCount != null">
        show_count = #{showCount,jdbcType=INTEGER},
      </if>
      <if test="isGdPlus != null">
        is_gd_plus = #{isGdPlus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    update gd_schedule_target_ratio
    set plan_id = #{planId,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      target_key_id = #{targetKeyId,jdbcType=INTEGER},
      ratio = #{ratio,jdbcType=INTEGER},
      launch_day = #{launchDay,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      schedule_id = #{scheduleId,jdbcType=INTEGER},
      inner_target_key_id = #{innerTargetKeyId,jdbcType=INTEGER},
      show_count = #{showCount,jdbcType=INTEGER},
      is_gd_plus = #{isGdPlus,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_target_ratio (plan_id, source, target_key_id, 
      ratio, launch_day, is_deleted, 
      ctime, mtime, schedule_id, 
      inner_target_key_id, show_count, is_gd_plus
      )
    values (#{planId,jdbcType=INTEGER}, #{source,jdbcType=INTEGER}, #{targetKeyId,jdbcType=INTEGER}, 
      #{ratio,jdbcType=INTEGER}, #{launchDay,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{scheduleId,jdbcType=INTEGER}, 
      #{innerTargetKeyId,jdbcType=INTEGER}, #{showCount,jdbcType=INTEGER}, #{isGdPlus,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      plan_id = values(plan_id),
      source = values(source),
      target_key_id = values(target_key_id),
      ratio = values(ratio),
      launch_day = values(launch_day),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      schedule_id = values(schedule_id),
      inner_target_key_id = values(inner_target_key_id),
      show_count = values(show_count),
      is_gd_plus = values(is_gd_plus),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_target_ratio
      (plan_id,source,target_key_id,ratio,launch_day,is_deleted,ctime,mtime,schedule_id,inner_target_key_id,show_count,is_gd_plus)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.planId,jdbcType=INTEGER},
        #{item.source,jdbcType=INTEGER},
        #{item.targetKeyId,jdbcType=INTEGER},
        #{item.ratio,jdbcType=INTEGER},
        #{item.launchDay,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.innerTargetKeyId,jdbcType=INTEGER},
        #{item.showCount,jdbcType=INTEGER},
        #{item.isGdPlus,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_target_ratio
      (plan_id,source,target_key_id,ratio,launch_day,is_deleted,ctime,mtime,schedule_id,inner_target_key_id,show_count,is_gd_plus)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.planId,jdbcType=INTEGER},
        #{item.source,jdbcType=INTEGER},
        #{item.targetKeyId,jdbcType=INTEGER},
        #{item.ratio,jdbcType=INTEGER},
        #{item.launchDay,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.innerTargetKeyId,jdbcType=INTEGER},
        #{item.showCount,jdbcType=INTEGER},
        #{item.isGdPlus,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      plan_id = values(plan_id),
      source = values(source),
      target_key_id = values(target_key_id),
      ratio = values(ratio),
      launch_day = values(launch_day),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      schedule_id = values(schedule_id),
      inner_target_key_id = values(inner_target_key_id),
      show_count = values(show_count),
      is_gd_plus = values(is_gd_plus),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_schedule_target_ratio
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        plan_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="targetKeyId != null">
        target_key_id,
      </if>
      <if test="ratio != null">
        ratio,
      </if>
      <if test="launchDay != null">
        launch_day,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="innerTargetKeyId != null">
        inner_target_key_id,
      </if>
      <if test="showCount != null">
        show_count,
      </if>
      <if test="isGdPlus != null">
        is_gd_plus,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="planId != null">
        #{planId,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="targetKeyId != null">
        #{targetKeyId,jdbcType=INTEGER},
      </if>
      <if test="ratio != null">
        #{ratio,jdbcType=INTEGER},
      </if>
      <if test="launchDay != null">
        #{launchDay,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="innerTargetKeyId != null">
        #{innerTargetKeyId,jdbcType=INTEGER},
      </if>
      <if test="showCount != null">
        #{showCount,jdbcType=INTEGER},
      </if>
      <if test="isGdPlus != null">
        #{isGdPlus,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="planId != null">
        plan_id = values(plan_id),
      </if>
      <if test="source != null">
        source = values(source),
      </if>
      <if test="targetKeyId != null">
        target_key_id = values(target_key_id),
      </if>
      <if test="ratio != null">
        ratio = values(ratio),
      </if>
      <if test="launchDay != null">
        launch_day = values(launch_day),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="scheduleId != null">
        schedule_id = values(schedule_id),
      </if>
      <if test="innerTargetKeyId != null">
        inner_target_key_id = values(inner_target_key_id),
      </if>
      <if test="showCount != null">
        show_count = values(show_count),
      </if>
      <if test="isGdPlus != null">
        is_gd_plus = values(is_gd_plus),
      </if>
    </trim>
  </insert>
</mapper>