<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.GdScheduleTargetDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    <id column="schedule_target_id" jdbcType="INTEGER" property="scheduleTargetId" />
    <result column="schedule_id" jdbcType="INTEGER" property="scheduleId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="target_type" jdbcType="INTEGER" property="targetType" />
    <result column="target_item_ids" jdbcType="VARCHAR" property="targetItemIds" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    schedule_target_id, schedule_id, campaign_id, target_type, target_item_ids, ctime, 
    mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_schedule_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_schedule_target
    where schedule_target_id = #{scheduleTargetId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_schedule_target
    where schedule_target_id = #{scheduleTargetId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPoExample">
    delete from gd_schedule_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    insert into gd_schedule_target (schedule_target_id, schedule_id, campaign_id, 
      target_type, target_item_ids, ctime, 
      mtime, is_deleted)
    values (#{scheduleTargetId,jdbcType=INTEGER}, #{scheduleId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, 
      #{targetType,jdbcType=INTEGER}, #{targetItemIds,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    insert into gd_schedule_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleTargetId != null">
        schedule_target_id,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetItemIds != null">
        target_item_ids,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleTargetId != null">
        #{scheduleTargetId,jdbcType=INTEGER},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetItemIds != null">
        #{targetItemIds,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPoExample" resultType="java.lang.Long">
    select count(*) from gd_schedule_target
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_schedule_target
    <set>
      <if test="record.scheduleTargetId != null">
        schedule_target_id = #{record.scheduleTargetId,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleId != null">
        schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.targetType != null">
        target_type = #{record.targetType,jdbcType=INTEGER},
      </if>
      <if test="record.targetItemIds != null">
        target_item_ids = #{record.targetItemIds,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_schedule_target
    set schedule_target_id = #{record.scheduleTargetId,jdbcType=INTEGER},
      schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      target_type = #{record.targetType,jdbcType=INTEGER},
      target_item_ids = #{record.targetItemIds,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    update gd_schedule_target
    <set>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        target_type = #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetItemIds != null">
        target_item_ids = #{targetItemIds,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where schedule_target_id = #{scheduleTargetId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    update gd_schedule_target
    set schedule_id = #{scheduleId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      target_type = #{targetType,jdbcType=INTEGER},
      target_item_ids = #{targetItemIds,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where schedule_target_id = #{scheduleTargetId,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    insert into gd_schedule_target (schedule_target_id, schedule_id, campaign_id, 
      target_type, target_item_ids, ctime, 
      mtime, is_deleted)
    values (#{scheduleTargetId,jdbcType=INTEGER}, #{scheduleId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, 
      #{targetType,jdbcType=INTEGER}, #{targetItemIds,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      schedule_id = values(schedule_id),
      campaign_id = values(campaign_id),
      target_type = values(target_type),
      target_item_ids = values(target_item_ids),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_target
      (schedule_id,campaign_id,target_type,target_item_ids,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.targetType,jdbcType=INTEGER},
        #{item.targetItemIds,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_schedule_target
      (schedule_id,campaign_id,target_type,target_item_ids,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.scheduleId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.targetType,jdbcType=INTEGER},
        #{item.targetItemIds,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      schedule_id = values(schedule_id),
      campaign_id = values(campaign_id),
      target_type = values(target_type),
      target_item_ids = values(target_item_ids),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo">
    insert into gd_schedule_target
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleTargetId != null">
        schedule_target_id,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="targetType != null">
        target_type,
      </if>
      <if test="targetItemIds != null">
        target_item_ids,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleTargetId != null">
        #{scheduleTargetId,jdbcType=INTEGER},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="targetType != null">
        #{targetType,jdbcType=INTEGER},
      </if>
      <if test="targetItemIds != null">
        #{targetItemIds,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="scheduleId != null">
        schedule_id = values(schedule_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="targetType != null">
        target_type = values(target_type),
      </if>
      <if test="targetItemIds != null">
        target_item_ids = values(target_item_ids),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>