<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.OgvPriceDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cycle_id" jdbcType="INTEGER" property="cycleId" />
    <result column="res_level" jdbcType="VARCHAR" property="resLevel" />
    <result column="season_type" jdbcType="INTEGER" property="seasonType" />
    <result column="platform_id" jdbcType="INTEGER" property="platformId" />
    <result column="sticker_id" jdbcType="INTEGER" property="stickerId" />
    <result column="source_id" jdbcType="INTEGER" property="sourceId" />
    <result column="external_price" jdbcType="INTEGER" property="externalPrice" />
    <result column="internal_price" jdbcType="INTEGER" property="internalPrice" />
    <result column="internal_cpm_price" jdbcType="INTEGER" property="internalCpmPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="order_product" jdbcType="INTEGER" property="orderProduct" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="raise_info" jdbcType="VARCHAR" property="raiseInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, cycle_id, res_level, season_type, platform_id, sticker_id, source_id, external_price, 
    internal_price, internal_cpm_price, status, order_product, is_deleted, ctime, mtime, 
    raise_info
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ogv_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ogv_price
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ogv_price
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePoExample">
    delete from ogv_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_price (cycle_id, res_level, season_type, 
      platform_id, sticker_id, source_id, 
      external_price, internal_price, internal_cpm_price, 
      status, order_product, is_deleted, 
      ctime, mtime, raise_info
      )
    values (#{cycleId,jdbcType=INTEGER}, #{resLevel,jdbcType=VARCHAR}, #{seasonType,jdbcType=INTEGER}, 
      #{platformId,jdbcType=INTEGER}, #{stickerId,jdbcType=INTEGER}, #{sourceId,jdbcType=INTEGER}, 
      #{externalPrice,jdbcType=INTEGER}, #{internalPrice,jdbcType=INTEGER}, #{internalCpmPrice,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{orderProduct,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{raiseInfo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cycleId != null">
        cycle_id,
      </if>
      <if test="resLevel != null">
        res_level,
      </if>
      <if test="seasonType != null">
        season_type,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="stickerId != null">
        sticker_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="externalPrice != null">
        external_price,
      </if>
      <if test="internalPrice != null">
        internal_price,
      </if>
      <if test="internalCpmPrice != null">
        internal_cpm_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orderProduct != null">
        order_product,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="raiseInfo != null">
        raise_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cycleId != null">
        #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="resLevel != null">
        #{resLevel,jdbcType=VARCHAR},
      </if>
      <if test="seasonType != null">
        #{seasonType,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=INTEGER},
      </if>
      <if test="stickerId != null">
        #{stickerId,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="externalPrice != null">
        #{externalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalPrice != null">
        #{internalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalCpmPrice != null">
        #{internalCpmPrice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="orderProduct != null">
        #{orderProduct,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="raiseInfo != null">
        #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePoExample" resultType="java.lang.Long">
    select count(*) from ogv_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ogv_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cycleId != null">
        cycle_id = #{record.cycleId,jdbcType=INTEGER},
      </if>
      <if test="record.resLevel != null">
        res_level = #{record.resLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.seasonType != null">
        season_type = #{record.seasonType,jdbcType=INTEGER},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=INTEGER},
      </if>
      <if test="record.stickerId != null">
        sticker_id = #{record.stickerId,jdbcType=INTEGER},
      </if>
      <if test="record.sourceId != null">
        source_id = #{record.sourceId,jdbcType=INTEGER},
      </if>
      <if test="record.externalPrice != null">
        external_price = #{record.externalPrice,jdbcType=INTEGER},
      </if>
      <if test="record.internalPrice != null">
        internal_price = #{record.internalPrice,jdbcType=INTEGER},
      </if>
      <if test="record.internalCpmPrice != null">
        internal_cpm_price = #{record.internalCpmPrice,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.orderProduct != null">
        order_product = #{record.orderProduct,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.raiseInfo != null">
        raise_info = #{record.raiseInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ogv_price
    set id = #{record.id,jdbcType=BIGINT},
      cycle_id = #{record.cycleId,jdbcType=INTEGER},
      res_level = #{record.resLevel,jdbcType=VARCHAR},
      season_type = #{record.seasonType,jdbcType=INTEGER},
      platform_id = #{record.platformId,jdbcType=INTEGER},
      sticker_id = #{record.stickerId,jdbcType=INTEGER},
      source_id = #{record.sourceId,jdbcType=INTEGER},
      external_price = #{record.externalPrice,jdbcType=INTEGER},
      internal_price = #{record.internalPrice,jdbcType=INTEGER},
      internal_cpm_price = #{record.internalCpmPrice,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      order_product = #{record.orderProduct,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      raise_info = #{record.raiseInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    update ogv_price
    <set>
      <if test="cycleId != null">
        cycle_id = #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="resLevel != null">
        res_level = #{resLevel,jdbcType=VARCHAR},
      </if>
      <if test="seasonType != null">
        season_type = #{seasonType,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=INTEGER},
      </if>
      <if test="stickerId != null">
        sticker_id = #{stickerId,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="externalPrice != null">
        external_price = #{externalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalPrice != null">
        internal_price = #{internalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalCpmPrice != null">
        internal_cpm_price = #{internalCpmPrice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="orderProduct != null">
        order_product = #{orderProduct,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="raiseInfo != null">
        raise_info = #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    update ogv_price
    set cycle_id = #{cycleId,jdbcType=INTEGER},
      res_level = #{resLevel,jdbcType=VARCHAR},
      season_type = #{seasonType,jdbcType=INTEGER},
      platform_id = #{platformId,jdbcType=INTEGER},
      sticker_id = #{stickerId,jdbcType=INTEGER},
      source_id = #{sourceId,jdbcType=INTEGER},
      external_price = #{externalPrice,jdbcType=INTEGER},
      internal_price = #{internalPrice,jdbcType=INTEGER},
      internal_cpm_price = #{internalCpmPrice,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      order_product = #{orderProduct,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      raise_info = #{raiseInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_price (cycle_id, res_level, season_type, 
      platform_id, sticker_id, source_id, 
      external_price, internal_price, internal_cpm_price, 
      status, order_product, is_deleted, 
      ctime, mtime, raise_info
      )
    values (#{cycleId,jdbcType=INTEGER}, #{resLevel,jdbcType=VARCHAR}, #{seasonType,jdbcType=INTEGER}, 
      #{platformId,jdbcType=INTEGER}, #{stickerId,jdbcType=INTEGER}, #{sourceId,jdbcType=INTEGER}, 
      #{externalPrice,jdbcType=INTEGER}, #{internalPrice,jdbcType=INTEGER}, #{internalCpmPrice,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{orderProduct,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{raiseInfo,jdbcType=VARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      cycle_id = values(cycle_id),
      res_level = values(res_level),
      season_type = values(season_type),
      platform_id = values(platform_id),
      sticker_id = values(sticker_id),
      source_id = values(source_id),
      external_price = values(external_price),
      internal_price = values(internal_price),
      internal_cpm_price = values(internal_cpm_price),
      status = values(status),
      order_product = values(order_product),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      raise_info = values(raise_info),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ogv_price
      (cycle_id,res_level,season_type,platform_id,sticker_id,source_id,external_price,internal_price,internal_cpm_price,status,order_product,is_deleted,ctime,mtime,raise_info)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cycleId,jdbcType=INTEGER},
        #{item.resLevel,jdbcType=VARCHAR},
        #{item.seasonType,jdbcType=INTEGER},
        #{item.platformId,jdbcType=INTEGER},
        #{item.stickerId,jdbcType=INTEGER},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.externalPrice,jdbcType=INTEGER},
        #{item.internalPrice,jdbcType=INTEGER},
        #{item.internalCpmPrice,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.orderProduct,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.raiseInfo,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ogv_price
      (cycle_id,res_level,season_type,platform_id,sticker_id,source_id,external_price,internal_price,internal_cpm_price,status,order_product,is_deleted,ctime,mtime,raise_info)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cycleId,jdbcType=INTEGER},
        #{item.resLevel,jdbcType=VARCHAR},
        #{item.seasonType,jdbcType=INTEGER},
        #{item.platformId,jdbcType=INTEGER},
        #{item.stickerId,jdbcType=INTEGER},
        #{item.sourceId,jdbcType=INTEGER},
        #{item.externalPrice,jdbcType=INTEGER},
        #{item.internalPrice,jdbcType=INTEGER},
        #{item.internalCpmPrice,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.orderProduct,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.raiseInfo,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      cycle_id = values(cycle_id),
      res_level = values(res_level),
      season_type = values(season_type),
      platform_id = values(platform_id),
      sticker_id = values(sticker_id),
      source_id = values(source_id),
      external_price = values(external_price),
      internal_price = values(internal_price),
      internal_cpm_price = values(internal_cpm_price),
      status = values(status),
      order_product = values(order_product),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      raise_info = values(raise_info),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.OgvPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ogv_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cycleId != null">
        cycle_id,
      </if>
      <if test="resLevel != null">
        res_level,
      </if>
      <if test="seasonType != null">
        season_type,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="stickerId != null">
        sticker_id,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="externalPrice != null">
        external_price,
      </if>
      <if test="internalPrice != null">
        internal_price,
      </if>
      <if test="internalCpmPrice != null">
        internal_cpm_price,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orderProduct != null">
        order_product,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="raiseInfo != null">
        raise_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cycleId != null">
        #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="resLevel != null">
        #{resLevel,jdbcType=VARCHAR},
      </if>
      <if test="seasonType != null">
        #{seasonType,jdbcType=INTEGER},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=INTEGER},
      </if>
      <if test="stickerId != null">
        #{stickerId,jdbcType=INTEGER},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=INTEGER},
      </if>
      <if test="externalPrice != null">
        #{externalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalPrice != null">
        #{internalPrice,jdbcType=INTEGER},
      </if>
      <if test="internalCpmPrice != null">
        #{internalCpmPrice,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="orderProduct != null">
        #{orderProduct,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="raiseInfo != null">
        #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="cycleId != null">
        cycle_id = values(cycle_id),
      </if>
      <if test="resLevel != null">
        res_level = values(res_level),
      </if>
      <if test="seasonType != null">
        season_type = values(season_type),
      </if>
      <if test="platformId != null">
        platform_id = values(platform_id),
      </if>
      <if test="stickerId != null">
        sticker_id = values(sticker_id),
      </if>
      <if test="sourceId != null">
        source_id = values(source_id),
      </if>
      <if test="externalPrice != null">
        external_price = values(external_price),
      </if>
      <if test="internalPrice != null">
        internal_price = values(internal_price),
      </if>
      <if test="internalCpmPrice != null">
        internal_cpm_price = values(internal_cpm_price),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="orderProduct != null">
        order_product = values(order_product),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="raiseInfo != null">
        raise_info = values(raise_info),
      </if>
    </trim>
  </insert>
</mapper>