<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.LauAccountCorpMidMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="mid" jdbcType="BIGINT" property="mid" />
    <result column="corp_name" jdbcType="VARCHAR" property="corpName" />
    <result column="corp_face" jdbcType="VARCHAR" property="corpFace" />
    <result column="space_url" jdbcType="VARCHAR" property="spaceUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, is_deleted, account_id, mid, corp_name, corp_face, space_url
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_account_corp_mid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_account_corp_mid_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_account_corp_mid_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPoExample">
    delete from lau_account_corp_mid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_account_corp_mid_mapping (ctime, mtime, is_deleted, 
      account_id, mid, corp_name, 
      corp_face, space_url)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT}, #{corpName,jdbcType=VARCHAR}, 
      #{corpFace,jdbcType=VARCHAR}, #{spaceUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_account_corp_mid_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="corpName != null">
        corp_name,
      </if>
      <if test="corpFace != null">
        corp_face,
      </if>
      <if test="spaceUrl != null">
        space_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="corpName != null">
        #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="corpFace != null">
        #{corpFace,jdbcType=VARCHAR},
      </if>
      <if test="spaceUrl != null">
        #{spaceUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPoExample" resultType="java.lang.Long">
    select count(*) from lau_account_corp_mid_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_account_corp_mid_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.mid != null">
        mid = #{record.mid,jdbcType=BIGINT},
      </if>
      <if test="record.corpName != null">
        corp_name = #{record.corpName,jdbcType=VARCHAR},
      </if>
      <if test="record.corpFace != null">
        corp_face = #{record.corpFace,jdbcType=VARCHAR},
      </if>
      <if test="record.spaceUrl != null">
        space_url = #{record.spaceUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_account_corp_mid_mapping
    set id = #{record.id,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      mid = #{record.mid,jdbcType=BIGINT},
      corp_name = #{record.corpName,jdbcType=VARCHAR},
      corp_face = #{record.corpFace,jdbcType=VARCHAR},
      space_url = #{record.spaceUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    update lau_account_corp_mid_mapping
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        mid = #{mid,jdbcType=BIGINT},
      </if>
      <if test="corpName != null">
        corp_name = #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="corpFace != null">
        corp_face = #{corpFace,jdbcType=VARCHAR},
      </if>
      <if test="spaceUrl != null">
        space_url = #{spaceUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    update lau_account_corp_mid_mapping
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      account_id = #{accountId,jdbcType=INTEGER},
      mid = #{mid,jdbcType=BIGINT},
      corp_name = #{corpName,jdbcType=VARCHAR},
      corp_face = #{corpFace,jdbcType=VARCHAR},
      space_url = #{spaceUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_account_corp_mid_mapping (ctime, mtime, is_deleted, 
      account_id, mid, corp_name, 
      corp_face, space_url)
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{accountId,jdbcType=INTEGER}, #{mid,jdbcType=BIGINT}, #{corpName,jdbcType=VARCHAR}, 
      #{corpFace,jdbcType=VARCHAR}, #{spaceUrl,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      mid = values(mid),
      corp_name = values(corp_name),
      corp_face = values(corp_face),
      space_url = values(space_url),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_account_corp_mid_mapping
      (ctime,mtime,is_deleted,account_id,mid,corp_name,corp_face,space_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.corpName,jdbcType=VARCHAR},
        #{item.corpFace,jdbcType=VARCHAR},
        #{item.spaceUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_account_corp_mid_mapping
      (ctime,mtime,is_deleted,account_id,mid,corp_name,corp_face,space_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.mid,jdbcType=BIGINT},
        #{item.corpName,jdbcType=VARCHAR},
        #{item.corpFace,jdbcType=VARCHAR},
        #{item.spaceUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      account_id = values(account_id),
      mid = values(mid),
      corp_name = values(corp_name),
      corp_face = values(corp_face),
      space_url = values(space_url),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_account_corp_mid_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="mid != null">
        mid,
      </if>
      <if test="corpName != null">
        corp_name,
      </if>
      <if test="corpFace != null">
        corp_face,
      </if>
      <if test="spaceUrl != null">
        space_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="mid != null">
        #{mid,jdbcType=BIGINT},
      </if>
      <if test="corpName != null">
        #{corpName,jdbcType=VARCHAR},
      </if>
      <if test="corpFace != null">
        #{corpFace,jdbcType=VARCHAR},
      </if>
      <if test="spaceUrl != null">
        #{spaceUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="mid != null">
        mid = values(mid),
      </if>
      <if test="corpName != null">
        corp_name = values(corp_name),
      </if>
      <if test="corpFace != null">
        corp_face = values(corp_face),
      </if>
      <if test="spaceUrl != null">
        space_url = values(space_url),
      </if>
    </trim>
  </insert>
</mapper>