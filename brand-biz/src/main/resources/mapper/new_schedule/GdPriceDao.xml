<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.GdPriceDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.GdPricePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="inline_sales_type" jdbcType="INTEGER" property="inlineSalesType" />
    <result column="creative_style" jdbcType="TINYINT" property="creativeStyle" />
    <result column="creative_style_name" jdbcType="VARCHAR" property="creativeStyleName" />
    <result column="base_price" jdbcType="INTEGER" property="basePrice" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="gd_sales_type" jdbcType="TINYINT" property="gdSalesType" />
    <result column="next_stage_price" jdbcType="INTEGER" property="nextStagePrice" />
    <result column="cycle_id" jdbcType="INTEGER" property="cycleId" />
    <result column="raise_info" jdbcType="VARCHAR" property="raiseInfo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, template_id, template_name, inline_sales_type, creative_style, creative_style_name, 
    base_price, is_deleted, ctime, mtime, gd_sales_type, next_stage_price, cycle_id, 
    raise_info
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gd_price
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_price
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePoExample">
    delete from gd_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_price (template_id, template_name, inline_sales_type, 
      creative_style, creative_style_name, base_price, 
      is_deleted, ctime, mtime, 
      gd_sales_type, next_stage_price, cycle_id, 
      raise_info)
    values (#{templateId,jdbcType=INTEGER}, #{templateName,jdbcType=VARCHAR}, #{inlineSalesType,jdbcType=INTEGER}, 
      #{creativeStyle,jdbcType=TINYINT}, #{creativeStyleName,jdbcType=VARCHAR}, #{basePrice,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{gdSalesType,jdbcType=TINYINT}, #{nextStagePrice,jdbcType=INTEGER}, #{cycleId,jdbcType=INTEGER}, 
      #{raiseInfo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="inlineSalesType != null">
        inline_sales_type,
      </if>
      <if test="creativeStyle != null">
        creative_style,
      </if>
      <if test="creativeStyleName != null">
        creative_style_name,
      </if>
      <if test="basePrice != null">
        base_price,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="gdSalesType != null">
        gd_sales_type,
      </if>
      <if test="nextStagePrice != null">
        next_stage_price,
      </if>
      <if test="cycleId != null">
        cycle_id,
      </if>
      <if test="raiseInfo != null">
        raise_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="inlineSalesType != null">
        #{inlineSalesType,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="creativeStyleName != null">
        #{creativeStyleName,jdbcType=VARCHAR},
      </if>
      <if test="basePrice != null">
        #{basePrice,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdSalesType != null">
        #{gdSalesType,jdbcType=TINYINT},
      </if>
      <if test="nextStagePrice != null">
        #{nextStagePrice,jdbcType=INTEGER},
      </if>
      <if test="cycleId != null">
        #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="raiseInfo != null">
        #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePoExample" resultType="java.lang.Long">
    select count(*) from gd_price
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_price
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.inlineSalesType != null">
        inline_sales_type = #{record.inlineSalesType,jdbcType=INTEGER},
      </if>
      <if test="record.creativeStyle != null">
        creative_style = #{record.creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="record.creativeStyleName != null">
        creative_style_name = #{record.creativeStyleName,jdbcType=VARCHAR},
      </if>
      <if test="record.basePrice != null">
        base_price = #{record.basePrice,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gdSalesType != null">
        gd_sales_type = #{record.gdSalesType,jdbcType=TINYINT},
      </if>
      <if test="record.nextStagePrice != null">
        next_stage_price = #{record.nextStagePrice,jdbcType=INTEGER},
      </if>
      <if test="record.cycleId != null">
        cycle_id = #{record.cycleId,jdbcType=INTEGER},
      </if>
      <if test="record.raiseInfo != null">
        raise_info = #{record.raiseInfo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_price
    set id = #{record.id,jdbcType=INTEGER},
      template_id = #{record.templateId,jdbcType=INTEGER},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      inline_sales_type = #{record.inlineSalesType,jdbcType=INTEGER},
      creative_style = #{record.creativeStyle,jdbcType=TINYINT},
      creative_style_name = #{record.creativeStyleName,jdbcType=VARCHAR},
      base_price = #{record.basePrice,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      gd_sales_type = #{record.gdSalesType,jdbcType=TINYINT},
      next_stage_price = #{record.nextStagePrice,jdbcType=INTEGER},
      cycle_id = #{record.cycleId,jdbcType=INTEGER},
      raise_info = #{record.raiseInfo,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    update gd_price
    <set>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="inlineSalesType != null">
        inline_sales_type = #{inlineSalesType,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        creative_style = #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="creativeStyleName != null">
        creative_style_name = #{creativeStyleName,jdbcType=VARCHAR},
      </if>
      <if test="basePrice != null">
        base_price = #{basePrice,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdSalesType != null">
        gd_sales_type = #{gdSalesType,jdbcType=TINYINT},
      </if>
      <if test="nextStagePrice != null">
        next_stage_price = #{nextStagePrice,jdbcType=INTEGER},
      </if>
      <if test="cycleId != null">
        cycle_id = #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="raiseInfo != null">
        raise_info = #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    update gd_price
    set template_id = #{templateId,jdbcType=INTEGER},
      template_name = #{templateName,jdbcType=VARCHAR},
      inline_sales_type = #{inlineSalesType,jdbcType=INTEGER},
      creative_style = #{creativeStyle,jdbcType=TINYINT},
      creative_style_name = #{creativeStyleName,jdbcType=VARCHAR},
      base_price = #{basePrice,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      gd_sales_type = #{gdSalesType,jdbcType=TINYINT},
      next_stage_price = #{nextStagePrice,jdbcType=INTEGER},
      cycle_id = #{cycleId,jdbcType=INTEGER},
      raise_info = #{raiseInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_price (template_id, template_name, inline_sales_type, 
      creative_style, creative_style_name, base_price, 
      is_deleted, ctime, mtime, 
      gd_sales_type, next_stage_price, cycle_id, 
      raise_info)
    values (#{templateId,jdbcType=INTEGER}, #{templateName,jdbcType=VARCHAR}, #{inlineSalesType,jdbcType=INTEGER}, 
      #{creativeStyle,jdbcType=TINYINT}, #{creativeStyleName,jdbcType=VARCHAR}, #{basePrice,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{gdSalesType,jdbcType=TINYINT}, #{nextStagePrice,jdbcType=INTEGER}, #{cycleId,jdbcType=INTEGER}, 
      #{raiseInfo,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      template_id = values(template_id),
      template_name = values(template_name),
      inline_sales_type = values(inline_sales_type),
      creative_style = values(creative_style),
      creative_style_name = values(creative_style_name),
      base_price = values(base_price),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      gd_sales_type = values(gd_sales_type),
      next_stage_price = values(next_stage_price),
      cycle_id = values(cycle_id),
      raise_info = values(raise_info),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      gd_price
      (template_id,template_name,inline_sales_type,creative_style,creative_style_name,base_price,is_deleted,ctime,mtime,gd_sales_type,next_stage_price,cycle_id,raise_info)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.templateId,jdbcType=INTEGER},
        #{item.templateName,jdbcType=VARCHAR},
        #{item.inlineSalesType,jdbcType=INTEGER},
        #{item.creativeStyle,jdbcType=TINYINT},
        #{item.creativeStyleName,jdbcType=VARCHAR},
        #{item.basePrice,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.gdSalesType,jdbcType=TINYINT},
        #{item.nextStagePrice,jdbcType=INTEGER},
        #{item.cycleId,jdbcType=INTEGER},
        #{item.raiseInfo,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      gd_price
      (template_id,template_name,inline_sales_type,creative_style,creative_style_name,base_price,is_deleted,ctime,mtime,gd_sales_type,next_stage_price,cycle_id,raise_info)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.templateId,jdbcType=INTEGER},
        #{item.templateName,jdbcType=VARCHAR},
        #{item.inlineSalesType,jdbcType=INTEGER},
        #{item.creativeStyle,jdbcType=TINYINT},
        #{item.creativeStyleName,jdbcType=VARCHAR},
        #{item.basePrice,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.gdSalesType,jdbcType=TINYINT},
        #{item.nextStagePrice,jdbcType=INTEGER},
        #{item.cycleId,jdbcType=INTEGER},
        #{item.raiseInfo,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      template_id = values(template_id),
      template_name = values(template_name),
      inline_sales_type = values(inline_sales_type),
      creative_style = values(creative_style),
      creative_style_name = values(creative_style_name),
      base_price = values(base_price),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      gd_sales_type = values(gd_sales_type),
      next_stage_price = values(next_stage_price),
      cycle_id = values(cycle_id),
      raise_info = values(raise_info),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdPricePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        template_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="inlineSalesType != null">
        inline_sales_type,
      </if>
      <if test="creativeStyle != null">
        creative_style,
      </if>
      <if test="creativeStyleName != null">
        creative_style_name,
      </if>
      <if test="basePrice != null">
        base_price,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="gdSalesType != null">
        gd_sales_type,
      </if>
      <if test="nextStagePrice != null">
        next_stage_price,
      </if>
      <if test="cycleId != null">
        cycle_id,
      </if>
      <if test="raiseInfo != null">
        raise_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="inlineSalesType != null">
        #{inlineSalesType,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="creativeStyleName != null">
        #{creativeStyleName,jdbcType=VARCHAR},
      </if>
      <if test="basePrice != null">
        #{basePrice,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="gdSalesType != null">
        #{gdSalesType,jdbcType=TINYINT},
      </if>
      <if test="nextStagePrice != null">
        #{nextStagePrice,jdbcType=INTEGER},
      </if>
      <if test="cycleId != null">
        #{cycleId,jdbcType=INTEGER},
      </if>
      <if test="raiseInfo != null">
        #{raiseInfo,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="templateId != null">
        template_id = values(template_id),
      </if>
      <if test="templateName != null">
        template_name = values(template_name),
      </if>
      <if test="inlineSalesType != null">
        inline_sales_type = values(inline_sales_type),
      </if>
      <if test="creativeStyle != null">
        creative_style = values(creative_style),
      </if>
      <if test="creativeStyleName != null">
        creative_style_name = values(creative_style_name),
      </if>
      <if test="basePrice != null">
        base_price = values(base_price),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="gdSalesType != null">
        gd_sales_type = values(gd_sales_type),
      </if>
      <if test="nextStagePrice != null">
        next_stage_price = values(next_stage_price),
      </if>
      <if test="cycleId != null">
        cycle_id = values(cycle_id),
      </if>
      <if test="raiseInfo != null">
        raise_info = values(raise_info),
      </if>
    </trim>
  </insert>
</mapper>