<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.biz.schedule.dao.GdTopViewScheduleDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="gd_schedule_id" jdbcType="INTEGER" property="gdScheduleId" />
    <result column="promotion_purpose_type" jdbcType="TINYINT" property="promotionPurposeType" />
    <result column="selling_type" jdbcType="TINYINT" property="sellingType" />
    <result column="ssa_res_type" jdbcType="TINYINT" property="ssaResType" />
    <result column="ssa_ad_type" jdbcType="TINYINT" property="ssaAdType" />
    <result column="ssa_screen_style" jdbcType="TINYINT" property="ssaScreenStyle" />
    <result column="ssa_show_style" jdbcType="TINYINT" property="ssaShowStyle" />
    <result column="hf_ad_type" jdbcType="TINYINT" property="hfAdType" />
    <result column="android_hf_source_id" jdbcType="INTEGER" property="androidHfSourceId" />
    <result column="ios_hf_source_id" jdbcType="INTEGER" property="iosHfSourceId" />
    <result column="android_app_package_id" jdbcType="INTEGER" property="androidAppPackageId" />
    <result column="ios_app_package_id" jdbcType="INTEGER" property="iosAppPackageId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="click_area" jdbcType="TINYINT" property="clickArea" />
    <result column="new_ios_hf_source_id" jdbcType="INTEGER" property="newIosHfSourceId" />
    <result column="new_android_hf_source_id" jdbcType="INTEGER" property="newAndroidHfSourceId" />
    <result column="top_view_video_type" jdbcType="INTEGER" property="topViewVideoType" />
    <result column="transition_mode" jdbcType="TINYINT" property="transitionMode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, gd_schedule_id, promotion_purpose_type, selling_type, ssa_res_type, ssa_ad_type,
    ssa_screen_style, ssa_show_style, hf_ad_type, android_hf_source_id, ios_hf_source_id,
    android_app_package_id, ios_app_package_id, is_deleted, ctime, mtime, click_area,
    new_ios_hf_source_id, new_android_hf_source_id, top_view_video_type, transition_mode
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gd_top_view_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from gd_top_view_schedule
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gd_top_view_schedule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePoExample">
    delete from gd_top_view_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_top_view_schedule (gd_schedule_id, promotion_purpose_type,
      selling_type, ssa_res_type, ssa_ad_type,
      ssa_screen_style, ssa_show_style, hf_ad_type,
      android_hf_source_id, ios_hf_source_id, android_app_package_id,
      ios_app_package_id, is_deleted, ctime,
      mtime, click_area, new_ios_hf_source_id,
      new_android_hf_source_id, top_view_video_type, transition_mode)
    values (#{gdScheduleId,jdbcType=INTEGER}, #{promotionPurposeType,jdbcType=TINYINT},
      #{sellingType,jdbcType=TINYINT}, #{ssaResType,jdbcType=TINYINT}, #{ssaAdType,jdbcType=TINYINT},
      #{ssaScreenStyle,jdbcType=TINYINT}, #{ssaShowStyle,jdbcType=TINYINT}, #{hfAdType,jdbcType=TINYINT},
      #{androidHfSourceId,jdbcType=INTEGER}, #{iosHfSourceId,jdbcType=INTEGER}, #{androidAppPackageId,jdbcType=INTEGER},
      #{iosAppPackageId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP},
      #{mtime,jdbcType=TIMESTAMP}, #{clickArea,jdbcType=TINYINT}, #{newIosHfSourceId,jdbcType=INTEGER},
      #{newAndroidHfSourceId,jdbcType=INTEGER}, #{topViewVideoType,jdbcType=TINYINT}, #{transitionMode,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_top_view_schedule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="sellingType != null">
        selling_type,
      </if>
      <if test="ssaResType != null">
        ssa_res_type,
      </if>
      <if test="ssaAdType != null">
        ssa_ad_type,
      </if>
      <if test="ssaScreenStyle != null">
        ssa_screen_style,
      </if>
      <if test="ssaShowStyle != null">
        ssa_show_style,
      </if>
      <if test="hfAdType != null">
        hf_ad_type,
      </if>
      <if test="androidHfSourceId != null">
        android_hf_source_id,
      </if>
      <if test="iosHfSourceId != null">
        ios_hf_source_id,
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id,
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="clickArea != null">
        click_area,
      </if>
      <if test="newIosHfSourceId != null">
        new_ios_hf_source_id,
      </if>
      <if test="newAndroidHfSourceId != null">
        new_android_hf_source_id,
      </if>
      <if test="topViewVideoType != null">
        top_view_video_type,
      </if>
      <if test="transitionMode != null">
        transition_mode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="sellingType != null">
        #{sellingType,jdbcType=TINYINT},
      </if>
      <if test="ssaResType != null">
        #{ssaResType,jdbcType=TINYINT},
      </if>
      <if test="ssaAdType != null">
        #{ssaAdType,jdbcType=TINYINT},
      </if>
      <if test="ssaScreenStyle != null">
        #{ssaScreenStyle,jdbcType=TINYINT},
      </if>
      <if test="ssaShowStyle != null">
        #{ssaShowStyle,jdbcType=TINYINT},
      </if>
      <if test="hfAdType != null">
        #{hfAdType,jdbcType=TINYINT},
      </if>
      <if test="androidHfSourceId != null">
        #{androidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="iosHfSourceId != null">
        #{iosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="androidAppPackageId != null">
        #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickArea != null">
        #{clickArea,jdbcType=TINYINT},
      </if>
      <if test="newIosHfSourceId != null">
        #{newIosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="newAndroidHfSourceId != null">
        #{newAndroidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="topViewVideoType != null">
        #{topViewVideoType,jdbcType=TINYINT},
      </if>
      <if test="transitionMode != null">
        #{transitionMode,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePoExample" resultType="java.lang.Long">
    select count(*) from gd_top_view_schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gd_top_view_schedule
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.gdScheduleId != null">
        gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.promotionPurposeType != null">
        promotion_purpose_type = #{record.promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="record.sellingType != null">
        selling_type = #{record.sellingType,jdbcType=TINYINT},
      </if>
      <if test="record.ssaResType != null">
        ssa_res_type = #{record.ssaResType,jdbcType=TINYINT},
      </if>
      <if test="record.ssaAdType != null">
        ssa_ad_type = #{record.ssaAdType,jdbcType=TINYINT},
      </if>
      <if test="record.ssaScreenStyle != null">
        ssa_screen_style = #{record.ssaScreenStyle,jdbcType=TINYINT},
      </if>
      <if test="record.ssaShowStyle != null">
        ssa_show_style = #{record.ssaShowStyle,jdbcType=TINYINT},
      </if>
      <if test="record.hfAdType != null">
        hf_ad_type = #{record.hfAdType,jdbcType=TINYINT},
      </if>
      <if test="record.androidHfSourceId != null">
        android_hf_source_id = #{record.androidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="record.iosHfSourceId != null">
        ios_hf_source_id = #{record.iosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="record.androidAppPackageId != null">
        android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.iosAppPackageId != null">
        ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.clickArea != null">
        click_area = #{record.clickArea,jdbcType=TINYINT},
      </if>
      <if test="record.newIosHfSourceId != null">
        new_ios_hf_source_id = #{record.newIosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="record.newAndroidHfSourceId != null">
        new_android_hf_source_id = #{record.newAndroidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="record.topViewVideoType != null">
        top_view_video_type = #{record.topViewVideoType,jdbcType=TINYINT},
      </if>
      <if test="record.transitionMode != null">
        transition_mode = #{record.transitionMode,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gd_top_view_schedule
    set id = #{record.id,jdbcType=INTEGER},
      gd_schedule_id = #{record.gdScheduleId,jdbcType=INTEGER},
      promotion_purpose_type = #{record.promotionPurposeType,jdbcType=TINYINT},
      selling_type = #{record.sellingType,jdbcType=TINYINT},
      ssa_res_type = #{record.ssaResType,jdbcType=TINYINT},
      ssa_ad_type = #{record.ssaAdType,jdbcType=TINYINT},
      ssa_screen_style = #{record.ssaScreenStyle,jdbcType=TINYINT},
      ssa_show_style = #{record.ssaShowStyle,jdbcType=TINYINT},
      hf_ad_type = #{record.hfAdType,jdbcType=TINYINT},
      android_hf_source_id = #{record.androidHfSourceId,jdbcType=INTEGER},
      ios_hf_source_id = #{record.iosHfSourceId,jdbcType=INTEGER},
      android_app_package_id = #{record.androidAppPackageId,jdbcType=INTEGER},
      ios_app_package_id = #{record.iosAppPackageId,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      click_area = #{record.clickArea,jdbcType=TINYINT},
      new_ios_hf_source_id = #{record.newIosHfSourceId,jdbcType=INTEGER},
      new_android_hf_source_id = #{record.newAndroidHfSourceId,jdbcType=INTEGER},
      top_view_video_type = #{record.topViewVideoType,jdbcType=TINYINT},
      transition_mode = #{record.transitionMode,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    update gd_top_view_schedule
    <set>
      <if test="gdScheduleId != null">
        gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="sellingType != null">
        selling_type = #{sellingType,jdbcType=TINYINT},
      </if>
      <if test="ssaResType != null">
        ssa_res_type = #{ssaResType,jdbcType=TINYINT},
      </if>
      <if test="ssaAdType != null">
        ssa_ad_type = #{ssaAdType,jdbcType=TINYINT},
      </if>
      <if test="ssaScreenStyle != null">
        ssa_screen_style = #{ssaScreenStyle,jdbcType=TINYINT},
      </if>
      <if test="ssaShowStyle != null">
        ssa_show_style = #{ssaShowStyle,jdbcType=TINYINT},
      </if>
      <if test="hfAdType != null">
        hf_ad_type = #{hfAdType,jdbcType=TINYINT},
      </if>
      <if test="androidHfSourceId != null">
        android_hf_source_id = #{androidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="iosHfSourceId != null">
        ios_hf_source_id = #{iosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id = #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id = #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickArea != null">
        click_area = #{clickArea,jdbcType=TINYINT},
      </if>
      <if test="newIosHfSourceId != null">
        new_ios_hf_source_id = #{newIosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="newAndroidHfSourceId != null">
        new_android_hf_source_id = #{newAndroidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="topViewVideoType != null">
        top_view_video_type = #{topViewVideoType,jdbcType=TINYINT},
      </if>
      <if test="transitionMode != null">
        transition_mode = #{transitionMode,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    update gd_top_view_schedule
    set gd_schedule_id = #{gdScheduleId,jdbcType=INTEGER},
      promotion_purpose_type = #{promotionPurposeType,jdbcType=TINYINT},
      selling_type = #{sellingType,jdbcType=TINYINT},
      ssa_res_type = #{ssaResType,jdbcType=TINYINT},
      ssa_ad_type = #{ssaAdType,jdbcType=TINYINT},
      ssa_screen_style = #{ssaScreenStyle,jdbcType=TINYINT},
      ssa_show_style = #{ssaShowStyle,jdbcType=TINYINT},
      hf_ad_type = #{hfAdType,jdbcType=TINYINT},
      android_hf_source_id = #{androidHfSourceId,jdbcType=INTEGER},
      ios_hf_source_id = #{iosHfSourceId,jdbcType=INTEGER},
      android_app_package_id = #{androidAppPackageId,jdbcType=INTEGER},
      ios_app_package_id = #{iosAppPackageId,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      click_area = #{clickArea,jdbcType=TINYINT},
      new_ios_hf_source_id = #{newIosHfSourceId,jdbcType=INTEGER},
      new_android_hf_source_id = #{newAndroidHfSourceId,jdbcType=INTEGER},
      top_view_video_type = #{topViewVideoType,jdbcType=TINYINT},
      transition_mode = #{transitionMode,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_top_view_schedule (gd_schedule_id, promotion_purpose_type,
      selling_type, ssa_res_type, ssa_ad_type,
      ssa_screen_style, ssa_show_style, hf_ad_type,
      android_hf_source_id, ios_hf_source_id, android_app_package_id,
      ios_app_package_id, is_deleted, ctime,
      mtime, click_area, new_ios_hf_source_id,
      new_android_hf_source_id, top_view_video_type, transition_mode)
    values (#{gdScheduleId,jdbcType=INTEGER}, #{promotionPurposeType,jdbcType=TINYINT},
      #{sellingType,jdbcType=TINYINT}, #{ssaResType,jdbcType=TINYINT}, #{ssaAdType,jdbcType=TINYINT},
      #{ssaScreenStyle,jdbcType=TINYINT}, #{ssaShowStyle,jdbcType=TINYINT}, #{hfAdType,jdbcType=TINYINT},
      #{androidHfSourceId,jdbcType=INTEGER}, #{iosHfSourceId,jdbcType=INTEGER}, #{androidAppPackageId,jdbcType=INTEGER},
      #{iosAppPackageId,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP},
      #{mtime,jdbcType=TIMESTAMP}, #{clickArea,jdbcType=TINYINT}, #{newIosHfSourceId,jdbcType=INTEGER},
      #{newAndroidHfSourceId,jdbcType=INTEGER}, #{topViewVideoType,jdbcType=TINYINT}, #{transitionMode,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      gd_schedule_id = values(gd_schedule_id),
      promotion_purpose_type = values(promotion_purpose_type),
      selling_type = values(selling_type),
      ssa_res_type = values(ssa_res_type),
      ssa_ad_type = values(ssa_ad_type),
      ssa_screen_style = values(ssa_screen_style),
      ssa_show_style = values(ssa_show_style),
      hf_ad_type = values(hf_ad_type),
      android_hf_source_id = values(android_hf_source_id),
      ios_hf_source_id = values(ios_hf_source_id),
      android_app_package_id = values(android_app_package_id),
      ios_app_package_id = values(ios_app_package_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      click_area = values(click_area),
      new_ios_hf_source_id = values(new_ios_hf_source_id),
      new_android_hf_source_id = values(new_android_hf_source_id),
      top_view_video_type = values(top_view_video_type),
      transition_mode = values(transition_mode),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into
      gd_top_view_schedule
      (gd_schedule_id,promotion_purpose_type,selling_type,ssa_res_type,ssa_ad_type,ssa_screen_style,ssa_show_style,hf_ad_type,android_hf_source_id,ios_hf_source_id,android_app_package_id,ios_app_package_id,is_deleted,ctime,mtime,click_area,new_ios_hf_source_id,new_android_hf_source_id,top_view_video_type,transition_mode)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gdScheduleId,jdbcType=INTEGER},
        #{item.promotionPurposeType,jdbcType=TINYINT},
        #{item.sellingType,jdbcType=TINYINT},
        #{item.ssaResType,jdbcType=TINYINT},
        #{item.ssaAdType,jdbcType=TINYINT},
        #{item.ssaScreenStyle,jdbcType=TINYINT},
        #{item.ssaShowStyle,jdbcType=TINYINT},
        #{item.hfAdType,jdbcType=TINYINT},
        #{item.androidHfSourceId,jdbcType=INTEGER},
        #{item.iosHfSourceId,jdbcType=INTEGER},
        #{item.androidAppPackageId,jdbcType=INTEGER},
        #{item.iosAppPackageId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.clickArea,jdbcType=TINYINT},
        #{item.newIosHfSourceId,jdbcType=INTEGER},
        #{item.newAndroidHfSourceId,jdbcType=INTEGER},
        #{item.topViewVideoType,jdbcType=TINYINT},
        #{item.transitionMode,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into
      gd_top_view_schedule
      (gd_schedule_id,promotion_purpose_type,selling_type,ssa_res_type,ssa_ad_type,ssa_screen_style,ssa_show_style,hf_ad_type,android_hf_source_id,ios_hf_source_id,android_app_package_id,ios_app_package_id,is_deleted,ctime,mtime,click_area,new_ios_hf_source_id,new_android_hf_source_id,top_view_video_type,transition_mode)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gdScheduleId,jdbcType=INTEGER},
        #{item.promotionPurposeType,jdbcType=TINYINT},
        #{item.sellingType,jdbcType=TINYINT},
        #{item.ssaResType,jdbcType=TINYINT},
        #{item.ssaAdType,jdbcType=TINYINT},
        #{item.ssaScreenStyle,jdbcType=TINYINT},
        #{item.ssaShowStyle,jdbcType=TINYINT},
        #{item.hfAdType,jdbcType=TINYINT},
        #{item.androidHfSourceId,jdbcType=INTEGER},
        #{item.iosHfSourceId,jdbcType=INTEGER},
        #{item.androidAppPackageId,jdbcType=INTEGER},
        #{item.iosAppPackageId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.clickArea,jdbcType=TINYINT},
        #{item.newIosHfSourceId,jdbcType=INTEGER},
        #{item.newAndroidHfSourceId,jdbcType=INTEGER},
        #{item.topViewVideoType,jdbcType=TINYINT},
        #{item.transitionMode,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      gd_schedule_id = values(gd_schedule_id),
      promotion_purpose_type = values(promotion_purpose_type),
      selling_type = values(selling_type),
      ssa_res_type = values(ssa_res_type),
      ssa_ad_type = values(ssa_ad_type),
      ssa_screen_style = values(ssa_screen_style),
      ssa_show_style = values(ssa_show_style),
      hf_ad_type = values(hf_ad_type),
      android_hf_source_id = values(android_hf_source_id),
      ios_hf_source_id = values(ios_hf_source_id),
      android_app_package_id = values(android_app_package_id),
      ios_app_package_id = values(ios_app_package_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      click_area = values(click_area),
      new_ios_hf_source_id = values(new_ios_hf_source_id),
      new_android_hf_source_id = values(new_android_hf_source_id),
      top_view_video_type = values(top_view_video_type),
      transition_mode = values(transition_mode),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.biz.schedule.po.GdTopViewSchedulePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gd_top_view_schedule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="sellingType != null">
        selling_type,
      </if>
      <if test="ssaResType != null">
        ssa_res_type,
      </if>
      <if test="ssaAdType != null">
        ssa_ad_type,
      </if>
      <if test="ssaScreenStyle != null">
        ssa_screen_style,
      </if>
      <if test="ssaShowStyle != null">
        ssa_show_style,
      </if>
      <if test="hfAdType != null">
        hf_ad_type,
      </if>
      <if test="androidHfSourceId != null">
        android_hf_source_id,
      </if>
      <if test="iosHfSourceId != null">
        ios_hf_source_id,
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id,
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="clickArea != null">
        click_area,
      </if>
      <if test="newIosHfSourceId != null">
        new_ios_hf_source_id,
      </if>
      <if test="newAndroidHfSourceId != null">
        new_android_hf_source_id,
      </if>
      <if test="topViewVideoType != null">
        top_view_video_type,
      </if>
      <if test="transitionMode != null">
        transition_mode,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gdScheduleId != null">
        #{gdScheduleId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=TINYINT},
      </if>
      <if test="sellingType != null">
        #{sellingType,jdbcType=TINYINT},
      </if>
      <if test="ssaResType != null">
        #{ssaResType,jdbcType=TINYINT},
      </if>
      <if test="ssaAdType != null">
        #{ssaAdType,jdbcType=TINYINT},
      </if>
      <if test="ssaScreenStyle != null">
        #{ssaScreenStyle,jdbcType=TINYINT},
      </if>
      <if test="ssaShowStyle != null">
        #{ssaShowStyle,jdbcType=TINYINT},
      </if>
      <if test="hfAdType != null">
        #{hfAdType,jdbcType=TINYINT},
      </if>
      <if test="androidHfSourceId != null">
        #{androidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="iosHfSourceId != null">
        #{iosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="androidAppPackageId != null">
        #{androidAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="iosAppPackageId != null">
        #{iosAppPackageId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickArea != null">
        #{clickArea,jdbcType=TINYINT},
      </if>
      <if test="newIosHfSourceId != null">
        #{newIosHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="newAndroidHfSourceId != null">
        #{newAndroidHfSourceId,jdbcType=INTEGER},
      </if>
      <if test="topViewVideoType != null">
        #{topViewVideoType,jdbcType=TINYINT},
      </if>
      <if test="transitionMode != null">
        #{transitionMode,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="gdScheduleId != null">
        gd_schedule_id = values(gd_schedule_id),
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = values(promotion_purpose_type),
      </if>
      <if test="sellingType != null">
        selling_type = values(selling_type),
      </if>
      <if test="ssaResType != null">
        ssa_res_type = values(ssa_res_type),
      </if>
      <if test="ssaAdType != null">
        ssa_ad_type = values(ssa_ad_type),
      </if>
      <if test="ssaScreenStyle != null">
        ssa_screen_style = values(ssa_screen_style),
      </if>
      <if test="ssaShowStyle != null">
        ssa_show_style = values(ssa_show_style),
      </if>
      <if test="hfAdType != null">
        hf_ad_type = values(hf_ad_type),
      </if>
      <if test="androidHfSourceId != null">
        android_hf_source_id = values(android_hf_source_id),
      </if>
      <if test="iosHfSourceId != null">
        ios_hf_source_id = values(ios_hf_source_id),
      </if>
      <if test="androidAppPackageId != null">
        android_app_package_id = values(android_app_package_id),
      </if>
      <if test="iosAppPackageId != null">
        ios_app_package_id = values(ios_app_package_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="clickArea != null">
        click_area = values(click_area),
      </if>
      <if test="newIosHfSourceId != null">
        new_ios_hf_source_id = values(new_ios_hf_source_id),
      </if>
      <if test="newAndroidHfSourceId != null">
        new_android_hf_source_id = values(new_android_hf_source_id),
      </if>
      <if test="topViewVideoType != null">
        top_view_video_type = values(top_view_video_type),
      </if>
      <if test="transitionMode != null">
        transition_mode = values(transition_mode),
      </if>
    </trim>
  </insert>
</mapper>