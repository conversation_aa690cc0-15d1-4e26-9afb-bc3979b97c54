<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.fb.biz.dao.FbResourceConfigDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="first_category" jdbcType="VARCHAR" property="firstCategory" />
    <result column="second_category" jdbcType="VARCHAR" property="secondCategory" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="contain_business" jdbcType="TINYINT" property="containBusiness" />
    <result column="sales_type" jdbcType="VARCHAR" property="salesType" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="original_price" jdbcType="BIGINT" property="originalPrice" />
    <result column="discount" jdbcType="INTEGER" property="discount" />
    <result column="unit_cpm" jdbcType="BIGINT" property="unitCpm" />
    <result column="duration_ratio" jdbcType="INTEGER" property="durationRatio" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <id column="fb_resource_config_id" jdbcType="BIGINT" property="id" />
    <result column="fb_resource_config_resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="fb_resource_config_type" jdbcType="INTEGER" property="type" />
    <result column="fb_resource_config_first_category" jdbcType="VARCHAR" property="firstCategory" />
    <result column="fb_resource_config_second_category" jdbcType="VARCHAR" property="secondCategory" />
    <result column="fb_resource_config_name" jdbcType="VARCHAR" property="name" />
    <result column="fb_resource_config_platform" jdbcType="VARCHAR" property="platform" />
    <result column="fb_resource_config_description" jdbcType="VARCHAR" property="description" />
    <result column="fb_resource_config_project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="fb_resource_config_contain_business" jdbcType="TINYINT" property="containBusiness" />
    <result column="fb_resource_config_sales_type" jdbcType="VARCHAR" property="salesType" />
    <result column="fb_resource_config_unit" jdbcType="VARCHAR" property="unit" />
    <result column="fb_resource_config_original_price" jdbcType="BIGINT" property="originalPrice" />
    <result column="fb_resource_config_discount" jdbcType="INTEGER" property="discount" />
    <result column="fb_resource_config_unit_cpm" jdbcType="BIGINT" property="unitCpm" />
    <result column="fb_resource_config_duration_ratio" jdbcType="INTEGER" property="durationRatio" />
    <result column="fb_resource_config_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="fb_resource_config_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="fb_resource_config_is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as fb_resource_config_id, ${alias}.resource_id as fb_resource_config_resource_id, 
    ${alias}.type as fb_resource_config_type, ${alias}.first_category as fb_resource_config_first_category, 
    ${alias}.second_category as fb_resource_config_second_category, ${alias}.name as fb_resource_config_name, 
    ${alias}.platform as fb_resource_config_platform, ${alias}.description as fb_resource_config_description, 
    ${alias}.project_level as fb_resource_config_project_level, ${alias}.contain_business as fb_resource_config_contain_business, 
    ${alias}.sales_type as fb_resource_config_sales_type, ${alias}.unit as fb_resource_config_unit, 
    ${alias}.original_price as fb_resource_config_original_price, ${alias}.discount as fb_resource_config_discount, 
    ${alias}.unit_cpm as fb_resource_config_unit_cpm, ${alias}.duration_ratio as fb_resource_config_duration_ratio, 
    ${alias}.ctime as fb_resource_config_ctime, ${alias}.mtime as fb_resource_config_mtime, 
    ${alias}.is_deleted as fb_resource_config_is_deleted
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, resource_id, type, first_category, second_category, name, platform, description, 
    project_level, contain_business, sales_type, unit, original_price, discount, unit_cpm, 
    duration_ratio, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fb_resource_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fb_resource_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fb_resource_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPoExample">
    delete from fb_resource_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config (resource_id, type, first_category, 
      second_category, name, platform, 
      description, project_level, contain_business, 
      sales_type, unit, original_price, 
      discount, unit_cpm, duration_ratio, 
      ctime, mtime, is_deleted
      )
    values (#{resourceId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{firstCategory,jdbcType=VARCHAR}, 
      #{secondCategory,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{projectLevel,jdbcType=VARCHAR}, #{containBusiness,jdbcType=TINYINT}, 
      #{salesType,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{originalPrice,jdbcType=BIGINT}, 
      #{discount,jdbcType=INTEGER}, #{unitCpm,jdbcType=BIGINT}, #{durationRatio,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="firstCategory != null">
        first_category,
      </if>
      <if test="secondCategory != null">
        second_category,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="projectLevel != null">
        project_level,
      </if>
      <if test="containBusiness != null">
        contain_business,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="unitCpm != null">
        unit_cpm,
      </if>
      <if test="durationRatio != null">
        duration_ratio,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="firstCategory != null">
        #{firstCategory,jdbcType=VARCHAR},
      </if>
      <if test="secondCategory != null">
        #{secondCategory,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="projectLevel != null">
        #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="containBusiness != null">
        #{containBusiness,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=INTEGER},
      </if>
      <if test="unitCpm != null">
        #{unitCpm,jdbcType=BIGINT},
      </if>
      <if test="durationRatio != null">
        #{durationRatio,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPoExample" resultType="java.lang.Long">
    select count(*) from fb_resource_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fb_resource_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.firstCategory != null">
        first_category = #{record.firstCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.secondCategory != null">
        second_category = #{record.secondCategory,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.projectLevel != null">
        project_level = #{record.projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.containBusiness != null">
        contain_business = #{record.containBusiness,jdbcType=TINYINT},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=VARCHAR},
      </if>
      <if test="record.unit != null">
        unit = #{record.unit,jdbcType=VARCHAR},
      </if>
      <if test="record.originalPrice != null">
        original_price = #{record.originalPrice,jdbcType=BIGINT},
      </if>
      <if test="record.discount != null">
        discount = #{record.discount,jdbcType=INTEGER},
      </if>
      <if test="record.unitCpm != null">
        unit_cpm = #{record.unitCpm,jdbcType=BIGINT},
      </if>
      <if test="record.durationRatio != null">
        duration_ratio = #{record.durationRatio,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fb_resource_config
    set id = #{record.id,jdbcType=BIGINT},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      first_category = #{record.firstCategory,jdbcType=VARCHAR},
      second_category = #{record.secondCategory,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      project_level = #{record.projectLevel,jdbcType=VARCHAR},
      contain_business = #{record.containBusiness,jdbcType=TINYINT},
      sales_type = #{record.salesType,jdbcType=VARCHAR},
      unit = #{record.unit,jdbcType=VARCHAR},
      original_price = #{record.originalPrice,jdbcType=BIGINT},
      discount = #{record.discount,jdbcType=INTEGER},
      unit_cpm = #{record.unitCpm,jdbcType=BIGINT},
      duration_ratio = #{record.durationRatio,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    update fb_resource_config
    <set>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="firstCategory != null">
        first_category = #{firstCategory,jdbcType=VARCHAR},
      </if>
      <if test="secondCategory != null">
        second_category = #{secondCategory,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="projectLevel != null">
        project_level = #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="containBusiness != null">
        contain_business = #{containBusiness,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        discount = #{discount,jdbcType=INTEGER},
      </if>
      <if test="unitCpm != null">
        unit_cpm = #{unitCpm,jdbcType=BIGINT},
      </if>
      <if test="durationRatio != null">
        duration_ratio = #{durationRatio,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    update fb_resource_config
    set resource_id = #{resourceId,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      first_category = #{firstCategory,jdbcType=VARCHAR},
      second_category = #{secondCategory,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      project_level = #{projectLevel,jdbcType=VARCHAR},
      contain_business = #{containBusiness,jdbcType=TINYINT},
      sales_type = #{salesType,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      original_price = #{originalPrice,jdbcType=BIGINT},
      discount = #{discount,jdbcType=INTEGER},
      unit_cpm = #{unitCpm,jdbcType=BIGINT},
      duration_ratio = #{durationRatio,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config (resource_id, type, first_category, 
      second_category, name, platform, 
      description, project_level, contain_business, 
      sales_type, unit, original_price, 
      discount, unit_cpm, duration_ratio, 
      ctime, mtime, is_deleted
      )
    values (#{resourceId,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{firstCategory,jdbcType=VARCHAR}, 
      #{secondCategory,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{projectLevel,jdbcType=VARCHAR}, #{containBusiness,jdbcType=TINYINT}, 
      #{salesType,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{originalPrice,jdbcType=BIGINT}, 
      #{discount,jdbcType=INTEGER}, #{unitCpm,jdbcType=BIGINT}, #{durationRatio,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      resource_id = values(resource_id),
      type = values(type),
      first_category = values(first_category),
      second_category = values(second_category),
      name = values(name),
      platform = values(platform),
      description = values(description),
      project_level = values(project_level),
      contain_business = values(contain_business),
      sales_type = values(sales_type),
      unit = values(unit),
      original_price = values(original_price),
      discount = values(discount),
      unit_cpm = values(unit_cpm),
      duration_ratio = values(duration_ratio),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      fb_resource_config
      (resource_id,type,first_category,second_category,name,platform,description,project_level,contain_business,sales_type,unit,original_price,discount,unit_cpm,duration_ratio,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.resourceId,jdbcType=VARCHAR},
        #{item.type,jdbcType=INTEGER},
        #{item.firstCategory,jdbcType=VARCHAR},
        #{item.secondCategory,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.platform,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.projectLevel,jdbcType=VARCHAR},
        #{item.containBusiness,jdbcType=TINYINT},
        #{item.salesType,jdbcType=VARCHAR},
        #{item.unit,jdbcType=VARCHAR},
        #{item.originalPrice,jdbcType=BIGINT},
        #{item.discount,jdbcType=INTEGER},
        #{item.unitCpm,jdbcType=BIGINT},
        #{item.durationRatio,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      fb_resource_config
      (resource_id,type,first_category,second_category,name,platform,description,project_level,contain_business,sales_type,unit,original_price,discount,unit_cpm,duration_ratio,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.resourceId,jdbcType=VARCHAR},
        #{item.type,jdbcType=INTEGER},
        #{item.firstCategory,jdbcType=VARCHAR},
        #{item.secondCategory,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.platform,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.projectLevel,jdbcType=VARCHAR},
        #{item.containBusiness,jdbcType=TINYINT},
        #{item.salesType,jdbcType=VARCHAR},
        #{item.unit,jdbcType=VARCHAR},
        #{item.originalPrice,jdbcType=BIGINT},
        #{item.discount,jdbcType=INTEGER},
        #{item.unitCpm,jdbcType=BIGINT},
        #{item.durationRatio,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      resource_id = values(resource_id),
      type = values(type),
      first_category = values(first_category),
      second_category = values(second_category),
      name = values(name),
      platform = values(platform),
      description = values(description),
      project_level = values(project_level),
      contain_business = values(contain_business),
      sales_type = values(sales_type),
      unit = values(unit),
      original_price = values(original_price),
      discount = values(discount),
      unit_cpm = values(unit_cpm),
      duration_ratio = values(duration_ratio),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="firstCategory != null">
        first_category,
      </if>
      <if test="secondCategory != null">
        second_category,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="projectLevel != null">
        project_level,
      </if>
      <if test="containBusiness != null">
        contain_business,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="discount != null">
        discount,
      </if>
      <if test="unitCpm != null">
        unit_cpm,
      </if>
      <if test="durationRatio != null">
        duration_ratio,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="firstCategory != null">
        #{firstCategory,jdbcType=VARCHAR},
      </if>
      <if test="secondCategory != null">
        #{secondCategory,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="projectLevel != null">
        #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="containBusiness != null">
        #{containBusiness,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=BIGINT},
      </if>
      <if test="discount != null">
        #{discount,jdbcType=INTEGER},
      </if>
      <if test="unitCpm != null">
        #{unitCpm,jdbcType=BIGINT},
      </if>
      <if test="durationRatio != null">
        #{durationRatio,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="resourceId != null">
        resource_id = values(resource_id),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="firstCategory != null">
        first_category = values(first_category),
      </if>
      <if test="secondCategory != null">
        second_category = values(second_category),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="platform != null">
        platform = values(platform),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
      <if test="projectLevel != null">
        project_level = values(project_level),
      </if>
      <if test="containBusiness != null">
        contain_business = values(contain_business),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="unit != null">
        unit = values(unit),
      </if>
      <if test="originalPrice != null">
        original_price = values(original_price),
      </if>
      <if test="discount != null">
        discount = values(discount),
      </if>
      <if test="unitCpm != null">
        unit_cpm = values(unit_cpm),
      </if>
      <if test="durationRatio != null">
        duration_ratio = values(duration_ratio),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>