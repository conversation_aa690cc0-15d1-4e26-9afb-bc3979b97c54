<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.brand.fb.biz.dao.FbResourceConfigProjectTypeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="resource_config_id" jdbcType="BIGINT" property="resourceConfigId" />
    <result column="project_major_type" jdbcType="INTEGER" property="projectMajorType" />
    <result column="project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <id column="fb_resource_config_project_type_id" jdbcType="BIGINT" property="id" />
    <result column="fb_resource_config_project_type_resource_config_id" jdbcType="BIGINT" property="resourceConfigId" />
    <result column="fb_resource_config_project_type_project_major_type" jdbcType="INTEGER" property="projectMajorType" />
    <result column="fb_resource_config_project_type_project_level" jdbcType="VARCHAR" property="projectLevel" />
    <result column="fb_resource_config_project_type_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="fb_resource_config_project_type_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="fb_resource_config_project_type_is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as fb_resource_config_project_type_id, ${alias}.resource_config_id as fb_resource_config_project_type_resource_config_id, 
    ${alias}.project_major_type as fb_resource_config_project_type_project_major_type, 
    ${alias}.project_level as fb_resource_config_project_type_project_level, ${alias}.ctime as fb_resource_config_project_type_ctime, 
    ${alias}.mtime as fb_resource_config_project_type_mtime, ${alias}.is_deleted as fb_resource_config_project_type_is_deleted
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, resource_config_id, project_major_type, project_level, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fb_resource_config_project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fb_resource_config_project_type
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fb_resource_config_project_type
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePoExample">
    delete from fb_resource_config_project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config_project_type (resource_config_id, project_major_type, 
      project_level, ctime, mtime, 
      is_deleted)
    values (#{resourceConfigId,jdbcType=BIGINT}, #{projectMajorType,jdbcType=INTEGER}, 
      #{projectLevel,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config_project_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resourceConfigId != null">
        resource_config_id,
      </if>
      <if test="projectMajorType != null">
        project_major_type,
      </if>
      <if test="projectLevel != null">
        project_level,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resourceConfigId != null">
        #{resourceConfigId,jdbcType=BIGINT},
      </if>
      <if test="projectMajorType != null">
        #{projectMajorType,jdbcType=INTEGER},
      </if>
      <if test="projectLevel != null">
        #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePoExample" resultType="java.lang.Long">
    select count(*) from fb_resource_config_project_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fb_resource_config_project_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.resourceConfigId != null">
        resource_config_id = #{record.resourceConfigId,jdbcType=BIGINT},
      </if>
      <if test="record.projectMajorType != null">
        project_major_type = #{record.projectMajorType,jdbcType=INTEGER},
      </if>
      <if test="record.projectLevel != null">
        project_level = #{record.projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fb_resource_config_project_type
    set id = #{record.id,jdbcType=BIGINT},
      resource_config_id = #{record.resourceConfigId,jdbcType=BIGINT},
      project_major_type = #{record.projectMajorType,jdbcType=INTEGER},
      project_level = #{record.projectLevel,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    update fb_resource_config_project_type
    <set>
      <if test="resourceConfigId != null">
        resource_config_id = #{resourceConfigId,jdbcType=BIGINT},
      </if>
      <if test="projectMajorType != null">
        project_major_type = #{projectMajorType,jdbcType=INTEGER},
      </if>
      <if test="projectLevel != null">
        project_level = #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    update fb_resource_config_project_type
    set resource_config_id = #{resourceConfigId,jdbcType=BIGINT},
      project_major_type = #{projectMajorType,jdbcType=INTEGER},
      project_level = #{projectLevel,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config_project_type (resource_config_id, project_major_type, 
      project_level, ctime, mtime, 
      is_deleted)
    values (#{resourceConfigId,jdbcType=BIGINT}, #{projectMajorType,jdbcType=INTEGER}, 
      #{projectLevel,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      resource_config_id = values(resource_config_id),
      project_major_type = values(project_major_type),
      project_level = values(project_level),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      fb_resource_config_project_type
      (resource_config_id,project_major_type,project_level,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.resourceConfigId,jdbcType=BIGINT},
        #{item.projectMajorType,jdbcType=INTEGER},
        #{item.projectLevel,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      fb_resource_config_project_type
      (resource_config_id,project_major_type,project_level,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.resourceConfigId,jdbcType=BIGINT},
        #{item.projectMajorType,jdbcType=INTEGER},
        #{item.projectLevel,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      resource_config_id = values(resource_config_id),
      project_major_type = values(project_major_type),
      project_level = values(project_level),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fb_resource_config_project_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resourceConfigId != null">
        resource_config_id,
      </if>
      <if test="projectMajorType != null">
        project_major_type,
      </if>
      <if test="projectLevel != null">
        project_level,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resourceConfigId != null">
        #{resourceConfigId,jdbcType=BIGINT},
      </if>
      <if test="projectMajorType != null">
        #{projectMajorType,jdbcType=INTEGER},
      </if>
      <if test="projectLevel != null">
        #{projectLevel,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="resourceConfigId != null">
        resource_config_id = values(resource_config_id),
      </if>
      <if test="projectMajorType != null">
        project_major_type = values(project_major_type),
      </if>
      <if test="projectLevel != null">
        project_level = values(project_level),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>