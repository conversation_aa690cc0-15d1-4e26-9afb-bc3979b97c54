<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="adxCatExecutorMybatisPlugin" class="com.bilibili.bjcom.cat.mybatis.CatExecutorMybatisPlugin"/>

<!--
    <bean id="adxDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${adx.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${adx.jdbc.url}"></property>
        <property name="user" value="${adx.jdbc.username}"></property>
        <property name="password" value="${adx.jdbc.password}"></property>
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>
-->

    <!-- 有时会报这个错 Caused by: com.atomikos.jdbc.internal.AtomikosSQLException: The transaction has timed out -->
    <!-- try increasing the timeout if needed，网上建议设置最大连接数为100 -->
    <!-- https://blog.csdn.net/qq_39002724/article/details/106671114 -->
    <bean id="adxDataSource" class="com.atomikos.jdbc.AtomikosDataSourceBean">
        <property name="uniqueResourceName" value="adxDataSource"/>
        <property name="xaDataSourceClassName" value="com.mysql.jdbc.jdbc2.optional.MysqlXADataSource"/>
        <property name="xaProperties">
            <props>
                <prop key="url">${adx.jdbc.url}</prop>
                <prop key="user">${adx.jdbc.username}</prop>
                <prop key="password">${adx.jdbc.password}</prop>
            </props>
        </property>
        <property name="minPoolSize" value="3"/>
        <property name="maxPoolSize" value="100"/>
        <property name="maxIdleTime" value="7200"/>
        <property name="testQuery" value="SELECT 1"/>
        <property name="localTransactionMode" value="true"/>
    </bean>

    <!-- Spring 和 MyBatis -->
    <bean id="adxSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adxDataSource"/>
        <property name="mapperLocations" value="classpath:mapper/adx/*.xml"/>
        <property name="plugins">
            <array>
                <ref bean="adxCatExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.brand.biz.order.adxdao"/>
        <property name="sqlSessionFactoryBeanName" value="adxSqlSessionFactory"/>
    </bean>
<!--
    <tx:annotation-driven transaction-manager="adxTransactionManager"/>
    &lt;!&ndash; 配置事务管理器 &ndash;&gt;
    <bean id="adxTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adxDataSource"/>
    </bean>
-->
</beans>
