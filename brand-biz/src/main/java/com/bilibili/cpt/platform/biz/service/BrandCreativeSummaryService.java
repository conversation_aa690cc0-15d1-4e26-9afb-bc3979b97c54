package com.bilibili.cpt.platform.biz.service;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.creative.dto.GdCreativeDto;
import com.bilibili.brand.biz.utils.BrandLittleAssistantUtil;
import com.bilibili.cpt.platform.biz.dao.BrandCreativeProductSalesSummaryDao;
import com.bilibili.cpt.platform.biz.po.BrandCreativeProductSalesSummaryPo;
import com.bilibili.cpt.platform.biz.po.BrandCreativeProductSalesSummaryPoExample;
import com.bilibili.cpt.platform.util.IpUtil;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/24
 **/

@Service
@Slf4j
public class BrandCreativeSummaryService {

    @Resource
    private BrandCreativeProductSalesSummaryDao brandCreativeProductSalesSummaryDao;


    @Transactional(rollbackFor = Exception.class)
    public void handleBatch(List<BrandCreativeProductSalesSummaryPo> batchInsertOrUpdatePos, List<Long> needDeleteCreativeIds, Boolean isSsa) {
        // 插入表中
        for (BrandCreativeProductSalesSummaryPo po : batchInsertOrUpdatePos) {
            try {
                brandCreativeProductSalesSummaryDao.insertUpdateSelective(po);
            } catch (Exception e) {
                log.error("[BrandCreativeSummaryService] handleBatch error, po:{}", po, e);
                // 发送消息到企微
                BrandLittleAssistantUtil.sendWithMarkdown(
                        BrandLittleAssistantUtil.GroupKey.BRAND_DEVELOPER_GROUP_KEY,
                        String.format("同步品牌新样式失败，订单ID=%d，创意ID=%d，样式类型=%s，机器IP=%s",
                                po.getOrderId(), po.getCreativeId(), po.getProductName(), IpUtil.getIp()));
            }
        }
        // 需要删除的数据
        if (CollectionUtils.isNotEmpty(needDeleteCreativeIds)) {
            BrandCreativeProductSalesSummaryPoExample example = new BrandCreativeProductSalesSummaryPoExample();
            BrandCreativeProductSalesSummaryPoExample.Criteria criteria = example.createCriteria();
            criteria.andCreativeIdIn(needDeleteCreativeIds);
            BrandCreativeProductSalesSummaryPo salesSummaryPo = BrandCreativeProductSalesSummaryPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();
            if (Objects.nonNull(isSsa)) {
                if (!isSsa) {
                    criteria.andOrderProductNotIn(getSsaOrderProduct());
                } else {
                    criteria.andOrderProductIn(getSsaOrderProduct());
                }
            }
            brandCreativeProductSalesSummaryDao.updateByExampleSelective(salesSummaryPo, example);

        }
    }

    public Map<Long, BrandCreativeProductSalesSummaryPo> queryByCreativeIds(List<Long> creativeIds, boolean isSsa) {
        BrandCreativeProductSalesSummaryPoExample example = new BrandCreativeProductSalesSummaryPoExample();
        BrandCreativeProductSalesSummaryPoExample.Criteria criteria = example.createCriteria();
        List<Integer> ssaOrderProduct = getSsaOrderProduct();
        if (isSsa) {
            criteria.andOrderProductIn(ssaOrderProduct);
        } else {
            criteria.andOrderProductNotIn(ssaOrderProduct);
        }
        criteria.andCreativeIdIn(creativeIds);
        List<BrandCreativeProductSalesSummaryPo> pos = brandCreativeProductSalesSummaryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Maps.newHashMap();
        }
        return pos.stream().collect(Collectors.toMap(BrandCreativeProductSalesSummaryPo::getCreativeId, Function.identity()));
    }

    public List<BrandCreativeProductSalesSummaryPo> quickQueryByPage(Long fromId, Integer size) {
        BrandCreativeProductSalesSummaryPoExample example = new BrandCreativeProductSalesSummaryPoExample();
        BrandCreativeProductSalesSummaryPoExample.Criteria criteria = example.createCriteria();
        criteria.andIdGreaterThan(fromId);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setLimit(size);
        example.setOrderByClause("id asc");
        List<BrandCreativeProductSalesSummaryPo> splashScreenPos = brandCreativeProductSalesSummaryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(splashScreenPos)) {
            return Collections.emptyList();
        }
        return splashScreenPos;
    }

    private List<Integer> getSsaOrderProduct() {
        // 获取闪频对应的订单类型
        return Lists.newArrayList(
                OrderProduct.SSA_CPT_PLUS.getCode(),
                OrderProduct.SSA_GD_PLUS.getCode(),
                OrderProduct.TOP_VIEW_CPT_PLUS.getCode(),
                OrderProduct.TOP_VIEW_GD_PLUS.getCode());
    }
}
