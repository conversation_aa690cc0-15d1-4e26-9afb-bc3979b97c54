package com.bilibili.cpt.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.cpt.platform.api.creative.service.ICptCreativeService;
import com.bilibili.cpt.platform.soa.ISoaCptCreativeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by 崔海船 on 2017/10/09.
 */
@Service
public class SoaCptCreativeService implements ISoaCptCreativeService {

    @Autowired
    private ICptCreativeService cptCreativeService;

    @Override
    public void auditRejectByMgkPageIds(Operator operator, List<Long> mgkPageIds) {
        cptCreativeService.auditRejectByMgkPageIds(operator, mgkPageIds);
    }
}