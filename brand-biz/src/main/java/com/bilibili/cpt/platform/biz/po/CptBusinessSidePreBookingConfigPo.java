package com.bilibili.cpt.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CptBusinessSidePreBookingConfigPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 业务方ID
     */
    private Integer businessSideId;

    /**
     * 资源位ID
     */
    private Integer sourceId;

    /**
     * 周期ID
     */
    private Integer cycleId;

    /**
     * 月预定轮播数上限
     */
    private Integer mRotationLimit;

    /**
     * 软删除 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}