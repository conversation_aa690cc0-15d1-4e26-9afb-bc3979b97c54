package com.bilibili.cpt.platform.biz.service.creative;

import com.bilibili.CommonBvidUtils;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.CommonValidator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.resource.api.app_awaken.dto.AwakenAppWhitelistDto;
import com.bilibili.adp.resource.api.soa.ISoaAwakenAppWhitelistService;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.dto.resource.GameDto;
import com.bilibili.cpt.platform.api.creative.dto.BackGroundColorBo;
import com.bilibili.cpt.platform.api.creative.dto.ButtonDto;
import com.bilibili.cpt.platform.biz.enumerate.BackgroundColorEnum;
import com.bilibili.cpt.platform.biz.enumerate.BackgroundColorType;
import com.bilibili.cpt.platform.common.ButtonCopyTypeEnum;
import com.bilibili.cpt.platform.common.ButtonStyle;
import com.bilibili.enums.AdditionalCardType;
import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.api.common.enums.GdOrderStatus;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.utils.CptConfigUtil;
import com.bilibili.brand.biz.utils.SwitchUtil;
import com.bilibili.cpt.platform.api.creative.dto.SearchCptCreativeDto;
import com.bilibili.cpt.platform.api.schedule.dto.CptScheduleDto;
import com.bilibili.cpt.platform.biz.bean.CreativeBaseBean;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.common.CptCmMark;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.enums.TemplatePropertyEnum;
import com.bilibili.enums.WakeAppType;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.EnumSet;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2017年6月22日
 */
@Component
public class SearchCptCreativeValidator extends CptBaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SearchCptCreativeValidator.class);
    private static final int MAX_URL_LENGTH = 2048;
    private final static Integer MAX_CREATIVE_NAME_LENGTH = 32;
    public final static EnumSet<GdJumpType> VIDEO_TYPE = EnumSet.of(GdJumpType.VIDEO_MOBILE, GdJumpType.VIDEO_WEB);
    @Autowired
    private ISoaAwakenAppWhitelistService soaAwakenAppWhitelistService;
    @Autowired
    private ISoaCrmContractService contractService;
    @Autowired
    private IWakeUpService wakeUpService;

    @Autowired
    private CptConfigUtil cptConfigUtil;
    @Autowired
    private SwitchUtil switchUtil;
    @Autowired
    private ConfigCenter configCenter;

    public void validate(CreativeBaseBean creative, TemplateDto template) {
        LOGGER.info("CptCreativeService.create.validateCreative param[creative-{}, template-{}]", creative, template);
        Assert.isTrue(creative != null && template != null, "创意信息或模板信息不可为空");

        Assert.isTrue(validateText(template.getIsFillTitle()
                , template.getTitleMinLength()
                , template.getTitleMaxLength()
                , creative.getTitle()), "创意标题不符合模板要求");

        Assert.isTrue(validateText(template.getIsFillDesc()
                , template.getDescMinLength()
                , template.getDescMaxLength()
                , creative.getDescription()), "创意描述不符合模板要求");

        Assert.isTrue(validateText(template.getIsFillExtDesc()
                , template.getExtDescMinLength()
                , template.getExtDescMaxLength()
                , creative.getExtDescription()), "创意扩展描述不符合模板要求");

        GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());

        if (VIDEO_TYPE.contains(jumpType)) {
            creative.setVideoId(transferVideoIdFromWeb(jumpType, creative.getPromotionPurposeContent()));
        }

//        if (template.getIsSupportVideoId()) {
//            Assert.isTrue(VIDEO_TYPE.contains(jumpType), "跳转类型必须为视频");
//            Assert.isTrue(creative.getVideoId() > 0, "创意视频ID不符合模板要求");
//            // 调稿件查询接口
//            this.validateArchive(creative.getVideoId());
//        } else {
//            if (creative.getVideoId() > 0) {
//                // 调稿件查询接口
//                this.validateArchive(creative.getVideoId());
//            }
//        }
        //TODO 支持图文等任意类型模板输入videoId

//        Assert.isTrue(validateUrl(template.getIsSupportImage()
//                , creative.getImageUrl()), "大图图片不符合模板要求");
//
//        Assert.isTrue(validateUrl(template.getIsSupportExtImage()
//                , creative.getExtImageUrl()), "小图图片不符合模板要求");

        Assert.isTrue(validateMd5(creative.getImageUrl(), creative.getImageMd5()), "大图图片与其MD5不匹配");

        Assert.isTrue(validateMd5(creative.getExtImageUrl(), creative.getExtImageMd5()), "小图图片与其MD5不匹配");

//        if (creative.getSalesType() != null
//                && creative.getSalesType() == SalesType.TOP_VIEW_CPT.getCode()) {
//            MaterialType.IMAGE.validateUrlHash(creative, template);
//        } else {
//            for (MaterialType materialType : MaterialType.values()) {
//                materialType.validateUrlHash(creative, template);
//            }
//        }

        //校验按钮文案
//        if (template.getIsSupportButton()) {
//            Assert.notNull(creative.getButtonCopyId(), "按钮文案ID不可为空");
//
//            // 该按钮是否绑定到该模板
//            List<ButtonCopyDto> validateButtonCopyDtos = template.getButtonCopyDtos();
//            Assert.notEmpty(validateButtonCopyDtos, "该模板没有绑定任何按钮文案");
//            Map<Integer, Integer> buttonCopyId2TypeMap = validateButtonCopyDtos.stream().collect(Collectors.toMap(ButtonCopyDto::getId, ButtonCopyDto::getType));
//            //todo 后续需要考虑是否要此校验
////            Assert.isTrue(buttonCopyId2TypeMap.containsKey(creative.getButtonCopyId()), "该模板没有绑定该按钮文案");
//        }
        TemplatePropertyEnum propertyEnum = TemplatePropertyEnum.getByTemplateIdWithoutEx(template.getTemplateId());
        if (!Objects.equals(TemplatePropertyEnum.UNKNOWN, propertyEnum)) {
            TemplatePropertyEnum.TemplateOptions options = propertyEnum.getOptions();
            List<AdditionalCardType> additionalCards = options.getSupportAdditionalCards();
            // 如果模板支持附加卡片，创意附加卡片类型必须在支持的附加卡片类型中
            if (!CollectionUtils.isEmpty(additionalCards)) {
                additionalCards.stream().filter(type -> Objects.equals(type.getCode(), creative.getAdditionalCardType()))
                        .findFirst().orElseThrow(() -> new IllegalArgumentException("该模板不支持该附加卡片类型"));
            }
        }
    }


    private boolean validateMd5(String url, String md5) {
        if (Strings.isNullOrEmpty(url)
                && !Strings.isNullOrEmpty(md5)) {
            return false;
        } else if (!Strings.isNullOrEmpty(url) && Strings.isNullOrEmpty(md5)) {
            return false;
        } else if (!Strings.isNullOrEmpty(url) && md5.length() != 32) {
            return false;
        } else {
            return true;
        }
    }

    private boolean validateUrl(Boolean isSupport, String url) {
        if (isSupport
                && Strings.isNullOrEmpty(url)) {
            return false;
        }
        return isSupport
                || Strings.isNullOrEmpty(url);
    }

    public void validateNewCreativeBasicInfo(SearchCptCreativeDto creative, CptScheduleDto scheduleDto) {
        LOGGER.info("validateNewCreativeBasicInfo creative: {}.", creative);
        Assert.notNull(creative, "创意信息不可为空");
        Assert.notNull(creative.getGdScheduleId(), "GD排期ID不可为空");
        Assert.hasText(creative.getCreativeName(), "创意名称不可为空");
        Assert.isTrue(creative.getCreativeName().length() <= MAX_CREATIVE_NAME_LENGTH, "创意名称不可超过" + MAX_CREATIVE_NAME_LENGTH + "个字符");
        PlatformType platform = PlatformType.getByCode(scheduleDto.getPlatformId());
        //目前web搜索cpt多稿件没有推广目的（跳转链接），因此不校验
        if (platform != PlatformType.WEB && needCheckJumpInfo(creative, scheduleDto)) {
            Assert.hasText(creative.getPromotionPurposeContent(), "广告跳转链接不可为空");
            Assert.isTrue(creative.getPromotionPurposeContent().length() <= MAX_URL_LENGTH, "广告跳转链接长度不可大于" + MAX_URL_LENGTH);
            //校验跳转类型
            GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());
            Assert.isTrue(jumpType.validateUrlIsValid(creative.getPromotionPurposeContent()), "跳转类型与链接值不匹配");
        }

        CommonValidator.bilibiliUrlMustHttps(creative.getPromotionPurposeContent());
        Assert.notNull(creative.getCmMark(), "广告标识不可为空");
        if (!configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(creative.getTemplateId())) {
            CptCmMark.getByCode(creative.getCmMark());
        }
        Assert.notNull(creative.getBeginTime(), "开始时间不可为空");
        Assert.notNull(creative.getEndTime(), "结束时间不可为空");
        Assert.notNull(creative.getTemplateId(), "模板ID不可为空");
        Assert.notNull(creative.getJumpType(), "跳转类型不可为空");

        Assert.isTrue(TimeUtils.isValidHourTimestamp(creative.getBeginTime()), "投放开始时间必须是精确到小时的时间戳");
        Assert.isTrue(TimeUtils.isValidHourTimestamp(creative.getEndTime()), "投放结束时间必须是精确到小时的时间戳");

        Assert.isTrue(creative.getBeginTime().compareTo(TimeUtils.getHourTimestamp(Utils.getNow())) >= 0, "投放开始时间必须大于当前时间");
        Assert.isTrue(creative.getBeginTime().compareTo(creative.getEndTime()) < 1, "投放开始时间必须小于投放结束时间");

        String clickUrl = creative.getCustomizedClickUrl();
        if (!Strings.isNullOrEmpty(clickUrl)) {
            Assert.isTrue(clickUrl.length() <= MAX_URL_LENGTH, "点击监控链接长度不可大于" + MAX_URL_LENGTH);
        }
        String impUrl = creative.getCustomizedImpUrl();
        if (!Strings.isNullOrEmpty(impUrl)) {
            Assert.isTrue(impUrl.length() <= MAX_URL_LENGTH, "展示监控链接长度不可大于" + MAX_URL_LENGTH);
        }
        this.validateGame(creative, scheduleDto);
        if (!Objects.equals(scheduleDto.getWakeAppType(), WakeAppType.APP.getCode())) {
            // 不支持填写App唤起链接
            Assert.isTrue(StringUtils.isEmpty(creative.getSchemeUrl()), "排期未选择唤起应用,创意不支持填写App唤起链接");
        }
        creative.setBackGroundColor(validateBackgroundColor(creative.getBackGroundColor()));
    }

    public void validateScheme(Integer buAccountId, Integer platformId, String schemeUrl, Integer contractId) {
        if (!Strings.isNullOrEmpty(schemeUrl)) {
            Assert.isTrue(Utils.isPositive(buAccountId), "支持Scheme唤起的业务方必须绑定商业账号");
            Assert.isTrue(PlatformType.getByCode(platformId).supportScheme(), "只有移动端才支持scheme唤起");

            if (Utils.isPositive(contractId)) {
                ContractDto contract = contractService.getContractById(contractId);
                Assert.notNull(contract, "合同信息不存在");
                Assert.isTrue(wakeUpService.isInDirectWakeUpWhiteList(contract.getAccountId()), "订单对应账号没有直接唤起权限");
            }

            //淘宝618取消校验
            if (!cptConfigUtil.containSkipValidUrl(schemeUrl)) {
                String scheme = UriComponentsBuilder.fromUriString(schemeUrl).build().getScheme();
                Assert.isTrue(!Strings.isNullOrEmpty(scheme), "唤起链接有误");

                Assert.isTrue(!schemeUrl.startsWith("http"), "唤起链接有误");
                AwakenAppWhitelistDto awakenApplistDto = soaAwakenAppWhitelistService.getByScheme(buAccountId, scheme);
                Assert.notNull(awakenApplistDto, "该唤起scheme不在白名单内");


                boolean hasBar = wakeUpService.hasWakeUpBar(schemeUrl);
                Assert.isTrue(hasBar
                        || wakeUpService.isInWakeUpBarWhiteList(scheme), "保存失败，唤起链接不符合要求");
            }
        }
    }


    /**
     * 搜索cpt下的单稿件自动生成跳转链接,不需要校验
     */
    private boolean needCheckJumpInfo(SearchCptCreativeDto creative, CptScheduleDto scheduleDto) {
        boolean isCptAndOneManuscript = scheduleDto.getSalesType().equals(SalesType.SEARCH_CPT.getCode())
                && !CollectionUtils.isEmpty(creative.getAids())
                && creative.getAids().size() == 1;

        return !isCptAndOneManuscript;
    }

    public void validateUpdateCreativeBasicInfo(SearchCptCreativeDto creative, CptScheduleDto scheduleDto) {
        Assert.hasText(creative.getCreativeName(), "创意名称不可为空");
        Assert.isTrue(creative.getCreativeName().length() <= MAX_CREATIVE_NAME_LENGTH, "创意名称不可超过" + MAX_CREATIVE_NAME_LENGTH + "个字符");
        PlatformType platform = PlatformType.getByCode(scheduleDto.getPlatformId());
        //目前web搜索cpt多稿件没有推广目的（跳转链接），因此不校验
        if (platform != PlatformType.WEB && needCheckJumpInfo(creative, scheduleDto)) {
            Assert.hasText(creative.getPromotionPurposeContent(), "广告跳转链接不可为空");
            Assert.isTrue(creative.getPromotionPurposeContent().length() <= MAX_URL_LENGTH, "广告跳转链接长度不可大于" + MAX_URL_LENGTH);

            //校验跳转类型
            GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());
            Assert.isTrue(jumpType.validateUrlIsValid(creative.getPromotionPurposeContent()), "跳转类型与链接值不匹配");
        }
        CommonValidator.bilibiliUrlMustHttps(creative.getPromotionPurposeContent());
        Assert.notNull(creative.getCmMark(), "广告标识不可为空");

        if (!configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(creative.getTemplateId())) {
            CptCmMark.getByCode(creative.getCmMark());
        }

        Assert.notNull(creative.getBeginTime(), "开始时间不可为空");
        Assert.notNull(creative.getEndTime(), "结束时间不可为空");

        Assert.isTrue(TimeUtils.isValidHourTimestamp(creative.getBeginTime()), "投放开始时间必须是精确到小时的时间戳");
        Assert.isTrue(TimeUtils.isValidHourTimestamp(creative.getEndTime()), "投放结束时间必须是精确到小时的时间戳");
        Assert.isTrue(creative.getBeginTime().compareTo(creative.getEndTime()) < 1, "投放开始时间必须小于投放结束时间");

        String clickUrl = creative.getCustomizedClickUrl();
        if (!Strings.isNullOrEmpty(clickUrl)) {
            Assert.isTrue(clickUrl.length() <= MAX_URL_LENGTH, "点击监控链接长度不可大于" + MAX_URL_LENGTH);
        }
        String impUrl = creative.getCustomizedImpUrl();
        if (!Strings.isNullOrEmpty(impUrl)) {
            Assert.isTrue(impUrl.length() <= MAX_URL_LENGTH, "展示监控链接长度不可大于" + MAX_URL_LENGTH);
        }
        this.validateGame(creative, scheduleDto);

        if (!Objects.equals(scheduleDto.getWakeAppType(), WakeAppType.APP.getCode())) {
            // 不支持填写App唤起链接
            Assert.isTrue(StringUtils.isEmpty(creative.getSchemeUrl()), "排期未选择唤起应用,创意不支持填写App唤起链接");
        }
        creative.setBackGroundColor(validateBackgroundColor(creative.getBackGroundColor()));
    }

    private boolean validateText(boolean isSupport
            , int minLength
            , int maxLength
            , String content) {

        if (isSupport && Strings.isNullOrEmpty(content)) {
            return false;
        } else if (!isSupport && !Strings.isNullOrEmpty(content)) {
            return false;
        }

        if (!isSupport) {
            return true;
        }

        if (content.length() < minLength) {
            return false;
        }

        return content.length() <= maxLength;
    }

    public void validateTime(SearchCptCreativeDto updateCptCreativeDto,
                             GdCreativePo creativePo,
                             CptScheduleDto scheduleDto,
                             GdOrderStatus orderStatus) {

        //创意已开始投放
        if (TimeUtils.getHourTimestamp(Utils.getNow()).compareTo(creativePo.getBeginTime()) >= 0) {
            Assert.isTrue(updateCptCreativeDto.getBeginTime().equals(creativePo.getBeginTime()), "该创意已开始投放, 投放开始时间不可更改");
        } else {
            //创意未开始投放,开始时间必须大于等于当前小时时间戳
            Assert.isTrue(updateCptCreativeDto.getBeginTime().compareTo(TimeUtils.getHourTimestamp(Utils.getNow())) >= 0, "投放开始时间必须大于当前时间");
        }

        if (GdOrderStatus.TO_BE_AUDIT.equals(orderStatus)) {
            //待审核的订单只允许改短创意投放时间
            Assert.isTrue(updateCptCreativeDto.getEndTime().compareTo(creativePo.getEndTime()) <= 0, "订单处于待审核状态下,投放结束时间不可延长");
        }

        //起始时间检验
        Assert.isTrue(updateCptCreativeDto.getBeginTime().compareTo(scheduleDto.getBeginDate()) >= 0, "投放开始时间不可小于排期开始时间");
        Assert.isTrue(updateCptCreativeDto.getEndTime().compareTo(Utils.getSomeDayAfter(scheduleDto.getEndDate(), 1)) < 0, "投放结束时间不可大于排期结束时间");
    }

    public void validateCreativeLaunchTime(SearchCptCreativeDto newCptCreativeDto, CptScheduleDto scheduleDto) {
        //起始时间检验
        Assert.isTrue(newCptCreativeDto.getBeginTime()
                .compareTo(scheduleDto.getBeginDate()) >= 0, "投放开始时间不可小于排期开始时间");
        Assert.isTrue(newCptCreativeDto.getEndTime()
                .compareTo(Utils.getSomeDayAfter(scheduleDto.getEndDate(), 1)) < 0, "投放结束时间不可大于排期结束时间");

    }

    public Long transferVideoIdFromWeb(GdJumpType jumpType, String promotionPurposeContent) {
        String idString = jumpType.parseLaunchUrl(promotionPurposeContent);
        return switchUtil.getAvIdSwitch() ? CommonBvidUtils.transferToAvid(idString) : Long.valueOf(idString);

    }

    private void validateGame(SearchCptCreativeDto creative, CptScheduleDto scheduleDto) {
        GameDto game = scheduleDto.getGame();
        if (Objects.isNull(game)) {
            return;
        }
        PromotionPurposeType ppt = PromotionPurposeType.getByCode(scheduleDto.getPromotionPurposeType());

        //校验按钮部分的游戏是否排期一致
        if (Objects.equals(ppt, PromotionPurposeType.ANDROID_GAME_DOWNLOAD)) {
            for (ButtonDto button : creative.getButtons()) {
                GdJumpType jumpType = GdJumpType.getByCode(button.getJumpType());
                boolean isNeedCheckGame = Objects.equals(jumpType, GdJumpType.GAME);
                if (Objects.equals(button.getButtonStyle(), ButtonStyle.SELECT.getCode())
                        && Objects.equals(button.getButtonType(), ButtonCopyTypeEnum.GAME_DOWNLOAD.getCode())
                        && Objects.equals(jumpType, GdJumpType.LINK)) {
                    isNeedCheckGame = true;
                }
                if (isNeedCheckGame) {
                    String s = GdJumpType.GAME.parseLaunchUrl(button.getJumpUrl());
                    Assert.hasText(s, "游戏id不能为空");
                    Assert.isTrue(Objects.equals(game.getGameBaseId().toString(), s), "游戏id必须和排期绑定的游戏id一致");
                }
            }

            //校验卡片部分的游戏是否排期一致
            GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());
            if (Objects.equals(jumpType, GdJumpType.GAME)) {
                String s = GdJumpType.GAME.parseLaunchUrl(creative.getPromotionPurposeContent());
                Assert.hasText(s, "卡片游戏id不能为空");
                Assert.isTrue(Objects.equals(game.getGameBaseId().toString(), s), "卡片游戏id必须和排期绑定的游戏id一致");
            }
        }

    }


    /**
     * 验证背景色
     *
     * @param backGroundColor
     * @return 返回验证且组装好的背景色
     */
    private BackGroundColorBo validateBackgroundColor(BackGroundColorBo backGroundColor) {
        if (Objects.isNull(backGroundColor) || Objects.isNull(backGroundColor.getCode())) {
            return null;
        }
        BackgroundColorEnum backgroundColorEnum = BackgroundColorEnum.getByCodeWithValidation(backGroundColor.getCode());
        BackGroundColorBo.BackGroundColorBoBuilder builder = BackGroundColorBo.builder().code(backgroundColorEnum.getCode());
        if (Objects.equals(backgroundColorEnum, BackgroundColorEnum.CUSTOMIZED)) {
            //自定义
            BackgroundColorType.getByCodeWithValidation(backGroundColor.getType());
            BackgroundColorEnum.validateColor(backGroundColor.getNightModeColor());
            BackgroundColorEnum.validateColor(backGroundColor.getNormalModeColor());
            builder
                    .type(backGroundColor.getType())
                    .name(backgroundColorEnum.getName())
                    .normalModeColor(backGroundColor.getNormalModeColor())
                    .nightModeColor(backGroundColor.getNightModeColor());
        } else {
            builder
                    .type(backgroundColorEnum.getColorType())
                    .name(backgroundColorEnum.getName())
                    .normalModeColor(backgroundColorEnum.getNormalModeColor())
                    .nightModeColor(backgroundColorEnum.getNightModeColor());
        }
        return builder.build();
    }

}
