package com.bilibili.cpt.platform.biz.service.creative;

import com.bilibili.brand.biz.creative.dao.GdCreativeImageDao;
import com.bilibili.brand.biz.creative.po.GdCreativeImagePo;
import com.bilibili.brand.biz.creative.po.GdCreativeImagePoExample;
import com.bilibili.cpt.platform.api.creative.dto.cpt.live.LiveCptImageDto;
import com.bilibili.cpt.platform.api.creative.service.ILiveCptCreativeMaterialService;
import com.bilibili.cpt.platform.biz.converter.LiveCptCreativeServiceConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/10
 */
@Service
public class LiveCptCreativeMaterialService implements ILiveCptCreativeMaterialService {

    @Autowired
    private GdCreativeImageDao gdCreativeImageDao;

    @Override
    public void saveImage(long creativeId, LiveCptImageDto image) {
        if (image == null) {
            return;
        }
        Assert.isTrue(StringUtils.isNotBlank(image.getImageUrl()), "图片url不能为空");
        Assert.isTrue(StringUtils.isNotBlank(image.getImageHash()), "图片hash不能为空");

        GdCreativeImagePo record = LiveCptCreativeServiceConverter.MAPPER.toImagePo(image);
        record.setCreativeId(creativeId);

        insertOrUpdateImage(record);
    }

    @Override
    public LiveCptImageDto getImage(long creativeId) {
        return LiveCptCreativeServiceConverter.MAPPER.toImageDto(queryImage(creativeId));
    }

    private GdCreativeImagePo queryImage(long creativeId) {
        GdCreativeImagePoExample example = new GdCreativeImagePoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(0);
        List<GdCreativeImagePo> imagePos = gdCreativeImageDao.selectByExample(example);
        return CollectionUtils.isEmpty(imagePos) ? null : imagePos.get(0);
    }

    private void insertOrUpdateImage(GdCreativeImagePo record) {
        long creativeId = record.getCreativeId();
        GdCreativeImagePoExample example = new GdCreativeImagePoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(0);
        List<GdCreativeImagePo> imagePos = gdCreativeImageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(imagePos)) {
            gdCreativeImageDao.insertSelective(record);
        } else {
            GdCreativeImagePo oldPo = imagePos.get(0);
            if (!oldPo.getImageMd5().equals(record.getImageMd5())) {
                gdCreativeImageDao.updateByExampleSelective(record, example);
            }
        }
    }
}
