package com.bilibili.cpt.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CptOrderBusinessOrderMappingPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CptOrderBusinessOrderMappingPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdIsNull() {
            addCriterion("cpt_order_id is null");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdIsNotNull() {
            addCriterion("cpt_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdEqualTo(Integer value) {
            addCriterion("cpt_order_id =", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdNotEqualTo(Integer value) {
            addCriterion("cpt_order_id <>", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdGreaterThan(Integer value) {
            addCriterion("cpt_order_id >", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cpt_order_id >=", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdLessThan(Integer value) {
            addCriterion("cpt_order_id <", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("cpt_order_id <=", value, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdIn(List<Integer> values) {
            addCriterion("cpt_order_id in", values, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdNotIn(List<Integer> values) {
            addCriterion("cpt_order_id not in", values, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("cpt_order_id between", value1, value2, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andCptOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("cpt_order_id not between", value1, value2, "cptOrderId");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberIsNull() {
            addCriterion("business_number is null");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberIsNotNull() {
            addCriterion("business_number is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberEqualTo(Long value) {
            addCriterion("business_number =", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberNotEqualTo(Long value) {
            addCriterion("business_number <>", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberGreaterThan(Long value) {
            addCriterion("business_number >", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("business_number >=", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberLessThan(Long value) {
            addCriterion("business_number <", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberLessThanOrEqualTo(Long value) {
            addCriterion("business_number <=", value, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberIn(List<Long> values) {
            addCriterion("business_number in", values, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberNotIn(List<Long> values) {
            addCriterion("business_number not in", values, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberBetween(Long value1, Long value2) {
            addCriterion("business_number between", value1, value2, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andBusinessNumberNotBetween(Long value1, Long value2) {
            addCriterion("business_number not between", value1, value2, "businessNumber");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Timestamp value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Timestamp value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Timestamp value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Timestamp value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Timestamp> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Timestamp> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Timestamp value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Timestamp value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Timestamp value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Timestamp value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Timestamp> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Timestamp> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andAvidIsNull() {
            addCriterion("avid is null");
            return (Criteria) this;
        }

        public Criteria andAvidIsNotNull() {
            addCriterion("avid is not null");
            return (Criteria) this;
        }

        public Criteria andAvidEqualTo(Long value) {
            addCriterion("avid =", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidNotEqualTo(Long value) {
            addCriterion("avid <>", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidGreaterThan(Long value) {
            addCriterion("avid >", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidGreaterThanOrEqualTo(Long value) {
            addCriterion("avid >=", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidLessThan(Long value) {
            addCriterion("avid <", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidLessThanOrEqualTo(Long value) {
            addCriterion("avid <=", value, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidIn(List<Long> values) {
            addCriterion("avid in", values, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidNotIn(List<Long> values) {
            addCriterion("avid not in", values, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidBetween(Long value1, Long value2) {
            addCriterion("avid between", value1, value2, "avid");
            return (Criteria) this;
        }

        public Criteria andAvidNotBetween(Long value1, Long value2) {
            addCriterion("avid not between", value1, value2, "avid");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdIsNull() {
            addCriterion("gd_order_id is null");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdIsNotNull() {
            addCriterion("gd_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdEqualTo(Integer value) {
            addCriterion("gd_order_id =", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdNotEqualTo(Integer value) {
            addCriterion("gd_order_id <>", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdGreaterThan(Integer value) {
            addCriterion("gd_order_id >", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("gd_order_id >=", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdLessThan(Integer value) {
            addCriterion("gd_order_id <", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("gd_order_id <=", value, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdIn(List<Integer> values) {
            addCriterion("gd_order_id in", values, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdNotIn(List<Integer> values) {
            addCriterion("gd_order_id not in", values, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("gd_order_id between", value1, value2, "gdOrderId");
            return (Criteria) this;
        }

        public Criteria andGdOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("gd_order_id not between", value1, value2, "gdOrderId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}