package com.bilibili.cpt.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandTemplateLabelPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 标签key
     */
    private String labelKey;

    /**
     * value的数值类型,BOOLEAN = 1;NUMBER = 2;STRING = 3;ENUM = 4;OBJECT = 5;ARRAY = 6
     */
    private Integer labelValueType;

    /**
     * value_type的子类型（仅数组有效）,BOOLEAN = 1;NUMBER = 2;STRING = 3;ENUM = 4;OBJECT = 5
     */
    private Integer labelValueSubType;

    /**
     * value的定义域
     */
    private String labelValueDomain;

    /**
     * 标签描述
     */
    private String labelDescription;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}