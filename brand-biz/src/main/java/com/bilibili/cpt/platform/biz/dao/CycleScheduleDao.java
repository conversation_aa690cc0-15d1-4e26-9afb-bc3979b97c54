package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CycleSchedulePo;
import com.bilibili.cpt.platform.biz.po.CycleSchedulePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CycleScheduleDao {
    long countByExample(CycleSchedulePoExample example);

    int deleteByExample(CycleSchedulePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CycleSchedulePo record);

    int insertBatch(List<CycleSchedulePo> records);

    int insertUpdateBatch(List<CycleSchedulePo> records);

    int insert(CycleSchedulePo record);

    int insertUpdateSelective(CycleSchedulePo record);

    int insertSelective(CycleSchedulePo record);

    List<CycleSchedulePo> selectByExample(CycleSchedulePoExample example);

    CycleSchedulePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CycleSchedulePo record, @Param("example") CycleSchedulePoExample example);

    int updateByExample(@Param("record") CycleSchedulePo record, @Param("example") CycleSchedulePoExample example);

    int updateByPrimaryKeySelective(CycleSchedulePo record);

    int updateByPrimaryKey(CycleSchedulePo record);
}