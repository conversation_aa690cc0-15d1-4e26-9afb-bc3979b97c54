package com.bilibili.cpt.platform.biz.handler.booking.strategy.impl;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeDto;
import com.bilibili.cpt.platform.api.creative.dto.QueryCreativeParamDto;
import com.bilibili.cpt.platform.api.creative.service.IQueryCptCreativeService;
import com.bilibili.cpt.platform.api.log.dto.CptLogOperationDto;
import com.bilibili.cpt.platform.api.order.service.ICptOrderService;
import com.bilibili.cpt.platform.api.schedule.service.ICptScheduleService;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.cpt.platform.biz.handler.booking.strategy.CptResourceBookingReleaseStrategy;
import com.bilibili.cpt.platform.common.CptCreativeStatus;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.LogOperateType;
import com.bilibili.crm.platform.soa.ISoaCrmOrderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import redis.clients.jedis.JedisCluster;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2023/3/27 16:58
 */
@Slf4j
public abstract class AbstractCptResourceBookingReleaseStrategy implements CptResourceBookingReleaseStrategy {
    @Autowired
    protected IResourceBookingService resourceBookingService;

    @Autowired
    protected ICptOrderService cptOrderService;

    @Autowired
    protected ICptScheduleService scheduleService;

    @Autowired
    protected IQueryCptCreativeService cptCreativeService;

    @Autowired
    protected ISoaCrmOrderService crmOrderService;

    @Autowired
    protected JedisCluster redisClient;

    @Autowired
    private IGdLogService logService;

    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final int EXPIRE_TIME = (int) Duration.ofDays(1).getSeconds();


    protected abstract boolean doMatch(ReleaseRequestBean releaseRequest);

    @Override
    public boolean match(ReleaseRequestBean releaseRequest) {
        return Objects.equals(releaseRequest.getResourceType(), ReleaseRequestBean.ResourceType.CPT) && doMatch(releaseRequest);
    }

    /**
     * 某策略当前执行的次数
     */
    protected int addCounter(String prefix, ReleaseRequestBean.TriggerType triggerType, LocalDateTime localDateTime) {
        String key = String.format("%s_%s_%s", prefix, triggerType.name(), localDateTime.format(dateTimeFormatter));
        long value = this.redisClient.incr(key);
        if (value == 1) {
            this.redisClient.expire(key, EXPIRE_TIME);
        }
        return (int) value;
    }


    /**
     * 某策略当前执行的次数
     */
    protected int getCounter(String prefix, ReleaseRequestBean.TriggerType triggerType, LocalDateTime localDateTime) {
        String key = String.format("%s_%s_%s", prefix, triggerType.name(), localDateTime.format(dateTimeFormatter));
        String value = this.redisClient.get(key);
        return StringUtils.isEmpty(value) ? 0 : Integer.parseInt(value);
    }


    /**
     * 是否存在审核通过的创意
     *
     * @param scheduleId
     * @return
     */
    protected boolean hasAuditPassedCreative(Integer scheduleId) {
        List<CptCreativeDto> creatives = cptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .scheduleId(scheduleId)
                .cptCreativeStatus(CptCreativeStatus.AUDIT_PASS.getCode())
                .build());
        return !CollectionUtils.isEmpty(creatives);
    }


    /**
     * 是否存在有效创意
     *
     * @param scheduleId
     * @return
     */
    protected boolean hasValidCreative(Integer scheduleId) {
        List<CptCreativeDto> creatives = cptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .scheduleId(scheduleId)
                .cptCreativeStatusList(Lists.newArrayList(
                        CptCreativeStatus.AUDIT_PASS.getCode(),
                        CptCreativeStatus.TO_BE_AUDIT.getCode(),
                        CptCreativeStatus.AUDIT_REJECT.getCode(),
                        CptCreativeStatus.WAIT_OFF_LINE.getCode()))
                .build());
        return !CollectionUtils.isEmpty(creatives);
    }

    /**
     * 是否存在审核通过的创意
     *
     * @param scheduleId
     * @return
     */
    protected boolean hasValidAuditPassedCreative(Integer scheduleId) {
        List<CptCreativeDto> creatives = cptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .scheduleId(scheduleId)
                .cptCreativeStatusList(Lists.newArrayList(CptCreativeStatus.AUDIT_PASS.getCode()))
                .build());
        return !CollectionUtils.isEmpty(creatives);
    }


    /**
     * 是否编辑过
     *
     * @param scheduleId
     * @return
     */
    protected boolean isExistUpdated(Integer scheduleId) {
        try {
            PageResult<CptLogOperationDto> updateLogs = this.logService.getLogsByObjId(
                    String.valueOf(scheduleId), GdLogFlag.SCHEDULE,
                    LogOperateType.UPDATE_SCHEDULE, 1, 1);
            return updateLogs.getTotal() > 0;
        } catch (ServiceException e) {
            log.error("[AbstractCptResourceBookingReleaseStrategy] getLogsByObjId error", e);
            //如果查询日志失败，则认为不存在编辑记录
        }
        return false;
    }
}
