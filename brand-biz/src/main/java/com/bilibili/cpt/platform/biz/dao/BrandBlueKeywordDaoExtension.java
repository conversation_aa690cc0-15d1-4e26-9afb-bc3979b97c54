package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.BrandBlueKeywordAggExtensionPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 22:30
 */
public interface BrandBlueKeywordDaoExtension {

    List<BrandBlueKeywordAggExtensionPo> selectBlueKeywordItemCount(@Param("configIdList") List<Long> configIdList,
                                                                    @Param("statusList") List<Integer> statusList);

    List<BrandBlueKeywordAggExtensionPo> selectBlueKeywordArchiveCount(@Param("configIdList") List<Long> configIdList,
                                                                       @Param("statusList") List<Integer> statusList);

}
