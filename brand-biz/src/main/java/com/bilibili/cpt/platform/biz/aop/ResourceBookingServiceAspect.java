//package com.bilibili.cpt.platform.biz.aop;
//
//import com.bilibili.adp.common.bean.Operator;
//import com.bilibili.brand.api.booking.dto.BookingItemDto;
//import com.bilibili.brand.api.booking.dto.NewResourceBookingDto;
//import com.bilibili.brand.api.booking.dto.UpdateScheduleBookingDto;
//import com.bilibili.brand.api.log.service.IGdLogService;
//import com.bilibili.brand.biz.log.bean.ResourceBookingLogBean;
//import com.bilibili.cpt.platform.common.GdLogFlag;
//import com.bilibili.cpt.platform.common.LogOperateType;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.annotation.AfterReturning;
//import org.aspectj.lang.annotation.Aspect;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * @description:
// * @author: wangbin01
// * @create: 2019-01-30
// **/
//@Aspect
//@Slf4j
//@Component
//public class ResourceBookingServiceAspect {
//
//    @Autowired
//    private IGdLogService logService;
//
//
//    @AfterReturning("execution(* com.bilibili.cpt.platform.biz.service.booking.ResourceBookingService.scheduleTheResourceBooking(..)) && args(updateDto)")
//    public void log4ScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto){
//        ResourceBookingLogBean logBean = ResourceBookingLogBean.builder()
//                .businessSideId(updateDto.getBusinessSideId())
//                .sourceId(updateDto.getSourceId())
//                .beginDate(updateDto.getBeginDate())
//                .endDate(updateDto.getEndDate())
//                .orderId(updateDto.getOrderId())
//                .scheduleId(updateDto.getScheduleId())
//                .build();
//        logService.insertLog(GdLogFlag.BOOKING.getCode(), GdLogFlag.BOOKING, LogOperateType.UPDATE_SCHEDULE_BOOKING, updateDto.getOperator(), logBean);
//        log.info("insert log for schedule cpt booking");
//    }
//
//    @AfterReturning("execution(* com.bilibili.cpt.platform.biz.service.booking.ResourceBookingService.unScheduleTheResourceBooking(..)) && args(updateDto)")
//    public void log4UnScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto){
//        ResourceBookingLogBean logBean = ResourceBookingLogBean.builder()
//                .businessSideId(updateDto.getBusinessSideId())
//                .sourceId(updateDto.getSourceId())
//                .beginDate(updateDto.getBeginDate())
//                .endDate(updateDto.getEndDate())
//                .orderId(updateDto.getOrderId())
//                .scheduleId(updateDto.getScheduleId())
//                .build();
//        logService.insertLog(GdLogFlag.BOOKING.getCode(), GdLogFlag.BOOKING, LogOperateType.UPDATE_UNSCHEDULED_BOOKING, updateDto.getOperator(), logBean);
//        log.info("insert log for unscheduled cpt booking");
//    }
//}
