package com.bilibili.cpt.platform.biz.service.business_side;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.booking.dto.BookingItemCountQueryDto;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.biz.log.bean.BusinessSideLogBean;
import com.bilibili.brand.biz.log.bean.SourceConfigLogBean;
import com.bilibili.cpt.platform.api.business_side.dto.*;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.business_side.service.ICptUserService;
import com.bilibili.cpt.platform.api.location.dto.CptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceBusinessSideLimitDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceBusinessSidePreBookingDto;
import com.bilibili.cpt.platform.api.location.dto.SourceConfigDto;
import com.bilibili.cpt.platform.api.location.service.ICptCycleService;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.biz.dao.*;
import com.bilibili.cpt.platform.biz.po.*;
import com.bilibili.cpt.platform.common.BusinessSideType;
import com.bilibili.cpt.platform.common.CptConstants;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.LogOperateType;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.ssa.platform.api.business_side.service.ISsaBusinessSideService;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017/6/13
 */
@Service
public class BusinessSideService implements IBusinessSideService {
    private final static Logger LOGGER = LoggerFactory.getLogger(BusinessSideService.class);
    private final static Integer MAX_BUSINESS_SIDE_NAME_LENGTH = 20;
    @Autowired
    CptBusinessSideDao cptBusinessSideDao;
    @Autowired
    CptBusinessSideRedPacketDao cptBusinessSideRedPacketDao;
    @Autowired
    CptBusinessSideRedPacketLimitDao cptBusinessSideRedPacketLimitDao;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ICptUserService cptUserService;
    @Autowired
    private CptUserBusinessSideMappingDao cptUserBusinessSideMappingDao;
    @Autowired
    private CptBusinessSideRotationConfigDao cptBusinessSideRotationConfigDao;
    @Autowired
    private LocalCptBusinessSideRotationConfigDao localCptBusinessSideRotationConfigDao;
    @Autowired
    private CptBusinessSideCashRechargeDao cptBusinessSideCashRechargeDao;
    @Autowired
    private LocalCptUserBusinessSideMappingDao localCptUserBusinessSideMappingDao;
    @Autowired
    private CptSourcePrivilegeDao cptSourcePrivilegeDao;
    @Autowired
    private LocalCptSourcePrivilegeDao localCptSourcePrivilegeDao;
    @Autowired
    private ICptCycleService cycleService;

    @Autowired
    private IQuerySourceService querySourceService;
    @Autowired
    private IGdLogService gdLogService;

    @Autowired
    private BusinessSideDelegate businessSideDelegate;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private ISsaBusinessSideService ssaBusinessSideService;
    @Autowired
    private CptBusinessSidePreBookingConfigDao cptBusinessSidePreBookingConfigDao;
    @Autowired
    @Lazy
    private ICptSourceService cptSourceService;
    @Autowired
    private IResourceBookingService resourceBookingService;

    @Override
    public Map<Integer, String> getAccountId2BusinessSideNameMapInAccountIds(List<Integer> accountIds) {
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(Status.VALID.getCode());
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        return cptBusinessSidePos.stream().collect(Collectors.toMap(CptBusinessSidePo::getAccountId, CptBusinessSidePo::getName));
    }

    @Override
    public List<BusinessSideBaseDto> getBusinessSideBaseDtos() {
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(Status.VALID.getCode());
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        if (CollectionUtils.isEmpty(cptBusinessSidePos)) {
            return Collections.emptyList();
        }
        return cptBusinessSidePos.stream().map(cptBusinessSidePo -> {
            BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
            BeanUtils.copyProperties(cptBusinessSidePo, businessSideBaseDto);
            return businessSideBaseDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BusinessSideBaseDto> getBusinessSidesByOperatorName(String operatorName) {
        Assert.notNull(operatorName);
        CptUserDto userDto = cptUserService.getUserByName(operatorName);
        CptUserBusinessSideMappingPoExample example = new CptUserBusinessSideMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUserIdEqualTo(userDto.getId());
        List<CptUserBusinessSideMappingPo> cptUserBusinessSideMappingPos = cptUserBusinessSideMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(cptUserBusinessSideMappingPos)) {
            return Collections.emptyList();
        }
        List<Integer> businessSideIds = cptUserBusinessSideMappingPos.stream().map(CptUserBusinessSideMappingPo::getBusinessSideId).collect(Collectors.toList());
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(businessSideIds).andStatusEqualTo(Status.VALID.getCode());
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        if (CollectionUtils.isEmpty(cptBusinessSidePos)) {
            return Collections.emptyList();
        }
        return cptBusinessSidePos.stream().map(cptBusinessSidePo -> {
            BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
            BeanUtils.copyProperties(cptBusinessSidePo, businessSideBaseDto);
            return businessSideBaseDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<BusinessSideDto> getAllValidBusinessSidesByOperatorName(String operatorName) {
        return null;
    }

    @Override
    public BusinessSideBaseDto getValidBusinessSideById(Integer businessSideId) {
        Assert.notNull(businessSideId);
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdEqualTo(businessSideId).andStatusEqualTo(Status.VALID.getCode());
        return getBusinessSideById(cptBusinessSidePoExample);
    }

    private BusinessSideBaseDto getBusinessSideById(CptBusinessSidePoExample cptBusinessSidePoExample) {
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        if (CollectionUtils.isEmpty(cptBusinessSidePos)) {
            return null;
        }
        BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
        BeanUtils.copyProperties(cptBusinessSidePos.get(0), businessSideBaseDto);
        return businessSideBaseDto;
    }

    @Override
    public BusinessSideBaseDto getBusinessSideById(Integer businessSideId) {
        Assert.notNull(businessSideId);
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdEqualTo(businessSideId);
        return getBusinessSideById(cptBusinessSidePoExample);
    }

    @Override
    public BusinessSideBaseDto getBusinessSideByAccountId(Integer accountId) {
        Assert.notNull(accountId, "accountId can not be null");
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(example);
        Assert.notEmpty(cptBusinessSidePos, "请联系管理员绑定一个业务方");
        Assert.isTrue(cptBusinessSidePos.size() == 1, "一个商业账号只能有一个业务方，请联系管理员处理");
        BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
        BeanUtils.copyProperties(cptBusinessSidePos.get(0), businessSideBaseDto);
        return businessSideBaseDto;
    }

    @Override
    public boolean checkBusinessSide(Integer accountId) {
        if (!Utils.isPositive(accountId)) {
            return false;
        }
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(example);
        if (CollectionUtils.isEmpty(cptBusinessSidePos)) {
            return false;
        }
        return true;
    }

    @Override
    public Map<Integer, BusinessSideBaseDto> getBusinessSideMapInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(ids);
        List<CptBusinessSidePo> cptBusinessSidePos = cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        if (CollectionUtils.isEmpty(cptBusinessSidePos)) {
            return Collections.emptyMap();
        }
        return cptBusinessSidePos.stream().collect(Collectors.toMap(CptBusinessSidePo::getId, cptBusinessSidePo -> {
            BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
            BeanUtils.copyProperties(cptBusinessSidePo, businessSideBaseDto);
            return businessSideBaseDto;
        }));
    }

    @Override
    public List<Integer> getSourceIdsByBuSideId(Integer id) {
        Assert.notNull(id);
        CptSourcePrivilegePoExample example = new CptSourcePrivilegePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdEqualTo(id);
        List<CptSourcePrivilegePo> pos = cptSourcePrivilegeDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CptSourcePrivilegePo::getSourceId).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Map<Integer, Integer>> getCycleBuSourceMFreqLimitMap(Integer buId, List<Integer> sourceIds) {
        Assert.notNull(buId);
        Assert.notEmpty(sourceIds);
        CptBusinessSideRotationConfigPoExample example = new CptBusinessSideRotationConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andBusinessSideIdEqualTo(buId)
                .andSourceIdIn(sourceIds);
        List<CptBusinessSideRotationConfigPo> pos = cptBusinessSideRotationConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.groupingBy(CptBusinessSideRotationConfigPo::getCycleId,
                Collectors.toMap(CptBusinessSideRotationConfigPo::getSourceId,
                        CptBusinessSideRotationConfigPo::getMRotationLimit)));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void charge(Integer buId, Long cash, Long redPacket) {
        Assert.notNull(buId);
        Assert.notNull(cash);
        Assert.notNull(redPacket);
        CptBusinessSidePo cptBusinessSidePo = businessSideDelegate.getById(buId);
        Assert.notNull(cptBusinessSidePo, "业务方不存在");
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andIdEqualTo(buId).andVersionEqualTo(cptBusinessSidePo.getVersion());

        CptBusinessSidePo update = new CptBusinessSidePo();
        Assert.isTrue(cptBusinessSidePo.getCash() + cash >= 0, "现金余额不足");
        Assert.isTrue(cptBusinessSidePo.getRedPacket() + redPacket >= 0, "红包余额不足");

        update.setVersion(cptBusinessSidePo.getVersion() + 1);
        update.setCash(cptBusinessSidePo.getCash() + cash);
        update.setRedPacket(cptBusinessSidePo.getRedPacket() + redPacket);

        int result = cptBusinessSideDao.updateByExampleSelective(update, example);
        Assert.isTrue(result > 0, "修改金额失败，请稍后重试");
    }

    @Override
    public void initRedPacket(Integer buId) {
        Assert.notNull(buId);
        RLock buLock = redissonClient.getLock(buId + CptConstants.BU_SIDE_LOCK_SUFFIX);
        try {
            buLock.lock();
            businessSideDelegate.initRedPacket(buId);
        } finally {
            buLock.unlock();
        }
    }

    @Override
    public void copyInsertBusinessSideLimitByCycleId(Integer lastCycleId, Integer currentCycleId) {
        Assert.notNull(lastCycleId, "上次刊例周期ID不可为空");
        Assert.notNull(currentCycleId, "当前刊例周期ID不可为空");
        LOGGER.info("copyInsertBusinessSideLimitByCycleId param lastCycleId {}, currentCycleId {}", lastCycleId, currentCycleId);

        List<CptBusinessSideRotationConfigPo> businessSideRotationConfigPos = getCptBusinessSideRotationConfigPosByCycleId(lastCycleId);
        if (!CollectionUtils.isEmpty(businessSideRotationConfigPos)) {
            businessSideRotationConfigPos = businessSideRotationConfigPos.stream().map(po -> {
                po.setCycleId(currentCycleId);
                return po;
            }).collect(Collectors.toList());
            localCptBusinessSideRotationConfigDao.batchSave(businessSideRotationConfigPos);
        } else {
            LOGGER.info("copyInsertBusinessSideLimitByCycleId lastCycleId {}, businessSideRotationConfig is null.");
        }
    }


    public void copyInsertBusinessSidPreBookingByCycleId(Integer lastCycleId, Integer currentCycleId) {
        Assert.notNull(lastCycleId, "上次刊例周期ID不可为空");
        Assert.notNull(currentCycleId, "当前刊例周期ID不可为空");
        LOGGER.info("copyInsertBusinessSideLimitByCycleId param lastCycleId {}, currentCycleId {}", lastCycleId, currentCycleId);

        List<CptBusinessSidePreBookingConfigPo> businessSidePreBookingConfigPos = getCptBusinessSidePreBookingPosByCycleId(lastCycleId);
        if (!CollectionUtils.isEmpty(businessSidePreBookingConfigPos)) {
            businessSidePreBookingConfigPos.stream().map(po -> {
                po.setCycleId(currentCycleId);
                cptBusinessSidePreBookingConfigDao.insertUpdateSelective(po);
                return po;
            }).collect(Collectors.toList());
        } else {
            LOGGER.info("copyInsertBusinessSidPreBookingByCycleId lastCycleId {}, businessSidePreBookingConfig is null.");
        }
    }

    private List<CptBusinessSideRotationConfigPo> getCptBusinessSideRotationConfigPosByCycleId(Integer cycleId) {
        Assert.notNull(cycleId);
        CptBusinessSideRotationConfigPoExample example = new CptBusinessSideRotationConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andCycleIdEqualTo(cycleId);
        return cptBusinessSideRotationConfigDao.selectByExample(example);
    }

    private List<CptBusinessSidePreBookingConfigPo> getCptBusinessSidePreBookingPosByCycleId(Integer cycleId) {
        Assert.notNull(cycleId);
        CptBusinessSidePreBookingConfigPoExample example = new CptBusinessSidePreBookingConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(cycleId);
        return cptBusinessSidePreBookingConfigDao.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer addBusinessSideLimit(Operator operator, Integer businessSideId, Integer cycleId, Integer sourceId,
                                        Integer mRotationLimit) {

        validateBusinessSideSourceConfig(operator, businessSideId, cycleId, sourceId, mRotationLimit);
        CptBusinessSidePo businessSidePo = this.validateAndGetValidBusinessSide(businessSideId);

        CptCycleDto cycleDto = cycleService.getCycleDtoById(cycleId);
        SourceAllInfoDto sourceInfo = querySourceService.getCptSourceBySourceId(sourceId);
        Assert.notNull(sourceInfo, "该位次已不支持CPT投放，不可再操作业务方限制");

        // 校验设定的月预定轮播数上限 >= 业务方在当前资源位已预定的量
        // 获取业务方在当前资源位已预定的量
        Integer hasBookingCount = (int) resourceBookingService.countBookingItem(BookingItemCountQueryDto.builder()
                .cycleId(cycleId)
                .sourceId(sourceId)
                .businessSideId(businessSideId)
                .build());
        Assert.isTrue(mRotationLimit >= hasBookingCount, "月预定轮播数上限不可小于业务方在当前资源位已预定量");

        LOGGER.info("BusinessSideService.addBusinessSideLimit operator {}, businessSideId {}, sourceId {}, mRotationLimit {}", operator, businessSideId, sourceId, mRotationLimit);

        CptBusinessSideRotationConfigPo cptBusinessSideRotationConfigPo = new CptBusinessSideRotationConfigPo();
        cptBusinessSideRotationConfigPo.setCycleId(cycleId);
        cptBusinessSideRotationConfigPo.setBusinessSideId(businessSideId);
        cptBusinessSideRotationConfigPo.setSourceId(sourceId);
        cptBusinessSideRotationConfigPo.setMRotationLimit(mRotationLimit);
        cptBusinessSideRotationConfigPo.setStatus(Status.VALID.getCode());
        localCptBusinessSideRotationConfigDao.save(cptBusinessSideRotationConfigPo);

        SourceConfigLogBean logBean = SourceConfigLogBean.builder().build();
        BeanUtils.copyProperties(cptBusinessSideRotationConfigPo, logBean);
        logBean.setCycleId(cycleId);
        logBean.setCycleName(cycleDto.getName());
        logBean.setBusinessSideName(businessSidePo.getName());
        logBean.setSourceName(sourceInfo == null ? "--" : sourceInfo.getName());
        gdLogService.insertLog(sourceId, GdLogFlag.SOURCE_CONFIG, LogOperateType.ADD_BUSINESS_SIDE_LIMIT, operator, logBean);
        return cptBusinessSideRotationConfigPo.getId();
    }

    private void validateBusinessSideSourceConfig(Operator operator, Integer businessSideId, Integer cycleId,
                                                  Integer sourceId, Integer mRotationLimit) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notNull(mRotationLimit, "月预定轮播数上限不可为空");
        Assert.isTrue(mRotationLimit > 0, "月预定轮播数上限必须大于0");
        Assert.notNull(cycleId, "周期ID不可为空");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteBusinessSideLimit(Operator operator, Integer limitId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(limitId, "业务方限制ID不可为空");
        CptBusinessSideRotationConfigPo cptBusinessSideRotationConfigPo = cptBusinessSideRotationConfigDao.selectByPrimaryKey(limitId);
        Assert.notNull(cptBusinessSideRotationConfigPo, "该业务方限制不存在");
        Assert.isTrue(Status.VALID.equals(Status.getByCode(cptBusinessSideRotationConfigPo.getStatus())), "该业务方限制已经被删除");

        CptBusinessSideRotationConfigPo businessSideRotationConfigPo = new CptBusinessSideRotationConfigPo();
        businessSideRotationConfigPo.setId(limitId);
        businessSideRotationConfigPo.setStatus(Status.INVALID.getCode());
        cptBusinessSideRotationConfigDao.updateByPrimaryKeySelective(businessSideRotationConfigPo);

        SourceAllInfoDto sourceAllInfo = querySourceService.getCptSourceBySourceId(cptBusinessSideRotationConfigPo.getSourceId());
        Assert.notNull(sourceAllInfo, "该位次已不支持CPT投放，不可再操作业务方限制");
        CptBusinessSidePo businessSidePo = cptBusinessSideDao.selectByPrimaryKey(cptBusinessSideRotationConfigPo.getBusinessSideId());
        CptCycleDto cycleDto = cycleService.getCycleDtoById(cptBusinessSideRotationConfigPo.getCycleId());

        SourceConfigLogBean logBean = SourceConfigLogBean.builder().build();
        BeanUtils.copyProperties(cptBusinessSideRotationConfigPo, logBean);
        logBean.setCycleId(cycleDto.getId());
        logBean.setCycleName(cycleDto.getName());
        logBean.setSourceName(sourceAllInfo.getName());
        logBean.setBusinessSideName(businessSidePo.getName());
        gdLogService.insertLog(sourceAllInfo.getSourceId(), GdLogFlag.SOURCE_CONFIG, LogOperateType.DELETE_BUSINESS_SIDE_LIMIT, operator, logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBusinessSideLimit(Operator operator, Integer limitId, Integer mRotationLimit) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(limitId, "业务方限制ID不可为空");
        CptBusinessSideRotationConfigPo cptBusinessSideRotationConfigPo = cptBusinessSideRotationConfigDao.selectByPrimaryKey(limitId);
        Assert.notNull(cptBusinessSideRotationConfigPo, "该业务方限制不存在");

        this.validateAndGetValidBusinessSide(cptBusinessSideRotationConfigPo.getBusinessSideId());

        Assert.isTrue(Status.VALID.equals(Status.getByCode(cptBusinessSideRotationConfigPo.getStatus())), "该业务方限制已经被删除,不可更改");
        Assert.isTrue(!cptBusinessSideRotationConfigPo.getMRotationLimit().equals(mRotationLimit), "您没有做任何改变");

        CptBusinessSideRotationConfigPo businessSideRotationConfigPo = new CptBusinessSideRotationConfigPo();
        businessSideRotationConfigPo.setId(limitId);
        businessSideRotationConfigPo.setMRotationLimit(mRotationLimit);
        cptBusinessSideRotationConfigDao.updateByPrimaryKeySelective(businessSideRotationConfigPo);

        SourceAllInfoDto sourceAllInfo = querySourceService.getCptSourceBySourceId(cptBusinessSideRotationConfigPo.getSourceId());
        CptBusinessSidePo businessSidePo = cptBusinessSideDao.selectByPrimaryKey(cptBusinessSideRotationConfigPo.getBusinessSideId());
        CptCycleDto cycleDto = cycleService.getCycleDtoById(cptBusinessSideRotationConfigPo.getCycleId());

        SourceConfigLogBean logBean = SourceConfigLogBean.builder().build();
        BeanUtils.copyProperties(businessSideRotationConfigPo, logBean);
        logBean.setCycleId(cycleDto.getId());
        logBean.setCycleName(cycleDto.getName());
        logBean.setSourceName(sourceAllInfo.getName());
        logBean.setBusinessSideName(businessSidePo.getName());
        gdLogService.insertLog(sourceAllInfo.getSourceId(), GdLogFlag.SOURCE_CONFIG, LogOperateType.UPDATE_BUSINESS_SIDE_LIMIT, operator, logBean);
    }

    @Override
    public List<CptSourceBusinessSideLimitDto> getBusinessSideLimitBySourceId(Integer cycleId, Integer sourceId) {
        return getCptSourceBusinessSideLimitDtos(cycleId, sourceId, null);
    }

    private List<CptSourceBusinessSideLimitDto> getCptSourceBusinessSideLimitDtos(Integer cycleId, Integer sourceId, Integer businessSideId) {
        Assert.notNull(cycleId, "刊例周期ID不可为空");
        Assert.notNull(sourceId, "位次ID不可为空");
        CptBusinessSideRotationConfigPoExample example = new CptBusinessSideRotationConfigPoExample();
        CptBusinessSideRotationConfigPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andSourceIdEqualTo(sourceId)
                .andCycleIdEqualTo(cycleId);

        if (Utils.isPositive(businessSideId)) {
            criteria.andBusinessSideIdEqualTo(businessSideId);
        }

        List<CptBusinessSideRotationConfigPo> businessSideRotationConfigPos = cptBusinessSideRotationConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(businessSideRotationConfigPos)) {
            return Collections.emptyList();
        }

        Map<Integer, Integer> businessSideId2LimitIdMap = businessSideRotationConfigPos.stream().collect(Collectors.toMap(CptBusinessSideRotationConfigPo::getBusinessSideId, CptBusinessSideRotationConfigPo::getId));

        List<Integer> businessSideIds = businessSideRotationConfigPos.stream().map(CptBusinessSideRotationConfigPo::getBusinessSideId).collect(Collectors.toList());
        CptBusinessSidePoExample businessSidePoExample = new CptBusinessSidePoExample();
        businessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode()).andIdIn(businessSideIds);

        List<CptBusinessSidePo> businessSidePos = cptBusinessSideDao.selectByExample(businessSidePoExample);
        Map<Integer, String> businessSideMap = businessSidePos.stream().collect(Collectors.toMap(CptBusinessSidePo::getId, CptBusinessSidePo::getName));
        return businessSideRotationConfigPos.stream().map(po -> businessSideRotationConfigPo2Dto(po, businessSideMap, businessSideId2LimitIdMap)).collect(Collectors.toList());
    }

    @Override
    public CptSourceBusinessSideLimitDto getCptSourceBusinessSideLimitDto(Integer cycleId, Integer sourceId, Integer businessSideId) {
        Assert.notNull(businessSideId, "业务方ID不可为空");
        List<CptSourceBusinessSideLimitDto> limitDtos = getCptSourceBusinessSideLimitDtos(cycleId, sourceId, businessSideId);
        Optional<CptSourceBusinessSideLimitDto> limitDto = limitDtos.stream().min(Comparator.comparing(CptSourceBusinessSideLimitDto::getMRotationLimit));
        LOGGER.info("BusinessSideService.getCptSourceBusinessSideLimitDto:limitDto={}", limitDto);
        return limitDto.orElse(null);
    }

    private CptSourceBusinessSideLimitDto businessSideRotationConfigPo2Dto(CptBusinessSideRotationConfigPo rotationConfigPo, Map<Integer, String> businessSideMap, Map<Integer, Integer> businessSideId2LimitIdMap) {
        CptSourceBusinessSideLimitDto dto = CptSourceBusinessSideLimitDto.builder().build();
        BeanUtils.copyProperties(rotationConfigPo, dto);
        dto.setBusinessSideName(businessSideMap.getOrDefault(dto.getBusinessSideId(), "--"));
        dto.setLimitId(businessSideId2LimitIdMap.get(dto.getBusinessSideId()));
        return dto;
    }

    @Override
    public List<BusinessSideDto> getAllBusinessSides() {
        CptBusinessSidePoExample businessSidePoExample = new CptBusinessSidePoExample();
        businessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<CptBusinessSidePo> businessSidePos = cptBusinessSideDao.selectByExample(businessSidePoExample);
        if (CollectionUtils.isEmpty(businessSidePos)) {
            return Collections.emptyList();
        }
        List<Integer> businessSideIds = businessSidePos.stream().map(CptBusinessSidePo::getId).collect(Collectors.toList());
        Map<Integer, List<CptUserDto>> businessSideUserMap = cptUserService.getBusinessSideUserMapInBusinessSideIds(businessSideIds);

        Timestamp nextMonth = TimeUtils.getBeginDayOfNextMonth();
        Timestamp currentMonth = TimeUtils.getBeginDayOfCurrentMonth();
        CptBusinessSideRedPacketPoExample example = new CptBusinessSideRedPacketPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdIn(businessSideIds)
                .andEffectiveDateLessThanOrEqualTo(nextMonth);
        List<CptBusinessSideRedPacketPo> businessSideRedPacketPos = cptBusinessSideRedPacketDao.selectByExample(example);
        Map<Integer, List<CptBusinessSideRedPacketPo>> businessSideRedPacetMap = businessSideRedPacketPos.stream().collect(Collectors.groupingBy(CptBusinessSideRedPacketPo::getBusinessSideId));

        // 查询商业帐号
        List<Integer> accountIds = businessSidePos.stream().map(CptBusinessSidePo::getAccountId).collect(Collectors.toList());
        Map<Integer, AccountBaseDto> accountBaseMap = soaQueryAccountService.getAccountBaseDtoMapInIds(accountIds);

        List<BusinessSideDto> businessSideDtos = businessSidePos.stream().map(po -> this.buildBusinessSideDto(po, businessSideUserMap, businessSideRedPacetMap, accountBaseMap, nextMonth, currentMonth)).collect(Collectors.toList());
        return businessSideDtos;
    }

    @Override
    public List<Integer> getAllValidBuIds() {
        CptBusinessSidePoExample businessSidePoExample = new CptBusinessSidePoExample();
        businessSidePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(Status.VALID.getCode());

        List<CptBusinessSidePo> businessSidePos = cptBusinessSideDao.selectByExample(businessSidePoExample);
        if (CollectionUtils.isEmpty(businessSidePos)) {
            return Collections.emptyList();
        }
        return businessSidePos.stream().map(CptBusinessSidePo::getId).collect(Collectors.toList());
    }

    private BusinessSideDto buildBusinessSideDto(CptBusinessSidePo po, Map<Integer, List<CptUserDto>> businessSideUserMap, Map<Integer, List<CptBusinessSideRedPacketPo>> businessSideRedPacetMap, Map<Integer, AccountBaseDto> accountBaseMap, Timestamp nextMonth, Timestamp currentMonth) {
        BusinessSideDto dto = BusinessSideDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        dto.setInitRedPacket(0L);
        dto.setNextMonthInitRedPacket(0L);
        dto.setUserDtos(businessSideUserMap.containsKey(po.getId()) ? businessSideUserMap.get(po.getId()) : Collections.emptyList());
        AccountBaseDto accountBaseDto = accountBaseMap.get(po.getAccountId());
        if (null != accountBaseDto) {
            dto.setAccountName(accountBaseDto.getUsername());
        }
        if (businessSideRedPacetMap.containsKey(po.getId())) {
            List<CptBusinessSideRedPacketPo> redPacketPos = businessSideRedPacetMap.get(po.getId());
            RedPacketDto redPacketDto = getBuSideInitalRedPacket(currentMonth, redPacketPos);
            dto.setInitRedPacket(redPacketDto.getInitRedPacket());
            dto.setNextMonthInitRedPacket(redPacketDto.getNextMonthInitRedPacket());
        }
        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer create(Operator operator, NewBusinessSideDto newBusinessSideDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(newBusinessSideDto, "业务方信息不可为空");
        Assert.hasText(newBusinessSideDto.getName(), "业务方名称不可为空");
        Assert.isTrue(newBusinessSideDto.getName().length() <= MAX_BUSINESS_SIDE_NAME_LENGTH, "业务方名称不可超过" + MAX_BUSINESS_SIDE_NAME_LENGTH + "个字符");
        Assert.hasText(newBusinessSideDto.getLogoColor(), "业务方颜色不可为空");
        Assert.isTrue(this.validateLogColorIsValid(newBusinessSideDto.getLogoColor()), "业务方颜色不合法");
        BusinessSideType.getByCode(newBusinessSideDto.getType());

        List<Integer> sourceIds = newBusinessSideDto.getSourceIds();
        if (!CollectionUtils.isEmpty(sourceIds)) {
            Map<Integer, SourceAllInfoDto> cptSourceMap = querySourceService.getCptSourceMapInSourceIds(sourceIds);
            Assert.isTrue(cptSourceMap.keySet().size() == sourceIds.size(), "位次不合法，存在不支持CPT投放的位次");
            sourceIds = Lists.newArrayList(cptSourceMap.keySet());
        }

        // 校验accountId（商业帐号ID）的合法性
        Integer accountId = newBusinessSideDto.getAccountId();
        if (null != accountId) {
            AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(accountId);
            Assert.notNull(accountBaseDto, "此商业帐号ID[" + accountId + "]为无效的帐号, 无法保存");
        }

        long existBusinessSideCount = this.getBusinessSideCountByAccountId(newBusinessSideDto.getAccountId());
        Assert.isTrue(existBusinessSideCount == 0, "该商业账号已关联过业务方，请联系管理员");

        CptBusinessSidePo record = new CptBusinessSidePo();
        BeanUtils.copyProperties(newBusinessSideDto, record);
        record.setStatus(Status.VALID.getCode());
        cptBusinessSideDao.insertSelective(record);

        this.batchSaveBusinessSideSource(sourceIds, record.getId());

        ssaBusinessSideService.create(operator, com.bilibili.ssa.platform.api.business_side.dto.NewBusinessSideDto.builder()
                .name(newBusinessSideDto.getName())
                .departmentId(newBusinessSideDto.getDepartmentId())
                .type(newBusinessSideDto.getType())
                .logoColor(newBusinessSideDto.getLogoColor())
                .crmAccountId(newBusinessSideDto.getAccountId())
                .build());

        BusinessSideLogBean logBean = BusinessSideLogBean.builder().build();
        BeanUtils.copyProperties(record, logBean);
        logBean.setTypeDesc(BusinessSideType.getByCode(record.getType()).getDesc());
        logBean.setStatusDesc(Status.getByCode(record.getStatus()).getName());
        gdLogService.insertLog(record.getId(), GdLogFlag.BUSINESS_SIDE, LogOperateType.ADD_BUSINESS_SIDE, operator, logBean);
        return record.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(Operator operator, UpdateBusinessSideDto updateBusinessSideDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        CptBusinessSidePo businessSidePo = this.validateAndGetValidBusinessSide(updateBusinessSideDto.getId());
        Assert.isTrue(businessSidePo.getAccountId().equals(updateBusinessSideDto.getAccountId()), "商业账号ID不可更改");
        Assert.notNull(updateBusinessSideDto, "业务方信息不可为空");
        Assert.notNull(updateBusinessSideDto.getId(), "业务方ID不可为空");
        Assert.hasText(updateBusinessSideDto.getName(), "业务方名称不可为空");
        Assert.isTrue(updateBusinessSideDto.getName().length() <= MAX_BUSINESS_SIDE_NAME_LENGTH, "业务方名称不可超过" + MAX_BUSINESS_SIDE_NAME_LENGTH + "个字符");
        Assert.hasText(updateBusinessSideDto.getLogoColor(), "业务方颜色不可为空");
        Assert.isTrue(this.validateLogColorIsValid(updateBusinessSideDto.getLogoColor()), "业务方颜色不合法");

        this.deleteBusinessSideSourceByBuSideId(updateBusinessSideDto.getId());
        List<Integer> sourceIds = updateBusinessSideDto.getSourceIds();
        if (!CollectionUtils.isEmpty(sourceIds)) {
            Map<Integer, SourceAllInfoDto> cptSourceMap = querySourceService.getCptSourceMapInSourceIds(sourceIds);
            Assert.isTrue(cptSourceMap.keySet().size() == sourceIds.size(), "位次不合法，存在不支持CPT投放的位次");
            sourceIds = Lists.newArrayList(cptSourceMap.keySet());

            this.batchSaveBusinessSideSource(sourceIds, updateBusinessSideDto.getId());
        }

        CptBusinessSidePo record = new CptBusinessSidePo();
        BeanUtils.copyProperties(updateBusinessSideDto, record);
        cptBusinessSideDao.updateByPrimaryKeySelective(record);

        BusinessSideLogBean logBean = BusinessSideLogBean.builder().build();
        BeanUtils.copyProperties(record, logBean);
        gdLogService.insertLog(record.getId(), GdLogFlag.BUSINESS_SIDE, LogOperateType.UPDATE_BUSINESS_SIDE, operator, logBean);

    }

    private void batchSaveBusinessSideSource(List<Integer> sourceIds, Integer businessSideId) {
        if (!CollectionUtils.isEmpty(sourceIds)) {
            List<CptSourcePrivilegePo> sourcePrivilegePoList = Lists.newArrayListWithCapacity(sourceIds.size());
            CptSourcePrivilegePo po = null;
            for (Integer sid : sourceIds) {
                po = new CptSourcePrivilegePo();
                po.setBusinessSideId(businessSideId);
                po.setSourceId(sid);
                sourcePrivilegePoList.add(po);
            }
            localCptSourcePrivilegeDao.batchSave(sourcePrivilegePoList);
        }
    }

    private void deleteBusinessSideSourceByBuSideId(Integer businessSideId) {
        CptSourcePrivilegePo sourcePrivilegePo = new CptSourcePrivilegePo();
        sourcePrivilegePo.setIsDeleted(IsDeleted.DELETED.getCode());

        CptSourcePrivilegePoExample example = new CptSourcePrivilegePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBusinessSideIdEqualTo(businessSideId);

        cptSourcePrivilegeDao.updateByExampleSelective(sourcePrivilegePo, example);
    }

    private void deleteBusinessSideSource(
            Integer businessSideId, List<Integer> sourceIds) {
        if (!CollectionUtils.isEmpty(sourceIds)) {
            CptSourcePrivilegePo sourcePrivilegePo = new CptSourcePrivilegePo();
            sourcePrivilegePo.setIsDeleted(IsDeleted.DELETED.getCode());

            CptSourcePrivilegePoExample example = new CptSourcePrivilegePoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andBusinessSideIdEqualTo(businessSideId)
                    .andSourceIdIn(sourceIds);

            cptSourcePrivilegeDao.updateByExampleSelective(sourcePrivilegePo, example);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Operator operator, Integer businessSideId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(businessSideId, "业务方ID不可为空");
        CptBusinessSidePo businessSidePo = cptBusinessSideDao.selectByPrimaryKey(businessSideId);
        Assert.notNull(businessSidePo, "该业务方不存在");
        Assert.isTrue(Status.VALID.equals(Status.getByCode(businessSidePo.getStatus())), "该业务方已被删除");

        CptBusinessSidePo record = new CptBusinessSidePo();
        record.setId(businessSideId);
        record.setStatus(Status.INVALID.getCode());
        cptBusinessSideDao.updateByPrimaryKeySelective(record);

        this.deleteBusinessSideRotationConfig(businessSideId);

        this.deleteUserBusinessSideMapping(businessSideId);

        ssaBusinessSideService.delete(operator, businessSidePo.getAccountId());

        BusinessSideLogBean logBean = BusinessSideLogBean.builder().build();
        BeanUtils.copyProperties(record, logBean);
        logBean.setStatusDesc(Status.getByCode(record.getStatus()).getName());
        gdLogService.insertLog(record.getId(), GdLogFlag.BUSINESS_SIDE, LogOperateType.DELETE_BUSINESS_SIDE, operator, logBean);
    }

    private void deleteUserBusinessSideMapping(Integer businessSideId) {
        CptUserBusinessSideMappingPo userBusinessSideMappingPo = new CptUserBusinessSideMappingPo();
        userBusinessSideMappingPo.setIsDeleted(IsDeleted.DELETED.getCode());

        CptUserBusinessSideMappingPoExample userBusinessSideMappingPoExample = new CptUserBusinessSideMappingPoExample();
        userBusinessSideMappingPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBusinessSideIdEqualTo(businessSideId);

        cptUserBusinessSideMappingDao.updateByExampleSelective(userBusinessSideMappingPo, userBusinessSideMappingPoExample);
    }

    private void deleteBusinessSideRotationConfig(Integer businessSideId) {
        CptBusinessSideRotationConfigPo rotationConfigPo = new CptBusinessSideRotationConfigPo();
        rotationConfigPo.setStatus(Status.INVALID.getCode());
        CptBusinessSideRotationConfigPoExample rotationConfigPoExample = new CptBusinessSideRotationConfigPoExample();
        rotationConfigPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode()).andBusinessSideIdEqualTo(businessSideId);
        cptBusinessSideRotationConfigDao.updateByExampleSelective(rotationConfigPo, rotationConfigPoExample);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateBusinessInitRedPacket(Operator operator, Integer businessSideId,
                                            Long initRedPacket) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        CptBusinessSidePo businessSide = this.validateAndGetValidBusinessSide(businessSideId);
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notNull(initRedPacket, "初始保障额度不可为空");
        Assert.isTrue(initRedPacket >= 0, "初始保障额度不可为负数");

        Timestamp nextMoth = TimeUtils.getBeginDayOfNextMonth();

        CptBusinessSideRedPacketPoExample example = new CptBusinessSideRedPacketPoExample();
        example.or().andBusinessSideIdEqualTo(businessSideId).andEffectiveDateEqualTo(nextMoth).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<CptBusinessSideRedPacketPo> businessSideRedPacketPos = cptBusinessSideRedPacketDao.selectByExample(example);
        if (CollectionUtils.isEmpty(businessSideRedPacketPos)) {
            CptBusinessSideRedPacketPo record = new CptBusinessSideRedPacketPo();
            record.setBusinessSideId(businessSideId);
            record.setEffectiveDate(nextMoth);
            record.setRedPacket(initRedPacket);
            cptBusinessSideRedPacketDao.insertSelective(record);
        } else {
            CptBusinessSideRedPacketPo po = businessSideRedPacketPos.get(0);
            po.setRedPacket(initRedPacket);
            cptBusinessSideRedPacketDao.updateByPrimaryKeySelective(po);
        }

        BusinessSideLogBean logBean = BusinessSideLogBean.builder()
                .id(businessSideId)
                .name(businessSide.getName())
                .nextMonthInitRedPacket(initRedPacket)
                .effectiveDate(TimeUtils.getTimestamp2DayString(nextMoth))
                .build();
        gdLogService.insertLog(businessSideId, GdLogFlag.BUSINESS_SIDE, LogOperateType.UPDATE_NEXT_MONTH_INITAL_RED_PACKET, operator, logBean);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cashRecharge(Operator operator, Integer businessSideId, Long cash) {

        this.validateAndGetValidBusinessSide(businessSideId);
        this.charge(businessSideId, cash, 0l);

        CptBusinessSideCashRechargePo record = new CptBusinessSideCashRechargePo();
        record.setBusinessSideId(businessSideId);
        record.setCash(cash);
        cptBusinessSideCashRechargeDao.insertSelective(record);

        BusinessSideLogBean logBean = BusinessSideLogBean.builder()
                .id(businessSideId)
                .cash(cash)
                .build();
        gdLogService.insertLog(businessSideId, GdLogFlag.BUSINESS_SIDE, LogOperateType.ADD_CASH, operator, logBean);

    }

    @Override
    public PageResult<CashRechargeRecord> getCashRechargeRecordsByBusinessSideId(Integer businessSideId, Integer page, Integer size) {
        Assert.notNull(page, "页码不可为空");
        Assert.notNull(size, "页长不可为空");
        Assert.notNull(cptBusinessSideDao.selectByPrimaryKey(businessSideId), "该业务方不存在");
        LOGGER.info("CptUserService.getCashRechargeRecordsByBusinessSideId businessSideId {}, page {}, size {}", businessSideId, page, size);

        Page pageBean = Page.valueOf(page, size);

        CptBusinessSideCashRechargePoExample example = new CptBusinessSideCashRechargePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdEqualTo(businessSideId);

        long total = cptBusinessSideCashRechargeDao.countByExample(example);
        if (total == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }

        example.setOffset(pageBean.getOffset());
        example.setLimit(pageBean.getLimit());
        example.setOrderByClause("mtime desc");
        List<CptBusinessSideCashRechargePo> rechargePos = cptBusinessSideCashRechargeDao.selectByExample(example);
        if (CollectionUtils.isEmpty(rechargePos)) {
            return PageResult.<CashRechargeRecord>builder().total((int) total).records(Collections.emptyList()).build();
        }
        return PageResult.<CashRechargeRecord>builder().records(this.buildCashRechargeRecords(rechargePos)).total((int) total).build();
    }

    private List<CashRechargeRecord> buildCashRechargeRecords(List<CptBusinessSideCashRechargePo> rechargePos) {
        return rechargePos.stream().map(po -> CashRechargeRecord.builder()
                .date(TimeUtils.getTimestamp2String(po.getRechargeTime()))
                .cash(Utils.fromFenToYuan(po.getCash())).build()).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bindUserToBusinessSide(Operator operator, Integer businessSideId,
                                       List<String> userNameList) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        CptBusinessSidePo businessSide = this.validateAndGetValidBusinessSide(businessSideId);
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notEmpty(userNameList, "用户名称不可为空");

        CptUserBusinessSideMappingPo mappingPo = null;
        for (String name : userNameList) {
            mappingPo = new CptUserBusinessSideMappingPo();
            mappingPo.setBusinessSideId(businessSideId);
            int userId = cptUserService.save(operator, name);
            mappingPo.setUserId(userId);
            localCptUserBusinessSideMappingDao.save(mappingPo);
        }

        BusinessSideLogBean logBean = BusinessSideLogBean.builder()
                .id(businessSideId)
                .name(businessSide.getName())
                .userName(String.join(";", userNameList))
                .build();
        gdLogService.insertLog(businessSideId, GdLogFlag.BUSINESS_SIDE, LogOperateType.ADD_USER_TO_BUSIDE, operator, logBean);

    }

    private CptBusinessSidePo validateAndGetValidBusinessSide(Integer businessSideId) {
        CptBusinessSidePo businessSide = cptBusinessSideDao.selectByPrimaryKey(businessSideId);
        Assert.notNull(businessSide, "该业务方不存在");
        Assert.isTrue(businessSide.getStatus().equals(Status.VALID.getCode()), "已删除的业务方不可再操作");

        return businessSide;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbundledUser(Operator operator, Integer businessSideId, Integer userId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        CptBusinessSidePo businessSide = this.validateAndGetValidBusinessSide(businessSideId);
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notNull(userId, "用户ID不可为空");
        CptUserDto user = cptUserService.getUserById(userId);
        Assert.notNull(user, "该用户不存在");

        CptUserBusinessSideMappingPo record = new CptUserBusinessSideMappingPo();
        record.setIsDeleted(IsDeleted.DELETED.getCode());

        CptUserBusinessSideMappingPoExample example = new CptUserBusinessSideMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBusinessSideIdEqualTo(businessSideId).andUserIdEqualTo(userId);

        cptUserBusinessSideMappingDao.updateByExampleSelective(record, example);

        BusinessSideLogBean logBean = BusinessSideLogBean.builder()
                .id(businessSideId)
                .name(businessSide.getName())
                .userName(user.getName())
                .build();
        gdLogService.insertLog(businessSideId, GdLogFlag.BUSINESS_SIDE, LogOperateType.DELETE_USER_FROM_BUSIDE, operator, logBean);

    }

    @Override
    public Map<Integer, RedPacketDto> getBusinessSide2RedPacketMapInBusinessSideIds(
            List<Integer> businessSideIds) {
        Assert.notEmpty(businessSideIds, "业务方ID不可为空");
        Timestamp nextMonth = TimeUtils.getBeginDayOfNextMonth();
        Timestamp currentMonth = TimeUtils.getBeginDayOfCurrentMonth();
        CptBusinessSideRedPacketPoExample example = new CptBusinessSideRedPacketPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdIn(businessSideIds)
                .andEffectiveDateLessThanOrEqualTo(nextMonth);
        List<CptBusinessSideRedPacketPo> businessSideRedPacketPos = cptBusinessSideRedPacketDao.selectByExample(example);
        Map<Integer, List<CptBusinessSideRedPacketPo>> businessSideRedPacetMap = businessSideRedPacketPos.stream().collect(Collectors.groupingBy(CptBusinessSideRedPacketPo::getBusinessSideId));

        return businessSideIds.stream().distinct().map(bid -> {
            RedPacketDto dto = RedPacketDto.builder()
                    .businessSideId(bid)
                    .initRedPacket(0L)
                    .nextMonthInitRedPacket(0L)
                    .build();
            if (businessSideRedPacetMap.containsKey(bid)) {
                List<CptBusinessSideRedPacketPo> redPacketPos = businessSideRedPacetMap.get(bid);
                RedPacketDto redPacketDto = getBuSideInitalRedPacket(currentMonth, redPacketPos);
                dto.setInitRedPacket(redPacketDto.getInitRedPacket());
                dto.setNextMonthInitRedPacket(redPacketDto.getNextMonthInitRedPacket());
            }
            return dto;
        }).collect(Collectors.toMap(RedPacketDto::getBusinessSideId, Function.identity()));
    }

    private RedPacketDto getBuSideInitalRedPacket(Timestamp currentMonth, List<CptBusinessSideRedPacketPo> redPacketPos) {
        RedPacketDto dto = RedPacketDto.builder()
                .initRedPacket(0L)
                .nextMonthInitRedPacket(0L)
                .build();
        if (!CollectionUtils.isEmpty(redPacketPos)) {
            List<CptBusinessSideRedPacketPo> sortRedPacketPos = redPacketPos.stream().sorted((t1, t2) -> t1.getEffectiveDate().compareTo(t2.getEffectiveDate())).collect(Collectors.toList());
            //最后一个肯定是下个月的初始额度
            CptBusinessSideRedPacketPo redPacketPo = sortRedPacketPos.get(sortRedPacketPos.size() - 1);
            dto.setNextMonthInitRedPacket(redPacketPo.getRedPacket());
            Map<Timestamp, CptBusinessSideRedPacketPo> redPacketMap = redPacketPos.stream().collect(Collectors.toMap(CptBusinessSideRedPacketPo::getEffectiveDate, Function.identity()));
            if (redPacketMap.containsKey(currentMonth)) {
                dto.setInitRedPacket(redPacketMap.get(currentMonth).getRedPacket());
            } else {
                if (sortRedPacketPos.size() > 1) {
                    redPacketPo = sortRedPacketPos.get(sortRedPacketPos.size() - 2);
                    dto.setInitRedPacket(redPacketPo.getRedPacket());
                } else if (sortRedPacketPos.size() == 1 && redPacketPo.getEffectiveDate().before(currentMonth)) {
                    dto.setInitRedPacket(redPacketPo.getRedPacket());
                }
            }
        }
        return dto;
    }

    private boolean validateLogColorIsValid(String logoColor) {
        return Pattern.matches("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", logoColor);
    }

    private long getBusinessSideCountByAccountId(Integer accountId) {
        Assert.notNull(accountId, "accountId can not be null");
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return cptBusinessSideDao.countByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addBusinessSidePreBooking(Operator operator, Integer businessSideId, Integer cycleId, Integer sourceId,
                                             Integer mRotationLimit) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notNull(cycleId, "刊例周期ID不可为空");
        Assert.notNull(sourceId, "位次ID不可为空");
        Assert.notNull(mRotationLimit, "月预定轮播数上限不可为空");
        Assert.isTrue(mRotationLimit > 0, "月预定轮播数上限必须大于0");

        CptBusinessSidePo businessSidePo = this.validateAndGetValidBusinessSide(businessSideId);

        SourceAllInfoDto sourceInfo = querySourceService.getCptSourceBySourceId(sourceId);
        Assert.notNull(sourceInfo, "该位次已不支持CPT投放，不可再操作业务方预定");

        // 校验业务方预定月预定轮播数上限总量 <= 商业月频次限制
        // 获取业务方预定月预定轮播数上限总量
        CptBusinessSidePreBookingConfigPoExample selectExample = new CptBusinessSidePreBookingConfigPoExample();
        selectExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(cycleId)
                .andSourceIdEqualTo(sourceId);
        List<CptBusinessSidePreBookingConfigPo> preBookingConfigPoList = cptBusinessSidePreBookingConfigDao.selectByExample(selectExample);
        Integer otherRotationLimit = (CollectionUtils.isEmpty(preBookingConfigPoList) ? 0 : preBookingConfigPoList.stream()
                .filter(po -> !businessSideId.equals(po.getBusinessSideId()))
                .mapToInt(CptBusinessSidePreBookingConfigPo::getMRotationLimit)
                .sum());
        // 获取当前资源位库存量
        SourceConfigDto sourceConfigDto = cptSourceService.getSourceConfigBySourceId(cycleId, sourceId, SalesType.CPT.getCode());
        Assert.notNull(sourceConfigDto, "位次配置不存在");
        if (Utils.isPositive(sourceConfigDto.getMFreqLimit())) {
            Assert.isTrue(otherRotationLimit + mRotationLimit <= sourceConfigDto.getMFreqLimit(), "所有业务方月预定轮播数上限总量已超过商业月频次限制");
        }

        // 校验设定的月预定轮播数上限 >= 业务方在当前资源位已预定的量
        // 获取业务方在当前资源位已预定的量
        int hasBookingCount = (int) resourceBookingService.countBookingItem(BookingItemCountQueryDto.builder()
                .cycleId(cycleId)
                .sourceId(sourceId)
                .businessSideId(businessSideId)
                .build());
        Assert.isTrue(mRotationLimit >= hasBookingCount, "月预定轮播数上限不可小于业务方在当前资源位已预定量");

        LOGGER.info("BusinessSideService.addBusinessSidePreBooking operator {}, cycleId {}, sourceId {}, businessSideId {}, mRotationLimit {}",
                operator, cycleId, sourceId, businessSideId, mRotationLimit);
        CptBusinessSidePreBookingConfigPo oldPreBookingConfigPo = (CollectionUtils.isEmpty(preBookingConfigPoList) ? null
                : preBookingConfigPoList.stream().filter(po -> businessSideId.equals(po.getBusinessSideId()))
                .findFirst().orElse(null));
        CptBusinessSidePreBookingConfigPo preBookingConfigPo = new CptBusinessSidePreBookingConfigPo();
        if (oldPreBookingConfigPo != null) {
            // 更新
            preBookingConfigPo.setId(oldPreBookingConfigPo.getId());
            preBookingConfigPo.setMRotationLimit(mRotationLimit);
            cptBusinessSidePreBookingConfigDao.updateByPrimaryKeySelective(preBookingConfigPo);
        } else {
            // 插入
            preBookingConfigPo.setCycleId(cycleId);
            preBookingConfigPo.setSourceId(sourceId);
            preBookingConfigPo.setBusinessSideId(businessSideId);
            preBookingConfigPo.setMRotationLimit(mRotationLimit);
            preBookingConfigPo.setIsDeleted(IsDeleted.VALID.getCode());
            cptBusinessSidePreBookingConfigDao.insertSelective(preBookingConfigPo);
        }

        // 记录操作日志
        CptCycleDto cycleDto = cycleService.getCycleDtoById(cycleId);
        SourceConfigLogBean logBean = SourceConfigLogBean.builder()
                .cycleId(cycleId)
                .cycleName(cycleDto != null ? cycleDto.getName() : null)
                .sourceId(sourceId)
                .sourceName(sourceInfo.getName())
                .businessSideId(businessSideId)
                .businessSideName(businessSidePo.getName())
                .mRotationLimit(mRotationLimit)
                .build();
        gdLogService.insertLog(sourceId, GdLogFlag.SOURCE_CONFIG, LogOperateType.ADD_BUSINESS_SIDE_PRE_BOOKING,
                operator, logBean);

        return preBookingConfigPo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBusinessSidePreBooking(Operator operator, Integer preBookingId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(preBookingId, "业务方预定ID不可为空");

        CptBusinessSidePreBookingConfigPo preBookingConfigPo = cptBusinessSidePreBookingConfigDao.selectByPrimaryKey(preBookingId);
        Assert.notNull(preBookingConfigPo, "该业务方预定不存在");
        Assert.isTrue(preBookingConfigPo.getIsDeleted() != null
                && preBookingConfigPo.getIsDeleted() == IsDeleted.VALID.getCode(), "该业务方预定已经被删除");

        SourceAllInfoDto sourceInfo = querySourceService.getCptSourceBySourceId(preBookingConfigPo.getSourceId());
        Assert.notNull(sourceInfo, "该位次已不支持CPT投放，不可再操作业务方限制");

        CptBusinessSidePreBookingConfigPo updatePo = new CptBusinessSidePreBookingConfigPo();
        updatePo.setId(preBookingConfigPo.getId());
        updatePo.setIsDeleted(IsDeleted.DELETED.getCode());
        cptBusinessSidePreBookingConfigDao.updateByPrimaryKeySelective(updatePo);

        // 记录操作日志
        CptBusinessSidePo businessSidePo = cptBusinessSideDao.selectByPrimaryKey(preBookingConfigPo.getBusinessSideId());
        CptCycleDto cycleDto = cycleService.getCycleDtoById(preBookingConfigPo.getCycleId());
        SourceConfigLogBean logBean = SourceConfigLogBean.builder()
                .cycleId(preBookingConfigPo.getCycleId())
                .cycleName(cycleDto != null ? cycleDto.getName() : null)
                .sourceId(preBookingConfigPo.getSourceId())
                .sourceName(sourceInfo.getName())
                .businessSideId(preBookingConfigPo.getBusinessSideId())
                .businessSideName(businessSidePo != null ? businessSidePo.getName() : null)
                .mRotationLimit(preBookingConfigPo.getMRotationLimit())
                .build();
        gdLogService.insertLog(preBookingConfigPo.getSourceId(), GdLogFlag.SOURCE_CONFIG,
                LogOperateType.DELETE_BUSINESS_SIDE_PRE_BOOKING, operator, logBean);
    }

    @Override
    public List<CptSourceBusinessSidePreBookingDto> getBusinessSidePreBookingBySourceId(Operator operator, Integer cycleId,
                                                                                        Integer sourceId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        return getSourceBusinessSidePreBookingList(cycleId, sourceId, null);
    }

    private List<CptSourceBusinessSidePreBookingDto> getSourceBusinessSidePreBookingList(Integer cycleId, Integer sourceId,
                                                                                         Integer businessSideId) {
        Assert.notNull(cycleId, "刊例周期ID不可为空");
        Assert.notNull(sourceId, "位次ID不可为空");

        CptBusinessSidePreBookingConfigPoExample example = new CptBusinessSidePreBookingConfigPoExample();
        CptBusinessSidePreBookingConfigPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(cycleId)
                .andSourceIdEqualTo(sourceId);
        if (Utils.isPositive(businessSideId)) {
            criteria.andBusinessSideIdEqualTo(businessSideId);
        }
        List<CptBusinessSidePreBookingConfigPo> preBookingConfigPoList = cptBusinessSidePreBookingConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(preBookingConfigPoList)) {
            return Collections.emptyList();
        }

        // 查询业务方名称
        List<Integer> businessSideIdList = preBookingConfigPoList.stream()
                .map(CptBusinessSidePreBookingConfigPo::getBusinessSideId)
                .collect(Collectors.toList());
        CptBusinessSidePoExample businessSideExample = new CptBusinessSidePoExample();
        businessSideExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andIdIn(businessSideIdList);
        List<CptBusinessSidePo> businessSidePoList = cptBusinessSideDao.selectByExample(businessSideExample);
        Map<Integer, String> businessSideNameMap = (CollectionUtils.isEmpty(businessSidePoList) ? Collections.emptyMap()
                : businessSidePoList.stream().collect(Collectors.toMap(CptBusinessSidePo::getId, CptBusinessSidePo::getName)));

        return preBookingConfigPoList.stream().map(preBookingConfigPo -> CptSourceBusinessSidePreBookingDto.builder()
                .preBookingId(preBookingConfigPo.getId())
                .cycleId(preBookingConfigPo.getCycleId())
                .businessSideId(preBookingConfigPo.getBusinessSideId())
                .businessSideName(businessSideNameMap.get(preBookingConfigPo.getBusinessSideId()))
                .mRotationLimit(preBookingConfigPo.getMRotationLimit())
                .build())
                .collect(Collectors.toList());
    }

    @Override
    public CptSourceBusinessSidePreBookingDto getCptSourceBusinessSidePreBookingDto(Integer cycleId, Integer sourceId, Integer businessSideId) {
        Assert.notNull(businessSideId, "业务方ID不可为空");
        List<CptSourceBusinessSidePreBookingDto> preBookingList = getSourceBusinessSidePreBookingList(cycleId, sourceId, businessSideId);
        CptSourceBusinessSidePreBookingDto preBookingDto = preBookingList.stream()
                .min(Comparator.comparing(CptSourceBusinessSidePreBookingDto::getMRotationLimit))
                .orElse(null);
        LOGGER.info("BusinessSideService.getCptSourceBusinessSidePreBookingDto:preBookingDto={}", preBookingDto);
        return preBookingDto;
    }

    @Override
    public boolean isExternalBusinessSide(Integer accountId) {
        Assert.notNull(accountId, "账号id不可为空");
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId);
        List<CptBusinessSidePo> poList = cptBusinessSideDao.selectByExample(example);
        Assert.notEmpty(poList, "业务方不存在");
        return BusinessSideType.EXTERNAL.getCode().equals(poList.get(0).getType());
    }

    @Override
    public Set<Integer> getInternalBusinessSideAccountIds(List<Integer> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return Collections.emptySet();
        }

        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdIn(accountIdList)
                .andTypeEqualTo(BusinessSideType.INERNAL.getCode());
        List<CptBusinessSidePo> poList = cptBusinessSideDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptySet();
        }
        return poList.stream().map(CptBusinessSidePo::getAccountId)
                .collect(Collectors.toSet());
    }

    @Override
    public Set<Integer> getAllInternalBusinessSideAccountIds() {
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTypeEqualTo(BusinessSideType.INERNAL.getCode());
        List<CptBusinessSidePo> poList = cptBusinessSideDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptySet();
        }

        return poList.stream().map(CptBusinessSidePo::getAccountId)
                .collect(Collectors.toSet());
    }

    @Override
    public Set<Integer> getAllExternalBusinessSideAccountIds() {
        CptBusinessSidePoExample example = new CptBusinessSidePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTypeEqualTo(BusinessSideType.EXTERNAL.getCode());
        List<CptBusinessSidePo> poList = cptBusinessSideDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptySet();
        }

        return poList.stream().map(CptBusinessSidePo::getAccountId)
                .collect(Collectors.toSet());
    }

    @Override
    public Map<Integer, BusinessSideBaseDto> getBusinessSideByAccountId(List<Integer> accountIdList) {
        if (CollectionUtils.isEmpty(accountIdList)) {
            return Maps.newHashMap();
        }
        CptBusinessSidePoExample cptBusinessSidePoExample = new CptBusinessSidePoExample();
        cptBusinessSidePoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andAccountIdIn(accountIdList);
        List<CptBusinessSidePo> pos = this.cptBusinessSideDao.selectByExample(cptBusinessSidePoExample);
        return pos.stream()
                .map(cptBusinessSidePo -> {
                    BusinessSideBaseDto businessSideBaseDto = new BusinessSideBaseDto();
                    BeanUtils.copyProperties(cptBusinessSidePo, businessSideBaseDto);
                    return businessSideBaseDto;
        }).collect(Collectors.toMap(BusinessSideBaseDto::getAccountId, Function.identity(), OptionalUtil.override()));
    }
}
