package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePo;
import com.bilibili.cpt.platform.biz.po.CptSourceDayBookingLivePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CptSourceDayBookingLiveDao {
    long countByExample(CptSourceDayBookingLivePoExample example);

    int deleteByExample(CptSourceDayBookingLivePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CptSourceDayBookingLivePo record);

    int insertBatch(List<CptSourceDayBookingLivePo> records);

    int insertUpdateBatch(List<CptSourceDayBookingLivePo> records);

    int insert(CptSourceDayBookingLivePo record);

    int insertUpdateSelective(CptSourceDayBookingLivePo record);

    int insertSelective(CptSourceDayBookingLivePo record);

    List<CptSourceDayBookingLivePo> selectByExample(CptSourceDayBookingLivePoExample example);

    CptSourceDayBookingLivePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CptSourceDayBookingLivePo record, @Param("example") CptSourceDayBookingLivePoExample example);

    int updateByExample(@Param("record") CptSourceDayBookingLivePo record, @Param("example") CptSourceDayBookingLivePoExample example);

    int updateByPrimaryKeySelective(CptSourceDayBookingLivePo record);

    int updateByPrimaryKey(CptSourceDayBookingLivePo record);
}