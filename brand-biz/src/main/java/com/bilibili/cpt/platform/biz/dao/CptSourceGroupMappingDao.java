package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptSourceGroupMappingPo;
import com.bilibili.cpt.platform.biz.po.CptSourceGroupMappingPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CptSourceGroupMappingDao {
    long countByExample(CptSourceGroupMappingPoExample example);

    int deleteByExample(CptSourceGroupMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CptSourceGroupMappingPo record);

    int insertSelective(CptSourceGroupMappingPo record);

    List<CptSourceGroupMappingPo> selectByExample(CptSourceGroupMappingPoExample example);

    CptSourceGroupMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CptSourceGroupMappingPo record, @Param("example") CptSourceGroupMappingPoExample example);

    int updateByExample(@Param("record") CptSourceGroupMappingPo record, @Param("example") CptSourceGroupMappingPoExample example);

    int updateByPrimaryKeySelective(CptSourceGroupMappingPo record);

    int updateByPrimaryKey(CptSourceGroupMappingPo record);
}