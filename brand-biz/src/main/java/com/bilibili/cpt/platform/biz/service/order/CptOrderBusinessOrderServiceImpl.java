package com.bilibili.cpt.platform.biz.service.order;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.cpt.platform.api.creative.dto.PeriodDto;
import com.bilibili.cpt.platform.api.order.dto.CptOrderBusinessOrderDto;
import com.bilibili.cpt.platform.api.order.service.ICptOrderBusinessOrderService;
import com.bilibili.cpt.platform.biz.dao.CptOrderBusinessOrderMappingDao;
import com.bilibili.cpt.platform.biz.po.CptOrderBusinessOrderMappingPo;
import com.bilibili.cpt.platform.biz.po.CptOrderBusinessOrderMappingPoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2018/5/21 22:08
 */
@Service
@Slf4j
public class CptOrderBusinessOrderServiceImpl implements ICptOrderBusinessOrderService {

    @Autowired
    private CptOrderBusinessOrderMappingDao mappingDao;

    @Override
    public PeriodDto getPeriodByGdOrderId(Integer gdOrderId) {
        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andGdOrderIdEqualTo(gdOrderId);

        List<CptOrderBusinessOrderMappingPo> mappingPos = mappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return null;
        }

        CptOrderBusinessOrderMappingPo mappingPo = mappingPos.get(0);
        return PeriodDto.builder().startTime(mappingPo.getStartTime()).endTime(mappingPo.getEndTime()).build();
    }

    @Override
    public CptOrderBusinessOrderDto getCptOrderBusinessOrderDtoByGdOrderId(Integer gdOrderId) {
        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andGdOrderIdEqualTo(gdOrderId);

        List<CptOrderBusinessOrderMappingPo> mappingPos = mappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return null;
        }

        CptOrderBusinessOrderMappingPo mappingPo = mappingPos.get(0);
        return this.poToDto(mappingPo);
    }

    @Override
    public Map<Integer, CptOrderBusinessOrderDto> getCptOrderBusinessOrderDtoByGdOrderIds(List<Integer> gdOrderIds) {

        if (CollectionUtils.isEmpty(gdOrderIds)) {
            return Collections.EMPTY_MAP;
        }

        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andGdOrderIdIn(gdOrderIds);

        List<CptOrderBusinessOrderMappingPo> mappingPos = mappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return Collections.EMPTY_MAP;
        }

        return mappingPos.stream().map(this::poToDto).collect(Collectors.toMap(CptOrderBusinessOrderDto::getGdOrderId, Function.identity()));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addMappingDto(CptOrderBusinessOrderDto businessOrderDto) {

        mappingDao.insertSelective(dtoToPo(businessOrderDto));
    }

    private CptOrderBusinessOrderMappingPo dtoToPo(CptOrderBusinessOrderDto dto) {
        CptOrderBusinessOrderMappingPo mappingPo = new CptOrderBusinessOrderMappingPo();

        BeanUtils.copyProperties(dto, mappingPo);

        log.info("CptOrderBusinessOrderDto: [[]], CptOrderBusinessOrderMappingPo: [{}]", dto, mappingPo);
        return mappingPo;
    }

    private CptOrderBusinessOrderDto poToDto(CptOrderBusinessOrderMappingPo po) {
        CptOrderBusinessOrderDto dto = CptOrderBusinessOrderDto.builder().build();

        BeanUtils.copyProperties(po, dto);

        return dto;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMappingDto(CptOrderBusinessOrderDto businessOrderDto) {


        CptOrderBusinessOrderMappingPo mappingPo = new CptOrderBusinessOrderMappingPo();
        mappingPo.setStartTime(businessOrderDto.getStartTime());
        mappingPo.setEndTime(businessOrderDto.getEndTime());

        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andGdOrderIdEqualTo(businessOrderDto.getGdOrderId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        mappingDao.updateByExampleSelective(mappingPo, example);

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateMappingDtoAvid(CptOrderBusinessOrderDto businessOrderDto) {

        CptOrderBusinessOrderMappingPo mappingPo = new CptOrderBusinessOrderMappingPo();
        mappingPo.setAvid(businessOrderDto.getAvid());

        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andGdOrderIdEqualTo(businessOrderDto.getGdOrderId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        mappingDao.updateByExampleSelective(mappingPo, example);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeMappingDtoAvid(CptOrderBusinessOrderDto businessOrderDto) {

        CptOrderBusinessOrderMappingPo mappingPo = new CptOrderBusinessOrderMappingPo();
        mappingPo.setAvid(0L);

        CptOrderBusinessOrderMappingPoExample example = new CptOrderBusinessOrderMappingPoExample();
        example.or().andGdOrderIdEqualTo(businessOrderDto.getGdOrderId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        mappingDao.updateByExampleSelective(mappingPo, example);
    }
}
