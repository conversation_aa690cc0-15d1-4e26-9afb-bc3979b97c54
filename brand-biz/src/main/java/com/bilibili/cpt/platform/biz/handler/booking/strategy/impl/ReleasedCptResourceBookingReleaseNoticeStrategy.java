package com.bilibili.cpt.platform.biz.handler.booking.strategy.impl;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.booking.dto.BookingItemDto;
import com.bilibili.brand.api.booking.dto.BookingItemReleaseMatchResultDto;
import com.bilibili.brand.api.booking.dto.ResourceBookingReleaseNotificationDto;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.utils.TimeUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <b>释放完资源之后邮件通知</b>
 * 每天约12:00、17:00，释放完成后告知已释放的资源位清单，若未释放资源则不发送
 * <p>
 * 接收对象：投放账号的所有关联人员
 * <p>
 * 内容参考：
 * <p>
 * 标题：【释放完成】CPT-已释放资源位告知
 * <p>
 * 正文：
 * <p>
 * 您的下列资源已被释放，如有需要可重新预订
 * <p>
 * 本次所释放的预约为: 2022-12-23（星期五）到 2022-12-26（星期六）
 * <p>
 * （表格同预警邮件）
 * <a href="https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002727323">【品牌】CPT资源预定释放逻辑调整</a>
 *
 * <AUTHOR>
 * @date 2023/3/26 18:58
 */
@Slf4j
@Component
public class ReleasedCptResourceBookingReleaseNoticeStrategy
        extends AbstractCptResourceBookingReleaseNoticeStrategy<ResourceBookingReleaseNotificationDto> {
    @Override
    protected String getSubject(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items) {
        return "【释放完成】CPT-已释放资源位告知";
    }

    @Override
    protected String getSummary(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items) {
        BookingItemReleaseMatchResultDto minGroupDateItem = items.stream()
                .min(Comparator.comparing(item -> item.getBookingItem().getGroupDate(), Timestamp::compareTo))
                .get();
        BookingItemReleaseMatchResultDto maxGroupDateItem = items.stream()
                .max(Comparator.comparing(item -> item.getBookingItem().getGroupDate(), Timestamp::compareTo))
                .get();
        return String.format("您的下列资源已被释放，如有需要可重新预订。\n本次所释放的预约为: %s 到 %s。",
                TimeUtil.formatDateWithE(minGroupDateItem.getBookingItem().getGroupDate()),
                TimeUtil.formatDateWithE(maxGroupDateItem.getBookingItem().getGroupDate()));
    }

    @Override
    protected List<ResourceBookingReleaseNotificationDto> getBody(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items) {
        Map<Integer, String> orderNameMap = cptOrderService.getOrderNameMapInOrderIdSet(items.stream()
                .map(item -> item.getBookingItem().getCptOrderId())
                .filter(Utils::isPositive)
                .collect(Collectors.toSet()));
        List<ResourceBookingReleaseNotificationDto> bodies = Lists.newArrayList();
        for (BookingItemReleaseMatchResultDto item : items) {
            BookingItemDto booking = item.getBookingItem();
            ResourceBookingReleaseNotificationDto body = ResourceBookingReleaseNotificationDto.builder()
                    .sourceDayBookId(booking.getId())
                    .businessSideId(booking.getBusinessSideId())
                    .businessSideName(businessSide.getName())
                    .orderId(booking.getCptOrderId())
                    .orderName(orderNameMap.getOrDefault(booking.getCptOrderId(), ""))
                    .scheduleId(booking.getCptScheduleId())
                    .sourceId(booking.getSourceId())
                    .sourceName(booking.getSourceName())
                    .groupDate(booking.getGroupDate().toLocalDateTime())
                    .groupDateStr(TimeUtil.formatDateWithE(booking.getGroupDate()))
                    .releaseDate(LocalDateTime.now())
                    .releaseDateStr(TimeUtil.formatDateTimeWithE(LocalDateTime.now()))
                    .sourceBookingTime(booking.getCtime().toLocalDateTime())
                    .sourceBookingTimeStr(TimeUtil.formatDateWithE(booking.getCtime()))
                    .operator(booking.getOperator())
                    .releaseReason(item.getReleaseReason())
                    .build();
            bodies.add(body);
        }
        return bodies;
    }

    @Override
    public boolean doMatch(ReleaseRequestBean releaseRequest) {
        return Objects.equals(releaseRequest.getTriggerType(), ReleaseRequestBean.TriggerType.RELEASE);
    }
}
