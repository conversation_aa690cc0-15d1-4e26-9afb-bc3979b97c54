package com.bilibili.cpt.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.common.IsAdmin;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/13.
 */
@Slf4j
@Service
public class CptBaseService {

    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IBusinessSideService businessSideService;

    public void validateValidBusinessSide(Integer businessSideId) {
        BusinessSideBaseDto businessSide = businessSideService.getValidBusinessSideById(businessSideId);
        Assert.notNull(businessSide, "该业务方已删除，不可操作");
    }

    public BusinessSideBaseDto getValidBusinessSide(Integer businessSideId) {
        BusinessSideBaseDto businessSide = businessSideService.getValidBusinessSideById(businessSideId);
        Assert.notNull(businessSide, "该业务方已删除，不可操作");
        return businessSide;
    }

    public boolean validateIsAdmin(List<BusinessSideBaseDto> businessSideBaseDtos) {
        return businessSideBaseDtos.stream().anyMatch(businessSideBaseDto -> businessSideBaseDto.getIsAdmin().equals(IsAdmin.ADMIN.getCode()));
    }

    public List<BusinessSideBaseDto> getBuSidesByOperator(Operator operator) {
        List<BusinessSideBaseDto> businessSideBaseDtos = null;
        if (null != operator.getOperatorId() && operator.getOperatorId() > 0) {
            businessSideBaseDtos = Lists.newArrayList(businessSideService.getBusinessSideByAccountId(operator.getOperatorId()));
        } else {
            businessSideBaseDtos = businessSideService.getBusinessSidesByOperatorName(operator.getOperatorName());
        }
        Assert.notNull(businessSideBaseDtos, "您没有关联任何业务方");
        return businessSideBaseDtos;
    }

    public List<Integer> getBuSideIds(List<BusinessSideBaseDto> businessSideBaseDtos) {
        return businessSideBaseDtos.stream().map(BusinessSideBaseDto::getId).collect(Collectors.toList());
    }

    protected RLock getLock(Object objId, String lockSuffix) {
        return getLock(objId, lockSuffix, 5, 5);
    }

    protected RLock getLock(Object objId, String lockSuffix, int waitSeconds,int leaseSeconds) {
        Assert.notNull(objId, "资源对象不可为空");
        Assert.hasText(lockSuffix, "锁前缀不可为空");

        String lockName = objId + lockSuffix;
        RLock lock = redissonClient.getLock(lockName);
        boolean isSuccess;
        try {
            isSuccess = lock.tryLock(waitSeconds, leaseSeconds, TimeUnit.SECONDS);
            log.info("CurrentThread: {}, getLock lockName: {}, isSuccess: {}", Thread.currentThread().getName(), lockName, isSuccess);
        } catch (InterruptedException e) {
            log.error("CurrentThread: {}, getLock lockName: {} failed", lockName, e);
            isSuccess = false;
        }
        Assert.isTrue(isSuccess, "资源正在被他人使用，请稍后重试");
        return lock;
    }

    protected List<RLock> getLocks(List<Integer> objIds, String lockSuffix) {
        Assert.isTrue(!CollectionUtils.isEmpty(objIds), "资源对象不可为空");
        Assert.hasText(lockSuffix, "锁前缀不可为空");

        List<RLock> rLocks = new ArrayList<>();
        objIds.forEach(o->{
            String lockName = o + lockSuffix;
            RLock lock = redissonClient.getLock(lockName);
            boolean isSuccess;
            try {
                isSuccess = lock.tryLock(5, 5, TimeUnit.SECONDS);
                log.info("CurrentThread: {}, getLock lockName: {}, isSuccess: {}", Thread.currentThread().getName(), lockName, isSuccess);
            } catch (InterruptedException e) {
                log.error("CurrentThread: {}, getLock lockName: {} failed", lockName, e);
                isSuccess = false;
                if(rLocks.size() > 0){
                    rLocks.forEach(Lock::unlock);
                }
            }
            if(!isSuccess){
                if(rLocks.size() > 0){
                    rLocks.forEach(Lock::unlock);
                }
                throw new IllegalArgumentException("资源正在被他人使用，请稍后重试");
            }
        });

        return rLocks;
    }
}
