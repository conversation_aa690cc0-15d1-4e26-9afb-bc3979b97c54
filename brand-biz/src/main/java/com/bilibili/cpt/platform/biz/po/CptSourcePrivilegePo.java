package com.bilibili.cpt.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class CptSourcePrivilegePo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 业务方ID
     */
    private Integer businessSideId;

    /**
     * 资源位ID
     */
    private Integer sourceId;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 最后修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBusinessSideId() {
        return businessSideId;
    }

    public void setBusinessSideId(Integer businessSideId) {
        this.businessSideId = businessSideId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}