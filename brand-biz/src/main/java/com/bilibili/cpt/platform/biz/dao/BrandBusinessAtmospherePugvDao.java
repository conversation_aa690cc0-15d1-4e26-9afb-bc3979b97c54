package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.BrandBusinessAtmospherePugvPo;
import com.bilibili.cpt.platform.biz.po.BrandBusinessAtmospherePugvPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BrandBusinessAtmospherePugvDao {
    long countByExample(BrandBusinessAtmospherePugvPoExample example);

    int deleteByExample(BrandBusinessAtmospherePugvPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BrandBusinessAtmospherePugvPo record);

    int insertBatch(List<BrandBusinessAtmospherePugvPo> records);

    int insertUpdateBatch(List<BrandBusinessAtmospherePugvPo> records);

    int insert(BrandBusinessAtmospherePugvPo record);

    int insertUpdateSelective(BrandBusinessAtmospherePugvPo record);

    int insertSelective(BrandBusinessAtmospherePugvPo record);

    List<BrandBusinessAtmospherePugvPo> selectByExample(BrandBusinessAtmospherePugvPoExample example);

    BrandBusinessAtmospherePugvPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BrandBusinessAtmospherePugvPo record, @Param("example") BrandBusinessAtmospherePugvPoExample example);

    int updateByExample(@Param("record") BrandBusinessAtmospherePugvPo record, @Param("example") BrandBusinessAtmospherePugvPoExample example);

    int updateByPrimaryKeySelective(BrandBusinessAtmospherePugvPo record);

    int updateByPrimaryKey(BrandBusinessAtmospherePugvPo record);
}