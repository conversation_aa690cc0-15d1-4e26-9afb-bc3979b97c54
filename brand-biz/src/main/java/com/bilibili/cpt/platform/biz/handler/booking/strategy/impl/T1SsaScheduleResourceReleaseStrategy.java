package com.bilibili.cpt.platform.biz.handler.booking.strategy.impl;

import com.bilibili.brand.api.ssa.SsaScheduleReleaseMatchResultDto;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 1、释放提醒
 * 【t-1，12：00】、【t-1，17：00】释放前邮件提醒。
 * 释放提醒邮件发送时间及校验条件：
 * <p>
 * 1.1、每天中午12：00，对t+1闪屏订单校验，如有t+1日期未过审订单的排期，则提醒需尽快过审且上传创意，否则有释放风险。
 * <p>
 * 1.2、每天下午17：00，对t+1闪屏订单校验，如有t+1日期未过审订单的排期，则提醒需尽快过审且上传创意，否则有释放风险。
 * <p>
 * 2、释放通知
 * 2.1、每天晚20：00，对t+1闪屏订单校验，如有t+1日期未过审订单且无创意的排期，则释放。
 * <p>
 * <a href="https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887003091340">【品牌】闪屏资源-订单排期释放规则</a>
 *
 * <AUTHOR>
 * @date 2023/11/6 15:34
 */
@Slf4j
@Service
public class T1SsaScheduleResourceReleaseStrategy extends AbstractSsaScheduleResourceReleaseStrategy {
    private final String RELEASE_EXECUTE_COUNTER_PREFIX = "ssa_strategy_t1";

    @Override
    public boolean doMatch(ReleaseRequestBean releaseRequest) {
        ReleaseRequestBean.TriggerType triggerType = releaseRequest.getTriggerType();
        int exeCounter = exeCount(triggerType);
        //1、匹配释放前提醒，每天执行2次，因此判断是否<=1
        //2、匹配释放，每天执行1次，因此判断是否<=0
        return this.getCounter(RELEASE_EXECUTE_COUNTER_PREFIX, triggerType,
                releaseRequest.getTriggerTime()) <= (exeCounter - 1);
    }

    @Override
    public List<SsaScheduleReleaseMatchResultDto> matchBookings(ReleaseRequestBean releaseRequest) {
        //t+1
        LocalDateTime launchTime = releaseRequest.getTriggerTime().toLocalDate().atStartOfDay().plusDays(1);
        return this.getMatchResult(launchTime);
    }

    @Override
    public void callbackAfterReleased(ReleaseRequestBean releaseRequest) {
        ReleaseRequestBean.TriggerType triggerType = releaseRequest.getTriggerType();
        int exeCounter = exeCount(triggerType);
        int counter = this.getCounter(RELEASE_EXECUTE_COUNTER_PREFIX, releaseRequest.getTriggerType(),
                releaseRequest.getTriggerTime());
        if (counter <= (exeCounter - 1)) {
            this.addCounter(RELEASE_EXECUTE_COUNTER_PREFIX, releaseRequest.getTriggerType(),
                    releaseRequest.getTriggerTime());
        }
    }

    private int exeCount(ReleaseRequestBean.TriggerType triggerType) {
        //匹配释放前提醒，每天执行2次
        if (Objects.equals(triggerType, ReleaseRequestBean.TriggerType.WARNING)) {
            return 2;
        }
        //匹配释放，每天执行1次
        if (Objects.equals(triggerType, ReleaseRequestBean.TriggerType.RELEASE)) {
            return 1;
        }
        return 0;
    }
}
