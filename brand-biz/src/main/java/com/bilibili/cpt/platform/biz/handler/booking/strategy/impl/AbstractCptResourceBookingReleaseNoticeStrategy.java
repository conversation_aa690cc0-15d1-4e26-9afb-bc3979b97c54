package com.bilibili.cpt.platform.biz.handler.booking.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.MailUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.brand.api.booking.dto.BookingItemReleaseMatchResultDto;
import com.bilibili.brand.biz.utils.BrandLittleAssistantUtil;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.dto.CptUserDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.business_side.service.ICptUserService;
import com.bilibili.cpt.platform.api.order.service.ICptOrderService;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.cpt.platform.biz.handler.booking.strategy.CptResourceBookingReleaseNoticeStrategy;
import com.bilibili.cpt.platform.util.IpUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/24 20:55
 */
@Slf4j
public abstract class AbstractCptResourceBookingReleaseNoticeStrategy<T> implements CptResourceBookingReleaseNoticeStrategy {
    @Autowired
    private IMailService mailService;

    @Autowired
    protected IBusinessSideService businessSideService;

    @Autowired
    protected ICptUserService cptUserService;

    @Autowired
    protected ICptOrderService cptOrderService;

    @Value("#{'${resource.booking.release.notice.default.recipient:wangyanjun02}'.split(',')}")
    private List<String> defaultRecipient;

    @Value("#{'${resource.booking.release.notice.recipient.count:20}'.split(',')}")
    private Integer recipientCount = 20;

    protected abstract boolean doMatch(ReleaseRequestBean releaseRequest);

    @Override
    public boolean match(ReleaseRequestBean releaseRequest) {
        return Objects.equals(releaseRequest.getResourceType(), ReleaseRequestBean.ResourceType.CPT) && doMatch(releaseRequest);
    }
    /**
     * 返回主题
     */
    protected abstract String getSubject(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items);

    /**
     * 返回正文摘要
     */
    protected abstract String getSummary(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items);

    /**
     * 返回正文数据
     */
    protected abstract List<T> getBody(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items);

    @Override
    public void notify(List<BookingItemReleaseMatchResultDto> items, ReleaseRequestBean releaseRequest) {
        Map<Integer, List<BookingItemReleaseMatchResultDto>> busIdToItemMap = items.stream()
                .filter(item -> Utils.isPositive(item.getBookingItem().getBusinessSideId()))
                .collect(Collectors.groupingBy(item -> item.getBookingItem().getBusinessSideId()));
        List<Integer> busIds = Lists.newArrayList(busIdToItemMap.keySet());
        Map<Integer, BusinessSideBaseDto> busMap = businessSideService.getBusinessSideMapInIds(busIds);
        Map<Integer, List<CptUserDto>> busToUserMap = cptUserService.getBusinessSideUserMapInBusinessSideIds(busIds);

        for (Map.Entry<Integer, List<BookingItemReleaseMatchResultDto>> entry : busIdToItemMap.entrySet()) {
            Integer businessSideId = entry.getKey();
            List<BookingItemReleaseMatchResultDto> bookings = entry.getValue();
            List<CptUserDto> users = busToUserMap.get(businessSideId);
            List<String> recipient;
            if (Objects.isNull(businessSideId) || CollectionUtils.isEmpty(users)) {
                recipient = this.defaultRecipient;
            } else {
                recipient = users.stream()
                        .map(CptUserDto::getName)
                        .collect(Collectors.toList());
            }

            BusinessSideBaseDto businessSide = busMap.getOrDefault(businessSideId, BusinessSideBaseDto.builder().build());

            String body = this.generateContent(businessSide, bookings);
            if (StringUtils.isEmpty(body)) {
                log.warn("[AbstractCptResourceBookingReleaseNoticeStrategy] failed to get notice body,bookings={}",
                        JSON.toJSONString(bookings));
                continue;
            }

            String subject = this.getSubject(businessSide, bookings);
            String summary = this.getSummary(businessSide, bookings);

            //分批处理，否则太多会导致发送失败（但是mailService.send(mail)并没有报错）
            List<List<String>> recipients = Lists.partition(recipient, recipientCount);
            for (List<String> element : recipients) {
                MailMessage mail = new MailMessage();
                mail.setTos(element.stream().map(u -> u + "@bilibili.com").collect(Collectors.toList()));
                mail.setHasFile(false);
                mail.setSubject(subject);
                mail.setUseHtml(true);
                mail.setText(summary + "\n" + body);
                for (int i = 0; i < 5; i++) {
                    try {
                        mailService.send(mail);
                        break;
                    } catch (Exception e) {
                        log.error("[AbstractCptResourceBookingReleaseNoticeStrategy] send notice error，request={}",
                                JSON.toJSONString(releaseRequest), e);
                        List<Integer> bookingIds = bookings.stream()
                                .map(item -> item.getBookingItem().getId())
                                .collect(Collectors.toList());
                        BrandLittleAssistantUtil.sendWithMarkdown(
                                BrandLittleAssistantUtil.GroupKey.BRAND_DEVELOPER_GROUP_KEY,
                                String.format("发送资源预订（预警、释放）邮件通知失败，预订ID=%s，机器IP=%s",
                                        bookingIds, IpUtil.getIp()));
                        LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(500));
                    }
                }
            }
        }
    }

    private String generateContent(BusinessSideBaseDto businessSide, List<BookingItemReleaseMatchResultDto> items) {
        List<T> body = this.getBody(businessSide, items);
        if (CollectionUtils.isEmpty(body)) {
            return null;
        }
        try {
            return MailUtils.genHtmlTableString(body);
        } catch (IllegalAccessException e) {
            log.error("[AbstractCptResourceBookingReleaseNoticeStrategy] generateContent error", e);
        }
        return null;
    }
}
