package com.bilibili.cpt.platform.biz.service.location.processor;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.biz.cycle.processor.CycleProcessorDispatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Component
public class CptCycleProcessorDispatcher implements CycleProcessorDispatcher<AbstractCptCycleProcessor> {

    @Autowired
    private ApplicationContext applicationContext;

    private final Map<Integer, AbstractCptCycleProcessor> SALES_TYPE_PROCESSOR_MAP = new HashMap<>();

    private final Map<Integer, AbstractCptCycleProcessor> ORDER_PRODUCT_PROCESSOR_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, AbstractCptCycleProcessor> beansOfType = applicationContext.getBeansOfType(AbstractCptCycleProcessor.class);
        for (AbstractCptCycleProcessor processor : beansOfType.values()) {
            Assert.notNull(processor.supportSalesType(), "supportSalesType不能为空，请检查你的代码！");
            Assert.isTrue(!SALES_TYPE_PROCESSOR_MAP.containsKey(processor.supportSalesType().getCode()), "定义了重复的AbstractCptCycleProcessor，请检查你的代码！");
            SALES_TYPE_PROCESSOR_MAP.put(processor.supportSalesType().getCode(), processor);

            Assert.notEmpty(processor.supportOrderProducts(), "supportOrderProducts不能为空，请检查你的代码");
            for (OrderProduct orderProduct : processor.supportOrderProducts()) {
                Assert.isTrue(!ORDER_PRODUCT_PROCESSOR_MAP.containsKey(orderProduct.getCode()), "定义了重复的AbstractCptCycleProcessor，请检查你的代码！");
                ORDER_PRODUCT_PROCESSOR_MAP.put(orderProduct.getCode(), processor);
            }
        }
    }

    public AbstractCptCycleProcessor dispatchCptCycleProcessorBySalesType(Integer code) {
        Integer newCode = Optional.ofNullable(code)
                .orElse(SalesType.CPT.getCode());
        AbstractCptCycleProcessor processor = SALES_TYPE_PROCESSOR_MAP.get(newCode);
        Assert.notNull(processor, "没有找到对应的AbstractCptCycleProcessor，请检查你的参数！");
        return processor;
    }

    @Override
    public AbstractCptCycleProcessor dispatchCycleProcessor(Integer code) {
        AbstractCptCycleProcessor processor = ORDER_PRODUCT_PROCESSOR_MAP.get(code);
        Assert.notNull(processor, "没有找到对应的AbstractCptCycleProcessor，请检查你的参数！");
        return processor;
    }

    @Override
    public boolean isSupportOrderProduct(Integer orderProduct) {
        return Objects.equals(orderProduct, OrderProduct.CPT.getCode())
                || Objects.equals(orderProduct, OrderProduct.SEARCH_CPT.getCode())
                || Objects.equals(orderProduct, OrderProduct.LIVE_CPT.getCode())
                || Objects.equals(orderProduct, OrderProduct.STANDARD_LIVE_CPT.getCode());
    }
}
