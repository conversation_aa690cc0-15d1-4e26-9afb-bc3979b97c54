package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.OgvBrandContentPo;
import com.bilibili.cpt.platform.biz.po.OgvBrandContentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface OgvBrandContentDao {
    long countByExample(OgvBrandContentPoExample example);

    int deleteByExample(OgvBrandContentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(OgvBrandContentPo record);

    int insertBatch(List<OgvBrandContentPo> records);

    int insertUpdateBatch(List<OgvBrandContentPo> records);

    int insert(OgvBrandContentPo record);

    int insertUpdateSelective(OgvBrandContentPo record);

    int insertSelective(OgvBrandContentPo record);

    List<OgvBrandContentPo> selectByExample(OgvBrandContentPoExample example);

    OgvBrandContentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") OgvBrandContentPo record, @Param("example") OgvBrandContentPoExample example);

    int updateByExample(@Param("record") OgvBrandContentPo record, @Param("example") OgvBrandContentPoExample example);

    int updateByPrimaryKeySelective(OgvBrandContentPo record);

    int updateByPrimaryKey(OgvBrandContentPo record);
}