package com.bilibili.cpt.platform.biz.service.location;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.booking.dto.BookingItemCountQueryDto;
import com.bilibili.brand.api.booking.service.ILiveResourceBookingService;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.common.enums.CycleStatus;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.biz.cycle.CycleScheduleService;
import com.bilibili.brand.biz.cycle.dto.CycleScheduleDto;
import com.bilibili.brand.biz.log.bean.CptCycleLogBean;
import com.bilibili.cpt.platform.api.location.dto.CptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.NewCptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.UpdateCptCycleDto;
import com.bilibili.cpt.platform.biz.dao.CptCycleDao;
import com.bilibili.cpt.platform.biz.po.CptCyclePo;
import com.bilibili.cpt.platform.biz.po.CptCyclePoExample;
import com.bilibili.cpt.platform.biz.service.location.processor.AbstractCptCycleProcessor;
import com.bilibili.cpt.platform.biz.service.location.processor.BaseCptCycleProcessor;
import com.bilibili.cpt.platform.biz.service.location.processor.CptCycleProcessorDispatcher;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.LogOperateType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2017/10/30.
 */
@Service
class CptCycleDelegate {
    @Autowired
    private CptCycleDao cptCycleDao;
    @Autowired
    private IResourceBookingService resourceBookingService;
    @Autowired
    private ILiveResourceBookingService liveResourceBookingService;
    @Autowired
    private CptCycleProcessorDispatcher cptCycleProcessorDispatcher;
    @Autowired
    private IGdLogService gdLogService;

    @Autowired
    private CycleScheduleService cycleScheduleService;

    public CptCycleDto getCycleDtoById(Integer id) {
        CptCyclePoExample example = new CptCyclePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(id);
        List<CptCycleDto> dtos = this.selectByExample(example);
        Assert.notEmpty(dtos, "刊例周期不存在" + id);
        return dtos.get(0);
    }

    public CptCycleDto getCycleDtoByTime(Timestamp time, Integer salesType) {
        Assert.notNull(time);
        CptCyclePoExample example = new CptCyclePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSalesTypeEqualTo(salesType == null ? SalesType.CPT.getCode() : salesType)
                .andBeginTimeLessThanOrEqualTo(time)
                .andEndTimeGreaterThanOrEqualTo(time);
        List<CptCycleDto> dtos = this.selectByExample(example);
        Assert.notEmpty(dtos, "刊例周期不存在 " + new SimpleDateFormat("yyyy-MM-dd").format(time));
        return dtos.get(0);
    }

    public CptCycleDto getValidCycleDtoByTime(Timestamp time, boolean isAdmin, Integer salesType) {
        Assert.notNull(time);
        List<Integer> status = new ArrayList<>();
        status.add(CycleStatus.VALID.getCode());
        if (isAdmin) {
            status.add(CycleStatus.ADMIN_VALID.getCode());
        }

        CptCyclePoExample example = new CptCyclePoExample();
        example.or()
                .andStatusIn(status)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSalesTypeEqualTo(salesType == null ? SalesType.CPT.getCode() : salesType)
                .andBeginTimeLessThanOrEqualTo(time)
                .andEndTimeGreaterThanOrEqualTo(time);
        List<CptCycleDto> dtos = this.selectByExample(example);
        if (CollectionUtils.isEmpty(dtos)) {
            return null;
        }
        return dtos.get(0);
    }

    public List<CptCycleDto> getCycleDtosByTime(Timestamp begin, Timestamp end, Integer salesType) {
        Assert.notNull(begin);
        Assert.notNull(end);
        Assert.isTrue(end.getTime() >= begin.getTime());
        CptCyclePoExample example = new CptCyclePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSalesTypeEqualTo(salesType == null ? SalesType.CPT.getCode() : salesType)
                .andBeginTimeLessThanOrEqualTo(end)
                .andEndTimeGreaterThanOrEqualTo(begin);
        return this.selectByExample(example);
    }

    public List<CptCycleDto> getCycleDtosInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        CptCyclePoExample example = new CptCyclePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdIn(ids);

        return this.selectByExample(example);
    }

    public List<CptCycleDto> getCycleDtoByStatus(Integer status, Integer salesType) {

        CptCyclePoExample example = new CptCyclePoExample();
        CptCyclePoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id desc");
        if (status != null) {
            CycleStatus.getByCode(status);
            criteria.andStatusEqualTo(status);
        }
        if (salesType == null) {
            criteria.andSalesTypeEqualTo(SalesType.CPT.getCode());
        } else {
            criteria.andSalesTypeEqualTo(salesType);
        }

        return this.selectByExample(example);
    }

    public Map<Integer, CptCycleDto> getCycleDtoMapInIds(List<Integer> ids) {
        return this.getCycleDtosInIds(ids).stream()
                .collect(Collectors.toMap(CptCycleDto::getId, Function.identity()));
    }

    @Transactional(rollbackFor = Exception.class)
    public int create(Operator operator, NewCptCycleDto newCptCycleDto) {
        AbstractCptCycleProcessor processor = cptCycleProcessorDispatcher.dispatchCptCycleProcessorBySalesType(newCptCycleDto.getSalesType());
        return processor.processCreate(operator, newCptCycleDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Operator operator, UpdateCptCycleDto updateCptCycleDto) {
        AbstractCptCycleProcessor processor = cptCycleProcessorDispatcher.dispatchCptCycleProcessorBySalesType(updateCptCycleDto.getSalesType());
        processor.processUpdate(operator, updateCptCycleDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public void enable(Operator operator, Integer cycleId) {
        this.updateStatus(operator, cycleId, CycleStatus.VALID);
    }

    public void adminEnable(Operator operator, Integer cycleId) {
        this.updateStatus(operator, cycleId, CycleStatus.ADMIN_VALID);
    }

    @Transactional(rollbackFor = Exception.class)
    public void disable(Operator operator, Integer cycleId) {
        Assert.notNull(cycleId, "cycleId为空");
        long count = resourceBookingService.countBookingItemByCycleId(cycleId);
        Assert.isTrue(count == 0L, "该刊例周期下已经存在常规预约, 若要禁用，请联系业务方删除预约");

        BookingItemCountQueryDto queryDto = BookingItemCountQueryDto.builder().cycleId(cycleId).build();
        long liveCount = liveResourceBookingService.countBookingItem(queryDto);
        Assert.isTrue(liveCount == 0L, "该刊例周期下已经存在直播预约, 若要禁用，请联系业务方删除预约");
        this.updateStatus(operator, cycleId, CycleStatus.INVALID);
    }

    private List<CptCycleDto> selectByExample(CptCyclePoExample example) {
        List<CptCyclePo> pos = cptCycleDao.selectByExample(example);
        List<CptCycleDto> cptCycleDtos = CollectionUtils.isEmpty(pos)
                ? Collections.emptyList()
                : pos.stream().map(this::convert).collect(Collectors.toList());
        populateExecutionTime(cptCycleDtos);
        return cptCycleDtos;
    }

    private CptCycleDto convert(CptCyclePo cptCyclePo) {
        CptCycleDto cptCycleDto = new CptCycleDto();
        BeanUtils.copyProperties(cptCyclePo, cptCycleDto);
        return cptCycleDto;
    }

    private void updateStatus(Operator operator, Integer cycleId, CycleStatus status) {

        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(cycleId, "刊例周期ID不可为空");

        CptCycleDto cptCycleDto = this.getCycleDtoById(cycleId);
        CycleStatus cycleStatus = CycleStatus.getByCode(cptCycleDto.getStatus());
        if (cycleStatus.equals(status)) {
            return;
        }
        CptCyclePo record = new CptCyclePo();
        record.setId(cycleId);
        record.setStatus(status.getCode());

        cptCycleDao.updateByPrimaryKeySelective(record);

        CptCycleLogBean logBean = CptCycleLogBean.builder()
                .id(cycleId)
                .name(cptCycleDto.getName())
                .statusDesc(status.getName())
                .build();
        gdLogService.insertLog(cycleId, GdLogFlag.CYCLE, LogOperateType.UPDATE_CYCLE_STATUS, operator, logBean);

    }

    private void populateExecutionTime(List<CptCycleDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return ;
        }
        for (CptCycleDto dto : dtos) {
            Integer cycleId = dto.getId();
            Integer orderProduct = BaseCptCycleProcessor.convertCptSaleType(SalesType.getByCode(dto.getSalesType())).getCode();
            CycleScheduleDto cycleSchedule = cycleScheduleService.getCycleSchedule(cycleId, orderProduct);
            if (Objects.nonNull(cycleSchedule)) {
                dto.setExecutionTime(cycleSchedule.getExecutionTime());
            }
        }
    }
}
