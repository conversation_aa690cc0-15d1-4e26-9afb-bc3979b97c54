package com.bilibili.cpt.platform.biz.service.creative;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.biz.creative.service.CreativeManuscriptService;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.creative.service.GdCreativeService;
import com.bilibili.cpt.platform.api.audit.dto.CptCreativeAuditDto;
import com.bilibili.cpt.platform.api.creative.dto.*;
import com.bilibili.cpt.platform.api.creative.service.ICptCreativeService;
import com.bilibili.cpt.platform.api.creative.service.IQueryCptCreativeService;
import com.bilibili.cpt.platform.api.order.dto.CptOrderDto;
import com.bilibili.cpt.platform.api.order.service.ICptOrderService;
import com.bilibili.cpt.platform.biz.handler.business.VideoHandler;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.common.CptConstants;
import com.bilibili.cpt.platform.common.CptCreativeStatus;
import com.bilibili.cpt.platform.common.LaunchStatus;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017年6月22日
 */
@Slf4j
@Service
public class CptCreativeService extends CptBaseService implements ICptCreativeService {

    @Autowired
    private ICptOrderService cptOrderService;
    @Autowired
    private ISoaLandingPageService soaLandingPageService;
    @Autowired
    private IQueryCptCreativeService queryCptCreativeService;

    @Autowired
    private BrandCptCreativeDelegate brandCptCreativeDelegate;
    @Autowired
    private ArchiveGrpcClient archiveGrpcClient;

    @Autowired
    private GdCreativeService gdCreativeService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private CreativeManuscriptService creativeManuscriptService;

    @Override
    public UrlHashDto uploadMaterial(Integer templateId, Integer materialType, BfsFile bfsFile) throws ServiceException {
        return brandCptCreativeDelegate.uploadMaterial(templateId, materialType, bfsFile);
    }

    @Override
    public Long create(Operator operator, NewCptCreativeDto newCptCreativeDto) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不可为空");
        Assert.notNull(newCptCreativeDto, "创意信息不可为空");
        Assert.isTrue(Utils.isPositive(newCptCreativeDto.getGdScheduleId()), "排期ID不可为空");
        RLock lock = super.getLock(newCptCreativeDto.getGdScheduleId(), CptConstants.GD_SCHEDULE_LOCK_SUFFIX);
        try {
            return brandCptCreativeDelegate.create(operator, newCptCreativeDto);
        } finally {
            log.info("CreateCreative unLock lockName: {}", newCptCreativeDto.getGdScheduleId() + CptConstants.GD_SCHEDULE_LOCK_SUFFIX);
            lock.unlock();
        }
    }

    @Override
    public void createForMultiSchedule(Operator operator, NewMultiScheduleCptCreativeDto multiScheduleCptCreativeDto) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不可为空");
        Assert.notNull(multiScheduleCptCreativeDto, "批量新建创意信息不可为空");
        Assert.notNull(multiScheduleCptCreativeDto.getNewCptCreativeDto(), "创意信息不可为空");
        Assert.notEmpty(multiScheduleCptCreativeDto.getScheduleIdList(), "排期ID不可为空");

        //校验内链投放
        List<ScheduleDto> scheduleDtos = queryScheduleService
                .getSchedulesInIds(multiScheduleCptCreativeDto.getScheduleIdList());
        boolean launchInnerJump = scheduleDtos.get(0).getLaunchInnerJump();
        scheduleDtos.forEach(scheduleDto -> Assert.isTrue(launchInnerJump == scheduleDto.getLaunchInnerJump(),
                "批量创建的创意,内链投放的开关必须要为同一个状态"));
        gdCreativeService.checkLaunchInnerJump(launchInnerJump,
                multiScheduleCptCreativeDto.getNewCptCreativeDto().getPromotionPurposeContent()
                , multiScheduleCptCreativeDto.getNewCptCreativeDto().getJumpType());

        List<RLock> lockList = new LinkedList<>();
        try {

            for (Integer scheduleId : multiScheduleCptCreativeDto.getScheduleIdList()) {
                RLock lock = getLock(scheduleId, CptConstants.GD_SCHEDULE_LOCK_SUFFIX, 0, 60);
                lockList.add(lock);
            }
            brandCptCreativeDelegate.createForMultiSchedule(operator, multiScheduleCptCreativeDto);
        } finally {
            for (RLock lock : lockList) {
                log.info("CreateCreative unLock lockName: {}", lock.getName());
                lock.unlock();
            }
        }
    }

    @Override
    public Long createBusinessCreative(Operator operator, NewCptCreativeDto newCptCreativeDto) throws ServiceException {
        return brandCptCreativeDelegate.createBusinessCreative(operator, newCptCreativeDto);
    }

    @SneakyThrows
    @Override
    public void update(Operator operator, UpdateCptCreativeDto updateCreativeDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人员不可为空");
        log.info("validateUpdateCreativeBasicInfo update-{}.", updateCreativeDto);
        Assert.notNull(updateCreativeDto, "创意信息不可为空");
        Assert.notNull(updateCreativeDto.getGdCreativeId(), "创意ID不可为空");

        GdCreativePo creativePo = brandCptCreativeDelegate.getGdCreativeById(updateCreativeDto.getGdCreativeId());
        CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(creativePo.getOrderId(), operator);
        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(creativePo.getScheduleId());
        boolean launchInnerJump = scheduleDto.getLaunchInnerJump();
        gdCreativeService.checkLaunchInnerJump(launchInnerJump, updateCreativeDto.getPromotionPurposeContent()
                , updateCreativeDto.getJumpType());

        if (cptOrderDto.getProduct() != null && cptOrderDto.getProduct().equals(OrderProduct.INVIT.getCode())) {
            RLock lock = this.getLock(creativePo.getOrderId(), CptConstants.ORDER_LOCK_SUFFIX);
            try {
                //updateCreativeDto.setEndTime(TimeUtils.getHourTimestamp(Utils.getEndSecondOfDay(updateCreativeDto.getEndTime())));
                updateCreativeDto.setEndTime(Utils.getEndSecondOfDay(updateCreativeDto.getEndTime()));
                brandCptCreativeDelegate.updateBusinessOrderCreative(operator, updateCreativeDto, cptOrderDto);
            } finally {
                lock.unlock();
            }
        } else {
            RLock lock = this.getLock(creativePo.getScheduleId(), CptConstants.GD_SCHEDULE_LOCK_SUFFIX);
            try {
                brandCptCreativeDelegate.update(operator, updateCreativeDto);
            } finally {
                lock.unlock();
            }
        }
    }

    @Override
    public void delete(Operator operator, Long brandCreativeId) {
        brandCptCreativeDelegate.delete(operator, brandCreativeId);
    }

    @Override
    public void deleteWithoutValidate(Operator operator, Long brandCreativeId) {
        brandCptCreativeDelegate.deleteWithoutValidate(operator, brandCreativeId);
    }

    @Override
    public void deleteByScheduleIdWithoutValidate(Operator operator, Integer gdScheduleId) {
        log.info("deleteByScheduleIdWithoutValidate with gdScheduleId {}", gdScheduleId);
        Map<Integer, List<CptCreativeDto>> map = this.getSchedule2CreativeMapInScheduleIds(Collections.singletonList(gdScheduleId));
        map.values().stream()
                .flatMap(Collection::stream)
                .forEach(dto -> this.deleteWithoutValidate(operator, dto.getCreativeId()));
    }

    @Override
    public void auditPass(Operator operator, List<CptCreativeAuditDto> auditDtos) {
        if (CollectionUtils.isEmpty(auditDtos)) {
            return;
        }
        for (CptCreativeAuditDto auditDto : auditDtos) {
            try {
                brandCptCreativeDelegate.auditPass(operator, auditDto);
            } catch (Exception e) {
                log.error("cpt创意审核通过失败, auditDto:{}", auditDto, e);
            }
        }
    }

    @Override
    public void offLine(Operator operator, List<CptCreativeAuditDto> auditDtos) {
        auditDtos.forEach(dto -> brandCptCreativeDelegate.offline(operator, dto, false));
    }

    @Override
    public void auditReject(Operator operator, List<CptCreativeAuditDto> auditDtos) {
        if (CollectionUtils.isEmpty(auditDtos)) {
            return;
        }
        for (CptCreativeAuditDto auditDto : auditDtos) {
            try {
                brandCptCreativeDelegate.auditReject(operator, auditDto);
            } catch (Exception e) {
                log.error("cpt创意审核驳回失败, auditDto:{}", auditDto, e);
            }
        }
    }

    @Override
    public UrlHashDto checkAndGetMaterialInfo(Integer templateId, Integer type, String url) throws ServiceException {
        return brandCptCreativeDelegate.checkAndGetMaterialInfo(templateId, type, url);
    }

    @Override
    public Map<Integer, Long> getSchedule2CreativeCountMapInScheduleIds(Operator operator, List<Integer> scheduleIds) {
        return queryCptCreativeService.getSchedule2CreativeCountMapInScheduleIds(operator, scheduleIds);
    }

    @Override
    public Long getCreativeCountByScheduleId(Operator operator, Integer scheduleId) {
        return queryCptCreativeService.getCreativeCountByScheduleId(operator, scheduleId);
    }

    @Override
    public Map<Integer, List<CptCreativeDto>> getSchedule2CreativeMapInScheduleIds(List<Integer> scheduleIds) {
        return queryCptCreativeService.getSchedule2CreativeMapInScheduleIds(scheduleIds);
    }

    @Override
    public Map<Integer, List<PeriodDto>> getSchedule2CreativeTimeMapInScheduleIds(List<Integer> scheduleIds) {
        return queryCptCreativeService.getSchedule2CreativeTimeMapInScheduleIds(scheduleIds);
    }

    @Override
    public List<PeriodDto> getSchedule2CreativeTimeByScheduleId(Integer scheduleId) {
        return queryCptCreativeService.getSchedule2CreativeTimeByScheduleId(scheduleId);
    }

    @Override
    public List<CptTemplateDto> getTemplatesByScheduleId(Operator operator, Integer scheduleId) {
        return queryCptCreativeService.getTemplatesByScheduleId(operator, scheduleId);
    }

    @Deprecated
    @Override
    public List<CptTemplateDto> getTemplatesByScheduleIds(Operator operator, List<Integer> scheduleIdList) {
        return queryCptCreativeService.getTemplatesByScheduleIds(operator, scheduleIdList);
    }

    @Override
    public Map<Integer, List<CptTemplateDto>> getTemplatesBySourceGroup(Operator operator, List<Integer> sourceIds) {
        return queryCptCreativeService.getTemplatesBySourceGroup(operator, sourceIds);
    }

    @Override
    public CptCreativeDto getCreativeById(Operator operator, Long creativeId) {
        return queryCptCreativeService.getCreativeById(creativeId);
    }

    @Override
    public List<PeriodDto> getOptionalPeriods(Operator operator, Integer scheduleId, Long creativeId) {
        return queryCptCreativeService.getOptionalPeriods(operator, scheduleId, creativeId);
    }

    @Override
    public List<PeriodDto> getMultiScheduleOptionalPeriods(Operator operator, List<Integer> scheduleIdList, Integer salesType) {
        return queryCptCreativeService.getMultiScheduleOptionalPeriods(operator, scheduleIdList, salesType);
    }

    @Override
    public PageResult<CptCreativeAllInfoDto> getCptCreatives(QueryCreativeParamDto queryCreativeParamDto, Integer page, Integer size) {
        return queryCptCreativeService.getCptCreatives(queryCreativeParamDto, page, size);
    }

    @Override
    public PageResult<CptCreativeAllInfoDto> getCptCreatives(Operator operator, QueryCreativeParamDto queryCreativeParamDto, Integer page, Integer size) {
        return queryCptCreativeService.getCptCreatives(operator, queryCreativeParamDto, page, size);
    }

    @Override
    public List<CptCreativeAllInfoDto> getCptLatestCreatives(Operator operator, QueryCreativeParamDto queryCreativeParamDto) {

        return queryCptCreativeService.getCptLatestCreatives(operator, queryCreativeParamDto);
    }

    @Override
    public List<CptCreativeAllInfoDto> getCptCreativeList(Operator operator, QueryCreativeParamDto queryCreativeParamDto) {
        return queryCptCreativeService.getCptCreativeList(operator, queryCreativeParamDto);
    }

    @Override
    public void refreshMgkCreativeStatusJob() {
        List<CptCreativeDto> mgkCreativeList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .beginTime(Utils.getToday())
                .endTime(Utils.getEndSecondOfDay(Utils.getSomeDayAfter(Utils.getToday(), 15)))
                .jumpType(GdJumpType.MGK_PAGE_ID.getCode())
                .cptCreativeStatusList(Lists.newArrayList(CptCreativeStatus.TO_BE_AUDIT.getCode(), CptCreativeStatus.AUDIT_PASS.getCode()))
                .build());
        if (CollectionUtils.isEmpty(mgkCreativeList)) {
            return;
        }

        List<Long> mgkPageIds = mgkCreativeList.stream().map(CptCreativeDto::getMgkPageId).filter(pid -> pid > 0).distinct().collect(Collectors.toList());
        List<Long> publishedMgkPageIds = soaLandingPageService.getPublishedLandingPageIds(mgkPageIds);
        List<Long> needRejectMgkPageIds = mgkPageIds.stream().filter(pid -> !publishedMgkPageIds.contains(pid)).collect(Collectors.toList());

        this.auditRejectByMgkPageIds(Operator.SYSTEM, needRejectMgkPageIds);

    }

    @Override
    public void auditRejectByMgkPageIds(Operator operator, List<Long> mgkPageIds) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        if (CollectionUtils.isEmpty(mgkPageIds)) {
            return;
        }

        List<CptCreativeDto> mgkCreativeList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .beginTime(Utils.getToday())
                .endTime(Utils.getEndSecondOfDay(Utils.getSomeDayAfter(Utils.getToday(), 15)))
                .mgkPageIds(mgkPageIds)
                .jumpType(GdJumpType.MGK_PAGE_ID.getCode())
                .cptCreativeStatusList(Lists.newArrayList(CptCreativeStatus.TO_BE_AUDIT.getCode(),
                        CptCreativeStatus.AUDIT_PASS.getCode()))
                .build());
        if (CollectionUtils.isEmpty(mgkCreativeList)) {
            return;
        }

        log.info("auditRejectByMgkPageIds total: [{}]", mgkCreativeList.size());
        List<CptCreativeAuditDto> creativeDtos = mgkCreativeList.stream().map(c -> CptCreativeAuditDto.builder()
                .gdCreativeId(c.getCreativeId())
                .version(c.getVersion())
                .reason("建站工具落地页已下线")
                .build()).collect(Collectors.toList());
        this.auditReject(operator, creativeDtos);
    }

    @Override
    public List<CptCreativeDto> refreshCreativeStatusByVideoJob() {
        log.info("refreshCreativeStatusByVideo start...");
        List<CptCreativeDto> videoCreativeList = this.processCreativeStatusByVideo(0L, null, Arrays.asList(GdJumpType.VIDEO_WEB.getCode(),
                GdJumpType.VIDEO_MOBILE.getCode()), CptCreativeDto::getVideoId);
        log.info("refreshCreativeStatusByVideo end...");

        log.info("refreshCreativeStatusByInvitationAvid start...");
        List<CptCreativeDto> avCreativeList = this.processCreativeStatusByVideo(null, 0L, null, CptCreativeDto::getInvitationAvid);
        log.info("refreshCreativeStatusByInvitationAvid end...");

        videoCreativeList.addAll(avCreativeList);
        return videoCreativeList;

    }


//    @Override
//    public void refreshCptCreativeToGdCreative(Timestamp cBeginTime, Timestamp cEndTime, List<Integer> cptCreativeIds) {
//        brandCptCreativeDelegate.refreshCptCreativeToGdCreative(cBeginTime, cEndTime, cptCreativeIds);
//    }
//
//    @Override
//    public void refreshButtonCopy() {
//        brandCptCreativeDelegate.refreshButtonCopy();
//    }

    @Override
    public CptCreativeDto getCreativeByIdWithoutOperator(Long creativeId) {
        return queryCptCreativeService.getCreativeByIdWithoutOperator(creativeId);
    }

    private List<CptCreativeDto> processCreativeStatusByVideo(Long videoId, Long avId, List<Integer> jumpTypes, Function<CptCreativeDto, Long> mapper) {
        List<CptCreativeDto> videoCreativeList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                .currentHourTime(Utils.getBeginOfHour(Utils.getNow()))
                .gtAvid(avId)
                .gtVideoId(videoId)
                .jumpTypes(jumpTypes)
                .cptCreativeStatusList(Lists.newArrayList(CptCreativeStatus.TO_BE_AUDIT.getCode(),
                        CptCreativeStatus.AUDIT_PASS.getCode(),
                        CptCreativeStatus.WAIT_OFF_LINE.getCode()))
                .build());
        //排除OGV的创意
        videoCreativeList = videoCreativeList.stream()
                .filter(c -> !OrderProduct.isOgv(c.getOrderProduct()))
                .collect(Collectors.toList());
        log.info("refreshCreativeStatusByCooperateVideoJob queryCptCreativeService.getCptCreativeBaseDtos size: [{}]", videoCreativeList.size());
        if (CollectionUtils.isEmpty(videoCreativeList)) {
            return Collections.emptyList();
        }

        List<Long> videoIds = videoCreativeList.stream().map(mapper).filter(pid -> pid > 0).distinct().collect(Collectors.toList());
        Map<Long, List<CptCreativeDto>> videoCreativeMap = videoCreativeList.stream().collect(Collectors.groupingBy(mapper));

        List<CptCreativeDto> allCreativeList = Lists.newArrayList();
        CollectionHelper.processInBatches(videoIds, 50, vids -> {
            try {
                Map<Long, ArchiveInfoBo> archiveMap = archiveGrpcClient.queryArchiveInfo(vids);
                Map<Integer, List<CptCreativeDto>> status2CreativeMap = vids.stream().filter(vid -> {
                    if (archiveMap.containsKey(vid)) {
                        ArchiveInfoBo archiveDetail = archiveMap.get(vid);
                        return archiveDetail == null
                                || !VideoHandler.validInvitationVideoStates.contains(archiveDetail.getState());
                    }
                    return true;
                }).map(videoCreativeMap::get).flatMap(Collection::stream).collect(Collectors.groupingBy(CptCreativeDto::getCptCreativeStatus));

                status2CreativeMap.keySet().forEach(status -> {
                    List<CptCreativeAuditDto> creatives = status2CreativeMap.get(status).stream().map(c -> CptCreativeAuditDto.builder()
                            .gdCreativeId(c.getCreativeId())
                            .version(c.getVersion())
                            .reason("视频不可播放")
                            .build()).collect(Collectors.toList());
                    if (LaunchStatus.MODIFY_WAIT_OFF_LINE.getCode() == status) {
                        this.offLine(Operator.SYSTEM, creatives);
                    } else {
                        this.auditReject(Operator.SYSTEM, creatives);
                    }
                });
                allCreativeList.addAll(status2CreativeMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("refreshCreativeStatusByVideoJob archiveManager.getArchivesByAids avids: [{}]", vids, e);
            }
        });
        return allCreativeList;
    }

    @Override
    public List<CptTemplateDto> getTemplates(Operator operator, QueryCreativeTemplateParamDto param) {
        return queryCptCreativeService.getTemplates(operator, param);
    }
}
