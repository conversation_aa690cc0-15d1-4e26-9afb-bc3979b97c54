package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptBusinessSidePo;
import com.bilibili.cpt.platform.biz.po.CptBusinessSidePoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CptBusinessSideDao {
    long countByExample(CptBusinessSidePoExample example);

    int deleteByExample(CptBusinessSidePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CptBusinessSidePo record);

    int insertSelective(CptBusinessSidePo record);

    List<CptBusinessSidePo> selectByExample(CptBusinessSidePoExample example);

    CptBusinessSidePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CptBusinessSidePo record, @Param("example") CptBusinessSidePoExample example);

    int updateByExample(@Param("record") CptBusinessSidePo record, @Param("example") CptBusinessSidePoExample example);

    int updateByPrimaryKeySelective(CptBusinessSidePo record);

    int updateByPrimaryKey(CptBusinessSidePo record);
}