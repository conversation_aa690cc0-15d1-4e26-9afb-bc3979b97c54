package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptUserPo;
import com.bilibili.cpt.platform.biz.po.CptUserPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CptUserDao {
    long countByExample(CptUserPoExample example);

    int deleteByExample(CptUserPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CptUserPo record);

    int insertSelective(CptUserPo record);

    List<CptUserPo> selectByExample(CptUserPoExample example);

    CptUserPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CptUserPo record, @Param("example") CptUserPoExample example);

    int updateByExample(@Param("record") CptUserPo record, @Param("example") CptUserPoExample example);

    int updateByPrimaryKeySelective(CptUserPo record);

    int updateByPrimaryKey(CptUserPo record);
}