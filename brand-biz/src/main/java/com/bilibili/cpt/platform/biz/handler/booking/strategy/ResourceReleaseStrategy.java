package com.bilibili.cpt.platform.biz.handler.booking.strategy;

import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;

import java.util.List;

/**
 * 资源释放策略
 *
 * <AUTHOR>
 * @date 2023/3/24 16:19
 */
public interface ResourceReleaseStrategy<R> {
    /**
     * 是否适配当前释放操作
     *
     * @param releaseRequest 释放请求参数，供具体策略识别业务逻辑使用 （请不要更改数据）
     * @return true：则调用{@link #matchBookings(ReleaseRequestBean)}，false：忽略
     */
    boolean match(ReleaseRequestBean releaseRequest);

    /**
     * 根据{@param releaseRequest} 匹配出需要释放的预订
     *
     * @param releaseRequest 释放请求参数，供具体策略识别业务逻辑使用 （请不要更改数据）
     * @return 返回释放、预警等需要进一步处理的的预订记录
     */
    List<R> matchBookings(ReleaseRequestBean releaseRequest);

    /**
     * 释放回调
     */
    void callbackAfterReleased(ReleaseRequestBean releaseRequest);
}
