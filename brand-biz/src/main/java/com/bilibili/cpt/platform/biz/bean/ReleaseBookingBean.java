package com.bilibili.cpt.platform.biz.bean;

import com.bilibili.adp.common.annotation.MailTableDesc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReleaseBookingBean {

    @MailTableDesc(value = "预订人", sort = 1)
    private String operator;

    private Integer sourceId;

    @MailTableDesc(value = "资源位名称", sort = 2)
    private String sourceName;

    private Timestamp groupDate;

    @MailTableDesc(value = "资源时间(排期时间)", sort = 3)
    private String groupDateStr;

    private Timestamp releaseDate;

    @MailTableDesc(value = "资源释放时间", sort = 4)
    private String releaseDateStr;

    @MailTableDesc(value = "订单ID", sort = 5)
    private Integer orderId;

    @MailTableDesc(value = "订单名称", sort = 6)
    private String orderName;

    @MailTableDesc(value = "排期ID", sort = 7)
    private Integer scheduleId;

    @MailTableDesc(value = "排期名称", sort = 8)
    private String scheduleName;

    private Timestamp sourceBookingTime;

    @MailTableDesc(value = "资源预定时间", sort = 9)
    private String sourceBookingTimeStr;
}
