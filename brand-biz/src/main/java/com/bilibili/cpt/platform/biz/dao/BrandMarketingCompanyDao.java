package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.BrandMarketingCompanyPo;
import com.bilibili.cpt.platform.biz.po.BrandMarketingCompanyPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BrandMarketingCompanyDao {
    long countByExample(BrandMarketingCompanyPoExample example);

    int deleteByExample(BrandMarketingCompanyPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BrandMarketingCompanyPo record);

    int insertBatch(List<BrandMarketingCompanyPo> records);

    int insertUpdateBatch(List<BrandMarketingCompanyPo> records);

    int insert(BrandMarketingCompanyPo record);

    int insertUpdateSelective(BrandMarketingCompanyPo record);

    int insertSelective(BrandMarketingCompanyPo record);

    List<BrandMarketingCompanyPo> selectByExample(BrandMarketingCompanyPoExample example);

    BrandMarketingCompanyPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BrandMarketingCompanyPo record, @Param("example") BrandMarketingCompanyPoExample example);

    int updateByExample(@Param("record") BrandMarketingCompanyPo record, @Param("example") BrandMarketingCompanyPoExample example);

    int updateByPrimaryKeySelective(BrandMarketingCompanyPo record);

    int updateByPrimaryKey(BrandMarketingCompanyPo record);
}