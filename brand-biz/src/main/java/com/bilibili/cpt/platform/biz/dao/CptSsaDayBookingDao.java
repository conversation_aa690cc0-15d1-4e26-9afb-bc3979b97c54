package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptSsaDayBookingPo;
import com.bilibili.cpt.platform.biz.po.CptSsaDayBookingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CptSsaDayBookingDao {
    long countByExample(CptSsaDayBookingPoExample example);

    int deleteByExample(CptSsaDayBookingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CptSsaDayBookingPo record);

    int insertBatch(List<CptSsaDayBookingPo> records);

    int insertUpdateBatch(List<CptSsaDayBookingPo> records);

    int insert(CptSsaDayBookingPo record);

    int insertUpdateSelective(CptSsaDayBookingPo record);

    int insertSelective(CptSsaDayBookingPo record);

    List<CptSsaDayBookingPo> selectByExample(CptSsaDayBookingPoExample example);

    CptSsaDayBookingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CptSsaDayBookingPo record, @Param("example") CptSsaDayBookingPoExample example);

    int updateByExample(@Param("record") CptSsaDayBookingPo record, @Param("example") CptSsaDayBookingPoExample example);

    int updateByPrimaryKeySelective(CptSsaDayBookingPo record);

    int updateByPrimaryKey(CptSsaDayBookingPo record);
}