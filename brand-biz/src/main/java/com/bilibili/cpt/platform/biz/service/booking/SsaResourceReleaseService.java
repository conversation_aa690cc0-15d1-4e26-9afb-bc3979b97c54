package com.bilibili.cpt.platform.biz.service.booking;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.MailUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.When;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.brand.api.booking.dto.*;
import com.bilibili.brand.api.booking.service.ISsaPlusResourceReleaseService;
import com.bilibili.brand.api.common.enums.CycleStatus;
import com.bilibili.brand.api.common.enums.PreBookingStatus;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.biz.account.dao.CrmOrderStatusRecordDao;
import com.bilibili.brand.biz.account.po.CrmOrderStatusRecordPoExample;
import com.bilibili.brand.biz.log.bean.ResourceBookingLogBean;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.dto.CptUserDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.business_side.service.ICptUserService;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeDto;
import com.bilibili.cpt.platform.api.creative.dto.QueryCreativeParamDto;
import com.bilibili.cpt.platform.api.creative.service.IQueryCptCreativeService;
import com.bilibili.cpt.platform.api.location.dto.CptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceBusinessSideLimitDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceBusinessSidePreBookingDto;
import com.bilibili.cpt.platform.api.location.dto.SourceConfigDto;
import com.bilibili.cpt.platform.api.location.service.ICptCycleService;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.api.order.dto.CptOrderBaseDto;
import com.bilibili.cpt.platform.api.order.dto.CptOrderDto;
import com.bilibili.cpt.platform.api.order.service.ICptOrderService;
import com.bilibili.cpt.platform.api.schedule.service.ICptScheduleService;
import com.bilibili.cpt.platform.biz.dao.CptSourceDayBookingDao;
import com.bilibili.cpt.platform.biz.dao.CptSourceDayBookingExtDao;
import com.bilibili.cpt.platform.biz.po.CptSourceDayBookingPo;
import com.bilibili.cpt.platform.biz.po.CptSourceDayBookingPoExample;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.biz.service.schedule.CptScheduleDelegate;
import com.bilibili.cpt.platform.biz.utils.ExampleUtils;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.crm.platform.common.CrmOrderStatus;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: booking 操作log请参看切面 ResourceBookingServiceAspect
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Service
@Slf4j
public class SsaResourceReleaseService extends CptBaseService implements ISsaPlusResourceReleaseService {
    @Autowired
    private CptSourceDayBookingDao cptSourceDayBookingDao;
    @Autowired
    private CptSourceDayBookingExtDao cptSourceDayBookingExtDao;
    @Autowired
    private ICptSourceService cptSourceService;
    @Autowired
    private ICptCycleService cptCycleService;
    @Autowired
    private IQuerySourceService querySourceService;
    @Autowired
    private IBusinessSideService businessSideService;
    @Autowired
    private IQueryCptCreativeService queryCptCreativeService;
    @Autowired
    private IMailService mailService;
    @Autowired
    private ICptUserService cptUserService;
    @Autowired
    private ICptOrderService cptOrderService;
    @Autowired
    private CrmOrderStatusRecordDao crmOrderStatusRecordDao;
    @Autowired
    private CptScheduleDelegate cptScheduleDelegate;
    @Autowired
    private ICptScheduleService cptScheduleService;

    @Autowired
    private CptUniteBookingMappingService bookingMappingService;

    @Autowired
    private IGdLogService logService;

    private final static BusinessSideBaseDto emptyBuSideDto = BusinessSideBaseDto.builder()
            .name("")
            .build();

    /**
     * 预约日期与资源日期差值，默认是30个自然日
     * 因为预约日期与资源日期差值不一样，自动资源释放的逻辑会不一样
     */
    @Value("${cpt.booking.booking_resource_range:30}")
    private Integer booking_resource_range;
    @Value("${cpt.booking.email_notify_range:2}")
    private Integer email_notify_range;
    @Value("#{'${cpt.booking.release.outer_business_side.source:3145,3146,3152,3153,3181,3182}'.split(',')}")
    private List<Integer> outerBusinessSideAutoReleaseSourceIdList;
    @Value("${cpt.booking.release.outer_business_side.focus.time.start}")
    private Long outerBusinessSideFocusReleaseStartTime;

    private final static Integer MAX_STEP = 1000;

    private Locale localeCN = Locale.SIMPLIFIED_CHINESE;

    private static final Set<Integer> OUTER_BUSINESS_SIDE_AUTO_RELEASE_DATE_LIST = Sets.newHashSet(7, 3, 1, 0);

    private static final Map<Integer, Integer> OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP = new HashMap<>();

    @PostConstruct
    public void init() {
        // key: 当前日期和投放日期gap
        // value: 当前日期和释放日期gap
        OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.put(9, 2);
        OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.put(8, 1);
        OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.put(5, 2);
        OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.put(4, 1);
        OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.put(2, 1);
    }

    @Override
    public long countBookingItemByCycleId(Integer cycleId) {
        Assert.notNull(cycleId, "刊例ID不可为空");
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(cycleId);
        return cptSourceDayBookingDao.countByExample(example);
    }

    @Override
    public List<BookingItemDto> queryResourceBooking(BookingQueryDto queryDto) {
        CptSourceDayBookingPoExample example = buildExampleByQueryDto(queryDto);
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertBookingPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<BookingItemDto> queryResourceBooking(Integer scheduleId, Timestamp maxTime) {
        List<CptSourceDayBookingPo> pos = queryResourceBookingPo(scheduleId, maxTime);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertBookingPo2Dto).collect(Collectors.toList());
    }

    private List<CptSourceDayBookingPo> queryResourceBookingPo(Integer scheduleId, Timestamp maxTime) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCptScheduleIdEqualTo(scheduleId)
                .andGroupDateLessThan(maxTime);

        return cptSourceDayBookingDao.selectByExample(example);
    }

    @Override
    public List<BookingItemDetailDto> convertToDetailDto(List<BookingItemDto> list) {
        List<BookingItemDetailDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> busIds = list.stream()
                .map(BookingItemDto::getBusinessSideId)
                .distinct().collect(Collectors.toList());
        Set<Integer> cptOrderIdSet = list.stream()
                .map(BookingItemDto::getCptOrderId)
                .collect(Collectors.toSet());

        Map<Integer, BusinessSideBaseDto> busBaseDtoMap = businessSideService.getBusinessSideMapInIds(busIds);
        Map<Integer, String> orderNameMap = cptOrderService.getOrderNameMapInOrderIdSet(cptOrderIdSet);
        BusinessSideBaseDto emptyBuSideDto = BusinessSideBaseDto.builder()
                .name("")
                .build();

        return list.stream()
                .map(item -> BookingItemDetailDto.detailBuilder()
                        .itemDto(item)
                        .cptOrderName(orderNameMap.getOrDefault(item.getCptOrderId(), ""))
                        .businessSideName(busBaseDtoMap.getOrDefault(item.getBusinessSideId(), emptyBuSideDto).getName())
                        .detailBuild())
                .collect(Collectors.toList());
    }

    @Override
    public BookingItemDto queryResourceBookingById(Integer id) {
        Assert.notNull(id, "预约ID不可为空");
        CptSourceDayBookingPo po = cptSourceDayBookingDao.selectByPrimaryKey(id);
        return po == null ? null : this.convertBookingPo2Dto(po);
    }

    @Override
    public List<BookingItemDto> queryResourceBookingByIds(List<Integer> ids) {
        Assert.isTrue(!CollectionUtils.isEmpty(ids), "预约ID集合不可为空");
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdIn(ids);
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convertBookingPo2Dto).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer deleteResourceBookingById(Integer bookingId, Operator operator) {
        BookingItemDto dto = this.queryResourceBookingById(bookingId);
        log.info("deleteResourceBookingById {}", dto);
        return this.deleteResourceBooking(dto, operator);
    }

    @Override
    public Integer deleteResourceBookingByIdWithoutValidate(Integer id, Operator operator) {
        BookingItemDto dto = this.queryResourceBookingById(id);
        log.info("deleteResourceBookingByIdWithoutValidate  id {}", id);
        return this.deleteResourceBookingWithoutValidate(dto, operator);
    }

    @Override
    public Integer deleteResourceBookingWithoutValidate(BookingItemDto dto, Operator operator) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(dto.getId());
        CptSourceDayBookingPo record = CptSourceDayBookingPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .operator(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                        : operator.getBilibiliUserName())
                .build();

        RLock bookingLock = super.getLock(dto.getSourceId(), dto.getGroupDate() + CptConstants.BOOKING_LOCK_SUFFIX);
        try {
            Integer count = cptSourceDayBookingDao.updateByExampleSelective(record, example);
            log4DeleteResourceBookingWithoutValidate(dto, operator);
            return count;
        } finally {
            if (bookingLock != null) {
                bookingLock.unlock();
            }
        }

    }

    private void log4DeleteResourceBookingWithoutValidate(BookingItemDto dto, Operator operator) {
        try {
            ResourceBookingLogBean logBean = ResourceBookingLogBean.builder().build();
            BeanUtils.copyProperties(dto, logBean);
            logService.insertLog(dto.getId(), GdLogFlag.BOOKING, LogOperateType.DELETE_BOOKING, operator, logBean);
            log.info("insert log for delete cpt booking ");
        } catch (Exception e) {
            log.warn("failed to add log ", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<String> addResourceBooking(NewResourceBookingDto dto, Operator operator) {
        //能否支持联合预定
        boolean supportUniteBooking = false;
        List<SourceAllInfoDto> allInfoDtos = null;
        if(dto.getIsUniteBooking()){
            allInfoDtos = bookingMappingService.getMappingDtoById(dto.getSourceId());
            if(!CollectionUtils.isEmpty(allInfoDtos)){
                dto.setSourceIds(allInfoDtos.stream().map(SourceAllInfoDto::getSourceId).collect(Collectors.toList()));
                supportUniteBooking = true;
            }
        }

        if(dto.getIsUniteBooking() && !CollectionUtils.isEmpty(dto.getSourceIds())){
            dto.getSourceIds().forEach(t->{
                dto.setSourceId(t);
                this.addValidateAndDecorate(dto, operator);
                for (SingleResourceBookingDto singleDto : dto.convert2SingleResource()) {
                    booking(singleDto, operator);
                }
            });
        }else {
            this.addValidateAndDecorate(dto, operator);
            for (SingleResourceBookingDto singleDto : dto.convert2SingleResource()) {
                booking(singleDto, operator);
            }
        }

        if(dto.getIsUniteBooking() && supportUniteBooking){
            return allInfoDtos.stream().map(SourceAllInfoDto::getName).collect(Collectors.toList());
        }
        return null;
    }

    private void booking(SingleResourceBookingDto singleDto, Operator operator){
        RLock rLock = null;
        try {
            rLock = super.getLock(singleDto.getSourceId(),
                    singleDto.getGroupDate() + CptConstants.BOOKING_LOCK_SUFFIX);
            this.addSingleResourceBooking(singleDto, operator);
       } finally {
            if (rLock != null) {
                rLock.unlock();
            }
        }
    }

    public void addSingleResourceBooking(SingleResourceBookingDto singleDto, Operator operator) {
        Transaction t = Cat.getProducer().newTransaction("BOOKING", "addResourceBooking");
        try {
            this.addValidate(singleDto);
            CptSourceDayBookingPo po = this.convertDto2Po(singleDto);
            po.setBusinessSideId(singleDto.getBusinessSideBaseDto().getId());
            po.setOperator(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                    : operator.getBilibiliUserName());
            po.setTopViewSourceId(singleDto.getTopViewSourceId());
            po.setResourceType(singleDto.getResourceType());
            cptSourceDayBookingDao.insertSelective(po);
            this.log4AddResourceBooking(po, operator);
            t.setStatus(Transaction.SUCCESS);
        } finally {
            t.complete();
        }
    }

    private void log4AddResourceBooking(CptSourceDayBookingPo po, Operator operator) {
        try {
            ResourceBookingLogBean logBean = ResourceBookingLogBean.builder()
                    .id(po.getId())
                    .platformId(po.getPlatformId())
                    .pageId(po.getPageId())
                    .sourceId(po.getSourceId())
                    .resourceId(po.getResourceId())
                    .beginDate(po.getGroupDate())
                    .endDate(po.getGroupDate())
                    .orderId(po.getCptOrderId())
                    .scheduleId(po.getCptScheduleId())
                    .cycleId(po.getCycleId())
                    .build();
            BeanUtils.copyProperties(po, logBean);
            logService.insertLog(po.getId(), GdLogFlag.BOOKING, LogOperateType.ADD_BOOKING, operator, logBean);
            log.info("insert log for add cpt booking");
        } catch (Exception e) {
            log.warn("failed add log ", e);
        }

    }

    @Override
    public List<BookingItemResultDto> addMultiResourceBooking(List<NewResourceBookingDto> dtoList, Operator operator) {
        Assert.isTrue(!CollectionUtils.isEmpty(dtoList), "预约集合不可为空");
        List<BookingItemResultDto> result = new ArrayList<>();
        BusinessSideBaseDto businessSideBaseDto = businessSideService.getBusinessSideByAccountId(operator.getOperatorId());
        for (NewResourceBookingDto dto : dtoList) {
            this.addValidateAndDecorate(dto, operator);
            dto.setBusinessSideBaseDto(businessSideBaseDto);
            RLock bookingLock;
            for (SingleResourceBookingDto singleDto : dto.convert2SingleResource()) {
                bookingLock = super.getLock(dto.getSourceId(), singleDto.getGroupDate()
                        + CptConstants.BOOKING_LOCK_SUFFIX);
                try {
                    this.addSingleResourceBooking(singleDto, operator);
                    result.add(this.convertSingleDto2ResultDto(singleDto, 1));
               } finally {
                    if (bookingLock != null) {
                        bookingLock.unlock();
                    }
                }
            }

        }
        return result;
    }

    @Override
    public List<BookingItemResultDto> deleteMultiResourceBooking(List<DeleteResourceBookingDto> dtoList, Operator operator) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        List<BookingItemResultDto> result = new ArrayList<>();
        for (DeleteResourceBookingDto dto : dtoList) {
            for (Integer id : dto.getIds()) {
                BookingItemResultDto resultDto = new BookingItemResultDto();
                try {
                    BookingItemDto itemDto = this.queryResourceBookingById(id);
                    resultDto = BookingItemResultDto.builder()
                            .accountId(itemDto.getAccountId())
                            .platformId(itemDto.getPlatformId())
                            .platformName(itemDto.getPlatformName())
                            .pageId(itemDto.getPageId())
                            .pageName(itemDto.getPageName())
                            .resourceId(itemDto.getResourceId())
                            .resourceName(itemDto.getResourceName())
                            .sourceId(itemDto.getSourceId())
                            .sourceName(itemDto.getSourceName())
                            .groupDate(itemDto.getGroupDate())
                            .build();
                    this.deleteResourceBooking(itemDto, operator);
                    resultDto.setSuccess(1);
                } catch (Exception e) {
                    log.error("failed to delete resource booking for id {} with error ", id, e);
                    resultDto.setSuccess(0);
                } finally {
                    result.add(resultDto);
                }
            }
        }
        return result;
    }

    private Integer getBookingScheduleStatus(Integer accountId, boolean isScheduled) {
        BusinessSideBaseDto dto = businessSideService.getBusinessSideByAccountId(accountId);
        if (isScheduled) {
            if (dto.getIsAdmin() == 1) {
                return CptBookingStatus.LOCKED_SCHEDULED.getCode();
            } else {
                return CptBookingStatus.BOOKED_SCHEDULED.getCode();
            }
        } else {
            if (dto.getIsAdmin() == 1) {
                return CptBookingStatus.LOCKED.getCode();
            } else {
                return CptBookingStatus.BOOKED.getCode();
            }
        }
    }

    @Override
    public void validateScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(updateDto.getAccountId())
                .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED.getCode(), CptBookingStatus.LOCKED.getCode()))
                .andSourceIdEqualTo(updateDto.getSourceId())
                .andGroupDateGreaterThanOrEqualTo(updateDto.getBeginDate())
                .andGroupDateLessThanOrEqualTo(updateDto.getEndDate());
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingExtDao.selectOneCycle(example);
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "根据条件未查询到可用预约");
        int dayRange = Utils.getDateSpace(updateDto.getBeginDate(), updateDto.getEndDate());
        Assert.isTrue(dayRange == pos.size(), "根据条件未查询到完整预约");
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer scheduleTheResourceBooking(UpdateScheduleBookingDto updateDto) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(updateDto.getAccountId())
                .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED.getCode(), CptBookingStatus.LOCKED.getCode()))
                .andSourceIdEqualTo(updateDto.getSourceId())
                .andGroupDateGreaterThanOrEqualTo(updateDto.getBeginDate())
                .andGroupDateLessThanOrEqualTo(updateDto.getEndDate())
                .andTopViewSourceIdEqualTo(0);
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingExtDao.selectOneCycle(example);
        Assert.isTrue(!CollectionUtils.isEmpty(pos), "根据条件未查询到可用预约");
        int dayRange = Utils.getDateSpace(updateDto.getBeginDate(), updateDto.getEndDate());
        Assert.isTrue(dayRange == pos.size(), "根据条件未查询到完整预约");

        CptSourceDayBookingPo updatePo = CptSourceDayBookingPo.builder()
                .cptOrderId(updateDto.getOrderId())
                .cptScheduleId(updateDto.getScheduleId())
                .status(this.getBookingScheduleStatus(updateDto.getAccountId(), true))
                .build();
        CptSourceDayBookingPoExample updateExample = new CptSourceDayBookingPoExample();
        updateExample.or()
                .andIdIn(pos.stream()
                        .map(CptSourceDayBookingPo::getId)
                        .collect(Collectors.toList()));
        Integer count = cptSourceDayBookingDao.updateByExampleSelective(updatePo, updateExample);
        this.log4ScheduleTheResourceBooking(updateDto);
        return count;
    }

    private void log4ScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto) {
        try {
            ResourceBookingLogBean logBean = ResourceBookingLogBean.builder()
                    .businessSideId(updateDto.getAccountId())
                    .sourceId(updateDto.getSourceId())
                    .beginDate(updateDto.getBeginDate())
                    .endDate(updateDto.getEndDate())
                    .orderId(updateDto.getOrderId())
                    .scheduleId(updateDto.getScheduleId())
                    .build();
            logService.insertLog(GdLogFlag.BOOKING.getCode(), GdLogFlag.BOOKING, LogOperateType.UPDATE_SCHEDULE_BOOKING, updateDto.getOperator(), logBean);
            log.info("insert log for schedule cpt booking");
        } catch (Exception e) {
            log.warn("failed to add log ", e);
        }

    }

    private void log4UnScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto) {
        try {
            ResourceBookingLogBean logBean = ResourceBookingLogBean.builder()
                    .businessSideId(updateDto.getAccountId())
                    .sourceId(updateDto.getSourceId())
                    .beginDate(updateDto.getBeginDate())
                    .endDate(updateDto.getEndDate())
                    .orderId(updateDto.getOrderId())
                    .scheduleId(updateDto.getScheduleId())
                    .build();
            logService.insertLog(GdLogFlag.BOOKING.getCode(), GdLogFlag.BOOKING, LogOperateType.UPDATE_UNSCHEDULED_BOOKING, updateDto.getOperator(), logBean);
            log.info("insert log for unscheduled cpt booking");
        } catch (Exception e) {
            log.warn("failed to add log ", e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer unScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED_SCHEDULED.getCode(), CptBookingStatus.LOCKED_SCHEDULED.getCode()))
                .andCptScheduleIdEqualTo(updateDto.getScheduleId());

        CptSourceDayBookingPo po = CptSourceDayBookingPo.builder()
                .status(this.getBookingScheduleStatus(updateDto.getAccountId(), false))
                .cptOrderId(0)
                .cptScheduleId(0)
                .build();
        Integer count = cptSourceDayBookingDao.updateByExampleSelective(po, example);
        this.log4UnScheduleTheResourceBooking(updateDto);
        return count;
    }

    @Override
    public List<Timestamp> queryAllBookedTime(BookingTimeQueryDto queryDto, Operator operator) {
        Assert.notNull(queryDto.getSourceId(), "位次ID不可为空");
        Assert.notNull(operator, "operator不可为空");

        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        CptSourceDayBookingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andSourceIdEqualTo(queryDto.getSourceId());
        criteria.andStatusIn(CptBookingStatus.BOOKED_STATUS_LIST);
        if (queryDto.getStartTime() != null && queryDto.getEndTime() != null) {
            criteria.andGroupDateGreaterThanOrEqualTo(queryDto.getStartTime());
            criteria.andGroupDateLessThanOrEqualTo(queryDto.getEndTime());
        }

        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream()
                .map(CptSourceDayBookingPo::getGroupDate)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Timestamp> queryAllMyBookedTime(BookingTimeQueryDto queryDto, Operator operator) {
        Assert.notNull(queryDto.getSourceId(), "位次ID不可为空");
        Assert.notNull(operator, "operator不可为空");

        CptSourceDayBookingPoExample example = buildExampleByTimeQueryDto(queryDto);
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream()
                .map(CptSourceDayBookingPo::getGroupDate)
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<Timestamp> multiQueryMyBookedTime(BookingTimeQueryDto queryDto, Operator operator) {
        List<Timestamp> result = new ArrayList<>();
        Assert.isTrue(!CollectionUtils.isEmpty(queryDto.getSourceIds()), "位次ID不可为空");
        Assert.notNull(operator, "operator不可为空");

        CptSourceDayBookingPoExample example = buildExampleByTimeQueryDto(queryDto);
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return result;
        }

        Map<Integer, Set<Timestamp>> sourceIdMap = pos.stream()
                .collect(Collectors.groupingBy(CptSourceDayBookingPo::getSourceId,
                        Collectors.mapping(CptSourceDayBookingPo::getGroupDate, Collectors.toSet())));

        if (sourceIdMap.keySet().size() < queryDto.getSourceIds().size()) {
            return result;
        }

        List<Integer> otherKeys = new ArrayList<>(sourceIdMap.keySet());
        Integer key = otherKeys.remove(0);

        Set<Timestamp> keyTimes = sourceIdMap.get(key);
        for (Timestamp keyTime : keyTimes) {
            boolean containCommonTime = true;

            for (Integer otherKey : otherKeys) {
                Set<Timestamp> otherKeySet = sourceIdMap.get(otherKey);
                if (!otherKeySet.contains(keyTime)) {
                    containCommonTime = false;
                }
            }
            if (containCommonTime) {
                result.add(keyTime);
            }
        }
        return result;
    }

    /**
     * 自动释放的记录不包含预约时间在昨天以前的
     */
    @Override
    public void bookingAutoRelease(List<TopViewSourceReleaseDto> willReleaseTopViewSourceList, List<Integer> releasedTopViewSourceIdList) {
        Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap = new HashMap<>();
        Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap = new HashMap<>();
        List<BusinessSideBaseDto> busDtoList = businessSideService.getBusinessSideBaseDtos();
        Map<Integer, BusinessSideBaseDto> busMap = busDtoList.stream()
                .collect(Collectors.toMap(BusinessSideBaseDto::getId, one -> one));
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andGroupDateGreaterThanOrEqualTo(Utils.getToday())
                .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED.getCode(),
                        CptBookingStatus.BOOKED_SCHEDULED.getCode()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Integer minId = cptSourceDayBookingExtDao.selectMinId(example);
        Integer maxId = cptSourceDayBookingExtDao.selectMaxId(example);

        Integer step = minId;
        while (step <= maxId) {
            log.info("begin to auto release from id {} to {}", step, step + MAX_STEP);
            CptSourceDayBookingPoExample stepExample = new CptSourceDayBookingPoExample();
            stepExample.or()
                    .andGroupDateGreaterThanOrEqualTo(Utils.getToday())
                    .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED.getCode(),
                            CptBookingStatus.BOOKED_SCHEDULED.getCode()))
                    .andIdGreaterThanOrEqualTo(step)
                    .andIdLessThan(step + MAX_STEP)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            stepExample.setOrderByClause("group_date");
            List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(stepExample);
            for (CptSourceDayBookingPo po : pos) {
                BusinessSideBaseDto buDto = busMap.get(po.getBusinessSideId());
                if (buDto == null) {
                    log.warn("not found the business side for {}", buDto);
                    continue;
                }

                if (BusinessSideType.INERNAL.getCode().equals(buDto.getType())) {
                    //对于内部客户
                    this.autoRelease4InnerBusinessSide(po, bookingMap, deletedMap);
                } else if (BusinessSideType.EXTERNAL.getCode().equals(buDto.getType())) {
                    //对于外部客户
                    this.autoRelease4OuterBusinessSide(po, bookingMap, deletedMap);
                }
            }
            step += MAX_STEP;
        }
        this.processNotifications(bookingMap, deletedMap);

        if (willReleaseTopViewSourceList != null) {
            Map<Integer, List<ResourceBookingNotificationDto>> topViewBookingMap = bookingMap.values().stream()
                    .flatMap(Collection::stream)
                    .filter(bookingItem -> Utils.isPositive(bookingItem.getTopViewSourceId()))
                    .collect(Collectors.groupingBy(ResourceBookingNotificationDto::getTopViewSourceId));
            List<TopViewSourceReleaseDto> releaseDtoList = new ArrayList<>(topViewBookingMap.keySet().size());
            for (Map.Entry<Integer, List<ResourceBookingNotificationDto>> bookingEntry : topViewBookingMap.entrySet()) {
                Optional<Timestamp> releaseDate = bookingEntry.getValue().stream()
                        .map(ResourceBookingNotificationDto::getReleaseDate)
                        .filter(Objects::nonNull)
                        .min(Timestamp::compareTo);
                releaseDate.ifPresent(date -> releaseDtoList.add(TopViewSourceReleaseDto.builder()
                        .topViewSourceId(bookingEntry.getKey())
                        .releasingDate(date)
                        .build()));
            }
            willReleaseTopViewSourceList.addAll(releaseDtoList);
        }
        if (releasedTopViewSourceIdList != null) {
            releasedTopViewSourceIdList.addAll(deletedMap.values().stream().flatMap(Collection::stream)
                    .map(ResourceBookingNotificationDto::getTopViewSourceId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList()));
        }
    }

    /**
     * 自动释放的记录不包含预约时间在昨天以前的
     */
    @Override
    public void bookingAutoTodayRelease(List<TopViewSourceReleaseDto> willReleaseTopViewSourceList, List<Integer> releasedTopViewSourceIdList) {
        Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap = new HashMap<>();
        Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap = new HashMap<>();
        List<BusinessSideBaseDto> busDtoList = businessSideService.getBusinessSideBaseDtos();
        Map<Integer, BusinessSideBaseDto> busMap = busDtoList.stream()
                .collect(Collectors.toMap(BusinessSideBaseDto::getId, one -> one));

        CptSourceDayBookingPoExample stepExample = new CptSourceDayBookingPoExample();
        stepExample.or().andGroupDateEqualTo(Utils.getToday())
                .andStatusIn(Arrays.asList(CptBookingStatus.BOOKED.getCode(),
                        CptBookingStatus.BOOKED_SCHEDULED.getCode()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(stepExample);
        for (CptSourceDayBookingPo po : pos) {
            BusinessSideBaseDto buDto = busMap.get(po.getBusinessSideId());
            if (buDto == null) {
                log.warn("not found the business side for {}", buDto);
                continue;
            }

            if (BusinessSideType.INERNAL.getCode().equals(buDto.getType())) {
                //对于内部客户
                this.autoReleaseTodayInnerBusinessSide(po, deletedMap);
            } else if (BusinessSideType.EXTERNAL.getCode().equals(buDto.getType())) {
                //对于外部客户
                this.autoReleaseTodayOuterBusinessSide(po, deletedMap);
            }
        }
        this.processNotifications(bookingMap, deletedMap);

        if (willReleaseTopViewSourceList != null) {
            Map<Integer, List<ResourceBookingNotificationDto>> topViewBookingMap = bookingMap.values().stream()
                    .flatMap(Collection::stream)
                    .filter(bookingItem -> Utils.isPositive(bookingItem.getTopViewSourceId()))
                    .collect(Collectors.groupingBy(ResourceBookingNotificationDto::getTopViewSourceId));
            List<TopViewSourceReleaseDto> releaseDtoList = new ArrayList<>(topViewBookingMap.keySet().size());
            for (Map.Entry<Integer, List<ResourceBookingNotificationDto>> bookingEntry : topViewBookingMap.entrySet()) {
                Optional<Timestamp> releaseDate = bookingEntry.getValue().stream()
                        .map(ResourceBookingNotificationDto::getReleaseDate)
                        .filter(Objects::nonNull)
                        .min(Timestamp::compareTo);
                releaseDate.ifPresent(date -> releaseDtoList.add(TopViewSourceReleaseDto.builder()
                        .topViewSourceId(bookingEntry.getKey())
                        .releasingDate(date)
                        .build()));
            }
            willReleaseTopViewSourceList.addAll(releaseDtoList);
        }
        if (releasedTopViewSourceIdList != null) {
            releasedTopViewSourceIdList.addAll(deletedMap.values().stream().flatMap(Collection::stream)
                    .map(ResourceBookingNotificationDto::getTopViewSourceId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public void clearAllBookingResource() {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIdGreaterThan(0);
        cptSourceDayBookingDao.deleteByExample(example);
    }

    @Override
    public void clearBookingDatasNotMappingWithSchedule() {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or()
                .andStatusEqualTo(CptBookingStatus.BOOKED_SCHEDULED.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Integer minId = cptSourceDayBookingExtDao.selectMinId(example);
        Integer maxId = cptSourceDayBookingExtDao.selectMaxId(example);

        Integer step = minId;
        while (step <= maxId) {
            CptSourceDayBookingPoExample stepExample = new CptSourceDayBookingPoExample();
            stepExample.or()
                    .andStatusEqualTo(CptBookingStatus.BOOKED_SCHEDULED.getCode())
                    .andIdGreaterThanOrEqualTo(step)
                    .andIdLessThan(step + MAX_STEP)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CptSourceDayBookingPo> pos = cptSourceDayBookingDao.selectByExample(stepExample);
            for (CptSourceDayBookingPo po : pos) {
                this.validateAndRemoveBookings(po);
            }
            step += MAX_STEP;
        }
    }

    @Override
    public List<ResourceBookingNotificationDto> getReleasingBookings(List<BookingItemDto> bookingItemDtos, Operator operator) {
        log.warn("ResourceBookingService.getReleasingBookings:bookingItemDtos.size={}", CollectionHelper.getSize(bookingItemDtos));
        if (operator != null) {
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(bookingItemDtos)) {
            return Collections.emptyList();
        }
        List<BookingItemDto> myBookingItemDtos = bookingItemDtos.stream()
                .filter(bookingItemDto -> Objects.equals(bookingItemDto.getAccountId(), operator.getOperatorId()))
                .filter(bookingItemDto -> bookingItemDto.getMtime().before(Utils.getSomeDayAfter(Utils.getToday(), 3)))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(myBookingItemDtos)) {
            return Collections.emptyList();
        }

        List<Integer> businessSideIds = myBookingItemDtos.stream().map(BookingItemDto::getBusinessSideId)
                .distinct().collect(Collectors.toList());

        Map<Integer, BusinessSideBaseDto> businessSideMap = businessSideService.getBusinessSideMapInIds(businessSideIds);
        log.warn("ResourceBookingService.getReleasingBookings:businessSideMap.size={}", CollectionHelper.getSize(businessSideMap));

        List<ResourceBookingNotificationDto> bookingNotificationDtos = new ArrayList<>();

        for (BookingItemDto bookingItemDto : myBookingItemDtos) {
            BusinessSideBaseDto buDto = businessSideMap.get(bookingItemDto.getBusinessSideId());
            if (buDto == null) {
                log.warn("getReleasingBookings no business side for {}", bookingItemDto.getBusinessSideId());
                continue;
            }

            if (BusinessSideType.INERNAL.getCode().equals(buDto.getType())) {
                //对于内部客户
                List<ResourceBookingNotificationDto> notificationDtos = getReleasing4InnerBusinessSide(bookingItemDto);
                bookingNotificationDtos.addAll(notificationDtos);
            } else if (BusinessSideType.EXTERNAL.getCode().equals(buDto.getType())) {
                //对于外部客户
                List<ResourceBookingNotificationDto> notificationDtos = getReleasing4OuterBusinessSide(bookingItemDto);
                bookingNotificationDtos.addAll(notificationDtos);
            }
        }

        return bookingNotificationDtos;
    }

    private List<ResourceBookingNotificationDto> getReleasing4InnerBusinessSide(BookingItemDto bookingItemDto) {
        switch (CptBookingStatus.getByCode(bookingItemDto.getStatus())) {
            case BOOKED:
                return getReleasing4StatusBooked(bookingItemDto);
            case BOOKED_SCHEDULED:
                if (bookingItemDto.getCptScheduleId() != null && bookingItemDto.getCptScheduleId() > 0) {
                    List<CptCreativeDto> creativeDtoList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                            .scheduleId(bookingItemDto.getCptScheduleId())
                            .cptCreativeStatus(CptCreativeStatus.AUDIT_PASS.getCode())
                            .build());
                    if (CollectionUtils.isEmpty(creativeDtoList)) {
                        return getReleasing4BookScheduled(bookingItemDto);
                    }
                }
                break;
        }
        return Collections.emptyList();
    }

    private List<ResourceBookingNotificationDto> getReleasing4OuterBusinessSide(BookingItemDto bookingItemDto) {
        switch (CptBookingStatus.getByCode(bookingItemDto.getStatus())) {
            case BOOKED:
                return getReleasing4StatusBooked(bookingItemDto);
            case BOOKED_SCHEDULED:
                if (bookingItemDto.getCptOrderId() != null && bookingItemDto.getCptOrderId() > 0) {
                    CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(bookingItemDto.getCptOrderId());
                    if (!this.validateCrmOrderHasAudited(cptOrderDto.getCrmOrderId())) {
                        return getReleasing4BookScheduled(bookingItemDto);
                    }
                }
                break;
        }
        return Collections.emptyList();
    }

    private List<ResourceBookingNotificationDto> getReleasing4BookScheduled(BookingItemDto bookingItemDto) {
        //对于一种情况的特殊处理
        //先预约6-10号，再预约1-5号，然后排期1-10号
        List<CptSourceDayBookingPo> matchedItems = new ArrayList<>();
        if (bookingItemDto.getCptScheduleId() != null && bookingItemDto.getCptScheduleId() > 0) {
            matchedItems = queryResourceBookingPo(bookingItemDto.getCptScheduleId(), bookingItemDto.getGroupDate());
        }

        List<ResourceBookingNotificationDto> bookingNotificationDtos = new ArrayList<>();
        if (Utils.getDateSpace(bookingItemDto.getCtime(), bookingItemDto.getGroupDate()) > booking_resource_range + 1) {
            //预约日期与资源日期相差 > 30个自然日
            if (Utils.getDateSpace(bookingItemDto.getCtime(), Utils.getToday()) < booking_resource_range + 1) {
                if (Utils.getDateSpace(bookingItemDto.getCtime(), Utils.getToday()) >= booking_resource_range + 1 - email_notify_range) {
                    //即将释放
                    Timestamp releaseDate = Utils.getSomeDayAfter(bookingItemDto.getCtime(), booking_resource_range);
                    ResourceBookingNotificationDto notificationDto = buildBookingNotificationDto(bookingItemDto, releaseDate);
                    bookingNotificationDtos.add(notificationDto);

                    List<ResourceBookingNotificationDto> notificationDtos = matchedItems.stream()
                            .map(bookingPo -> buildBookingNotificationDto(bookingPo, releaseDate)).collect(Collectors.toList());
                    bookingNotificationDtos.addAll(notificationDtos);
                }
            }
        } else if (Utils.getDateSpace(bookingItemDto.getCtime(), bookingItemDto.getGroupDate()) < 1) {
            //预约日期与资源日期 < 0
            //历史数据，暂时就不处理了
            return bookingNotificationDtos;
        } else {
            //预约日期与资源日期相差 <= 30个自然日 且 > 0
            if (!bookingItemDto.getGroupDate().before(Utils.getToday())
                    && !bookingItemDto.getGroupDate().equals(Utils.getToday())) {
                if (Utils.getDateSpace(Utils.getToday(), bookingItemDto.getGroupDate()) <= email_notify_range + 1) {
                    //即将释放
                    Timestamp releaseDate = Utils.getBeginOfDay(bookingItemDto.getGroupDate());
                    ResourceBookingNotificationDto notificationDto = buildBookingNotificationDto(bookingItemDto, releaseDate);
                    bookingNotificationDtos.add(notificationDto);

                    List<ResourceBookingNotificationDto> notificationDtos = matchedItems.stream()
                            .map(bookingPo -> buildBookingNotificationDto(bookingPo, releaseDate)).collect(Collectors.toList());
                    bookingNotificationDtos.addAll(notificationDtos);
                }
            }
        }
        return bookingNotificationDtos;
    }

    private List<ResourceBookingNotificationDto> getReleasing4StatusBooked(BookingItemDto bookingItemDto) {
        if (Utils.getDateSpace(bookingItemDto.getCtime(), bookingItemDto.getGroupDate()) > booking_resource_range + 1) {
            if (Utils.getDateSpace(bookingItemDto.getCtime(), Utils.getToday()) < booking_resource_range + 1) {
                if (Utils.getDateSpace(bookingItemDto.getCtime(), Utils.getToday()) >= booking_resource_range + 1 - email_notify_range) {
                    //即将释放
                    Timestamp releaseDate = Utils.getSomeDayAfter(bookingItemDto.getCtime(), booking_resource_range);
                    return Collections.singletonList(buildBookingNotificationDto(bookingItemDto, releaseDate));
                }
            }
        } else if (Utils.getDateSpace(bookingItemDto.getCtime(), bookingItemDto.getGroupDate()) < 1) {
            //预约日期与资源日期 < 0
            //历史数据，暂时就不处理了
            return Collections.emptyList();
        } else {
            if (!bookingItemDto.getGroupDate().before(Utils.getToday())
                    && !bookingItemDto.getGroupDate().equals(Utils.getToday())) {
                if (Utils.getDateSpace(Utils.getToday(), bookingItemDto.getGroupDate()) <= email_notify_range + 1) {
                    //即将释放
                    Timestamp releaseDate = Utils.getBeginOfDay(bookingItemDto.getGroupDate());
                    return Collections.singletonList(buildBookingNotificationDto(bookingItemDto, releaseDate));
                }
            }
        }
        return Collections.emptyList();
    }

    private ResourceBookingNotificationDto buildBookingNotificationDto(BookingItemDto bookingItemDto, Timestamp releaseDate) {
        return ResourceBookingNotificationDto.builder()
                .sourceDayBookId(bookingItemDto.getId())
                .businessSideId(bookingItemDto.getBusinessSideId())
                .orderId(bookingItemDto.getCptOrderId())
                .scheduleId(bookingItemDto.getCptScheduleId())
                .sourceId(bookingItemDto.getSourceId())
                .sourceName(bookingItemDto.getSourceName())
                .groupDate(bookingItemDto.getGroupDate())
                .releaseDate(Utils.getBeginOfDay(releaseDate))
                .sourceBookingTime(bookingItemDto.getCtime())
                .operator(bookingItemDto.getOperator())
                .topViewSourceId(bookingItemDto.getTopViewSourceId())
                .build();
    }

    private void validateAndRemoveBookings(CptSourceDayBookingPo po) {
        if (po == null || po.getCptScheduleId() == null || po.getCptScheduleId() <= 0) {
            return;
        }
        GdSchedulePo gdSchedulePo = cptScheduleDelegate.getGdScheduleById(po.getCptScheduleId());

        if (gdSchedulePo == null) {
            log.info("delete not mapping data in booking with id {}", po.getId());
            log.debug("delete not mapping data in booking {}", po);
            deleteResourceBookingByIdWithoutValidate(po.getId(), Operator.SYSTEM);
        } else if (po.getGroupDate().before(gdSchedulePo.getBeginDate())
                || po.getGroupDate().after(gdSchedulePo.getEndDate())

        ) {
            log.info("begin to delete po {}", po);
            log.debug("gdSchedulePo {}", po);
            deleteResourceBookingByIdWithoutValidate(po.getId(), Operator.SYSTEM);
        }
    }

    /**
     * 对于内部客户
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoRelease4InnerBusinessSide(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        switch (CptBookingStatus.getByCode(po.getStatus())) {
            case BOOKED:
                this.autoRelease4StatusBooked(po, bookingMap, deletedMap);
                break;
            case BOOKED_SCHEDULED:
                if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0) {
                    List<CptCreativeDto> creativeDtoList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                            .scheduleId(po.getCptScheduleId())
                            .cptCreativeStatus(CptCreativeStatus.AUDIT_PASS.getCode())
                            .build());
                    if (CollectionUtils.isEmpty(creativeDtoList)) {
                        this.autoRelease(po, bookingMap, deletedMap);
                    }
                }
                break;
        }
    }

    /**
     * 对于内部客户
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoReleaseTodayInnerBusinessSide(CptSourceDayBookingPo po,
                                                  Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        switch (CptBookingStatus.getByCode(po.getStatus())) {
            case BOOKED:
                this.autoReleaseTodayStatusBooked(po, deletedMap);
                break;
            case BOOKED_SCHEDULED:
                if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0) {
                    List<CptCreativeDto> creativeDtoList = queryCptCreativeService.getCptCreativeBaseDtos(QueryCreativeParamDto.builder()
                            .scheduleId(po.getCptScheduleId())
                            .cptCreativeStatus(CptCreativeStatus.AUDIT_PASS.getCode())
                            .build());
                    if (CollectionUtils.isEmpty(creativeDtoList)) {
                        this.autoReleaseToday(po, deletedMap);
                    }
                }
                break;
        }
    }

    /**
     * 对于外部客户
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoRelease4OuterBusinessSide(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        // 外部账户焦点图释放规则
        if (outerBusinessSideFocusReleaseStartTime != null
                && System.currentTimeMillis() >= outerBusinessSideFocusReleaseStartTime
                && !CollectionUtils.isEmpty(outerBusinessSideAutoReleaseSourceIdList)
                && outerBusinessSideAutoReleaseSourceIdList.contains(po.getSourceId())) {
            autoRelease4OuterBusinessSideAndFocusPicture(po, bookingMap, deletedMap);
            return;
        }

        switch (CptBookingStatus.getByCode(po.getStatus())) {
            case BOOKED:
                this.autoRelease4StatusBooked(po, bookingMap, deletedMap);
                break;
            case BOOKED_SCHEDULED:
                if (po.getCptOrderId() != null && po.getCptOrderId() > 0) {
                    CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(po.getCptOrderId());
                    if (!this.validateCrmOrderHasAudited(cptOrderDto.getCrmOrderId())) {
                        this.autoRelease(po, bookingMap, deletedMap);
                    }
                }
                break;
        }
    }

    /**
     * 对于外部客户释放当日
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoReleaseTodayOuterBusinessSide(CptSourceDayBookingPo po, Map<Integer,
            Set<ResourceBookingNotificationDto>> deletedMap) {
        // 外部账户焦点图释放规则
        if (outerBusinessSideFocusReleaseStartTime != null
                && System.currentTimeMillis() >= outerBusinessSideFocusReleaseStartTime
                && !CollectionUtils.isEmpty(outerBusinessSideAutoReleaseSourceIdList)
                && outerBusinessSideAutoReleaseSourceIdList.contains(po.getSourceId())) {
            autoReleaseTodayOuterBusinessSideAndFocusPicture(po,deletedMap);
            return;
        }

        switch (CptBookingStatus.getByCode(po.getStatus())) {
            case BOOKED:
                this.autoReleaseTodayStatusBooked(po, deletedMap);
                break;
            case BOOKED_SCHEDULED:
                if (po.getCptOrderId() != null && po.getCptOrderId() > 0) {
                    CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(po.getCptOrderId());
                    if (!this.validateCrmOrderHasAudited(cptOrderDto.getCrmOrderId())) {
                        this.autoReleaseToday(po, deletedMap);
                    }
                }
                break;
        }
    }

    private void autoRelease4OuterBusinessSideAndFocusPicture(CptSourceDayBookingPo po,
                                                              Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap,
                                                              Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        // 预定状态 已预约/预约已排期
        if (!CptBookingStatus.BOOKED.getCode().equals(po.getStatus())
                && !CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(po.getStatus())) {
            return;
        }
        // 已排期 订单被审核次数=0
        List<CptSourceDayBookingPo> scheduleBeforeItemList = Collections.emptyList();
        if (CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(po.getStatus())) {
            if (po.getCptOrderId() == null
                    || po.getCptOrderId() <= 0) {
                return;
            }
            CptOrderBaseDto orderBaseDto = cptOrderService.getOrderByGdOrderId(po.getCptOrderId());
            if (validateCrmOrderHasAudited(orderBaseDto.getCrmOrderId())) {
                return;
            }
            // 同一排期下当前日期前的预定需要删除
            if (po.getCptScheduleId() != null
                    && po.getCptScheduleId() > 0
                    && !Utils.isPositive(po.getTopViewSourceId())) {
                scheduleBeforeItemList = queryResourceBookingPo(po.getCptScheduleId(), po.getGroupDate());
            }
        }
        // 投放日期判断 t-7/t-3/t-1/t
        int gap = Utils.getDateSpace(Utils.getToday(), po.getGroupDate()) - 1;
        if (!OUTER_BUSINESS_SIDE_AUTO_RELEASE_DATE_LIST.contains(gap)) {
            // 即将释放告警
            Integer releaseGap = OUTER_BUSINESS_SIDE_RELEASE_ALERT_DATE_MAP.get(gap);
            if (releaseGap != null) {
                Timestamp releaseDate = Utils.getSomeDayAfterToday(releaseGap);
                addIntoBookingMap(releaseDate, po, bookingMap, scheduleBeforeItemList);
            }
            return;
        }
        log.info("autoRelease4OuterBusinessSideAndFocusPicture bookingId {}", po.getId());
        // 释放资源
        autoReleaseDelete(po, deletedMap, scheduleBeforeItemList);
    }

    private void autoReleaseTodayOuterBusinessSideAndFocusPicture(CptSourceDayBookingPo po,
                                                              Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        // 预定状态 已预约/预约已排期
        if (!CptBookingStatus.BOOKED.getCode().equals(po.getStatus())
                && !CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(po.getStatus())) {
            return;
        }
        // 已排期 订单被审核次数=0
        List<CptSourceDayBookingPo> scheduleBeforeItemList = Collections.emptyList();
        if (CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(po.getStatus())) {
            if (po.getCptOrderId() == null
                    || po.getCptOrderId() <= 0) {
                return;
            }
            CptOrderBaseDto orderBaseDto = cptOrderService.getOrderByGdOrderId(po.getCptOrderId());
            if (validateCrmOrderHasAudited(orderBaseDto.getCrmOrderId())) {
                return;
            }
            // 同一排期下当前日期前的预定需要删除
            if (po.getCptScheduleId() != null
                    && po.getCptScheduleId() > 0
                    && !Utils.isPositive(po.getTopViewSourceId())) {
                scheduleBeforeItemList = queryResourceBookingPo(po.getCptScheduleId(), po.getGroupDate());
            }
        }
        // 投放日期判断 t
        int gap = Utils.getDateSpace(Utils.getToday(), po.getGroupDate()) - 1;
        if (gap == 0) {
            log.info("autoReleaseTodayOuterBusinessSideAndFocusPicture bookingId {}", po.getId());
            // 释放资源
            autoReleaseDelete(po, deletedMap, scheduleBeforeItemList);
        }

    }

    /**
     * 对于已预约状态的自动释放
     *
     * @param po
     */
    public void autoRelease4StatusBooked(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        if (Utils.getDateSpace(po.getCtime(), po.getGroupDate()) > booking_resource_range + 1) {
            //预约日期与资源日期相差 > 22个自然日
            if (Utils.getDateSpace(po.getCtime(), Utils.getToday()) >= booking_resource_range + 1) {
                //系统自动资源释放
                this.autoReleaseDelete(po, deletedMap);
            } else if (Utils.getDateSpace(po.getCtime(), Utils.getToday()) >= booking_resource_range + 1 - email_notify_range) {
                //触发邮件
                Timestamp releaseDate = Utils.getSomeDayAfter(po.getCtime(), booking_resource_range);
                this.addIntoBookingMap(releaseDate, po, bookingMap);
            }
        } else if (Utils.getDateSpace(po.getCtime(), po.getGroupDate()) < 1) {
            //预约日期与资源日期 < 0
            //历史数据，暂时就不处理了
        } else {
            //预约日期与资源日期相差 <= 22个自然日 且 > 0
            if (po.getGroupDate().before(Utils.getToday())
                    || po.getGroupDate().equals(Utils.getToday())) {
                //系统自动资源释放
                this.autoReleaseDelete(po, deletedMap);
            } else if (Utils.getDateSpace(Utils.getToday(), po.getGroupDate()) <= email_notify_range + 1) {
                //触发邮件
                Timestamp releaseDate = Utils.getBeginOfDay(po.getGroupDate());
                this.addIntoBookingMap(releaseDate, po, bookingMap);
            }
        }
    }

    /**
     * 对于已预约状态的自动释放
     *
     * @param po
     */
    public void autoReleaseTodayStatusBooked(CptSourceDayBookingPo po,
                                             Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        if (po.getGroupDate().equals(Utils.getToday())) {
            //系统自动资源释放
            this.autoReleaseDelete(po, deletedMap);
        }
    }

    /**
     * 验证该CRM订单有被审核通过过
     *
     * @return true 表示有审核通过的状态
     * false 表示没有审核通过的状态
     */
    private boolean validateCrmOrderHasAudited(Integer crmOrderId) {
        if (crmOrderId == null) {
            return false;
        }
        CrmOrderStatusRecordPoExample example = new CrmOrderStatusRecordPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCrmOrderIdEqualTo(crmOrderId)
                .andStatusEqualTo(CrmOrderStatus.WAIT.getCode());
        long count = crmOrderStatusRecordDao.countByExample(example);
        return count > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoReleaseDelete(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        RLock bookingLock = super.getLock(po.getSourceId(), CptConstants.BOOKING_LOCK_SUFFIX);
        try {
            // topView资源这边只mark不做操作
            if (!Utils.isPositive(po.getTopViewSourceId())) {
                if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0) {
                    cptScheduleService.deleteCptScheduleDayFromBooking(po.getCptScheduleId(), po.getGroupDate());
                }
                this.deleteResourceBookingByIdWithoutValidate(po.getId(), Operator.SYSTEM);
            }
            this.addIntoDeletedMap(po, deletedMap);
        } finally {
            if (bookingLock != null) {
                bookingLock.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void autoReleaseDelete(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap, List<CptSourceDayBookingPo> matchedOtherItems) {
        RLock bookingLock = super.getLock(po.getSourceId(), CptConstants.BOOKING_LOCK_SUFFIX);
        try {
            // topView资源这边只mark不做操作
            if (!Utils.isPositive(po.getTopViewSourceId())) {
                if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0) {
                    cptScheduleService.deleteCptScheduleDayFromBooking(po.getCptScheduleId(), po.getGroupDate());
                }
                this.deleteResourceBookingByIdWithoutValidate(po.getId(), Operator.SYSTEM);
                matchedOtherItems.stream()
                        .forEach(item -> this.deleteResourceBookingByIdWithoutValidate(item.getId(), Operator.SYSTEM));
            }
            this.addIntoDeletedMap(po, deletedMap, matchedOtherItems);
        } finally {
            if (bookingLock != null) {
                bookingLock.unlock();
            }
        }
    }

    /**
     * 把需要邮件提醒的（即将删除的）记录下来
     *
     * @param po
     * @param bookingMap
     */
    private void addIntoBookingMap(Timestamp releaseDate, CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap) {
        Set<ResourceBookingNotificationDto> bookingPoSet = bookingMap.getOrDefault(po.getBusinessSideId(), new HashSet<>());
        bookingPoSet.add(buildBookingNotificationDto(po, releaseDate));
        bookingMap.put(po.getBusinessSideId(), bookingPoSet);
    }

    private void addIntoBookingMap(Timestamp releaseDate, CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, List<CptSourceDayBookingPo> matchedOtherItems) {
        this.addIntoBookingMap(releaseDate, po, bookingMap);
        matchedOtherItems.stream().forEach(item -> {
            Set<ResourceBookingNotificationDto> bookingPoSet = bookingMap.getOrDefault(item.getBusinessSideId(), new HashSet<>());
            bookingPoSet.add(buildBookingNotificationDto(item, releaseDate));
            bookingMap.put(item.getBusinessSideId(), bookingPoSet);
        });
    }

    private ResourceBookingNotificationDto buildBookingNotificationDto(CptSourceDayBookingPo item, Timestamp releaseDate) {
        return ResourceBookingNotificationDto.builder()
                .sourceDayBookId(item.getId())
                .businessSideId(item.getBusinessSideId())
                .orderId(item.getCptOrderId())
                .scheduleId(item.getCptScheduleId())
                .sourceId(item.getSourceId())
                .sourceName(item.getSourceName())
                .groupDate(item.getGroupDate())
                .releaseDate(Utils.getBeginOfDay(releaseDate))
                .sourceBookingTime(item.getCtime())
                .operator(item.getOperator())
                .topViewSourceId(item.getTopViewSourceId())
                .build();
    }

    /**
     * 把删除的记录下
     *
     * @param po
     * @param deletedMap
     */
    private void addIntoDeletedMap(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        Set<ResourceBookingNotificationDto> set = deletedMap.getOrDefault(po.getBusinessSideId(), new HashSet<>());
        set.add(ResourceBookingNotificationDto.builder()
                .businessSideId(po.getBusinessSideId())
                .orderId(po.getCptOrderId())
                .scheduleId(po.getCptScheduleId())
                .sourceId(po.getSourceId())
                .sourceName(po.getSourceName())
                .groupDate(po.getGroupDate())
                .releaseDate(Utils.getToday())
                .sourceBookingTime(po.getCtime())
                .sourceBookingTime(po.getCtime())
                .operator(po.getOperator())
                .topViewSourceId(po.getTopViewSourceId())
                .build());
        deletedMap.put(po.getBusinessSideId(), set);
    }

    private void addIntoDeletedMap(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap, List<CptSourceDayBookingPo> matchedOtherItems) {
        this.addIntoDeletedMap(po, deletedMap);
        matchedOtherItems.stream().forEach(item -> {
            Set<ResourceBookingNotificationDto> set = deletedMap.getOrDefault(item.getBusinessSideId(), new HashSet<>());
            set.add(ResourceBookingNotificationDto.builder()
                    .businessSideId(item.getBusinessSideId())
                    .orderId(item.getCptOrderId())
                    .scheduleId(item.getCptScheduleId())
                    .sourceId(item.getSourceId())
                    .sourceName(item.getSourceName())
                    .groupDate(item.getGroupDate())
                    .releaseDate(Utils.getToday())
                    .sourceBookingTime(item.getCtime())
                    .operator(item.getOperator())
                    .topViewSourceId(item.getTopViewSourceId())
                    .build());
            deletedMap.put(po.getBusinessSideId(), set);
        });
    }

    /**
     * 自动释放
     *
     * @param po
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoRelease(CptSourceDayBookingPo po, Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        //对于一种情况的特殊处理
        //先预约6-10号，再预约1-5号，然后排期1-10号
        List<CptSourceDayBookingPo> matchedItems = new ArrayList<>();
        if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0
                && !Utils.isPositive(po.getTopViewSourceId())) {
            matchedItems = queryResourceBookingPo(po.getCptScheduleId(), po.getGroupDate());
        }

        if (Utils.getDateSpace(po.getCtime(), po.getGroupDate()) > booking_resource_range + 1) {
            //预约日期与资源日期相差 > 30个自然日
            if (Utils.getDateSpace(po.getCtime(), Utils.getToday()) >= booking_resource_range + 1) {
                //系统自动资源释放
                this.autoReleaseDelete(po, deletedMap, matchedItems);
            } else if (Utils.getDateSpace(po.getCtime(), Utils.getToday()) >= booking_resource_range + 1 - email_notify_range) {
                //触发邮件
                Timestamp releaseDate = Utils.getSomeDayAfter(po.getCtime(), booking_resource_range);
                this.addIntoBookingMap(releaseDate, po, bookingMap, matchedItems);
            }
        } else if (Utils.getDateSpace(po.getCtime(), po.getGroupDate()) < 1) {
            //预约日期与资源日期 < 0
            //历史数据，暂时就不处理了
            return;
        } else {
            //预约日期与资源日期相差 <= 30个自然日 且 > 0
            if (po.getGroupDate().before(Utils.getToday())
                    || po.getGroupDate().equals(Utils.getToday())) {
                //系统自动资源释放
                this.autoReleaseDelete(po, deletedMap, matchedItems);
            } else if (Utils.getDateSpace(Utils.getToday(), po.getGroupDate()) <= email_notify_range + 1) {
                //触发邮件
                Timestamp releaseDate = Utils.getBeginOfDay(po.getGroupDate());
                this.addIntoBookingMap(releaseDate, po, bookingMap, matchedItems);
            }
        }
    }

    /**
     * 自动释放
     *
     * @param po
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoReleaseToday(CptSourceDayBookingPo po,Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        //对于一种情况的特殊处理
        //先预约6-10号，再预约1-5号，然后排期1-10号
        List<CptSourceDayBookingPo> matchedItems = new ArrayList<>();
        if (po.getCptScheduleId() != null && po.getCptScheduleId() > 0
                && !Utils.isPositive(po.getTopViewSourceId())) {
            matchedItems = queryResourceBookingPo(po.getCptScheduleId(), po.getGroupDate());
        }
        //预约日期与资源日期相差 <= 30个自然日 且 > 0
        if (po.getGroupDate().equals(Utils.getToday())) {
            //系统自动资源释放
            this.autoReleaseDelete(po, deletedMap, matchedItems);
        }
    }

    /**
     * 异常情况处理
     * 在bookingSet中存在的，且也在deletedSet中存在的条目，
     * 则删除bookingSet中的条目
     *
     * @param bookingMap
     * @param deletedMap
     */
    private void removeDuplicateBookingSet(Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        for (Map.Entry<Integer, Set<ResourceBookingNotificationDto>> entry : bookingMap.entrySet()) {
            Integer businessSideId = entry.getKey();
            Set<ResourceBookingNotificationDto> bookingSet = entry.getValue();
            Set<ResourceBookingNotificationDto> deletedSet = deletedMap.getOrDefault(businessSideId, new HashSet<>());
            deletedSet.forEach(deleted -> {
                if (bookingSet.contains(deleted)) {
                    bookingSet.remove(deleted);
                    log.info("remove duplicate item {}", deleted);
                }
            });
        }
    }

    /**
     * 处理邮件告警
     *
     * @param bookingMap
     */
    private void processNotifications(Map<Integer, Set<ResourceBookingNotificationDto>> bookingMap, Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap) {
        if (CollectionUtils.isEmpty(bookingMap) && CollectionUtils.isEmpty(deletedMap)) {
            return;
        }
        Set<Integer> busIdSet = new HashSet<>();
        Set<Integer> cptOrderIdSet = new HashSet<>();
        bookingMap.values().stream()
                .flatMap(Collection::stream)
                .forEach(s -> {
                    When.notNull(s.getBusinessSideId(), value -> busIdSet.add(s.getBusinessSideId()));
                    When.notNull(s.getOrderId(), value -> cptOrderIdSet.add(s.getOrderId()));
                });
        deletedMap.values().stream()
                .flatMap(Collection::stream)
                .forEach(s -> {
                    When.notNull(s.getBusinessSideId(), value -> busIdSet.add(s.getBusinessSideId()));
                    When.notNull(s.getOrderId(), value -> cptOrderIdSet.add(s.getOrderId()));
                });
        List<Integer> busIds = new ArrayList<>(busIdSet);
        Map<Integer, BusinessSideBaseDto> busBaseDtoMap = businessSideService.getBusinessSideMapInIds(busIds);
        Map<Integer, List<CptUserDto>> busUserMap = cptUserService.getBusinessSideUserMapInBusinessSideIds(busIds);
        Map<Integer, String> orderNameMap = cptOrderService.getOrderNameMapInOrderIdSet(cptOrderIdSet);

        removeDuplicateBookingSet(bookingMap, deletedMap);
        for (Map.Entry<Integer, Set<ResourceBookingNotificationDto>> entry : bookingMap.entrySet()) {
            Integer businessSideId = entry.getKey();
            Set<ResourceBookingNotificationDto> dtos = entry.getValue();
            this.buildNotificationDtos(entry, busBaseDtoMap, orderNameMap);
            List<CptUserDto> userDtos = busUserMap.get(businessSideId);
            if (CollectionUtils.isEmpty(userDtos)) {
                log.info("failed to find users by business side id {}", businessSideId);
                continue;
            }
            List<String> users = userDtos.stream()
                    .map(CptUserDto::getName)
                    .collect(Collectors.toList());
            List<ResourceBookingNotificationDto> list = dtos.stream()
                    .filter(dto -> !Utils.isPositive(dto.getTopViewSourceId()))
                    .sorted(Comparator.comparing(ResourceBookingNotificationDto::getGroupDate))
                    .collect(Collectors.toList());
            this.sendNotificationEmail(list, users);
        }
        for (Map.Entry<Integer, Set<ResourceBookingNotificationDto>> entry : deletedMap.entrySet()) {
            Integer businessSideId = entry.getKey();
            Set<ResourceBookingNotificationDto> dtos = entry.getValue();
            this.buildNotificationDtos(entry, busBaseDtoMap, orderNameMap);
            List<CptUserDto> userDtos = busUserMap.get(businessSideId);
            if (CollectionUtils.isEmpty(userDtos)) {
                log.info("failed to find users by business side id {}", businessSideId);
                continue;
            }
            List<String> users = userDtos.stream()
                    .map(CptUserDto::getName)
                    .collect(Collectors.toList());
            List<ResourceBookingNotificationDto> list = dtos.stream()
                    .filter(dto -> !Utils.isPositive(dto.getTopViewSourceId()))
                    .sorted(Comparator.comparing(ResourceBookingNotificationDto::getGroupDate))
                    .collect(Collectors.toList());
            this.sendDeletedNotificationEmail(list, users);
        }
    }

    private void buildNotificationDtos(Map.Entry<Integer, Set<ResourceBookingNotificationDto>> entry, Map<Integer, BusinessSideBaseDto> busBaseDtoMap, Map<Integer, String> orderNameMap) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss E", localeCN);
        entry.getValue().stream()
                .forEach(dto -> {
                    dto.setBusinessSideName(busBaseDtoMap.getOrDefault(entry.getKey(), emptyBuSideDto).getName());
                    dto.setOrderName(orderNameMap.getOrDefault(dto.getOrderId(), ""));
                    dto.setGroupDateStr(sdf.format(dto.getGroupDate()));
                    dto.setReleaseDateStr(sdf.format(dto.getReleaseDate()));
                    dto.setSourceBookingTimeStr(sdf.format(dto.getSourceBookingTime()));
                });
    }

    protected String emailContent(List<ResourceBookingNotificationDto> dtos) throws IllegalAccessException {
        String maxDateStr = dtos.stream()
                .map(ResourceBookingNotificationDto::getGroupDateStr)
                .max(Comparator.naturalOrder())
                .orElse("");
        String minDateStr = dtos.stream()
                .map(ResourceBookingNotificationDto::getGroupDateStr)
                .min(String::compareTo)
                .orElse("");

        StringBuilder sb = new StringBuilder();
        sb.append("<br/>");
        sb.append("<p><b>亲爱的用户，系统检测到您的账户下有创意或订单还未过审，将影响您的广告正常投放，请及时查看！</b></p>");
        sb.append("<p><b>本次所释放的预约为: " + minDateStr + "到" + maxDateStr + "</b></p>");
        String text = MailUtils.genHtmlTableString(dtos.stream()
                .collect(Collectors.toList()));
        sb.append(text);
        return sb.toString();
    }

    protected String emailDeletedContent(List<ResourceBookingNotificationDto> dtos) throws IllegalAccessException {
        String maxDateStr = dtos.stream()
                .map(ResourceBookingNotificationDto::getGroupDateStr)
                .max(Comparator.naturalOrder())
                .orElse("");
        String minDateStr = dtos.stream()
                .map(ResourceBookingNotificationDto::getGroupDateStr)
                .min(String::compareTo)
                .orElse("");

        StringBuilder sb = new StringBuilder();
        sb.append("<br/>");
        sb.append("<p><b>您的下列资源已被释放，如有需要可重新预订</b></p>");
        sb.append("<p><b>本次所释放的预约为: " + minDateStr + "到" + maxDateStr + "</b></p>");
        String text = MailUtils.genHtmlTableString(dtos.stream()
                .collect(Collectors.toList()));
        sb.append(text);
        return sb.toString();
    }

    /**
     * 发送即将释放资源邮件提醒
     *
     * @param dtos
     * @param users
     */
    private void sendNotificationEmail(List<ResourceBookingNotificationDto> dtos, List<String> users) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        users.stream()
                .map(user -> user + "@bilibili.com")
                .forEach(bilibiliUser -> {
                    MailMessage mail = new MailMessage();
                    try {
                        mail.setTos(Lists.newArrayList(bilibiliUser));
                        mail.setHasFile(false);
                        mail.setSubject("【预警】CPT-预约资源释放预警");
                        mail.setUseHtml(true);
                        mail.setText(this.emailContent(dtos));
                        mailService.send(mail);
                    } catch (Exception e) {
                        log.error("failed to send mail {} for exception ", mail, e);
                    }
                });
    }

    private void sendDeletedNotificationEmail(List<ResourceBookingNotificationDto> dtos, List<String> users) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        if (CollectionUtils.isEmpty(users)) {
            return;
        }
        users.stream()
                .map(user -> user + "@bilibili.com")
                .forEach(bilibiliUser -> {
                    MailMessage mail = new MailMessage();
                    try {
                        mail.setTos(Lists.newArrayList(bilibiliUser));
                        mail.setHasFile(false);
                        mail.setSubject("【释放完成】CPT-已释放资源位告知");
                        mail.setUseHtml(true);
                        mail.setText(this.emailDeletedContent(dtos));
                        mailService.send(mail);
                    } catch (Exception e) {
                        log.error("failed to send mail {} for exception ", mail, e);
                    }
                });
    }

    private BookingItemResultDto convertSingleDto2ResultDto(SingleResourceBookingDto dto, Integer success) {
        return BookingItemResultDto.builder()
                .success(success)
                .accountId(dto.getAccountId())
                .platformId(dto.getPlatformId())
                .platformName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getPlatformName())
                .pageId(dto.getPageId())
                .pageName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getPageName())
                .resourceId(dto.getResourceId())
                .resourceName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getResourceName())
                .sourceId(dto.getSourceId())
                .sourceName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getName())
                .groupDate(dto.getGroupDate())
                .build();
    }

    private CptSourceDayBookingPo convertDto2Po(SingleResourceBookingDto dto) {
        return CptSourceDayBookingPo.builder()
                .platformId(dto.getPlatformId())
                .platformName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getPlatformName())
                .pageId(dto.getPageId())
                .pageName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getPageName())
                .resourceId(dto.getResourceId())
                .resourceName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getResourceName())
                .sourceId(dto.getSourceId())
                .sourceName(dto.getSourceInfoDto() == null ? "" : dto.getSourceInfoDto().getName())
                .cycleId(dto.getCycleId())
                .groupDate(dto.getGroupDate())
                .status(this.getBookingStatus(dto.getCptScheduleId(), dto.isAdmin()))
                .accountId(dto.getAccountId())
                .cptOrderId(dto.getCptOrderId())
                .cptScheduleId(dto.getCptScheduleId())
                .bookingRatio(dto.getBookingRatio() == null ? 1000 : dto.getBookingRatio())
                .relatedId(dto.getRelatedId())
                .build();

    }

    private Integer getBookingStatus(Integer scheduleId, boolean isAdmin) {
        if (isAdmin) {
            if (scheduleId != null && scheduleId != 0) {
                return CptBookingStatus.LOCKED_SCHEDULED.getCode();
            } else {
                return CptBookingStatus.LOCKED.getCode();
            }
        } else {
            if (scheduleId != null && scheduleId != 0) {
                return CptBookingStatus.BOOKED_SCHEDULED.getCode();
            } else {
                return CptBookingStatus.BOOKED.getCode();
            }
        }
    }

    private BookingItemDto convertBookingPo2Dto(CptSourceDayBookingPo po) {
        BookingItemDto dto = new BookingItemDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
    @Override
    public void addValidateAndDecorate(NewResourceBookingDto dto, Operator operator) {
        Assert.notNull(operator, "operator不可为空");
        BusinessSideBaseDto busDto = businessSideService.getBusinessSideByAccountId(dto.getAccountId());

        List<Integer> sourceIds = cptSourceService.getSourceIdsByBusinessSideId(busDto.getId());
        Assert.isTrue(sourceIds.contains(dto.getSourceId()), "您没有权限预约该位置");

        dto.setBusinessSideBaseDto(busDto);
        List<SourceAllInfoDto> sourceAllInfoDtoList = querySourceService.getSourcesInSourceIds(Collections.singletonList(dto.getSourceId()));
        if (!CollectionUtils.isEmpty(sourceAllInfoDtoList)) {
            dto.setSourceInfoDto(sourceAllInfoDtoList.get(0));
        }
        BusinessSideBaseDto businessSideBaseDto = businessSideService.getBusinessSideByAccountId(operator.getOperatorId());
        dto.setBusinessSideBaseDto(businessSideBaseDto);
    }

    /**
     * 验证添加的预约
     * 设置预约对应的刊例ID
     */
    private void addValidate(SingleResourceBookingDto dto) {
        Assert.isTrue(dto.getGroupDate().getTime() >= Utils.getToday().getTime(), "不能预定今天之前的日期");

        CptCycleDto cycleDto = cptCycleService.getCycleDtoByTime(dto.getGroupDate(), SalesType.CPT.getCode());
        Assert.notNull(cycleDto, "未查询到有效刊例");
        dto.setCycleId(cycleDto.getId());
        List<BookingItemDto> allBookingItemDtoList = queryResourceBooking(cycleDto.getId(), dto.getSourceId());
        if (cycleDto.getStatus() == CycleStatus.INVALID.getCode()) {
            // 提前预定验证
            addValidateForPreBooking(dto, allBookingItemDtoList);
        } else if (cycleDto.getStatus() == CycleStatus.VALID.getCode()
                || (cycleDto.getStatus() == CycleStatus.ADMIN_VALID.getCode() && dto.getBusinessSideBaseDto().getIsAdmin() == 1)) {
            // 常规预定验证
            addValidateForNormal(dto, allBookingItemDtoList);
        } else {
            throw new IllegalArgumentException("未查询到有效刊例");
        }
    }

    private void addValidateForNormal(SingleResourceBookingDto dto, List<BookingItemDto> allBookingItemDtoList) {
        List<BookingItemDto> cycleBookingItemDtos = CollectionUtils.isEmpty(allBookingItemDtoList) ?
                Collections.emptyList() : allBookingItemDtoList.stream()
                .filter(item -> Objects.equals(item.getCycleId(), dto.getCycleId()))
                .collect(Collectors.toList());

        SourceConfigDto sourceConfig = cptSourceService.getSourceConfigBySourceId(dto.getCycleId(), dto.getSourceId(), SalesType.CPT.getCode());
        Assert.notNull(sourceConfig, "未查询到有效资源配置");

        if (Utils.isPositive(sourceConfig.getMFreqLimit()) && cycleBookingItemDtos.size() >= sourceConfig.getMFreqLimit()) {
            throw new IllegalArgumentException("该广告位已超出总频次限制");
        }

        List<BookingItemDto> myBookingItemDtoList = cycleBookingItemDtos.stream().filter(itemDto -> Objects.equals(itemDto.getAccountId(), dto.getAccountId())).collect(Collectors.toList());
        CptSourceBusinessSideLimitDto limitDto = businessSideService.getCptSourceBusinessSideLimitDto(dto.getCycleId(), dto.getSourceId(), dto.getBusinessSideBaseDto().getId());

        if (limitDto != null && Utils.isPositive(limitDto.getMRotationLimit()) && myBookingItemDtoList.size() >= limitDto.getMRotationLimit()) {
            throw new IllegalArgumentException("该广告位已超出业务方频次限制");
        }
        //轮次验证
        int rotationNum = sourceConfig.getRotationNum();
        if (CollectionUtils.isEmpty(cycleBookingItemDtos)) {
            return;
        }
        long count = cycleBookingItemDtos.stream()
                .filter(d -> d.getGroupDate().equals(dto.getGroupDate()))
                .count();
        Assert.isTrue(count < rotationNum, "位次该时间段轮数已预约完");

    }

    private void addValidateForPreBooking(SingleResourceBookingDto dto, List<BookingItemDto> allBookingItemDtoList) {
        // 提前预定条件
        // 1 刊例周期状态=无效
        // 2 资源位提前预定开关=开启
        // 3 账号对应的业务方在提前预定白名单
        SourceConfigDto sourceConfig = cptSourceService.getSourceConfigBySourceId(dto.getCycleId(), dto.getSourceId(), SalesType.CPT.getCode());
        Assert.notNull(sourceConfig, "未查询到有效资源配置");
        Assert.isTrue(PreBookingStatus.ON.getCode().equals(sourceConfig.getPreBookingStatus()), "未查询到有效刊例");

        CptSourceBusinessSidePreBookingDto preBookingDto = businessSideService.getCptSourceBusinessSidePreBookingDto(
                dto.getCycleId(), dto.getSourceId(), dto.getBusinessSideBaseDto().getId());
        Assert.notNull(preBookingDto, "未查询到有效刊例");

        List<BookingItemDto> cycleBookingItemDtos = CollectionUtils.isEmpty(allBookingItemDtoList) ?
                Collections.emptyList() : allBookingItemDtoList.stream()
                .filter(item -> Objects.equals(item.getCycleId(), dto.getCycleId()))
                .collect(Collectors.toList());

        // 总频次验证
        if (Utils.isPositive(sourceConfig.getMFreqLimit())
                && cycleBookingItemDtos.size() >= sourceConfig.getMFreqLimit()) {
            throw new IllegalArgumentException("该广告位已超出总频次限制");
        }

        // 轮次验证
        int rotationNum = sourceConfig.getRotationNum();
        long count = cycleBookingItemDtos.stream()
                .filter(d -> d.getGroupDate().equals(dto.getGroupDate()))
                .count();
        Assert.isTrue(count < rotationNum, "位次该时间段轮数已预约完");

        // 业务方月预定上限验证
        long myBookingItemCount = cycleBookingItemDtos.stream()
                .filter(itemDto -> Objects.equals(itemDto.getAccountId(), dto.getAccountId()))
                .count();
        if (Utils.isPositive(preBookingDto.getMRotationLimit())
                && myBookingItemCount >= preBookingDto.getMRotationLimit()) {
            throw new IllegalArgumentException("该广告位已超出业务方频次限制");
        }
    }

    private CptSourceDayBookingPoExample buildExampleByTimeQueryDto(BookingTimeQueryDto queryDto) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        CptSourceDayBookingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andStatusIn(CptBookingStatus.BOOKED_STATUS_LIST);
        criteria.andTopViewSourceIdEqualTo(0);

        ExampleUtils.notNull(queryDto.getSourceId(), criteria::andSourceIdEqualTo);
        ExampleUtils.notEmpty(queryDto.getSourceIds(), criteria::andSourceIdIn);
        ExampleUtils.notNull(queryDto.getAccountId(), criteria::andAccountIdEqualTo);
        ExampleUtils.notNull(queryDto.getStartTime(), criteria::andGroupDateGreaterThanOrEqualTo);
        ExampleUtils.notNull(queryDto.getEndTime(), criteria::andGroupDateLessThanOrEqualTo);
        return example;
    }

    private CptSourceDayBookingPoExample buildExampleByQueryDto(BookingQueryDto queryDto) {
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        CptSourceDayBookingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (queryDto.isMyResource()) {
            ExampleUtils.notNull(queryDto.getAccountId(), criteria::andAccountIdEqualTo);
        }
        ExampleUtils.notNull(queryDto.getPlatformId(), criteria::andPlatformIdEqualTo);

        ExampleUtils.notNull(queryDto.getPageId(), criteria::andPageIdEqualTo);
        ExampleUtils.notEmpty(queryDto.getPageIds(), criteria::andPageIdIn);

        ExampleUtils.notNull(queryDto.getResourceId(), criteria::andResourceIdEqualTo);
        ExampleUtils.notEmpty(queryDto.getResourceIds(), criteria::andResourceIdIn);

        ExampleUtils.notNull(queryDto.getSourceId(), criteria::andSourceIdEqualTo);
        ExampleUtils.notEmpty(queryDto.getSourceIds(), criteria::andSourceIdIn);

        ExampleUtils.notNull(queryDto.getBeginTime(), criteria::andGroupDateGreaterThanOrEqualTo);
        ExampleUtils.notNull(queryDto.getEndTime(), criteria::andGroupDateLessThanOrEqualTo);
        ExampleUtils.notNull(queryDto.getStatus(), criteria::andStatusIn);
        ExampleUtils.notEmpty(queryDto.getGroupDates(), criteria::andGroupDateIn);

        return example;
    }

    private void log4AddResourceBooking(NewResourceBookingDto dto, Operator operator) {
        ResourceBookingLogBean logBean = ResourceBookingLogBean.builder().build();
        BeanUtils.copyProperties(dto, logBean);
        logService.insertLog(GdLogFlag.BOOKING.getCode(), GdLogFlag.BOOKING, LogOperateType.ADD_BOOKING, operator, logBean);
        log.info("insert log for add cpt booking");
    }

    @Override
    public Integer deleteResourceBooking(BookingItemDto dto, Operator operator) {
        log.info("deleteResourceBooking {}", dto);
        Assert.isTrue(operator.getOperatorId().equals(dto.getAccountId()), "禁止删除非本业务方的预约资源");
        Assert.isTrue(CptBookingStatus.BOOKED_LOCKED_LIST.contains(dto.getStatus()), "删除预约前请先删除排期");
        Assert.isTrue(dto.getTopViewSourceId() == null || dto.getTopViewSourceId() == 0,
                "TopView资源请从TopView资源位删除");
        return this.deleteResourceBookingWithoutValidate(dto, operator);
    }

    @Override
    public long countBookingItem(BookingItemCountQueryDto countQueryDto) {
        Assert.notNull(countQueryDto, "查询条件不可为空");
        Assert.notNull(countQueryDto.getCycleId(), "刊例周期ID不可为空");

        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        CptSourceDayBookingPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(countQueryDto.getCycleId())
                .andStatusIn(CptBookingStatus.BOOKED_STATUS_LIST);
        if (countQueryDto.getSourceId() != null) {
            criteria.andSourceIdEqualTo(countQueryDto.getSourceId());
        }
        if (countQueryDto.getBusinessSideId() != null) {
            criteria.andBusinessSideIdEqualTo(countQueryDto.getBusinessSideId());
        }

        return new BigDecimal(cptSourceDayBookingDao.selectByExample(example).stream()
                .mapToInt(CptSourceDayBookingPo::getBookingRatio).sum()).divide(new BigDecimal(1000), 0,
                BigDecimal.ROUND_HALF_UP).longValue();
    }

    @Override
    public List<BookingItemDto> queryResourceBooking(Integer cycleId, Integer sourceId) {
        Assert.notNull(cycleId, "刊例ID不可为空");
        Assert.notNull(sourceId, "位次ID不可为空");
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCycleIdEqualTo(cycleId)
                .andSourceIdEqualTo(sourceId);
        List<CptSourceDayBookingPo> poList = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(this::convertBookingPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<ResourceBookingNotificationDto> releaseAccountBooking(Integer accountId, Timestamp groupDate) {
        Assert.notNull(accountId, "账号id不可为空");
        Assert.notNull(groupDate, "投放日期不可为空");

        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId)
                .andGroupDateEqualTo(Utils.getBeginOfDay(groupDate))
                .andTopViewSourceIdEqualTo(0);
        List<CptSourceDayBookingPo> bookingPoList = cptSourceDayBookingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(bookingPoList)) {
            return Collections.emptyList();
        }

        Map<Integer, Set<ResourceBookingNotificationDto>> deletedMap = new HashMap<>();
        for (CptSourceDayBookingPo bookingPo : bookingPoList) {
            // 预定状态 已预约/预约已排期
            if (!CptBookingStatus.BOOKED.getCode().equals(bookingPo.getStatus())
                    && !CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(bookingPo.getStatus())) {
                continue;
            }
            // 同一排期下当前日期前的预定需要删除
            List<CptSourceDayBookingPo> scheduleBeforeItemList = Collections.emptyList();
            if (CptBookingStatus.BOOKED_SCHEDULED.getCode().equals(bookingPo.getStatus())
                    && bookingPo.getCptScheduleId() != null
                    && bookingPo.getCptScheduleId() > 0) {
                // 排期已开始则不释放
                GdSchedulePo gdSchedulePo = cptScheduleDelegate.getGdScheduleById(bookingPo.getCptScheduleId());
                if (gdSchedulePo != null
                        && Utils.getToday().compareTo(gdSchedulePo.getBeginDate()) > 0) {
                    continue;
                }
                scheduleBeforeItemList = queryResourceBookingPo(bookingPo.getCptScheduleId(), bookingPo.getGroupDate());
            }

            // 释放资源
            autoReleaseDelete(bookingPo, deletedMap, scheduleBeforeItemList);
        }
        Set<ResourceBookingNotificationDto> deleteBookingSet = deletedMap.get(bookingPoList.get(0).getBusinessSideId());
        if (CollectionUtils.isEmpty(deleteBookingSet)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(deleteBookingSet);
    }

    @Override
    public void scheduleForTopView(List<Integer> topViewSourceIdList, Integer gdOrderId, Integer gdScheduleId, Operator operator) {
        Assert.notEmpty(topViewSourceIdList, "TopView资源预定id不可为空");
        Assert.notNull(gdOrderId, "订单id不可为空");
        Assert.notNull(gdScheduleId, "排期id不可为空");
        Assert.notNull(operator, "操作人不可为空");

        CptSourceDayBookingPo update = new CptSourceDayBookingPo();
        update.setCptOrderId(gdOrderId);
        update.setCptScheduleId(gdScheduleId);
        update.setStatus(CptBookingStatus.BOOKED_SCHEDULED.getCode());
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTopViewSourceIdIn(topViewSourceIdList);
        cptSourceDayBookingDao.updateByExampleSelective(update, example);
    }

    @Override
    public void scheduleForTopViewPlus(List<Integer> ids, Integer gdOrderId, Integer gdScheduleId, Operator operator) {
        Assert.notEmpty(ids, "首焦资源预定id不可为空");
        Assert.notNull(gdOrderId, "订单id不可为空");
        Assert.notNull(gdScheduleId, "排期id不可为空");
        Assert.notNull(operator, "操作人不可为空");

        CptSourceDayBookingPo update = new CptSourceDayBookingPo();
        update.setCptOrderId(gdOrderId);
        update.setCptScheduleId(gdScheduleId);
        update.setStatus(CptBookingStatus.BOOKED_SCHEDULED.getCode());
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdIn(ids);
        cptSourceDayBookingDao.updateByExampleSelective(update, example);
    }

    @Override
    public void bookingForTopView(SingleResourceBookingDto singleDto, Integer rotationNum, Operator operator) {
        Assert.notNull(singleDto, "资源预定信息不可为空");
        Assert.notNull(rotationNum, "资源预定轮数不可为空");
        Assert.notNull(operator, "操作人不可为空");

        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(CptBookingStatus.BOOKED_LOCKED_LIST)
                .andAccountIdEqualTo(operator.getOperatorId())
                .andOperatorEqualTo(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                        : operator.getBilibiliUserName())
                .andCycleIdEqualTo(singleDto.getCycleId())
                .andGroupDateEqualTo(singleDto.getGroupDate())
                .andSourceIdEqualTo(singleDto.getSourceId())
                .andTopViewSourceIdEqualTo(0);
        List<CptSourceDayBookingPo> unUsedCommonBookingList = cptSourceDayBookingDao.selectByExample(example);

        int reUsedCount = 0;
        if (!CollectionUtils.isEmpty(unUsedCommonBookingList)) {
            unUsedCommonBookingList = unUsedCommonBookingList.stream().limit(rotationNum).collect(Collectors.toList());
            reUsedCount = unUsedCommonBookingList.size();
            CptSourceDayBookingPo updatePo = CptSourceDayBookingPo.builder()
                    .topViewSourceId(singleDto.getTopViewSourceId())
                    .resourceType(singleDto.getResourceType())
                    .operator(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                            : operator.getBilibiliUserName())
                    .bookingRatio(singleDto.getBookingRatio()/rotationNum)
                    .relatedId(singleDto.getRelatedId())
                    .build();
            CptSourceDayBookingPoExample updateExample = new CptSourceDayBookingPoExample();
            updateExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andStatusIn(CptBookingStatus.BOOKED_LOCKED_LIST)
                    .andTopViewSourceIdEqualTo(0)
                    .andIdIn(unUsedCommonBookingList.stream().map(CptSourceDayBookingPo::getId)
                            .collect(Collectors.toList()));
            int result = cptSourceDayBookingDao.updateByExampleSelective(updatePo, updateExample);
            Assert.isTrue(result == reUsedCount, "资源预定信息已更新, 请刷新重试");
        }

        int newCount = rotationNum - reUsedCount;
        for (int i = 0; i < newCount; i++) {
            CptSourceDayBookingPo po = this.convertDto2Po(singleDto);
            po.setBusinessSideId(singleDto.getBusinessSideBaseDto().getId());
            po.setOperator(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                    : operator.getBilibiliUserName());
            po.setTopViewSourceId(singleDto.getTopViewSourceId());
            po.setResourceType(singleDto.getResourceType());
            po.setBookingRatio(singleDto.getBookingRatio()/rotationNum);
            cptSourceDayBookingDao.insertSelective(po);
        }
    }

    @Override
    public void deleteForTopView(Integer topViewSourceId, Operator operator) {
        Assert.notNull(topViewSourceId, "资源预定Id不可为空");
        Assert.notNull(operator, "操作人不可为空");

        CptSourceDayBookingPo updatePo = CptSourceDayBookingPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .operator(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName()
                : operator.getBilibiliUserName())
                .build();
        CptSourceDayBookingPoExample example = new CptSourceDayBookingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andTopViewSourceIdEqualTo(topViewSourceId)
                .andStatusIn(CptBookingStatus.BOOKED_LOCKED_LIST);
        cptSourceDayBookingDao.updateByExampleSelective(updatePo, example);
    }


}
