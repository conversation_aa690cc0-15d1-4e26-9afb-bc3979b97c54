package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.BrandMainsiteAccountConfigPo;
import com.bilibili.cpt.platform.biz.po.BrandMainsiteAccountConfigPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BrandMainsiteAccountConfigDao {
    long countByExample(BrandMainsiteAccountConfigPoExample example);

    int deleteByExample(BrandMainsiteAccountConfigPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BrandMainsiteAccountConfigPo record);

    int insertBatch(List<BrandMainsiteAccountConfigPo> records);

    int insertUpdateBatch(List<BrandMainsiteAccountConfigPo> records);

    int insert(BrandMainsiteAccountConfigPo record);

    int insertUpdateSelective(BrandMainsiteAccountConfigPo record);

    int insertSelective(BrandMainsiteAccountConfigPo record);

    List<BrandMainsiteAccountConfigPo> selectByExample(BrandMainsiteAccountConfigPoExample example);

    BrandMainsiteAccountConfigPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BrandMainsiteAccountConfigPo record, @Param("example") BrandMainsiteAccountConfigPoExample example);

    int updateByExample(@Param("record") BrandMainsiteAccountConfigPo record, @Param("example") BrandMainsiteAccountConfigPoExample example);

    int updateByPrimaryKeySelective(BrandMainsiteAccountConfigPo record);

    int updateByPrimaryKey(BrandMainsiteAccountConfigPo record);
}