package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.BrandBlueKeywordItemPo;
import com.bilibili.cpt.platform.biz.po.BrandBlueKeywordItemPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BrandBlueKeywordItemDao {
    long countByExample(BrandBlueKeywordItemPoExample example);

    int deleteByExample(BrandBlueKeywordItemPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BrandBlueKeywordItemPo record);

    int insertBatch(List<BrandBlueKeywordItemPo> records);

    int insertUpdateBatch(List<BrandBlueKeywordItemPo> records);

    int insert(BrandBlueKeywordItemPo record);

    int insertUpdateSelective(BrandBlueKeywordItemPo record);

    int insertSelective(BrandBlueKeywordItemPo record);

    List<BrandBlueKeywordItemPo> selectByExample(BrandBlueKeywordItemPoExample example);

    BrandBlueKeywordItemPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BrandBlueKeywordItemPo record, @Param("example") BrandBlueKeywordItemPoExample example);

    int updateByExample(@Param("record") BrandBlueKeywordItemPo record, @Param("example") BrandBlueKeywordItemPoExample example);

    int updateByPrimaryKeySelective(BrandBlueKeywordItemPo record);

    int updateByPrimaryKey(BrandBlueKeywordItemPo record);
}