package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo;
import com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CreativeDrawGestureDao {
    long countByExample(CreativeDrawGesturePoExample example);

    int deleteByExample(CreativeDrawGesturePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CreativeDrawGesturePo record);

    int insertBatch(List<CreativeDrawGesturePo> records);

    int insertUpdateBatch(List<CreativeDrawGesturePo> records);

    int insert(CreativeDrawGesturePo record);

    int insertUpdateSelective(CreativeDrawGesturePo record);

    int insertSelective(CreativeDrawGesturePo record);

    List<CreativeDrawGesturePo> selectByExample(CreativeDrawGesturePoExample example);

    CreativeDrawGesturePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CreativeDrawGesturePo record, @Param("example") CreativeDrawGesturePoExample example);

    int updateByExample(@Param("record") CreativeDrawGesturePo record, @Param("example") CreativeDrawGesturePoExample example);

    int updateByPrimaryKeySelective(CreativeDrawGesturePo record);

    int updateByPrimaryKey(CreativeDrawGesturePo record);
}