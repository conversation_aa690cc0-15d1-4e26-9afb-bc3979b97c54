package com.bilibili.cpt.platform.biz.dao;

import com.bilibili.cpt.platform.biz.po.CptLogOperationPo;
import com.bilibili.cpt.platform.biz.po.CptLogOperationPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CptLogOperationDao {
    long countByExample(CptLogOperationPoExample example);

    int deleteByExample(CptLogOperationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(CptLogOperationPo record);

    int insertSelective(CptLogOperationPo record);

    List<CptLogOperationPo> selectByExampleWithBLOBs(CptLogOperationPoExample example);

    List<CptLogOperationPo> selectByExample(CptLogOperationPoExample example);

    CptLogOperationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CptLogOperationPo record, @Param("example") CptLogOperationPoExample example);

    int updateByExampleWithBLOBs(@Param("record") CptLogOperationPo record, @Param("example") CptLogOperationPoExample example);

    int updateByExample(@Param("record") CptLogOperationPo record, @Param("example") CptLogOperationPoExample example);

    int updateByPrimaryKeySelective(CptLogOperationPo record);

    int updateByPrimaryKeyWithBLOBs(CptLogOperationPo record);

    int updateByPrimaryKey(CptLogOperationPo record);
}