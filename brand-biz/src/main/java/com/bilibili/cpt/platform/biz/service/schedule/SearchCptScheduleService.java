package com.bilibili.cpt.platform.biz.service.schedule;

import com.alibaba.fastjson2.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.SwitchStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.CreativeStatus;
import com.bilibili.brand.api.common.enums.GdOrderType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.api.common.enums.ScheduleStatus;
import com.bilibili.brand.api.common.exception.ScheduleExceptionCode;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.creative.po.GdCreativePoExample;
import com.bilibili.brand.biz.cycle.CycleServiceFacade;
import com.bilibili.brand.biz.cycle.dto.CycleQueryDto;
import com.bilibili.brand.biz.log.bean.ScheduleLogBean;
import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDateDao;
import com.bilibili.brand.biz.schedule.dao.SearchKeywordDao;
import com.bilibili.brand.biz.schedule.handler.DeleteScheduleValidateHandler;
import com.bilibili.brand.biz.schedule.handler.WakePriceRaiseHandler;
import com.bilibili.brand.biz.schedule.po.GdScheduleDatePo;
import com.bilibili.brand.biz.schedule.po.GdScheduleDatePoExample;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePoExample;
import com.bilibili.brand.biz.schedule.po.SearchKeywordPo;
import com.bilibili.brand.biz.schedule.po.SearchKeywordPoExample;
import com.bilibili.brand.biz.schedule.service.ScheduleGameService;
import com.bilibili.brand.dto.cycle.PriceRaiseDto;
import com.bilibili.brand.dto.resource.GameDto;
import com.bilibili.brand.exception.ConflictException;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.creative.dto.PeriodDto;
import com.bilibili.cpt.platform.api.creative.service.IQueryCptCreativeService;
import com.bilibili.cpt.platform.api.location.dto.CptSourceAllInfoDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceQueryDto;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.api.order.dto.CptOrderDto;
import com.bilibili.cpt.platform.api.schedule.dto.KeywordDto;
import com.bilibili.cpt.platform.api.schedule.dto.KeywordsPackageDto;
import com.bilibili.cpt.platform.api.schedule.dto.NewBrandScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.SearchCptScheduleAddUpdateResult;
import com.bilibili.cpt.platform.api.schedule.dto.SearchCptScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.SearchCptUpdateScheduleDto;
import com.bilibili.cpt.platform.api.schedule.service.ISearchCptScheduleService;
import com.bilibili.cpt.platform.biz.handler.schedule.CycleDateHandler;
import com.bilibili.cpt.platform.biz.handler.schedule.HistoryScheduleDateHandler;
import com.bilibili.cpt.platform.biz.handler.schedule.SearchKeyWordHandler;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.biz.service.order.CptOrderService;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.order.dto.UpdateOrderDto;
import com.bilibili.crm.platform.soa.ISoaCrmOrderService;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.enums.BizSalesTypeEnum;
import com.bilibili.enums.GameChannelEnum;
import com.bilibili.enums.ScheduleLaunchSceneEnum;
import com.bilibili.enums.WakeAppType;
import com.bilibili.location.api.service.ITemplateService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2017/6/19.
 */
@Service
@Slf4j
public class SearchCptScheduleService extends CptBaseService implements ISearchCptScheduleService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CptOrderService orderService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private GdScheduleDao scheduleDao;

    @Value("${search.cpt.max.schedule.days:31}")
    private Long maxScheduleDays;

    @Autowired
    private CptScheduleDelegate cptScheduleDelegate;

    @Autowired
    private GdScheduleDateDao gdScheduleDateDao;

    @Autowired
    private HistoryScheduleDateHandler historyScheduleDateHandler;

    @Autowired
    private GdScheduleDao gdScheduleDao;

    @Autowired
    private IGdLogService logService;

    @Autowired
    private SearchKeywordDao searchKeywordDao;

    @Autowired
    private IQuerySourceService querySourceService;

    @Autowired
    private IQueryCptCreativeService queryCptCreativeService;

    @Autowired
    private ITemplateService templateService;

    @Value("${search.cpt.limit.keywords:100}")
    private Integer limitWord;

    @Autowired
    private ICptSourceService cptSourceService;

    @Autowired
    private IBusinessSideService businessSideService;

    @Autowired
    private ISoaCrmOrderService soaCrmOrderService;

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private GdCreativeDao gdCreativeDao;

    @Autowired
    private CycleDateHandler cycleDateHandler;

    @Autowired
    private CptScheduleDelegate delegate;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private ISoaQueryAccountService accountService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private SearchKeyWordHandler searchKeyWordHandler;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private DeleteScheduleValidateHandler deleteScheduleValidateHandler;
    @Autowired
    private CycleServiceFacade cycleServiceFacade;
    @Autowired
    private ScheduleGameService scheduleGameService;

    @Autowired
    private IGdCreativeService gdCreativeService;

    @Autowired
    private WakePriceRaiseHandler wakePriceRaiseHandler;

    private void batchLock(Set<RLock> allLock) {
        Set<RLock> successLockSet = new HashSet<>();
        boolean isSuccess = true;
        for (RLock rLock : allLock) {
            try {
                isSuccess = rLock.tryLock(5, 5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                LOGGER.error("rLock.tryLock.e", e);
                isSuccess = false;
            }
            if (!isSuccess) {
                break;
            } else {
                successLockSet.add(rLock);
            }
        }
        if (!isSuccess) {
            successLockSet.forEach(Lock::unlock);
        }
        Assert.isTrue(isSuccess, "资源正在被其他人编辑，请稍后重试");
    }

    private Set<RLock> lockUpdate(GdSchedulePo schedulePo) {
        Set<RLock> allLock = new HashSet<>();
        RLock buLock = redissonClient.getLock(schedulePo.getBusinessSideId() + CptConstants.BU_SIDE_LOCK_SUFFIX);
        RLock scheduleLock = redissonClient.getLock(schedulePo.getScheduleId() + CptConstants.SCHEDULE_LOCK_SUFFIX);
        RLock sourceLock = redissonClient.getLock(schedulePo.getSlotId() + CptConstants.SOURCE_LOCK_SUFFIX);
        RLock orderLock = redissonClient.getLock(schedulePo.getOrderId() + CptConstants.ORDER_LOCK_SUFFIX);
        allLock.add(buLock);
        allLock.add(scheduleLock);
        allLock.add(sourceLock);
        allLock.add(orderLock);
        this.batchLock(allLock);
        return allLock;
    }

    @Override
    public SearchCptScheduleDto getScheduleById(Integer scheduleId) throws ServiceException {
        LOGGER.info("getScheduleById.scheduleId {}", scheduleId);
        GdSchedulePo po = scheduleDao.selectByPrimaryKey(scheduleId);
        if (null == po || po.getIsDeleted().equals(IsDeleted.DELETED.getCode())) {
            throw new ServiceException(ScheduleExceptionCode.NOT_EXIST_SCHEDULE);
        }

        FcOrderPo orderPo = orderService.getGdOrderPoById(po.getOrderId());
        SearchCptScheduleDto scheduleDto = this.poToDto(po);
        scheduleDto.setOrderName(orderPo.getOrderName());
        if (Utils.isPositive(scheduleDto.getTemplateId())) {
            //搜索cpt排期模板一般都会存在，以防万一兜底防止报错
            TemplateDto template = queryTemplateService.getTemplateById(scheduleDto.getTemplateId());
            scheduleDto.setTemplateName(template.getTemplateName());
            scheduleDto.setCardType(template.getCardType());
        }
        return scheduleDto;
    }

    private SearchCptScheduleDto poToDto(GdSchedulePo schedulePo) {
        SearchCptScheduleDto scheduleDto = new SearchCptScheduleDto();
        BeanUtils.copyProperties(schedulePo, scheduleDto);

        if (configCenter.getSearchCptConfig().isSupportHourAndDayTemplate(schedulePo.getTemplateId())) {
            scheduleDto.setBeginTime(schedulePo.getGdBeginTime());
            scheduleDto.setEndTime(schedulePo.getGdEndTime());
        } else {
            scheduleDto.setBeginTime(schedulePo.getBeginDate());
            scheduleDto.setEndTime(schedulePo.getEndDate());
        }

        scheduleDto.setSalesTypeDesc(SalesType.getByCode(schedulePo.getSalesType()).getDesc());

        Map<Integer, Pair<List<String>, List<KeywordsPackageDto>>> keywordsAndPackages = this.searchKeyWordHandler.
                getKeywordsAndPackages(Collections.singletonList(schedulePo.getScheduleId()), false);
        Pair<List<String>, List<KeywordsPackageDto>> keywordPair = keywordsAndPackages.
                getOrDefault(schedulePo.getScheduleId(), Pair.of(new ArrayList<>(), new ArrayList<>()));
        scheduleDto.setKeywords(keywordPair.getFirst());
        scheduleDto.setKeywordsPackages(keywordPair.getSecond());

        Map<Integer, GameDto> scheduleGameMap = this.scheduleGameService.getGameMapByScheduleId(Lists.newArrayList(schedulePo.getScheduleId()));
        scheduleDto.setGame(scheduleGameMap.get(schedulePo.getScheduleId()));
        return scheduleDto;
    }

    /**
     * 只有增加排期 减少排期
     * 刊例价格修改时已有时间段价格计算不变 新增的用新的刊例价格
     *
     * @param update   更新排期对象
     * @param operator 操作人信息
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public SearchCptScheduleAddUpdateResult updateCptSchedule(SearchCptUpdateScheduleDto update, Operator operator) {
        GdSchedulePo schedulePo = cptScheduleDelegate.getGdScheduleById(update.getScheduleId());
        Assert.notNull(schedulePo, "排期不存在");
        Set<RLock> allLock = this.lockUpdate(schedulePo);
        try {
            return this.update(schedulePo, update, operator);
        } finally {
            allLock.forEach(Lock::unlock);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public SearchCptScheduleAddUpdateResult update(GdSchedulePo schedulePo,
                                                   SearchCptUpdateScheduleDto update,
                                                   Operator operator) {

        SearchCptScheduleAddUpdateResult updateResult = new SearchCptScheduleAddUpdateResult();

        CptOrderDto cptOrderDto = cptScheduleDelegate.getValidOrder(schedulePo.getOrderId(), operator);
        Assert.isTrue(cptOrderDto.getAccountId().equals(operator.getOperatorId()), "不能操作不属于自己的订单");
        update.setOrderDto(cptOrderDto);

        this.validateUpdate(update);

        historyScheduleDateHandler.validateUpdate(
                update.getBeginTime(),
                update.getEndTime(),
                update.getAppPackageId(),
                schedulePo
        );

        List<Integer> cycleIds = cycleDateHandler.validateForSearchCpt(update.getBeginTime(), update.getEndTime());
        if (Utils.isPositive(update.getCycleId())) {
            cycleServiceFacade.getValidCycle(CycleQueryDto.builder()
                    .cycleId(update.getCycleId())
                    .orderProduct(OrderProduct.SEARCH_CPT)
                    .build());
            cycleIds = Lists.newArrayList(update.getCycleId());
        }

        List<KeywordsPackageDto> keywordsPackages = null;
        try {
            keywordsPackages = checkKeyWordConflict(Lists.newArrayList(schedulePo.getSlotId()),
                    update.getKeywords(),
                    update.getKeywordsPackages(),
                    update.getBeginTime(),
                    update.getEndTime(),
                    schedulePo.getScheduleId(),
                    update.isDoubleConfirm(),
                    update.isSkipCheck(),
                    cptOrderDto.getId());
        } catch (ConflictException e) {
            updateResult.setConflictMsg(e.getMessage());
            return updateResult;
        }

        update.setKeywordsPackages(keywordsPackages);

        List<Timestamp> updateEachDays = TimeUtils.getEachDay(update.getBeginTime(), update.getEndTime());
        Set<Timestamp> updateEachDaySet = new HashSet<>(updateEachDays);

        List<Timestamp> oldEachDays = TimeUtils.getEachDay(schedulePo.getBeginDate(), schedulePo.getEndDate());
        List<Timestamp> deleteEachDays = oldEachDays.stream().filter(oldEachDay -> !updateEachDaySet.contains(oldEachDay)).collect(Collectors.toList());
        List<PeriodDto> periodDtos = queryCptCreativeService.getSchedule2CreativeTimeByScheduleId(schedulePo.getScheduleId());
        if (!CollectionUtils.isEmpty(deleteEachDays)) {
            Set<Timestamp> deleteDaySet = new HashSet<>(deleteEachDays);
            for (PeriodDto periodDto : periodDtos) {
                List<Timestamp> creativeEachDays = TimeUtils.getEachDay(periodDto.getStartTime(), periodDto.getEndTime());
                for (Timestamp creativeEachDay : creativeEachDays) {
                    Assert.isTrue(!deleteDaySet.contains(creativeEachDay), "请先删除缩短的排期中的创意");
                }
            }
        }
        // 在排期下无创意时支持变更
        if (Objects.nonNull(update.getBizSalesType()) && !Objects.equals(update.getBizSalesType(), schedulePo.getBizSalesType())) {
            Assert.isTrue(CollectionUtils.isEmpty(periodDtos), "在排期下无创意时才支持变更");
        }


        SearchCptScheduleDto dto = new SearchCptScheduleDto();
        BeanUtils.copyProperties(update, dto);
        TemplateDto templateDto = queryTemplateService.getTemplateById(schedulePo.getTemplateId());
        dto.setTemplateId(schedulePo.getTemplateId());
        dto.setCycleIds(cycleIds);
        dto.setCardType(templateDto.getCardType());
        dto.setSourceIds(Lists.newArrayList(schedulePo.getSlotId()));
        dto.setContractAccountId(gdOrderService.getContractAccountId(cptOrderDto.getCrmContractId()));
        dto.setAccountId(operator.getOperatorId());
        dto.setLaunchScene(schedulePo.getLaunchScene());
        UpdateOrderDto updateOrderDto = this.buildCptOrderDto(dto);
        Assert.notNull(updateOrderDto, "不存在对应的刊例价");
        LOGGER.info("UpdateOrderDto id {} dto {}", updateOrderDto.getId(), updateOrderDto);

        if (cptOrderDto.getCrmContractId() > 0) {
            this.tryUpdateCrm(updateOrderDto, operator);
        }

        BusinessSideBaseDto businessSideBaseDto = businessSideService.getBusinessSideById(schedulePo.getBusinessSideId());

        GdSchedulePo updateGdSchedulePo = GdSchedulePo.builder()
                .scheduleId(update.getScheduleId())
                .beginDate(update.getBeginTime())
                .endDate(update.getEndTime())
                .businessSideId(businessSideBaseDto.getId())
                .externalPrice(dto.getExternalPrice())
                .internalPrice(dto.getInternalPrice())
                .cycleId(dto.getCycleId())
                .businessSideId(businessSideBaseDto.getId())
                .slotId(schedulePo.getSlotId())
                .orderId(schedulePo.getOrderId())
                .accountId(schedulePo.getAccountId())
                .name(update.getName())
                .mid(update.getMid())
                .promotionPurposeType(update.getPromotionPurposeType())
                .appPackageId(update.getAppPackageId())
                .gdBeginTime(update.getBeginTime())
                .gdEndTime(getGdEndTime(update.getEndTime(), schedulePo.getTemplateId()))
                .bizSalesType(update.getBizSalesType())
                .totalImpression(update.getTotalImpression())
                .wakeAppType(update.getWakeAppType())
                .build();


        int result = gdScheduleDao.updateByPrimaryKeySelective(updateGdSchedulePo);

        insertKeyWordsRelations(Lists.newArrayList(update.getScheduleId()), update.getKeywordsPackages(), true);

        this.scheduleGameService.saveGame(update.getScheduleId(), update.getGame());

        if (result > 0) {
            updateGdScheduleDate(schedulePo.getAccountId(), schedulePo.getOrderId(),
                    update.getScheduleId(),
                    update.getBeginTime(),
                    update.getEndTime());

            if (cptOrderDto.getCrmContractId() > 0) {
                orderService.toBeAudit(cptOrderDto.getId());
                this.updateCrm(updateOrderDto, operator);
            }
            gdOrderService.updateOrder(cptOrderDto.getId(), updateOrderDto.getBeginTime(),
                    updateOrderDto.getEndTime(), updateOrderDto.getAmount(), 0L);

        }

        logService.insertLog(update.getScheduleId(),
                GdLogFlag.SCHEDULE,
                LogOperateType.UPDATE_SCHEDULE,
                operator,
                ScheduleLogBean.builder()
                        .id(update.getScheduleId())
                        .beginDate(TimeUtils.getTimestamp2String(update.getBeginTime()))
                        .endDate(TimeUtils.getTimestamp2String(update.getEndTime()))
                        .externalPrice(updateGdSchedulePo.getExternalPrice())
                        .internalPrice(updateGdSchedulePo.getInternalPrice())
                        .build());
        return updateResult;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public SearchCptScheduleAddUpdateResult createCptScheduleBySourceGroup(SearchCptScheduleDto scheduleDto,
                                                                           Operator operator) {
        this.validate(scheduleDto);

        SearchCptScheduleAddUpdateResult insertResult = new SearchCptScheduleAddUpdateResult();

        CptOrderDto cptOrderDto = cptScheduleDelegate.getValidOrder(scheduleDto.getOrderId(), operator);
        Assert.isTrue(cptOrderDto.getAccountId().equals(operator.getOperatorId()), "不能操作不属于自己的订单");
        scheduleDto.setOrderDto(cptOrderDto);

        List<Integer> cycleIds = cycleDateHandler.validateForSearchCpt(scheduleDto.getBeginTime(), scheduleDto.getEndTime());
        if (CollectionUtils.isEmpty(scheduleDto.getCycleIds())) {
            scheduleDto.setCycleIds(cycleIds);
        } else {
            cycleServiceFacade.getValidCycle(CycleQueryDto.builder()
                    .cycleId(scheduleDto.getCycleIds().get(0))
                    .orderProduct(OrderProduct.SEARCH_CPT)
                    .build());
        }

        Assert.isTrue(!CollectionUtils.isEmpty(scheduleDto.getSourceIds()), "请选择资源位!");
        Map<Integer, SourceAllInfoDto> sourceAllInfoDtoMap = querySourceService
                .getCptSourceMapInSourceIds(scheduleDto.getSourceIds());
        scheduleDto.getSourceIds().forEach(sourceId -> {
            Assert.isTrue(!CollectionUtils.isEmpty(sourceAllInfoDtoMap), "找不到当前资源位信息!");
            SourceAllInfoDto infoDto = sourceAllInfoDtoMap.get(sourceId);
            Assert.notNull(infoDto, "广告位不存在 " + sourceId);
            Assert.isTrue(!CollectionUtils.isEmpty(infoDto.getTemplateDtos()), "找不到当前资源位对应的模板!");
            Set<Integer> templateIds = infoDto.getTemplateDtos()
                    .stream().map(TemplateDto::getTemplateId).collect(Collectors.toSet());
            Assert.isTrue(templateIds.contains(scheduleDto.getTemplateId()), "当前资源位不支持此模板!");
        });
        scheduleDto.setSourceAllInfoDtoMap(sourceAllInfoDtoMap);

        List<KeywordsPackageDto> keywordsPackages;
        try {
            keywordsPackages = checkKeyWordConflict(scheduleDto.getSourceIds(),
                    scheduleDto.getKeywords(),
                    scheduleDto.getKeywordsPackages(),
                    scheduleDto.getBeginTime(),
                    scheduleDto.getEndTime(),
                    0,
                    scheduleDto.isDoubleConfirm(),
                    scheduleDto.isSkipCheck(),
                    cptOrderDto.getId());
        } catch (ConflictException e) {
            insertResult.setConflictMsg(e.getMessage());
            return insertResult;
        }

        scheduleDto.setKeywordsPackages(keywordsPackages);

        List<TemplateDto> templateDtoS = templateService
                .getValidTemplatesInIds(Lists.newArrayList(scheduleDto.getTemplateId()));
        if (CollectionUtils.isEmpty(templateDtoS)) {
            throw new IllegalArgumentException("不存在对应的模板");
        }
        TemplateDto templateDto = templateDtoS.get(0);
        scheduleDto.setTemplateName(templateDto.getTemplateName());

        sourceAllInfoDtoMap.forEach((k, v) -> {
            scheduleDto.setSourceIds(Lists.newArrayList(k));
            this.createSchedule(scheduleDto, operator);
        });

        return insertResult;
    }

    private List<KeywordsPackageDto> checkKeyWordConflict(List<Integer> slotIds, List<String> keywords,
                                                          List<KeywordsPackageDto> keywordPackages,
                                                          Timestamp beginDate, Timestamp endDate, Integer scheduleId,
                                                          boolean isDoubleConfirm, boolean isSkipCheck, Integer orderId) {
        if (CollectionUtils.isEmpty(keywords) && CollectionUtils.isEmpty(keywordPackages)) {
            throw new IllegalArgumentException("关键词/词包不能为空");
        }

        StringBuilder conflictMsg = new StringBuilder();

        //关键词检查
        Set<String> keywordsSet = new HashSet<>();
        if (!CollectionUtils.isEmpty(keywords)) {
            if (keywords.size() > limitWord) {
                throw new IllegalArgumentException("关键词不能超过" + limitWord + "个!");
            }

            //关键词之间冲突直接抛出参数异常（用户调整下即可）
            keywords.forEach(t -> {
                if (keywordsSet.contains(t)) {
                    throw new IllegalArgumentException("当前排期，关键词【" + t + "】重复");
                }
                keywordsSet.add(t);
            });
        }

        List<Integer> keywordPackageIds = keywordPackages.stream()
                .map(KeywordsPackageDto::getPackageId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(keywordsSet) && CollectionUtils.isEmpty(keywordPackageIds)) {
            //防止词包没查到数据
            throw new IllegalArgumentException("关键词/词包不能为空");
        }

        List<KeywordsPackageDto> result = new ArrayList<>();

        //添加关键词
        result.add(KeywordsPackageDto.builder()
                .packageId(0)
                .keywords(new ArrayList<>(keywordsSet))
                .build());

        //添加词包
        result.addAll(keywordPackageIds.stream()
                .map(id -> KeywordsPackageDto.builder().packageId(id).build())
                .collect(Collectors.toList()));

        if (isDoubleConfirm || isSkipCheck) {
            return result;
        }

        //【品牌】品牌系统搜索cpt增加订单白名单跳过词包过滤
        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004561790
        //有些词包太大并且词包&排期大量冲突，导致排期创建超时
        List<Integer> skipOrderIdList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SEARCH_CPT_SKIP_KEYWORDS_CONFLICT_ORDER_WHITE_LIST.getCode());
        if (skipOrderIdList.contains(orderId)) {
            //只校验词包是否存在即可
            //更新操作时，keywordPackageIds中可能有暂停的词包
            Map<Integer, KeywordsPackageDto> keywordsPackages = this.searchKeyWordHandler.getKeywordsPackages(keywordPackageIds, false, true);
            Assert.isTrue(keywordPackageIds.size() == keywordsPackages.size(), "部分词包不存在，请检查后重试");
            return result;
        }
        Map<Integer, KeywordsPackageDto> kwPackages = this.searchKeyWordHandler.getKeywordsPackages(keywordPackageIds,
                true, true);//更新操作时，keywordPackageIds中可能有暂停的词包
        Map<String, Set<Integer>> keyword2PackageListMap = new HashMap<>();
        kwPackages.forEach((k, v) -> v.getKeywords().forEach(keyword -> {
            if (!keyword2PackageListMap.containsKey(keyword)) {
                keyword2PackageListMap.put(keyword, new HashSet<>());
            }
            keyword2PackageListMap.get(keyword).add(k);
        }));

        AtomicInteger conflictIndex = new AtomicInteger(0);
        AtomicInteger conflictSubIndex = new AtomicInteger(0);

        //词包之间检查
        keyword2PackageListMap.forEach((keyword, packageIds) -> {
            if (packageIds.size() <= 1) {
                return;
            }
            if (conflictSubIndex.get() == 0) {
                conflictMsg.append(conflictIndex.incrementAndGet())
                        .append("、词包之间冲突<br>");
            }
            StringBuilder conflictPackageBuilder = new StringBuilder();
            int index = 1;
            for (Integer packageId : packageIds) {
                conflictPackageBuilder.append("【")
                        .append(packageId)
                        .append("，")
                        .append(kwPackages.get(packageId).getPackageName())
                        .append("】");
                if (index++ < packageIds.size()) {
                    conflictPackageBuilder.append("、");
                }
            }
            conflictMsg.append(String.format("（%d.%d）关键词【%s】，在词包%s之间重复。<br>", conflictIndex.get(),
                    conflictSubIndex.incrementAndGet(), keyword, conflictPackageBuilder));
        });

        //关键词&词包重复性检查
        conflictSubIndex.set(0);
        keywordsSet.forEach(keyword -> {
            if (keyword2PackageListMap.containsKey(keyword)) {
                if (conflictSubIndex.get() == 0) {
                    conflictMsg.append(conflictIndex.incrementAndGet())
                            .append("、关键词与词包之间冲突<br>");
                }
                Set<Integer> packageIds = keyword2PackageListMap.get(keyword);
                StringBuilder conflictPackageBuilder = new StringBuilder();
                int index = 1;
                for (Integer packageId : packageIds) {
                    conflictPackageBuilder.append("【")
                            .append(packageId)
                            .append("，")
                            .append(kwPackages.get(packageId).getPackageName())
                            .append("】");
                    if (index++ < packageIds.size()) {
                        conflictPackageBuilder.append("、");
                    }
                }
                conflictMsg.append(String.format("（%d.%d）关键词【%s】与 词包%s中关键词重复。<br>",
                        conflictIndex.get(), conflictSubIndex.incrementAndGet(), keyword, conflictPackageBuilder));
            }
        });

        GdSchedulePoExample example = new GdSchedulePoExample();
        example.or()
                .andGdEndTimeGreaterThanOrEqualTo(beginDate)
                .andGdBeginTimeLessThanOrEqualTo(endDate)
                .andSlotIdIn(slotIds)
                .andScheduleIdNotEqualTo(scheduleId)
                .andSalesTypeEqualTo(SalesType.SEARCH_CPT.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdSchedulePo> schedulePos = gdScheduleDao.selectByExample(example);

        if (!CollectionUtils.isEmpty(schedulePos)) {
            List<Integer> scheduleIds = schedulePos.stream().map(GdSchedulePo::getScheduleId).collect(Collectors.toList());
            SearchKeywordPoExample keywordPoExample = new SearchKeywordPoExample();
            keywordPoExample.or().andScheduleIdIn(scheduleIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<SearchKeywordPo> searchKeyPos = searchKeywordDao.selectByExample(keywordPoExample);

            List<Integer> existPackageIdList = searchKeyPos.stream()
                    .filter(p -> Utils.isPositive(p.getPackageId()))
                    .map(SearchKeywordPo::getPackageId)
                    .collect(Collectors.toList());

            Map<Integer, Set<Integer>> packageId2ScheduleMap = searchKeyPos.stream()
                    .filter(p -> Utils.isPositive(p.getPackageId()))
                    .collect(Collectors.groupingBy(SearchKeywordPo::getPackageId,
                            Collectors.mapping(SearchKeywordPo::getScheduleId, Collectors.toSet())));

            Map<Integer, KeywordsPackageDto> kpMap = this.searchKeyWordHandler.getKeywordsPackages(existPackageIdList,
                    true, false);

            Map<String, Set<Integer>> keywordScheduleMap = new HashMap<>();
            Map<Integer, Set<String>> schedulePureKeywordMap = new HashMap<>();
            Map<Integer, Map<String, Set<Integer>>> scheduleKeywordPackageMap = new HashMap<>();

            for (SearchKeywordPo searchKeyPo : searchKeyPos) {
                if (Utils.isPositive(searchKeyPo.getPackageId())) {
                    KeywordsPackageDto packageDto = kpMap.get(searchKeyPo.getPackageId());
                    if (Objects.nonNull(packageDto)) {
                        if (!scheduleKeywordPackageMap.containsKey(searchKeyPo.getScheduleId())) {
                            scheduleKeywordPackageMap.put(searchKeyPo.getScheduleId(), new HashMap<>());
                        }
                        Map<String, Set<Integer>> stringSetMap = scheduleKeywordPackageMap
                                .get(searchKeyPo.getScheduleId());
                        for (String keyword : packageDto.getKeywords()) {
                            if (!keywordScheduleMap.containsKey(keyword)) {
                                keywordScheduleMap.put(keyword, new HashSet<>());
                            }
                            keywordScheduleMap.get(keyword).add(searchKeyPo.getScheduleId());
                            if (!stringSetMap.containsKey(keyword)) {
                                stringSetMap.put(keyword, new HashSet<>());
                            }
                            stringSetMap.get(keyword).add(packageDto.getPackageId());
                        }
                    }
                } else {
                    if (!keywordScheduleMap.containsKey(searchKeyPo.getKeyword())) {
                        keywordScheduleMap.put(searchKeyPo.getKeyword(), new HashSet<>());
                    }
                    keywordScheduleMap.get(searchKeyPo.getKeyword()).add(searchKeyPo.getScheduleId());

                    if (!schedulePureKeywordMap.containsKey(searchKeyPo.getScheduleId())) {
                        schedulePureKeywordMap.put(searchKeyPo.getScheduleId(), new HashSet<>());
                    }
                    schedulePureKeywordMap.get(searchKeyPo.getScheduleId()).add(searchKeyPo.getKeyword());
                }
            }
            //检查关键词是否和已有排期冲突
            conflictSubIndex.set(0);
            keywordsSet.forEach(keyword -> {
                if (keywordScheduleMap.containsKey(keyword)) {
                    if (conflictSubIndex.get() == 0) {
                        conflictMsg.append(conflictIndex.incrementAndGet())
                                .append("、关键词与排期之间冲突<br>");
                    }
                    StringBuilder dst = new StringBuilder();
                    Set<Integer> mappedScheduleIds = keywordScheduleMap.get(keyword);
                    int mIndex = 1;
                    for (Integer mappedScheduleId : mappedScheduleIds) {
                        dst.append("排期【").append(mappedScheduleId).append("】");
                        Set<String> pureKeywords = schedulePureKeywordMap.getOrDefault(mappedScheduleId, new HashSet<>());
                        if (pureKeywords.contains(keyword)) {
                            //纯关键词冲突
                            dst.append("关键词");
                        }
                        Map<String, Set<Integer>> purePackages = scheduleKeywordPackageMap.getOrDefault(mappedScheduleId,
                                new HashMap<>());
                        if (purePackages.containsKey(keyword) && purePackages.get(keyword).size() > 0) {
                            //词包冲突
                            if (pureKeywords.contains(keyword)) {
                                dst.append("、");
                            }
                            dst.append("词包");
                            int pIndex = 1;
                            Set<Integer> pIdSet = purePackages.get(keyword);
                            for (Integer pId : pIdSet) {
                                dst.append("【").append(pId).append("，").append(kpMap.get(pId).getPackageName()).append("】");
                                if (pIndex++ < pIdSet.size()) {
                                    dst.append("、");
                                }
                            }
                        }
                        if (mIndex++ < mappedScheduleIds.size()) {
                            dst.append("，");
                        }
                    }
                    conflictMsg.append(String.format("（%d.%d）关键词【%s】与 %s冲突。<br>", conflictIndex.get(),
                            conflictSubIndex.incrementAndGet(), keyword, dst));
                }
            });

            //检查词包是否和已有排期冲突
            conflictSubIndex.set(0);
            kwPackages.forEach((packageId, kwPackage) -> {
                if (packageId2ScheduleMap.containsKey(packageId)) {
                    if (conflictSubIndex.get() == 0) {
                        conflictMsg.append(conflictIndex.incrementAndGet())
                                .append("、词包与排期之间冲突<br>");
                    }
                    Set<Integer> conflictScheduleIds = packageId2ScheduleMap.get(packageId);
                    StringBuilder pre = new StringBuilder("词包【")
                            .append(packageId)
                            .append("，")
                            .append(kwPackage.getPackageName())
                            .append("】与排期【");
                    int preIndex = 1;
                    for (Integer cScheduleId : conflictScheduleIds) {
                        pre.append(cScheduleId);
                        if (preIndex++ < conflictScheduleIds.size()) {
                            pre.append("、");
                        }
                    }
                    pre.append("】");
                    conflictMsg.append(String.format("（%d.%d）%s中的词包冲突。<br>", conflictIndex.get(),
                            conflictSubIndex.incrementAndGet(), pre));
                }
            });

            //检查词包关键词是否和已有排期冲突
            conflictSubIndex.set(0);
            keyword2PackageListMap.forEach((keyword, packageIdSet) -> {
                //排除掉纯词包的case
                packageIdSet = Sets.newHashSet(packageIdSet);
                packageIdSet.removeAll(packageId2ScheduleMap.keySet());
                if (CollectionUtils.isEmpty(packageIdSet)) {
                    return;
                }
                //at here:说明当前keyword所在的词包没有被历史排期使用过，但是可能会和历史排期中其他词包中的关键词冲突
                if (keywordScheduleMap.containsKey(keyword)) {
                    if (conflictSubIndex.get() == 0) {
                        conflictMsg.append(conflictIndex.incrementAndGet())
                                .append("、词包关键词与排期之间冲突<br>");
                    }
                    StringBuilder pre = new StringBuilder("关键词【")
                            .append(keyword)
                            .append("】（所属词包");
                    int preIndex = 1;
                    for (Integer pId : packageIdSet) {
                        pre.append("【").append(pId).append("，").append(kwPackages.get(pId).getPackageName()).append("】");
                        if (preIndex++ < packageIdSet.size()) {
                            pre.append("、");
                        }
                    }
                    pre.append("）");

                    StringBuilder dst = new StringBuilder();
                    Set<Integer> mappedScheduleIds = keywordScheduleMap.get(keyword);
                    int mIndex = 1;
                    for (Integer mappedScheduleId : mappedScheduleIds) {
                        dst.append("排期【").append(mappedScheduleId).append("】");
                        Set<String> pureKeywords = schedulePureKeywordMap.getOrDefault(mappedScheduleId, new HashSet<>());
                        if (pureKeywords.contains(keyword)) {
                            //纯关键词冲突
                            dst.append("关键词");
                        }
                        Map<String, Set<Integer>> purePackages = scheduleKeywordPackageMap.getOrDefault(mappedScheduleId,
                                new HashMap<>());
                        if (purePackages.containsKey(keyword) && purePackages.get(keyword).size() > 0) {
                            //词包冲突
                            if (pureKeywords.contains(keyword)) {
                                dst.append("，");
                            }
                            dst.append("词包");
                            int pIndex = 1;
                            Set<Integer> pIdSet = purePackages.get(keyword);
                            for (Integer pId : pIdSet) {
                                KeywordsPackageDto packageDto = kpMap.get(pId);
                                dst.append("【").append(pId).append("，").append(packageDto.getPackageName()).append("】");
                                if (pIndex++ < pIdSet.size()) {
                                    dst.append("、");
                                }
                            }
                        }
                        if (mIndex++ < mappedScheduleIds.size()) {
                            dst.append("，");
                        }
                    }
                    conflictMsg.append(String.format("（%d.%d）%s 与 %s冲突。<br>", conflictIndex.get(),
                            conflictSubIndex.incrementAndGet(), pre, dst));
                }
            });
        }

        if (conflictMsg.length() > 0) {
            //非二次确认抛出异常
            conflictMsg.append("存在以上重复项，是否继续投放？");
            throw new ConflictException(conflictMsg.toString());
        }

        return result;
    }

    private void createSchedule(SearchCptScheduleDto newScheduleBean, Operator operator) {
        Set<RLock> allLock = new HashSet<>();
        RLock buLock = redissonClient.getLock(newScheduleBean.getOrderDto().getBusinessSideId()
                + CptConstants.BU_SIDE_LOCK_SUFFIX);
        allLock.add(buLock);
        RLock orderLock = redissonClient.getLock(newScheduleBean.getOrderDto().getId() + CptConstants.ORDER_LOCK_SUFFIX);
        allLock.add(orderLock);
        newScheduleBean.getSourceAllInfoDtoMap().keySet().forEach(sourceId ->
                allLock.add(redissonClient.getLock(sourceId + CptConstants.SOURCE_LOCK_SUFFIX)));
        this.batchLock(allLock);
        try {
            createCptSchedule(newScheduleBean, operator);
        } catch (Exception e) {
            LOGGER.error("createSchedule.error", e);
            throw e;
        } finally {
            allLock.forEach(Lock::unlock);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void createCptSchedule(SearchCptScheduleDto newScheduleBean, Operator operator) {
        newScheduleBean.setAccountId(operator.getOperatorId());
        newScheduleBean.setContractAccountId(gdOrderService.getContractAccountId(newScheduleBean.getOrderDto()
                .getCrmContractId()));
        UpdateOrderDto updateCptOrderDto = this.buildCptOrderDto(newScheduleBean);
        if (newScheduleBean.getOrderDto().getCrmContractId() > 0) {
            this.tryUpdateCrm(updateCptOrderDto, operator);
        }

        List<NewBrandScheduleDto> newBrandScheduleDtos = this.batchInsert(newScheduleBean, operator);

        if (newScheduleBean.getOrderDto().getCrmContractId() > 0) {
            orderService.toBeAudit(newScheduleBean.getOrderDto().getId());
            this.updateCrm(updateCptOrderDto, operator);
        }
        gdOrderService.updateOrder(newScheduleBean.getOrderDto().getId(), updateCptOrderDto.getBeginTime(),
                updateCptOrderDto.getEndTime(), updateCptOrderDto.getAmount(), 0L);

        insertKeyWordsRelations(newBrandScheduleDtos.stream().map(NewBrandScheduleDto::getGdScheduleId)
                .collect(Collectors.toList()), newScheduleBean.getKeywordsPackages(), false);

        newBrandScheduleDtos.forEach(scheduleInfo -> scheduleGameService.saveGame(scheduleInfo.getGdScheduleId(), newScheduleBean.getGame()));

    }

    private void insertKeyWordsRelations(List<Integer> scheduleIds, List<KeywordsPackageDto> packageDtos, boolean isUpdate) {
        if (CollectionUtils.isEmpty(packageDtos)) {
            return;
        }
        //词包
        List<KeywordsPackageDto> purePackageDtos = packageDtos.stream()
                .filter(pd -> Utils.isPositive(pd.getPackageId()))
                .collect(Collectors.toList());
        //关键词
        KeywordsPackageDto pureKeywordDto = packageDtos.stream()
                .filter(pd -> !Utils.isPositive(pd.getPackageId()))
                .findFirst()
                .orElse(KeywordsPackageDto.builder().keywords(new ArrayList<>()).packageId(0).build());
        this.insertKeyWordPackages(scheduleIds, purePackageDtos, isUpdate);
        this.insertKeyWords(scheduleIds, pureKeywordDto.getKeywords().stream()
                .map(kw -> KeywordDto.builder().keyword(kw).packageId(0).build())
                .collect(Collectors.toList()), isUpdate);
    }

    private void insertKeyWordPackages(List<Integer> scheduleIds, List<KeywordsPackageDto> packageDtos, boolean isUpdate) {
        List<Integer> toApplyScheduleList = isUpdate ? new ArrayList<>() : scheduleIds;
        if (isUpdate) {
            SearchKeywordPoExample example = new SearchKeywordPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andScheduleIdIn(scheduleIds)
                    .andPackageIdGreaterThan(0);
            List<SearchKeywordPo> keywordPos = searchKeywordDao.selectByExample(example);
            Map<Integer, List<SearchKeywordPo>> schedulePackageMap = keywordPos.stream()
                    .collect(Collectors.groupingBy(SearchKeywordPo::getScheduleId));
            for (Integer scheduleId : scheduleIds) {
                Set<Integer> oldPackageIds = schedulePackageMap.getOrDefault(scheduleId, Lists.newArrayList())
                        .stream()
                        .map(SearchKeywordPo::getPackageId)
                        .collect(Collectors.toSet());
                boolean isChange = (packageDtos.size() != oldPackageIds.size());
                if (packageDtos.size() == oldPackageIds.size()) {
                    for (KeywordsPackageDto packageDto : packageDtos) {
                        if (!oldPackageIds.contains(packageDto.getPackageId())) {
                            isChange = true;
                            break;
                        }
                    }
                }
                if (isChange) {
                    toApplyScheduleList.add(scheduleId);
                    GdCreativePoExample example1 = new GdCreativePoExample();
                    example1.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andScheduleIdEqualTo(scheduleId)
                            .andStatusIn(CreativeStatus.NON_FINISH_STATUS_LIST);
                    List<GdCreativePo> creativePos = gdCreativeDao.selectByExample(example1);
                    if (!CollectionUtils.isEmpty(creativePos)) {
                        creativePos = creativePos.stream().peek(t -> {
                            t.setVersion(t.getVersion() + 1);
                            t.setAuditStatus(AuditStatus.INIT.getCode());
                        }).collect(Collectors.toList());
                        gdCreativeDao.insertUpdateBatch(creativePos);
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(toApplyScheduleList)) {
            return;
        }

        SearchKeywordPoExample example = new SearchKeywordPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andScheduleIdIn(toApplyScheduleList)
                .andPackageIdGreaterThan(0);

        //清理历史词包
        this.searchKeywordDao.updateByExampleSelective(SearchKeywordPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        if (!CollectionUtils.isEmpty(packageDtos)) {
            List<SearchKeywordPo> pos = new ArrayList<>();
            toApplyScheduleList.forEach(t -> packageDtos.forEach(x -> {
                SearchKeywordPo po = SearchKeywordPo.builder()
                        .scheduleId(t)
                        .keyword("")
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(new Timestamp(System.currentTimeMillis()))
                        .mtime(new Timestamp(System.currentTimeMillis()))
                        .packageId(x.getPackageId())
                        .build();
                pos.add(po);
            }));
            searchKeywordDao.insertUpdateBatch(pos);
        }
    }

    private void insertKeyWords(List<Integer> scheduleIds, List<KeywordDto> keyWords, boolean isUpdate) {
        List<Integer> toApplyScheduleList = isUpdate ? new ArrayList<>() : scheduleIds;
        if (isUpdate) {
            SearchKeywordPoExample example = new SearchKeywordPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andPackageIdEqualTo(0)
                    .andScheduleIdIn(scheduleIds);

            List<SearchKeywordPo> keywordPos = searchKeywordDao.selectByExample(example);
            Map<Integer, List<SearchKeywordPo>> scheduleKeywordMap = keywordPos.stream()
                    .collect(Collectors.groupingBy(SearchKeywordPo::getScheduleId));
            for (Integer scheduleId : scheduleIds) {
                Set<String> oldKeywords = scheduleKeywordMap.getOrDefault(scheduleId, Lists.newArrayList())
                        .stream()
                        .map(SearchKeywordPo::getKeyword)
                        .collect(Collectors.toSet());
                boolean isChange = (keyWords.size() != oldKeywords.size());
                if (keyWords.size() == oldKeywords.size()) {
                    for (KeywordDto kw : keyWords) {
                        if (!oldKeywords.contains(kw.getKeyword())) {
                            isChange = true;
                            break;
                        }
                    }
                }
                if (isChange) {
                    toApplyScheduleList.add(scheduleId);
                    GdCreativePoExample example1 = new GdCreativePoExample();
                    example1.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andScheduleIdEqualTo(scheduleId)
                            .andStatusIn(CreativeStatus.NON_FINISH_STATUS_LIST);
                    List<GdCreativePo> creativePos = gdCreativeDao.selectByExample(example1);
                    if (!CollectionUtils.isEmpty(creativePos)) {
                        creativePos = creativePos.stream().peek(t -> {
                            t.setVersion(t.getVersion() + 1);
                            t.setAuditStatus(AuditStatus.INIT.getCode());
                        }).collect(Collectors.toList());
                        gdCreativeDao.insertUpdateBatch(creativePos);
                    }
                }
            }
        }
        if (CollectionUtils.isEmpty(toApplyScheduleList)) {
            return;
        }
        SearchKeywordPoExample example = new SearchKeywordPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPackageIdEqualTo(0)
                .andScheduleIdIn(toApplyScheduleList);

        searchKeywordDao.updateByExampleSelective(SearchKeywordPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        if (!CollectionUtils.isEmpty(keyWords)) {
            List<SearchKeywordPo> pos = new ArrayList<>();
            Timestamp now = new Timestamp(System.currentTimeMillis());
            toApplyScheduleList.forEach(t -> keyWords.forEach(x -> {
                SearchKeywordPo po = SearchKeywordPo.builder()
                        .scheduleId(t)
                        .keyword(x.getKeyword())
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(now)
                        .mtime(now)
                        .packageId(0)
                        .build();
                pos.add(po);
            }));
            searchKeywordDao.insertUpdateBatch(pos);
        }
    }

    private void tryUpdateCrm(UpdateOrderDto updateCptOrderDto, Operator operator) {
        try {
            soaCrmOrderService.tryUpdateOrder(updateCptOrderDto, operator);
        } catch (Exception e) {
            LOGGER.error("soaCrmOrderService.tryUpdateCptOrder.error crmOrderId" + updateCptOrderDto.getId(), e);
            throw e;
        }
    }

    private void updateCrm(UpdateOrderDto updateCptOrderDto, Operator operator) {
        try {
            soaCrmOrderService.updateOrder(updateCptOrderDto, operator);
        } catch (Exception e) {
            LOGGER.error("soaCrmOrderService.updateCptOrder.error crmOrderId" + updateCptOrderDto.getId(), e);
            throw e;
        }
    }

    private UpdateOrderDto buildCptOrderDto(SearchCptScheduleDto newScheduleBean) {

        List<GdSchedulePo> cptSchedulePos = delegate.getGdSchedulePosByGdOrderId(newScheduleBean.getOrderDto().getId());
        if (newScheduleBean.getScheduleId() != null) {
            cptSchedulePos = cptSchedulePos.stream()
                    .filter(po -> !po.getScheduleId().equals(newScheduleBean.getScheduleId()))
                    .collect(Collectors.toList());
        }
        List<Timestamp> beginDates = cptSchedulePos.stream().map(GdSchedulePo::getBeginDate).collect(Collectors.toList());
        beginDates.add(newScheduleBean.getBeginTime());
        List<Timestamp> endDates = cptSchedulePos.stream().map(GdSchedulePo::getEndDate).collect(Collectors.toList());
        endDates.add(newScheduleBean.getEndTime());

        Timestamp beginDate = Collections.min(beginDates);
        Timestamp endDate = Collections.max(endDates);

        long base = cptSchedulePos.stream().mapToLong(GdSchedulePo::getExternalPrice).sum();

        PriceInfo priceInfo = calculatePrice(newScheduleBean);

        newScheduleBean.setInternalPrice(priceInfo.getInternalPrice());
        newScheduleBean.setExternalPrice(priceInfo.getExternalPrice());

        return UpdateOrderDto.builder()
                .id(newScheduleBean.getOrderDto().getCrmOrderId())
                .beginTime(beginDate)
                .endTime(Utils.getEndSecondOfDay(endDate))
                .amount(priceInfo.getExternalPrice() + base)
                .explanation(newScheduleBean.getOrderDto().getOrderName())
                .resourceType(newScheduleBean.getOrderDto().getResourceType())
                .build();
    }

    private int getPriceType(LocalDateTime beginTime, LocalDateTime endTime, int templateId) {

        PriceType priceType;

        if (configCenter.getSearchCptConfig().isSupportHourAndDayTemplate(templateId)) {

            if (beginTime.isEqual(beginTime.toLocalDate().atStartOfDay()) && endTime.isEqual(endTime.with(LocalTime.of(23, 59, 59)))) {
                priceType = PriceType.DAY;
            } else {
                priceType = PriceType.HOUR;
            }

        } else {

            //普通投放至少1天
            long dayGap = beginTime.until(endTime, ChronoUnit.DAYS) + 1;

            priceType = PriceType.WEEK;

            //是否是整月：正好是每月1号到每月最后一天
            boolean isWholeMonth = Objects.equals(beginTime.getMonth(), endTime.getMonth())
                    && Objects.equals(beginTime.getDayOfMonth(), 1)
                    && Objects.equals(endTime.getDayOfMonth(), endTime.toLocalDate().lengthOfMonth());

            //是否是满月：正好跨若干个月，比如5.13-6.12，5.13-7.12，1.31-2.28(29)，2.28(29)-3.31
            boolean isFullMonth = !Objects.equals(beginTime.getMonth(), endTime.getMonth())
                    && Objects.equals(beginTime.getDayOfMonth() - endTime.getDayOfMonth(), 1);

            if (beginTime.getMonthValue() == endTime.getMonthValue() && dayGap == beginTime.toLocalDate().lengthOfMonth()
                    || dayGap >= 28 && beginTime.getDayOfMonth() - endTime.getDayOfMonth() == 1) {

                priceType = PriceType.MONTH;

            }
        }
        return priceType.getCode();
    }

    private PriceInfo calculatePrice(SearchCptScheduleDto newScheduleBean) {

        LocalDateTime beginTime = newScheduleBean.getBeginTime().toLocalDateTime();
        LocalDateTime endTime = newScheduleBean.getEndTime().toLocalDateTime();

        long dayGap = beginTime.until(endTime, ChronoUnit.DAYS) + 1;

        if (dayGap > maxScheduleDays) {
            throw new IllegalArgumentException("搜索品专预约不能超过" + maxScheduleDays + "天");
        }

        int priceType = getPriceType(beginTime, endTime, newScheduleBean.getTemplateId());

        CptSourceAllInfoDto cptSourceAllInfoDto = getCptCycleInfo(newScheduleBean, priceType);
        newScheduleBean.setCycleId(cptSourceAllInfoDto.getCycleId());

        PriceInfo priceInfo = doCalculatePrice(newScheduleBean, cptSourceAllInfoDto, priceType, beginTime, endTime);

        log.info("搜索cpt计算价格结果为:{}", priceInfo);

        return priceInfo;
    }

    private PriceInfo doCalculatePrice(SearchCptScheduleDto searchCptScheduleDto,
                                       CptSourceAllInfoDto cptSourceAllInfoDto,
                                       int priceType,
                                       LocalDateTime beginTime,
                                       LocalDateTime endTime) {

        // CPM单价
        if (Objects.equals(searchCptScheduleDto.getBizSalesType(), BizSalesTypeEnum.CPM.getCode())) {
            return calculateCpmPrice(searchCptScheduleDto, cptSourceAllInfoDto);
        }

        BigDecimal count = calculateCount(priceType, beginTime, endTime);

        BigDecimal internalPrice = cptSourceAllInfoDto.getInternalPrice().multiply(count);
        BigDecimal externalPrice = cptSourceAllInfoDto.getExternalPrice().multiply(count);

        Pair<BigDecimal, BigDecimal> raise = handleRaise(cptSourceAllInfoDto.getRaise(), 100, internalPrice, externalPrice, searchCptScheduleDto);

        return buildPriceInfo(cptSourceAllInfoDto.getCycleId(), raise);
    }

    private PriceInfo calculateCpmPrice(SearchCptScheduleDto searchCptScheduleDto, CptSourceAllInfoDto cptSourceAllInfoDto) {
        BigDecimal cpmPrice = cptSourceAllInfoDto.getCpmPrice();
        BigDecimal count = new BigDecimal(searchCptScheduleDto.getTotalImpression());

        Assert.isTrue(!Objects.equals(cpmPrice, BigDecimal.ZERO), "CPM单价配置为空，请检查刊例配置！刊例周期ID：" + cptSourceAllInfoDto.getCycleId());

        log.info("SearchCptScheduleService doCalculatePrice，searchCptScheduleDto：{}，cptSourceAllInfoDto：{}，priceType：{}，beginTime：{}，endTime：{}",
                JSONObject.toJSONString(searchCptScheduleDto),
                JSONObject.toJSONString(cptSourceAllInfoDto),
                cptSourceAllInfoDto.getPriceType(),
                searchCptScheduleDto.getBeginTime(),
                searchCptScheduleDto.getEndTime());

        BigDecimal price = cpmPrice.multiply(count);
        Pair<BigDecimal, BigDecimal> raise = handleRaise(cptSourceAllInfoDto.getRaise(), 100, price, price, searchCptScheduleDto);

        return buildPriceInfo(cptSourceAllInfoDto.getCycleId(), raise);
    }

    private BigDecimal calculateCount(int priceType, LocalDateTime beginTime, LocalDateTime endTime) {
        switch (PriceType.getByCode(priceType)) {
            case HOUR:
                return new BigDecimal(endTime.getHour() - beginTime.getHour() + 1);
            case DAY:
                return new BigDecimal(beginTime.until(endTime, ChronoUnit.DAYS) + 1);
            case WEEK:
                long dayGap = beginTime.until(endTime, ChronoUnit.DAYS) + 1;
                return new BigDecimal((dayGap + 6) / 7); // Equivalent to ceiling(dayGap / 7)
            default:
                return BigDecimal.ONE;
        }
    }

    private PriceInfo buildPriceInfo(Integer cycleId, Pair<BigDecimal, BigDecimal> raise) {
        return PriceInfo.builder()
                .cycleId(cycleId)
                .internalPrice(raise.getFirst().longValue())
                .externalPrice(raise.getSecond().longValue())
                .build();
    }

    private Pair<BigDecimal, BigDecimal> handleRaise(PriceRaiseDto raise, int ratio, BigDecimal internalPrice, BigDecimal externalPrice, SearchCptScheduleDto searchCptScheduleDto) {
        if (WakeAppType.needRaise(searchCptScheduleDto.getWakeAppType())) {
            ratio += wakePriceRaiseHandler.handleRaise(searchCptScheduleDto.getOrderDto().getId(), raise.getWakeRaiseRatio());
            log.info("[SearchCptScheduleService] doCalculatePrice, wake_ratio={}", raise.getWakeRaiseRatio());
        }

        if (ScheduleLaunchSceneEnum.isSearchPro(searchCptScheduleDto.getLaunchScene())) {
            ratio += raise.getSearchProRaiseRatio();
            log.info("[SearchCptScheduleService] doCalculatePrice, search_pro_ratio={}", raise.getSearchProRaiseRatio());
        }

        internalPrice = internalPrice.multiply(new BigDecimal(ratio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        externalPrice = externalPrice.multiply(new BigDecimal(ratio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);

        return Pair.of(internalPrice, externalPrice);
    }

    private CptSourceAllInfoDto getCptCycleInfo(SearchCptScheduleDto newScheduleBean, int priceType) {
        return newScheduleBean.getCycleIds()
                .stream()
                .distinct()
                .map(item -> getPrice(newScheduleBean, item, priceType))
                .max(Comparator.comparingLong(item -> item.getExternalPrice().longValue()))
                .orElseThrow(() -> new IllegalArgumentException("查询刊例价出现错误"));
    }

    private CptSourceAllInfoDto getPrice(SearchCptScheduleDto newScheduleBean, Integer cycleId, Integer priceType) {
        AccountBaseDto baseDto = accountService.getAccountBaseDtoById(newScheduleBean.getContractAccountId());
        CptSourceQueryDto cptSourceQueryDto = CptSourceQueryDto.builder()
                .sourceIds(newScheduleBean.getSourceIds())
                .cycleId(cycleId)
                .priceType(priceType)
                .salesType(SalesType.SEARCH_CPT.getCode())
                .build();
        List<CptSourceAllInfoDto> cptSourceAllInfoDtos = cptSourceService
                .queryCptSourceList(cptSourceQueryDto);
        Assert.isTrue(!CollectionUtils.isEmpty(cptSourceAllInfoDtos), "请先配置刊例价再操作排期");

        Integer unitedFirstIndustryId = baseDto.getUnitedFirstIndustryId();
        List<CptSourceAllInfoDto> supportTemplate = cptSourceAllInfoDtos.stream()
                .filter(t -> newScheduleBean.getTemplateId().equals(t.getTemplateId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supportTemplate)) {
            List<CptSourceAllInfoDto> supportIndustry = supportTemplate.stream()
                    .filter(t -> Objects.equals(unitedFirstIndustryId, t.getUnitIndustryId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(supportIndustry)) {
                return supportIndustry.get(0);
            }
            return supportTemplate.get(0);
        }

        List<CptSourceAllInfoDto> supportCardType = cptSourceAllInfoDtos.stream()
                .filter(t -> newScheduleBean.getCardType().equals(t.getCardType()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supportCardType)) {
            List<CptSourceAllInfoDto> supportIndustry = supportCardType.stream()
                    .filter(t -> Objects.equals(unitedFirstIndustryId, t.getUnitIndustryId()))
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(supportIndustry)) {
                return supportIndustry.get(0);
            }
            return supportCardType.get(0);
        }

        List<CptSourceAllInfoDto> supportIndustry = cptSourceAllInfoDtos.stream()
                .filter(t -> Objects.equals(unitedFirstIndustryId, t.getUnitIndustryId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supportIndustry)) {
            return supportIndustry.get(0);
        }
        return cptSourceAllInfoDtos.get(0);
    }


    private List<NewBrandScheduleDto> batchInsert(SearchCptScheduleDto newScheduleBean, Operator operator) {
        List<NewBrandScheduleDto> newBrandScheduleDtos = new ArrayList<>();
        Map<Integer, SourceAllInfoDto> sourceAllInfoDtoMap = newScheduleBean.getSourceAllInfoDtoMap();
        newScheduleBean.getSourceIds().forEach(sourceId -> {
            SourceAllInfoDto sourceAllInfoDto = sourceAllInfoDtoMap.get(sourceId);
            GdSchedulePo gdSchedulePo = this.buildGdSchedulePo(sourceAllInfoDto, newScheduleBean, sourceId);
            gdScheduleDao.insertSelective(gdSchedulePo);
            updateGdScheduleDate(gdSchedulePo.getAccountId(), gdSchedulePo.getOrderId(),
                    gdSchedulePo.getScheduleId(), gdSchedulePo.getBeginDate(), gdSchedulePo.getEndDate());

            logService.insertLog(gdSchedulePo.getScheduleId(),
                    GdLogFlag.SCHEDULE,
                    LogOperateType.ADD_SCHEDULE,
                    operator,
                    ScheduleLogBean.builder()
                            .id(gdSchedulePo.getScheduleId())
                            .beginDate(TimeUtils.getTimestamp2String(gdSchedulePo.getBeginDate()))
                            .endDate(TimeUtils.getTimestamp2String(gdSchedulePo.getEndDate()))
                            .internalPrice(gdSchedulePo.getInternalPrice())
                            .externalPrice(gdSchedulePo.getExternalPrice())
                            .sourceId(gdSchedulePo.getSlotId())
                            .build());

            newBrandScheduleDtos.add(NewBrandScheduleDto.builder()
                    .gdScheduleId(gdSchedulePo.getScheduleId()).build());
        });
        return newBrandScheduleDtos;
    }

    public void updateGdScheduleDate(Integer accountId, Integer orderId, Integer scheduleId, Timestamp beginDate, Timestamp endDate) {
        GdScheduleDatePo delete = new GdScheduleDatePo();
        delete.setIsDeleted(IsDeleted.DELETED.getCode());
        GdScheduleDatePoExample example = new GdScheduleDatePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andScheduleIdEqualTo(scheduleId);
        gdScheduleDateDao.updateByExampleSelective(delete, example);

        List<Timestamp> days = Utils.getEachDays(beginDate, endDate);
        for (Timestamp day : days) {
            GdScheduleDatePo insert = new GdScheduleDatePo();
            insert.setAccountId(accountId);
            insert.setScheduleId(scheduleId);
            insert.setOrderId(orderId);
            insert.setScheduleDate(day);
            insert.setRotationNum(1);
            gdScheduleDateDao.insertSelective(insert);
        }
    }

    private GdSchedulePo buildGdSchedulePo(SourceAllInfoDto sourceAllInfoDto,
                                           SearchCptScheduleDto newScheduleBean, Integer sourceId) {
        GdSchedulePo gdSchedulePo = new GdSchedulePo();
        BeanUtils.copyProperties(newScheduleBean, gdSchedulePo);
        gdSchedulePo.setOrderProduct(newScheduleBean.getOrderDto().getProduct());
        gdSchedulePo.setPlatformId(sourceAllInfoDto.getPlatformId());
        gdSchedulePo.setPlatformName(sourceAllInfoDto.getPlatformName());
        gdSchedulePo.setPageId(sourceAllInfoDto.getPageId());
        gdSchedulePo.setPageName(sourceAllInfoDto.getPageName());
        gdSchedulePo.setResourceId(sourceAllInfoDto.getResourceId());
        gdSchedulePo.setResourceName(sourceAllInfoDto.getResourceName());
        gdSchedulePo.setSlotId(sourceId);
        gdSchedulePo.setSourceName(sourceAllInfoDto.getName());
        gdSchedulePo.setLevel(sourceAllInfoDto.getLevel());
        gdSchedulePo.setBeginDate(Utils.getBeginOfDay(newScheduleBean.getBeginTime()));
        gdSchedulePo.setEndDate(Utils.getBeginOfDay(newScheduleBean.getEndTime()));
        gdSchedulePo.setExternalPrice(newScheduleBean.getExternalPrice());
        gdSchedulePo.setInternalPrice(newScheduleBean.getInternalPrice());
        gdSchedulePo.setOrderId(newScheduleBean.getOrderDto().getId());
        gdSchedulePo.setBusinessSideId(newScheduleBean.getOrderDto().getBusinessSideId());
        gdSchedulePo.setStatus(com.bilibili.adp.common.enums.Status.VALID.getCode());
        gdSchedulePo.setSalesType(SalesType.SEARCH_CPT.getCode());
        gdSchedulePo.setTotalImpression(newScheduleBean.getTotalImpression());
        gdSchedulePo.setBizSalesType(newScheduleBean.getBizSalesType());
        gdSchedulePo.setAccountId(newScheduleBean.getOrderDto().getAccountId());
        gdSchedulePo.setPromotionPurposeType(Values.defaultIfNull(newScheduleBean.getPromotionPurposeType(),
                PromotionPurposeType.LANDING_PAGE.getCode()));

        gdSchedulePo.setGdBeginTime(newScheduleBean.getBeginTime());
        gdSchedulePo.setGdEndTime(getGdEndTime(newScheduleBean.getEndTime(), newScheduleBean.getTemplateId()));
        gdSchedulePo.setLaunchScene(newScheduleBean.getLaunchScene());

        return gdSchedulePo;
    }

    private Timestamp getGdEndTime(Timestamp endTime, Integer templateId) {
        //目前只有热搜模板支持分时，其余的都是到指定日期的结束时间点
        if (!configCenter.getSearchCptConfig().isSupportHourAndDayTemplate(templateId)) {
            LocalDateTime end = endTime.toLocalDateTime().with(LocalTime.of(23, 59, 59));
            return TimeUtil.toTimestamp(end);
        } else {
            return endTime;
        }
    }

    public void deleteCptScheduleById(Integer id, Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "operator can not be null");
        Assert.notNull(id, "排期id不能为空");
        GdSchedulePo schedulePo = cptScheduleDelegate.getGdScheduleById(id);
        Assert.notNull(schedulePo, "排期不存在");

        Set<RLock> allLock = new HashSet<>();
        RLock buLock = redissonClient.getLock(schedulePo.getBusinessSideId() + CptConstants.BU_SIDE_LOCK_SUFFIX);
        RLock scheduleLock = redissonClient.getLock(schedulePo.getScheduleId() + CptConstants.SCHEDULE_LOCK_SUFFIX);
        RLock orderLock = redissonClient.getLock(schedulePo.getOrderId() + CptConstants.ORDER_LOCK_SUFFIX);
        allLock.add(buLock);
        allLock.add(scheduleLock);
        allLock.add(orderLock);
        this.batchLock(allLock);

        try {
            this.deleteCptSchedule(schedulePo, operator);
        } finally {
            allLock.forEach(Lock::unlock);
        }
    }

    public void deleteCptSchedule(GdSchedulePo schedulePo, Operator operator) {

        deleteScheduleValidateHandler.checkWhenDelete(schedulePo.getScheduleId(), schedulePo.getBeginDate());

        CptOrderDto order = cptScheduleDelegate.getValidOrder(schedulePo.getOrderId(), operator);
        Assert.isTrue(order.getAccountId().equals(operator.getOperatorId()), "不能操作不属于自己的订单");
        Assert.isTrue(0L == queryCptCreativeService
                        .getCreativeCountByScheduleId(operator, schedulePo.getScheduleId()),
                "请先删除该排期下的创意");

        List<GdSchedulePo> cptSchedulePos = delegate.getGdSchedulePosByGdOrderId(schedulePo.getOrderId());
        List<GdSchedulePo> otherSchedulePos = cptSchedulePos.stream().filter(po -> !po.getScheduleId()
                .equals(schedulePo.getScheduleId())).collect(Collectors.toList());
        Timestamp minOtherScheduleDate = null;
        Timestamp maxOtherScheduleDate = null;
        long amount = 0;
        if (!CollectionUtils.isEmpty(otherSchedulePos)) {
            List<Timestamp> beginDates = otherSchedulePos.stream().map(GdSchedulePo::getBeginDate).collect(Collectors.toList());
            List<Timestamp> endDates = otherSchedulePos.stream().map(GdSchedulePo::getEndDate).collect(Collectors.toList());
            minOtherScheduleDate = Collections.min(beginDates);
            maxOtherScheduleDate = Collections.max(endDates);
            amount = otherSchedulePos.stream().mapToLong(GdSchedulePo::getExternalPrice).sum();
        }
        UpdateOrderDto updateCptOrderDto = UpdateOrderDto.builder()
                .id(order.getCrmOrderId())
                .resourceType(order.getResourceType())
                .explanation(order.getOrderName())
                .beginTime(minOtherScheduleDate)
                .endTime(null != maxOtherScheduleDate ? Utils.getEndSecondOfDay(maxOtherScheduleDate) : null)
                .amount(amount)
                .build();
        if (order.getGdOrderType().equals(GdOrderType.SALE.getCode())) {
            soaCrmOrderService.tryUpdateOrder(updateCptOrderDto, operator);
        }

        GdSchedulePo updateGdPo = GdSchedulePo.builder()
                .scheduleId(schedulePo.getScheduleId())
                .status(SwitchStatus.DELETE.getCode())
                .build();
        int result = cptScheduleDelegate.updateGdSchedule(updateGdPo);

        if (result > 0) {
            if (order.getCrmContractId() > 0) {
                if (null == updateCptOrderDto.getEndTime() || null == updateCptOrderDto.getBeginTime()) {
                    orderService.toBeEmpty(order.getId());
                } else {
                    orderService.toBeAudit(order.getId());
                }
                soaCrmOrderService.updateOrder(updateCptOrderDto, operator);
            }
            gdOrderService.updateOrder(order.getId(), updateCptOrderDto.getBeginTime(), updateCptOrderDto.getEndTime(),
                    updateCptOrderDto.getAmount(), 0L);

            SearchKeywordPoExample example = new SearchKeywordPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andScheduleIdEqualTo(schedulePo.getScheduleId());
            searchKeywordDao.updateByExampleSelective(SearchKeywordPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode()).build(), example);

            logService.insertLog(schedulePo.getScheduleId(),
                    GdLogFlag.SCHEDULE,
                    LogOperateType.DELETE_SCHEDULE,
                    operator,
                    ScheduleLogBean.builder()
                            .id(schedulePo.getScheduleId())
                            .build());
        }
    }

    @Override
    public void deleteCptScheduleByIds(List<Integer> ids, Operator operator) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        for (Integer scheduleId : ids) {
            deleteCptScheduleById(scheduleId, operator);
        }
    }

    @Override
    public List<Integer> queryScheduleUsingKeywordPackage(Long packageId) {
        if (!Utils.isPositive(packageId)) {
            return Lists.newArrayList();
        }
        SearchKeywordPoExample example = new SearchKeywordPoExample();
        example.createCriteria()
                .andPackageIdEqualTo(packageId.intValue())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SearchKeywordPo> searchKeywordPos = this.searchKeywordDao.selectByExample(example);
        if (CollectionUtils.isEmpty(searchKeywordPos)) {
            return Lists.newArrayList();
        }
        List<Integer> scheduleIds = searchKeywordPos.stream()
                .map(SearchKeywordPo::getScheduleId)
                .distinct()
                .collect(Collectors.toList());

        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.createCriteria()
                .andScheduleIdIn(scheduleIds)
                .andStatusEqualTo(ScheduleStatus.VALID.getCode())//正在进行的排期
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdSchedulePo> gdSchedulePos = this.gdScheduleDao.selectByExample(schedulePoExample);
        if (CollectionUtils.isEmpty(gdSchedulePos)) {
            return Lists.newArrayList();
        }
        return gdSchedulePos.stream().map(GdSchedulePo::getScheduleId).collect(Collectors.toList());
    }

    private void validate(SearchCptScheduleDto scheduleDto) {
        // 校验时间
        validateTime(scheduleDto.getBeginTime(), scheduleDto.getEndTime());
        // 校验关键词
        validateKeywords(scheduleDto);
        // 校验唤起类型
        validateWakeAppType(scheduleDto.getWakeAppType());
        // 校验投放场景
        validateLaunchScene(scheduleDto.getLaunchScene());
        // 校验游戏
        validateGame(scheduleDto.getPromotionPurposeType(), scheduleDto.getGame());
    }

    private void validateTime(Timestamp beginTime, Timestamp endTime) {
        Assert.isTrue(!endTime.before(beginTime), "结束时间必须大于等于开始时间");
    }

    private void validateKeywords(SearchCptScheduleDto scheduleDto) {
        boolean isSearchCptLinkedSsaTemplate = configCenter.getSearchCptConfig()
                .isSearchCptLinkedSsaTemplate(scheduleDto.getTemplateId());
        if (isSearchCptLinkedSsaTemplate) {
            List<String> keywords = scheduleDto.getKeywords();
            Assert.isTrue(!CollectionUtils.isEmpty(keywords) && keywords.size() == 1,
                    "当前模板支持的搜索词数量有且仅有1个");
            Assert.isTrue(keywords.get(0).length() <= 13, "当前模板支持的搜索词长度不能超过13个字符");
        }
    }

    private void validateWakeAppType(Integer wakeAppType) {
        if (Objects.nonNull(wakeAppType)) {
            Assert.isTrue(Objects.equals(WakeAppType.NO.getCode(), wakeAppType) ||
                            Objects.equals(WakeAppType.APP.getCode(), wakeAppType),
                    "搜索品专唤起选项仅支持「无」和「外部应用」");
        }
    }

    private void validateLaunchScene(Integer launchScene) {
        if (Objects.nonNull(launchScene)) {
            Assert.isTrue(ScheduleLaunchSceneEnum.supportSearchPro(launchScene),
                    "搜索品专投放场景仅支持「默认」和「品专pro」");
        }
    }

    private void validateUpdate(SearchCptUpdateScheduleDto updateScheduleDto) {
        validateWakeAppType(updateScheduleDto.getWakeAppType());
        this.validateGame(updateScheduleDto.getPromotionPurposeType(), updateScheduleDto.getGame());
    }

    private void validateGame(Integer ppt, GameDto game) {
        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004168063
        PromotionPurposeType ppType = PromotionPurposeType.getByCode(ppt);
        if (Objects.equals(ppType, PromotionPurposeType.ANDROID_GAME_DOWNLOAD)) {
            Assert.notNull(game, "【安卓游戏下载】必须选择一个游戏包");
            GameChannelEnum.getByCode(game.getChannelId());
            Assert.isTrue(Utils.isPositive(game.getGameBaseId()), "【安卓游戏下载】游戏id不能为空");
        }
    }


    @Data
    @Builder
    private static class PriceInfo {
        private int cycleId;

        private long internalPrice;

        private long externalPrice;
    }

}
