package com.bilibili.cpt.platform.biz.service.ogv;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.log.bean.OgvBrandContentLogBean;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.dto.SaveBrandOgvDto;
import com.bilibili.brand.biz.rpc.dto.VideoMaterialDetailDto;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.CrmProductGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.VideoGrpcClient;
import com.bilibili.brand.biz.utils.TimeUtils;
import com.bilibili.cpt.platform.api.log.service.ICptLogService;
import com.bilibili.cpt.platform.api.ogv.dto.OgvBrandContentDto;
import com.bilibili.cpt.platform.api.ogv.dto.QueryOgvBrandContentDto;
import com.bilibili.cpt.platform.api.ogv.dto.SaveOgvBrandContentDto;
import com.bilibili.cpt.platform.api.ogv.service.IOgvBrandContentService;
import com.bilibili.cpt.platform.biz.dao.OgvBrandContentDao;
import com.bilibili.cpt.platform.biz.po.OgvBrandContentPo;
import com.bilibili.cpt.platform.biz.po.OgvBrandContentPoExample;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.LogOperateType;
import com.bilibili.mas.common.utils.BeanHelper;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/29
 **/

@Service
@Slf4j
public class OgvBrandContentService implements IOgvBrandContentService {

    @Resource
    private OgvBrandContentDao ogvBrandContentDao;

    @Resource
    private VideoGrpcClient videoGrpcClient;

    @Resource
    private ICptLogService cptLogService;

    @Resource
    private CrmProductGrpcClient crmProductGrpcClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long save(Operator operator, SaveOgvBrandContentDto saveOgvBrandContentDto) {
        // 校验稿件
        preCheck(saveOgvBrandContentDto);
        OgvBrandContentPo po = toPo(saveOgvBrandContentDto);
        List<OgvBrandContentDto> contentDtos = existsStartTime(saveOgvBrandContentDto.getCid(), saveOgvBrandContentDto.getAdStartTime());
        ogvBrandContentDao.insertSelective(po);
        Long id = po.getId();
        OgvBrandContentLogBean logBean = OgvBrandContentLogBean.fromPo(po);
        cptLogService.insertLog(Math.toIntExact(id), GdLogFlag.OGV_BRAND_CONTENT, LogOperateType.CREATE_OGV_BRAND_CONTENT, operator, logBean);
        // 通知OGV服务端
        handleAddBrandOgv(operator, saveOgvBrandContentDto, contentDtos);
        return id;
    }

    private void preCheck(SaveOgvBrandContentDto saveOgvBrandContentDto) {
        Long avid = saveOgvBrandContentDto.getAvid();
        Long cid = saveOgvBrandContentDto.getCid();
        Long brandId = saveOgvBrandContentDto.getBrandId();
        String adStartTime = saveOgvBrandContentDto.getAdStartTime();
        String adEndTime = saveOgvBrandContentDto.getAdEndTime();
        Assert.hasText(adStartTime, "开始时间不能为空");
        Assert.hasText(adEndTime, "结束时间不能为空");
        Assert.isTrue(TimeUtils.timeStringToSeconds(adStartTime) <= TimeUtils.timeStringToSeconds(adEndTime), "开始时间必须小于等于结束时间");
        Assert.isTrue(Utils.isPositive(avid), "缺少稿件avid");
        Assert.isTrue(Utils.isPositive(cid), "缺少稿件cid");
        VideoMaterialDetailDto videoMaterialDetailDto = this.videoGrpcClient.getByCid(cid);
        Assert.isTrue(Objects.nonNull(videoMaterialDetailDto), "填写的cid不存在");
        Assert.isTrue(Objects.equals(videoMaterialDetailDto.getAid(), avid), "aid填写错误");
        Assert.notNull(brandId, "缺少品牌信息");
        String brandName = crmProductGrpcClient.getProductName(Math.toIntExact(brandId));
        saveOgvBrandContentDto.setBrandName(brandName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, Operator operator, SaveOgvBrandContentDto saveOgvBrandContentDto) {
        preCheck(saveOgvBrandContentDto);
        Assert.isTrue(Utils.isPositive(id), "缺少需要修改的id");
        OgvBrandContentPo existsPo = ogvBrandContentDao.selectByPrimaryKey(id);
        Assert.notNull(existsPo, "记录不存在");
        Assert.isTrue(Objects.equals(existsPo.getStatus(), 0), "已下线的记录不支持修改");
        OgvBrandContentPo po = toPo(saveOgvBrandContentDto);
        po.setId(id);
        List<OgvBrandContentDto> contentDtos = existsStartTime(saveOgvBrandContentDto.getCid(), saveOgvBrandContentDto.getAdStartTime());
        List<OgvBrandContentDto> existsDtos = existsStartTime(existsPo.getCid(), existsPo.getAdStartTime());
        ogvBrandContentDao.updateByPrimaryKeySelective(po);
        OgvBrandContentLogBean logBean = OgvBrandContentLogBean.fromPo(po);
        cptLogService.insertLog(Math.toIntExact(id), GdLogFlag.OGV_BRAND_CONTENT, LogOperateType.UPDATE_OGV_BRAND_CONTENT, operator, logBean);
        handleUpdateBrandOgv(operator, toDto(existsPo), saveOgvBrandContentDto, contentDtos, existsDtos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id, Operator operator) {
        Assert.isTrue(Utils.isPositive(id), "缺少需要删除的id");
        OgvBrandContentPo existsPo = ogvBrandContentDao.selectByPrimaryKey(id);
        Assert.notNull(existsPo, "记录不存在");
        existsPo.setIsDeleted(IsDeleted.DELETED.getCode());
        List<OgvBrandContentDto> existsDtos = existsStartTime(existsPo.getCid(), existsPo.getAdStartTime());
        ogvBrandContentDao.updateByPrimaryKeySelective(existsPo);
        OgvBrandContentLogBean logBean = OgvBrandContentLogBean.fromPo(existsPo);
        cptLogService.insertLog(Math.toIntExact(id), GdLogFlag.OGV_BRAND_CONTENT, LogOperateType.DELETE_OGV_BRAND_CONTENT, operator, logBean);
        handleDeleteBrandOgv(operator, toDto(existsPo), existsDtos);
    }

    @Override
    public PageResult<OgvBrandContentDto> queryPage(QueryOgvBrandContentDto queryOgvBrandContentDto) {
        OgvBrandContentPoExample example = new OgvBrandContentPoExample();
        OgvBrandContentPoExample.Criteria criteria = example.createCriteria();
        Long cid = queryOgvBrandContentDto.getCid();
        if (Utils.isPositive(cid)) {
            criteria.andCidEqualTo(cid);
        }
        ObjectUtils.setLikeString(queryOgvBrandContentDto::getBrandName, criteria::andBrandNameLike);
        QueryOgvBrandContentDto.PageInfo pageInfo = queryOgvBrandContentDto.getPageInfo();
        if (Objects.isNull(pageInfo)) {
            pageInfo = QueryOgvBrandContentDto.PageInfo.of(1, 20);
        }
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Page page = Page.valueOf(pageInfo.getPageNum(), pageInfo.getPageSize());
        long total = ogvBrandContentDao.countByExample(example);
        if (total <= 0) {
            return PageResult.emptyPageResult();
        }
        example.setLimit(page.getLimit());
        example.setOffset(page.getOffset());
        List<OgvBrandContentPo> contentPos = ogvBrandContentDao.selectByExample(example);
        return new PageResult<>((int) total, BeanHelper.copyForBeans(contentPos, OgvBrandContentDto::new));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sourceChange(Long newCid, Long originalCid, Long aid) {
        OgvBrandContentPoExample example = new OgvBrandContentPoExample();
        OgvBrandContentPoExample.Criteria criteria = example.createCriteria();
        criteria.andCidEqualTo(originalCid);
        criteria.andAvidEqualTo(aid);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<OgvBrandContentPo> contentPos = ogvBrandContentDao.selectByExample(example);
        // 换源
        if (CollectionUtils.isEmpty(contentPos)) {
            return ;
        }
        for (OgvBrandContentPo contentPo : contentPos) {
            // 状态 已下线
            contentPo.setStatus(1);
            contentPo.setMtime(null);
            ogvBrandContentDao.updateByPrimaryKeySelective(contentPo);
            OgvBrandContentLogBean logBean = OgvBrandContentLogBean.fromPo(contentPo);
            cptLogService.insertLog(Math.toIntExact(contentPo.getId()), GdLogFlag.OGV_BRAND_CONTENT, LogOperateType.OFFLINE_OGV_BRAND_CONTENT, Operator.SYSTEM, logBean);
            videoGrpcClient.deleteBrandOgv(contentPo.getCid(), TimeUtils.timeStringToSeconds(contentPo.getAdStartTime()), TimeUtils.timeStringToSeconds(contentPo.getAdEndTime()));
        }
    }

    @Override
    public List<OgvBrandContentDto> existsStartTime(Long cid, String adStartTime) {
        OgvBrandContentPoExample example = new OgvBrandContentPoExample();
        OgvBrandContentPoExample.Criteria criteria = example.createCriteria();
        criteria.andCidEqualTo(cid);
        criteria.andAdStartTimeEqualTo(adStartTime);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<OgvBrandContentPo> contentPos = ogvBrandContentDao.selectByExample(example);
        if (CollectionUtils.isEmpty(contentPos)) {
            return Lists.newArrayList();
        }
        return BeanHelper.copyForBeans(contentPos, OgvBrandContentDto::new);
    }

    @Override
    public void handleAddBrandOgv(Operator operator, SaveOgvBrandContentDto dto, List<OgvBrandContentDto> contentDtos) {
        // 根据 cid 和 ad_start_time 查询是否存在
        String adStartTime = dto.getAdStartTime();
        String adEndTime = dto.getAdEndTime();
        SaveBrandOgvDto.BrandInfo brandInfo = buildBrandInfo(adStartTime, adEndTime, dto.getBrandName());
        if (CollectionUtils.isEmpty(contentDtos)) {
            // 不存在
            videoGrpcClient.addBrandOgv(SaveBrandOgvDto.builder()
                    .featureType("BRAND_ADVERTISEMENT")
                    .cid(dto.getCid())
                    .startTime(TimeUtils.timeStringToSeconds(adStartTime))
                    .endTime(TimeUtils.timeStringToSeconds(adEndTime))
                    .brandInfo(Lists.newArrayList(brandInfo))
                    .operator(operator.getOperatorName())
                    .build());
            return ;
        }
        // 存在
        List<SaveBrandOgvDto.BrandInfo> brandInfos = contentDtos.stream().map(content -> buildBrandInfo(content.getAdStartTime(), content.getAdEndTime(), content.getBrandName())).collect(Collectors.toList());
        // 获取最大的结束时间 ms
        Long maxEndTime = brandInfos.stream().map(SaveBrandOgvDto.BrandInfo::getEndTime).map(Long::valueOf).max(Comparator.comparingLong(i -> i)).orElseThrow(() -> new IllegalArgumentException("参数有误"));
        maxEndTime /= 1000;
        brandInfos.add(brandInfo);
        // s
        long endTime = TimeUtils.timeStringToSeconds(adEndTime);
        // 更新end_time
        if (endTime > maxEndTime) {
            maxEndTime = endTime;
        }
        videoGrpcClient.updateBrandOgv(SaveBrandOgvDto.builder()
                .featureType("BRAND_ADVERTISEMENT")
                .cid(dto.getCid())
                .startTime(TimeUtils.timeStringToSeconds(adStartTime))
                .endTime(maxEndTime)
                .brandInfo(brandInfos)
                .originStartTime(TimeUtils.timeStringToSeconds(adStartTime))
                .originEndTime(maxEndTime)
                .operator(operator.getOperatorName())
                .build());
    }

    @Override
    public void handleUpdateBrandOgv(Operator operator, OgvBrandContentDto exists, SaveOgvBrandContentDto dto, List<OgvBrandContentDto> contentDtos, List<OgvBrandContentDto> existsDtos) {
        // 根据 cid 和 ad_start_time 查询是否存在
        String adStartTime = dto.getAdStartTime();
        String adEndTime = dto.getAdEndTime();
        // 所有的cid-ad_start_time品牌信息
        SaveBrandOgvDto.BrandInfo brandInfo = buildBrandInfo(adStartTime, adEndTime, dto.getBrandName());
        // ad_start_time 没有更改
        if (Objects.equals(adStartTime, exists.getAdStartTime())) {
            // 存在
            List<SaveBrandOgvDto.BrandInfo> brandInfos = contentDtos.stream()
                    .filter(d -> !Objects.equals(exists.getId(), d.getId()))
                    .map(content -> buildBrandInfo(content.getAdStartTime(), content.getAdEndTime(), content.getBrandName()))
                    .collect(Collectors.toList());
            // 获取最大的结束时间 s
            Long maxEndTime = TimeUtils.timeStringToSeconds(adEndTime);
            if (!CollectionUtils.isEmpty(brandInfos)) {
                // ms
                maxEndTime = brandInfos.stream().map(SaveBrandOgvDto.BrandInfo::getEndTime).map(Long::valueOf).max(Comparator.comparingLong(i -> i)).orElseThrow(() -> new IllegalArgumentException("参数有误"));
                // s
                maxEndTime /= 1000;
            }
            brandInfos.add(brandInfo);
            // s
            long endTime = TimeUtils.timeStringToSeconds(adEndTime);
            // 更新end_time
            if (endTime > maxEndTime) {
                maxEndTime = endTime;
            }
            videoGrpcClient.updateBrandOgv(SaveBrandOgvDto.builder()
                    .featureType("BRAND_ADVERTISEMENT")
                    .cid(dto.getCid())
                    .startTime(TimeUtils.timeStringToSeconds(adStartTime))
                    .endTime(maxEndTime)
                    .brandInfo(brandInfos)
                    .originStartTime(TimeUtils.timeStringToSeconds(adStartTime))
                    .originEndTime(maxEndTime)
                    .operator(operator.getOperatorName())
                    .build());
        } else {
            // ad_start_time 更改了 原来存在的需要删除
            // 删除存在的
            handleDeleteBrandOgv(operator, exists, existsDtos);
            // 新增修改的
            handleAddBrandOgv(operator, dto, contentDtos);
        }
    }

    @Override
    public void handleDeleteBrandOgv(Operator operator, OgvBrandContentDto exists, List<OgvBrandContentDto> existsContentDtos) {
        List<SaveBrandOgvDto.BrandInfo> existsBrandInfos = existsContentDtos.stream()
                .filter(d -> !Objects.equals(exists.getId(), d.getId()))
                .map(content -> buildBrandInfo(content.getAdStartTime(), content.getAdEndTime(), content.getBrandName()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existsBrandInfos)) {
            // 空已经存在的需要删除
            videoGrpcClient.deleteBrandOgv(exists.getCid(),
                    TimeUtils.timeStringToSeconds(exists.getAdStartTime()),
                    TimeUtils.timeStringToSeconds(exists.getAdEndTime()));
        } else {
            // 不空 需要更新
            long maxEndTime = existsBrandInfos.stream().map(SaveBrandOgvDto.BrandInfo::getEndTime).map(Long::valueOf).max(Comparator.comparingLong(i -> i)).orElseThrow(() -> new IllegalArgumentException("参数有误"));
            maxEndTime /= 1000;
            videoGrpcClient.updateBrandOgv(SaveBrandOgvDto.builder()
                    .featureType("BRAND_ADVERTISEMENT")
                    .cid(exists.getCid())
                    .startTime(TimeUtils.timeStringToSeconds(exists.getAdStartTime()))
                    .endTime(maxEndTime)
                    .brandInfo(existsBrandInfos)
                    .originStartTime(TimeUtils.timeStringToSeconds(exists.getAdEndTime()))
                    .originEndTime(maxEndTime)
                    .operator(operator.getOperatorName())
                    .build());
        }
    }

    private OgvBrandContentPo toPo(SaveOgvBrandContentDto ogvBrandContentDto) {
        OgvBrandContentPo po = new OgvBrandContentPo();
        BeanUtils.copyProperties(ogvBrandContentDto, po);
        return po;
    }

    private OgvBrandContentDto toDto(OgvBrandContentPo po) {
        OgvBrandContentDto dto = new OgvBrandContentDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private SaveBrandOgvDto.BrandInfo buildBrandInfo(String adStartTime, String adEndTime, String brandName) {
        String start = String.valueOf(TimeUtils.timeStringToSeconds(adStartTime) * 1000);
        String end = String.valueOf(TimeUtils.timeStringToSeconds(adEndTime) * 1000);
        return new SaveBrandOgvDto.BrandInfo(brandName,
                start,
                end,
                new SaveBrandOgvDto.ReportInfo(start, end));
    }
}
