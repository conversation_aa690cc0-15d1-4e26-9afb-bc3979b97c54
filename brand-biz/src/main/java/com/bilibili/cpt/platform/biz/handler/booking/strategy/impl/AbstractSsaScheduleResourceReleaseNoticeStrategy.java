package com.bilibili.cpt.platform.biz.handler.booking.strategy.impl;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.MailUtils;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.brand.api.account.dto.AccountDto;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.ssa.SsaScheduleReleaseMatchResultDto;
import com.bilibili.brand.biz.utils.BrandLittleAssistantUtil;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.dto.CptUserDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.business_side.service.ICptUserService;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.cpt.platform.biz.handler.booking.strategy.SsaScheduleResourceReleaseNoticeStrategy;
import com.bilibili.cpt.platform.util.IpUtil;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/6 15:31
 */
@Slf4j
public abstract class AbstractSsaScheduleResourceReleaseNoticeStrategy<T>
        implements SsaScheduleResourceReleaseNoticeStrategy {
    @Autowired
    private IMailService mailService;

    @Autowired
    protected IBusinessSideService businessSideService;

    @Autowired
    protected ICptUserService cptUserService;

    @Autowired
    protected IQueryAccountService queryAccountService;

    @Value("#{'${resource.booking.release.notice.default.recipient:wangyanjun02}'.split(',')}")
    private List<String> defaultRecipient;

    @Value("#{'${resource.booking.release.notice.recipient.count:20}'.split(',')}")
    private Integer recipientCount = 20;

    protected abstract boolean doMatch(ReleaseRequestBean releaseRequest);

    @Override
    public boolean match(ReleaseRequestBean releaseRequest) {
        return Objects.equals(releaseRequest.getResourceType(), ReleaseRequestBean.ResourceType.SSA) && doMatch(releaseRequest);
    }

    /**
     * 返回主题
     */
    protected abstract String getSubject(AccountDto account, List<SsaScheduleReleaseMatchResultDto> items);

    /**
     * 返回正文摘要
     */
    protected abstract String getSummary(AccountDto account, List<SsaScheduleReleaseMatchResultDto> items);

    /**
     * 返回正文数据
     */
    protected abstract List<T> getBody(AccountDto account, List<SsaScheduleReleaseMatchResultDto> items);

    @Override
    public void notify(List<SsaScheduleReleaseMatchResultDto> items, ReleaseRequestBean releaseRequest) {
        Map<Integer, List<SsaScheduleReleaseMatchResultDto>> accountItemMap = items.stream()
                .collect(Collectors.groupingBy(SsaScheduleReleaseMatchResultDto::getAccountId));
        //查询账号信息
        List<AccountDto> accounts = Lists.newArrayList();
        try {
            accounts = queryAccountService.getAccountDtosInAccountIds(Lists.newArrayList(accountItemMap.keySet()));
        } catch (Exception e) {
            log.error("[AbstractSsaScheduleResourceReleaseNoticeStrategy] query account error，request={}",
                    JSON.toJSONString(releaseRequest), e);
            BrandLittleAssistantUtil.sendWithMarkdown(
                    BrandLittleAssistantUtil.GroupKey.BRAND_DEVELOPER_GROUP_KEY,
                    String.format("【闪屏资源排期释放通知】查询账号失败，账号ID=%s，机器IP=%s",
                            Lists.newArrayList(accountItemMap.keySet()), IpUtil.getIp()));
            //即使报错了，也继续尝试发邮件，只是账号列为空而已
        }

        Map<Integer, AccountDto> accountMap = accounts.stream()
                .collect(Collectors.toMap(AccountDto::getAccountId, Function.identity(), OptionalUtil.override()));

        //根据账号查询对应的业务方账号，主要目的是为了查询关联的用户
        //accountId -> businessSide
        Map<Integer, BusinessSideBaseDto> accountBusinessMap = businessSideService.getBusinessSideByAccountId(
                Lists.newArrayList(accountItemMap.keySet()));
        //查询每个业务方关联的用户
        Map<Integer, List<CptUserDto>> busToUserMap = cptUserService.getBusinessSideUserMapInBusinessSideIds(
                accountBusinessMap.values()
                        .stream()
                        .map(BusinessSideBaseDto::getId)
                        .distinct()
                        .collect(Collectors.toList()));

        //发送通知
        for (Map.Entry<Integer, List<SsaScheduleReleaseMatchResultDto>> entry : accountItemMap.entrySet()) {
            BusinessSideBaseDto businessSide = accountBusinessMap.getOrDefault(entry.getKey(),
                    BusinessSideBaseDto.builder().id(0).name("").build());
            AccountDto account = accountMap.getOrDefault(entry.getKey(),
                    AccountDto.builder().accountId(0).username("").build());
            Integer businessSideId = businessSide.getId();
            List<SsaScheduleReleaseMatchResultDto> aItems = entry.getValue();
            List<CptUserDto> users = busToUserMap.get(businessSideId);
            List<String> recipient;
            if (Objects.isNull(businessSideId) || CollectionUtils.isEmpty(users)) {
                recipient = Lists.newArrayList(this.defaultRecipient);
            } else {
                recipient = users.stream().map(CptUserDto::getName).collect(Collectors.toList());
            }
            //for mock
//            recipient = Lists.newArrayList(this.defaultRecipient);

            String body = this.generateContent(account, aItems);
            if (StringUtils.isEmpty(body)) {
                log.warn("[AbstractSsaScheduleResourceReleaseNoticeStrategy] failed to get notice body,bookings={}",
                        JSON.toJSONString(aItems));
                continue;
            }

            String subject = this.getSubject(account, aItems);
            String summary = this.getSummary(account, aItems);

            //分批处理，否则太多会导致发送失败（但是mailService.send(mail)并没有报错）
            List<List<String>> recipients = Lists.partition(recipient, recipientCount);
            for (List<String> element : recipients) {
                MailMessage mail = new MailMessage();
                mail.setTos(element.stream().map(u -> u + "@bilibili.com").collect(Collectors.toList()));
                mail.setHasFile(false);
                mail.setSubject(subject);
                mail.setUseHtml(true);
                mail.setText(summary + "\n" + body);
                for (int i = 0; i < 5; i++) {
                    try {
                        mailService.send(mail);
                        break;
                    } catch (Exception e) {
                        log.error("[AbstractSsaScheduleResourceReleaseNoticeStrategy] send notice error，request={}",
                                JSON.toJSONString(releaseRequest), e);
                        List<Integer> scheduleIds = aItems.stream()
                                .map(SsaScheduleReleaseMatchResultDto::getScheduleId)
                                .collect(Collectors.toList());
                        BrandLittleAssistantUtil.sendWithMarkdown(
                                BrandLittleAssistantUtil.GroupKey.BRAND_DEVELOPER_GROUP_KEY,
                                String.format("【闪屏资源排期释放通知】发送闪屏资源排期释放邮件失败，排期ID=%s，机器IP=%s",
                                        scheduleIds, IpUtil.getIp()));
                        LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(500));
                    }
                }
            }
        }
    }

    private String generateContent(AccountDto account, List<SsaScheduleReleaseMatchResultDto> items) {
        List<T> body = this.getBody(account, items);
        if (CollectionUtils.isEmpty(body)) {
            return null;
        }
        try {
            return MailUtils.genHtmlTableString(body);
        } catch (IllegalAccessException e) {
            log.error("[AbstractSsaScheduleResourceReleaseNoticeStrategy] generateContent error", e);
        }
        return null;
    }

    protected String getSourceName(SsaScheduleReleaseMatchResultDto item) {
        if (OrderProduct.SSA_CODE_SET.contains(item.getOrderProduct())) {
            return "闪屏";
        }
        if (OrderProduct.TOPVIEW_CODE_SET.contains(item.getOrderProduct())) {
            return "TopView";
        }
        return "";
    }
}
