package com.bilibili.cpt.platform.biz.service.creative;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.CreativeImageType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.creative.service.ICreativeRelationService;
import com.bilibili.brand.api.ext.ILiveInfoService;
import com.bilibili.brand.api.launch.dto.CreativeIpVideoDto;
import com.bilibili.brand.api.live.dto.LiveRoomDto;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.LiveGrpcClient;
import com.bilibili.brand.api.creative.dto.CreativeExtHolderDto;
import com.bilibili.brand.dto.resource.GameDto;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.enums.*;
import com.bilibili.brand.api.creative.dto.GdCreativeShareDto;
import com.bilibili.brand.api.creative.service.IGdCreativeShareService;
import com.bilibili.brand.api.launch.dto.ImageHash;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.platform.IPlatformService;
import com.bilibili.brand.api.resource.platform.MgkVideoDto;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.creative.dao.*;
import com.bilibili.brand.biz.creative.po.*;
import com.bilibili.brand.biz.creative.po.querydsl.GdCreativeBackgroundPo;
import com.bilibili.brand.biz.creative.service.*;
import com.bilibili.brand.biz.log.bean.CreativeLogBean;
import com.bilibili.brand.biz.utils.CptConfigUtil;
import com.bilibili.cpt.platform.api.audit.dto.CptCreativeAuditDto;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.creative.dto.*;
import com.bilibili.cpt.platform.api.creative.service.IQueryCptCreativeService;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.api.order.dto.CptOrderDto;
import com.bilibili.cpt.platform.api.order.service.ICptOrderService;
import com.bilibili.cpt.platform.api.schedule.dto.CptScheduleDto;
import com.bilibili.cpt.platform.api.schedule.service.IQueryCptScheduleService;
import com.bilibili.cpt.platform.biz.bean.CreativeBaseBean;
import com.bilibili.cpt.platform.biz.enumerate.MaterialType;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.cpt.platform.biz.enumerate.BackgroundColorEnum;
import com.bilibili.cpt.platform.biz.helper.GdStatusConvertHelper;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.location.api.service.IBusMarkService;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.*;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SearchCptCreativeDelegate extends CptBaseService {

    @Value("${cpt.creative.is.splice.url:false}")
    private Boolean isSpliceUrl;

    @Value("${search.cpt.creative.need.query.aids:false}")
    private Boolean needQueryAids;

    @Value("${bfs.categoryname}")
    private String categoryName;

    @Value("${search.cpt.no.bg.bus.mark.id:78}")
    private Integer noBgMarkId;

    @Value("${search.cpt.bg.bus.mark.id:77}")
    private Integer bgMarkId;

    @Autowired
    private IBfsService bfsService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private SearchCptCreativeValidator creativeValidator;

    @Autowired
    private ICptOrderService cptOrderService;

    @Autowired
    private IQueryCptScheduleService queryCptScheduleService;

    @Autowired
    private ICptSourceService cptSourceService;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private ISoaLandingPageService soaLandingPageService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IQueryCptCreativeService queryCptCreativeService;

    @Autowired
    private GdCreativeDao gdCreativeDao;
    @Autowired
    private GdCreativeImageDao gdCreativeImageDao;
    @Autowired
    private GdCreativeDateDao gdCreativeDateDao;
    @Autowired
    private GdCreativeManuscriptInfoDao manuscriptInfoDao;

    @Autowired
    private GdCreativeCustomizeBrandInfoDao brandInfoDao;

    @Autowired
    private IGdLogService gdLogService;

    @Autowired
    private GdStatusConvertHelper gdStatusConvertHelper;

    @Autowired
    private IGdCreativeShareService gdCreativeShareService;

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private IWakeUpService wakeUpService;

    @Autowired
    private CptConfigUtil cptConfigUtil;

    @Autowired
    private IBusinessSideService businessSideService;

    @Autowired
    private ArchiveGrpcClient archiveGrpcClient;

    @Autowired
    private BrandCptCreativeDelegate delegate;

    @Autowired
    private GdCreativeBackgroundService backgroundService;

    @Autowired
    private LiveGrpcClient liveGrpcClient;

    @Autowired
    private GdCreativeService gdCreativeService;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private GdCreativeVideoDao gdCreativeVideoDao;
    @Autowired
    private IPlatformService platformService;

    @Autowired
    private CreativeButtonService creativeButtonService;

    @Value("${gd.feeds.video.creative.dynamicTime:5000}")
    private Integer dynamicTime;

    @Autowired
    private CreativeVideoService creativeVideoService;
    @Autowired
    private IBusMarkService busMarkService;
    @Autowired
    private CreativeExtService creativeExtService;
    @Autowired
    private ICreativeRelationService relationService;
    @Autowired
    private IQueryAccountService accountService;
    @Autowired
    private ILiveInfoService liveInfoService;


    private static final String SOURCE_PARAM = "cpc___SOURCEID_____CREATIVEID_____ADTYPE_____REQUESTID__";
//    private static final String BRAND_BUTTON_SOURCE_FROM_PARAM = "**********";
    // 右上角按钮 sourceFrom
    private static final String BRAND_BUTTON_SOURCE_FROM_PARAM = "**********";
//    private static final String OTHER_SOURCE_FROM_PARAM = "**********";
    // 搜索结果大图 sourceFrom
    private static final String BRAND_LAND_PAGE_FROM_PARAM = "**********";

    // http(s)://game.bilibili.com或者http(s)://**.biligame.com
    private static final String GAME_NEED_ADD_SOURCE_FROM_URL_REGEX = "https?:\\/\\/(game\\.bilibili\\.com|([a-zA-Z0-9_-]+\\.)?biligame\\.com)";

    public UrlHashDto uploadMaterial(Integer templateId, Integer materialType, BfsFile bfsFile) throws ServiceException {
        Assert.notNull(templateId, "模板ID不可为空");
        Assert.notNull(materialType, "物料类型不可为空");
        Assert.notNull(bfsFile, "文件信息不可为空");
        log.info("uploadAndGetMaterialInfo.param templateId-{}, materialType-{}", templateId, materialType);

        TemplateDto template = queryTemplateService.getTemplateById(templateId);
        MaterialType validMaterialType = MaterialType.getByCode(materialType);
        try {
            validMaterialType.validateMaterial(template, null, bfsFile);
            BfsUploadResult result = bfsService.upload(categoryName, bfsFile.getConvFile());
            return validMaterialType.getUrlHash(result, template, isSpliceUrl);
        } catch (IOException e) {
            log.error("Creative upload failed: {}", Throwables.getStackTraceAsString(e));
            throw new ServiceException("文件上传失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(Operator operator, Long brandCreativeId) {
        Assert.notNull(brandCreativeId, "创意ID不可为空");
        GdCreativePo creativePo = this.getGdCreativeById(brandCreativeId);
        Assert.notNull(creativePo, "该创意不存在");
        LaunchStatus launchStatus = LaunchStatus.getByCode(creativePo.getStatus());
        Assert.isTrue(launchStatus.validateToStatus(LaunchStatus.DELETE), "状态为" + launchStatus.getName() + "创意不可被删除");

        this.deleteGdCreative(creativePo);
        CreativeLogBean logBean = CreativeLogBean.builder()
                .id(creativePo.getCreativeId().toString())
                .creativeName(creativePo.getCreativeName())
                .statusDesc(CptCreativeStatus.DELETE.getDesc())
                .lastStatusDesc(CptCreativeStatus.getByCode(creativePo.getStatus()).getDesc())
                .build();
        gdLogService.insertLog(creativePo.getCreativeId(), GdLogFlag.CREATIVE, LogOperateType.DELETE_CREATIVE, operator, logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long create(Operator operator, SearchCptCreativeDto newCptCreativeDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不可为空");

        //校验排期
        CptScheduleDto scheduleDto = queryCptScheduleService.getGdScheduleById(newCptCreativeDto.getGdScheduleId(), operator);
        newCptCreativeDto.setLauncher(scheduleDto.getLauncher());

        //校验基本信息
        creativeValidator.validateNewCreativeBasicInfo(newCptCreativeDto, scheduleDto);

        //校验业务方
        BusinessSideBaseDto businessSide = this.getValidBusinessSide(scheduleDto.getBusinessSideId());
        //校验投放時間
        creativeValidator.validateCreativeLaunchTime(newCptCreativeDto, scheduleDto);

        //设置默认值
        setDefaultValueIfNecessary(scheduleDto, newCptCreativeDto);

        //校验订单
        CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(scheduleDto.getGdOrderId(), operator);

        Assert.isTrue(gdStatusConvertHelper.convertGdOrderStatusToCpt(GdOrderStatus
                .getByCode(cptOrderDto.getGdOrderStatus())).equals(CptOrderStatus.FORMAL), "待审核或已删除的订单下不可以新建创意");
        // 校验scheme
        creativeValidator.validateScheme(businessSide.getAccountId(), scheduleDto.getPlatformId(),
                newCptCreativeDto.getSchemeUrl(), cptOrderDto.getCrmContractId());
        //校验位次
        List<Integer> hasPrivilegeSourceIds = cptSourceService.getSourceIdsByBusinessSideId(scheduleDto.getBusinessSideId());
        Assert.isTrue(hasPrivilegeSourceIds.contains(scheduleDto.getSourceId()), "该业务方无权限在该位次投放");
        //校验模板
        Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapBySourceId(scheduleDto.getSourceId());
        Assert.isTrue(templateMap.containsKey(newCptCreativeDto.getTemplateId()), "位次不支持该模板");

        TemplateDto template = templateMap.get(newCptCreativeDto.getTemplateId());

        CreativeBaseBean creativeBaseBean = CreativeBaseBean.builder().build();
        BeanUtils.copyProperties(newCptCreativeDto, creativeBaseBean);
//        this.buildCreativeImageMd5(creativeBaseBean);
        creativeValidator.validate(creativeBaseBean, template);
        // 校验分享信息
        GdCreativeShareDto creativeShareDto = GdCreativeShareDto.builder()
                .state(newCptCreativeDto.getShareState())
                .title(newCptCreativeDto.getShareTitle())
                .subTitle(newCptCreativeDto.getShareSubTitle())
                .imageUrl(newCptCreativeDto.getShareImageUrl())
                .imageHash(newCptCreativeDto.getShareImageHash())
                .build();
        gdCreativeShareService.validateCreativeShareInfo(creativeShareDto);

        // 校验直播预约id
        List<Long> liveBookingIdList = Lists.newArrayList(newCptCreativeDto.getLiveBookingId());
        liveInfoService.validateLiveBookingIdList(liveBookingIdList);

        long brandCreativeId = snowflakeIdWorker.nextId();

        GdCreativePo record = this.buildGdCreativePo(creativeBaseBean, scheduleDto);
        record.setOriginTag(cptOrderDto.getOriginTag());
        record.setCreativeType(template.getCreativeType());
        record.setCreativeId(brandCreativeId);
        record.setCreatorName(operator.getBilibiliUserName());
        record.setUniversalApp((cptConfigUtil.getAppSchema(creativeBaseBean.getSchemeUrl())));

        //填充落地页
        MgkLandingPageBean mgkLandingPageBean = getLandingPageBean(newCptCreativeDto, scheduleDto);
        record.setPromotionPurposeContent(mgkLandingPageBean.getLaunchUrl());
        record.setMgkPageId(mgkLandingPageBean.getMgkPageId());
        record.setAdVersionControllId(mgkLandingPageBean.getAdVersionControllId());

        GdJumpType jumpType = GdJumpType.getByCode(record.getJumpType());
        record.setVideoId(CptCreativeValidator.VIDEO_TYPE.contains(jumpType) ?
                creativeValidator.transferVideoIdFromWeb(jumpType, record.getPromotionPurposeContent()) : 0);
        record.setVersion(1);
        record.setIsWithWakeUpBar(wakeUpService.hasWakeUpBar(record.getSchemeUrl()) ? 1 : 0);

        List<ImageDto> imageDtos = newCptCreativeDto.getImages();
        if (imageDtos != null) {
            for (ImageDto imageDto : imageDtos) {
                if (ImageStyleEnum.MAIN.getCode().equals(imageDto.getImageStyle())) {
                    record.setImageUrl(imageDto.getImageUrl());
                    //搜索cpt直播下获取直播间封面无hash
                    if (!scheduleDto.getPromotionPurposeType().equals(PromotionPurposeType.LIVE.getCode())) {
                        String md5 = this.getMd5FromHash(imageDto.getHash());
                        record.setImageMd5(md5);
                    }
                }
            }
        }

        //添加默认商业标
        fillDefaultBusMark(record, template, newCptCreativeDto);

        gdCreativeDao.insertSelective(record);

        this.saveButton(scheduleDto, brandCreativeId, 0L, CategoryEnum.THEME, newCptCreativeDto.getButtons());

        this.saveOrUpdateCreativeImage(imageDtos, brandCreativeId);

        //一定放在saveButton防止后，因为saveBrandInfo依赖按钮链接
        saveBrandInfo(newCptCreativeDto, brandCreativeId);

        //保存稿件信息
        saveManuscriptIfNecessary(
                newCptCreativeDto.getAids(),
                brandCreativeId,
                newCptCreativeDto.getOpenDanmuku(),
                newCptCreativeDto.getImages()
        );

        //保存本地视频
//        saveMgkVideoIfNecessary(brandCreativeId,
//                newCptCreativeDto.getMgkVideoInfoDto(), 0);
        saveOrUpdateCreativeVideo(newCptCreativeDto.getMgkVideos(), brandCreativeId);
        //保存背景颜色
        backgroundService.saveOrUpdateBackground(brandCreativeId, newCptCreativeDto.getBackGroundColor());

        this.saveOrUpdateCreativeDate(GdCreativeDatePo.builder()
                .accountId(record.getAccountId())
                .orderId(record.getOrderId())
                .scheduleId(record.getScheduleId())
                .creativeId(record.getCreativeId())
                .beginTime(record.getBeginTime())
                .endTime(TimeUtils.getLastMinAndSecOfTime(record.getEndTime()))
                .build());

        // 保存分享信息
        creativeShareDto.setCreativeId(record.getCreativeId());
        creativeShareDto.setSalesType(record.getSalesType());
        gdCreativeShareService.saveCreativeShare(creativeShareDto);

        //拓展信息
        this.saveCreativeExt(brandCreativeId, newCptCreativeDto, cptOrderDto, scheduleDto);
        //监控信息
        this.saveMonitor(brandCreativeId, newCptCreativeDto, scheduleDto, mgkLandingPageBean);

        CreativeLogBean logBean = CreativeLogBean.builder()
                .id(record.getCreativeId().toString())
                .build();
        BeanUtils.copyProperties(record, logBean);
        logBean.setJumpTypeDesc(GdJumpType.getByCode(record.getJumpType()).getDesc());
        logBean.setStatusDesc(CptCreativeStatus.getByCode(record.getStatus()).getDesc());
        if (!configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(record.getTemplateId())) {
            logBean.setCmMarkDesc(CptCmMark.getByCode(record.getCmMark()).getDesc());
        }
        logBean.setTemplateName(template.getTemplateName());
        logBean.setStartTime(TimeUtils.getTimestamp2HourString(record.getBeginTime()));
        logBean.setEndTime(TimeUtils.getTimestamp2HourString(record.getEndTime()));
        gdLogService.insertLog(record.getCreativeId(), GdLogFlag.CREATIVE, LogOperateType.ADD_CREATIVE, operator, logBean);

        return brandCreativeId;
    }

    private void setDefaultValueIfNecessary(CptScheduleDto scheduleDto, SearchCptCreativeDto newCptCreativeDto) {
        int jumpType = getJumpType(scheduleDto, newCptCreativeDto.getJumpType());
        newCptCreativeDto.setJumpType(jumpType);
    }

    private int getJumpType(CptScheduleDto scheduleDto, Integer originalJumpType) {
        if (scheduleDto.getSalesType().equals(SalesType.SEARCH_CPT.getCode())) {
            if (scheduleDto.getPromotionPurposeType().equals(PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode())) {
                return GdJumpType.VIDEO_MOBILE.getCode();
            }
            if (scheduleDto.getPromotionPurposeType().equals(PromotionPurposeType.LIVE.getCode())) {
                return GdJumpType.LINK.getCode();
            }
        }
        if (originalJumpType == null) {
            return GdJumpType.LINK.getCode();
        }
        return originalJumpType;
    }

    //搜索CPT使用的是新版广告标
    private void fillDefaultBusMark(GdCreativePo record, TemplateDto template, SearchCptCreativeDto newCptCreativeDto) {
        if (Objects.equals(newCptCreativeDto.getBusMarkId(), 9)) {
            //如果选择的无标，则无需修改
            return;
        }
        //from 搜索原生的需求，https://www.tapd.bilibili.co/********/prong/stories/view/11********002837729
        if (this.configCenter.getSearchCptConfig().isShowNormalAdMark(template.getTemplateId())) {
            //对应loc_business_mark表的id=1，广告标
            record.setBusMarkId(1);
            return;
        }
        //如果创意有背景色并且不是默认背景色，则带上含有背景色的标
        if (newCptCreativeDto.getBackGroundColor() != null) {
            BackGroundColorBo backGroundColor = newCptCreativeDto.getBackGroundColor();
            if (!Objects.equals(backGroundColor.getCode(), BackgroundColorEnum.SEARCH_CPT_DEFAULT_COLOR.getCode())) {
                record.setBusMarkId(bgMarkId);
            } else {
                record.setBusMarkId(noBgMarkId);
            }
            return;
        }
        if (configCenter.getSearchCptConfig().isHotSearchTemplate(newCptCreativeDto.getTemplateId())) {
            record.setBusMarkId(configCenter.getSearchCptConfig().getHotSearchMark());
            return;
        }

        if (template.getIsSupportExtImage()) {
            record.setBusMarkId(bgMarkId);
        } else {
            record.setBusMarkId(noBgMarkId);
        }

        /**
         * 这段代码后期可以删了，因为前端传的CmMark已经被映射到BusMarkId了
         */
        if (configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(template.getTemplateId())) {
            record.setBusMarkId(newCptCreativeDto.getCmMark());
        }

    }

    private void saveBrandInfo(SearchCptCreativeDto dto, Long creativeId) {
        if (dto.getLauncher() == 0) {
            return;
        }
        GdCreativeCustomizeBrandInfoPoExample example = new GdCreativeCustomizeBrandInfoPoExample();
        example.or().andCreativeIdEqualTo(creativeId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        brandInfoDao.updateByExampleSelective(GdCreativeCustomizeBrandInfoPo.builder().isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        if (StringUtils.isEmpty(dto.getBrandName())) {
            return;
        }
        GdCreativeCustomizeBrandInfoPo brandInfoPo = GdCreativeCustomizeBrandInfoPo.builder()
                .brandDescription(dto.getBrandDescription())
                .brandName(dto.getBrandName())
                .creativeId(creativeId)
                .face(dto.getFaceUrl())
                .faceMd5(dto.getFaceMd5())
                .build();
        if (!CollectionUtils.isEmpty(dto.getButtons())) {
            ButtonDto buttonDto = dto.getButtons().get(0);
            brandInfoPo.setJumpType(buttonDto.getJumpType());
            brandInfoPo.setJumpUrl(buttonDto.getJumpUrl());
        }

        brandInfoDao.insertSelective(brandInfoPo);
    }

    private void saveManuscriptIfNecessary(List<Long> aids, long creativeId, Integer openDanmuku, List<ImageDto> images) {

        if (CollectionUtils.isEmpty(aids)) {
            return;
        }

        Map<Long, ArchiveInfoBo> archiveDetailMap = needQueryAids ? this.queryManuscript(aids) : new HashMap<>();
        GdCreativeManuscriptInfoPoExample example = new GdCreativeManuscriptInfoPoExample();
        example.or().andCreativeIdEqualTo(creativeId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        manuscriptInfoDao.updateByExampleSelective(GdCreativeManuscriptInfoPo.builder().isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
        if (CollectionUtils.isEmpty(aids)) {
            return;
        }
        AtomicInteger seq = new AtomicInteger(0);
        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<GdCreativeManuscriptInfoPo> pos = aids.stream()
                .map(t -> {
                    ArchiveInfoBo archiveInfoBo = archiveDetailMap.get(t);
                    GdCreativeManuscriptInfoPo infoPo = GdCreativeManuscriptInfoPo.builder()
                            .aid(t)
                            .creativeId(creativeId)
                            .coverUrl(Optional.ofNullable(archiveInfoBo).map(ArchiveInfoBo::getCover).orElse(""))
                            .ctime(now)
                            .mtime(now)
                            .isDeleted(IsDeleted.VALID.getCode())
                            .title("")
                            .cid(0L)
                            .seq(seq.incrementAndGet())
                            .openDanmuku(openDanmuku == null ? 0 : openDanmuku)
                            .jumpType(GdJumpType.LINK.getCode())
                            .jumpUrl("")
                            .mid(0L)
                            .scene(ManuscriptLaunchSceneEnum.NORMAL.getCode())
                            .bizType(ManuscriptBizTypeEnum.NORMAL.getCode())
                            .secondPartitionId(Optional.ofNullable(archiveInfoBo).map(ArchiveInfoBo::getSecondPartitionId).orElse(0))
                            .build();

                    if (aids.size() == 1) {
                        //目前只有单稿件单封面
                        if (images != null
                                && images.size() == 1
                                && images.get(0).getImageStyle().equals(ImageStyleEnum.MANUSCRIPT_COVER.getCode())) {
                            infoPo.setCoverUrl(images.get(0).getCoverUrl());
                        }

                        //单稿件支持填写跳转链接
                        infoPo.setJumpType(GdJumpType.VIDEO_MOBILE.getCode());
                        infoPo.setJumpUrl(GdJumpType.VIDEO_MOBILE.getLaunchUrl(String.valueOf(aids.get(0))));
                    }
                    return infoPo;
                }).collect(Collectors.toList());
        manuscriptInfoDao.insertBatch(pos);
    }

    public Map<Long, ArchiveInfoBo> queryManuscript(List<Long> aids) {
        Map<Long, ArchiveInfoBo> archiveInfoMap = this.archiveGrpcClient.queryArchiveInfo(aids);
        if (archiveInfoMap.size() == aids.size()) {
            return archiveInfoMap;
        }
        String invalidAid = aids.stream()
                .filter(aid -> !archiveInfoMap.containsKey(aid))
                .map(Objects::toString)
                .collect(Collectors.joining("，"));
        throw new IllegalArgumentException("下述稿件不存在，稿件id=" + invalidAid);
    }

    private void saveOrUpdateCreativeImage(List<ImageDto> imageDtos, Long creativeId) {
        GdCreativeImagePoExample example = new GdCreativeImagePoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        gdCreativeImageDao.updateByExampleSelective(GdCreativeImagePo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        if (CollectionUtils.isEmpty(imageDtos)) {
            return;
        }

        Timestamp now = new Timestamp(System.currentTimeMillis());
        int size = imageDtos.size();
        for (int i = 0; i < size; i++) {
            ImageDto imageDto = imageDtos.get(i);
            String md5 = this.getMd5FromHash(imageDto.getHash());
            GdCreativeImagePo imagePo = GdCreativeImagePo.builder()
                    .creativeId(creativeId)
                    .schemeUrl(StringUtils.isEmpty(imageDto.getSchemeUrl()) ? "" : imageDto.getSchemeUrl())
                    .jumpUrl(StringUtils.isEmpty(imageDto.getJumpUrl()) ? "" : imageDto.getJumpUrl())
                    .jumpType(StringUtils.isEmpty(imageDto.getJumpType()) ? 0 : imageDto.getJumpType())
                    .imageMd5(md5) // different with gif
                    .imageUrl(imageDto.getImageUrl()) // different with gif
                    .seq(i)
                    .type(CreativeImageType.DUAL_ROW.getCode())
                    .gifImageUrl("") // different with gif
                    .gifImageUrlHash("")  // different with gif
                    .imageStyle(imageDto.getImageStyle())
                    .ctime(now)
                    .mtime(now)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .title(imageDto.getTitle())
                    .description(imageDto.getDescription())
                    .build();
            if (imageDto.getIsGif() != null && imageDto.getIsGif()) {
                String coverMd5 = this.getMd5FromHash(imageDto.getCoverHash());
                imagePo.setImageMd5(coverMd5);
                imagePo.setImageUrl(imageDto.getCoverUrl());
                imagePo.setGifImageUrl(imageDto.getImageUrl());
                imagePo.setGifImageUrlHash(md5);
            }
            //需要主键，因此单条插入
            gdCreativeImageDao.insertSelective(imagePo);
            this.creativeButtonService.saveOrUpdateButtonCopy(creativeId, imagePo.getId().longValue(), CategoryEnum.IMAGE,
                    imageDto.getButtons());
        }
    }

    private void saveOrUpdateCreativeVideo(List<MgkVideoInfoDto> mgkVideoInfoDtos, Long creativeId) {
        GdCreativeVideoPoExample example = new GdCreativeVideoPoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        gdCreativeVideoDao.updateByExampleSelective(GdCreativeVideoPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        if (CollectionUtils.isEmpty(mgkVideoInfoDtos)) {
            return;
        }

        int size = mgkVideoInfoDtos.size();
        for (int i = 0; i < size; i++) {
            MgkVideoInfoDto videoInfoDto = mgkVideoInfoDtos.get(i);
            MgkVideoDto mgkVideoDto = platformService.getMgkVideoDto(videoInfoDto.getMgkVideoId());
            GdCreativeVideoPo creativeVideoPo = new GdCreativeVideoPo();
            BeanUtils.copyProperties(mgkVideoDto, creativeVideoPo);
            creativeVideoPo.setId(null);
            creativeVideoPo.setSeq(i);
            creativeVideoPo.setCover(org.apache.commons.lang3.StringUtils.isBlank(mgkVideoDto.getCover()) ?
                    mgkVideoDto.getCover() : videoInfoDto.getMgkVideoCover());
            creativeVideoPo.setCreativeId(creativeId);
            creativeVideoPo.setMgkVideoId(mgkVideoDto.getId());
            creativeVideoPo.setSource("vupload");
            creativeVideoPo.setDynamicTime(dynamicTime);
            creativeVideoPo.setJumpType(videoInfoDto.getJumpType() == null ?
                    GdJumpType.LINK.getCode() : videoInfoDto.getJumpType());
            creativeVideoPo.setJumpUrl(videoInfoDto.getJumpType() == null ? "" :
                    GdJumpType.getByCode(videoInfoDto.getJumpType()).getLaunchUrl(videoInfoDto.getJumpUrl()));
            creativeVideoPo.setEggStartTime(0L);
            creativeVideoPo.setEggEndTime(0L);
            creativeVideoPo.setVideoType(VideoType.MGK_VIDEO.getCode());
            creativeVideoPo.setTitle(videoInfoDto.getTitle());
            creativeVideoPo.setDescription(videoInfoDto.getDescription());

            //需要主键，因此单条插入
            gdCreativeVideoDao.insertSelective(creativeVideoPo);
            this.creativeButtonService.saveOrUpdateButtonCopy(creativeId, creativeVideoPo.getId().longValue(),
                    CategoryEnum.VIDEO_LIST, videoInfoDto.getButtons());
        }
    }

    private void saveOrUpdateCreativeDate(GdCreativeDatePo record) {

        GdCreativeDatePoExample example = new GdCreativeDatePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(record.getCreativeId());

        List<GdCreativeDatePo> gdCreativeDates = gdCreativeDateDao.selectByExample(example);
        if (CollectionUtils.isEmpty(gdCreativeDates)) {
            gdCreativeDateDao.insertSelective(record);
        } else {
            GdCreativeDatePo oldPo = gdCreativeDates.get(0);
            if (!oldPo.getBeginTime().equals(record.getBeginTime()) || !oldPo.getEndTime().equals(record.getEndTime())) {
                gdCreativeDateDao.updateByExampleSelective(record, example);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(Operator operator, SearchCptCreativeDto updateCptCreativeDto) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人员不可为空");

        SearchCptCreativeDto creativeDto = this.getByCreativeId(updateCptCreativeDto.getGdCreativeId(), operator);
        //校验排期
        CptScheduleDto scheduleDto = queryCptScheduleService.getGdScheduleById(creativeDto.getGdScheduleId(), operator);
        updateCptCreativeDto.setLauncher(scheduleDto.getLauncher());

        //校验基本信息
        creativeValidator.validateUpdateCreativeBasicInfo(updateCptCreativeDto, scheduleDto);

        LaunchStatus creativeStatus = LaunchStatus.getByCode(creativeDto.getStatus());
        Assert.notNull(creativeStatus, "不合法的创意状态");
        Assert.isTrue(LaunchStatus.START.equals(creativeStatus) || LaunchStatus.STOP.equals(creativeStatus), "状态为" + creativeStatus.getName() + "的创意不可再编辑");

        Assert.isTrue(TimeUtils.getHourTimestamp(Utils.getNow()).compareTo(creativeDto.getEndTime()) < 0, "已结束的投放不可再编辑");


        //校验业务方
        this.validateValidBusinessSide(scheduleDto.getBusinessSideId());

        //设置默认值
        setDefaultValueIfNecessary(scheduleDto, updateCptCreativeDto);

        //校验订单
        CptOrderDto cptOrderDto = cptOrderService.getOrderDtoByGdOrderId(scheduleDto.getGdOrderId(), operator);
        GdOrderStatus orderStatus = GdOrderStatus.getByCode(cptOrderDto.getGdOrderStatus());
        Assert.isTrue(!GdOrderStatus.DELETED.equals(orderStatus), "已删除的订单下不可以新建创意");
        // 校验scheme
        creativeValidator.validateScheme(scheduleDto.getAccountId(), scheduleDto.getPlatformId(),
                updateCptCreativeDto.getSchemeUrl(), cptOrderDto.getCrmContractId());
        //校验时间
        GdCreativePo creativePo = delegate.getGdCreativeById(updateCptCreativeDto.getGdCreativeId());
        creativeValidator.validateTime(updateCptCreativeDto, creativePo, scheduleDto, orderStatus);
        //校验位次
        List<Integer> hasPrivilegeSourceIds = cptSourceService.getSourceIdsByBusinessSideId(scheduleDto.getBusinessSideId());
        Assert.isTrue(hasPrivilegeSourceIds.contains(scheduleDto.getSourceId()), "该业务方已无权限在该位次投放");
        //校验模板
        Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapBySourceId(scheduleDto.getSourceId());
        Assert.isTrue(templateMap.containsKey(creativeDto.getTemplateId()), "位次已不支持该模板");
        TemplateDto template = templateMap.get(creativeDto.getTemplateId());

        CreativeBaseBean creativeBaseBean = CreativeBaseBean.builder().build();
        BeanUtils.copyProperties(updateCptCreativeDto, creativeBaseBean);
//        creativeBaseBean.setVersion(creativeDto.getVersion());
        creativeBaseBean.setAccountId(operator.getOperatorId());
        creativeBaseBean.setCreativeId(updateCptCreativeDto.getGdCreativeId());
        creativeValidator.validate(creativeBaseBean, template);
        // 校验分享信息
        GdCreativeShareDto creativeShareDto = GdCreativeShareDto.builder()
                .state(updateCptCreativeDto.getShareState())
                .title(updateCptCreativeDto.getShareTitle())
                .subTitle(updateCptCreativeDto.getShareSubTitle())
                .imageUrl(updateCptCreativeDto.getShareImageUrl())
                .imageHash(updateCptCreativeDto.getShareImageHash())
                .creativeId(creativeDto.getGdCreativeId())
                .salesType(creativeDto.getSalesType())
                .build();
        gdCreativeShareService.validateCreativeShareInfo(creativeShareDto);

        // 校验直播预约id
        List<Long> liveBookingIdList = Lists.newArrayList(updateCptCreativeDto.getLiveBookingId());
        liveInfoService.validateLiveBookingIdList(liveBookingIdList);

        MgkLandingPageBean mgkLandingPageBean = getLandingPageBean(updateCptCreativeDto, scheduleDto);

        creativePo.setJumpType(updateCptCreativeDto.getJumpType());
        creativePo.setPromotionPurposeContent(mgkLandingPageBean.getLaunchUrl());
        creativePo.setMgkPageId(mgkLandingPageBean.getMgkPageId());
        creativePo.setAdVersionControllId(mgkLandingPageBean.getAdVersionControllId());
        creativePo.setCreativeName(updateCptCreativeDto.getCreativeName());
        creativePo.setSchemeUrl(updateCptCreativeDto.getSchemeUrl());
        creativePo.setCustomizedImpUrl(updateCptCreativeDto.getCustomizedImpUrl());
        creativePo.setCustomizedClickUrl(updateCptCreativeDto.getCustomizedClickUrl());
        creativePo.setTitle(updateCptCreativeDto.getTitle());
        creativePo.setDescription(updateCptCreativeDto.getDescription());
        creativePo.setAdditionalCardType(updateCptCreativeDto.getAdditionalCardType());
        creativePo.setLiveBookingId(updateCptCreativeDto.getLiveBookingId() != null ? updateCptCreativeDto.getLiveBookingId() : 0L);
        fillDefaultBusMark(creativePo, template, updateCptCreativeDto);
//        if (configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(template.getTemplateId())) {
//            creativePo.setBusMarkId(updateCptCreativeDto.getCmMark());
//        }

        boolean isModified = this.isAlertAuditStatus(updateCptCreativeDto, creativeDto);
        creativePo.setAuditStatus(isModified ? AuditStatus.INIT.getCode() : creativePo.getAuditStatus());
        creativePo.setVersion(creativePo.getVersion() + 1);
        creativePo.setBeginTime(updateCptCreativeDto.getBeginTime());
        creativePo.setEndTime(TimeUtils.getLastMinAndSecOfTime(updateCptCreativeDto.getEndTime()));
        List<ImageDto> imageDtos = updateCptCreativeDto.getImages();
        if (imageDtos != null) {
            for (ImageDto imageDto : imageDtos) {
                if (ImageStyleEnum.MAIN.getCode().equals(imageDto.getImageStyle())) {
                    String md5 = this.getMd5FromHash(imageDto.getHash());
                    creativePo.setImageUrl(imageDto.getImageUrl());
                    creativePo.setImageMd5(md5);
                }
            }
        }
        //bug fix：推广目的是稿件时，编辑时PromotionPurposeContent是null，因为详情压根没返回
        //参考：com.bilibili.cpt.platform.biz.service.creative.QueryCptCreativeService.getPromotionPurposeContent
        if (StringUtils.hasText(creativePo.getPromotionPurposeContent())) {
            GdJumpType jumpType = GdJumpType.getByCode(updateCptCreativeDto.getJumpType());
            creativePo.setVideoId(CptCreativeValidator.VIDEO_TYPE.contains(jumpType) ?
                    creativeValidator.transferVideoIdFromWeb(jumpType, creativePo.getPromotionPurposeContent()) : 0);
        }

        gdCreativeDao.updateByPrimaryKeySelective(creativePo);

        this.saveOrUpdateCreativeDate(GdCreativeDatePo.builder()
                .accountId(creativePo.getAccountId())
                .orderId(creativePo.getOrderId())
                .scheduleId(creativePo.getScheduleId())
                .creativeId(creativePo.getCreativeId())
                .beginTime(updateCptCreativeDto.getBeginTime())
                .endTime(TimeUtils.getLastMinAndSecOfTime(updateCptCreativeDto.getEndTime()))
                .build());

        long brandCreativeId = updateCptCreativeDto.getGdCreativeId();
        this.saveButton(scheduleDto, brandCreativeId, 0L,
                CategoryEnum.THEME, updateCptCreativeDto.getButtons());

        this.saveOrUpdateCreativeImage(updateCptCreativeDto.getImages(), brandCreativeId);

        //一定放在saveButton防止后，因为saveBrandInfo依赖按钮链接
        saveBrandInfo(updateCptCreativeDto, brandCreativeId);

        //保存稿件信息
        saveManuscriptIfNecessary(
                updateCptCreativeDto.getAids(),
                brandCreativeId,
                updateCptCreativeDto.getOpenDanmuku(),
                updateCptCreativeDto.getImages()
        );
        //保存本地视频
//        saveMgkVideoIfNecessary(brandCreativeId,
//                updateCptCreativeDto.getMgkVideoInfoDto(), 0);

        saveOrUpdateCreativeVideo(updateCptCreativeDto.getMgkVideos(), brandCreativeId);

        //保存背景颜色
        backgroundService.saveOrUpdateBackground(brandCreativeId, updateCptCreativeDto.getBackGroundColor());

        // 更新分享信息
        gdCreativeShareService.saveCreativeShare(creativeShareDto);

        //拓展信息
        this.saveCreativeExt(brandCreativeId, updateCptCreativeDto, cptOrderDto, scheduleDto);

        //监控信息
        this.saveMonitor(brandCreativeId, updateCptCreativeDto, scheduleDto, mgkLandingPageBean);

        CreativeLogBean logBean = CreativeLogBean.builder().build();
        BeanUtils.copyProperties(updateCptCreativeDto, logBean);
        gdLogService.insertLog(updateCptCreativeDto.getGdCreativeId(), GdLogFlag.CREATIVE, LogOperateType.UPDATE_CREATIVE, operator, logBean);
    }

    private void saveMgkVideoIfNecessary(long creativeId,
                                         MgkVideoInfoDto mgkVideoInfoDto,
                                         Integer ipVideoId) {
        if (mgkVideoInfoDto != null) {
//            gdCreativeService.updateCreativeVideo(creativeId, mgkVideoInfoDto, ipVideoId, 0, 0L, 0L);
            this.gdCreativeService.updateGdCreativeVideo(creativeId, CreativeIpVideoDto.builder()
                    .videoId(ipVideoId)
                    .build(), mgkVideoInfoDto, null);
        }
    }

    private boolean isAlertAuditStatus(SearchCptCreativeDto newCreative, SearchCptCreativeDto oldCreative) {
        boolean isModified = (oldCreative.getPromotionPurposeContent() != null && !oldCreative.getPromotionPurposeContent()
                .equals(newCreative.getPromotionPurposeContent()))
                || (oldCreative.getCustomizedClickUrl() != null && !oldCreative.getCustomizedClickUrl().equals(newCreative.getCustomizedClickUrl()))
                || (oldCreative.getBrandName() != null && !oldCreative.getBrandName().equals(newCreative.getBrandName()))
                || (oldCreative.getBrandDescription() != null && !oldCreative.getBrandDescription().equals(newCreative.getBrandDescription()))
                || (oldCreative.getCmMark() != null && !oldCreative.getCmMark().equals(newCreative.getCmMark()))
                || (oldCreative.getCustomizedImpUrl() != null && !oldCreative.getCustomizedImpUrl().equals(newCreative.getCustomizedImpUrl()));
        if (isModified) {
            return true;
        }
        List<Long> newAids = newCreative.getAids();
        List<Long> oldAids = newCreative.getAids();
        if (newAids == null && oldAids != null) {
            return true;
        }
        if (oldAids == null && newAids != null) {
            return true;
        }
        if (newAids != null && newAids.size() != oldAids.size()) {
            return true;
        }
        AtomicBoolean isModifiedAid = new AtomicBoolean(false);
        if (newAids != null) {
            newAids.forEach(t -> {
                if (!oldAids.contains(t)) {
                    isModifiedAid.set(true);
                }
            });
            if (isModifiedAid.get()) {
                return true;
            }
        }


        List<ImageDto> newImages = newCreative.getImages();
        List<ImageDto> oldImages = oldCreative.getImages();
        if (newImages == null && oldImages != null) {
            return true;
        }
        if (oldImages == null && newImages != null) {
            return true;
        }
        if (newImages != null && newImages.size() != oldImages.size()) {
            return true;
        }
        if (newImages != null) {
            Map<Integer, List<ImageDto>> newImageMap = newImages.stream()
                    .collect(Collectors.groupingBy(ImageDto::getImageStyle));
            Map<Integer, List<ImageDto>> oldImageMap = oldImages.stream()
                    .collect(Collectors.groupingBy(ImageDto::getImageStyle));
            AtomicBoolean isModifiedImageAto = new AtomicBoolean(false);
            newImageMap.forEach((k, v) -> {
                List<ImageDto> olds = oldImageMap.get(k);
                if (CollectionUtils.isEmpty(olds)) {
                    isModifiedImageAto.set(true);
                }
                ImageDto newOne = v.get(0);
                ImageDto oldOne = olds.get(0);
                if ((newOne.getHash() != null && !newOne.getHash().equals(oldOne.getHash())) ||
                        (newOne.getCoverHash() != null && !newOne.getCoverHash().equals(oldOne.getCoverHash()))
                        || (newOne.getJumpUrl() != null && !newOne.getJumpUrl().equals(oldOne.getJumpUrl()))
                        || (newOne.getSchemeUrl() != null && !newOne.getSchemeUrl().equals(oldOne.getSchemeUrl()))) {
                    isModifiedImageAto.set(true);
                }
            });
            if (isModifiedImageAto.get()) {
                return true;
            }
        }

        List<ButtonDto> oldButtons = oldCreative.getButtons();
        List<ButtonDto> newButtons = newCreative.getButtons();
        if (newButtons == null && oldButtons != null) {
            return true;
        }
        if (oldButtons == null && newButtons != null) {
            return true;
        }
        if (newButtons != null && newButtons.size() != oldButtons.size()) {
            return true;
        }
        if (newButtons != null) {
            for (int n = 0; n < newButtons.size(); n++) {
                ButtonDto oldButton = oldButtons.get(n);
                ButtonDto newButton = newButtons.get(n);
                if ((oldButton.getButtonCopyId() != null && !oldButton.getButtonCopyId().equals(newButton.getButtonCopyId()))
                        || (oldButton.getJumpUrl() != null && !oldButton.getJumpUrl().equals(newButton.getJumpUrl())) ||
                        (oldButton.getSchemeUrl() != null && !oldButton.getSchemeUrl().equals(newButton.getSchemeUrl()))
                        || (oldButton.getButtonName() != null && !oldButton.getButtonName().equals(newButton.getButtonName()))
                        || (newButton.getSchemeUrl() != null && !newButton.getSchemeUrl().equals(oldButton.getSchemeUrl()))) {
                    return true;
                }
            }
        }
        return isModified;
    }

    public SearchCptCreativeDto getByCreativeId(Long creativeId, Operator operator) throws ServiceException {
        CptCreativeDto cptCreativeDto = queryCptCreativeService.getCreativeById(creativeId);
        SearchCptCreativeDto creativeDto = new SearchCptCreativeDto();
        BeanUtils.copyProperties(cptCreativeDto, creativeDto);

        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(cptCreativeDto.getScheduleId());
        GdOrderDto gdOrderDto = gdOrderService.getOrderById(cptCreativeDto.getOrderId(), operator);

        TemplateDto template = queryTemplateService.getTemplateById(cptCreativeDto.getTemplateId());
        //现在使用都是新版的标
        String markName = busMarkService.getBusMark(cptCreativeDto.getBusMarkId()).getName();
//        if (configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(template.getTemplateId())) {
//            markName = busMarkService.getBusMark(cptCreativeDto.getBusMarkId()).getName();
//        } else {
//            CmMarkEnum cmMarkEnum = CmMarkEnum.getByCode(cptCreativeDto.getCmMark());
//            markName = cmMarkEnum.name();
//        }
        creativeDto.setCmMarkName(markName);
        creativeDto.setOrderName(gdOrderDto.getOrderName());
        creativeDto.setSalesType(scheduleDto.getSalesType());
        creativeDto.setSalesTypeDesc(SalesType.getByCode(scheduleDto.getSalesType()).getDesc());
        creativeDto.setScheduleName(scheduleDto.getName());
        creativeDto.setSourceId(scheduleDto.getSlotId());
        creativeDto.setSourceName(scheduleDto.getSourceName());
        creativeDto.setTemplateName(template.getTemplateName());
        creativeDto.setTemplate(template);
        creativeDto.setLauncher(scheduleDto.getLauncher());
        creativeDto.setGdScheduleId(scheduleDto.getScheduleId());
        creativeDto.setGdCreativeId(creativeId);
        creativeDto.setPromotionPurposeType(scheduleDto.getPromotionPurposeType());

        BusinessSideBaseDto businessSide = businessSideService.getBusinessSideById(scheduleDto.getBusinessSideId());

        boolean isSupportScheme = false;
        //一些模板不支持卡片跳转，如果不是这些模板则走正常逻辑
        if (!this.configCenter.getSearchCptConfig().isDisableSupportSchemaUrl(template.getTemplateId())) {
            isSupportScheme = isSupportScheme(businessSide.getAccountId(), scheduleDto.getPlatformId());
        }
        creativeDto.setIsSupportSchema(isSupportScheme);

        creativeDto.setButtons(this.creativeButtonService.getButtons(creativeId, CategoryEnum.THEME));
        //填充图片相关信息
        fillImageInfoIfNecessary(creativeDto, template);
        //填充品牌相关信息
        fillBrandInfoIfNecessary(creativeDto);
        //填充稿件相关信息
        fillManiScriptJumpInfoIfNecessary(creativeDto);
        //填充本地视频信息
        fillVideoInfoIfNecessary(creativeDto);
        //填充背景信息
        fillBackgroundInfoIfNecessary(creativeDto);
        //填充分享信息
        fillShare(creativeId, creativeDto);
        return creativeDto;
    }

    private void fillBrandInfoIfNecessary(SearchCptCreativeDto creativeDto) {
        GdCreativeCustomizeBrandInfoPoExample brandInfoPoExample = new GdCreativeCustomizeBrandInfoPoExample();
        brandInfoPoExample.or()
                .andCreativeIdEqualTo(creativeDto.getGdCreativeId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<GdCreativeCustomizeBrandInfoPo> brandInfoPos = brandInfoDao.selectByExample(brandInfoPoExample);
        if (!CollectionUtils.isEmpty(brandInfoPos)) {
            GdCreativeCustomizeBrandInfoPo po = brandInfoPos.get(0);
            creativeDto.setBrandDescription(po.getBrandDescription());
            creativeDto.setBrandName(po.getBrandName());
            creativeDto.setFaceUrl(po.getFace());
            creativeDto.setFaceMd5(po.getFaceMd5());
        }
    }

    private void fillImageInfoIfNecessary(SearchCptCreativeDto creativeDto, TemplateDto template) {
        GdCreativeImagePoExample example = new GdCreativeImagePoExample();
        example.or()
                .andCreativeIdEqualTo(creativeDto.getGdCreativeId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<GdCreativeImagePo> imagePoList = gdCreativeImageDao.selectByExample(example);

        List<ImageDto> imageDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(imagePoList)) {
            imagePoList.forEach(t -> {
                ImageDto dto = new ImageDto();
                BeanUtils.copyProperties(t, dto);
                if (!StringUtils.isEmpty(t.getGifImageUrl())) {
                    dto.setIsGif(true);
                    dto.setCoverUrl(t.getImageUrl());
                    dto.setCoverHash(buildHashCode(template.getTemplateId(), com.bilibili.brand.api.common.enums.MaterialType.IMAGE,
                            t.getImageUrl(), t.getImageMd5()));
                    dto.setImageUrl(t.getGifImageUrl());
                    dto.setHash(buildHashCode(template.getTemplateId(), com.bilibili.brand.api.common.enums.MaterialType.GIF,
                            t.getGifImageUrl(), t.getGifImageUrlHash()));
                } else {
                    dto.setHash(buildHashCode(template.getTemplateId(), com.bilibili.brand.api.common.enums.MaterialType.IMAGE,
                            t.getImageUrl(), t.getImageMd5()));
                }
                dto.setButtons(this.creativeButtonService.getButtons(creativeDto.getGdCreativeId(), dto.getId().longValue(), CategoryEnum.IMAGE));
                imageDtoList.add(dto);
            });
        }
        imageDtoList.sort(Comparator.comparing(ImageDto::getSeq));
        creativeDto.setImages(imageDtoList);
    }

    private void fillManiScriptJumpInfoIfNecessary(SearchCptCreativeDto creativeDto) {
        GdCreativeManuscriptInfoPoExample manuscriptInfoPoExample = new GdCreativeManuscriptInfoPoExample();
        manuscriptInfoPoExample.or()
                .andCreativeIdEqualTo(creativeDto.getGdCreativeId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdCreativeManuscriptInfoPo> manuscriptInfoPos = manuscriptInfoDao.selectByExample(manuscriptInfoPoExample);
        if (!CollectionUtils.isEmpty(manuscriptInfoPos)) {
            creativeDto.setAids(manuscriptInfoPos.stream()
                    .map(GdCreativeManuscriptInfoPo::getAid)
                    .collect(Collectors.toList())
            );
        }

        if (manuscriptInfoPos.size() == 1) {
            GdCreativeManuscriptInfoPo manuscriptInfo = manuscriptInfoPos.get(0);
            creativeDto.setOpenDanmuku(manuscriptInfo.getOpenDanmuku());
            creativeDto.setManuscriptJumpType(manuscriptInfo.getJumpType());
            creativeDto.setManuscriptJumpUrl(manuscriptInfo.getJumpUrl());
        }
    }

    private void fillBackgroundInfoIfNecessary(SearchCptCreativeDto creativeDto) {
        GdCreativeBackgroundPo backgroundPo = backgroundService.queryByCreativeId(creativeDto.getGdCreativeId());

        if (backgroundPo != null) {
            BackgroundColorEnum backgroundColorEnum = BackgroundColorEnum.getByCode(backgroundPo.getBusinessCode());
            creativeDto.setBackGroundColor(BackGroundColorBo.builder()
                            .code(backgroundPo.getBusinessCode())
                            .type(backgroundPo.getBusinessType())
                            .name(Objects.isNull(backgroundColorEnum) ? "" : backgroundColorEnum.getName())
                            .nightModeColor(backgroundPo.getNightModeBackgroundColor())
                            .normalModeColor(backgroundPo.getNormalModeBackgroundColor())
                    .build());
//            creativeDto.setBackgroundColorCode(backgroundPo.getBusinessCode());
        }
    }

    private void fillVideoInfoIfNecessary(SearchCptCreativeDto creativeDto) {
        creativeDto.setMgkVideos(this.creativeVideoService.getMgkVideoByCreative(creativeDto.getGdCreativeId()));
    }

    private void fillShare(long creativeId, SearchCptCreativeDto creativeDto) {
        GdCreativeShareDto shareDto = gdCreativeShareService.getCreativeShare(creativeId, SalesType.SEARCH_CPT.getCode());
        if (shareDto == null) {
            return;
        }
        creativeDto.setShareState(shareDto.getState());
        creativeDto.setShareTitle(shareDto.getTitle());
        creativeDto.setShareSubTitle(shareDto.getSubTitle());
        creativeDto.setShareImageUrl(shareDto.getImageUrl());
        creativeDto.setShareImageUrl(shareDto.getImageHash());
    }

    private boolean isSupportScheme(Integer accountId, Integer platformId) {
        return Utils.isPositive(accountId)
                && PlatformType.getByCode(platformId).supportScheme();
    }

    private String buildHashCode(Integer templateId, com.bilibili.brand.api.common.enums.MaterialType type, String url, String md5) {
        ImageHash hash = ImageHash.builder().url(url).md5(md5).templateId(templateId).type(type).build();
        return Base64Utils.encodeToString(JSON.toJSONString(hash).getBytes());
    }


    @Transactional(rollbackFor = Exception.class)
    public void offline(Operator operator, CptCreativeAuditDto auditDto, boolean isAuditPassDownline) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人员不可为空");
        Assert.notNull(auditDto, "创意审核信息不可为空");
        Assert.notNull(auditDto.getGdCreativeId(), "创意ID不可为空");
        Assert.notNull(auditDto.getVersion(), "创意版本号不可为空");
        Assert.hasText(auditDto.getReason(), "创意下线理由不可为空");
        GdCreativePo gdCreativePo = this.getGdCreativeById(auditDto.getGdCreativeId());

        LaunchStatus oldLaunchStatus = LaunchStatus.getByCode(gdCreativePo.getStatus());
        if (isAuditPassDownline && !LaunchStatus.MODIFY_WAIT_OFF_LINE.equals(oldLaunchStatus)) {
            //说明老创意已下线或已删除
            return;
        }
        Assert.isTrue(gdCreativePo.getVersion().equals(auditDto.getVersion()), "创意信息已发生变更，请刷新页面后重试");
        Assert.isTrue(LaunchStatus.MODIFY_WAIT_OFF_LINE.equals(oldLaunchStatus), "创意状态为" + oldLaunchStatus.getName() + "不可更改为" + LaunchStatus.OFFLINE.getName());
        this.updateCreativeStatus(gdCreativePo.getCreativeId(), gdCreativePo.getVersion(), null, LaunchStatus.OFFLINE, auditDto.getReason());
        CreativeLogBean logBean = CreativeLogBean.builder()
                .id(auditDto.getGdCreativeId().toString())
                .statusDesc(LaunchStatus.MODIFY_WAIT_OFF_LINE.getName())
                .build();
        gdLogService.insertLog(auditDto.getGdCreativeId(), GdLogFlag.CREATIVE, LogOperateType.UPDATE_CREATIVE_STATUS, operator, logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditReject(Operator operator, CptCreativeAuditDto auditDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人员不可为空");
        Assert.notNull(auditDto, "创意审核信息不可为空");
        Assert.notNull(auditDto.getGdCreativeId(), "创意ID不可为空");
        Assert.hasText(auditDto.getReason(), "创意驳回理由不可为空");
        Assert.notNull(auditDto.getVersion(), "创意版本号不可为空");
        this.audit(operator, auditDto, AuditStatus.REJECT);
        CreativeLogBean logBean = CreativeLogBean.builder()
                .id(auditDto.getGdCreativeId().toString())
                .statusDesc(AuditStatus.REJECT.getName())
                .build();
        gdLogService.insertLog(auditDto.getGdCreativeId(), GdLogFlag.CREATIVE, LogOperateType.UPDATE_CREATIVE_STATUS, operator, logBean);
    }


    private String getMd5FromHash(String hash) {
        if (Strings.isNullOrEmpty(hash)) {
            return "";
        }
        String decodeHash = new String(Base64.decodeBase64(hash));
        ImageHash hashBean = JSON.parseObject(decodeHash, ImageHash.class);
        return hashBean.getMd5();
    }

    private GdCreativePo buildGdCreativePo(CreativeBaseBean baseBean, CptScheduleDto scheduleDto) {

        GdCreativePo record = new GdCreativePo();
        BeanUtils.copyProperties(baseBean, record);

        record.setEndTime(TimeUtils.getLastMinAndSecOfTime(baseBean.getEndTime()));

        record.setBusinessSideId(scheduleDto.getBusinessSideId());
        record.setAccountId(scheduleDto.getAccountId());
        record.setPlatformId(scheduleDto.getPlatformId());
        record.setPlatformName(scheduleDto.getPlatformName());
        record.setLocPageId(scheduleDto.getPageId());
        record.setLocPageName(scheduleDto.getPageName());
        record.setResourceId(scheduleDto.getResourceId());
        record.setResourceName(scheduleDto.getResourceName());
        record.setSourceId(scheduleDto.getSourceId());
        record.setSourceName(scheduleDto.getSourceName());

        record.setOrderId(scheduleDto.getGdOrderId());
        record.setScheduleId(scheduleDto.getId());
        record.setStatus(LaunchStatus.START.getCode());
        record.setAuditStatus(AuditStatus.INIT.getCode());
        record.setSalesType(SalesType.SEARCH_CPT.getCode());
        record.setAppPackageId(scheduleDto.getAppPackageId());
        record.setSearchCreativeType(SearchCptCreativeType.getByTemplate(scheduleDto.getTemplateId()).getCode());
        record.setOrderProduct(OrderProduct.SEARCH_CPT.getCode());
        return record;
    }

    private MgkLandingPageBean getLandingPageBean(SearchCptCreativeDto creative, CptScheduleDto scheduleDto) {
        String ppc = creative.getPromotionPurposeContent();
        GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());
        if (Objects.equals(jumpType, GdJumpType.GAME)) {
            //https://www.tapd.cn/********/prong/stories/view/11********004168063
            jumpType.validateUrlIsValid(ppc);
            String launchUrl = UriComponentsBuilder.fromUriString(GdJumpType.GAME.getLaunchUrl(ppc))
                    .queryParam("source", SOURCE_PARAM)
                    .queryParam("sourceFrom", BRAND_LAND_PAGE_FROM_PARAM)
                    .build()
                    .toUriString();
            return MgkLandingPageBean.builder()
                    .adVersionControllId(0)
                    .launchUrl(launchUrl)
                    .templateStyle(0)
                    .pageType(0)
                    .build();
        }

        int promotionType = scheduleDto.getPromotionPurposeType();

        if (promotionType == PromotionPurposeType.LIVE.getCode()) {
            Long liveId = Long.parseLong(GdJumpType.LIVE.parseLaunchUrl(creative.getPromotionPurposeContent())
                    .replace(GdJumpType.LIVE.getPreffix(), ""));
            LiveRoomDto liveRoom = liveGrpcClient.queryLiveRoom(liveId);
            return MgkLandingPageBean.builder()
                    .adVersionControllId(0)
                    .launchUrl(GdJumpType.LIVE.getLaunchUrl(liveRoom.getLink()).trim())
                    .templateStyle(0)
                    .pageType(0)
                    .build();

        } else if (promotionType == PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode()) {
            //目前Web存在搜索cpt多稿件的场景，因此只需第一个来填充主表的稿件信息
            Assert.isTrue(!CollectionUtils.isEmpty(creative.getAids()), "稿件数量不正确");
            return MgkLandingPageBean.builder()
                    .adVersionControllId(0)
                    .launchUrl(GdJumpType.VIDEO_MOBILE.getLaunchUrl(String.valueOf(creative.getAids().get(0))).trim())
                    .templateStyle(0)
                    .pageType(0)
                    .build();
        }

        if (Objects.equals(jumpType, GdJumpType.MGK_PAGE_ID)) {
            //搜索CPT创意和按钮维度都支持所有类型的建站落地页，validatePageIdAndGetLandingPage(jumpType, pageId)接口只会返回非401（小程序）的页面
            //品牌之前不支持401页面时考虑的加载慢等性能问题比如在闪屏场景，所以在非闪屏场景可以支持401
            MgkLandingPageBean mgkLandingPageBean = soaLandingPageService.validatePageIdAndGetLandingPage(creative.getPromotionPurposeContent());
            Assert.isTrue(Objects.nonNull(mgkLandingPageBean) && StringUtils.hasText(mgkLandingPageBean.getLaunchUrl()),
                    "建站落地页不存在，页面id=" + creative.getPromotionPurposeContent());
            if (!CollectionUtils.isEmpty(mgkLandingPageBean.getGames())) {
                String launchUrl = UriComponentsBuilder.fromUriString(mgkLandingPageBean.getLaunchUrl())
                        .replaceQueryParam("source", SOURCE_PARAM)
                        .replaceQueryParam("sourceFrom", BRAND_LAND_PAGE_FROM_PARAM)
                        .build()
                        .toUriString();
                mgkLandingPageBean.setLaunchUrl(launchUrl);
            }
            return mgkLandingPageBean;
        }

        String jumpUrl = replaceGameSourceFrom(creative.getPromotionPurposeContent(), BRAND_LAND_PAGE_FROM_PARAM, jumpType);

        return MgkLandingPageBean.builder()
                .adVersionControllId(0)
                .launchUrl(jumpUrl)
                .templateStyle(0)
                .pageType(0)
                .build();
    }

    protected GdCreativePo getGdCreativeById(Long creativeId) {
        GdCreativePoExample example = new GdCreativePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(creativeId);
        List<GdCreativePo> gdCreativePos = gdCreativeDao.selectByExample(example);
        Assert.notEmpty(gdCreativePos, "该创意不存在");
        return gdCreativePos.get(0);
    }

    private void deleteGdCreative(GdCreativePo creativePo) {
        GdCreativePo record = GdCreativePo.builder()
                .status(LaunchStatus.DELETE.getCode())
                .version(creativePo.getVersion() + 1)
                .build();
        GdCreativePoExample example = new GdCreativePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(creativePo.getCreativeId())
                .andVersionEqualTo(creativePo.getVersion())
                .andStatusNotEqualTo(LaunchStatus.DELETE.getCode());
        gdCreativeDao.updateByExampleSelective(record, example);
    }

    private void updateCreativeStatus(Long gdCreativeId, Integer version,
                                      AuditStatus auditStatus,
                                      LaunchStatus launchStatus,
                                      String reason) {
        GdCreativePoExample example = new GdCreativePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(gdCreativeId)
                .andVersionEqualTo(version);

        GdCreativePo auditPo = GdCreativePo.builder()
                .auditStatus(auditStatus == null ? null : auditStatus.getCode())
                .status(launchStatus == null ? null : launchStatus.getCode())
                .reason(Strings.isNullOrEmpty(reason) ? "" : reason)
                .version(version + 1)
                .build();

        long result = gdCreativeDao.updateByExampleSelective(auditPo, example);
        Assert.isTrue(result > 0, "创意【" + gdCreativeId + "】操作失败，请刷新后重试");
    }

    private void audit(Operator operator, CptCreativeAuditDto creativeAuditDto, AuditStatus auditStatus) {

        GdCreativePo gdCreativePo = this.getGdCreativeById(creativeAuditDto.getGdCreativeId());
        Assert.isTrue(creativeAuditDto.getVersion().equals(gdCreativePo.getVersion()), "创意信息已发生变更，请刷新页面后重试");

        AuditStatus oldAuditStatus = AuditStatus.getByCode(gdCreativePo.getAuditStatus());
        LaunchStatus launchStatus = LaunchStatus.getByCode(gdCreativePo.getStatus());
        Assert.isTrue(!auditStatus.equals(oldAuditStatus), "该创意已" + auditStatus.getName());
        Assert.isTrue(launchStatus.isCanAudit(), launchStatus.getName() + "的创意不可再审核");

        this.updateCreativeStatus(gdCreativePo.getCreativeId(), gdCreativePo.getVersion(), auditStatus, null, creativeAuditDto.getReason());

        if (gdCreativePo.getDisableCreativeId() > 0 && AuditStatus.ACCEPT.equals(auditStatus)) {
            GdCreativePo disableCreativePo = this.getGdCreativeById(gdCreativePo.getDisableCreativeId());
            this.offline(operator, CptCreativeAuditDto.builder().gdCreativeId(disableCreativePo.getCreativeId())
                    .version(disableCreativePo.getVersion())
                    .reason("新创意审核通过，系统自动下线旧创意")
                    .build(), true);
        }
    }

    public void batchUpdateStatus(List<Long> creativeIdList, LaunchStatus status) {
        GdCreativePo updatePo = GdCreativePo.builder()
                .status(status.getCode())
                .build();
        GdCreativePoExample example = new GdCreativePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIdList);
        gdCreativeDao.updateByExampleSelective(updatePo, example);
    }

    //保存拓展信息
    private void saveCreativeExt(Long creativeId, SearchCptCreativeDto creativeDto,
                                 CptOrderDto order, CptScheduleDto schedule) {
        this.creativeExtService.saveCreativeExt(CreativeExtHolderDto.builder()
                .orderId(order.getId())
                .scheduleId(schedule.getId())
                .creativeId(creativeId)
                .orderProduct(order.getProduct())
                .productLabel(creativeDto.getProductLabel())
                .build());
    }

    private void saveButton(CptScheduleDto schedule, Long creativeId, Long boundId, CategoryEnum category,
                            List<ButtonDto> buttons) {
        List<ButtonDto> buttonDtos = buttons.stream()
                .filter(button -> {
                    ButtonStyle buttonStyle = ButtonStyle.getByCodeWithValidation(button.getButtonStyle());
                    if (Objects.equals(ButtonStyle.SELECT, buttonStyle)) {
                        //如果是选择式按钮，则按钮id和按钮名称必须都存在才是有效
                        return StringUtils.hasText(button.getButtonName()) && Utils.isPositive(button.getButtonCopyId());
                    }
                    //自定义按钮，按钮名称必须存在才是有效
                    return StringUtils.hasText(button.getButtonName());
                }).collect(Collectors.toList());
        List<ButtonDto> selectedButtons = buttonDtos.stream().filter(button -> Objects.equals(ButtonStyle.SELECT.getCode(), button.getButtonStyle())).collect(Collectors.toList());
        List<ButtonDto> res = new ArrayList<>(handleSelectedButtons(schedule, selectedButtons));
        List<ButtonDto> customizedButtons = buttonDtos.stream().filter(button -> !Objects.equals(ButtonStyle.SELECT.getCode(), button.getButtonStyle())).collect(Collectors.toList());
        res.addAll(handleCustomizedButtons(customizedButtons));
        this.creativeButtonService.saveOrUpdateButtonCopy(creativeId, boundId, category, res);
    }

    private List<ButtonDto> handleCustomizedButtons(List<ButtonDto> customizedButtons) {
        if (CollectionUtils.isEmpty(customizedButtons)) {
            return Collections.emptyList();
        }
        for (int i = 0; i < customizedButtons.size(); i++) {
            String launchUrl = null;
            ButtonDto button = customizedButtons.get(i);
            GdJumpType jumpType = GdJumpType.getByCode(button.getJumpType());
            SearchCptCustomizedSourceFromEnum sourceFromEnum = SearchCptCustomizedSourceFromEnum.getSourceFromByIndex(i);
            //运营位按钮
            if (Objects.equals(jumpType, GdJumpType.GAME)) {
                //https://www.tapd.cn/********/prong/stories/view/11********004168063
                GdJumpType.GAME.parseLaunchUrl(button.getJumpUrl());
                launchUrl = UriComponentsBuilder.fromUriString(GdJumpType.GAME.getLaunchUrl(button.getJumpUrl()))
                        .queryParam("source", SOURCE_PARAM)
                        .queryParam("sourceFrom",sourceFromEnum.getSourceFrom())
                        .build()
                        .toUriString();
            } else if (Objects.equals(jumpType, GdJumpType.MGK_PAGE_ID)) {
                MgkLandingPageBean pageBean = soaLandingPageService.validatePageIdAndGetLandingPage(button.getJumpUrl());
                Assert.notNull(pageBean, "落地页不存在");
                Assert.isTrue(!StringUtils.isEmpty(pageBean.getLaunchUrl()), "不支持的落地页类型!");
                launchUrl = pageBean.getLaunchUrl();
                if (!CollectionUtils.isEmpty(pageBean.getGames())) {
                    launchUrl = UriComponentsBuilder.fromUriString(pageBean.getLaunchUrl())
                            .replaceQueryParam("source", SOURCE_PARAM)
                            .replaceQueryParam("sourceFrom", sourceFromEnum.getSourceFrom())
                            .build()
                            .toUriString();
                }
            } else {
                launchUrl = replaceGameSourceFrom(button.getJumpUrl(), sourceFromEnum.getSourceFrom(), jumpType);
            }
            button.setJumpUrl(launchUrl);
        }
        return customizedButtons;
    }

    private static String replaceGameSourceFrom(String jumpUrl, String sourceFrom, GdJumpType jumpType) {
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(jumpUrl).build();
        MultiValueMap<String, String> queryParams = uriComponents.getQueryParams();

        // Check if the URL starts with "bilibili://game_center" for native links
        if (jumpUrl.startsWith("bilibili://game_center")) {
            // Validate the link format
            GdJumpType.GAME.parseLaunchUrl(jumpUrl);
            jumpUrl = UriComponentsBuilder.fromUriString(jumpUrl)
                    .queryParam("source", SOURCE_PARAM)
                    .toUriString();
        } else if (!Pattern.compile(GAME_NEED_ADD_SOURCE_FROM_URL_REGEX).matcher(jumpUrl).matches()) {
            return jumpType.getLaunchUrl(jumpUrl); // Return launch URL directly for other cases
        }

        // If the link doesn't have "sourceFrom" parameter, add it; otherwise pass through
        return queryParams.containsKey("sourceFrom")
                ? jumpUrl
                : UriComponentsBuilder.fromUriString(jumpUrl)
                .queryParam("sourceFrom", sourceFrom)
                .toUriString();
    }

    private List<ButtonDto> handleSelectedButtons(CptScheduleDto schedule, List<ButtonDto> selectedButtons) {
        if (CollectionUtils.isEmpty(selectedButtons)) {
            return Collections.emptyList();
        }
        // 品牌按钮 **********
        selectedButtons.forEach(button -> {
            String launchUrl = null;
            ButtonCopyTypeEnum buttonType = ButtonCopyTypeEnum.getByCode(button.getButtonType());
            GdJumpType jumpType = GdJumpType.getByCodeWithoutEx(button.getJumpType());
            //是否是品牌按钮同时又是关注，关注按钮直接遵循原来的蓝v逻辑，不需要单独的链接
            boolean isSelectFollow = Objects.equals("关注", button.getButtonName());
            //直播预约按钮不需要单独的链接
            boolean isLiveBooking = Objects.equals(buttonType, ButtonCopyTypeEnum.LIVE_BOOKING);
            if (!isSelectFollow && !isLiveBooking) {
                Assert.notNull(jumpType, "非法的跳转类型：" + button.getJumpType());
                Assert.isTrue(jumpType.validateUrlIsValid(button.getJumpUrl()), "非法的跳转链接：" + button.getJumpUrl());
            }
            //品牌按钮
            if (Objects.equals(buttonType, ButtonCopyTypeEnum.GAME_DOWNLOAD)) {
                //品牌按钮：游戏下载类型
                //https://www.tapd.cn/********/prong/stories/view/11********004168063
                GameDto game = schedule.getGame();
                Assert.notNull(game, "游戏下载必须在排期层绑定游戏");
                //游戏下载
                if (Objects.equals(jumpType, GdJumpType.LINK)) {
                    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(button.getJumpUrl());
                    MultiValueMap<String, String> queryParams = uriComponentsBuilder.build().getQueryParams();
                    //解析trackadf
                    String buttonTrackAdf = "";
                    String source = queryParams.getFirst("source");
                    if (StringUtils.hasText(source) && source.startsWith("trackadf_")) {
                        buttonTrackAdf = source;
                    }

                    //解析gameBaseId
                    //之所以没有直接从queryParams取id，是因为想通过GdJumpType.GAME.parseLaunchUrl校验下是否存在合法的gameBaseId
                    String gameBaseId = GdJumpType.GAME.parseLaunchUrl(button.getJumpUrl());

                    //如果不存在source，则使用宏参覆盖
                    if (!queryParams.containsKey("source")) {
                        uriComponentsBuilder.queryParam("source", SOURCE_PARAM);
                    }

                    launchUrl = uriComponentsBuilder
                            .replaceQueryParam("sourceFrom", BRAND_BUTTON_SOURCE_FROM_PARAM)//override
                            .replaceQueryParam("channelId", game.getChannelId())//override
                            .toUriString();

                    button.setGameBaseId(Integer.valueOf(gameBaseId));
                    button.setTrackAdf(buttonTrackAdf);
                } else if (Objects.equals(jumpType, GdJumpType.GAME)) {
                    String gameBaseId = GdJumpType.GAME.parseLaunchUrl(button.getJumpUrl());
                    launchUrl = UriComponentsBuilder.fromUriString(GdJumpType.GAME.getLaunchUrl(button.getJumpUrl()))
                            .queryParam("source", SOURCE_PARAM)
                            .queryParam("sourceFrom", BRAND_BUTTON_SOURCE_FROM_PARAM)
                            .queryParam("channelId", game.getChannelId())
                            .toUriString();
                    button.setGameBaseId(Integer.valueOf(gameBaseId));
                } else if (Objects.equals(jumpType, GdJumpType.MGK_PAGE_ID)) {
                    MgkLandingPageBean pageBean = soaLandingPageService.validatePageIdAndGetLandingPage(button.getJumpUrl());
                    Assert.notNull(pageBean, "落地页不存在");
                    Assert.isTrue(!StringUtils.isEmpty(pageBean.getLaunchUrl()), "不支持的落地页类型!");
                    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(pageBean.getLaunchUrl())
                            .queryParam("sourceFrom", BRAND_BUTTON_SOURCE_FROM_PARAM);
                    if (!CollectionUtils.isEmpty(pageBean.getGames())) {
                        uriComponentsBuilder.queryParam("source", SOURCE_PARAM);
                        button.setGameBaseId(pageBean.getGames().get(0).getGameBaseId());
                    }
                    launchUrl = uriComponentsBuilder.build().toUriString();
                }
                Integer gameBaseId = Utils.isPositive(button.getGameBaseId()) ? button.getGameBaseId() : game.getGameBaseId();
                boolean isInner = IsInnerEnum.INNER.getCode().equals(accountService.getAccount(schedule.getAccountId()).getIsInner());
                button.setCustomizedUrl(this.buildGameClickMonitorUrl(gameBaseId, game.getChannelId(), isInner));
                button.setJumpUrl(launchUrl);
            }
        });
        return selectedButtons;
    }

    private void saveMonitor(Long creativeId, SearchCptCreativeDto creative, CptScheduleDto schedule, MgkLandingPageBean pageBean) {
        PromotionPurposeType ppt = PromotionPurposeType.getByCode(schedule.getPromotionPurposeType());
        if (!Objects.equals(ppt, PromotionPurposeType.ANDROID_GAME_DOWNLOAD)) {
            return;
        }
        String ppc = creative.getPromotionPurposeContent();
        GdJumpType jumpType = GdJumpType.getByCode(creative.getJumpType());
        String gameBaseId = "";
        if (Objects.equals(jumpType, GdJumpType.LINK)) {
            if (ppc.trim().startsWith("bilibili://game_center/")) {
                //如果游戏链接，则尝试解析游戏id，否则忽略
                gameBaseId = GdJumpType.GAME.parseLaunchUrl(ppc);
            }
        } else if (Objects.equals(jumpType, GdJumpType.GAME)) {
            gameBaseId = GdJumpType.GAME.parseLaunchUrl(ppc);
        } else if (Objects.equals(jumpType, GdJumpType.MGK_PAGE_ID)) {
            if (!CollectionUtils.isEmpty(pageBean.getGames())) {
                gameBaseId = pageBean.getGames().get(0).getGameBaseId().toString();
            }
        }

        List<CptCreativeMonitoringDto> monitoringList = Lists.newArrayList();

        //1、游戏下载监控连接
        //https://www.tapd.cn/********/prong/stories/view/11********004168063
        if (StringUtils.hasText(gameBaseId)) {
            boolean isInner = IsInnerEnum.INNER.getCode().equals(accountService.getAccount(schedule.getAccountId()).getIsInner());
            Integer gameChannelId = Objects.nonNull(schedule.getGame()) ? schedule.getGame().getChannelId() : GameChannelEnum.NORMAL.getCode();
            String gameClickMonitorUrl = buildGameClickMonitorUrl(Integer.valueOf(gameBaseId), gameChannelId, isInner);
            monitoringList.add(CptCreativeMonitoringDto.builder()
                    .type(MonitorType.GAME_CLICK.getCode())
                    .urls(Lists.newArrayList(gameClickMonitorUrl))
                    .build());
        }

        //其他...
        try {
            this.relationService.saveCptCreativeMonitoring(creativeId, schedule.getId(), monitoringList);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    private String buildGameClickMonitorUrl(Integer gameBaseId, Integer gameChannelId, boolean isInner) {
        // platform_type:ANDROID(1, "安卓"), IOS(2, "IOS");
        // platform_type固定为1（安卓）
        String clickMonitorPattern = "https://ad-bili-data.biligame.com/api/biliad/ocpx/click?game_base_id=%d" +
                "&platform_type=1&track_id=__TRACKID__&mid=__UID__&gamesdkuid=__GAMESDKUID__&device_type=__OS__" +
                "&time=__TIME__&account_id=__ACCOUNTID__&sycp_pkg_type=%d&sycp_oaid=__OAID__&sycp_imei=__IMEI__" +
                "&sycp_buvid=__BUVID__&sycp_ip=__IP__&sycp_ua=__UA__";
        String url = String.format(clickMonitorPattern, gameBaseId,
                Objects.nonNull(gameChannelId) ? gameChannelId : GameChannelEnum.NORMAL.getCode());
        if (isInner) {
            url += "&sourcetype=inner";
        }
        return url;
    }
}
