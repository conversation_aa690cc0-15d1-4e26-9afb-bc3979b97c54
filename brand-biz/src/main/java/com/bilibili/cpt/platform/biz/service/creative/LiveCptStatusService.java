package com.bilibili.cpt.platform.biz.service.creative;

import com.bilibili.brand.api.creative.dto.GdCreativeDto;
import com.bilibili.brand.biz.creative.service.GdCreativeService;
import com.bilibili.cpt.platform.api.creative.dto.cpt.live.LiveCptCreativeDto;
import com.bilibili.cpt.platform.api.creative.dto.cpt.live.LiveCptImageDto;
import com.bilibili.cpt.platform.api.creative.service.ILiveCptStatusService;
import com.bilibili.cpt.platform.common.AuditStatus;
import com.bilibili.cpt.platform.common.LaunchStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/9
 */
@Service
public class LiveCptStatusService implements ILiveCptStatusService {

    @Autowired
    private GdCreativeService gdCreativeService;
    @Autowired
    private LiveCptCreativeMaterialService materialService;

    @Override
    public void setDefaultStatus(LiveCptCreativeDto creative) {
        creative.setStatus(LaunchStatus.START.getCode());
        creative.setAuditStatus(AuditStatus.INIT.getCode());
    }

    public void changeStatusIfNecessary(LiveCptCreativeDto creative) {
        GdCreativeDto oldCreative = gdCreativeService.getGdCreativeDtoById(creative.getCreativeId());
        creative.setAuditStatus(oldCreative.getAuditStatus());
        creative.setStatus(oldCreative.getStatus());
        boolean titleChanged = !Objects.equals(oldCreative.getTitle(), creative.getTitle());
        boolean promotionUrlChanged = !Objects.equals(oldCreative.getPromotionPurposeContent(), creative.getPromotionPurposeContent());
        if (titleChanged || promotionUrlChanged) {
            creative.setAuditStatus(AuditStatus.INIT.getCode());
            return;
        }
        LiveCptImageDto image = materialService.getImage(creative.getCreativeId());
        if (creative.getImageInfo() != null && !image.getImageMd5().equals(creative.getImageInfo().getImageMd5())) {
            creative.setAuditStatus(AuditStatus.INIT.getCode());
        }
    }
}
