package com.bilibili.ssa.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class SsaSplashScreenAnimationPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SsaSplashScreenAnimationPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIsNull() {
            addCriterion("splash_screen_id is null");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIsNotNull() {
            addCriterion("splash_screen_id is not null");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdEqualTo(Integer value) {
            addCriterion("splash_screen_id =", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotEqualTo(Integer value) {
            addCriterion("splash_screen_id <>", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdGreaterThan(Integer value) {
            addCriterion("splash_screen_id >", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("splash_screen_id >=", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdLessThan(Integer value) {
            addCriterion("splash_screen_id <", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdLessThanOrEqualTo(Integer value) {
            addCriterion("splash_screen_id <=", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIn(List<Integer> values) {
            addCriterion("splash_screen_id in", values, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotIn(List<Integer> values) {
            addCriterion("splash_screen_id not in", values, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdBetween(Integer value1, Integer value2) {
            addCriterion("splash_screen_id between", value1, value2, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotBetween(Integer value1, Integer value2) {
            addCriterion("splash_screen_id not between", value1, value2, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(Integer value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(Integer value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(Integer value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(Integer value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<Integer> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<Integer> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(Integer value1, Integer value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNull() {
            addCriterion("image_url is null");
            return (Criteria) this;
        }

        public Criteria andImageUrlIsNotNull() {
            addCriterion("image_url is not null");
            return (Criteria) this;
        }

        public Criteria andImageUrlEqualTo(String value) {
            addCriterion("image_url =", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotEqualTo(String value) {
            addCriterion("image_url <>", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThan(String value) {
            addCriterion("image_url >", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("image_url >=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThan(String value) {
            addCriterion("image_url <", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLessThanOrEqualTo(String value) {
            addCriterion("image_url <=", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlLike(String value) {
            addCriterion("image_url like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotLike(String value) {
            addCriterion("image_url not like", value, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlIn(List<String> values) {
            addCriterion("image_url in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotIn(List<String> values) {
            addCriterion("image_url not in", values, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlBetween(String value1, String value2) {
            addCriterion("image_url between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageUrlNotBetween(String value1, String value2) {
            addCriterion("image_url not between", value1, value2, "imageUrl");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNull() {
            addCriterion("image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andImageMd5IsNotNull() {
            addCriterion("image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andImageMd5EqualTo(String value) {
            addCriterion("image_md5 =", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotEqualTo(String value) {
            addCriterion("image_md5 <>", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThan(String value) {
            addCriterion("image_md5 >", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("image_md5 >=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThan(String value) {
            addCriterion("image_md5 <", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5LessThanOrEqualTo(String value) {
            addCriterion("image_md5 <=", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Like(String value) {
            addCriterion("image_md5 like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotLike(String value) {
            addCriterion("image_md5 not like", value, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5In(List<String> values) {
            addCriterion("image_md5 in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotIn(List<String> values) {
            addCriterion("image_md5 not in", values, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5Between(String value1, String value2) {
            addCriterion("image_md5 between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andImageMd5NotBetween(String value1, String value2) {
            addCriterion("image_md5 not between", value1, value2, "imageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlIsNull() {
            addCriterion("guide_image_url is null");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlIsNotNull() {
            addCriterion("guide_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlEqualTo(String value) {
            addCriterion("guide_image_url =", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlNotEqualTo(String value) {
            addCriterion("guide_image_url <>", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlGreaterThan(String value) {
            addCriterion("guide_image_url >", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("guide_image_url >=", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlLessThan(String value) {
            addCriterion("guide_image_url <", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlLessThanOrEqualTo(String value) {
            addCriterion("guide_image_url <=", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlLike(String value) {
            addCriterion("guide_image_url like", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlNotLike(String value) {
            addCriterion("guide_image_url not like", value, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlIn(List<String> values) {
            addCriterion("guide_image_url in", values, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlNotIn(List<String> values) {
            addCriterion("guide_image_url not in", values, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlBetween(String value1, String value2) {
            addCriterion("guide_image_url between", value1, value2, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageUrlNotBetween(String value1, String value2) {
            addCriterion("guide_image_url not between", value1, value2, "guideImageUrl");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5IsNull() {
            addCriterion("guide_image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5IsNotNull() {
            addCriterion("guide_image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5EqualTo(String value) {
            addCriterion("guide_image_md5 =", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5NotEqualTo(String value) {
            addCriterion("guide_image_md5 <>", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5GreaterThan(String value) {
            addCriterion("guide_image_md5 >", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("guide_image_md5 >=", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5LessThan(String value) {
            addCriterion("guide_image_md5 <", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5LessThanOrEqualTo(String value) {
            addCriterion("guide_image_md5 <=", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5Like(String value) {
            addCriterion("guide_image_md5 like", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5NotLike(String value) {
            addCriterion("guide_image_md5 not like", value, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5In(List<String> values) {
            addCriterion("guide_image_md5 in", values, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5NotIn(List<String> values) {
            addCriterion("guide_image_md5 not in", values, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5Between(String value1, String value2) {
            addCriterion("guide_image_md5 between", value1, value2, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideImageMd5NotBetween(String value1, String value2) {
            addCriterion("guide_image_md5 not between", value1, value2, "guideImageMd5");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeIsNull() {
            addCriterion("guide_show_time is null");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeIsNotNull() {
            addCriterion("guide_show_time is not null");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeEqualTo(Integer value) {
            addCriterion("guide_show_time =", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeNotEqualTo(Integer value) {
            addCriterion("guide_show_time <>", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeGreaterThan(Integer value) {
            addCriterion("guide_show_time >", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeGreaterThanOrEqualTo(Integer value) {
            addCriterion("guide_show_time >=", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeLessThan(Integer value) {
            addCriterion("guide_show_time <", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeLessThanOrEqualTo(Integer value) {
            addCriterion("guide_show_time <=", value, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeIn(List<Integer> values) {
            addCriterion("guide_show_time in", values, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeNotIn(List<Integer> values) {
            addCriterion("guide_show_time not in", values, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeBetween(Integer value1, Integer value2) {
            addCriterion("guide_show_time between", value1, value2, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowTimeNotBetween(Integer value1, Integer value2) {
            addCriterion("guide_show_time not between", value1, value2, "guideShowTime");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationIsNull() {
            addCriterion("guide_show_duration is null");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationIsNotNull() {
            addCriterion("guide_show_duration is not null");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationEqualTo(Integer value) {
            addCriterion("guide_show_duration =", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationNotEqualTo(Integer value) {
            addCriterion("guide_show_duration <>", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationGreaterThan(Integer value) {
            addCriterion("guide_show_duration >", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("guide_show_duration >=", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationLessThan(Integer value) {
            addCriterion("guide_show_duration <", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationLessThanOrEqualTo(Integer value) {
            addCriterion("guide_show_duration <=", value, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationIn(List<Integer> values) {
            addCriterion("guide_show_duration in", values, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationNotIn(List<Integer> values) {
            addCriterion("guide_show_duration not in", values, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationBetween(Integer value1, Integer value2) {
            addCriterion("guide_show_duration between", value1, value2, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andGuideShowDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("guide_show_duration not between", value1, value2, "guideShowDuration");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andPositionXIsNull() {
            addCriterion("position_x is null");
            return (Criteria) this;
        }

        public Criteria andPositionXIsNotNull() {
            addCriterion("position_x is not null");
            return (Criteria) this;
        }

        public Criteria andPositionXEqualTo(Integer value) {
            addCriterion("position_x =", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXNotEqualTo(Integer value) {
            addCriterion("position_x <>", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXGreaterThan(Integer value) {
            addCriterion("position_x >", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXGreaterThanOrEqualTo(Integer value) {
            addCriterion("position_x >=", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXLessThan(Integer value) {
            addCriterion("position_x <", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXLessThanOrEqualTo(Integer value) {
            addCriterion("position_x <=", value, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXIn(List<Integer> values) {
            addCriterion("position_x in", values, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXNotIn(List<Integer> values) {
            addCriterion("position_x not in", values, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXBetween(Integer value1, Integer value2) {
            addCriterion("position_x between", value1, value2, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionXNotBetween(Integer value1, Integer value2) {
            addCriterion("position_x not between", value1, value2, "positionX");
            return (Criteria) this;
        }

        public Criteria andPositionYIsNull() {
            addCriterion("position_y is null");
            return (Criteria) this;
        }

        public Criteria andPositionYIsNotNull() {
            addCriterion("position_y is not null");
            return (Criteria) this;
        }

        public Criteria andPositionYEqualTo(Integer value) {
            addCriterion("position_y =", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYNotEqualTo(Integer value) {
            addCriterion("position_y <>", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYGreaterThan(Integer value) {
            addCriterion("position_y >", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYGreaterThanOrEqualTo(Integer value) {
            addCriterion("position_y >=", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYLessThan(Integer value) {
            addCriterion("position_y <", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYLessThanOrEqualTo(Integer value) {
            addCriterion("position_y <=", value, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYIn(List<Integer> values) {
            addCriterion("position_y in", values, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYNotIn(List<Integer> values) {
            addCriterion("position_y not in", values, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYBetween(Integer value1, Integer value2) {
            addCriterion("position_y between", value1, value2, "positionY");
            return (Criteria) this;
        }

        public Criteria andPositionYNotBetween(Integer value1, Integer value2) {
            addCriterion("position_y not between", value1, value2, "positionY");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(Integer value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(Integer value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(Integer value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(Integer value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(Integer value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<Integer> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<Integer> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(Integer value1, Integer value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andAnimationsIsNull() {
            addCriterion("animations is null");
            return (Criteria) this;
        }

        public Criteria andAnimationsIsNotNull() {
            addCriterion("animations is not null");
            return (Criteria) this;
        }

        public Criteria andAnimationsEqualTo(String value) {
            addCriterion("animations =", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsNotEqualTo(String value) {
            addCriterion("animations <>", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsGreaterThan(String value) {
            addCriterion("animations >", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsGreaterThanOrEqualTo(String value) {
            addCriterion("animations >=", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsLessThan(String value) {
            addCriterion("animations <", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsLessThanOrEqualTo(String value) {
            addCriterion("animations <=", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsLike(String value) {
            addCriterion("animations like", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsNotLike(String value) {
            addCriterion("animations not like", value, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsIn(List<String> values) {
            addCriterion("animations in", values, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsNotIn(List<String> values) {
            addCriterion("animations not in", values, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsBetween(String value1, String value2) {
            addCriterion("animations between", value1, value2, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationsNotBetween(String value1, String value2) {
            addCriterion("animations not between", value1, value2, "animations");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqIsNull() {
            addCriterion("animation_seq is null");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqIsNotNull() {
            addCriterion("animation_seq is not null");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqEqualTo(Integer value) {
            addCriterion("animation_seq =", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqNotEqualTo(Integer value) {
            addCriterion("animation_seq <>", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqGreaterThan(Integer value) {
            addCriterion("animation_seq >", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqGreaterThanOrEqualTo(Integer value) {
            addCriterion("animation_seq >=", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqLessThan(Integer value) {
            addCriterion("animation_seq <", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqLessThanOrEqualTo(Integer value) {
            addCriterion("animation_seq <=", value, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqIn(List<Integer> values) {
            addCriterion("animation_seq in", values, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqNotIn(List<Integer> values) {
            addCriterion("animation_seq not in", values, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqBetween(Integer value1, Integer value2) {
            addCriterion("animation_seq between", value1, value2, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andAnimationSeqNotBetween(Integer value1, Integer value2) {
            addCriterion("animation_seq not between", value1, value2, "animationSeq");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andImageTypeIsNull() {
            addCriterion("image_type is null");
            return (Criteria) this;
        }

        public Criteria andImageTypeIsNotNull() {
            addCriterion("image_type is not null");
            return (Criteria) this;
        }

        public Criteria andImageTypeEqualTo(Integer value) {
            addCriterion("image_type =", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotEqualTo(Integer value) {
            addCriterion("image_type <>", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeGreaterThan(Integer value) {
            addCriterion("image_type >", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_type >=", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeLessThan(Integer value) {
            addCriterion("image_type <", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("image_type <=", value, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeIn(List<Integer> values) {
            addCriterion("image_type in", values, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotIn(List<Integer> values) {
            addCriterion("image_type not in", values, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeBetween(Integer value1, Integer value2) {
            addCriterion("image_type between", value1, value2, "imageType");
            return (Criteria) this;
        }

        public Criteria andImageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("image_type not between", value1, value2, "imageType");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlIsNull() {
            addCriterion("top_image_url is null");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlIsNotNull() {
            addCriterion("top_image_url is not null");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlEqualTo(String value) {
            addCriterion("top_image_url =", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlNotEqualTo(String value) {
            addCriterion("top_image_url <>", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlGreaterThan(String value) {
            addCriterion("top_image_url >", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("top_image_url >=", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlLessThan(String value) {
            addCriterion("top_image_url <", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlLessThanOrEqualTo(String value) {
            addCriterion("top_image_url <=", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlLike(String value) {
            addCriterion("top_image_url like", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlNotLike(String value) {
            addCriterion("top_image_url not like", value, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlIn(List<String> values) {
            addCriterion("top_image_url in", values, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlNotIn(List<String> values) {
            addCriterion("top_image_url not in", values, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlBetween(String value1, String value2) {
            addCriterion("top_image_url between", value1, value2, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageUrlNotBetween(String value1, String value2) {
            addCriterion("top_image_url not between", value1, value2, "topImageUrl");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5IsNull() {
            addCriterion("top_image_md5 is null");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5IsNotNull() {
            addCriterion("top_image_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5EqualTo(String value) {
            addCriterion("top_image_md5 =", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5NotEqualTo(String value) {
            addCriterion("top_image_md5 <>", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5GreaterThan(String value) {
            addCriterion("top_image_md5 >", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5GreaterThanOrEqualTo(String value) {
            addCriterion("top_image_md5 >=", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5LessThan(String value) {
            addCriterion("top_image_md5 <", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5LessThanOrEqualTo(String value) {
            addCriterion("top_image_md5 <=", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5Like(String value) {
            addCriterion("top_image_md5 like", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5NotLike(String value) {
            addCriterion("top_image_md5 not like", value, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5In(List<String> values) {
            addCriterion("top_image_md5 in", values, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5NotIn(List<String> values) {
            addCriterion("top_image_md5 not in", values, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5Between(String value1, String value2) {
            addCriterion("top_image_md5 between", value1, value2, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andTopImageMd5NotBetween(String value1, String value2) {
            addCriterion("top_image_md5 not between", value1, value2, "topImageMd5");
            return (Criteria) this;
        }

        public Criteria andLinerColorIsNull() {
            addCriterion("liner_color is null");
            return (Criteria) this;
        }

        public Criteria andLinerColorIsNotNull() {
            addCriterion("liner_color is not null");
            return (Criteria) this;
        }

        public Criteria andLinerColorEqualTo(String value) {
            addCriterion("liner_color =", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorNotEqualTo(String value) {
            addCriterion("liner_color <>", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorGreaterThan(String value) {
            addCriterion("liner_color >", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorGreaterThanOrEqualTo(String value) {
            addCriterion("liner_color >=", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorLessThan(String value) {
            addCriterion("liner_color <", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorLessThanOrEqualTo(String value) {
            addCriterion("liner_color <=", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorLike(String value) {
            addCriterion("liner_color like", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorNotLike(String value) {
            addCriterion("liner_color not like", value, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorIn(List<String> values) {
            addCriterion("liner_color in", values, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorNotIn(List<String> values) {
            addCriterion("liner_color not in", values, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorBetween(String value1, String value2) {
            addCriterion("liner_color between", value1, value2, "linerColor");
            return (Criteria) this;
        }

        public Criteria andLinerColorNotBetween(String value1, String value2) {
            addCriterion("liner_color not between", value1, value2, "linerColor");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthIsNull() {
            addCriterion("top_image_width is null");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthIsNotNull() {
            addCriterion("top_image_width is not null");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthEqualTo(Integer value) {
            addCriterion("top_image_width =", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthNotEqualTo(Integer value) {
            addCriterion("top_image_width <>", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthGreaterThan(Integer value) {
            addCriterion("top_image_width >", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("top_image_width >=", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthLessThan(Integer value) {
            addCriterion("top_image_width <", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthLessThanOrEqualTo(Integer value) {
            addCriterion("top_image_width <=", value, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthIn(List<Integer> values) {
            addCriterion("top_image_width in", values, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthNotIn(List<Integer> values) {
            addCriterion("top_image_width not in", values, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthBetween(Integer value1, Integer value2) {
            addCriterion("top_image_width between", value1, value2, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("top_image_width not between", value1, value2, "topImageWidth");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightIsNull() {
            addCriterion("top_image_height is null");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightIsNotNull() {
            addCriterion("top_image_height is not null");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightEqualTo(Integer value) {
            addCriterion("top_image_height =", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightNotEqualTo(Integer value) {
            addCriterion("top_image_height <>", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightGreaterThan(Integer value) {
            addCriterion("top_image_height >", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("top_image_height >=", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightLessThan(Integer value) {
            addCriterion("top_image_height <", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightLessThanOrEqualTo(Integer value) {
            addCriterion("top_image_height <=", value, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightIn(List<Integer> values) {
            addCriterion("top_image_height in", values, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightNotIn(List<Integer> values) {
            addCriterion("top_image_height not in", values, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightBetween(Integer value1, Integer value2) {
            addCriterion("top_image_height between", value1, value2, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andTopImageHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("top_image_height not between", value1, value2, "topImageHeight");
            return (Criteria) this;
        }

        public Criteria andImageWidthIsNull() {
            addCriterion("image_width is null");
            return (Criteria) this;
        }

        public Criteria andImageWidthIsNotNull() {
            addCriterion("image_width is not null");
            return (Criteria) this;
        }

        public Criteria andImageWidthEqualTo(Integer value) {
            addCriterion("image_width =", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotEqualTo(Integer value) {
            addCriterion("image_width <>", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthGreaterThan(Integer value) {
            addCriterion("image_width >", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_width >=", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthLessThan(Integer value) {
            addCriterion("image_width <", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthLessThanOrEqualTo(Integer value) {
            addCriterion("image_width <=", value, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthIn(List<Integer> values) {
            addCriterion("image_width in", values, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotIn(List<Integer> values) {
            addCriterion("image_width not in", values, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthBetween(Integer value1, Integer value2) {
            addCriterion("image_width between", value1, value2, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("image_width not between", value1, value2, "imageWidth");
            return (Criteria) this;
        }

        public Criteria andImageHeightIsNull() {
            addCriterion("image_height is null");
            return (Criteria) this;
        }

        public Criteria andImageHeightIsNotNull() {
            addCriterion("image_height is not null");
            return (Criteria) this;
        }

        public Criteria andImageHeightEqualTo(Integer value) {
            addCriterion("image_height =", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotEqualTo(Integer value) {
            addCriterion("image_height <>", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightGreaterThan(Integer value) {
            addCriterion("image_height >", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_height >=", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightLessThan(Integer value) {
            addCriterion("image_height <", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightLessThanOrEqualTo(Integer value) {
            addCriterion("image_height <=", value, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightIn(List<Integer> values) {
            addCriterion("image_height in", values, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotIn(List<Integer> values) {
            addCriterion("image_height not in", values, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightBetween(Integer value1, Integer value2) {
            addCriterion("image_height between", value1, value2, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andImageHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("image_height not between", value1, value2, "imageHeight");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(Integer value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(Integer value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(Integer value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(Integer value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(Integer value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<Integer> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<Integer> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(Integer value1, Integer value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}