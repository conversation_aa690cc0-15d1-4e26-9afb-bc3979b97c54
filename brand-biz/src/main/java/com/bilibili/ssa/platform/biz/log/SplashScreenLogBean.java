package com.bilibili.ssa.platform.biz.log;

import com.bilibili.enums.PlatformType;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.common.enums.SsaJumpType;
import com.bilibili.ssa.platform.common.enums.BaseImageTypeEnum;
import com.bilibili.ssa.platform.common.enums.SsaLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(ssaLogFlag = SsaLogFlag.SPLASH_SCREEN_NEW)
public class SplashScreenLogBean implements Serializable {
    private static final long serialVersionUID = -4386575865244147215L;

    @Data
    public static class SplashScreenBaseImageLogBean implements Serializable {

		private static final long serialVersionUID = 2747419531675019389L;

        private Integer type;

        private String url;

        @Override
        public String toString() {
        	return String.format("%s:%s", BaseImageTypeEnum.getByCode(this.type).getDesc(), this.url);
        }
    }
    
    @Data
    public static class SplashScreenVersionLogBean implements Serializable {

		private static final long serialVersionUID = 5100474775501783671L;

        private Integer startVersion;

        private Integer endVersion;

        @Override
        public String toString() {
        	return String.format("%s:%s", this.startVersion, this.endVersion);
        }
    }

    @Data
    public static class ScheduleSplashScreenMappingLogBean implements Serializable {

        private static final long serialVersionUID = 5100474775501783671L;

        //投放开始时间
        private Timestamp beginTime;

        private Timestamp endTime;

        @Override
        public String toString() {
            return String.format("%s-%s", TimeUtil.timestampToIsoTimeStr(this.beginTime),
                    TimeUtil.timestampToIsoTimeStr(this.endTime));
        }
    }

    @Data
    public static class ScheduleSplashScreenButtonLogBean implements Serializable {

        private static final long serialVersionUID = 2447753149757357484L;

        /**
         * 引导说明
         */
        private String guideInstructions;

        @Override
        public String toString() {
            return String.format("%s", this.guideInstructions);
        }
    }

    @Data
    public static class ScheduleSplashScreenJumpInfoLogBean implements Serializable {

        private static final long serialVersionUID = 7744610229359208893L;


        /**
         * 平台编号: 1-iPhone 2-Android 3-iPad
         */
        private Integer platformId;

        /**
         * 跳转类型: 1-链接 2-视频 3-游戏 4-番剧 5-直播 6-游戏中心
         */
        private Integer jumpType;

        /**
         * 跳转链接
         */
        private String jumpLink;

        /**
         * 跳转链接
         */
        private String schemeUrl;

        @Override
        public String toString() {
            return String.format("%s %s link:%s   schema:%s", PlatformType.getByCode(platformId).getDesc(),
                    SsaJumpType.getByCode(jumpType).getDesc(), jumpLink, schemeUrl);
        }
    }

    @LogProperty("订单id")
    private Integer ssaOrderId;
    
    @LogProperty("业务方")
    private String businessSideName;

    @LogProperty("闪屏标题")
    private String title;

    @LogProperty("闪屏文案")
    private String copywriting;
    
    @LogProperty("闪屏样式")
    private String showStyle;

    @LogProperty("是否可跳过")
    private String isSkip;

    @LogProperty("下发时间")
    private String issuedTime;

    @LogProperty("广告角标")
    private String cmMark;

    @LogProperty("状态")
    private Integer status;

    @LogProperty("状态描述")
    private String status_desc;

    @LogProperty("驳回原因")
    private String rejectReason;
    
    @LogProperty("基础图片")
    private ListLogBean<SplashScreenBaseImageLogBean, SsaNewSplashScreenBaseImageDto> baseImageDtos;
    
    @LogProperty("版本控制")
    private ListLogBean<SplashScreenVersionLogBean, SsaNewSplashScreenVersionControlDto> ssaNewSplashScreenVersionControlDtos;

    @LogProperty("投放时间")
    private ListLogBean<ScheduleSplashScreenMappingLogBean, SsaNewScheduleSplashScreenMappingDto> ssaNewScheduleSplashScreenMappingDtos;

    @LogProperty("按钮文案")
    private ListLogBean<ScheduleSplashScreenButtonLogBean, SsaButtonDto> ssaButtonDtos;

    @LogProperty("跳转信息")
    private ListLogBean<ScheduleSplashScreenJumpInfoLogBean, SplashScreenJumpDTO> ssaJumpDTOs;
}
