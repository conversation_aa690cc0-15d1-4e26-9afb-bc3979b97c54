package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.TopViewBusinessSideRotationConfigPo;
import com.bilibili.ssa.platform.biz.po.TopViewBusinessSideRotationConfigPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface TopViewBusinessSideRotationConfigDao {
    long countByExample(TopViewBusinessSideRotationConfigPoExample example);

    int deleteByExample(TopViewBusinessSideRotationConfigPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(TopViewBusinessSideRotationConfigPo record);

    int insertBatch(List<TopViewBusinessSideRotationConfigPo> records);

    int insertUpdateBatch(List<TopViewBusinessSideRotationConfigPo> records);

    int insert(TopViewBusinessSideRotationConfigPo record);

    int insertUpdateSelective(TopViewBusinessSideRotationConfigPo record);

    int insertSelective(TopViewBusinessSideRotationConfigPo record);

    List<TopViewBusinessSideRotationConfigPo> selectByExample(TopViewBusinessSideRotationConfigPoExample example);

    TopViewBusinessSideRotationConfigPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TopViewBusinessSideRotationConfigPo record, @Param("example") TopViewBusinessSideRotationConfigPoExample example);

    int updateByExample(@Param("record") TopViewBusinessSideRotationConfigPo record, @Param("example") TopViewBusinessSideRotationConfigPoExample example);

    int updateByPrimaryKeySelective(TopViewBusinessSideRotationConfigPo record);

    int updateByPrimaryKey(TopViewBusinessSideRotationConfigPo record);
}