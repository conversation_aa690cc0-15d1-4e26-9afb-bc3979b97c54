package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class SsaScheduleTargetPo implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 闪屏排期id
     */
    private Integer scheduleGroupId;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 定向类型
     */
    private Integer targetType;

    /**
     * 定向项ID列表, 逗号分隔
     */
    private String targetItemIds;

    /**
     * 软删除:0-有效,1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleGroupId() {
        return scheduleGroupId;
    }

    public void setScheduleGroupId(Integer scheduleGroupId) {
        this.scheduleGroupId = scheduleGroupId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public String getTargetItemIds() {
        return targetItemIds;
    }

    public void setTargetItemIds(String targetItemIds) {
        this.targetItemIds = targetItemIds;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}