package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaSplashScreenJumpInfoDao {
    long countByExample(SsaSplashScreenJumpInfoPoExample example);

    int deleteByExample(SsaSplashScreenJumpInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaSplashScreenJumpInfoPo record);

    int insertBatch(List<SsaSplashScreenJumpInfoPo> records);

    int insertUpdateBatch(List<SsaSplashScreenJumpInfoPo> records);

    int insert(SsaSplashScreenJumpInfoPo record);

    int insertUpdateSelective(SsaSplashScreenJumpInfoPo record);

    int insertSelective(SsaSplashScreenJumpInfoPo record);

    List<SsaSplashScreenJumpInfoPo> selectByExample(SsaSplashScreenJumpInfoPoExample example);

    SsaSplashScreenJumpInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaSplashScreenJumpInfoPo record, @Param("example") SsaSplashScreenJumpInfoPoExample example);

    int updateByExample(@Param("record") SsaSplashScreenJumpInfoPo record, @Param("example") SsaSplashScreenJumpInfoPoExample example);

    int updateByPrimaryKeySelective(SsaSplashScreenJumpInfoPo record);

    int updateByPrimaryKey(SsaSplashScreenJumpInfoPo record);
}