
package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.mas.common.utils.BeanHelper;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewScheduleSplashScreenMappingDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaScheduleSplashScreenMappingDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenSchduleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleSplashScreenMappingDao;
import com.bilibili.ssa.platform.biz.po.SsaScheduleSplashScreenMappingPo;
import com.bilibili.ssa.platform.biz.po.SsaScheduleSplashScreenMappingPoExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SsaSplashScreenSchduleService implements ISsaSplashScreenSchduleService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private SsaScheduleSplashScreenMappingDao ssaScheduleSplashScreenMappingDao;

    @Autowired
    private ISsaScheduleService ssaScheduleService;
    @Autowired
    private ISsaSplashScreenService ssaSplashScreenService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<SsaNewScheduleSplashScreenMappingDto> ssaNewScheduleSplashScreenMappingDtos) {

        LOGGER.info("SsaSplashScreenSchduleService.createCptSchedule param ssaNewScheduleSplashScreenMappingDtos -{}",
                ssaNewScheduleSplashScreenMappingDtos);

        Assert.notEmpty(ssaNewScheduleSplashScreenMappingDtos, "闪屏排期信息不可为空");

        List<SsaScheduleSplashScreenMappingPo> mappingPos = this
                .ssaNewScheduleSplashScreenMappingDtos2Pos(ssaNewScheduleSplashScreenMappingDtos);

        mappingPos.forEach(t->{
            ssaScheduleSplashScreenMappingDao.insertUpdateSelective(t);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(List<SsaNewScheduleSplashScreenMappingDto> dtos) {

        LOGGER.info("SsaSplashScreenSchduleService.update param SsaScheduleSplashScreenMappingDto -{}", dtos);

        Assert.notEmpty(dtos, "闪屏排期信息不可为空");

//        for (SsaNewScheduleSplashScreenMappingDto ssaScheduleSplashScreenMappingDto : dtos) {
//            Assert.notNull(ssaScheduleSplashScreenMappingDto.getScheduleId(), "排期ID不可为空");
//        }

        this.deletedBySplashScreenId(dtos.get(0).getSplashScreenId());

        this.batchSave(dtos);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deletedBySplashScreenId(Integer splashScreenId) {

        LOGGER.info("SsaSplashScreenSchduleService.deletedBysplashScreenId param splashScreenId -{}", splashScreenId);

        Assert.notNull(splashScreenId, "闪屏ID不可为空");

        SsaScheduleSplashScreenMappingPo po = SsaScheduleSplashScreenMappingPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andSplashScreenIdEqualTo(splashScreenId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ssaScheduleSplashScreenMappingDao.updateByExampleSelective(po, example);
    }

    @Override
    public void batchDeletedBySplashScreenId(List<Integer> splashScreenIds) {

        LOGGER.info("SsaSplashScreenSchduleService.batchDeletedBySplashScreenId param splashScreenIds -{}", splashScreenIds);

        Assert.notEmpty(splashScreenIds, "闪屏ID不可为空");

        SsaScheduleSplashScreenMappingPo po = SsaScheduleSplashScreenMappingPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andSplashScreenIdIn(splashScreenIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ssaScheduleSplashScreenMappingDao.updateByExampleSelective(po, example);
    }

    @Override
    public void deletedById(List<Integer> ids) {

        LOGGER.info("SsaSplashScreenSchduleService.deletedById param ids -{}", ids);

        Assert.notEmpty(ids, "闪屏和排期映射ID不可为空");

        SsaScheduleSplashScreenMappingPo po = SsaScheduleSplashScreenMappingPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build();

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andIdIn(ids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ssaScheduleSplashScreenMappingDao.updateByExampleSelective(po, example);

    }

    @Override
    public List<SsaScheduleSplashScreenMappingDto> getSplashScreenSchduleBySplashScreenId(Integer splashScreenId) {

        LOGGER.info("SsaSplashScreenSchduleService.getSplashScreenSchduleBySplashScreenId param splashScreenId -{}", splashScreenId);

        Assert.notNull(splashScreenId, "闪屏ID不可为空");

        SsaScheduleSplashScreenMappingPoExample ssaScheduleSplashScreenMappingPoExample = new SsaScheduleSplashScreenMappingPoExample();

        ssaScheduleSplashScreenMappingPoExample.or().andSplashScreenIdEqualTo(splashScreenId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> ssaScheduleSplashScreenMappingPos =
                ssaScheduleSplashScreenMappingDao.selectByExample(ssaScheduleSplashScreenMappingPoExample);

        if (CollectionUtils.isEmpty(ssaScheduleSplashScreenMappingPos)) {
            return Collections.emptyList();
        }

        return this.ssaScheduleSplashScreenMappingPos2Dtos(ssaScheduleSplashScreenMappingPos);
    }

    @Override
    public Map<Integer, List<SsaScheduleDto>> getSchduleDtoBySplashScreenIds(List<Integer> splashScreenIds) {

        LOGGER.info("SsaSplashScreenSchduleService.getSchduleIdsBySplashScreenIds param splashScreenIds -{}", splashScreenIds);

        Assert.notEmpty(splashScreenIds, "闪屏IDs不可为空");

        SsaScheduleSplashScreenMappingPoExample ssaScheduleSplashScreenMappingPoExample = new SsaScheduleSplashScreenMappingPoExample();

        ssaScheduleSplashScreenMappingPoExample.or().andSplashScreenIdIn(splashScreenIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> ssaScheduleSplashScreenMappingPos =
                ssaScheduleSplashScreenMappingDao.selectByExample(ssaScheduleSplashScreenMappingPoExample);
        if (CollectionUtils.isEmpty(ssaScheduleSplashScreenMappingPos)) {
            return Collections.emptyMap();
        }

        return ssaScheduleSplashScreenMappingPos.stream()
                .collect(Collectors.groupingBy(SsaScheduleSplashScreenMappingPo::getSplashScreenId,
                        Collectors.mapping(mappingPo -> {
                            SsaScheduleDto ssaScheduleRes = new SsaScheduleDto();
                            ssaScheduleRes.setSsaStartTime(mappingPo.getStartTime());
                            ssaScheduleRes.setSsaEndTime(mappingPo.getEndTime());
                            return ssaScheduleRes;
                        }, Collectors.toList())));
    }

    @Override
    public Map<Integer, SsaScheduleSplashScreenMappingDto> getSSaSplashScheduleBySplashScreenIds(List<Integer> splashScreenIds) {

        LOGGER.info("SsaSplashScreenSchduleService.getSchduleIdsBySplashScreenIds param splashScreenIds -{}", splashScreenIds);

        Assert.notEmpty(splashScreenIds, "闪屏IDs不可为空");

        SsaScheduleSplashScreenMappingPoExample ssaScheduleSplashScreenMappingPoExample = new SsaScheduleSplashScreenMappingPoExample();

        ssaScheduleSplashScreenMappingPoExample.or().andSplashScreenIdIn(splashScreenIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> ssaScheduleSplashScreenMappingPos =
                ssaScheduleSplashScreenMappingDao.selectByExample(ssaScheduleSplashScreenMappingPoExample);
        if (CollectionUtils.isEmpty(ssaScheduleSplashScreenMappingPos)) {
            return Collections.emptyMap();
        }

        Map<Integer, SsaScheduleSplashScreenMappingDto> mappingMap = new HashMap<>();
        for(SsaScheduleSplashScreenMappingPo mappingPo : ssaScheduleSplashScreenMappingPos){
            SsaScheduleSplashScreenMappingDto mappingDto = new SsaScheduleSplashScreenMappingDto();
            BeanUtils.copyProperties(mappingPo, mappingDto);
            mappingMap.put(mappingPo.getSplashScreenId(), mappingDto);
        }
        return mappingMap;
    }

    @Override
    public Map<Integer, SsaScheduleSplashScreenMappingDto> getAllSsaSplashScheduleBySplashScreenIds(List<Integer> splashScreenIds) {

        LOGGER.info("SsaSplashScreenSchduleService.getSchduleIdsBySplashScreenIds param splashScreenIds -{}", splashScreenIds);

        Assert.notEmpty(splashScreenIds, "闪屏IDs不可为空");

        SsaScheduleSplashScreenMappingPoExample ssaScheduleSplashScreenMappingPoExample = new SsaScheduleSplashScreenMappingPoExample();

        ssaScheduleSplashScreenMappingPoExample.or().andSplashScreenIdIn(splashScreenIds);

        List<SsaScheduleSplashScreenMappingPo> ssaScheduleSplashScreenMappingPos =
                ssaScheduleSplashScreenMappingDao.selectByExample(ssaScheduleSplashScreenMappingPoExample);
        if (CollectionUtils.isEmpty(ssaScheduleSplashScreenMappingPos)) {
            return Collections.emptyMap();
        }

        Map<Integer, SsaScheduleSplashScreenMappingDto> mappingMap = new HashMap<>();
        for(SsaScheduleSplashScreenMappingPo mappingPo : ssaScheduleSplashScreenMappingPos){
            SsaScheduleSplashScreenMappingDto mappingDto = new SsaScheduleSplashScreenMappingDto();
            BeanUtils.copyProperties(mappingPo, mappingDto);
            mappingMap.put(mappingPo.getSplashScreenId(), mappingDto);
        }
        return mappingMap;
    }



    @Override
    public Map<Integer, Timestamp> getMaxSchduleMapBySplashScreenIds(List<Integer> splashScreenIds) {

        LOGGER.info("SsaSplashScreenSchduleService.getMaxSchduleMapBySplashScreenIds param splashScreenIds -{}",
                splashScreenIds);

        Assert.notEmpty(splashScreenIds, "闪屏IDs不可为空");

        Map<Integer, List<SsaScheduleDto>> splashScreen2SchduleMap = this.getSchduleDtoBySplashScreenIds(splashScreenIds);

        return splashScreen2SchduleMap.keySet().stream()
                .collect(Collectors.toMap(Function.identity(),
                        key -> splashScreen2SchduleMap.get(key).stream()
                                .max(Comparator.comparing(SsaScheduleDto::getLaunchDate))
                                .orElse(new SsaScheduleDto()).getLaunchDate()));
    }

    @Override
    public List<SsaScheduleSplashScreenMappingDto> getSplashScreenSchduleByScheduleId(Integer scheduleId) {

        LOGGER.info("SsaSplashScreenSchduleService.getSplashScreenSchduleByScheduleId param ScheduleId -{}", scheduleId);

        Assert.notNull(scheduleId, "排期ID不可为空");

        SsaScheduleSplashScreenMappingPoExample ssaScheduleSplashScreenMappingPoExample = new SsaScheduleSplashScreenMappingPoExample();

        ssaScheduleSplashScreenMappingPoExample.or().andScheduleIdEqualTo(scheduleId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> ssaScheduleSplashScreenMappingPos = ssaScheduleSplashScreenMappingDao
                .selectByExample(ssaScheduleSplashScreenMappingPoExample);

        if (CollectionUtils.isEmpty(ssaScheduleSplashScreenMappingPos)) {
            return Collections.emptyList();
        }

        return this.ssaScheduleSplashScreenMappingPos2Dtos(ssaScheduleSplashScreenMappingPos);
    }

    @Override
    public List<SsaScheduleSplashScreenMappingDto> getSplashScreenSchdulesInScheduleIds(List<Integer> scheduleIds) {

        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andScheduleIdIn(scheduleIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> pos = ssaScheduleSplashScreenMappingDao.selectByExample(example);

        return CollectionUtils.isEmpty(pos) ? Collections.emptyList() : this.ssaScheduleSplashScreenMappingPos2Dtos(pos);
    }
    @Override
    public List<SsaScheduleSplashScreenMappingDto> getSplashScreenSchdulesInGdScheduleIds(List<Integer> gdScheduleIds) {

        if (CollectionUtils.isEmpty(gdScheduleIds)) {
            return Collections.emptyList();
        }

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andGdScheduleIdIn(gdScheduleIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> pos = ssaScheduleSplashScreenMappingDao.selectByExample(example);

        return CollectionUtils.isEmpty(pos) ? Collections.emptyList() : this.ssaScheduleSplashScreenMappingPos2Dtos(pos);
    }


    @Override
    public Map<Integer, Long> getSchduleId2SplashScreenCountMapInScheduleIds(List<Integer> scheduleIds) {

        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyMap();
        }

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andScheduleIdIn(scheduleIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> pos = ssaScheduleSplashScreenMappingDao.selectByExample(example);

        return CollectionUtils.isEmpty(pos) ? Collections.emptyMap() : pos.stream().collect(Collectors.groupingBy(SsaScheduleSplashScreenMappingPo::getScheduleId, Collectors.counting()));
    }

    @Override
    public void deletedByScheduleId(Integer scheduleId, Operator operator) {

        LOGGER.info("SsaSplashScreenSchduleService.deletedByScheduleId param ScheduleId -{}", scheduleId);

        Assert.notNull(scheduleId, "排期ID不可为空");

        List<SsaScheduleSplashScreenMappingDto> ssaScheduleSplashScreenMappingDtos = this.getSplashScreenSchduleByScheduleId(scheduleId);

        List<Integer> ids = new ArrayList<>();

        List<Integer> splashScreenIds = new ArrayList<>();

        for (SsaScheduleSplashScreenMappingDto dto : ssaScheduleSplashScreenMappingDtos) {
            ids.add(dto.getId());
            splashScreenIds.add(dto.getSplashScreenId());
        }

        this.deletedById(ids);

        for (Integer splashScreenId : splashScreenIds) {
            if (CollectionUtils.isEmpty(this.getSplashScreenSchduleBySplashScreenId(splashScreenId))) {
                ssaSplashScreenService.deleted(splashScreenId, operator);
            }
        }

    }

    @Override
    public void deletedInIds(List<Integer> ids, Operator operator) {

        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notEmpty(ids, "映射ID不可为空");

        SsaScheduleSplashScreenMappingPo po = SsaScheduleSplashScreenMappingPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build();

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andIdIn(ids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        ssaScheduleSplashScreenMappingDao.updateByExampleSelective(po, example);

    }

    @Override
    public List<SsaScheduleSplashScreenMappingDto> getSplashScreenSchduleInSplashScreenIds(List<Integer> splashScreenIds) {
        if (CollectionUtils.isEmpty(splashScreenIds)) {
            return Collections.emptyList();
        }

        SsaScheduleSplashScreenMappingPoExample example = new SsaScheduleSplashScreenMappingPoExample();

        example.or().andSplashScreenIdIn(splashScreenIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<SsaScheduleSplashScreenMappingPo> pos = ssaScheduleSplashScreenMappingDao.selectByExample(example);

        return CollectionUtils.isEmpty(pos) ? Collections.emptyList() : this.ssaScheduleSplashScreenMappingPos2Dtos(pos);
    }

    private SsaScheduleSplashScreenMappingDto ssaScheduleSplashScreenMappingPo2Dto(SsaScheduleSplashScreenMappingPo po) {
        return SsaScheduleSplashScreenMappingDto.builder()
                .startTime(po.getStartTime())
                .endTime(po.getEndTime())
                .splashScreenId(po.getSplashScreenId())
                .scheduleId(po.getScheduleId())
                .gdScheduleId(po.getGdScheduleId())
                .id(po.getId())
                .build();

    }

    private List<SsaScheduleSplashScreenMappingDto> ssaScheduleSplashScreenMappingPos2Dtos(List<SsaScheduleSplashScreenMappingPo> pos) {
        return pos.stream().map(this::ssaScheduleSplashScreenMappingPo2Dto).collect(Collectors.toList());
    }

    private List<SsaScheduleSplashScreenMappingPo> ssaNewScheduleSplashScreenMappingDtos2Pos(List<SsaNewScheduleSplashScreenMappingDto> dtos) {
        return dtos.stream().map(this::ssaNewScheduleSplashScreenMappingDto2Po).collect(Collectors.toList());
    }

    private SsaScheduleSplashScreenMappingPo ssaNewScheduleSplashScreenMappingDto2Po(SsaNewScheduleSplashScreenMappingDto dto) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        return SsaScheduleSplashScreenMappingPo.builder()
                .scheduleId(dto.getScheduleId())
                .splashScreenId(dto.getSplashScreenId())
                .startTime(dto.getBeginTime())
                .endTime(dto.getEndTime())
                .gdScheduleId(dto.getGdScheduleId())
                .ctime(now)
                .isDeleted(IsDeleted.VALID.getCode())
                .mtime(now)
                .build();
    }
}
