package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.SsaLinkageType;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.crm.platform.common.IsInnerEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/9/21
 */
@Service
public class SsaCreativeCalledUpService {

    @Autowired
    private IQueryAccountService accountService;

    public boolean supportCalledUp(GdOrderDto orderDto, ScheduleDto scheduleDto) {
        Integer accountId = orderDto.getAccountId();

        boolean isOuter = IsInnerEnum.OUTER.getCode().equals(accountService.getAccount(accountId).getIsInner());

        Integer orderProduct = scheduleDto.getOrderProduct();
        boolean isTopView = OrderProduct.TOPVIEW_CODE_SET.contains(orderProduct);
        boolean isSsaStory = SsaVideoPlayModeEnum.isArchive(scheduleDto.getSsaVideoPlayMode());
        boolean isSsaSearch = SsaLinkageType.SEARCH.getCode().equals(scheduleDto.getLinkageType());

        return !(isTopView || isSsaSearch || isSsaStory) && isOuter;
    }
}
