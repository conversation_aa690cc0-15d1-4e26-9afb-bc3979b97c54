package com.bilibili.ssa.platform.biz.component;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.enums.AppPlatformType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.CommonValidator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.resource.api.app_awaken.dto.AwakenAppWhitelistDto;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.adp.resource.api.soa.ISoaAwakenAppWhitelistService;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.biz.material.IPVideoService;
import com.bilibili.enums.PlatformType;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.utils.SsaConfigUtil;
import com.bilibili.cpt.platform.common.ButtonInteractStyleEnum;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.common.ContractStatus;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.enums.WakeAppType;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.api.splash_screen.dto.second_page.SsaSecondPageInteractEggDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenVideoService;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.bilibili.ssa.platform.biz.service.schedule.SsaScheduleService;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.sql.Timestamp;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.common.enums.SsaConstants.MONITOR_URL_SCHEME_HTTPS;


/**
 * <AUTHOR>
 * @date 2018年03月14日
 */
@SuppressWarnings("Duplicates")
@Component
public class SplashScreenValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(SplashScreenValidator.class);

    private static final int MAX_URL_LENGTH = 5096;
    private static final int MAX_TEXT_LENGTH_32 = 32;
    private static final int MAX_TEXT_LENGTH_128 = 128;

    private static final List<Integer> CAN_CREATE_SPLASH_SCREEN_CONTRACT_STATUS_LIST =
            Arrays.asList(ContractStatus.AUDITED.getCode(), ContractStatus.EXECUTABLE.getCode(), ContractStatus.EXECUTE.getCode(), ContractStatus.RECEIVABLE.getCode());

    @Autowired
    private SsaScheduleService ssaScheduleService;
    @Autowired
    private ISoaAwakenAppWhitelistService awakenAppWhitelistService;
    @Autowired
    private SsaSplashScreenServiceDelegate ssaSplashScreenServiceDelegate;
    @Autowired
    private ISoaCrmContractService contractService;
    @Autowired
    private ISoaAppPackageService soaAppPackageService;
    @Autowired
    private ISsaUposVideoService ssaUposVideoService;
    @Autowired
    private IWakeUpService wakeUpService;

    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private SsaConfigUtil ssaConfigUtil;

    @Autowired
    private IPVideoService ipVideoService;

    @Autowired
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;

    public void validateNewExternalSsaBasicInfo(Operator operator, SsaNewExternalSplashScreenDto dto,
                                                final GdOrderDto orderDto, ScheduleDto scheduleDto) {

        LOGGER.info("validateNewExternalSsaBasicInfo operator:[{}], SsaNewExternalSplashScreenDto: [{}], orderDto: [{}].", operator, dto, orderDto);

        Assert.isTrue(!Operator.validateParamIsNull(operator));

        Assert.isTrue(CollectionUtils.isEmpty(ssaSplashScreenServiceDelegate.getSsaSplashScreens(QuerySplashScreenParamDto.builder()
                .accountId(operator.getOperatorId())
                .statusNotInList(Collections.singletonList(SsaSplashScreenStatus.DELETED.getCode()))
                .title(dto.getTitle()).build())), "存在重复的闪屏创意名称，请修改后提交");

        ContractDto contract = null;
        if (Utils.isPositive(orderDto.getCrmContractId())) {
            contract = contractService.getContractById(orderDto.getCrmContractId());
            // 去除合同审核 https://www.tapd.cn/********/prong/stories/view/11********004235692
//            Assert.isTrue(CAN_CREATE_SPLASH_SCREEN_CONTRACT_STATUS_LIST.contains(contract.getStatus()),
//                    "crm合同未审核通过, 不可新建闪屏");
            Assert.notNull(contract, "合同信息不存在");
        }


        Assert.notNull(dto, "闪屏信息不可为空");
        Assert.notNull(dto.getType(), "闪屏类型不可为空");
        Assert.hasText(dto.getTitle(), "闪屏标题不可为空");
        Assert.isTrue(dto.getTitle().length() <= MAX_TEXT_LENGTH_32, "闪屏标题长度最长限制为" + MAX_TEXT_LENGTH_32);

        Assert.notNull(dto.getShowStyle(), "展示样式不可为空");

        boolean isSsaArchive = SsaVideoPlayModeEnum.isArchive(scheduleDto.getSsaVideoPlayMode());

        if (!OrderProduct.COMIC_SSA_CPT.getCode().equals(dto.getOrderProduct())
                && !SsaConstants.TOP_VIEW_SALES_TYPES.contains(dto.getSalesType())
                || (!dto.getShowStyle().equals(SsaShowStyleType.VERTICAL_SCREEN_VIDEO.getCode())
                && !dto.getShowStyle().equals(SsaShowStyleType.FULL_SCREEN_VERTICAL_SCREEN_VIDEO.getCode()))) {
            if (!isSsaArchive) {
                Assert.notEmpty(dto.getBaseImageDtos(), "闪屏基础图片不可为空");
            }
        }

        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        if (!CollectionUtils.isEmpty(dto.getBaseImageDtos())) {
            dto.getBaseImageDtos().forEach(bi -> this.validateBaseImage(ssaShowStyleType, bi.getType(), bi.getUrl(), bi.getHash()));
        }

        if (!isSsaArchive && ssaShowStyleType.isVideo()) {
            // 校验闪屏视频
            this.validateNewSplashScreenVideo(scheduleDto, ssaShowStyleType, dto.getVideoDto());
            if (Objects.equals(scheduleDto.getSsaVideoPlayMode(), SsaVideoPlayModeEnum.EASTER_EGG.getCode())) {
                Assert.notNull(dto.getEggVideoDto(), "【浮窗彩蛋视频】播放形式必须选择彩蛋视频");
                Assert.notNull(dto.getEggVideoDto().getBizId(), "彩蛋视频ID不能为空");
                IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(dto.getEggVideoDto().getBizId());
                Assert.notNull(ipVideoBo, "彩蛋视频不合法");
                // 注释的状态貌似有问题，看了下IP视频库应该和这个枚举对应
                SsaSplashScreenVideoStatus videoStatus = SsaSplashScreenVideoStatus.getByCode(ipVideoBo.getDealStatus());
                Assert.state(!SsaSplashScreenVideoStatus.TRANS_CODING.equals(videoStatus), "视频转码中，请稍后重试");
                Assert.isTrue(SsaSplashScreenVideoStatus.TRANS_SUCCESS.equals(videoStatus), "视频转码失败，请核实视频是否符合规范");
            }
        }

        //校验跳转信息和监控信息
        this.checkCustomizedAndJumpInfo(dto, operator, contract, scheduleDto);

        if (!Strings.isNullOrEmpty(dto.getCopywriting())) {
            Assert.isTrue(dto.getCopywriting().length() <= MAX_TEXT_LENGTH_32, "文案长度最长限制为" + MAX_TEXT_LENGTH_32);
        }

        Assert.notNull(dto.getIsSkip(), "是否跳过不可为空");
        Assert.notNull(dto.getIssuedTime(), "闪屏下发时间不可为空");

        Assert.notEmpty(dto.getSsaNewScheduleSplashScreenMappingDtos(), "闪屏排期不可为空");

        if (ButtonInteractStyleEnum.SLIDE.getCode().equals(dto.getInteractStyle()) &&
                SsaAdType.IMAGE.getCode().equals(ssaShowStyleType.getAdType().getCode())) {
            Assert.isTrue(dto.getIsSupportButtonToInteract(), "互动闪屏的图文按钮必须要选用动效按钮");
        }

        if (OrderProduct.SSA_CPM.getCode().equals(orderDto.getProduct())) {
            SsaSplashScreenPoExample poExample = new SsaSplashScreenPoExample();
            poExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andStatusNotEqualTo(SsaSplashScreenStatus.DELETED.getCode())
                    .andGdScheduleIdEqualTo(dto.getScheduleId());
            long count = ssaSplashScreenDao.countByExample(poExample);

            String maxCreatives = systemConfigService.getValueByItemEnum(SystemConfigEnum.SSA_CPM_MAX_CREATIVE_NUMS);
            Assert.notNull(maxCreatives, "闪屏cpm最大创意个数配置不能为空");
            int maxCreativeNums = Integer.parseInt(maxCreatives);
            Assert.isTrue(count + 1 <= maxCreativeNums, "闪屏cpm创建的创意不能超过"
                    + maxCreativeNums + "个!");
        }

        this.validateNewVersionControl(dto.getSsaNewSplashScreenVersionControlDtos(), ssaShowStyleType);

        //验证唤起
        if (WakeAppType.isMiniProgram(scheduleDto.getWakeAppType())) {
            Assert.isTrue(Objects.nonNull(dto.getMiniProgram()) && dto.getMiniProgram().isValid(), "小程序信息不能为空");
        }

        Integer buttonStyle = scheduleDto.getButtonStyle();
        //【品牌】闪屏样式拓展--品牌卡片闪屏
        //https://www.tapd.cn/********/prong/stories/view/11********004200276
        //前端也会校验的，因此此处仅仅是兜底
        if (SsaButtonStyle.isBrandCardButton(buttonStyle)) {
            dto.getButtonBOS().forEach(button -> {
                Assert.isTrue(StringUtils.hasText(button.getLogoImageUrl())
                        && StringUtils.hasText(button.getLogoImageHash()), "品牌卡logo图片必传");
                Assert.isTrue(StringUtils.hasText(button.getBrandCardTitle()), "品牌卡标题必传");
                Assert.isTrue(StringUtils.hasText(button.getBrandCardDesc()), "品牌卡描述必传");
            });
        }

        if (SsaButtonStyle.isEggButton(buttonStyle)) {
            // 点击彩蛋 需要有两个彩蛋素材
            Assert.isTrue(dto.getBaseImageDtos().stream().anyMatch(image -> Objects.equals(BaseImageTypeEnum.CLICK_EGG_IMAGE_1.getCode(), image.getType())), "点击彩蛋缺少彩蛋素材1");
            Assert.isTrue(dto.getBaseImageDtos().stream().anyMatch(image -> Objects.equals(BaseImageTypeEnum.CLICK_EGG_IMAGE_2.getCode(), image.getType())), "点击彩蛋缺少彩蛋素材2");
        }

        this.validateMiddlePage(dto.getMiddlePage(), scheduleDto);
        this.validateInteractEgg(dto.getInteractEgg(), scheduleDto);
    }

    private void validateVersionControl(boolean isVideo, List<SsaSplashScreenVersionControlDto> versionControls) {
        if (isVideo) {
            Assert.notEmpty(versionControls, "版本控制不可为空");
            versionControls.forEach(vc -> {
                PlatformType platformType = PlatformType.getByCode(vc.getPlatformId());
                Assert.notNull(vc.getStartVersion(), platformType.getDesc() + "起始版本不可为空");
                Assert.notNull(vc.getEndVersion(), platformType.getDesc() + "截止版本不可为空");
                Assert.isTrue(vc.getStartVersion().compareTo(vc.getEndVersion()) <= 0, platformType.getDesc() + "版本控制超过最大值");
            });
        } else if (!CollectionUtils.isEmpty(versionControls)) {
            versionControls.forEach(vc -> {
                PlatformType platformType = PlatformType.getByCode(vc.getPlatformId());
                Assert.notNull(vc.getStartVersion(), platformType.getDesc() + "起始版本不可为空");
                Assert.notNull(vc.getEndVersion(), platformType.getDesc() + "截止版本不可为空");
                Assert.isTrue(vc.getStartVersion().compareTo(vc.getEndVersion()) <= 0, platformType.getDesc() + "版本控制超过最大值");
            });
        }
    }

    public Map<Integer, Integer> validateAppPackageAndGetPlatformType2AppPackageIdMap(
            Integer crmAccountId, SsaJumpType jumpType, List<Integer> appPackageIds, Integer salesType) {
        Map<Integer, Integer> platformId2AppPackageIdMap = Maps.newHashMap();
        appPackageIds = Optional.ofNullable(appPackageIds)
                .orElse(Lists.newArrayList())
                .stream().filter(Utils::isPositive).collect(Collectors.toList());
        if (SsaJumpType.H5_DOWNLOAD.equals(jumpType) && !CollectionUtils.isEmpty(appPackageIds)) {
            Assert.notNull(crmAccountId, "商业账号ID不可为空");
            Assert.notEmpty(appPackageIds, "应用包下载ID不可为空");
            Map<Integer, AppPackageDto> appPackageMap = soaAppPackageService.getAllMapInPrimaryKeys(appPackageIds);
            appPackageIds.forEach(appId -> {
                AppPackageDto appPackageDto = appPackageMap.get(appId);
                Assert.notNull(appPackageDto, "存在无效的APPId");
                Assert.isTrue(crmAccountId.equals(appPackageDto.getAccountId()), "该AppId:[" + appId + "]不属于您");
                Assert.isTrue(AppPackageStatus.VALID.getCode() == appPackageDto.getStatus(), "该AppId:[" + appId + "]状态为无效");

                AppPlatformType appPlatformType = AppPlatformType.getByCode(appPackageDto.getPlatform());
                Integer platformTypeCode = com.bilibili.ssa.platform.biz.enumerate.PlatformType.getByAppPlatform(appPlatformType).getCode();
                if (SsaConstants.TOP_VIEW_SALES_TYPES.contains(salesType)) {
                    Assert.isTrue(!platformId2AppPackageIdMap.containsKey(platformTypeCode), "存在重复平台的下载包,请按照下载包支持平台：Android+IOS或Android+iPhone来选择");
                    if (AppPlatformType.IOS.equals(appPlatformType)) {
                        platformId2AppPackageIdMap.put(PlatformType.IPHONE.getCode(), appId);
                    } else {
                        platformId2AppPackageIdMap.put(platformTypeCode, appId);
                    }
                } else {
                    Assert.isTrue(!platformId2AppPackageIdMap.containsKey(platformTypeCode), "存在重复平台的下载包,请按照下载包支持平台：Android+IOS或Android+iPhone+iPad来选择");
                    if (AppPlatformType.IOS.equals(appPlatformType)) {
                        platformId2AppPackageIdMap.put(PlatformType.IPAD.getCode(), appId);
                        platformId2AppPackageIdMap.put(PlatformType.IPHONE.getCode(), appId);
                        platformId2AppPackageIdMap.put(PlatformType.IPAD_HD.getCode(), appId);
                    } else {
                        platformId2AppPackageIdMap.put(PlatformType.ANDROID.getCode(), appId);
                        platformId2AppPackageIdMap.put(PlatformType.ANDROID_PAD.getCode(), appId);
                    }
                }
            });

            if (SsaConstants.TOP_VIEW_SALES_TYPES.contains(salesType)) {
//                Assert.isTrue(platformId2AppPackageIdMap.size() == 2, "选择APP下载包的平台必须覆盖Android、iPhone平台");
            }
        }
        return platformId2AppPackageIdMap;
    }

    private void validateCallUpInfo(CallUpInfo callUpInfo) {
        Assert.notNull(callUpInfo, "唤起信息不可为空");
        Assert.notNull(callUpInfo.getIsCallUpApp(), "是否唤起不可为空");
        if (IsCallAppType.YES.getCode().equals(callUpInfo.getIsCallUpApp())) {
            Assert.hasText(callUpInfo.getSchemeUrl(), "唤起scheme不可为空");
            Assert.isTrue(callUpInfo.getSchemeUrl().length() <= MAX_URL_LENGTH, "唤起URL长度超过限制");
            Assert.hasText(callUpInfo.getSchemeCopywriting(), "唤起文案不可为空");
            Assert.isTrue(callUpInfo.getSchemeCopywriting().length() <= MAX_TEXT_LENGTH_32, "唤起文案长度超过限制");
            if (PlatformType.ANDROID.getCode().equals(callUpInfo.getPlatformId())) {
                Assert.hasText(callUpInfo.getAndroidAppPackageName(), "安卓包名不可为空");
                Assert.isTrue(callUpInfo.getAndroidAppPackageName().length() <= MAX_TEXT_LENGTH_128, "安卓包名长度超过限制");
            }
            if (PlatformType.IPHONE.getCode().equals(callUpInfo.getPlatformId()) || PlatformType.IPAD.getCode().equals(callUpInfo.getPlatformId())) {
                Assert.hasText(callUpInfo.getIosAppPackageName(), "IOS包名不可为空");
                Assert.isTrue(callUpInfo.getIosAppPackageName().length() <= MAX_TEXT_LENGTH_128, "IOS包名长度超过限制");
            }

            Assert.isTrue(callUpInfo.getContractAccountId() == null
                    || wakeUpService.isInDirectWakeUpWhiteList(callUpInfo.getContractAccountId()), "订单对应账号没有直接唤起权限");

            this.validateScheme(callUpInfo.getCrmAccountId(), callUpInfo.getSchemeUrl());
        }
    }

    public void validateNewInternalSsaBasicInfo(Operator operator, SsaNewInternalSplashScreenDto dto) {

        LOGGER.info("validateNewInternalSsaBasicInfo SsaNewInternalSplashScreenDto-{}.", dto);
        Assert.isTrue(!Operator.validateParamIsNull(operator));

        Assert.notNull(dto, "闪屏信息不可为空");
        Assert.notNull(dto.getType(), "闪屏类型不可为空");
        Assert.hasText(dto.getTitle(), "闪屏标题不可为空");
        Assert.isTrue(dto.getTitle().length() <= MAX_TEXT_LENGTH_32, "闪屏标题长度最长限制为" + MAX_TEXT_LENGTH_32);
        Assert.notNull(dto.getShowStyle(), "展示样式不可为空");
        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        if (ssaShowStyleType.isVideo()) {
            this.validateNewSplashScreenVideo(null, ssaShowStyleType, dto.getVideoDto());
        }

        dto.getSplashScreenJumpDTOS().forEach(t -> {
//            validateHttpsCustomizedUrlList(t.getSsaCustomizedImpUrlList(), CustomizedUrlType.SHOW_URL);
//            validateHttpsCustomizedUrlList(t.getSsaCustomizedClickUrlList(), CustomizedUrlType.CLICK_URL);
            this.validateCallUpInfo(CallUpInfo.builder()
                    .isCallUpApp(t.getIsCallApp())
                    .schemeUrl(t.getSchemeUrl())
                    .schemeCopywriting(t.getSchemeCopywriting())
                    .androidAppPackageName(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ? t.getPackageName() : "")
                    .iosAppPackageName((PlatformType.IPAD.getCode().equals(t.getPlatformId()) || PlatformType.IPHONE.getCode().equals(t.getPlatformId())) ? t.getPackageName() : "")
                    .crmAccountId(operator.getOperatorId())
                    .contractAccountId(operator.getOperatorId())
                    .build());
            SsaJumpType jumpType = SsaJumpType.getByCode(t.getJumpType());
            Assert.isTrue(jumpType.validateUrlIsValid(t.getJumpLink()), "跳转类型与链接值不匹配");

            if (ssaShowStyleType.getScreenStyle().equals(SsaScreenStyle.HALF_SCREEN)
                    && SsaClickAreaType.COMMON.getCode().equals(dto.getClickArea())
                    && Utils.isPositive(t.getJumpType()) && !Strings.isNullOrEmpty(t.getJumpLink())) {
                Assert.isTrue(!Strings.isNullOrEmpty(dto.getCopywriting()), "跳转链接不为空时，必须填写闪屏文案");
            }
            CommonValidator.bilibiliUrlMustHttps(t.getJumpLink());
        });


        Assert.notNull(dto.getIsSkip(), "是否跳过不可为空");
        Assert.notEmpty(dto.getBaseImageDtos(), "闪屏基础图片不可为空");
        dto.getBaseImageDtos().forEach(bi -> this.validateBaseImage(ssaShowStyleType, bi.getType(), bi.getUrl(), bi.getHash()));

        if (!Strings.isNullOrEmpty(dto.getCopywriting())) {
            Assert.isTrue(dto.getCopywriting().length() <= MAX_TEXT_LENGTH_32, "文案长度最长限制为32");
        }

        this.validateNewVersionControl(dto.getSsaNewSplashScreenVersionControlDtos(), ssaShowStyleType);

        Assert.isTrue(CollectionUtils.isEmpty(ssaSplashScreenServiceDelegate.getSsaSplashScreens(QuerySplashScreenParamDto.builder()
                .accountId(operator.getOperatorId())
                .statusNotInList(Collections.singletonList(SsaSplashScreenStatus.DELETED.getCode()))
                .title(dto.getTitle()).build())), "存在重复的闪屏创意名称，请修改后提交");
    }

    private void validateNewVersionControl(List<SsaNewSplashScreenVersionControlDto> dtos, SsaShowStyleType ssaShowStyleType) {
        List<SsaSplashScreenVersionControlDto> versionControlDtos = Collections.emptyList();
        if (!CollectionUtils.isEmpty(dtos)) {
            versionControlDtos = dtos.stream().map(vc -> {
                SsaSplashScreenVersionControlDto versionControlDto = SsaSplashScreenVersionControlDto.builder().build();
                BeanUtils.copyProperties(vc, versionControlDto);
                return versionControlDto;
            }).collect(Collectors.toList());
        }
        this.validateVersionControl(ssaShowStyleType.isVideo(), versionControlDtos);
    }

    private void validateBaseImage(SsaShowStyleType showStyleType, Integer type, String url, String hash) {
        BaseImageTypeEnum imageType = BaseImageTypeEnum.getByCode(type);
        if (BaseImageTypeEnum.LOGO.equals(imageType) || BaseImageTypeEnum.INTERACT.equals(imageType)) {
            return;
        }
        Assert.isTrue(showStyleType.getNeedBaseImageTypes().contains(imageType), showStyleType.getDesc() + "样式的闪屏，无需上传" + imageType.getDesc());

        Assert.hasText(url, imageType.getDesc() + "URL不可为空");
        Assert.hasText(hash, imageType.getDesc() + "Hash不可为空");
        String decodeImageHash = new String(Base64.decodeBase64(hash));
        ImageHashDto imageHash = JSON.parseObject(decodeImageHash, ImageHashDto.class);

        Assert.isTrue(url.equals(imageHash.getImage_url()) && type.equals(imageHash.getImage_type()), imageType.getDesc() + "URL和Hash不匹配");
    }

    public void validateNewSplashScreenVideo(ScheduleDto scheduleDto, SsaShowStyleType ssaShowStyleType, SsaNewSplashScreenVideoDto dto) {
        LOGGER.info("validateNewSplashScreenVideo SsaNewSplashScreenVideoDto: [{}]", dto);
        Assert.notNull(dto, "闪屏视频不可为空");
        Assert.isTrue(!StringUtils.isEmpty(dto.getUposUrl()), "转码前视频URL不可为空");
        Assert.notNull(dto.getBizId(), "视频云业务ID不可为空");
        Assert.isTrue(!StringUtils.isEmpty(dto.getUposAuth()), "视频上传出现异常");

        replaceBizIdIfNecessary(Objects.nonNull(scheduleDto) ? scheduleDto.getScheduleId() : null, dto.getBizId(), dto::setBizId);

        SsaUposVideoDto uposVideoDto = ssaUposVideoService.getSsaUposVideoByBizId(dto.getBizId(),
                Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.MGK_AV.getCode(),
                        UposProfileEnum.SSA_BUP.getCode()));

        LOGGER.info("validateNewSplashScreenVideo getUposVideoByBizId: [{}] result: [{}]", dto.getBizId(), uposVideoDto);
        Assert.notNull(uposVideoDto, "视频处理中，请稍后重试");
        SsaSplashScreenVideoStatus videoStatus = SsaSplashScreenVideoStatus.getByCode(uposVideoDto.getStatus());
        Assert.state(!SsaSplashScreenVideoStatus.TRANS_CODING.equals(videoStatus), "视频转码中，请稍后重试");
        Assert.isTrue(SsaSplashScreenVideoStatus.TRANS_SUCCESS.equals(videoStatus), "视频转码失败，请核实视频是否符合规范");
        Assert.hasText(uposVideoDto.getUposUrl(), "视频转码失败，请核实视频是否符合规范");
        Assert.hasText(uposVideoDto.getXcodeUposUrl(), "视频转码失败，请核实视频是否符合规范");
        Assert.isTrue(uposVideoDto.getXcodeWidth() != null && uposVideoDto.getXcodeWidth() > 0, "视频转码失败，请核实视频是否符合规范");
        Assert.isTrue(uposVideoDto.getXcodeHeight() != null && uposVideoDto.getXcodeHeight() > 0, "视频转码失败，请核实视频是否符合规范");
        Assert.hasText(uposVideoDto.getXcodeMd5(), "视频转码失败，请核实视频是否符合规范");

        if (Objects.nonNull(scheduleDto)) {
            //校验视频
            //现在闪屏和TopView都是走的批量异步接口，因此要将校验提前，否则在事务内校验+回滚，导致用户无法感知
            this.ssaSplashScreenVideoService.validateVideo(ssaShowStyleType, uposVideoDto, scheduleDto.getSalesType(),
                    scheduleDto.getOrderProduct(), scheduleDto.getOttScreenType(), scheduleDto.getSsaVideoPlayMode());
        }
    }

    private void replaceBizIdIfNecessary(Integer scheduleId, int bizId, Consumer<Integer> replace) {

        List<Integer> scheduleIds = systemConfigService.getValueReturnListInt(SystemConfigEnum.SSA_IGNORE_MD5_DUPLICATE_REMOVAL_SCHEDULES.getCode());
        if (scheduleId != null
                && !CollectionUtils.isEmpty(scheduleIds)
                && scheduleIds.contains(scheduleId)) {
            return;
        }

        int theEarliestSameVideo = ssaUposVideoService.findTheEarliestSameVideo(bizId);

        replace.accept(theEarliestSameVideo);
        LOGGER.info("替换创意的biz_id:{}->{}", bizId, theEarliestSameVideo);
    }

    private void validateUpdateSplashScreenVideo(ScheduleDto scheduleDto, SsaShowStyleType ssaShowStyleType, SsaUpdateSplashScreenVideoDto dto) {

        LOGGER.info("validateUpdateSplashScreenVideo SsaUpdateSplashScreenVideoDto: [{}]", dto);

        Assert.notNull(dto, "闪屏视频不可为空");
        Assert.notNull(dto.getSplashScreenId(), "闪屏ID不可为空");
        Assert.isTrue(!StringUtils.isEmpty(dto.getUposUrl()), "转码前视频URL不可为空");
        Assert.notNull(dto.getBizId(), "视频云业务ID不可为空");
        Assert.isTrue(!StringUtils.isEmpty(dto.getUposAuth()), "视频上传出现异常");

        replaceBizIdIfNecessary(Objects.nonNull(scheduleDto) ? scheduleDto.getScheduleId() : null, dto.getBizId(), dto::setBizId);

        SsaUposVideoDto uposVideoDto = ssaUposVideoService.getSsaUposVideoByBizId(dto.getBizId(),
                Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.MGK_AV.getCode(),
                        UposProfileEnum.SSA_BUP.getCode()));

        LOGGER.info("validateUpdateSplashScreenVideo getUposVideoByBizId: [{}] result: [{}]", dto.getBizId(), uposVideoDto);
        Assert.notNull(uposVideoDto, "视频转码中，请稍后");
        Assert.isTrue(!SsaSplashScreenVideoStatus.TRANS_CODING.getCode().equals(uposVideoDto.getStatus()), "视频转码中，请稍后");
        Assert.hasText(uposVideoDto.getUposUrl(), "视频转码失败，请核实视频是否符合规范");
        Assert.hasText(uposVideoDto.getXcodeUposUrl(), "视频转码失败，请核实视频是否符合规范");
        Assert.isTrue(uposVideoDto.getXcodeWidth() != null && uposVideoDto.getXcodeWidth() > 0, "视频转码失败，请核实视频是否符合规范");
        Assert.isTrue(uposVideoDto.getXcodeHeight() != null && uposVideoDto.getXcodeHeight() > 0, "视频转码失败，请核实视频是否符合规范");
        Assert.hasText(uposVideoDto.getXcodeMd5(), "视频转码失败，请核实视频是否符合规范");

        if (Objects.nonNull(scheduleDto)) {
            //校验视频
            //现在闪屏和TopView都是走的批量异步接口，因此要将校验提前，否则在事务内校验+回滚，导致用户无法感知
            this.ssaSplashScreenVideoService.validateVideo(ssaShowStyleType, uposVideoDto, scheduleDto.getSalesType(),
                    scheduleDto.getOrderProduct(), scheduleDto.getOttScreenType(), scheduleDto.getSsaVideoPlayMode());
        }
    }

    public void validateInternalUpdateSplashScreenBasicInfo(SsaUpdateSplashScreenDto dto, Operator operator) {

        LOGGER.info("validateUpdateSplashScreenBasicInfo validateInternalUpdateSplashScreenBasicInfo-{}.", dto);
        Assert.notNull(dto, "闪屏信息不可为空");
        Assert.notNull(dto.getId(), "闪屏ID不可为空");
        Assert.hasText(dto.getTitle(), "闪屏标题不可为空");
        Assert.isTrue(dto.getTitle().length() <= MAX_TEXT_LENGTH_32, "闪屏标题长度最长限制为" + MAX_TEXT_LENGTH_32);
        Assert.notNull(dto.getShowStyle(), "展示样式不可为空");

        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        if (ssaShowStyleType.isVideo()) {
            this.validateUpdateSplashScreenVideo(null, ssaShowStyleType, dto.getVideoDto());
        }

        dto.getSplashScreenJumpDTOS().forEach(t -> {
//            validateHttpsCustomizedUrlList(t.getSsaCustomizedImpUrlList(), CustomizedUrlType.SHOW_URL);
//            validateHttpsCustomizedUrlList(t.getSsaCustomizedClickUrlList(), CustomizedUrlType.CLICK_URL);
            this.validateCallUpInfo(CallUpInfo.builder()
                    .isCallUpApp(t.getIsCallApp())
                    .schemeUrl(t.getSchemeUrl())
                    .schemeCopywriting(t.getSchemeCopywriting())
                    .androidAppPackageName(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ? t.getPackageName() : "")
                    .iosAppPackageName((PlatformType.IPAD.getCode().equals(t.getPlatformId()) || PlatformType.IPHONE.getCode().equals(t.getPlatformId())) ? t.getPackageName() : "")
                    .crmAccountId(operator.getOperatorId())
                    .contractAccountId(operator.getOperatorId())
                    .build());
            SsaJumpType jumpType = SsaJumpType.getByCode(t.getJumpType());
            Assert.isTrue(jumpType.validateUrlIsValid(t.getJumpLink()), "跳转类型与链接值不匹配");

            CommonValidator.bilibiliUrlMustHttps(t.getJumpLink());
        });

        Assert.notNull(dto.getIsSkip(), "是否跳过不可为空");
        Assert.notEmpty(dto.getSsaBaseImageDtos(), "闪屏基础图片不可为空");
        dto.getSsaBaseImageDtos().forEach(bi -> this.validateBaseImage(ssaShowStyleType, bi.getType(), bi.getUrl(), bi.getHash()));

        if (!Strings.isNullOrEmpty(dto.getCopyWriting())) {
            Assert.isTrue(dto.getCopyWriting().length() <= MAX_TEXT_LENGTH_32, "文案长度最长限制为" + MAX_TEXT_LENGTH_32);
        }

        this.validateVersionControl(ssaShowStyleType.isVideo(), dto.getSsaVersionControlDtos());

    }

    public Timestamp validateScheduleAndGetLaunchStartTime(final SsaUpdateSplashScreenDto updateSplashScreenDto, final SsaSplashScreenDetailDto oldSplashScreen) {
        if (SalesType.SSA_CPM.getCode() == oldSplashScreen.getSalesType() ||
                SalesType.ADX_SPLASH_CPM.getCode() == oldSplashScreen.getSalesType() ||
                SalesType.SSA_CPT_PLUS.getCode() == oldSplashScreen.getSalesType()
                || SalesType.SSA_GD_PLUS.getCode() == oldSplashScreen.getSalesType()
                || SalesType.TOP_VIEW_PLUS.getCode() == oldSplashScreen.getSalesType()
                || SalesType.TOP_VIEW_GD_PLUS.getCode() == oldSplashScreen.getSalesType()) {
            List<Timestamp> historyLaunchDates = oldSplashScreen.getSsaScheduleDtos().stream()
                    .map(SsaScheduleDto::getSsaStartTime)
                    .filter(launchDate -> Utils.getToday().compareTo(launchDate) >= 0).collect(Collectors.toList());

            List<Timestamp> updateHistoryLaunchDates = updateSplashScreenDto.getSsaScheduleMappingDtos().stream()
                    .map(SsaNewScheduleSplashScreenMappingDto::getBeginTime)
                    .filter(launchDate -> Utils.getToday().compareTo(launchDate) >= 0).collect(Collectors.toList());

            List<Timestamp> updateLaunchDates = updateSplashScreenDto.getSsaScheduleMappingDtos().stream()
                    .map(SsaNewScheduleSplashScreenMappingDto::getBeginTime).collect(Collectors.toList());
            Assert.isTrue(updateHistoryLaunchDates.containsAll(historyLaunchDates), "今天及历史的投放时间不可更改");

            return updateLaunchDates.stream().min(Timestamp::compareTo).orElse(null);
        }

        List<Integer> newScheduleIds = updateSplashScreenDto
                .getSsaScheduleMappingDtos().stream().map(SsaNewScheduleSplashScreenMappingDto::getScheduleId).distinct().collect(Collectors.toList());
        List<SsaScheduleDto> ssaScheduleDtos = ssaScheduleService.getSchedulesInSsaScheduleIds(newScheduleIds);

        Assert.isTrue(newScheduleIds.size() == ssaScheduleDtos.size(), "存在无效的排期");

        List<Timestamp> historyLaunchDates = oldSplashScreen.getSsaScheduleDtos().stream().map(SsaScheduleDto::getLaunchDate)
                .filter(launchDate -> Utils.getToday().compareTo(launchDate) >= 0).collect(Collectors.toList());

        List<Timestamp> updateLaunchDates = ssaScheduleDtos.stream().map(SsaScheduleDto::getLaunchDate).collect(Collectors.toList());
        Assert.isTrue(updateLaunchDates.containsAll(historyLaunchDates), "今天及历史的投放时间不可更改");

        return updateLaunchDates.stream().min(Timestamp::compareTo).orElse(null);
    }

    public void validateExternalUpdateSplashScreenBasicInfo(Operator operator, ScheduleDto schedule,
                                                            SsaUpdateSplashScreenDto dto, GdOrderDto orderDto) {

        LOGGER.info("validateUpdateSplashScreenBasicInfo validateExternalUpdateSplashScreenBasicInfo-{}.", dto);
        Assert.notNull(dto, "闪屏信息不可为空");
        Assert.notNull(dto.getId(), "闪屏ID不可为空");
        Assert.hasText(dto.getTitle(), "闪屏标题不可为空");
        Assert.isTrue(dto.getTitle().length() <= MAX_TEXT_LENGTH_32, "闪屏标题长度最长限制为" + MAX_TEXT_LENGTH_32);
        Assert.notNull(dto.getShowStyle(), "展示样式不可为空");
        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        if (ssaShowStyleType.isVideo()) {
            SsaUpdateSplashScreenVideoDto videoInfo = dto.getVideoDto();
            if (Objects.equals(schedule.getSsaVideoPlayMode(), SsaVideoPlayModeEnum.IMMERSIVE_VIDEO.getCode())) {
                if (!Utils.isPositive(videoInfo.getSplashScreenId())) {
                    videoInfo.setSplashScreenId(dto.getId());
                }
            }

            if (!SsaVideoPlayModeEnum.isArchive(schedule.getSsaVideoPlayMode())) {
                this.validateUpdateSplashScreenVideo(schedule, ssaShowStyleType, dto.getVideoDto());

                if (Objects.equals(schedule.getSsaVideoPlayMode(), SsaVideoPlayModeEnum.EASTER_EGG.getCode())) {
                    Assert.notNull(dto.getEggVideoDto(), "【浮窗彩蛋视频】播放形式必须选择彩蛋视频");
                    Assert.notNull(dto.getEggVideoDto().getBizId(), "彩蛋视频ID不能为空");
                    IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(dto.getEggVideoDto().getBizId());
                    // 注释的状态貌似有问题，看了下IP视频库应该和这个枚举对应
                    SsaSplashScreenVideoStatus videoStatus = SsaSplashScreenVideoStatus.getByCode(ipVideoBo.getDealStatus());
                    Assert.state(!SsaSplashScreenVideoStatus.TRANS_CODING.equals(videoStatus), "视频转码中，请稍后重试");
                    Assert.isTrue(SsaSplashScreenVideoStatus.TRANS_SUCCESS.equals(videoStatus), "视频转码失败，请核实视频是否符合规范");
                }
            }
        }
        if (ButtonInteractStyleEnum.SLIDE.getCode().equals(dto.getInteractStyle()) &&
                SsaAdType.IMAGE.getCode().equals(ssaShowStyleType.getAdType().getCode())) {
            Assert.isTrue(dto.getIsSupportButtonToInteract(), "互动闪屏的图文按钮必须要选用动效按钮");
        }


//        jumpDTOS.forEach(t->this.validateHttpsCustomizedUrlList(t.getSsaCustomizedClickUrlList(),
//                CustomizedUrlType.CLICK_URL));
//        jumpDTOS.forEach(t->this.validateHttpsCustomizedUrlList(t.getSsaCustomizedImpUrlList(),
//                CustomizedUrlType.SHOW_URL));

        ContractDto contract = contractService.getContractById(orderDto.getCrmContractId());
        Assert.notNull(contract, "合同信息不存在");

        List<SplashScreenJumpDTO> jumpDTOS = dto.getSplashScreenJumpDTOS();
        if (CollectionUtils.isEmpty(jumpDTOS)) {
            List<SplashScreenDynamicButtonBO> buttonBOS = dto.getButtonBOS();
            jumpDTOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(buttonBOS)) {
                List<SplashScreenJumpDTO> finalJumpDTOS = jumpDTOS;
                buttonBOS.forEach(t -> {
                    List<SplashScreenJumpDTO> temp = t.getSplashScreenJumpDTOS();
                    finalJumpDTOS.addAll(temp);
                });
                jumpDTOS = finalJumpDTOS;
            }
        }
        boolean isSupportCallApp = WakeAppType.isSupportApp(schedule.getWakeAppType());
        if (!CollectionUtils.isEmpty(jumpDTOS)) {
            jumpDTOS.forEach(t -> {
                // 排期未选择唤起应用 创意不支持唤起应用
                if (!isSupportCallApp) {
                    // 不支持填写App唤起链接
                    Assert.isTrue(Objects.equals(t.getIsCallApp(), IsCallAppType.NO.getCode()) && StringUtils.isEmpty(t.getSchemeUrl()), "排期未选择唤起应用,创意不支持唤起应用");
                }
                this.validateCallUpInfo(CallUpInfo.builder()
                        .isCallUpApp(t.getIsCallApp())
                        .schemeUrl(t.getSchemeUrl())
                        .schemeCopywriting(t.getSchemeCopywriting())
                        .androidAppPackageName(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ? t.getPackageName() : "")
                        .iosAppPackageName((PlatformType.IPAD.getCode().equals(t.getPlatformId())
                                || PlatformType.IPHONE.getCode().equals(t.getPlatformId())) ? t.getPackageName() : "")
                        .crmAccountId(operator.getOperatorId())
                        .contractAccountId(contract.getAccountId())
                        .build());

                CommonValidator.bilibiliUrlMustHttps(t.getJumpLink());
            });
        }

        Assert.notNull(dto.getIsSkip(), "是否跳过不可为空");

        if (!OrderProduct.COMIC_SSA_CPT.getCode().equals(dto.getOrderProduct())
                && !SsaConstants.TOP_VIEW_SALES_TYPES.contains(schedule.getSalesType())
                || (!dto.getShowStyle().equals(SsaShowStyleType.VERTICAL_SCREEN_VIDEO.getCode())
                && !dto.getShowStyle().equals(SsaShowStyleType.FULL_SCREEN_VERTICAL_SCREEN_VIDEO.getCode()))) {
            if (!SsaVideoPlayModeEnum.isArchive(schedule.getSsaVideoPlayMode())) {
                Assert.notEmpty(dto.getSsaBaseImageDtos(), "闪屏基础图片不可为空");
                dto.getSsaBaseImageDtos().forEach(bi -> this.validateBaseImage(ssaShowStyleType, bi.getType(), bi.getUrl(), bi.getHash()));
            }
        }

        Assert.notNull(dto.getIssuedTime(), "闪屏下发时间不可为空");
        Assert.notEmpty(dto.getSsaScheduleMappingDtos(), "闪屏排期不可为空");

        if (!Strings.isNullOrEmpty(dto.getCopyWriting())) {
            Assert.isTrue(dto.getCopyWriting().length() <= 32, "文案长度最长限制为32");
        }

        this.validateVersionControl(ssaShowStyleType.isVideo(), dto.getSsaVersionControlDtos());

        //验证唤起
        if (WakeAppType.isMiniProgram(schedule.getWakeAppType())) {
            Assert.isTrue(Objects.nonNull(dto.getMiniProgram()) && dto.getMiniProgram().isValid(), "小程序信息不能为空");
        }
        this.validateMiddlePage(dto.getMiddlePage(), schedule);
        this.validateInteractEgg(dto.getInteractEgg(), schedule);
    }

    private void validateScheme(Integer accountId, String schemeUrl) {
        Assert.isTrue(accountId != null && accountId > 0, "您无法使用App唤起功能，请联系管理员绑定商业账号，谢谢！");
        if (!Strings.isNullOrEmpty(schemeUrl)) {
            //淘宝618取消校验
            if (!ssaConfigUtil.containSkipValidUrl(schemeUrl)) {
                String scheme = this.getSchemeByUrl(schemeUrl);
                Assert.isTrue(!schemeUrl.startsWith("http"), "唤起scheme" + "不可以http开头");

                AwakenAppWhitelistDto awakenApplistDto = awakenAppWhitelistService.getByScheme(accountId, scheme);
                Assert.notNull(awakenApplistDto, "该唤起scheme不在白名单内");
                Assert.isTrue(!Strings.isNullOrEmpty(scheme), "唤起scheme" + "有误");

                boolean hasBar = wakeUpService.hasWakeUpBar(schemeUrl);
                Assert.isTrue(hasBar
                        || wakeUpService.isInWakeUpBarWhiteList(scheme), "保存失败，唤起链接没有配置小把手");
            }
        }
    }

    private String getSchemeByUrl(String url) {
        return UriComponentsBuilder.fromUriString(url).build().getScheme();
    }

    private void validateHttpsCustomizedUrl(String customizedUrl, CustomizedUrlType urlType) {
        if (StringUtils.isEmpty(customizedUrl)) {
            return;
        }
        Assert.isTrue(customizedUrl.length() <= MAX_URL_LENGTH, urlType.getDesc() + "不可大于" + MAX_URL_LENGTH);
        String scheme = UriComponentsBuilder.fromUriString(customizedUrl).build().getScheme();
        Assert.isTrue(MONITOR_URL_SCHEME_HTTPS.equals(scheme), urlType.getDesc() + "必须以https开头");
    }

    private void validateHttpsCustomizedUrlList(List<String> customizedUrlList, CustomizedUrlType urlType) {
        if (CollectionUtils.isEmpty(customizedUrlList)) {
            return;
        }
        for (String customizedUrl : customizedUrlList) {
            validateHttpsCustomizedUrl(customizedUrl, urlType);
        }
    }

    public void checkCustomizedAndJumpInfo(SsaNewExternalSplashScreenDto dto, Operator operator,
                                           ContractDto contract, ScheduleDto scheduleDto) {
        List<SplashScreenJumpDTO> jumpDTOS = dto.getSplashScreenJumpDTOS();
        if (CollectionUtils.isEmpty(jumpDTOS)) {
            List<SplashScreenDynamicButtonBO> buttonBOS = dto.getButtonBOS();
            jumpDTOS = new ArrayList<>();
            if (!CollectionUtils.isEmpty(buttonBOS)) {
                List<SplashScreenJumpDTO> finalJumpDTOS = jumpDTOS;
                buttonBOS.forEach(t -> {
                    List<SplashScreenJumpDTO> temp = t.getSplashScreenJumpDTOS();
                    finalJumpDTOS.addAll(temp);
                });
                jumpDTOS = finalJumpDTOS;
            }
        }
        boolean isSupportCallApp = WakeAppType.isSupportApp(scheduleDto.getWakeAppType());
        if (!CollectionUtils.isEmpty(jumpDTOS)) {
            jumpDTOS.forEach(t -> {
                checkLaunchInnerJump(scheduleDto.getLaunchInnerJump(), t.getJumpLink(), t.getJumpType());
                // 排期未选择唤起应用 创意不支持唤起应用
                if (!isSupportCallApp) {
                    // 不支持填写App唤起链接
                    Assert.isTrue(Objects.equals(t.getIsCallApp(), IsCallAppType.NO.getCode()) && StringUtils.isEmpty(t.getSchemeUrl()), "排期未选择唤起应用,创意不支持唤起应用");
                }
                this.validateCallUpInfo(CallUpInfo.builder()
                        .isCallUpApp(t.getIsCallApp())
                        .schemeUrl(t.getSchemeUrl())
                        .schemeCopywriting(t.getSchemeCopywriting())
                        .androidAppPackageName(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ? t.getPackageName() : "")
                        .iosAppPackageName((PlatformType.IPAD.getCode().equals(t.getPlatformId())
                                || PlatformType.IPHONE.getCode().equals(t.getPlatformId())) ? t.getPackageName() : "")
                        .crmAccountId(operator.getOperatorId())
                        .contractAccountId(contract.getAccountId())
                        .build());

                //校验跳转类型
                SsaJumpType jumpType = SsaJumpType.getByCode(t.getJumpType());
                if (!StringUtils.isEmpty(t.getJumpLink())) {
                    Assert.isTrue(jumpType.validateUrlIsValid(t.getJumpLink()), "跳转类型与链接值不匹配");
                    CommonValidator.bilibiliUrlMustHttps(t.getJumpLink());
                }
            });
        }

        List<SplashScreenCustomizedDTO> customizedDTOS = dto.getSsaCustomizedDTOS();
        if (!CollectionUtils.isEmpty(customizedDTOS)) {
            customizedDTOS.forEach(t -> {
                validateHttpsCustomizedUrlList(t.getSsaCustomizedImpUrlList(), CustomizedUrlType.SHOW_URL);
                validateHttpsCustomizedUrlList(t.getSsaCustomizedClickUrlList(), CustomizedUrlType.CLICK_URL);
            });
        }
    }

    private void checkLaunchInnerJump(Boolean launchInnerJump, String jumpUrl, Integer jumpType) {
        if (launchInnerJump != null && launchInnerJump && !StringUtils.isEmpty(jumpUrl)
                && SsaJumpType.LINK.getCode().equals(jumpType)) {
            List<String> domains = systemConfigService.getValueReturnList(SystemConfigEnum.LAUNCH_INNER_JUMP.getCode());
            boolean contain = false;
            for (String domain : domains) {
                if (jumpUrl.contains(domain)) {
                    contain = true;
                    break;
                }
            }
            Assert.isTrue(contain, "选择内链投放的时候必须要包含以下域名" + domains);
        }

    }

    //https://www.tapd.cn/********/prong/stories/view/11********004394974
    private void validateMiddlePage(SsaSplashScreenMiddlePageDto middlePage, ScheduleDto schedule) {
        if (SsaVideoPlayModeEnum.isAutoContinuePlay(schedule.getSsaVideoPlayMode())) {
            Assert.notNull(middlePage, "中间页（续播页）信息必传");
            Assert.hasText(middlePage.getLogo(), "中间页品牌头像不能为空");
            Assert.hasText(middlePage.getTitle(), "中间页品牌标题不能为空");
            Assert.hasText(middlePage.getDescription(), "中间页品牌描述不能为空");
            Assert.notNull(middlePage.getShowLive(), "中间页是否展示直播标不能为空");
            Assert.hasText(middlePage.getBtnTxt(), "中间页跳转按钮文案不能为空");
        }
    }

    //https://www.tapd.cn/********/prong/stories/view/11********004448078
    private void validateInteractEgg(SsaSecondPageInteractEggDto interactEggDto, ScheduleDto schedule) {
        SsaEffectiveType effectiveType = SsaEffectiveType.getByCode(schedule.getEffectiveType());
        if (Objects.equals(effectiveType, SsaEffectiveType.INTERACT_EGG)) {
            Assert.notNull(interactEggDto, "交互彩蛋素材不能为空");
            Assert.notNull(interactEggDto.getVideo(), "交互彩蛋素材不能为空");
            Assert.isTrue(Utils.isPositive(interactEggDto.getVideo().getBizId()), "交互彩蛋视频id不能为空");
            IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(interactEggDto.getVideo().getBizId());
            SsaSplashScreenVideoStatus videoStatus = SsaSplashScreenVideoStatus.getByCode(ipVideoBo.getDealStatus());
            Assert.state(!SsaSplashScreenVideoStatus.TRANS_CODING.equals(videoStatus), "视频转码中，请稍后重试");
            Assert.isTrue(SsaSplashScreenVideoStatus.TRANS_SUCCESS.equals(videoStatus), "视频转码失败，请核实视频是否符合规范");
        }
    }
}


