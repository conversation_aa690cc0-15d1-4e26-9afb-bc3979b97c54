package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaSplashScreenJumpInfoPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 闪屏id
     */
    private Integer splashScreenId;

    /**
     * 平台id 1-iPhone 2-Android 3-iPad
     */
    private Integer platformId;

    /**
     * 跳转类型: 1-链接 2-视频 3-游戏中心 4-番剧 5-直播 6-落地页下载 10-tv_schema 11-本地视频 12-全屏视频落地页 13-建站落地页
     */
    private Integer jumpType;

    /**
     * 跳转链接
     */
    private String jumpLink;

    /**
     * 是否唤起应用 1：否 2：是
     */
    private Integer isCallApp;

    /**
     * 唤起url
     */
    private String schemeUrl;

    /**
     * universal唤起app
     */
    private String universalApp;

    /**
     * 唤起文案
     */
    private String schemeCopywriting;

    /**
     * 包名
     */
    private String packageName;

    /**
     * 是否有唤起小把手 0-无 1-有
     */
    private Integer isWithWakeUpBar;

    /**
     * 软删除: 0-有效, 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 交互链接（用于跟用户有交互的场景)
     */
    private String interactLink;

    /**
     * 5-建站落地页
     */
    private Integer interactLinkType;

    /**
     * 序号
     */
    private Integer seq;

    /**
     * 按钮id，对应闪屏按钮表的id
     */
    private Integer buttonId;

    /**
     * 新版滑动唤起
     */
    private String schemeCopywritingNew;

    /**
     * 直播类型 0-运营 1-电竞
     */
    private Integer liveType;

    /**
     * 番号id
     */
    private Long seasonId;

    /**
     * 剧集id
     */
    private Long epId;

    /**
     * 建站视频id
     */
    private Long mgkVideoId;

    /**
     * 唤起应用被用户取消授权之后的降级按钮
     */
    private String userCancelJumpLink;

    private static final long serialVersionUID = 1L;
}