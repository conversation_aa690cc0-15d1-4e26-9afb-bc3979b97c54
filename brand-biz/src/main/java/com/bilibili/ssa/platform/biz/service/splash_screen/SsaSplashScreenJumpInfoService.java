package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.CommonBvidUtils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.handler.TvCreativeJumpHandler;
import com.bilibili.enums.PlatformType;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.utils.SsaConfigUtil;
import com.bilibili.brand.biz.utils.SwitchUtil;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenJumpDTO;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenJumpInfoService;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenJumpInfoDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenJumpInfoPoExample;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.bilibili.ssa.platform.common.enums.IsCallAppType;
import com.bilibili.ssa.platform.common.enums.OrderProduct;
import com.bilibili.ssa.platform.common.enums.SsaConstants;
import com.bilibili.ssa.platform.common.enums.SsaJumpType;
import com.bilibili.utils.MacroUtils;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 闪屏跳转服务
 * <AUTHOR>
 * @Date 2020.05.26 23:00
 */
@Slf4j
@Service
public class SsaSplashScreenJumpInfoService implements ISsaSplashScreenJumpInfoService {

    @Autowired
    private SsaSplashScreenJumpInfoDao jumpInfoDao;

    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;

    @Autowired
    private IWakeUpService wakeUpService;

    @Autowired
    private IQueryScheduleService scheduleService;

    @Autowired
    private SsaConfigUtil ssaConfigUtil;

    @Autowired
    private SwitchUtil switchUtil;

    private final static String TV_SCHEMA="yst";

    @Autowired
    private ISoaLandingPageService soaLandingPageService;

    @Autowired
    private TvCreativeJumpHandler tvCreativeJumpHandler;

    @Override
    public void batchSaveJumpInfo(Integer orderProduct, Integer splashScreenId, List<SplashScreenJumpDTO> screenJumpDTOS,
                                  Map<Integer, Integer> Platform2ButtonIdMap) {
        Timestamp now = new Timestamp(System.currentTimeMillis());

        List<SsaSplashScreenJumpInfoPo> pos = screenJumpDTOS.stream()
                .map(t-> {
                    String jumpUrl = StringUtils.isEmpty(t.getJumpLink()) ?
                            "" : t.getJumpLink();
                    if(!StringUtils.isEmpty(t.getJumpLink()) && !OrderProduct.SSA_OTT_CPT.getCode().equals(orderProduct)){
                        jumpUrl = transferJumpLinkFromWeb(SsaJumpType.getByCode(t.getJumpType()), t.getJumpLink());
                    }
                    String userCancelJumpLink = transferJumpLinkFromWeb(SsaJumpType.LINK, t.getUserCancelJumpLink());
                    return SsaSplashScreenJumpInfoPo.builder().jumpType(t.getJumpType())
                           .splashScreenId(splashScreenId)
                           .jumpLink(jumpUrl)
                           .platformId(t.getPlatformId()).isCallApp(t.getIsCallApp())
                           .isDeleted(IsDeleted.VALID.getCode())
                           .packageName(StringUtils.isEmpty(t.getPackageName()) ? "" : t.getPackageName())
                           .schemeCopywriting(StringUtils.isEmpty(t.getSchemeCopywriting()) ? "" : t.getSchemeCopywriting())
                           .schemeUrl(StringUtils.isEmpty(t.getSchemeUrl()) ? "":t.getSchemeUrl())
                           .isWithWakeUpBar(IsCallAppType.NO.getCode().equals(t.getIsCallApp()) ?
                                   0 : (wakeUpService.hasWakeUpBar(t.getSchemeUrl()) ? 1 : 0))
                           .ctime(now).mtime(now).seq(t.getSeq() == null ? 0 : t.getSeq())
                           .buttonId(Platform2ButtonIdMap.getOrDefault(t.getPlatformId(),
                                   0))
                           .universalApp(ssaConfigUtil.getAppSchema(t.getSchemeUrl()))
                                .interactLinkType(t.getJumpType())
                               .interactLink(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ?
                                       (StringUtils.isEmpty(t.getSchemeUrl()) ? jumpUrl : t.getSchemeUrl()) : "")
                           .schemeCopywritingNew(t.getSchemeCopywritingNew() ==null? "" : t.getSchemeCopywritingNew())
                           .liveType(t.getLiveType() == null ? 0 : t.getLiveType())
                            .seasonId(t.getSeasonId() == null ? 0 : t.getSeasonId())
                            .epId(t.getEpId() == null ? 0: t.getEpId())
                            .mgkVideoId(t.getJumpMgkVideoId() == null? 0 : t.getJumpMgkVideoId())
                            .userCancelJumpLink(userCancelJumpLink == null ? "" : userCancelJumpLink)
                            .build();
                }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pos)) {
            jumpInfoDao.insertBatch(pos);
        }
    }

    @Override
    public void batchUpdateJumpInfo(Integer orderProduct, Integer splashScreenId, List<SplashScreenJumpDTO> screenJumpDTOS,
                                    Map<Integer, Integer> Platform2ButtonIdMap) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<SsaSplashScreenJumpInfoPo> pos = screenJumpDTOS.stream()
                .map(t-> {
                    String jumpUrl = StringUtils.isEmpty(t.getJumpLink()) ?
                            "" : t.getJumpLink();
                    if(!StringUtils.isEmpty(t.getJumpLink())  && !OrderProduct.SSA_OTT_CPT.getCode().equals(orderProduct)){
                        jumpUrl = transferJumpLinkFromWeb(SsaJumpType.getByCode(t.getJumpType()), t.getJumpLink());
                    }
                    return SsaSplashScreenJumpInfoPo.builder()
                            .jumpType(t.getJumpType())
                            .splashScreenId(splashScreenId)
                            .jumpLink(jumpUrl)
                            .platformId(t.getPlatformId()).isCallApp(t.getIsCallApp())
                            .isDeleted(IsDeleted.VALID.getCode())
                            .packageName(StringUtils.isEmpty(t.getPackageName()) ? "" : t.getPackageName())
                            .schemeCopywriting(StringUtils.isEmpty(t.getSchemeCopywriting()) ? "" : t.getSchemeCopywriting())
                            .schemeUrl(StringUtils.isEmpty(t.getSchemeUrl()) ? "" : t.getSchemeUrl())
                            .isWithWakeUpBar(IsCallAppType.NO.getCode().equals(t.getIsCallApp()) ?
                                    0 : (wakeUpService.hasWakeUpBar(t.getSchemeUrl()) ? 1 : 0))
                            .universalApp(ssaConfigUtil.getAppSchema(t.getSchemeUrl()))
                            .mtime(now).ctime(now)
                            .seq(t.getSeq() == null ? 0 : t.getSeq())
                            .buttonId(Platform2ButtonIdMap.getOrDefault(t.getPlatformId(),
                                    0))
                            .interactLinkType(1)
                            .interactLink(PlatformType.ANDROID.getCode().equals(t.getPlatformId()) ?
                                    (StringUtils.isEmpty(t.getSchemeUrl()) ? jumpUrl : t.getSchemeUrl()) : "")
                            .schemeCopywritingNew(t.getSchemeCopywritingNew() == null ? "" : t.getSchemeCopywritingNew())
                            .liveType(t.getLiveType() == null ? 0 : t.getLiveType())
                            .seasonId(t.getSeasonId() == null ? 0 : t.getSeasonId())
                            .epId(t.getEpId() == null ? 0: t.getEpId())
                            .mgkVideoId(t.getJumpMgkVideoId() == null? 0 : t.getJumpMgkVideoId())
                            .userCancelJumpLink(transferJumpLinkFromWeb(SsaJumpType.LINK, t.getUserCancelJumpLink()))
                            .build();
                }).collect(Collectors.toList());
        jumpInfoDao.insertUpdateBatch(pos);
    }

    @Override
    public List<SplashScreenJumpDTO> getJumpInfo(Integer splashScreenId) {
        if(splashScreenId == null){
            return new ArrayList<>();
        }
        SsaSplashScreenJumpInfoPoExample example = new SsaSplashScreenJumpInfoPoExample();
        example.or().andSplashScreenIdEqualTo(splashScreenId)
        .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaSplashScreenJumpInfoPo> pos = jumpInfoDao.selectByExample(example);
        if(CollectionUtils.isEmpty(pos)){
            return new ArrayList<>();
        }

        SsaSplashScreenPoExample ssaSplashScreenPoExample = new SsaSplashScreenPoExample();
        ssaSplashScreenPoExample.or().andIdEqualTo(splashScreenId);
        SsaSplashScreenPo ssaSplashScreenPo = ssaSplashScreenDao.selectByExample(ssaSplashScreenPoExample)
                .stream().findFirst().orElse(new SsaSplashScreenPo());
        ScheduleDto scheduleDto = new ScheduleDto();
        if(ssaSplashScreenPo.getGdScheduleId() != 0) {
            scheduleDto = scheduleService.getScheduleBaseInfoById(ssaSplashScreenPo.getGdScheduleId());
        }

        List<SplashScreenJumpDTO> jumpDTOS = pos.stream().map(t-> {
            SplashScreenJumpDTO jumpDTO = new SplashScreenJumpDTO();
            BeanUtils.copyProperties(t, jumpDTO);
            jumpDTO.setActualJumpLink(t.getInteractLink());
            jumpDTO.setJumpMgkVideoId(t.getMgkVideoId());
            return jumpDTO;
        }).collect(Collectors.toList());

        List<SplashScreenJumpDTO> onlyWords = jumpDTOS.stream()
                .filter(t->t.getSeq() == 99).collect(Collectors.toList());

        jumpDTOS = jumpDTOS.stream().filter(t->t.getSeq() != 99 && t.getSeq() != 90
                && t.getSeq() != -1).collect(Collectors.toList());
        SsaJumpType ssaJumpType;
        boolean isOttSSa = com.bilibili.brand.api.common.enums.OrderProduct.isSsaOtt(scheduleDto.getOrderProduct());
        for (SplashScreenJumpDTO jumpDTO : jumpDTOS) {
            ssaJumpType = SsaJumpType.getByCode(jumpDTO.getJumpType());
            jumpDTO.setActualJumpLink(this.getAcutalLaunchUrl(ssaJumpType, jumpDTO.getJumpLink(),
                    scheduleDto.getOrderProduct()));
            //parseLaunchUrl会把ott链接相关信息全部截掉了，因此识别是不是ott闪屏，原样返回
            jumpDTO.setJumpLink(isOttSSa ? ssaJumpType.parseTVLaunchUrl(jumpDTO.getJumpLink()) : ssaJumpType.parseLaunchUrl(jumpDTO.getJumpLink()));
        }

        //此处是为了把选择式闪屏的纯文字按钮返回
        if(!CollectionUtils.isEmpty(onlyWords)){
            SplashScreenJumpDTO jumpDTO = onlyWords.get(0);
            jumpDTOS.add(SplashScreenJumpDTO.builder().seq(99)
                    .schemeCopywriting(jumpDTO.getSchemeCopywriting()).build());
        }

        return jumpDTOS;
    }

    @Override
    public List<SplashScreenJumpDTO> getJumpInfoWithoutOnlyWordsJump(List<SplashScreenJumpDTO> jumpDTOS) {
        return jumpDTOS.stream().filter(t->t.getSeq() != 99 && t.getSeq() != 90 && t.getSeq() != -1)
                .collect(Collectors.toList());
    }

    @Override
    public String getSelectOnlyWordsJumpSchemaCopyWriting(List<SplashScreenJumpDTO> jumpDTOS) {
        List<SplashScreenJumpDTO> onlyWords = jumpDTOS.stream()
                .filter(t->t.getSeq() == 99).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(onlyWords)){
            SplashScreenJumpDTO jumpDTO = onlyWords.get(0);
            return jumpDTO.getSchemeCopywriting();
        }
        return null;
    }


    private String transferJumpLinkFromWeb(SsaJumpType ssaJumpType, String url) {
        if(!StringUtils.isEmpty(url) && url.startsWith(TV_SCHEMA)){
            return url;
        }
        if (switchUtil.getAvIdSwitch() && SsaJumpType.VIDEO.getCode().equals(ssaJumpType.getCode())
                && (url.toUpperCase().startsWith(CommonBvidUtils.bvIdPrefix))) {
            return StringUtils.trimWhitespace(ssaJumpType.getLaunchUrl(String.valueOf(BVIDUtils.bvToAv(url))));
        }else if(SsaJumpType.VIDEO_PAGE == ssaJumpType){
            MgkLandingPageBean pageBean = soaLandingPageService
                    .validatePageIdAndGetLandingPage(ssaJumpType.changeToGdJumpType(), ssaJumpType.parseLaunchUrl(url));
            Assert.notNull(pageBean, "不支持的落地页类型!");
            Assert.isTrue(!StringUtils.isEmpty(pageBean.getLaunchUrl()),
                    "不支持的落地页类型!");
            Assert.isTrue(pageBean.getTemplateStyle() == 208,
                    "闪屏只支持全屏视频落地页类型!");
            return pageBean.getLaunchUrl();
        } else if(SsaJumpType.MGK_PAGE_ID == ssaJumpType){
            MgkLandingPageBean pageBean = soaLandingPageService
                    .validatePageIdAndGetLandingPage(ssaJumpType.parseLaunchUrl(url));
            Assert.notNull(pageBean, "不支持的落地页类型!");
            Assert.isTrue(!StringUtils.isEmpty(pageBean.getLaunchUrlSecondary()), "不支持的落地页类型!");
            return MacroUtils.appendCommonMacro(pageBean.getLaunchUrlSecondary());
        }else {
            return StringUtils.trimWhitespace(ssaJumpType.getLaunchUrl(url));
        }
    }

    public String getAcutalLaunchUrl(SsaJumpType ssaJumpType, String url, Integer orderProduct) {
        if (Objects.equals(OrderProduct.SSA_OTT_CPT.getCode(), orderProduct)
                || Objects.equals(OrderProduct.SSA_OTT_GD.getCode(), orderProduct)) {
            return this.tvCreativeJumpHandler.getActualLaunchUrl(orderProduct, ssaJumpType.getCode(), url);
        }

        if (!Strings.isNullOrEmpty(url) && (url.startsWith(SsaConstants.GAME_PREFFIX)
                || SsaJumpType.GAME.equals(ssaJumpType))) {
            return String.format(SsaConstants.BILIBILI_GAME_SHOW_URL, SsaJumpType.GAME.getGameIdFromUrl(url));
        }

        if (!Strings.isNullOrEmpty(url) && (url.startsWith(SsaConstants.VIDEO_PREFFIX)
                || SsaJumpType.VIDEO.equals(ssaJumpType))) {
            return SsaConstants.BILIBILI_VIDEO_SHOW_BVID_URL.concat(CommonBvidUtils
                        .transferToBvId(Long.valueOf(ssaJumpType.parseLaunchUrl(url))));
        }

        return url;
    }


    /**
     * 所有链接，包含自动生成跳转链接
     */
    @Override
    public List<SplashScreenJumpDTO> getJumpInfoV2(Integer splashScreenId) {
        if(splashScreenId == null) {
            return Collections.emptyList();
        }
        SsaSplashScreenJumpInfoPoExample example = new SsaSplashScreenJumpInfoPoExample();
        example.or()
                .andSplashScreenIdEqualTo(splashScreenId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaSplashScreenJumpInfoPo> pos = jumpInfoDao.selectByExample(example);
        if(CollectionUtils.isEmpty(pos)){
            return Collections.emptyList();
        }

        SsaSplashScreenPoExample ssaSplashScreenPoExample = new SsaSplashScreenPoExample();
        ssaSplashScreenPoExample.or()
                .andIdEqualTo(splashScreenId);
        SsaSplashScreenPo ssaSplashScreenPo = ssaSplashScreenDao.selectByExample(ssaSplashScreenPoExample)
                .stream().findFirst().orElse(new SsaSplashScreenPo());
        ScheduleDto scheduleDto = ssaSplashScreenPo.getGdScheduleId() == 0
                ? new ScheduleDto() : scheduleService.getScheduleBaseInfoById(ssaSplashScreenPo.getGdScheduleId());

        return pos.stream().map(t-> {
            SplashScreenJumpDTO jumpDTO = new SplashScreenJumpDTO();
            BeanUtils.copyProperties(t, jumpDTO);
            SsaJumpType ssaJumpType = SsaJumpType.getByCode(jumpDTO.getJumpType());
            jumpDTO.setActualJumpLink(this.getAcutalLaunchUrl(ssaJumpType, jumpDTO.getJumpLink(),
                    scheduleDto.getOrderProduct()));
            jumpDTO.setJumpLink(ssaJumpType.parseLaunchUrl(jumpDTO.getJumpLink()));
            jumpDTO.setJumpMgkVideoId(t.getMgkVideoId());
            return jumpDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<SplashScreenJumpDTO>> getSimpleJumpInfoMap(List<Integer> splashScreenIdList) {
        if (CollectionUtils.isEmpty(splashScreenIdList)) {
            return new HashMap<>();
        }
        SsaSplashScreenJumpInfoPoExample ssaSplashScreenJumpInfoPoExample = new SsaSplashScreenJumpInfoPoExample();
        ssaSplashScreenJumpInfoPoExample.createCriteria()
                .andSplashScreenIdIn(splashScreenIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaSplashScreenJumpInfoPo> pos = jumpInfoDao.selectByExample(ssaSplashScreenJumpInfoPoExample);
        return pos.stream().collect(Collectors.groupingBy(SsaSplashScreenJumpInfoPo::getSplashScreenId,
                Collectors.mapping(t -> {
                    SplashScreenJumpDTO jumpDTO = new SplashScreenJumpDTO();
                    BeanUtils.copyProperties(t, jumpDTO);
                    SsaJumpType ssaJumpType = SsaJumpType.getByCode(jumpDTO.getJumpType());
                    jumpDTO.setJumpLink(ssaJumpType.parseLaunchUrl(jumpDTO.getJumpLink()));
                    jumpDTO.setJumpMgkVideoId(t.getMgkVideoId());
                    return jumpDTO;
                }, Collectors.toList())));
    }
}
