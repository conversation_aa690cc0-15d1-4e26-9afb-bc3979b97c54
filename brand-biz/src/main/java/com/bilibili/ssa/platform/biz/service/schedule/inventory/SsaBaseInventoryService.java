package com.bilibili.ssa.platform.biz.service.schedule.inventory;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.common.enums.GoblinSceneTypeEnum;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.SsaLinkageType;
import com.bilibili.brand.api.common.enums.SsaOpenEventEnum;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.bo.LockTaskBo;
import com.bilibili.brand.api.schedule.bo.information_flow.InventoryHolder;
import com.bilibili.brand.api.schedule.bo.ssa.SsaBaseQueryInventoryBo;
import com.bilibili.brand.api.schedule.bo.ssa.SsaInventoryContext;
import com.bilibili.brand.api.schedule.dto.ReleaseInventoryMessageDto;
import com.bilibili.brand.api.schedule.service.inventory.IGoblinInventoryService;
import com.bilibili.brand.api.schedule.service.inventory.IReleaseInventoryService;
import com.bilibili.brand.api.stock.dto.ssa.QueryStockResponseDto;
import com.bilibili.brand.api.stock.dto.ssa.StockAdInfoDto;
import com.bilibili.brand.api.stock.dto.ssa.StockTaskDto;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.config.business.SystemMetaDataConfig;
import com.bilibili.brand.biz.schedule.helper.TargetHelper;
import com.bilibili.brand.biz.schedule.service.inventory.AbstractInventoryService;
import com.bilibili.brand.biz.utils.TargetUtil;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.BusinessSideType;
import com.bilibili.cpt.platform.common.ResourceType;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.QueryInventoryTimeInfoBo;
import com.bilibili.ssa.platform.biz.service.schedule.SsaScheduleServiceValidator;
import com.bilibili.ssa.platform.common.enums.OpType;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/11
 */
@Component
@Slf4j
public abstract class SsaBaseInventoryService extends AbstractInventoryService {
    @Autowired
    protected IGoblinInventoryService goblinInventoryService;
    @Resource(name = "completableFutureExecutorWithDecorator")
    private Executor completableFutureExecutor;
    @Autowired
    private SsaScheduleServiceValidator validator;
    @Autowired
    private IQueryAccountService accountService;
    @Autowired
    protected ISystemConfigService systemConfigService;
    @Autowired
    private IReleaseInventoryService ssaReleaseInventoryService;
    @Autowired
    protected ConfigCenter configCenter;

    protected List<InventoryHolder> queryInventory(SsaBaseQueryInventoryBo baseQueryInventoryBo) {

        List<TargetRule> targets = baseQueryInventoryBo.getTargets();
        List<QueryInventoryTimeInfoBo> timeInfos = baseQueryInventoryBo.getTimeInfos();
        String dealSeq = baseQueryInventoryBo.getDealSeq();
        Integer frequencyLimit = baseQueryInventoryBo.getFrequencyLimit();

        List<CompletableFuture<InventoryHolder>> futures = timeInfos.stream()
                .map(time -> CompletableFuture.supplyAsync(() -> {

                    List<TargetRule> copiedTargetRules = copyTargets(targets);

                    //这里重新处理一下时间，排期可能会选多个不同日期的不同分时
                    TargetHelper.processTimeTarget(copiedTargetRules, time.getBeginTime(), time.getEndTime());

                    addColdBootIfNecessary(baseQueryInventoryBo, time.getBeginTime().toLocalDate(), copiedTargetRules);

                    StockTaskDto task = goblinInventoryService.buildQueryTask(OpType.QUERY, copiedTargetRules);

                    OrderProduct orderProduct = baseQueryInventoryBo.getOrderProduct();
                    boolean isTopView = OrderProduct.isTopView(orderProduct);
                    task.getAd_info().setTop_view(isTopView);

                    LocalDate launchDate = time.getBeginTime().toLocalDate();

                    QueryStockResponseDto queryStockResponseDto = goblinInventoryService.queryInventory(GoblinSceneTypeEnum.SPLASH.getCode(),
                            dealSeq,
                            launchDate,
                            task);
                    //holder.getResponse().getTasks().get(0).getAd_info().getTotal_flow_impression()
                    //https://www.tapd.cn/********/prong/stories/view/11********004127975
                    //TopView库存打折
                    if (isTopView) {
                        if (!CollectionUtils.isEmpty(queryStockResponseDto.getTasks())) {
                            StockAdInfoDto adInfo = queryStockResponseDto.getTasks().get(0).getAd_info();
                            Long impression = configCenter.getTopViewConfig().discount(adInfo.getTotal_flow_impression());
                            log.info("[SsaBaseInventoryService] top_view query inventory, original inventory ={}, discounted inventory ={}",
                                    adInfo.getTotal_flow_impression(), impression);
                            if (orderProduct == OrderProduct.TOP_VIEW_GD_PLUS) {
                                impression = getStockByFrequencyLimit(frequencyLimit, impression);
                            }
                            adInfo.setTotal_flow_impression(impression);
                        }
                    }
                    return InventoryHolder.builder()
                            .time(time)
                            .response(queryStockResponseDto)
                            .build();
                }, completableFutureExecutor).whenComplete((result, exception) -> {
                    if (exception != null) {
                        log.warn("gd寻量发生异常:{}", ExceptionUtils.getStackTrace(exception));
                    }
                })).collect(Collectors.toList());

        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    @Deprecated
    protected void lockInventory(List<TargetRule> targets,
                                 LocalDate launchDate,
                                 LocalDateTime beginTime,
                                 LocalDateTime endTime,
                                 int scheduleId,
                                 int lockInventory,
                                 String dealSeq,
                                 Integer orderId) {

        SystemMetaDataConfig metaDataConfig = configCenter.getMetaDataConfig();
        Set<Integer> forceLockWhiteList = metaDataConfig.getForceLockWhiteList();
        boolean forceUpdate = forceLockWhiteList.contains(orderId);

        TargetHelper.processTimeTarget(targets, beginTime, endTime);

        StockTaskDto task = goblinInventoryService.buildLockTask(LockTaskBo.builder()
                .scheduleId(scheduleId)
                .lockInventory(lockInventory)
                .targets(targets)
                .resourceType(ResourceType.SSA.getCode())
                .forceUpdate(forceUpdate)
                .build());
        goblinInventoryService.lockInventory(GoblinSceneTypeEnum.SPLASH.getCode(),
                String.valueOf(dealSeq),
                launchDate,
                task);
    }

    protected void lockInventory(SsaInventoryContext ctx) {

        TargetHelper.processTimeTarget(ctx.getTargets(), ctx.getBeginTime(), ctx.getEndTime());

        StockTaskDto task = goblinInventoryService.buildLockTask(LockTaskBo.builder()
                .scheduleId(ctx.getScheduleId())
                .lockInventory(ctx.getLockInventory())
                .targets(ctx.getTargets())
                .resourceType(ResourceType.SSA.getCode())
                .forceUpdate(ctx.isForceUpdate())
                .todaySchedule(ctx.isTodaySchedule())
                .build());
        task.getAd_info().setTop_view(ctx.isTopView());
        goblinInventoryService.lockInventory(GoblinSceneTypeEnum.SPLASH.getCode(),
                ctx.getDealSeq(),
                ctx.getLaunchDate(),
                task);
    }

    public void releaseInventory(int scheduleId, LocalDate launchDate) {
        ssaReleaseInventoryService.sendReleaseInventoryMessage(ReleaseInventoryMessageDto.builder()
                .scheduleId(scheduleId)
                .launchDate(launchDate)
                .scene(GoblinSceneTypeEnum.SPLASH.getCode())
                .build());
    }

    protected void addColdBootIfNecessary(SsaBaseQueryInventoryBo baseQueryInventoryBo, LocalDate launchDate, List<TargetRule> targets) {
        if (needColdBootInventory(baseQueryInventoryBo.getSsaLinkageType(),
                baseQueryInventoryBo.getSsaVideoPlayMode(),
                baseQueryInventoryBo.getOrderProduct(),
                baseQueryInventoryBo.getAccountId(),
                launchDate,
                true)) {

            addColdBoot(targets);
        }
    }

    protected void addColdBootIfNecessary(SsaPlusScheduleBo scheduleBo) {
        Integer orderProduct = scheduleBo.getOrderProduct();
        if (needColdBootInventory(scheduleBo.getLinkageType(),
                scheduleBo.getSsaVideoPlayMode(),
                OrderProduct.getByCode(orderProduct),
                scheduleBo.getOperator().getOperatorId(),
                scheduleBo.getBeginTime().toLocalDateTime().toLocalDate(),
                false)) {

            addColdBoot(scheduleBo.getTargetRules());
        }
    }

    protected void addColdBoot(List<TargetRule> targets) {
        if (CollectionUtils.isEmpty(targets)
                || targets.stream().noneMatch(target -> target.getRuleType() == TargetType.SSA_OPEN_EVENT.getCode())) {
            targets.add(TargetRule.builder()
                    .ruleType(TargetType.SSA_OPEN_EVENT.getCode())
                    .valueIds(Collections.singletonList(SsaOpenEventEnum.COLD_BOOT.getCode()))
                    .build());
        }
    }

    protected boolean needColdBootInventory(Integer ssaLinkageType,
                                            Integer ssaVideoPlayMode,
                                            OrderProduct orderProduct,
                                            int accountId,
                                            LocalDate launchDay,
                                            boolean isQuery) {
        if (OrderProduct.isSsaOtt(orderProduct)) {
            return false;
        }

        //v1：
        //topView：询冷启，锁通投，热启下发，冷启出
        //v2：
        //topView：询冷启，锁冷启，热启下发，冷启出
        //TAPD：https://www.tapd.cn/********/prong/stories/view/11********004169545
        if (OrderProduct.isTopView(orderProduct)) {
            return true;
        }

        // 稿件闪屏/联动搜索闪屏，无关内广预订闪屏通投日期配置，始终询冷启库存
        if (SsaLinkageType.isSearch(ssaLinkageType)
                || SsaVideoPlayModeEnum.isArchive(ssaVideoPlayMode)) {
            return true;
        }

        //内广在指定日期以内走通投逻辑，指定日期以外走冷启逻辑，ALL代表长期走通投逻辑
        boolean isInnerAccount = accountService.getAccount(accountId).getIsInner().equals(BusinessSideType.INERNAL.getCode());
        if (isInnerAccount) {
            String dateString = launchDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            List<String> dayList = systemConfigService.getValueReturnList(SystemConfigEnum.INNER_SSA_COMMON_BOOT_DAY_CONFIG.getCode());
            return !dayList.contains(dateString) && !dayList.contains("ALL");
        }
        // 默认寻通投
        return false;
    }


    protected long getMaxInventory(OrderProduct orderProduct, long totalCpmInventory, LocalDateTime launchTime, int accountId) {
        return getMaxInventory(orderProduct, totalCpmInventory, TimeUtil.toTimestamp(launchTime), accountId);
    }

    protected long getMaxInventory(OrderProduct orderProduct, long totalCpmInventory, Timestamp launchTime, int accountId) {
        Long maxInventory = validator.checkImpressionAndReturn(orderProduct, launchTime, accountId);
        return Math.min(totalCpmInventory, maxInventory);
    }

    protected List<TargetRule> copyTargets(List<TargetRule> targets) {
        return GsonUtils.toList(GsonUtils.toJson(targets), TargetRule.class);
    }

    protected Long getStockByFrequencyLimit(Integer frequencyLimit, Long stock) {
        if (Objects.nonNull(frequencyLimit) && stock / 1000 < configCenter.getTopViewConfig().getFrequencyLimitsMinimumStock() && frequencyLimit < 4) {
            throw new IllegalArgumentException("库存余量过低，无法进行频控限制");
        }
        return stock / 4 * frequencyLimit;
    }
}
