package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaSplashScreenAnimationPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenAnimationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaSplashScreenAnimationDao {
    long countByExample(SsaSplashScreenAnimationPoExample example);

    int deleteByExample(SsaSplashScreenAnimationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(SsaSplashScreenAnimationPo record);

    int insertBatch(List<SsaSplashScreenAnimationPo> records);

    int insertUpdateBatch(List<SsaSplashScreenAnimationPo> records);

    int insert(SsaSplashScreenAnimationPo record);

    int insertUpdateSelective(SsaSplashScreenAnimationPo record);

    int insertSelective(SsaSplashScreenAnimationPo record);

    List<SsaSplashScreenAnimationPo> selectByExample(SsaSplashScreenAnimationPoExample example);

    SsaSplashScreenAnimationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SsaSplashScreenAnimationPo record, @Param("example") SsaSplashScreenAnimationPoExample example);

    int updateByExample(@Param("record") SsaSplashScreenAnimationPo record, @Param("example") SsaSplashScreenAnimationPoExample example);

    int updateByPrimaryKeySelective(SsaSplashScreenAnimationPo record);

    int updateByPrimaryKey(SsaSplashScreenAnimationPo record);
}