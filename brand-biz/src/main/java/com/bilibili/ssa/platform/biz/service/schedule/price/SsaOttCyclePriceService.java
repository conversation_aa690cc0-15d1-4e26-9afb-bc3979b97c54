package com.bilibili.ssa.platform.biz.service.schedule.price;

import com.alibaba.fastjson2.JSON;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.TargetType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.AreaGroupType;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.ICycleFrequencyService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.cycle.CycleServiceFacade;
import com.bilibili.brand.biz.cycle.dto.CycleQueryDto;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.brand.biz.schedule.handler.TopViewFestivalPriceRaiseHandler;
import com.bilibili.brand.dto.cycle.PriceRaiseDto;
import com.bilibili.cpt.platform.common.OriginTag;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttGdCyclePriceBo;
import com.bilibili.ssa.platform.api.schedule.service.ISsaOttCyclePriceService;
import com.bilibili.ssa.platform.biz.dao.SsaGdPriceDao;
import com.bilibili.ssa.platform.biz.po.SsaGdPricePo;
import com.bilibili.ssa.platform.biz.po.SsaGdPricePoExample;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.SsaScheduleServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import com.bilibili.utils.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/3/21
 **/

@Service
@Slf4j
public class SsaOttCyclePriceService implements ISsaOttCyclePriceService {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private SsaGdPriceDao ssaGdPriceDao;

    @Autowired
    private SsaScheduleServiceDelegate ssaScheduleServiceDelegate;

    @Autowired
    private CycleServiceFacade cycleServiceFacade;

    @Autowired
    private TopViewFestivalPriceRaiseHandler topViewFestivalPriceRaiseHandler;

    @Autowired
    private GdOrderService orderService;

    @Autowired
    private ICycleFrequencyService cycleFrequencyService;


    @Override
    public Pair<Integer, Long> getGdPrice(SsaOttGdCyclePriceBo gdCyclePriceBo) {
        GdInventoryDetailBo detail = gdCyclePriceBo.getReserveInventoryDetail();
        OttScreenStyle ottScreenStyle = OttScreenStyle.getByCodeWithoutEx(gdCyclePriceBo.getOttScreenStyle());
        Integer cycleId = ssaScheduleServiceDelegate.getCycleIdFromNewOrder(
                Collections.singletonList(detail.getBeginTime()),
                launchTime -> new Timestamp(launchTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli()),
                ottScreenStyle.getSsaAdType().getCode(),
                OrderProduct.SSA_OTT_CPT.getCode());

        if (Utils.isPositive(gdCyclePriceBo.getCycleId())) {
            cycleId = cycleServiceFacade.getValidCycle(CycleQueryDto.builder()
                    .cycleId(gdCyclePriceBo.getCycleId())
                    .orderProduct(com.bilibili.brand.api.common.enums.OrderProduct.SSA_OTT_GD)
                    .ottScreenStyle(ottScreenStyle)
                    .build()).getCycleId();
        }

        SsaGdPricePoExample poExample = new SsaGdPricePoExample();
        poExample.or()
                .andShowStyleEqualTo(ottScreenStyle.getSsaShowStyleType().getCode())
                .andCycleIdEqualTo(cycleId)
                .andOrderProductEqualTo(OrderProduct.SSA_OTT_GD.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaGdPricePo> pricePos = ssaGdPriceDao.selectByExample(poExample);
        Assert.notEmpty(pricePos, "查询不到闪屏ott对应的闪屏gd价格");

        SsaGdPricePo ssaGdPricePo = pricePos.get(0);

        LocalDateTime launchTime = detail.getBeginTime();

        long price = ssaGdPricePo.getBasePrice();

        Integer baseRatio = 100;

        Assert.isTrue(Utils.isPositive(price), "闪屏ott对应gd刊例价为0，请配置后再创建排期");

        PriceRaiseDto priceRaise = JSON.parseObject(ssaGdPricePo.getRaiseInfo(), PriceRaiseDto.class);

        GdOrderDto order = orderService.getOrderById(gdCyclePriceBo.getOrderId());
        if (Objects.nonNull(order)
                && Objects.equals(order.getProduct(), OrderProduct.SSA_OTT_GD.getCode())
                && Objects.equals(order.getOriginTag(), OriginTag.ADX.getCode())) {
            //如果是程序化即OTT闪屏PDB，则PDB加收
            baseRatio += NumberUtil.toValidInt(priceRaise.getPdbRaiseRatio());
            log.info("[SsaOttCyclePriceService] getGdPrice, PdbRaiseRatio, resulted ratio={}", baseRatio);
        }

        // 【品牌】 运营后台新增 config配置项:客户维度免营销节点加收配置 https://www.tapd.cn/67874887/prong/stories/view/1167874887004254823
        Integer festivalRaiseRatio = topViewFestivalPriceRaiseHandler.handleRaise(gdCyclePriceBo.getOrderId(),
                launchTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                ssaGdPricePo.getFestivalRaiseRatio());
        baseRatio += festivalRaiseRatio;
        if (festivalRaiseRatio > 0) {
            log.info("[SsaOttCyclePriceService] getGdPrice, FestivalRaiseRatio, resulted ratio={}", baseRatio);
        }

        if (ssaGdPricePo.getWeekendRaiseRatio() != 0 && launchTime.getDayOfWeek().getValue() > 5) {
            baseRatio += ssaGdPricePo.getWeekendRaiseRatio();
            log.info("[SsaOttCyclePriceService] getGdPrice, WeekendRaiseRatio, resulted ratio={}", baseRatio);
        }

        // 性别加收
        List<TargetRule> targets = gdCyclePriceBo.getTargets();
        for (TargetRule target : targets) {
            TargetType targetType = TargetType.getByCode(target.getRuleType());
            if (CollectionUtils.isEmpty(target.getValueIds())) {
                continue;
            }
            switch (targetType) {
                case GENDER:
                    if (ssaGdPricePo.getGenderRaiseRatio() != 0) {
                        baseRatio += ssaGdPricePo.getGenderRaiseRatio();
                        log.info("[SsaOttCyclePriceService] getGdPrice, GenderRaiseRatio, resulted ratio={}", baseRatio);
                    }
                    break;
                case AGE:
                    if (ssaGdPricePo.getAgeRaiseRatio() != 0) {
                        baseRatio += ssaGdPricePo.getAgeRaiseRatio();
                        log.info("[SsaOttCyclePriceService] getGdPrice, AgeRaiseRatio, resulted ratio={}", baseRatio);
                    }
                    break;
                case AREA:
                    baseRatio += regionalSurcharge(gdCyclePriceBo.getAreaGroupId(), priceRaise);
                    log.info("[SsaOttCyclePriceService] getGdPrice, AreaRaiseRatio, resulted ratio={}", baseRatio);
                    break;
                default:
                    break;
            }
        }

        // 人群包加收
        List<Integer> crowdPackIds = gdCyclePriceBo.getCrowdPackIds();
        if (!org.springframework.util.CollectionUtils.isEmpty(crowdPackIds)) {
            baseRatio += ssaGdPricePo.getCrowdPackRaiseRatio();
            log.info("getGdPrice ratio for crowd pack, ratio={}", ssaGdPricePo.getCrowdPackRaiseRatio());
        }

        // 周期频控加收
        if (gdCyclePriceBo.isEnableCycleFrequency()) {
            int cycleFrequencyRaise = this.cycleFrequencyService.queryCycleFrequencyRaise();
            baseRatio += cycleFrequencyRaise;
            log.info("getGdPrice ratio for cycle frequency, ratio={}", cycleFrequencyRaise);
        }

        price = new BigDecimal(price)
                .multiply(BigDecimal.valueOf((double) (baseRatio) / 100))
                .longValue();

        return Pair.of(cycleId, price);
    }

    private Integer regionalSurcharge(Integer areaGroupId, PriceRaiseDto raise) {
        int baseRatio = 0;
        if (!Utils.isPositive(areaGroupId)) {
            return baseRatio;
        }
        AreaGroupType areaGroupType = AreaGroupType.getByCode(areaGroupId);
        switch (areaGroupType){
            case MAJOR_CITY:
                int majorCityRaiseRatio;
                if ((majorCityRaiseRatio = raise.getMajorCityRaiseRatio()) != 0) {
                    baseRatio += majorCityRaiseRatio;
                }
                break;
            case CORE_CITY:
                int coreCityRaiseRatio;
                if ((coreCityRaiseRatio = raise.getCoreCityRaiseRatio()) != 0) {
                    baseRatio += coreCityRaiseRatio;
                }
                break;
            case OTHER_PROVINCE_CITY:
                int otherCityRaiseRatio;
                if ((otherCityRaiseRatio = raise.getOtherCityRaiseRatio()) != 0) {
                    baseRatio += otherCityRaiseRatio;
                }
                break;
            default:
                break;
        }
        return baseRatio;
    }
}
