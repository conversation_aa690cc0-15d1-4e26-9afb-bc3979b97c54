package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaSplashScreenDynamicButtonImagePo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 图片名称
     */
    private String imageName;

    /**
     * 图片类型: 0-引导图片 1-跳转图片 2-唤起图片
     */
    private Integer imageType;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 动效样式 0-引导 1-光效 2-扩散变色 3-颜色翻转 4-自定义
     */
    private Integer imageStyle;

    /**
     * 跳转类型: -1 唤起 0-引导 1-跳转链接 2-视频 3-游戏中心 4-番剧 5-直播 6-落地页
     */
    private Integer jumpType;

    /**
     * 交互方式 0-点击 1-滑动 2-滑动点击 7-扭一扭 11-长按
     */
    private Integer interactStyle;

    /**
     * 素材交互类型 0-点击 1-滑动
     */
    private Integer materialInteractStyle;

    /**
     * 图片md5
     */
    private String imageMd5;

    /**
     * 素材点击方式 0-模块点击 1-全屏 2全屏滑动加点击
     */
    private Integer materialClickArea;

    /**
     * 资源类型，0：闪屏，1：inline，2：story
     */
    private Integer resourceType;

    /**
     * 闪屏第二屏展示方式，0-无，1-沉浸视频
     */
    private Integer ssaSecondShowStyle;

    /**
     * 第二屏交互类型，0-无，1-全屏点击 2-向上滑动
     */
    private Integer secondInteractStyle;

    /**
     * 动效格式，0、lottie 1、webp
     */
    private Integer imageFormat;

    /**
     * 手势方向，0：无方向，1：向右，2：向下，3：向左，4：向上
     */
    private Integer drawDirection;

    /**
     * 动效宽度
     */
    private Integer imageWidth;

    /**
     * 动效高度
     */
    private Integer imageHeight;

    private static final long serialVersionUID = 1L;
}