package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPo;
import com.bilibili.ssa.platform.biz.po.SsaBusinessSideRotationConfigPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaBusinessSideRotationConfigDao {
    long countByExample(SsaBusinessSideRotationConfigPoExample example);

    int deleteByExample(SsaBusinessSideRotationConfigPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaBusinessSideRotationConfigPo record);

    int insertBatch(List<SsaBusinessSideRotationConfigPo> records);

    int insertUpdateBatch(List<SsaBusinessSideRotationConfigPo> records);

    int insert(SsaBusinessSideRotationConfigPo record);

    int insertUpdateSelective(SsaBusinessSideRotationConfigPo record);

    int insertSelective(SsaBusinessSideRotationConfigPo record);

    List<SsaBusinessSideRotationConfigPo> selectByExample(SsaBusinessSideRotationConfigPoExample example);

    SsaBusinessSideRotationConfigPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaBusinessSideRotationConfigPo record, @Param("example") SsaBusinessSideRotationConfigPoExample example);

    int updateByExample(@Param("record") SsaBusinessSideRotationConfigPo record, @Param("example") SsaBusinessSideRotationConfigPoExample example);

    int updateByPrimaryKeySelective(SsaBusinessSideRotationConfigPo record);

    int updateByPrimaryKey(SsaBusinessSideRotationConfigPo record);
}