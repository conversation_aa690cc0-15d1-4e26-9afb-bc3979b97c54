package com.bilibili.ssa.platform.biz.service.converter;

import com.bilibili.brand.dto.common.AppPackageDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/31 17:11
 */
@Mapper(imports = {com.bilibili.adp.common.enums.AppPlatformType.class})
public interface AppPackageConverter {
    AppPackageConverter MAPPER = Mappers.getMapper(AppPackageConverter.class);

    @Mapping(target = "platformDesc", expression = "java(AppPlatformType.getByCode(dto.getPlatform()).getDesc())")
    AppPackageDto toCommonAppPackageDto(com.bilibili.adp.resource.api.app_package.dto.AppPackageDto dto);

    List<AppPackageDto> toCommonAppPackageDto(List<com.bilibili.adp.resource.api.app_package.dto.AppPackageDto> dtos);


}
