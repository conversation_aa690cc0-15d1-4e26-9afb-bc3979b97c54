package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaSplashScreenVideoPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 闪屏ID
     */
    private Integer splashScreenId;

    /**
     * BIZ_ID
     */
    private Integer bizId;

    /**
     * 转码前视频url
     */
    private String uposUrl;

    /**
     * 视频名称
     */
    private String fileName;

    /**
     * 转码后视频URL
     */
    private String xcodeUposUrl;

    /**
     * 转码后视频MD5
     */
    private String xcodeMd5;

    /**
     * 转码后视频宽度
     */
    private Integer xcodeWidth;

    /**
     * 转码后视频高度
     */
    private Integer xcodeHeight;

    /**
     * 视频状态0：转码中  1：转码成功  2：转码失败  3:视频不合法
     */
    private Integer status;

    /**
     * 权限认证
     */
    private String uposAuth;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 转码后的时长(单位毫秒) 
     */
    private Integer xcodeDuration;

    /**
     * 视频类型（0、普通闪屏视频 1、彩蛋视频）
     */
    private Integer videoType;

    private static final long serialVersionUID = 1L;
}