package com.bilibili.ssa.platform.biz.soa;

import com.bilibili.adp.common.enums.SwitchStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.AdxOrderType;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IAdxOrderService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.AdxScheduleStatDto;
import com.bilibili.brand.api.schedule.dto.GdScheduleDateDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.soa.dto.GdTargetDto;
import com.bilibili.brand.dto.cycle.CycleDto;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.enums.WakeAppType;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.service.ISsaCycleService;
import com.bilibili.ssa.platform.api.schedule.dto.*;
import com.bilibili.ssa.platform.soa.ISoaSsaPdScheduleService;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SoaSsaPdScheduleServiceImpl implements ISoaSsaPdScheduleService {

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IAdxOrderService adxOrderService;

    @Autowired
    private ISsaCycleService ssaCycleService;

    @Override
    public List<SoaSsaPdScheduleDto> getSsaSchedulesByCrmOrderId(Integer crmOrderId) {
        GdOrderDto orderDto = gdOrderService.getOrderByCrmOrderId(crmOrderId);
        List<ScheduleDto> scheduleList;
        try {
            scheduleList = queryScheduleService.getSchedulesByOrderId(orderDto.getOrderId());

            scheduleList.forEach(t->{
                LocalDate beginDate = TimeUtil.timestampToLocalDate(t.getBeginDate());
                LocalDate endDate = TimeUtil.timestampToLocalDate(t.getEndDate());
                List<GdScheduleDateDto> scheduleDates = new ArrayList<>();
                for(; !beginDate.isAfter(endDate); beginDate = beginDate.plusDays(1)){
                    scheduleDates.add(GdScheduleDateDto.builder().scheduleId(t.getScheduleId())
                            .beginTime(Utils.getBeginOfDay(TimeUtil.localDateToTimestamp(beginDate)))
                            .endTime(Utils.getEndOfDay(TimeUtil.localDateToTimestamp(beginDate)))
                            .build());
                }
                t.setScheduleDates(scheduleDates);
            });

        } catch (ServiceException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(scheduleList)) {
            return Collections.emptyList();
        }
        scheduleList = scheduleList.stream().filter(scheduleDto -> scheduleDto.getStatus()
                .equals(SwitchStatus.STARTED.getCode())
                || scheduleDto.getStatus().equals(SwitchStatus.STOPED.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scheduleList)) {
            return Collections.emptyList();
        }

        List<Integer> scheduleIds = scheduleList.stream().map(ScheduleDto::getScheduleId).collect(Collectors.toList());
        //曝光数据
        Map<Integer, List<AdxScheduleStatDto>> statMap = adxOrderService.get(AdxOrderType.SSA_PD,
                orderDto.getBidderId(), scheduleIds);

        Map<Integer, SsaCycleDto> cycleMap = this.ssaCycleService.getCycleDtoMapInIds(scheduleList.stream()
                .map(ScheduleDto::getCycleId)
                .filter(Utils::isPositive).distinct()
                .collect(Collectors.toList()));

        return scheduleList.stream().map(scheduleDto -> {
            List<AdxScheduleStatDto> statDtos = statMap.getOrDefault(scheduleDto.getScheduleId(), new ArrayList<>());
            Map<Timestamp, AdxScheduleStatDto> dayStat = new HashMap<>();
            if(!CollectionUtils.isEmpty(statDtos)){
                dayStat = statDtos.stream().collect(Collectors.toMap(stat-> Utils.getBeginOfDay(stat.getGroupDate()),
                        Function.identity(), (a, b) -> {
                            a.setActualClick(a.getActualClick() + b.getActualClick());
                            a.setActualShow(a.getActualShow() + b.getActualShow());
                            return a;
                        }));
            }
            SsaCycleDto cycle = cycleMap.get(scheduleDto.getCycleId());
            Map<Timestamp, AdxScheduleStatDto> finalDayStat = dayStat;
            GdTargetDto targetDto = scheduleDto.getTargetDto();
            return SoaSsaPdScheduleDto.builder()
                    .scheduleId(scheduleDto.getScheduleId())
                    .scheduleName(scheduleDto.getName())
                    .beginTime(scheduleDto.getBeginDate())
                    .endTime(scheduleDto.getEndDate())
                    .dailyDtoList(Optional.ofNullable(scheduleDto.getScheduleDates())
                            .orElse(Collections.emptyList())
                            .stream().map(scheduleDate -> {
                                AdxScheduleStatDto statDto = finalDayStat.getOrDefault(Utils
                                                .getBeginOfDay(scheduleDate.getBeginTime()), new AdxScheduleStatDto());
                                return SoaSsaPdScheduleDailyDto.builder()
                                        .actualShow(statDto.getActualShow())
                                        .beginTime(TimeUtil.timestampToIsoTimeStr(scheduleDate.getBeginTime()))
                                        .endTime(TimeUtil.timestampToIsoTimeStr(scheduleDate.getEndTime()))
                                        .build();
                            }).collect(Collectors.toList()))
                    .showStyle(scheduleDto.getShowStyle())
                    .clickArea(scheduleDto.getClickArea())
                    .buttonStyle(scheduleDto.getButtonStyle())
                    .interactStyle(scheduleDto.getInteractStyle())
                    .costPrice(scheduleDto.getCostPrice())
                    .targetCpm(scheduleDto.getTotalImpression())
                    .actualCpm(statDtos.stream().mapToInt(AdxScheduleStatDto::getActualShow).sum()/1000)
                    .sourceName(Lists.newArrayList("闪屏"))
                    .interactStyle(scheduleDto.getInteractStyle())
                    .targetDto(SsaTargetDto.builder().age(targetDto.getAge())
                            .area(targetDto.getArea()).crowdPacks(targetDto.getCrowdPacks())
                            .excludeCrowdPacks(targetDto.getExcludeCrowdPacks())
                            .firstBrush(targetDto.getFirstBrush()).gender(targetDto.getGender())
                            .inlineSalesType(targetDto.getInlineSalesType()).os(targetDto.getOs())
                            .build())
                    .wakeAppTypeDesc(WakeAppType.getByCode(scheduleDto.getWakeAppType()).getDesc())
                    .cycle(CycleDto.builder().id(scheduleDto.getCycleId()).name(Objects.nonNull(cycle) ? cycle.getName() : "").build())
                    .build();
        }).collect(Collectors.toList());
    }
}
