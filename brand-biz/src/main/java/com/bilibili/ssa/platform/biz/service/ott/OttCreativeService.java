package com.bilibili.ssa.platform.biz.service.ott;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.common.enums.SsaVideoTypeEnum;
import com.bilibili.brand.api.creative.dto.CreativeExtHolderDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.creative.handler.TvCreativeJumpHandler;
import com.bilibili.brand.biz.creative.service.CreativeExtService;
import com.bilibili.brand.biz.proto.ImageUtil;
import com.bilibili.brand.biz.utils.RlockUtil;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.MgkJumpTypeEnum;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.ssa.platform.api.log.service.ISsaLogService;
import com.bilibili.ssa.platform.api.schedule.dto.SsaQueryScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.api.splash_screen.service.*;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.component.OttCreativeValidator;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.log.OttCptCreativeLogBean;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.bilibili.ssa.platform.biz.service.business.SsaQrInfoService;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021.03.23 15:18
 */
@Slf4j
@Service
public class OttCreativeService implements IOttCreativeService {


    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;

    @Autowired
    private OttCreativeValidator validator;

    @Autowired
    private ISsaSplashScreenImageService ssaSplashScreenImageService;

    @Autowired
    private ISsaSplashScreenVersionService ssaSplashScreenVersionService;
    @Autowired
    private ISsaSplashScreenSchduleService ssaSplashScreenSchduleService;
    @Autowired
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;
    @Autowired
    private ISsaScheduleService ssaScheduleService;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;

    @Autowired
    private ISsaSplashScreenCustomUrlService ssaSplashScreenCustomUrlService;

    @Autowired
    private SsaSplashScreenServiceDelegate delegate;

    @Autowired
    private IGdOrderService orderService;

    @Autowired
    private IQueryScheduleService scheduleService;

    @Autowired
    private ISsaLogService ssaLogService;

    @Autowired
    private ISoaLandingPageService soaLandingPageService;

    @Autowired
    private ISsaUposVideoService ssaUposVideoService;

    @Autowired
    private ISsaSplashScreenJumpInfoService jumpInfoService;

    @Autowired
    private TvCreativeJumpHandler tvCreativeJumpHandler;

    @Autowired
    private SsaQrInfoService ssaQrInfoService;

    @Value("${ott.ssa.base.img.url.md5:c53cd451e8b6f0a1e581f2b9e6c4b079}")
    private String ottBaseImgMd5;

    @Value("${ott.ssa.base.img.url:https://i0.hdslb.com/bfs/sycp/creative_img/202206/937b20e7fc1cf2b464a41d8596134ce8.png}")
    private String ottBaseImgUrl;

    @Autowired
    private CreativeExtService creativeExtService;

    @Autowired
    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveOttCpt(OttCptCreativeDto dto, Operator operator) throws ServiceException {
        GdOrderDto orderDto = orderService.getOrderById(dto.getGdOrderId());
        ScheduleDto scheduleDto = scheduleService.getScheduleById(dto.getScheduleId());

        setCreativeLaunchTime(scheduleDto, dto);

        validator.validateOttBasicInfo(operator, dto, scheduleDto);
        SsaSplashScreenPo po = this.newExternalSplashScreenDto2Po(dto, orderDto, operator, scheduleDto);

        int result = ssaSplashScreenDao.insertSelective(po);
        dto.setId(po.getId());
        Assert.isTrue(result > 0, "新建闪屏失败");

        this.saveSsaRelatedInfo(scheduleDto.getOrderProduct(), dto);

        recordLog(dto, operator, SsaLogFlag.SPLASH_SCREEN_NEW, SsaLogOperateType.ADD_SPLASH_SCREEN);

        return po.getId();
    }

    @Override
    public void batchSaveOttCpt(Operator operator, List<Integer> scheduleIdList, OttCptCreativeDto ottCptCreativeDto) throws ServiceException {
        log.info("OttCreativeService batchSaveOttCpt operator:{}，scheduleIdList:{}，OttCptCreativeDto：{}", operator, scheduleIdList, ottCptCreativeDto);
        Assert.notEmpty(scheduleIdList, "排期ID列表不可为空");
        Assert.notNull(ottCptCreativeDto, "创意信息不可为空");

        List<RLock> batchCreateLocks = new ArrayList<>();
        Transaction t = Cat.newTransaction("ott_creative_transaction", "batch_add_ott_creative");
        try {
            for (Integer scheduleId : scheduleIdList) {
                String key = "batch_create_ott_creatives_lock:" + scheduleId;
                RLock lock = RlockUtil.getInstance().getLock(key, 0, 60, "批量创意创建中，请稍等~", TimeUnit.SECONDS);
                batchCreateLocks.add(lock);
            }
            String title = ottCptCreativeDto.getTitle();
            for (Integer scheduleId : scheduleIdList) {
                ottCptCreativeDto.setScheduleId(scheduleId);
                ottCptCreativeDto.setTitle(title + scheduleId);
                ((OttCreativeService) AopContext.currentProxy()).saveOttCpt(ottCptCreativeDto, operator);
            }
        } catch (Exception e) {
            t.setStatus(e);
            throw e;
        } finally {
            batchCreateLocks.forEach(Lock::unlock);
            t.complete();
        }
    }


    private void recordLog(OttCptCreativeDto creative, Operator operator, SsaLogFlag logFlag, SsaLogOperateType operateType) {
        OttCptCreativeLogBean logBean = new OttCptCreativeLogBean();
        BeanUtils.copyProperties(creative, logBean);

        logBean.setIsSkip(creative.getIsSkip() != null && creative.getIsSkip() == 1 ? "是" : "否");
        logBean.setShowStyle(SsaShowStyleType.getByCode(creative.getShowStyle()).getDesc());
        logBean.setIssuedTime(SsaIssuedTimeType.getByCode(creative.getIssuedTime()).getDesc());
        logBean.setCmMark(creative.getCmMark() != null && creative.getCmMark() == 1 ? "广告" : "无");
        ssaLogService.insertLog(creative.getId(), logFlag, operateType, operator, logBean);
    }

    private void setCreativeLaunchTime(ScheduleDto schedule, OttCptCreativeDto creative) {

        SsaNewScheduleSplashScreenMappingDto dto = new SsaNewScheduleSplashScreenMappingDto();

        dto.setBeginTime(schedule.getGdBeginTime());
        dto.setEndTime(schedule.getGdEndTime());
        dto.setSplashScreenId(creative.getId());
        dto.setGdScheduleId(schedule.getScheduleId());
        dto.setScheduleId(schedule.getScheduleId());

        creative.setSsaNewScheduleSplashScreenMappingDtos(Collections.singletonList(dto));
    }

    private void saveSsaRelatedInfo(int orderProduct, OttCptCreativeDto dto) throws ServiceException {

        //保存闪屏排期信息
        List<SsaNewScheduleSplashScreenMappingDto> ssaNewScheduleSplashScreenMappingDtos = dto.getSsaNewScheduleSplashScreenMappingDtos();
        if (!CollectionUtils.isEmpty(ssaNewScheduleSplashScreenMappingDtos)) {
            ssaNewScheduleSplashScreenMappingDtos
                    .forEach(ssaNewScheduleSplashScreenMappingDto -> ssaNewScheduleSplashScreenMappingDto.setSplashScreenId(dto.getId()));
            ssaSplashScreenSchduleService.batchSave(ssaNewScheduleSplashScreenMappingDtos);
        }

        // 保存跳转链接信息
        List<SplashScreenJumpDTO> jumpDTOS = dto.getSplashScreenJumpDTOS();
        if (!CollectionUtils.isEmpty(jumpDTOS)) {
            jumpDTOS = jumpDTOS.stream().peek(t -> {
                try {
                    t.setJumpLink(tvCreativeJumpHandler.getJumpUrl(OrderProduct.SSA_OTT_CPT.getCode(),
                            t.getJumpType(), t.getJumpLink(), t.getLiveType(), t.getSeasonId(),
                            t.getEpId(), t.getJumpMgkVideoId()));
                } catch (ServiceException e) {
                    throw new IllegalArgumentException("稿件或直播在tv端不存在");
                }
                t.setPlatformId(PlatformType.OTT.getCode());
            }).collect(Collectors.toList());
            jumpInfoService.batchSaveJumpInfo(OrderProduct.SSA_OTT_CPT.getCode(), dto.getId(), jumpDTOS, new HashMap<>());
        }

        //保存版本控制信息
        if (!CollectionUtils.isEmpty(dto.getSsaNewSplashScreenVersionControlDtos())) {
            List<SsaNewSplashScreenVersionControlDto> ssaNewSplashScreenVersionControlDtos = dto.getSsaNewSplashScreenVersionControlDtos();
            ssaNewSplashScreenVersionControlDtos
                    .forEach(ssaNewSplashScreenVersionControlDto -> ssaNewSplashScreenVersionControlDto.setSplashScreenId(dto.getId()));
            ssaSplashScreenVersionService.batchSave(ssaNewSplashScreenVersionControlDtos);
        }

        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        //保存视频信息
        if (ssaShowStyleType.isVideo()) {
            SsaSplashScreenVideoDto videoDto = dto.getVideoDto();
            SsaNewSplashScreenVideoDto video = new SsaNewSplashScreenVideoDto();
            BeanUtils.copyProperties(videoDto, video);
            video.setSplashScreenId(dto.getId());
            video.setSalesType(SalesType.OTT_CPT.getCode());
            video.setOrderProduct(orderProduct);
            video.setVideoType(SsaVideoTypeEnum.SSA_NORMAL.getCode());
            ssaSplashScreenVideoService.save(video);
        }

        // 保存监测链接信息
        if (!CollectionUtils.isEmpty(dto.getSsaCustomizedDTOS())) {
            ssaSplashScreenCustomUrlService.batchSave(dto.getId(), dto.getSsaCustomizedDTOS());
        }

        //保存图片
        this.saveImages(dto.getId(), dto.getImageDtos());

        //保存二维码边框和动效信息
        ssaQrInfoService.saveOrUpdateQrInfo(dto.getId(), dto.getQrInfo());
        //拓展信息
        this.saveCreativeExt(orderProduct, dto);
    }

    private SsaSplashScreenPo newExternalSplashScreenDto2Po(
            OttCptCreativeDto dto, GdOrderDto orderDto, Operator operator, ScheduleDto scheduleDto) {
        OttScreenType ottScreenType = OttScreenType.getByCode(scheduleDto.getOttScreenType());
        OttScreenStyle ottScreenStyle = OttScreenStyle.getByCodeWithoutEx(scheduleDto.getOttScreenStyle());
        SsaShowStyleType showStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());

        Integer cycleId = scheduleDto.getCycleId();

        SsaSplashScreenPo ssaSplashScreenPo = new SsaSplashScreenPo();
        ssaSplashScreenPo.setSsaOrderId(0);
        ssaSplashScreenPo.setBusinessSideId(orderDto.getBusinessSideId());
        AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(operator.getOperatorId());
        if (null != accountBaseDto) {
            ssaSplashScreenPo.setBusinessSideType(accountBaseDto.getIsInner().equals(0) ? 2 : 1);
        }
        ssaSplashScreenPo.setCopywriting(dto.getCopywriting());
        ssaSplashScreenPo.setCycleId(cycleId);
        ssaSplashScreenPo.setIsSkip(dto.getIsSkip());
        ssaSplashScreenPo.setIssuedTime(dto.getIssuedTime());
        ssaSplashScreenPo.setShowStyle(dto.getShowStyle());
        ssaSplashScreenPo.setClickArea(scheduleDto.getClickArea());
        ssaSplashScreenPo.setCardType(this.getCardType(ottScreenType, ottScreenStyle));
        ssaSplashScreenPo.setCmMark(dto.getCmMark());
        ssaSplashScreenPo.setTitle(dto.getTitle());
        ssaSplashScreenPo.setType(SsaSplashScreenType.OPERATE.getCode());
        ssaSplashScreenPo.setStatus(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode());
        ssaSplashScreenPo.setGdScheduleId(dto.getScheduleId());
        ssaSplashScreenPo.setGdOrderId(orderDto.getOrderId());
        ssaSplashScreenPo.setAccountId(operator.getOperatorId());
        ssaSplashScreenPo.setCreatorName(operator.getBilibiliUserName());
        ssaSplashScreenPo.setPlatformSwitch(0);
        ssaSplashScreenPo.setShowTime(this.getShowTime(showStyleType, dto.getVideoDto()));
        ssaSplashScreenPo.setSalesType(scheduleDto.getSalesType());
        ssaSplashScreenPo.setSourceType(scheduleDto.getResType());
        if (dto.getTimeTarget() != null) {
            ssaSplashScreenPo.setTimeTarget(IsValid.getByCode(dto.getTimeTarget()).getCode());
        }
        if (dto.getEncryption() != null) {
            ssaSplashScreenPo.setEncryption(IsValid.getByCode(dto.getEncryption()).getCode());
        }
        ssaSplashScreenPo.setTimeTarget(dto.getTimeTarget());

        if (SsaJumpType.H5_DOWNLOAD.getCode().equals(dto.getQrJumpType())) {
            MgkLandingPageBean pageBean = soaLandingPageService
                    .validatePageIdAndGetLandingPage(MgkJumpTypeEnum.PAGE_ID.getCode(),
                            dto.getQrJumpUrl());
            ssaSplashScreenPo.setQrJumpUrl(pageBean.getLaunchUrl());
        } else {
            ssaSplashScreenPo.setQrJumpUrl(dto.getQrJumpUrl());
        }
        ssaSplashScreenPo.setQrJumpType(dto.getQrJumpType());
        ssaSplashScreenPo.setQrPopTime(dto.getQrPopTime());
        ssaSplashScreenPo.setSupportRealTimeDelivery(ottScreenType.getSupportRealTimeDelivery());
        return ssaSplashScreenPo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateOttCpt(OttCptCreativeDto dto, Operator operator) throws ServiceException {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        SsaSplashScreenDetailDto oldSplashScreen = delegate.getSsaSplashScreenDetailById(dto.getId());
        ScheduleDto scheduleDto = scheduleService.getScheduleById(oldSplashScreen.getGdScheduleId());

        setCreativeLaunchTime(scheduleDto, dto);

        validator.validateExternalUpdateSplashScreenBasicInfo(dto, oldSplashScreen, scheduleDto);

        Timestamp updatedLaunchStartTime = scheduleDto.getGdBeginTime();

        SsaSplashScreenPo ssaSplashScreenPo = this.updateExternalSplashScreenDto2Po(oldSplashScreen, dto, updatedLaunchStartTime);

        int result = ssaSplashScreenDao.updateByPrimaryKeySelective(ssaSplashScreenPo);

        Assert.isTrue(result > 0, dto.getId() + "闪屏编辑失败");

        ssaSplashScreenSchduleService.update(dto.getSsaNewScheduleSplashScreenMappingDtos());

        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(oldSplashScreen.getShowStyle());
        if (ssaShowStyleType.isVideo()) {
            SsaSplashScreenVideoDto videoDto = dto.getVideoDto();
            SsaUpdateSplashScreenVideoDto video = new SsaUpdateSplashScreenVideoDto();
            BeanUtils.copyProperties(videoDto, video);
            video.setOrderProduct(scheduleDto.getOrderProduct());
            video.setSalesType(SalesType.OTT_CPT.getCode());
            video.setVideoType(SsaVideoTypeEnum.SSA_NORMAL.getCode());
            video.setSplashScreenId(dto.getId());
            ssaSplashScreenVideoService.insertOrUpdate(video);
        }

        try {
            ssaSplashScreenCustomUrlService.batchSave(dto.getId(),
                    dto.getSsaCustomizedDTOS());
        } catch (Exception e) {
            log.error("errInfo" + e);
            throw new ServiceException("闪屏编辑失败");
        }


        //保存图片
        this.saveImages(dto.getId(), dto.getImageDtos());

        // 保存跳转链接信息
        List<SplashScreenJumpDTO> jumpDTOS = dto.getSplashScreenJumpDTOS();
        if (!CollectionUtils.isEmpty(jumpDTOS)) {
            jumpDTOS = jumpDTOS.stream().peek(t -> {
                try {
                    t.setJumpLink(tvCreativeJumpHandler.getJumpUrl(OrderProduct.SSA_OTT_CPT.getCode(),
                            t.getJumpType(), t.getJumpLink(), t.getLiveType(),
                            t.getSeasonId(), t.getEpId(), t.getJumpMgkVideoId()));
                } catch (ServiceException e) {
                    throw new IllegalArgumentException("稿件或直播在tv端不存在");
                }
                t.setPlatformId(PlatformType.OTT.getCode());
            }).collect(Collectors.toList());
            jumpInfoService.batchUpdateJumpInfo(OrderProduct.SSA_OTT_CPT.getCode(), dto.getId(), jumpDTOS, new HashMap<>());
        }

        //保存二维码边框和动效信息
        ssaQrInfoService.saveOrUpdateQrInfo(dto.getId(), dto.getQrInfo());
        //拓展信息
        this.saveCreativeExt(scheduleDto.getOrderProduct(), dto);

        recordLog(dto, operator, SsaLogFlag.SPLASH_SCREEN_UPDATE, SsaLogOperateType.UPDATE_SPLASH_SCREEN);

        return result;
    }

    private SsaSplashScreenPo updateExternalSplashScreenDto2Po(SsaSplashScreenDetailDto oldSplashScreen, OttCptCreativeDto update, Timestamp updatedLaunchStartTime) {
        SsaSplashScreenPo updateSplashScreenPo = this.updateSplashScreenDto2Po(oldSplashScreen, update);
        if (updateSplashScreenPo.getStatus() == null
                || !SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode().equals(updateSplashScreenPo.getStatus())) {
            int currentStatus = delegate.getCurrentStatusByIssuedTime(oldSplashScreen, SsaIssuedTimeType.getByCode(update.getIssuedTime()), updatedLaunchStartTime);
            if (!oldSplashScreen.getStatus().equals(currentStatus)) {
                updateSplashScreenPo.setStatus(currentStatus);
            }
        }

        return updateSplashScreenPo;
    }

    private SsaSplashScreenPo updateSplashScreenDto2Po(SsaSplashScreenDetailDto oldSplashScreenDto, OttCptCreativeDto dto) {
        SsaSplashScreenPo po = new SsaSplashScreenPo();
        po.setId(dto.getId());
        po.setCopywriting(dto.getCopywriting());
        po.setIsSkip(dto.getIsSkip());
        po.setCmMark(dto.getCmMark());
        po.setIssuedTime(dto.getIssuedTime());
        //show_style和card_type都不能修改
        //po.setShowStyle(dto.getShowStyle());
        //SsaShowStyleType showStyleType = SsaShowStyleType.getByCode(dto.getShowStyle());
        //po.setCardType(oldSplashScreenDto.getCardType());
        po.setTitle(dto.getTitle());
        po.setShowTime(this.getShowTime(SsaShowStyleType.getByCode(oldSplashScreenDto.getShowStyle()), dto.getVideoDto()));

        if (isNeedReAudit(oldSplashScreenDto, dto)) {
            po.setStatus(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode());
        }
        if (!SsaSplashScreenType.OPERATE.getCode().equals(oldSplashScreenDto.getType()) &&
                SsaSplashScreenStatus.AUDIT_REJECT.getCode().equals(oldSplashScreenDto.getStatus())) {
            po.setStatus(SsaSplashScreenStatus.ON_LINE.getCode());
        }
        if (dto.getTimeTarget() != null) {
            po.setTimeTarget(IsValid.getByCode(dto.getTimeTarget()).getCode());
        }
        if (dto.getEncryption() != null) {
            po.setEncryption(IsValid.getByCode(dto.getEncryption()).getCode());
        }
        po.setTimeTarget(dto.getTimeTarget());
        if (SsaJumpType.H5_DOWNLOAD.getCode().equals(dto.getQrJumpType())) {
            MgkLandingPageBean pageBean = soaLandingPageService
                    .validatePageIdAndGetLandingPage(MgkJumpTypeEnum.PAGE_ID.getCode(),
                            dto.getQrJumpUrl());
            po.setQrJumpUrl(pageBean.getLaunchUrl());
        } else {
            po.setQrJumpUrl(dto.getQrJumpUrl());
        }
        po.setQrJumpType(dto.getQrJumpType());
        po.setQrPopTime(dto.getQrPopTime());
        return po;
    }

    public Boolean isNeedReAudit(SsaSplashScreenDetailDto detailDto, OttCptCreativeDto dto) {
        if (!SsaSplashScreenType.OPERATE.getCode().equals(detailDto.getType())) {
            return false;
        }

        if (SsaSplashScreenStatus.AUDIT_REJECT.getCode().equals(detailDto.getStatus())) {
            return true;
        }

        if (!dto.getCmMark().equals(detailDto.getCmMark())) {
            return true;
        }

        if (!dto.getShowStyle().equals(detailDto.getShowStyle())) {
            return true;
        }

        if (!dto.getIsSkip().equals(detailDto.getIsSkip())) {
            return true;
        }

        if (!Strings.isNullOrEmpty(dto.getCopywriting()) && !Strings.isNullOrEmpty(detailDto.getCopywriting())
                && !dto.getCopywriting().equals(detailDto.getCopywriting())) {
            return true;
        }

        if (!Strings.isNullOrEmpty(dto.getCopywriting()) && Strings.isNullOrEmpty(detailDto.getCopywriting())) {
            return true;
        }

        if (!dto.getQrPopTime().equals(detailDto.getQrPopTime())) {
            return true;
        }

        if (!dto.getQrJumpUrl().equals(detailDto.getQrJumpUrl())) {
            return true;
        }

        return !Strings.isNullOrEmpty(detailDto.getCopywriting()) && Strings.isNullOrEmpty(dto.getCopywriting());
    }


    @Override
    public OttCptCreativeDto getSsaSplashScreenById(Integer splashScreenId) throws ServiceException {

        log.info("SsaSplashScreenServiceDelegate.getSsaSplashScreenById splashScreenId - {}", splashScreenId);

        Assert.notNull(splashScreenId, "闪屏ID不可为空");

        SsaSplashScreenPoExample ssaSplashScreenPoExample = new SsaSplashScreenPoExample();
        ssaSplashScreenPoExample.or().andIdEqualTo(splashScreenId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaSplashScreenPo> ssaSplashScreenPos = ssaSplashScreenDao.selectByExample(ssaSplashScreenPoExample);

        Assert.notEmpty(ssaSplashScreenPos, splashScreenId + "对应的闪屏信息不存在");

        OttCptCreativeDto creativeDto = this.ssaSplashScreenPo2Dto(ssaSplashScreenPos.get(0));

        List<SsaScheduleSplashScreenMappingDto> splashScreenMappingDtos = ssaSplashScreenSchduleService
                .getSplashScreenSchduleBySplashScreenId(splashScreenId);

        List<Integer> ssaSchduleIds = splashScreenMappingDtos.stream().map(SsaScheduleSplashScreenMappingDto::getScheduleId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ssaSchduleIds)) {
            Map<Integer, SsaScheduleSplashScreenMappingDto> mappingDtoMap = splashScreenMappingDtos.stream()
                    .collect(Collectors.toMap(SsaScheduleSplashScreenMappingDto::getScheduleId, Function.identity(), (a, b) -> a));
            List<SsaScheduleDto> ssaScheduleDtos = ssaScheduleService.querySsaSchedule(SsaQueryScheduleDto.builder().ssaScheduleIds(ssaSchduleIds).build());
            ssaScheduleDtos.forEach(ssaScheduleDto -> {
                SsaScheduleSplashScreenMappingDto mappingDto = mappingDtoMap.get(ssaScheduleDto.getSsaScheduleId());
                ssaScheduleDto.setSsaStartTime(mappingDto == null ? null : mappingDto.getStartTime());
                ssaScheduleDto.setSsaEndTime(mappingDto == null ? null : mappingDto.getEndTime());
            });
            creativeDto.setSsaScheduleDtos(ssaScheduleDtos);
        } else {
            creativeDto.setSsaScheduleDtos(Collections.emptyList());
        }

        creativeDto.setImageDtos(ssaSplashScreenImageService.getSsaSplashScreenImageBySplashScreenId(splashScreenId));

        creativeDto.setVideoDto(ssaSplashScreenVideoService.getVideoBySplashScreenIdAndVideoType(creativeDto.getId(), SsaVideoTypeEnum.SSA_NORMAL.getCode()));

        List<SplashScreenJumpDTO> jumpDTOS = jumpInfoService.getJumpInfo(creativeDto.getId());
        if (!CollectionUtils.isEmpty(jumpDTOS)) {
            jumpDTOS = jumpDTOS.stream().peek(t -> t.setJumpLink(tvCreativeJumpHandler.parseJumpUrl(0,
                            OrderProduct.SSA_OTT_CPT.getCode(), t.getJumpType(), t.getJumpLink())))
                    .collect(Collectors.toList());
            creativeDto.setSplashScreenJumpDTOS(jumpDTOS);
        }

        creativeDto.setQrInfo(ssaQrInfoService.queryQrInfo(splashScreenId));

        creativeDto.setProductLabel(this.creativeExtService.getCreativeExtHolder(creativeDto.getOrderProduct(),
                creativeDto.getId().longValue()).getProductLabel());

        return creativeDto;
    }

    private OttCptCreativeDto ssaSplashScreenPo2Dto(SsaSplashScreenPo po) throws ServiceException {
        OttCptCreativeDto dto = ssaSplashScreenPo2DtoSimple(po);
        if (delegate.isVideoSplashScreen(dto.getShowStyle())) {
            dto.setVideoDto(ssaSplashScreenVideoService.getVideoBySplashScreenIdAndVideoType(dto.getId(), SsaVideoTypeEnum.SSA_NORMAL.getCode()));
        }

        Map<Integer, Map<Integer, List<String>>> urlMap = ssaSplashScreenCustomUrlService.getUrlPlatformMap(dto.getId());
        List<SplashScreenCustomizedDTO> list = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, List<String>>> entry : urlMap.entrySet()) {
            list.add(SplashScreenCustomizedDTO.builder().platformId(entry.getKey()).ssaCustomizedImpUrlList(entry.getValue()
                            .getOrDefault(CustomizedUrlType.SHOW_URL.getCode(), new ArrayList<>()))
                    .ssaCustomizedClickUrlList(entry.getValue()
                            .getOrDefault(CustomizedUrlType.CLICK_URL.getCode(), new ArrayList<>())).build());
        }
        dto.setSsaCustomizedDTOS(list);
        dto.setScheduleId(po.getGdScheduleId());
        dto.setGdOrderId(po.getGdOrderId());

        ScheduleDto scheduleDto = scheduleService.getScheduleById(po.getGdScheduleId());
        dto.setScheduleName(scheduleDto.getName());
        dto.setSalesType(scheduleDto.getSalesType());
        GdOrderDto orderDto = orderService.getOrderById(po.getGdOrderId());
        dto.setOrderName(orderDto.getOrderName());
        dto.setQrJumpType(po.getQrJumpType());
        dto.setQrJumpUrl(po.getQrJumpUrl());
        dto.setQrPopTime(po.getQrPopTime());
        dto.setOrderProduct(orderDto.getProduct());
        return dto;
    }

    private OttCptCreativeDto ssaSplashScreenPo2DtoSimple(SsaSplashScreenPo po) {
        OttCptCreativeDto dto = new OttCptCreativeDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    //拓展信息
    private void saveCreativeExt(Integer orderProduct, OttCptCreativeDto dto) {
        this.creativeExtService.saveCreativeExt(CreativeExtHolderDto.builder()
                .orderId(dto.getGdOrderId())
                .scheduleId(dto.getScheduleId())
                .creativeId(dto.getId().longValue())
                .orderProduct(orderProduct)
                .productLabel(dto.getProductLabel())
                .build());
    }


    //cardType
    //https://www.tapd.cn/67874887/prong/stories/view/1167874887004261902
    //预期：60-OTT_topview, 61-OTT_视频闪屏（改个名字），130-OTT_图片闪屏；针对图片闪屏cardtype，仅客户端171版本以上支持下发（版本号：107100）
    private Integer getCardType(OttScreenType screenType, OttScreenStyle screenStyle) {
        if (Objects.equals(screenType, OttScreenType.TOP_VIEW)) {
            return 60;
        }
        return screenStyle.getCardType();
    }

    //广告展示时长，单位：秒
    //https://www.tapd.cn/67874887/prong/stories/view/1167874887004261902
    //图片闪屏、视频闪屏、topview都支持下发：图片闪屏固定展示时长为5s，暂不支持编辑；Topview、视频闪屏展示时长=视频时长
    public Integer getShowTime(SsaShowStyleType showStyleType, SsaSplashScreenVideoDto video) {
        if (showStyleType.isVideo()) {
            SsaUposVideoDto uposVideo = ssaUposVideoService.getSsaUposVideoByBizId(video.getBizId(),
                    Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.SSA_BUP.getCode()));
            Assert.notNull(uposVideo.getXcodeDuration(), "视频转码失败，请核实视频是否符合规范");
            return uposVideo.getXcodeDuration() / 1000;
        }
        return 5;
    }


    private void saveImages(Integer splashScreenId, List<SsaSplashScreenImageDto> imageDtos){
        List<SsaNewSplashScreenImageDto> imageList;
        if (CollectionUtils.isEmpty(imageDtos)) {
            //引擎必须要一张兜底图不然索引构建不出来
            imageList = Lists.newArrayList(SsaNewSplashScreenImageDto.builder()
                    .splashScreenId(splashScreenId)
                    .platformId(PlatformType.OTT.getCode())
                    .imageRuleId(0)
                    .status(1)
                    .scheduleGroupId(0)
                    .brandCreativeId(0)
                    .appPackageId(0)
                    .width(BaseImageTypeEnum.OTT_SSA.getWidth())
                    .height(BaseImageTypeEnum.OTT_SSA.getHeight())
                    .md5(ottBaseImgMd5)
                    .url(ottBaseImgUrl)
                    .build());
        } else {
            imageList = imageDtos.stream().map(t -> {
                SsaNewSplashScreenImageDto imageDto = new SsaNewSplashScreenImageDto();
                BeanUtils.copyProperties(t, imageDto);
                imageDto.setSplashScreenId(splashScreenId);
                imageDto.setImageRuleId(0);
                imageDto.setStatus(1);
                imageDto.setScheduleGroupId(0);
                imageDto.setBrandCreativeId(0);
                imageDto.setAppPackageId(0);
                return imageDto;
            }).collect(Collectors.toList());
        }
        ssaSplashScreenImageService.batchSave(imageList);

        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004261902
        //保存ssa_splash_screen_base_image数据，理论上不需要这个数据（因为ott已经是单端了，不需要分端裁剪），但是为了兼容通用的闪屏接口，
        //因此冗余一份数据，（已经和引擎确认，引擎除了查询logo图之外，没有其他用）
        ssaSplashScreenBaseImageService.bathSave(imageList
                .stream()
                .map(image -> SsaNewSplashScreenBaseImageDto.builder()
                        .splashScreenId(splashScreenId)
                        .type(BaseImageTypeEnum.OTT_SSA.getCode())
                        .hash(ImageUtil.buildHashCodeForSsa(BaseImageTypeEnum.OTT_SSA, image.getUrl(), image.getMd5()))
                        .url(image.getUrl())
                        .md5(image.getMd5())
                        .build())
                .collect(Collectors.toList()));
    }
}
