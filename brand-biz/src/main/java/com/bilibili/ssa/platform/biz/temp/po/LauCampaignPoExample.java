package com.bilibili.ssa.platform.biz.temp.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class LauCampaignPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public LauCampaignPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCampaignNameIsNull() {
            addCriterion("campaign_name is null");
            return (Criteria) this;
        }

        public Criteria andCampaignNameIsNotNull() {
            addCriterion("campaign_name is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignNameEqualTo(String value) {
            addCriterion("campaign_name =", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameNotEqualTo(String value) {
            addCriterion("campaign_name <>", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameGreaterThan(String value) {
            addCriterion("campaign_name >", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameGreaterThanOrEqualTo(String value) {
            addCriterion("campaign_name >=", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameLessThan(String value) {
            addCriterion("campaign_name <", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameLessThanOrEqualTo(String value) {
            addCriterion("campaign_name <=", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameLike(String value) {
            addCriterion("campaign_name like", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameNotLike(String value) {
            addCriterion("campaign_name not like", value, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameIn(List<String> values) {
            addCriterion("campaign_name in", values, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameNotIn(List<String> values) {
            addCriterion("campaign_name not in", values, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameBetween(String value1, String value2) {
            addCriterion("campaign_name between", value1, value2, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCampaignNameNotBetween(String value1, String value2) {
            addCriterion("campaign_name not between", value1, value2, "campaignName");
            return (Criteria) this;
        }

        public Criteria andCostTypeIsNull() {
            addCriterion("cost_type is null");
            return (Criteria) this;
        }

        public Criteria andCostTypeIsNotNull() {
            addCriterion("cost_type is not null");
            return (Criteria) this;
        }

        public Criteria andCostTypeEqualTo(Integer value) {
            addCriterion("cost_type =", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotEqualTo(Integer value) {
            addCriterion("cost_type <>", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeGreaterThan(Integer value) {
            addCriterion("cost_type >", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cost_type >=", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeLessThan(Integer value) {
            addCriterion("cost_type <", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cost_type <=", value, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeIn(List<Integer> values) {
            addCriterion("cost_type in", values, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotIn(List<Integer> values) {
            addCriterion("cost_type not in", values, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeBetween(Integer value1, Integer value2) {
            addCriterion("cost_type between", value1, value2, "costType");
            return (Criteria) this;
        }

        public Criteria andCostTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cost_type not between", value1, value2, "costType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNull() {
            addCriterion("budget_type is null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIsNotNull() {
            addCriterion("budget_type is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeEqualTo(Integer value) {
            addCriterion("budget_type =", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotEqualTo(Integer value) {
            addCriterion("budget_type <>", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThan(Integer value) {
            addCriterion("budget_type >", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("budget_type >=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThan(Integer value) {
            addCriterion("budget_type <", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeLessThanOrEqualTo(Integer value) {
            addCriterion("budget_type <=", value, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeIn(List<Integer> values) {
            addCriterion("budget_type in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotIn(List<Integer> values) {
            addCriterion("budget_type not in", values, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeBetween(Integer value1, Integer value2) {
            addCriterion("budget_type between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("budget_type not between", value1, value2, "budgetType");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNull() {
            addCriterion("budget is null");
            return (Criteria) this;
        }

        public Criteria andBudgetIsNotNull() {
            addCriterion("budget is not null");
            return (Criteria) this;
        }

        public Criteria andBudgetEqualTo(Long value) {
            addCriterion("budget =", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotEqualTo(Long value) {
            addCriterion("budget <>", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThan(Long value) {
            addCriterion("budget >", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetGreaterThanOrEqualTo(Long value) {
            addCriterion("budget >=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThan(Long value) {
            addCriterion("budget <", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetLessThanOrEqualTo(Long value) {
            addCriterion("budget <=", value, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetIn(List<Long> values) {
            addCriterion("budget in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotIn(List<Long> values) {
            addCriterion("budget not in", values, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetBetween(Long value1, Long value2) {
            addCriterion("budget between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andBudgetNotBetween(Long value1, Long value2) {
            addCriterion("budget not between", value1, value2, "budget");
            return (Criteria) this;
        }

        public Criteria andSpeedModeIsNull() {
            addCriterion("speed_mode is null");
            return (Criteria) this;
        }

        public Criteria andSpeedModeIsNotNull() {
            addCriterion("speed_mode is not null");
            return (Criteria) this;
        }

        public Criteria andSpeedModeEqualTo(Integer value) {
            addCriterion("speed_mode =", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeNotEqualTo(Integer value) {
            addCriterion("speed_mode <>", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeGreaterThan(Integer value) {
            addCriterion("speed_mode >", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("speed_mode >=", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeLessThan(Integer value) {
            addCriterion("speed_mode <", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeLessThanOrEqualTo(Integer value) {
            addCriterion("speed_mode <=", value, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeIn(List<Integer> values) {
            addCriterion("speed_mode in", values, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeNotIn(List<Integer> values) {
            addCriterion("speed_mode not in", values, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeBetween(Integer value1, Integer value2) {
            addCriterion("speed_mode between", value1, value2, "speedMode");
            return (Criteria) this;
        }

        public Criteria andSpeedModeNotBetween(Integer value1, Integer value2) {
            addCriterion("speed_mode not between", value1, value2, "speedMode");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNull() {
            addCriterion("sales_type is null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNotNull() {
            addCriterion("sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeEqualTo(Integer value) {
            addCriterion("sales_type =", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotEqualTo(Integer value) {
            addCriterion("sales_type <>", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThan(Integer value) {
            addCriterion("sales_type >", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_type >=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThan(Integer value) {
            addCriterion("sales_type <", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sales_type <=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIn(List<Integer> values) {
            addCriterion("sales_type in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotIn(List<Integer> values) {
            addCriterion("sales_type not in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("sales_type between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_type not between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusIsNull() {
            addCriterion("campaign_status is null");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusIsNotNull() {
            addCriterion("campaign_status is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusEqualTo(Integer value) {
            addCriterion("campaign_status =", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusNotEqualTo(Integer value) {
            addCriterion("campaign_status <>", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusGreaterThan(Integer value) {
            addCriterion("campaign_status >", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_status >=", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusLessThan(Integer value) {
            addCriterion("campaign_status <", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_status <=", value, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusIn(List<Integer> values) {
            addCriterion("campaign_status in", values, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusNotIn(List<Integer> values) {
            addCriterion("campaign_status not in", values, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusBetween(Integer value1, Integer value2) {
            addCriterion("campaign_status between", value1, value2, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_status not between", value1, value2, "campaignStatus");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeIsNull() {
            addCriterion("campaign_status_mtime is null");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeIsNotNull() {
            addCriterion("campaign_status_mtime is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeEqualTo(Timestamp value) {
            addCriterion("campaign_status_mtime =", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeNotEqualTo(Timestamp value) {
            addCriterion("campaign_status_mtime <>", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeGreaterThan(Timestamp value) {
            addCriterion("campaign_status_mtime >", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("campaign_status_mtime >=", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeLessThan(Timestamp value) {
            addCriterion("campaign_status_mtime <", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("campaign_status_mtime <=", value, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeIn(List<Timestamp> values) {
            addCriterion("campaign_status_mtime in", values, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeNotIn(List<Timestamp> values) {
            addCriterion("campaign_status_mtime not in", values, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("campaign_status_mtime between", value1, value2, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andCampaignStatusMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("campaign_status_mtime not between", value1, value2, "campaignStatusMtime");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNull() {
            addCriterion("promotion_purpose_type is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIsNotNull() {
            addCriterion("promotion_purpose_type is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeEqualTo(Integer value) {
            addCriterion("promotion_purpose_type =", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <>", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThan(Integer value) {
            addCriterion("promotion_purpose_type >", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type >=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThan(Integer value) {
            addCriterion("promotion_purpose_type <", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("promotion_purpose_type <=", value, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeIn(List<Integer> values) {
            addCriterion("promotion_purpose_type in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotIn(List<Integer> values) {
            addCriterion("promotion_purpose_type not in", values, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andPromotionPurposeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("promotion_purpose_type not between", value1, value2, "promotionPurposeType");
            return (Criteria) this;
        }

        public Criteria andOriginTagIsNull() {
            addCriterion("origin_tag is null");
            return (Criteria) this;
        }

        public Criteria andOriginTagIsNotNull() {
            addCriterion("origin_tag is not null");
            return (Criteria) this;
        }

        public Criteria andOriginTagEqualTo(Integer value) {
            addCriterion("origin_tag =", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagNotEqualTo(Integer value) {
            addCriterion("origin_tag <>", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagGreaterThan(Integer value) {
            addCriterion("origin_tag >", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("origin_tag >=", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagLessThan(Integer value) {
            addCriterion("origin_tag <", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagLessThanOrEqualTo(Integer value) {
            addCriterion("origin_tag <=", value, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagIn(List<Integer> values) {
            addCriterion("origin_tag in", values, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagNotIn(List<Integer> values) {
            addCriterion("origin_tag not in", values, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagBetween(Integer value1, Integer value2) {
            addCriterion("origin_tag between", value1, value2, "originTag");
            return (Criteria) this;
        }

        public Criteria andOriginTagNotBetween(Integer value1, Integer value2) {
            addCriterion("origin_tag not between", value1, value2, "originTag");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdIsNull() {
            addCriterion("lau_account_id is null");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdIsNotNull() {
            addCriterion("lau_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdEqualTo(Integer value) {
            addCriterion("lau_account_id =", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdNotEqualTo(Integer value) {
            addCriterion("lau_account_id <>", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdGreaterThan(Integer value) {
            addCriterion("lau_account_id >", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("lau_account_id >=", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdLessThan(Integer value) {
            addCriterion("lau_account_id <", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("lau_account_id <=", value, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdIn(List<Integer> values) {
            addCriterion("lau_account_id in", values, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdNotIn(List<Integer> values) {
            addCriterion("lau_account_id not in", values, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("lau_account_id between", value1, value2, "lauAccountId");
            return (Criteria) this;
        }

        public Criteria andLauAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("lau_account_id not between", value1, value2, "lauAccountId");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}