package com.bilibili.ssa.platform.biz.service.business_side;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.ssa.platform.api.business_side.dto.SsaUserDto;
import com.bilibili.ssa.platform.api.business_side.service.ISsaUserService;
import com.bilibili.ssa.platform.biz.dao.SsaBusinessSideDao;
import com.bilibili.ssa.platform.biz.dao.SsaUserBusinessSideMappingDao;
import com.bilibili.ssa.platform.biz.dao.SsaUserDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSsaUserDao;
import com.bilibili.ssa.platform.biz.po.SsaUserBusinessSideMappingPo;
import com.bilibili.ssa.platform.biz.po.SsaUserBusinessSideMappingPoExample;
import com.bilibili.ssa.platform.biz.po.SsaUserPo;
import com.bilibili.ssa.platform.biz.po.SsaUserPoExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class SsaUserServiceImpl implements ISsaUserService {
    
    private final static Logger LOGGER = LoggerFactory.getLogger(SsaUserServiceImpl.class);
    
    @Autowired
    private SsaUserDao ssaUserDao;
    @Autowired
    private ExtSsaUserDao extSsaUserDao;
    @Autowired
    private SsaBusinessSideDao ssaBusinessSideDao;
    @Autowired
    private SsaUserBusinessSideMappingDao ssaUserBusinessSideMappingDao;
    

    @Override
    public SsaUserDto getUserByName(String name) {
        Assert.notNull(name);
        SsaUserPoExample example = new SsaUserPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andNameEqualTo(name);
        List<SsaUserPo> pos = ssaUserDao.selectByExample(example);
        Assert.notEmpty(pos, "user not exist");
        SsaUserDto dto = new SsaUserDto();
        BeanUtils.copyProperties(pos.get(0), dto);
        return dto;
    }
    
    @Override
    public SsaUserDto getUserById(Integer userId) {
        Assert.notNull(userId);
        SsaUserPo po = ssaUserDao.selectByPrimaryKey(userId);
        Assert.notNull(po, "user not exist");
        SsaUserDto dto = new SsaUserDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateUserName(Operator operator, Integer userId, String userName) {
        Assert.notNull(userId, "用户ID不可为空");
        Assert.hasText(userName, "用户名称不可为空");
        Assert.notNull(ssaUserDao.selectByPrimaryKey(userId), "该用户不存在");
        
        SsaUserPo cptUserPo = new SsaUserPo();
        cptUserPo.setId(userId);
        cptUserPo.setName(userName);
        ssaUserDao.updateByPrimaryKeySelective(cptUserPo);
    }
    
    @Override
    public List<SsaUserDto> getBindedUsersByBusinessSideId(Integer businessSideId) {
        Assert.notNull(businessSideId, "业务方ID不可为空");
        Assert.notNull(ssaBusinessSideDao.selectByPrimaryKey(businessSideId), "该业务方不存在");
        
        SsaUserBusinessSideMappingPoExample mappingPoExample = new SsaUserBusinessSideMappingPoExample();
        mappingPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdEqualTo(businessSideId);
        List<SsaUserBusinessSideMappingPo> mappingPos = ssaUserBusinessSideMappingDao.selectByExample(mappingPoExample);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return Collections.emptyList();
        }
        List<Integer> userIds = mappingPos.stream().map(SsaUserBusinessSideMappingPo::getUserId).collect(Collectors.toList());
        
        SsaUserPoExample userPoExample = new SsaUserPoExample();
        userPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
        .andStatusEqualTo(Status.VALID.getCode()).andIdIn(userIds);
        
        List<SsaUserPo> cptUserPos = ssaUserDao.selectByExample(userPoExample);
        return cptUserPos.stream().map(this::cptUserPo2Dto).collect(Collectors.toList());
    }
    
    private SsaUserDto cptUserPo2Dto(SsaUserPo po){
        SsaUserDto dto = SsaUserDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public Integer save(Operator operator, String name) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.hasText(name, "用户名称不可为空");
        SsaUserPo po = new SsaUserPo();
        po.setName(name);
        po.setStatus(Status.VALID.getCode());
        extSsaUserDao.save(po);
        SsaUserDto cptUserDto = this.getUserByName(name);
        return cptUserDto.getId();
    }

    @Override
    public Map<Integer, List<SsaUserDto>> getBusinessSideUserMapInBusinessSideIds(List<Integer> businessSideIds) {
        Assert.notEmpty(businessSideIds, "业务方ID不可为空");
        
        SsaUserBusinessSideMappingPoExample mappingPoExample = new SsaUserBusinessSideMappingPoExample();
        mappingPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBusinessSideIdIn(businessSideIds);
        List<SsaUserBusinessSideMappingPo> mappingPos = ssaUserBusinessSideMappingDao.selectByExample(mappingPoExample);
        if (CollectionUtils.isEmpty(mappingPos)) {
            return Collections.emptyMap();
        }
        List<Integer> userIds = mappingPos.stream().map(SsaUserBusinessSideMappingPo::getUserId).collect(Collectors.toList());

        SsaUserPoExample userPoExample = new SsaUserPoExample();
        userPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
        .andStatusEqualTo(Status.VALID.getCode()).andIdIn(userIds);
        
        List<SsaUserPo> cptUserPos = ssaUserDao.selectByExample(userPoExample);
        Map<Integer, SsaUserDto> ssaUserMap = cptUserPos.stream().collect(Collectors.toMap(SsaUserPo::getId, this::cptUserPo2Dto));
        
        return mappingPos.stream().collect(Collectors.groupingBy(SsaUserBusinessSideMappingPo::getBusinessSideId, Collectors.mapping(po -> ssaUserMap.get(po.getUserId()), Collectors.toList())));
    }
}
