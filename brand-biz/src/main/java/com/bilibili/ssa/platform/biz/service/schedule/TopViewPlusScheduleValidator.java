package com.bilibili.ssa.platform.biz.service.schedule;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.PromotionPurposeType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.ScheduleStatus;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.common.enums.SsaTransitionModeEnum;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.service.ICycleFrequencyService;
import com.bilibili.brand.api.order.service.IGdOrderExtService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePoExample;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.ssa.platform.api.schedule.dto.SplitDaysImpressBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.TopViewPlusScheduleBo;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.TopViewPlusScheduleServiceDelegate;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.common.enums.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.util.set.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/12/5 11:43
 */
@Slf4j
@Component
public class TopViewPlusScheduleValidator {

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private SsaScheduleServiceValidator delegateValidator;

    @Autowired
    private GdScheduleDao gdScheduleDao;

    @Autowired
    private ISystemConfigService configService;

    @Autowired
    private TopViewPlusScheduleServiceDelegate topViewPlusScheduleServiceDelegate;

    @Autowired
    private ISsaSplashScreenService ssaSplashScreenService;

    @Autowired
    private ICycleFrequencyService cycleFrequencyService;

    @Autowired
    private IGdOrderExtService gdOrderExtService;

    public static final Integer DEFAULT_FREQUENCY_LIMIT = 4;
    public static final Integer MIN_FREQUENCY_LIMIT = 1;

    //支持的推广目的类型
    private static final Set<PromotionPurposeType> SUPPORTS_PPT = Sets.newHashSet(
            PromotionPurposeType.LANDING_PAGE,
            PromotionPurposeType.APP_DOWNLOAD,
            PromotionPurposeType.LIVE_ROOM,
            PromotionPurposeType.BRAND_VIDEO_PROMOTION);

    public void validate(TopViewPlusScheduleBo topViewPlusScheduleBo) throws ServiceException {
        Assert.notNull(topViewPlusScheduleBo, "排期信息不能为空");
        Assert.notNull(topViewPlusScheduleBo.getSsaScheduleBo(), "排期信息不能为空");
        Assert.notNull(topViewPlusScheduleBo.getSsaScheduleBo().getOperator(), "操作人信息不能为空");
        SsaPlusScheduleBo scheduleBo = topViewPlusScheduleBo.getSsaScheduleBo();
        Assert.hasText(scheduleBo.getName(), "排期名称不可为空");
        Assert.notNull(scheduleBo.getOrderId(), "订单ID不可为空");
        Assert.notNull(scheduleBo.getPromotionPurposeType(), "推广目的不可为空");
        PromotionPurposeType ppt = PromotionPurposeType.getByCode(scheduleBo.getPromotionPurposeType());
        Assert.isTrue(SUPPORTS_PPT.contains(ppt), "不支持的推广目的：" + ppt.getDesc());
        Integer frequencyLimit = topViewPlusScheduleBo.getSsaScheduleBo().getFrequencyLimit();
        Assert.isTrue(Objects.isNull(frequencyLimit) || (frequencyLimit <= DEFAULT_FREQUENCY_LIMIT && frequencyLimit >= MIN_FREQUENCY_LIMIT), String.format("频控限制「%s~%s」", MIN_FREQUENCY_LIMIT, DEFAULT_FREQUENCY_LIMIT));
//        https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887003002772
//        if (Objects.equals(ppt, PromotionPurposeType.APP_DOWNLOAD)) {
//            Assert.notEmpty(scheduleBo.getAppPackageIdList(), "应用包不可为空");
//            Assert.isTrue(scheduleBo.getAppPackageIdList().size() == 2,
//                    "请按照下载包支持平台：Android+IOS或Android+iPhone来选择");
//        }

        SsaSourceType.getByCode(scheduleBo.getResType());
        SsaClickAreaType.getByCode(scheduleBo.getClickArea());
        SsaJumpAreaEffect.getByCode(scheduleBo.getJumpAreaEffect());
        SsaJumpAreaStyle.getByCode(scheduleBo.getJumpAreaStyle());
        SsaClickAreaType.getByCodeWithValidation(scheduleBo.getClickArea());
        SsaVideoPlayModeEnum.getByCode(scheduleBo.getSsaVideoPlayMode());

        // 校验过渡形式与播放形式的关联
        validateTransitionMode(scheduleBo);

        BannerShowType bannerShowType = BannerShowType.getByCode(scheduleBo.getHfAdType());
        if (Objects.equals(ppt, PromotionPurposeType.LIVE_ROOM)) {
            Assert.isTrue(Objects.equals(bannerShowType, BannerShowType.LIVE_VIDEO)
                            || Objects.equals(bannerShowType, BannerShowType.LIVE_IMAGE),
                    "直播推广目的下，请选择【直播-视频兜底】或者【直播-图片兜底】首焦类型");
        }

        delegateValidator.validateShowStyle(scheduleBo.getAdType(), scheduleBo.getScreenStyle(),
                scheduleBo.getShowStyle());

        Assert.notNull(scheduleBo.getNeedWakeApp(), "唤起APP选项不可为空");
        Operator operator = scheduleBo.getOperator();
        // 应用下载下放到创意
        // https://www.tapd.cn/67874887/prong/stories/view/1167874887004170445
//        delegateValidator.validateAndSetPlatformAppPackageId(scheduleBo, operator.getOperatorId());

        GdOrderDto gdOrderDto = gdOrderService.getOrderById(scheduleBo.getOrderId(), operator);
        scheduleBo.setBusinessSideId(gdOrderDto.getBusinessSideId());

        if (Utils.isPositive(scheduleBo.getScheduleId())) {
           //检查是否有创意
            Assert.isTrue(
                    ssaSplashScreenService.countValidSsaSplashScreenByGdScheduleId(scheduleBo.getScheduleId()) == 0,
                    "当前排期存在有效创意，不允编辑");
        } else {
            //创建时的验证
            checkLimitByDay(topViewPlusScheduleBo, gdOrderDto);
        }
    }

    private void checkLimitByDay(TopViewPlusScheduleBo topViewPlusScheduleBo, GdOrderDto orderDto) {
        SsaPlusScheduleBo scheduleBo = topViewPlusScheduleBo.getSsaScheduleBo();
        OrderProduct orderProduct = OrderProduct.getByCode(scheduleBo.getOrderProduct());
        Integer gdMaxImpression = configService.getValueReturnInt(SystemConfigEnum
                .TOPVIEW_GD_MAX_TOTAL_IMPRESSION.getCode());
        Integer gdMinImpression = configService.getValueReturnInt(SystemConfigEnum
                .TOPVIEW_GD_MIN_TOTAL_IMPRESSION.getCode());
        Integer dayLimitImpression = configService.getValueReturnInt(SystemConfigEnum
                .TOPVIEW_DAY_LIMIT_IMPRESSION.getCode());
        List<SplitDaysImpressBo> impressBos = scheduleBo.getImpressBos();
        Integer oneRotationCpm = topViewPlusScheduleServiceDelegate.getOneRotationCpm(orderDto);
        GdOrderExtDto gdOrderExtInfo = gdOrderExtService.getGdOrderExtInfo(orderDto.getOrderId());
        for (SplitDaysImpressBo im : impressBos) {
            long bookedCpm = 0L;
            long bookingCpm = 0L;
            if (Objects.equals(orderProduct, OrderProduct.TOP_VIEW_CPT_PLUS)) {
                Assert.isTrue(Integer.valueOf(1).equals(im.getRotationNum()),
                        "TopView CPT+排期轮数必须为1轮");
                bookingCpm = (long) im.getRotationNum() * oneRotationCpm;
            } else {
                Assert.isTrue(Utils.isPositive(im.getImpressionCpm()), "TopView GD+的预约量不可为空");
                bookingCpm = im.getImpressionCpm();
                Assert.isTrue(gdMaxImpression >= im.getImpressionCpm()
                                && gdMinImpression <= im.getImpressionCpm(),
                        String.format("TopView GD+的预约量必须在区间【%d，%d】cpm 内", gdMinImpression, gdMaxImpression));
            }
            Timestamp beginOfDay = TimeUtil.getBeginOfDay(TimeUtil.isoTimeStr2Timestamp(im.getBeginTime()));
            GdSchedulePoExample example = new GdSchedulePoExample();
            example.or()
                    .andBeginDateEqualTo(beginOfDay)
                    .andStatusIn(Lists.newArrayList(ScheduleStatus.VALID.getCode(), ScheduleStatus.PAUSED.getCode()))
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andOrderProductIn(Lists.newArrayList(OrderProduct.TOPVIEW_CODE_SET));
            List<GdSchedulePo> gdSchedulePos = this.gdScheduleDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(gdSchedulePos)) {
                bookedCpm = gdSchedulePos.stream().mapToLong(GdSchedulePo::getTotalImpression).sum();
            }
            //当日总量校验
            Assert.isTrue(bookingCpm + bookedCpm <= dayLimitImpression, "当日预定的TopView总量已达上限");

            //周期频控校验
            if (this.cycleFrequencyService.isCycleFrequencyOrder(gdOrderExtInfo)) {
                Timestamp startTime = TimeUtil.isoTimeStr2Timestamp(im.getBeginTime());
                Timestamp endTime = TimeUtil.isoTimeStr2Timestamp(im.getEndTime());
                this.cycleFrequencyService.validateInSchedule(gdOrderExtInfo, startTime, endTime);
            }
        }
    }

    /**
     * 校验过渡形式与播放形式的关联
     */
    private void validateTransitionMode(SsaPlusScheduleBo scheduleBo) {
        Integer transitionMode = scheduleBo.getTransitionMode();
        if (transitionMode == null) {
            return;
        }

        // 当过渡形式选择【自定义过渡】时
        if (SsaTransitionModeEnum.isCustom(transitionMode)) {
            Integer playMode = scheduleBo.getSsaVideoPlayMode();
            Assert.isTrue(!Objects.equals(playMode, SsaVideoPlayModeEnum.EASTER_EGG.getCode())
                          && !Objects.equals(playMode, SsaVideoPlayModeEnum.AUTO_CONTINUE_PLAY.getCode()),
                          "播放形式选择【浮窗彩蛋视频】或【自动续播】时，过渡形式不可选择【自定义过渡】");
        }
    }
}
