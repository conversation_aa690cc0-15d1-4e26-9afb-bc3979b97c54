package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaSearchKeywordPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 闪屏排期id
     */
    private Integer gdScheduleId;

    /**
     * 搜索cpt排期id
     */
    private Integer searchScheduleId;

    /**
     * 展示词
     */
    private String showKeyword;

    /**
     * 搜索词
     */
    private String keyword;

    /**
     * 软删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}