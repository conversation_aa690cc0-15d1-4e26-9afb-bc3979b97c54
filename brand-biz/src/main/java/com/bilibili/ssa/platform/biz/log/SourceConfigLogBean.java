package com.bilibili.ssa.platform.biz.log;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.ssa.platform.common.enums.SsaLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(ssaLogFlag = SsaLogFlag.SOURCE_CONFIG)
public class SourceConfigLogBean implements Serializable{

    private static final long serialVersionUID = 4252402532575059791L;
    @LogProperty("刊例周期ID")
    private Integer cycleId;
    @LogProperty("刊例周期名称")
    private String cycleName;
    @LogProperty("位次ID")
    private Integer sourceId;
    @LogProperty("位次名称")
	private String sourceName;
    @LogProperty("轮播数")
    private Integer rotationNum;
    @LogProperty("内容月频次限制")
    private Integer mFreqLimit;
    @LogProperty("商业月频次限制")
    private Integer busMFreqLimit;
    @LogProperty("外部刊例价")
    private Integer externalPrice;
    @LogProperty("内部刊例价")
    private Integer internalPrice;
    @LogProperty("业务方ID")
    private Integer businessSideId;
    @LogProperty("业务方名称")
    private String businessSideName;
    @LogProperty("内容月频次限制")
    private Integer conRotationLimit;
    @LogProperty("商业月频次限制")
    private Integer busRotationLimit;
    @LogProperty("内部cpm净单价")
    private Integer internalCpmPrice;
    @LogProperty("topView配置ID")
    private Integer topViewConfigId;

    @LogProperty("周末加收比例")
    private Integer weekendRaiseRatio;

    @LogProperty("营销节点加收比例")
    private Integer festivalRaiseRatio;

    @LogProperty("播放形式，0：普通视频，1：沉浸式视频，2：稿件视频")
    private Integer videoPlayMode;
}
