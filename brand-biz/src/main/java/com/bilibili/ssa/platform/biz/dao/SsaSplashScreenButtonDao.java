package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaSplashScreenButtonPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenButtonPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaSplashScreenButtonDao {
    long countByExample(SsaSplashScreenButtonPoExample example);

    int deleteByExample(SsaSplashScreenButtonPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaSplashScreenButtonPo record);

    int insertBatch(List<SsaSplashScreenButtonPo> records);

    int insertUpdateBatch(List<SsaSplashScreenButtonPo> records);

    int insert(SsaSplashScreenButtonPo record);

    int insertUpdateSelective(SsaSplashScreenButtonPo record);

    int insertSelective(SsaSplashScreenButtonPo record);

    List<SsaSplashScreenButtonPo> selectByExample(SsaSplashScreenButtonPoExample example);

    SsaSplashScreenButtonPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaSplashScreenButtonPo record, @Param("example") SsaSplashScreenButtonPoExample example);

    int updateByExample(@Param("record") SsaSplashScreenButtonPo record, @Param("example") SsaSplashScreenButtonPoExample example);

    int updateByPrimaryKeySelective(SsaSplashScreenButtonPo record);

    int updateByPrimaryKey(SsaSplashScreenButtonPo record);
}