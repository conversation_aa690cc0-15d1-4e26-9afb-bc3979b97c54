package com.bilibili.ssa.platform.biz.soa;

import com.bilibili.brand.biz.utils.OssStorageUtil;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaUposVideoDto;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposSequenceService;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.bean.CoverFormattedUrlDto;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenVideoStatus;
import com.bilibili.ssa.platform.common.enums.UposProfileEnum;
import com.bilibili.ssa.platform.soa.ISoaUposVideoService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class SoaUposVideoServiceImpl implements ISoaUposVideoService {

	@Value("${upos.cover.api.prefix}")
	private String uposCoverApiPrefix;
	@Value("${upos.video.cover.prefix}")
	private String uposVideoCoverPrefix;
	@Value("${upos.video.cover.prefix.new}")
	private String uposVideoCoverPrefixNew;

	private static final String REPLACE_VIDEO_COVER_URL_PREFIX = "kodo://videocovers";
	private static final String REPLACE_VIDEO_COVER_URL_PREFIX_NEW = "boss://vc_ttl";

	@Autowired
	private ISsaUposVideoService ssaUposVideoService;
	@Autowired
	private OssStorageUtil ossStorageUtil;

	@Autowired
	private ISsaUposSequenceService ssaUposSequenceService;

	@Override
	public int getUposVideoSequence() {
		return ssaUposSequenceService.getUposVideoSequence();
	}

	@Deprecated
	@Override
	public SsaUposVideoDto getUposVideoByBizId(Integer bizId) {
		return ssaUposVideoService.getSsaUposVideoByBizId(bizId,
				Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.SSA_BUP.getCode()));
	}

	@Deprecated
	@Override
	public List<SsaUposVideoDto> getUposVideosInBizIds(List<Integer> bizIds) {
		return ssaUposVideoService.getSsaUposVideosInBizIds(bizIds, Lists.newArrayList());
	}

	@Deprecated
	@Override
	public Map<Integer, SsaUposVideoDto> getBizId2UposVideoMapInBizIds(List<Integer> bizIds) {
		Map<Integer, SsaUposVideoDto> bizId2UposVideoMap = ssaUposVideoService.getMgkBizIdTo1080PVideoMapInBizIds(bizIds);
		if (!CollectionUtils.isEmpty(bizId2UposVideoMap)) {
			for (SsaUposVideoDto dto : bizId2UposVideoMap.values()) {
				// 转码成功获取封面
				if (SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode().equals(dto.getStatus())) {
					dto.setVideoCoverUrls(this.getCoverUrlByUposUrl(dto.getUposUrl()));
				}
			}
		}
		return bizId2UposVideoMap;
	}

	@Override
	public Map<Integer, SsaUposVideoDto> getMgkDefinition2UposVideoWithCoverMapByBizId(Integer bizId) {
		return this.processUposVideoCover(Collections.singletonList(bizId)).collect(Collectors.toMap(SsaUposVideoDto::getDefinitionType, Function.identity()));
	}

	@Override
	public Map<Integer, List<SsaUposVideoDto>> getMgkBizId2UposVideoWithCoverMapInBizIds(List<Integer> bizIds) {
		return this.processUposVideoCover(bizIds).collect(Collectors.groupingBy(SsaUposVideoDto::getBizId));
	}

	private Stream<SsaUposVideoDto> processUposVideoCover(List<Integer> bizIds){
        List<SsaUposVideoDto> uposVideoDtos = ssaUposVideoService.getMgkUposVideosInBizIds(bizIds);
        Map<String, List<SsaUposVideoDto>>  uposUrl2VideosMap = uposVideoDtos.stream().collect(Collectors.groupingBy(SsaUposVideoDto::getUposUrl));
        return uposUrl2VideosMap.keySet().stream().map(uposUrl ->{
            List<SsaUposVideoDto> videos = uposUrl2VideosMap.get(uposUrl);
            SsaUposVideoDto videoDto = videos.get(0);
            if (Strings.isNullOrEmpty(videoDto.getCover())){
                String firstCoverUrl = this.getFirstCoverImageUrl(videoDto, uposUrl);
                if (!Strings.isNullOrEmpty(firstCoverUrl)){
                    videoDto.setCover(firstCoverUrl);
                    ssaUposVideoService.updateCover(videoDto.getBizId(), firstCoverUrl);
                }
            }
            return videos.stream().map(v -> {
                v.setCover(videoDto.getCover());
                return v;
            }).collect(Collectors.toList());
        }).flatMap(Collection::stream);
    }

	private String getFirstCoverImageUrl(SsaUposVideoDto v, String uposUrl){
		if (Strings.isNullOrEmpty(v.getCover()) && SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode().equals(v.getStatus())) {
			List<String> videoCovers = this.getCoverUrlByUposUrl(uposUrl);
			if (!CollectionUtils.isEmpty(videoCovers)){
				String firstCover = videoCovers.get(0);
                return ssaUposVideoService.uploadImageByUrl(firstCover);
			}
		}
		return "";
	}

	public List<String> getCoverUrlByUposUrl(String uposUrl){
		// http获取封面信息
		CoverFormattedUrlDto urlDto = ossStorageUtil.getVideoCoverUrl(uposUrl);
		Integer count = urlDto.getData().getCount();
		if (count == 0) {
			return Collections.emptyList();
		}
		// 封面最多返回5张
		int coverCount = (count > 5 ? 5 : count);
		String coverFormattedUrl = urlDto.getData().getUrlFormat();
		List<String> coverUrlList = new ArrayList<>(coverCount);
		for (int i = 1; i <= coverCount; i++) {
			coverUrlList.add(String.format(coverFormattedUrl, i)
					.replace(REPLACE_VIDEO_COVER_URL_PREFIX_NEW, uposVideoCoverPrefixNew)
					.replace(REPLACE_VIDEO_COVER_URL_PREFIX, uposVideoCoverPrefix));
		}

		return coverUrlList;
	}
}