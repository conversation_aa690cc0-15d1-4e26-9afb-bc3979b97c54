package com.bilibili.ssa.platform.biz.service.location.validator;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.ssa.platform.api.location.dto.NewSsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.UpdateSsaCycleDto;
import com.bilibili.ssa.platform.biz.dao.SsaCycleDao;
import com.bilibili.ssa.platform.biz.po.SsaCyclePo;
import com.bilibili.ssa.platform.biz.po.SsaCyclePoExample;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.List;

@Slf4j
@Component
public class SsaCycleValidator {

    private final static Integer CYCLE_NAME_MAX_LENGTH = 20;

    @Autowired
    private SsaCycleDao ssaCycleDao;

    public void validateCreateCptCycle(Operator operator, NewSsaCycleDto newSsaCycleDto) {
        Assert.isTrue(BooleanUtils.isFalse(Operator.validateParamIsNull(operator)), "operator信息为空");
        Assert.notNull(newSsaCycleDto, "需要新增的刊例周期信息不可为空");

        validateRequiredParam(newSsaCycleDto.getName(), newSsaCycleDto.getBeginTime(), newSsaCycleDto.getEndTime(), newSsaCycleDto.getAdType(), newSsaCycleDto.getOrderProduct());
        validateOptionalParam(newSsaCycleDto.getParentCycleId(), newSsaCycleDto.getOrderProduct());
    }

    public void validateUpdateCptCycle(Operator operator, UpdateSsaCycleDto updateCptCycleDto) {
        Assert.isTrue(BooleanUtils.isFalse(Operator.validateParamIsNull(operator)), "operator信息为空");
        Assert.notNull(updateCptCycleDto, "需要修改的刊例周期信息不可为空");

        validateRequiredParam(updateCptCycleDto.getName(), updateCptCycleDto.getBeginTime(), updateCptCycleDto.getEndTime(), updateCptCycleDto.getAd_type(), updateCptCycleDto.getOrderProduct());
        validateOptionalParam(updateCptCycleDto.getParentCycleId(), updateCptCycleDto.getOrderProduct());

        Assert.notNull(updateCptCycleDto.getId(), "刊例周期ID不可为空");
        SsaCyclePo ssaCyclePo = selectSsaCycleByIdAndOrderProduct(updateCptCycleDto.getId(), updateCptCycleDto.getOrderProduct());
        Assert.notNull(ssaCyclePo, "刊例周期不存在，请核实后再重试");
    }


    /**
     * 校验必填参数
     */
    private void validateRequiredParam(String name, Timestamp beginTime, Timestamp endTime, Integer adType, Integer orderProduct) {
        // 校验刊例周期名称
        Assert.hasText(name, "刊例周期名称不可为空");
        Assert.isTrue(name.length() <= CYCLE_NAME_MAX_LENGTH, "刊例周期名称长度不可超过" + CYCLE_NAME_MAX_LENGTH + "个字");
        // 校验刊例周期起止时间
        Assert.notNull(beginTime, "起始时间不可为空");
        Assert.notNull(endTime, "结束时间不可为空");
        Assert.isTrue(endTime.compareTo(beginTime) >= 0, "结束日期必须大于等于开始日期");
        // 校验广告类型
        Assert.notNull(SsaAdType.getByCode(adType), "广告类型不能为空");
        // 校验orderProduct
        Assert.notNull(orderProduct, "orderProduct不能为空，请核实后重试");
    }


    /**
     * 校验可选参数
     */
    private void validateOptionalParam(Integer parentCycleId, Integer orderProduct) {
        if (Utils.isPositive(parentCycleId)) {
            SsaCyclePo ssaCyclePo = selectSsaCycleByIdAndOrderProduct(parentCycleId, orderProduct);
            Assert.notNull(ssaCyclePo, "继承刊例周期不存在");
        }
    }

    private SsaCyclePo selectSsaCycleByIdAndOrderProduct(Integer id, Integer orderProduct) {
        SsaCyclePoExample example = new SsaCyclePoExample();
        example.or()
                .andIdEqualTo(id)
                .andOrderProductEqualTo(orderProduct)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaCyclePo> ssaCyclePos = ssaCycleDao.selectByExample(example);
        return ssaCyclePos.stream()
                .findFirst()
                .orElse(null);
    }
}
