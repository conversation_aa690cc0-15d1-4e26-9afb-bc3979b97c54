package com.bilibili.ssa.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class SsaPriceConfigPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SsaPriceConfigPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdIsNull() {
            addCriterion("source_config_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdIsNotNull() {
            addCriterion("source_config_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdEqualTo(Integer value) {
            addCriterion("source_config_id =", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdNotEqualTo(Integer value) {
            addCriterion("source_config_id <>", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdGreaterThan(Integer value) {
            addCriterion("source_config_id >", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_config_id >=", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdLessThan(Integer value) {
            addCriterion("source_config_id <", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdLessThanOrEqualTo(Integer value) {
            addCriterion("source_config_id <=", value, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdIn(List<Integer> values) {
            addCriterion("source_config_id in", values, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdNotIn(List<Integer> values) {
            addCriterion("source_config_id not in", values, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdBetween(Integer value1, Integer value2) {
            addCriterion("source_config_id between", value1, value2, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andSourceConfigIdNotBetween(Integer value1, Integer value2) {
            addCriterion("source_config_id not between", value1, value2, "sourceConfigId");
            return (Criteria) this;
        }

        public Criteria andShowStyleIsNull() {
            addCriterion("show_style is null");
            return (Criteria) this;
        }

        public Criteria andShowStyleIsNotNull() {
            addCriterion("show_style is not null");
            return (Criteria) this;
        }

        public Criteria andShowStyleEqualTo(Integer value) {
            addCriterion("show_style =", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleNotEqualTo(Integer value) {
            addCriterion("show_style <>", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleGreaterThan(Integer value) {
            addCriterion("show_style >", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleGreaterThanOrEqualTo(Integer value) {
            addCriterion("show_style >=", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleLessThan(Integer value) {
            addCriterion("show_style <", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleLessThanOrEqualTo(Integer value) {
            addCriterion("show_style <=", value, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleIn(List<Integer> values) {
            addCriterion("show_style in", values, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleNotIn(List<Integer> values) {
            addCriterion("show_style not in", values, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleBetween(Integer value1, Integer value2) {
            addCriterion("show_style between", value1, value2, "showStyle");
            return (Criteria) this;
        }

        public Criteria andShowStyleNotBetween(Integer value1, Integer value2) {
            addCriterion("show_style not between", value1, value2, "showStyle");
            return (Criteria) this;
        }

        public Criteria andClickAreaIsNull() {
            addCriterion("click_area is null");
            return (Criteria) this;
        }

        public Criteria andClickAreaIsNotNull() {
            addCriterion("click_area is not null");
            return (Criteria) this;
        }

        public Criteria andClickAreaEqualTo(Integer value) {
            addCriterion("click_area =", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaNotEqualTo(Integer value) {
            addCriterion("click_area <>", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaGreaterThan(Integer value) {
            addCriterion("click_area >", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaGreaterThanOrEqualTo(Integer value) {
            addCriterion("click_area >=", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaLessThan(Integer value) {
            addCriterion("click_area <", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaLessThanOrEqualTo(Integer value) {
            addCriterion("click_area <=", value, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaIn(List<Integer> values) {
            addCriterion("click_area in", values, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaNotIn(List<Integer> values) {
            addCriterion("click_area not in", values, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaBetween(Integer value1, Integer value2) {
            addCriterion("click_area between", value1, value2, "clickArea");
            return (Criteria) this;
        }

        public Criteria andClickAreaNotBetween(Integer value1, Integer value2) {
            addCriterion("click_area not between", value1, value2, "clickArea");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNull() {
            addCriterion("external_price is null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNotNull() {
            addCriterion("external_price is not null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceEqualTo(Integer value) {
            addCriterion("external_price =", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotEqualTo(Integer value) {
            addCriterion("external_price <>", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThan(Integer value) {
            addCriterion("external_price >", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("external_price >=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThan(Integer value) {
            addCriterion("external_price <", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("external_price <=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIn(List<Integer> values) {
            addCriterion("external_price in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotIn(List<Integer> values) {
            addCriterion("external_price not in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("external_price between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("external_price not between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIsNull() {
            addCriterion("internal_price is null");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIsNotNull() {
            addCriterion("internal_price is not null");
            return (Criteria) this;
        }

        public Criteria andInternalPriceEqualTo(Integer value) {
            addCriterion("internal_price =", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotEqualTo(Integer value) {
            addCriterion("internal_price <>", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceGreaterThan(Integer value) {
            addCriterion("internal_price >", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("internal_price >=", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceLessThan(Integer value) {
            addCriterion("internal_price <", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("internal_price <=", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIn(List<Integer> values) {
            addCriterion("internal_price in", values, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotIn(List<Integer> values) {
            addCriterion("internal_price not in", values, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("internal_price between", value1, value2, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("internal_price not between", value1, value2, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceIsNull() {
            addCriterion("internal_cpm_price is null");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceIsNotNull() {
            addCriterion("internal_cpm_price is not null");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceEqualTo(Integer value) {
            addCriterion("internal_cpm_price =", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceNotEqualTo(Integer value) {
            addCriterion("internal_cpm_price <>", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceGreaterThan(Integer value) {
            addCriterion("internal_cpm_price >", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("internal_cpm_price >=", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceLessThan(Integer value) {
            addCriterion("internal_cpm_price <", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceLessThanOrEqualTo(Integer value) {
            addCriterion("internal_cpm_price <=", value, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceIn(List<Integer> values) {
            addCriterion("internal_cpm_price in", values, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceNotIn(List<Integer> values) {
            addCriterion("internal_cpm_price not in", values, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceBetween(Integer value1, Integer value2) {
            addCriterion("internal_cpm_price between", value1, value2, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andInternalCpmPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("internal_cpm_price not between", value1, value2, "internalCpmPrice");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceIsNull() {
            addCriterion("half_external_price is null");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceIsNotNull() {
            addCriterion("half_external_price is not null");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceEqualTo(Integer value) {
            addCriterion("half_external_price =", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceNotEqualTo(Integer value) {
            addCriterion("half_external_price <>", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceGreaterThan(Integer value) {
            addCriterion("half_external_price >", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("half_external_price >=", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceLessThan(Integer value) {
            addCriterion("half_external_price <", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("half_external_price <=", value, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceIn(List<Integer> values) {
            addCriterion("half_external_price in", values, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceNotIn(List<Integer> values) {
            addCriterion("half_external_price not in", values, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("half_external_price between", value1, value2, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andHalfExternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("half_external_price not between", value1, value2, "halfExternalPrice");
            return (Criteria) this;
        }

        public Criteria andDisplayModeIsNull() {
            addCriterion("display_mode is null");
            return (Criteria) this;
        }

        public Criteria andDisplayModeIsNotNull() {
            addCriterion("display_mode is not null");
            return (Criteria) this;
        }

        public Criteria andDisplayModeEqualTo(Integer value) {
            addCriterion("display_mode =", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeNotEqualTo(Integer value) {
            addCriterion("display_mode <>", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeGreaterThan(Integer value) {
            addCriterion("display_mode >", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("display_mode >=", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeLessThan(Integer value) {
            addCriterion("display_mode <", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeLessThanOrEqualTo(Integer value) {
            addCriterion("display_mode <=", value, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeIn(List<Integer> values) {
            addCriterion("display_mode in", values, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeNotIn(List<Integer> values) {
            addCriterion("display_mode not in", values, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeBetween(Integer value1, Integer value2) {
            addCriterion("display_mode between", value1, value2, "displayMode");
            return (Criteria) this;
        }

        public Criteria andDisplayModeNotBetween(Integer value1, Integer value2) {
            addCriterion("display_mode not between", value1, value2, "displayMode");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeIsNull() {
            addCriterion("linkage_type is null");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeIsNotNull() {
            addCriterion("linkage_type is not null");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeEqualTo(Integer value) {
            addCriterion("linkage_type =", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeNotEqualTo(Integer value) {
            addCriterion("linkage_type <>", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeGreaterThan(Integer value) {
            addCriterion("linkage_type >", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("linkage_type >=", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeLessThan(Integer value) {
            addCriterion("linkage_type <", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("linkage_type <=", value, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeIn(List<Integer> values) {
            addCriterion("linkage_type in", values, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeNotIn(List<Integer> values) {
            addCriterion("linkage_type not in", values, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeBetween(Integer value1, Integer value2) {
            addCriterion("linkage_type between", value1, value2, "linkageType");
            return (Criteria) this;
        }

        public Criteria andLinkageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("linkage_type not between", value1, value2, "linkageType");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIsNull() {
            addCriterion("raise_info is null");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIsNotNull() {
            addCriterion("raise_info is not null");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoEqualTo(String value) {
            addCriterion("raise_info =", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotEqualTo(String value) {
            addCriterion("raise_info <>", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoGreaterThan(String value) {
            addCriterion("raise_info >", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoGreaterThanOrEqualTo(String value) {
            addCriterion("raise_info >=", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLessThan(String value) {
            addCriterion("raise_info <", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLessThanOrEqualTo(String value) {
            addCriterion("raise_info <=", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLike(String value) {
            addCriterion("raise_info like", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotLike(String value) {
            addCriterion("raise_info not like", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIn(List<String> values) {
            addCriterion("raise_info in", values, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotIn(List<String> values) {
            addCriterion("raise_info not in", values, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoBetween(String value1, String value2) {
            addCriterion("raise_info between", value1, value2, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotBetween(String value1, String value2) {
            addCriterion("raise_info not between", value1, value2, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeIsNull() {
            addCriterion("video_play_mode is null");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeIsNotNull() {
            addCriterion("video_play_mode is not null");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeEqualTo(Integer value) {
            addCriterion("video_play_mode =", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeNotEqualTo(Integer value) {
            addCriterion("video_play_mode <>", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeGreaterThan(Integer value) {
            addCriterion("video_play_mode >", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeGreaterThanOrEqualTo(Integer value) {
            addCriterion("video_play_mode >=", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeLessThan(Integer value) {
            addCriterion("video_play_mode <", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeLessThanOrEqualTo(Integer value) {
            addCriterion("video_play_mode <=", value, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeIn(List<Integer> values) {
            addCriterion("video_play_mode in", values, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeNotIn(List<Integer> values) {
            addCriterion("video_play_mode not in", values, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeBetween(Integer value1, Integer value2) {
            addCriterion("video_play_mode between", value1, value2, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andVideoPlayModeNotBetween(Integer value1, Integer value2) {
            addCriterion("video_play_mode not between", value1, value2, "videoPlayMode");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeIsNull() {
            addCriterion("effective_type is null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeIsNotNull() {
            addCriterion("effective_type is not null");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeEqualTo(Integer value) {
            addCriterion("effective_type =", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeNotEqualTo(Integer value) {
            addCriterion("effective_type <>", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeGreaterThan(Integer value) {
            addCriterion("effective_type >", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("effective_type >=", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeLessThan(Integer value) {
            addCriterion("effective_type <", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeLessThanOrEqualTo(Integer value) {
            addCriterion("effective_type <=", value, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeIn(List<Integer> values) {
            addCriterion("effective_type in", values, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeNotIn(List<Integer> values) {
            addCriterion("effective_type not in", values, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeBetween(Integer value1, Integer value2) {
            addCriterion("effective_type between", value1, value2, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andEffectiveTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("effective_type not between", value1, value2, "effectiveType");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectIsNull() {
            addCriterion("jump_area_effect is null");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectIsNotNull() {
            addCriterion("jump_area_effect is not null");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectEqualTo(Integer value) {
            addCriterion("jump_area_effect =", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectNotEqualTo(Integer value) {
            addCriterion("jump_area_effect <>", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectGreaterThan(Integer value) {
            addCriterion("jump_area_effect >", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectGreaterThanOrEqualTo(Integer value) {
            addCriterion("jump_area_effect >=", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectLessThan(Integer value) {
            addCriterion("jump_area_effect <", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectLessThanOrEqualTo(Integer value) {
            addCriterion("jump_area_effect <=", value, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectIn(List<Integer> values) {
            addCriterion("jump_area_effect in", values, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectNotIn(List<Integer> values) {
            addCriterion("jump_area_effect not in", values, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectBetween(Integer value1, Integer value2) {
            addCriterion("jump_area_effect between", value1, value2, "jumpAreaEffect");
            return (Criteria) this;
        }

        public Criteria andJumpAreaEffectNotBetween(Integer value1, Integer value2) {
            addCriterion("jump_area_effect not between", value1, value2, "jumpAreaEffect");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}