package com.bilibili.ssa.platform.biz.service.schedule;

import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.brand.api.common.enums.GoblinSceneTypeEnum;
import com.bilibili.brand.api.schedule.bo.ssa.SsaInventoryContext;
import com.bilibili.brand.api.schedule.bo.ssa.SsaOttQueryInventoryBo;
import com.bilibili.brand.api.schedule.dto.ReleaseInventoryMessageDto;
import com.bilibili.brand.api.schedule.service.inventory.ISsaOttCptInventoryService;
import com.bilibili.brand.api.schedule.service.inventory.ISsaOttGdInventoryService;
import com.bilibili.brand.api.schedule.service.inventory.ISsaOttInventoryService;
import com.bilibili.brand.biz.cache.RedisDao;
import com.bilibili.brand.biz.schedule.service.inventory.ReleaseInventoryService;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.SsaOttCptInventoryResultBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.SsaOttGdInventoryResultBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.SsaOttInventoryBaseBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttCptCreateSingleScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttGdCreateSingleScheduleBo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Optional;
import java.util.concurrent.Executor;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Service
@Slf4j
public class SsaOttInventoryService implements ISsaOttInventoryService {

    @Autowired
    private RedisDao redis;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Resource(name = "completableFutureExecutorWithDecorator")
    private Executor threadPool;
    @Autowired
    private ISsaOttGdInventoryService ssaOttGdInventoryService;
    @Autowired
    private ISsaOttCptInventoryService ssaOttCptInventoryService;
    @Autowired
    private ReleaseInventoryService releaseInventoryService;

    @Override
    public void calculateLockInventory(SsaOttCptCreateSingleScheduleBo scheduleBo) {
        ssaOttCptInventoryService.calculateLockInventory(scheduleBo);
    }

    @Override
    public void lockSsaOttCptInventory(SsaOttCptCreateSingleScheduleBo schedule) {
        ssaOttCptInventoryService.lockInventory(schedule, new SsaInventoryContext());
    }

    @Override
    public void lockSsaOttGdInventory(SsaOttGdCreateSingleScheduleBo schedule) {
        ssaOttGdInventoryService.lockInventory(schedule, new SsaInventoryContext());
    }

    @Override
    public void releaseSsaOttInventory(int scheduleId, LocalDate launchDate) {

        releaseInventoryService.sendReleaseInventoryMessage(ReleaseInventoryMessageDto.builder()
                .scheduleId(scheduleId)
                .launchDate(launchDate)
                .scene(GoblinSceneTypeEnum.SPLASH.getCode())
                .build());
    }

    @Override
    public SsaOttGdInventoryResultBo querySsaGdInventory(SsaOttQueryInventoryBo queryInventoryBo) {

        SsaOttGdInventoryResultBo result = doQueryInventory(queryInventoryBo, this::doGetSsaOttGdInventory, SsaOttGdInventoryResultBo.class);

        return Optional.ofNullable(result)
                .orElse(SsaOttGdInventoryResultBo.builder()
                        .alreadyFinish(false)
                        .dealSeq(queryInventoryBo.getDealSeq())
                        .restInventories(null)
                        .build());

    }

    @Override
    public SsaOttCptInventoryResultBo querySsaCptInventory(SsaOttQueryInventoryBo queryInventoryBo) {

        SsaOttCptInventoryResultBo result = doQueryInventory(queryInventoryBo, this::doGetSsaOttCptInventory, SsaOttCptInventoryResultBo.class);

        return Optional.ofNullable(result)
                .orElse(SsaOttCptInventoryResultBo.builder()
                        .alreadyFinish(false)
                        .dealSeq(queryInventoryBo.getDealSeq())
                        .restInventories(null)
                        .build());
    }

    private <T extends SsaOttInventoryBaseBo> T doQueryInventory(SsaOttQueryInventoryBo queryInventoryBo,
                                                                 Consumer<SsaOttQueryInventoryBo> query,
                                                                 Class<T> resultClass) {
        String dealSeq = queryInventoryBo.getDealSeq();
        if (StringUtils.isBlank(dealSeq)) {
            dealSeq = String.valueOf(snowflakeIdWorker.nextId());
            queryInventoryBo.setDealSeq(dealSeq);

            threadPool.execute(() -> query.accept(queryInventoryBo));
        }

        T result = redis.getObject(queryInventoryBo.getDealSeq(), resultClass);
        if (result != null && result.isAlreadyFinish()) {
            return result;
        } else {
            return null;
        }
    }

    private void doGetSsaOttGdInventory(SsaOttQueryInventoryBo queryInventoryBo) {

        SsaOttGdInventoryResultBo result;
        try {
            result = ssaOttGdInventoryService.queryInventory(queryInventoryBo);
        } catch (Exception e) {
            log.error("查询闪屏ott-gd库存时发生异常:{}", ExceptionUtils.getStackTrace(e));
            result = new SsaOttGdInventoryResultBo();
            result.setAlreadyFinish(true);
            result.setErrorMsg(ExceptionUtils.getMessage(e));
            result.setDealSeq(queryInventoryBo.getDealSeq());
        }

        long fiveMinuteSeconds = 300;
        redis.setString(queryInventoryBo.getDealSeq(), GsonUtils.toJson(result), fiveMinuteSeconds);
    }

    private void doGetSsaOttCptInventory(SsaOttQueryInventoryBo queryInventoryBo) {
        SsaOttCptInventoryResultBo result;
        try {
            result = ssaOttCptInventoryService.queryInventory(queryInventoryBo);
        } catch (Exception e) {
            log.error("查询闪屏ott-gd库存时发生异常:{}", ExceptionUtils.getStackTrace(e));
            result = new SsaOttCptInventoryResultBo();
            result.setAlreadyFinish(true);
            result.setErrorMsg(ExceptionUtils.getMessage(e));
            result.setDealSeq(queryInventoryBo.getDealSeq());
        }

        long fiveMinuteSeconds = 300;
        redis.setString(queryInventoryBo.getDealSeq(), GsonUtils.toJson(result), fiveMinuteSeconds);
    }
}


