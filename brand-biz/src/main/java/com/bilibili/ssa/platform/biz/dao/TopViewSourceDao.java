package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.TopViewSourcePo;
import com.bilibili.ssa.platform.biz.po.TopViewSourcePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface TopViewSourceDao {
    long countByExample(TopViewSourcePoExample example);

    int deleteByExample(TopViewSourcePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(TopViewSourcePo record);

    int insertBatch(List<TopViewSourcePo> records);

    int insertUpdateBatch(List<TopViewSourcePo> records);

    int insert(TopViewSourcePo record);

    int insertUpdateSelective(TopViewSourcePo record);

    int insertSelective(TopViewSourcePo record);

    List<TopViewSourcePo> selectByExample(TopViewSourcePoExample example);

    TopViewSourcePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") TopViewSourcePo record, @Param("example") TopViewSourcePoExample example);

    int updateByExample(@Param("record") TopViewSourcePo record, @Param("example") TopViewSourcePoExample example);

    int updateByPrimaryKeySelective(TopViewSourcePo record);

    int updateByPrimaryKey(TopViewSourcePo record);
}