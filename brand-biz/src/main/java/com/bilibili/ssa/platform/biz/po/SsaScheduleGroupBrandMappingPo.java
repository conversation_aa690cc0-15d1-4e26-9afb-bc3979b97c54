package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class SsaScheduleGroupBrandMappingPo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 排期组id
     */
    private Integer scheduleGroupId;

    /**
     * 资源位组id
     */
    private Integer sourceId;

    /**
     * brand系统排期id
     */
    private Integer brandScheduleId;

    /**
     * 软删除: 0-有效, 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp mtime;

    /**
     * 修改时间
     */
    private Timestamp ctime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleGroupId() {
        return scheduleGroupId;
    }

    public void setScheduleGroupId(Integer scheduleGroupId) {
        this.scheduleGroupId = scheduleGroupId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getBrandScheduleId() {
        return brandScheduleId;
    }

    public void setBrandScheduleId(Integer brandScheduleId) {
        this.brandScheduleId = brandScheduleId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }
}