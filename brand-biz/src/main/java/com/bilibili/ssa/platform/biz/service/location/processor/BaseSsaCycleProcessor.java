package com.bilibili.ssa.platform.biz.service.location.processor;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.cycle.CycleScheduleService;
import com.bilibili.brand.biz.cycle.dto.CycleScheduleDto;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.ssa.platform.api.location.dto.NewSsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.UpdateSsaCycleDto;
import com.bilibili.ssa.platform.api.log.service.ISsaLogService;
import com.bilibili.ssa.platform.biz.dao.SsaCycleDao;
import com.bilibili.ssa.platform.biz.dao.SsaFlowAllocationDao;
import com.bilibili.ssa.platform.biz.log.SsaCycleLogBean;
import com.bilibili.ssa.platform.biz.po.SsaCyclePo;
import com.bilibili.ssa.platform.biz.po.SsaCyclePoExample;
import com.bilibili.ssa.platform.biz.po.SsaFlowAllocationPo;
import com.bilibili.ssa.platform.biz.po.SsaFlowAllocationPoExample;
import com.bilibili.ssa.platform.common.enums.SsaLogFlag;
import com.bilibili.ssa.platform.common.enums.SsaLogOperateType;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Slf4j
public abstract class BaseSsaCycleProcessor extends AbstractSsaCycleProcessor {

    @Autowired
    private SsaCycleDao ssaCycleDao;

    @Autowired
    private SsaFlowAllocationDao flowAllocationDao;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private ISsaLogService ssaLogService;

    @Autowired
    private CycleScheduleService cycleScheduleService;

    @Autowired
    @Qualifier("cycleThreadPool")
    private Executor executor;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer processCreate(Operator operator, NewSsaCycleDto newSsaCycleDto) {
        Timestamp beginTime = TimeUtil.localDateTimeToTimestamp(newSsaCycleDto.getBeginTime().toLocalDateTime().truncatedTo(ChronoUnit.DAYS));
        Timestamp endTime = TimeUtil.localDateTimeToTimestamp(newSsaCycleDto.getEndTime().toLocalDateTime().with(LocalTime.of(23, 59, 59)));

        // 继承刊例版本逻辑
        Integer parentCycleId = findParentCycleId(newSsaCycleDto.getParentCycleId(), beginTime, newSsaCycleDto.getAdType());

        // 插入SSA刊例周期数据
        SsaCyclePo record = new SsaCyclePo();
        record.setStatus(Status.INVALID.getCode());
        record.setName(newSsaCycleDto.getName());
        record.setBeginTime(beginTime);
        record.setEndTime(endTime);
        record.setParentCycleId(parentCycleId);
        record.setAdType(newSsaCycleDto.getAdType());
        record.setOrderProduct(supportOrderProduct().getCode());
        ssaCycleDao.insertSelective(record);
        if (Utils.isPositive(parentCycleId)) {
            CompletableFuture.runAsync(() -> businessProcessCreate(parentCycleId, record.getId()), executor);
        }

        // 继承闪屏+内广默认流量比例
        int innerRatio = systemConfigService.getValueReturnInt(SystemConfigEnum.SSA_PLUS_INNER_FLOW_DEFAULT_RATIO.getCode());
        LocalDate beginDate = TimeUtil.timestampToLocalDate(beginTime);
        LocalDate endDate = TimeUtil.timestampToLocalDate(endTime);
        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<SsaFlowAllocationPo> allocationPos = new ArrayList<>();
        for (; beginDate.isBefore(endDate); beginDate = beginDate.plusDays(1)) {
            SsaFlowAllocationPo build = SsaFlowAllocationPo.builder()
                    .cycleId(record.getId())
                    .innerAllocRatio(innerRatio)
                    .externalAllocRatio(1000 - innerRatio)
                    .launchDate(TimeUtil.localDateToTimestamp(beginDate))
                    .isDeleted(IsDeleted.VALID.getCode())
                    .ctime(now)
                    .mtime(now)
                    .build();
            allocationPos.add(build);
        }
        flowAllocationDao.insertUpdateBatch(allocationPos);

        // 记录操作日志
        SsaCycleLogBean logBean = SsaCycleLogBean.builder().build();
        BeanUtils.copyProperties(record, logBean);

        Timestamp executionTime = newSsaCycleDto.getExecutionTime();
        if (Objects.nonNull(executionTime)) {
            cycleScheduleService.save(CycleScheduleDto.builder()
                    .cycleId(record.getId())
                    .orderProduct(supportOrderProduct().getCode())
                    .executionTime(executionTime)
                    .build());
            logBean.setExecutionTime(Utils.getTimestamp2StringBySecond(executionTime));
        }

        logBean.setBeginTime(Utils.getDate(record.getBeginTime().getTime()));
        logBean.setEndTime(Utils.getDate(record.getEndTime().getTime()));
        logBean.setParentCycleId(parentCycleId);
        logBean.setStatusDesc(Status.getByCode(record.getStatus()).getName());
        ssaLogService.insertLog(record.getId(), SsaLogFlag.CYCLE, SsaLogOperateType.ADD_CYCLE, operator, logBean);
        return record.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processUpdate(Operator operator, UpdateSsaCycleDto updateSsaCycleDto) {
        Timestamp beginTime = Utils.getBeginOfDay(updateSsaCycleDto.getBeginTime());
        Timestamp endTime = Utils.getEndOfDay(updateSsaCycleDto.getEndTime());

        // 更新SSA刊例周期
        SsaCyclePo record = new SsaCyclePo();
        record.setId(updateSsaCycleDto.getId());
        record.setName(updateSsaCycleDto.getName());
        record.setBeginTime(beginTime);
        record.setEndTime(endTime);
        Integer orderProduct = supportOrderProduct().getCode();
        record.setOrderProduct(orderProduct);
        ssaCycleDao.updateByPrimaryKeySelective(record);

        // 记录操作日志
        SsaCycleLogBean logBean = SsaCycleLogBean.builder().build();
        BeanUtils.copyProperties(record, logBean);

        Timestamp executionTime = updateSsaCycleDto.getExecutionTime();
        Integer cycleId = record.getId();
        if (Objects.nonNull(executionTime)) {
            cycleScheduleService.save(CycleScheduleDto.builder()
                    .cycleId(cycleId)
                    .orderProduct(orderProduct)
                    .executionTime(executionTime)
                    .build());
            logBean.setExecutionTime(Utils.getTimestamp2StringBySecond(executionTime));
        } else {
            CycleScheduleDto cycleSchedule = cycleScheduleService.getCycleSchedule(cycleId, orderProduct);
            if (Objects.nonNull(cycleSchedule)) {
                // 需要删除
                cycleScheduleService.delete(cycleId, orderProduct);
            }
        }


        logBean.setBeginTime(Utils.getTimestamp2String(record.getBeginTime()));
        logBean.setEndTime(Utils.getTimestamp2String(record.getEndTime()));
        logBean.setParentCycleId(updateSsaCycleDto.getParentCycleId());
        ssaLogService.insertLog(cycleId, SsaLogFlag.CYCLE, SsaLogOperateType.UPDATE_CYCLE, operator, logBean);
    }

    @Override
    public void processEnable(Operator operator, Integer cycleId, Integer orderProduct) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(cycleId, "刊例周期ID不可为空");
        Status status = Status.VALID;
        SsaCycleDto cptCycleDto = this.getCycleDtoById(cycleId);
        Status cycleStatus = Status.getByCode(cptCycleDto.getStatus());
        if (cycleStatus.equals(status)) {
            return;
        }
        SsaCyclePo record = new SsaCyclePo();
        record.setId(cycleId);
        record.setStatus(status.getCode());

        ssaCycleDao.updateByPrimaryKeySelective(record);

        SsaCycleLogBean logBean = SsaCycleLogBean.builder()
                .id(cycleId)
                .name(cptCycleDto.getName())
                .statusDesc(status.getName())
                .build();
        ssaLogService.insertLog(cycleId, SsaLogFlag.CYCLE, SsaLogOperateType.UPDATE_CYCLE_STATUS, operator, logBean);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processDelete(Operator operator, Integer cycleId, Integer orderProduct) {
        Assert.notNull(cycleId, "刊例周期ID不可为空");

        SsaCyclePoExample queryExample = new SsaCyclePoExample();
        queryExample.createCriteria()
                .andIdEqualTo(cycleId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<SsaCyclePo> ssaCyclePoList = ssaCycleDao.selectByExample(queryExample);
        Assert.notEmpty(ssaCyclePoList, "刊例周期不存在" + cycleId);
        log.info("processDelete cycleId:{},ssaCyclePoList:{}", cycleId, ssaCyclePoList);
        SsaCyclePo ssaCyclePo = ssaCyclePoList.get(0);

        // 更新刊例周期主表
        SsaCyclePoExample updateSsaCyclePoExample = new SsaCyclePoExample();
        updateSsaCyclePoExample.createCriteria()
                .andIdEqualTo(cycleId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        SsaCyclePo updateSsaCyclePo = SsaCyclePo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build();
        ssaCycleDao.updateByExampleSelective(updateSsaCyclePo, updateSsaCyclePoExample);

        // 更新刊例周期关联表
        businessProcessDelete(cycleId, orderProduct);

        SsaFlowAllocationPoExample updateSsaFlowAllocationPoExample = new SsaFlowAllocationPoExample();
        updateSsaFlowAllocationPoExample.createCriteria()
                .andCycleIdEqualTo(cycleId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        SsaFlowAllocationPo updateSsaFlowAllocationPo = SsaFlowAllocationPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build();
        flowAllocationDao.updateByExampleSelective(updateSsaFlowAllocationPo, updateSsaFlowAllocationPoExample);

        cycleScheduleService.delete(cycleId, orderProduct);

        // 插入日志
        SsaCycleLogBean logBean = SsaCycleLogBean.builder().build();
        BeanUtils.copyProperties(ssaCyclePo, logBean);
        logBean.setBeginTime(Utils.getTimestamp2String(updateSsaCyclePo.getBeginTime()));
        logBean.setEndTime(Utils.getTimestamp2String(updateSsaCyclePo.getEndTime()));
        ssaLogService.insertLog(ssaCyclePo.getId(), SsaLogFlag.CYCLE, SsaLogOperateType.DELETE_CYCLE, operator, logBean);
    }

    public SsaCycleDto getCycleDtoById(Integer id) {
        Assert.notNull(id);
        SsaCyclePoExample example = new SsaCyclePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(id);

        List<SsaCyclePo> ssaCyclePos = ssaCycleDao.selectByExample(example);
        Assert.notEmpty(ssaCyclePos, "刊例周期不存在" + id);
        return convert(ssaCyclePos.get(0));
    }

    protected Integer findParentCycleId(Integer parentCycleId, Timestamp beginTime, Integer adType) {
        if (Utils.isPositive(parentCycleId)) {
            return parentCycleId;
        }
        return getLastCycleIdByBeginTime(beginTime, adType);
    }

    protected Integer getLastCycleIdByBeginTime(Timestamp beginTime, Integer adType) {
        SsaCyclePoExample example = new SsaCyclePoExample();
        example.setLimit(1);
        example.setOrderByClause("end_time desc, id desc");
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAdTypeEqualTo(adType)
                .andOrderProductEqualTo(supportOrderProduct().getCode())
                .andEndTimeLessThanOrEqualTo(beginTime);
        List<SsaCyclePo> cptCyclePos = ssaCycleDao.selectByExample(example);
        return CollectionUtils.isEmpty(cptCyclePos) ? null : cptCyclePos.get(0).getId();
    }

    private SsaCycleDto convert(SsaCyclePo cptCyclePo) {
        SsaCycleDto cptCycleDto = new SsaCycleDto();
        BeanUtils.copyProperties(cptCyclePo, cptCycleDto);
        return cptCycleDto;
    }

    protected abstract void businessProcessCreate(Integer sourceCycleId, Integer targetCycleId);

    protected abstract void businessProcessDelete(Integer cycleId, Integer orderProduct);
}
