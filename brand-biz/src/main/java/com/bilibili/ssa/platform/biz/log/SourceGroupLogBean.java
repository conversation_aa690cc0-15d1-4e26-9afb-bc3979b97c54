package com.bilibili.ssa.platform.biz.log;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.ssa.platform.common.enums.SsaLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(ssaLogFlag = SsaLogFlag.SOURCE_GROUP)
public class SourceGroupLogBean implements Serializable{

    private static final long serialVersionUID = -558487142383851787L;
    @LogProperty("位置组ID")
    private Integer id;
    @LogProperty("位置组名称")
    private String name;
    @LogProperty("位次ID")
    private Integer sourceId;
    @LogProperty("位次名称")
    private String sourceName;
    @LogProperty("状态")
    private String statusDesc;
    
}
