package com.bilibili.ssa.platform.biz.service.converter;

import com.bilibili.brand.api.component.ComponentDto;
import com.bilibili.brand.api.creative.dto.GdCreativeCustomizeBrandInfoBO;
import com.bilibili.brand.api.creative.dto.GdCreativeMonitoringDto;
import com.bilibili.brand.api.creative.dto.ManuscriptInfoBO;
import com.bilibili.brand.api.resource.ssa.archive.bo.ArchiveTranscodingBo;
import com.bilibili.ssa.platform.api.splash_screen.dto.CustomizeBrandInfoDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaStoryComponentDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaStoryMonitorDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30
 */
@Mapper
public interface SsaArchiveProcessConverter {
    SsaArchiveProcessConverter MAPPER = Mappers.getMapper(SsaArchiveProcessConverter.class);

    @Mappings({
            @Mapping(target = "title", source = "name")
    })
    ManuscriptInfoBO toManuscriptInfoBO(ArchiveTranscodingBo bo);

    List<GdCreativeMonitoringDto> toGdCreativeMonitorings(List<SsaStoryMonitorDto> monitors);

    List<SsaStoryMonitorDto> toSsaStoryMonitor(List<GdCreativeMonitoringDto> monitors);

    @Mappings({
            @Mapping(target = "faceUrl", source = "face")
    })
    CustomizeBrandInfoDto toBrandInfo(GdCreativeCustomizeBrandInfoBO brandInfoBO);

    List<SsaStoryComponentDto> toSsaStoryComponents(List<ComponentDto> componentDtos);
}
