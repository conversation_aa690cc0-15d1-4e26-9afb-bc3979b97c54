package com.bilibili.ssa.platform.biz.service.schedule;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.PromotionPurposeType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.mas.common.utils.BeanHelper;
import com.bilibili.ssa.platform.api.schedule.dto.*;
import com.bilibili.ssa.platform.api.schedule.service.IComicSsaScheduleService;
import com.bilibili.ssa.platform.biz.component.SsaLock;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleDao;
import com.bilibili.ssa.platform.biz.po.SsaSchedulePo;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.ComicSsaScheduleServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ComicSsaScheduleService implements IComicSsaScheduleService {

    @Autowired
    private ComicSsaScheduleServiceDelegate delegate;
    @Autowired
    private SsaScheduleDao ssaScheduleDao;

    @Autowired
    private SsaLock ssaLock;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createCptSchedule(SsaNewCptScheduleDto scheduleDto, Operator operator) {
        log.info("createCptSchedule SsaNewCptScheduleDto: [{}], Operator: [{}]", scheduleDto, operator);
        Assert.notNull(scheduleDto, "排期信息不能为空");
        SsaSourceType.getByCode(scheduleDto.getResType());
        PromotionPurposeType.getByCode(scheduleDto.getPromotionPurposeType());
        validateShowStyle(scheduleDto.getAdType(), scheduleDto.getScreenStyle(), scheduleDto.getShowStyle());
        Assert.notEmpty(scheduleDto.getSchedules(), "排期轮播不可为空");
        scheduleDto.getSchedules().forEach(s -> {
            Assert.notNull(s.getLaunchDate(), "排期时间不可为空");
            Assert.isTrue(Utils.isPositive(s.getRotationNum()), "轮播数不可为空");
        });
        Assert.hasText(scheduleDto.getName(), "排期名称不可为空");
        Assert.notNull(scheduleDto.getGdOrderId(), "订单ID不可为空");
        Assert.notNull(scheduleDto.getClickArea(), "点击区域不可为空");
        SsaClickAreaType.getByCodeWithValidation(scheduleDto.getClickArea());

        List<Long> times = scheduleDto.getSchedules().stream().map(dto -> dto.getLaunchDate().getTime()).distinct().collect(Collectors.toList());
        Assert.isTrue(scheduleDto.getSchedules().size() == times.size(), "排期时间缺失");

        List<RLock> locks = ssaLock.getBatchLock(times, + operator.getOperatorId()
                + SsaConstants.COMIC_SCHEDULE_LOCK_SUFFIX);
        log.info("createCptSchedule get locks");

        try {
            delegate.createCptSchedule(scheduleDto, operator);
        }finally {
            log.info("createCptSchedule release locks");
            ssaLock.releaseBatchLock(locks);
        }
    }

    private void validateShowStyle(Integer adType, Integer screenStyle, Integer showStyle) {
        SsaAdType ssaAdType = SsaAdType.getByCode(adType);
        SsaScreenStyle ssaScreenStyle = SsaScreenStyle.getByCodeWithValidation(screenStyle);
        SsaShowStyleType ssaShowStyleType = SsaShowStyleType.getByCode(showStyle);
        Assert.isTrue(ssaShowStyleType.getAdType().equals(ssaAdType), "展示类型和媒体类型不相符");
        Assert.isTrue(ssaShowStyleType.getScreenStyle().equals(ssaScreenStyle), "展示类型与屏幕样式不相符");
    }

    @Override
    public void updateCptSchedule(SsaUpdateCptScheduleDto scheduleDto, Operator operator) {
        log.info("updateCptSchedule SsaUpdateCptScheduleDto: [{}], Operator: [{}]", scheduleDto, operator);
        Assert.notNull(scheduleDto.getScheduleId(), "排期ID不可为空");
        Assert.notNull(scheduleDto, "排期信息不可为空");
        Assert.notEmpty(scheduleDto.getSchedules(), "排期轮播不能为空");
        scheduleDto.getSchedules().forEach(s -> {
            Assert.notNull(s.getLaunchDate(), "排期时间不可为空");
            Assert.isTrue(Utils.isPositive(s.getRotationNum()), "轮播数不可为空");
        });
        List<Long> times = scheduleDto.getSchedules().stream().map(dto -> dto.getLaunchDate().getTime()).distinct().collect(Collectors.toList());
        Assert.isTrue(scheduleDto.getSchedules().size() == times.size(), "排期时间缺失");

        List<RLock> locks = ssaLock.getBatchLock(times, SsaConstants.SCHEDULE_DATE_LOCK_SUFFIX);
        log.info("updateCptSchedule get locks");
        try {
            delegate.updateCptSchedule(scheduleDto, operator);
        }finally {
            log.info("updateCptSchedule release locks");
            ssaLock.releaseBatchLock(locks);
        }
    }

    @Override
    public List<SsaScheduleDto> getSsaSchedulesBySsaOrderId(Integer orderId) {
        return delegate.getSsaSchedulesBySsaOrderId(orderId);
    }

    @Override
    public void deleteGdSchedule(Integer scheduleId, Operator operator) {
        delegate.deleteGdSchedule(scheduleId, operator);
    }

    @Override
    public void deleteGdSchedules(List<Integer> scheduleIds, Operator operator) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return;
        }
        for (Integer scheduleId : scheduleIds) {
            deleteGdSchedule(scheduleId, operator);
        }
    }

    @Override
    public List<SsaScheduleDto> querySsaSchedule(SsaQueryScheduleDto query) {
        return delegate.getSsaSchedules(query);
    }

    @Override
    public SsaScheduleDto getSaaScheduleDtoById(Integer scheduleId) {
        SsaSchedulePo po = ssaScheduleDao.selectByPrimaryKey(scheduleId);
        return BeanHelper.copyForBean(po, SsaScheduleDto::new);
    }
}
