package com.bilibili.ssa.platform.biz.service.upos;

import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.cpt.platform.util.SsaUtils;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaUposVideoDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.VideoXcodeDto;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.api.upos.service.dto.CallBackUposVideoDto;
import com.bilibili.ssa.platform.biz.dao.SsaUposVideoDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtUposVideoDao;
import com.bilibili.ssa.platform.biz.po.SsaUposVideoPo;
import com.bilibili.ssa.platform.biz.po.SsaUposVideoPoExample;
import com.bilibili.ssa.platform.common.enums.DefinitionTypeEnum;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenVideoStatus;
import com.bilibili.ssa.platform.common.enums.UposProfileEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.lucene.search.FieldComparator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SsaUposVideoServiceImpl implements ISsaUposVideoService {
    
    @Autowired
    private SsaUposVideoDao ssaUposVideoDao;
    @Autowired
    private ExtUposVideoDao extUposVideoDao;

    @Autowired
    private IBfsService bfsService;

    @Override
    public int findTheEarliestSameVideo(int bizId) {
        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or()
                .andIsDeletedEqualTo(0)
                .andBizIdEqualTo(bizId);

        List<SsaUposVideoPo> ssaUposVideoPos = ssaUposVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(ssaUposVideoPos)) {
            return bizId;
        }

        String md5 = ssaUposVideoPos.get(0).getMd5();
        example.clear();
        example.or()
                .andMd5EqualTo(md5)
                .andIsDeletedEqualTo(0)
                .andStatusIn(Arrays.asList(SsaSplashScreenVideoStatus.TRANS_CODING.getCode(), SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode()));

        List<SsaUposVideoPo> alreadyTranscodeVideos = ssaUposVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(alreadyTranscodeVideos)) {
            return bizId;
        }

        List<SsaUposVideoPo> orderById = alreadyTranscodeVideos.stream()
                .sorted(Comparator.comparing(SsaUposVideoPo::getId))
                .collect(Collectors.toList());

        return orderById.get(0).getBizId();
    }

    @Override
    public SsaUposVideoDto getSsaUposVideoByBizId(Integer bizId, List<Integer> uposProfiles) {
        Assert.notNull(bizId, "视频业务ID不可为空");

        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        //topview的首焦
        if(uposProfiles.contains(UposProfileEnum.MGK_AV.getCode())) {
            //topview的首焦视频需要支持有声播放,变更了视频云的上传接口,会转码生成两个不同码率的视频，我们只取符合模板的那一个就行了
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andBizIdEqualTo(bizId)
                    .andDefinitionTypeEqualTo(DefinitionTypeEnum.P1080.getCode())
                    .andSourceIn(Lists.newArrayList(UposProfileEnum.MGK_AV.getCode()));
            List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(pos)){
                //兼容历史数据
                SsaUposVideoDto ssaUposVideoDto = SsaUposVideoDto.builder().build();
                BeanUtils.copyProperties(pos.get(0), ssaUposVideoDto);
                return ssaUposVideoDto;
            }

        }
        SsaUposVideoPoExample example1 = new SsaUposVideoPoExample();
        example1.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBizIdEqualTo(bizId)
                .andDefinitionTypeEqualTo(DefinitionTypeEnum.DEFAULT.getCode())
                .andSourceIn(Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.SSA_BUP.getCode()));

        List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example1);
        if (CollectionUtils.isEmpty(pos)){
            return null;
        }else {
            SsaUposVideoDto ssaUposVideoDto = SsaUposVideoDto.builder().build();
            BeanUtils.copyProperties(pos.get(0), ssaUposVideoDto);
            return ssaUposVideoDto;
        }
    }

    @Override
    public List<SsaUposVideoDto> getSsaUposVideosInBizIds(List<Integer> bizIds, List<Integer> uposProfiles) {
        if (CollectionUtils.isEmpty(bizIds)){
            return Collections.emptyList();
        }
        List<SsaUposVideoDto> uposVideoDtos = new ArrayList<>();
        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        if(uposProfiles.contains(UposProfileEnum.MGK_AV.getCode())){
            //topview的首焦视频需要支持有声播放,变更了视频云的上传接口,会转码生成两个不同码率的视频，我们只取符合模板的那一个就行了
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andBizIdIn(bizIds)
                    .andDefinitionTypeEqualTo(DefinitionTypeEnum.P1080.getCode())
                    .andSourceIn(Lists.newArrayList(UposProfileEnum.MGK_AV.getCode()));
            List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example);
            if(!CollectionUtils.isEmpty(pos)){
                //兼容历史数据
                uposVideoDtos  = pos.stream().map(po -> {
                    SsaUposVideoDto dto = SsaUposVideoDto.builder().build();
                    BeanUtils.copyProperties(po, dto);
                    return dto;
                }).collect(Collectors.toList());
                if(uposVideoDtos.size() == bizIds.size()){
                    return uposVideoDtos;
                }
            }
        }

        SsaUposVideoPoExample example1 = new SsaUposVideoPoExample();
        example1.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBizIdIn(bizIds)
                .andDefinitionTypeEqualTo(DefinitionTypeEnum.DEFAULT.getCode())
                .andSourceIn(Lists.newArrayList(UposProfileEnum.SSA_YB.getCode(), UposProfileEnum.SSA_BUP.getCode()));

        List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example1);
        if(CollectionUtils.isEmpty(pos)){
            return uposVideoDtos;
        }
        List<SsaUposVideoDto> ssaUposVideoDtos = pos.stream().map(po -> {
            SsaUposVideoDto dto = SsaUposVideoDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());

        ssaUposVideoDtos.addAll(uposVideoDtos);
        return ssaUposVideoDtos;
    }

    @Override
    public Map<Integer, SsaUposVideoDto> getBizId2UposVideoMapInBizIds(List<Integer> bizIds) {
        List<SsaUposVideoDto> uposVideos = this.getSsaUposVideosInBizIds(bizIds,
                Lists.newArrayList(UposProfileEnum.MGK_AV.getCode()));
        return uposVideos.stream().collect(Collectors.toMap(SsaUposVideoDto::getBizId, Function.identity()));
    }

    @Override
    public Map<Integer, SsaUposVideoDto> getMgkBizIdTo1080PVideoMapInBizIds(List<Integer> bizIds) {
        if (CollectionUtils.isEmpty(bizIds)){
            return Collections.emptyMap();
        }

        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBizIdIn(bizIds)
                .andDefinitionTypeEqualTo(DefinitionTypeEnum.P1080.getCode())
                .andSourceEqualTo(UposProfileEnum.MGK_AV.getCode());

        List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example);
        return pos.stream().map(po -> {
            SsaUposVideoDto dto = SsaUposVideoDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toMap(SsaUposVideoDto::getBizId, Function.identity()));
    }

    @Override
    public Map<Integer, SsaUposVideoDto> getMgkDefinition2UposVideoMapByBizId(Integer bizId) {
        Assert.notNull(bizId, "视频业务ID不可为空");

        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBizIdEqualTo(bizId)
                .andSourceEqualTo(UposProfileEnum.MGK_AV.getCode());

        List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)){
            return Collections.emptyMap();
        }

        return pos.stream().map(po -> {
            SsaUposVideoDto dto = SsaUposVideoDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toMap(SsaUposVideoDto::getDefinitionType, Function.identity()));
    }

    @Override
    public List<SsaUposVideoDto> getMgkUposVideosInBizIds(List<Integer> bizIds) {
        if (CollectionUtils.isEmpty(bizIds)){
            return Collections.emptyList();
        }

        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBizIdIn(bizIds)
                .andSourceEqualTo(UposProfileEnum.MGK_AV.getCode());

        List<SsaUposVideoPo> pos = ssaUposVideoDao.selectByExample(example);
        return pos.stream().map(po -> {
            SsaUposVideoDto dto = SsaUposVideoDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<SsaUposVideoDto>> getMgkBizId2UposVideosMapInBizIds(List<Integer> bizIds) {
        List<SsaUposVideoDto> uposVideos = this.getMgkUposVideosInBizIds(bizIds);
        return uposVideos.stream().collect(Collectors.groupingBy(SsaUposVideoDto::getBizId));
    }

    @Override
    public String uploadImageByUrl(String imageUrl) {
        try {
            Assert.hasText(imageUrl, "图片URL不可为空");
            InputStream imageInputStream = OkHttpUtils.get(imageUrl).callForStream();
            byte[] buffer = IOUtils.toByteArray(imageInputStream);
            String prefix = imageUrl.substring(imageUrl.lastIndexOf(".") + 1);
            BfsUploadResult result = bfsService.upload("video/cover", "2233.".concat(prefix), buffer);
            return result.getUrl();
        } catch (Exception e) {
            log.error("cover picutre upload failed: {}", e);
            throw new RuntimeException("图片上传失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateCover(Integer bizId, String firstCoverUrl) {
        Assert.notNull(bizId, "业务视频ID不可为空");
        Assert.hasText(firstCoverUrl, "封面URL不可为空");
        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
        .andBizIdEqualTo(bizId);
        ssaUposVideoDao.updateByExampleSelective(SsaUposVideoPo.builder().cover(firstCoverUrl).build(), example);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processSplashXcodeCallBack(boolean success, boolean videocovers, Integer bizId, VideoXcodeDto before, VideoXcodeDto after) {
        log.info("processSplashXcodeCallBack success: [{}], videocovers: [{}], bizId: [{}], before: [{}], after: [{}]", success, videocovers, bizId, before, after);
        Assert.notNull(bizId,"视频业务ID不可为空");
        this.validateVideo(before, "转码前");

        SsaUposVideoPo po = new SsaUposVideoPo();
        po.setBizId(bizId);
        po.setUposUrl(before.getUposUri());
        po.setWidth(before.getWidth());
        po.setHeight(before.getHeight());
        po.setMd5(before.getMd5());
        po.setSize(before.getSize());
        po.setDuration(SsaUtils.getVideoMilliDuration(before.getDuration()));
        po.setDefinitionType(DefinitionTypeEnum.DEFAULT.getCode());
        po.setSource(UposProfileEnum.SSA_BUP.getCode());

        if (success){
            if (videocovers && after != null){
                this.validateVideo(after, "转码后");
                po.setXcodeUposUrl(after.getUposUri());
                po.setXcodeMd5(after.getMd5());
                po.setXcodeWidth(after.getWidth());
                po.setXcodeHeight(after.getHeight());
                po.setXcodeSize(after.getSize());
                po.setXcodeDuration(SsaUtils.getVideoMilliDuration(after.getDuration()));
                po.setStatus(SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode());
            }else {
                po.setStatus(SsaSplashScreenVideoStatus.TRANS_CODING.getCode());
            }
        }else if (videocovers){
            po.setStatus(SsaSplashScreenVideoStatus.TRANS_FAILED.getCode());
        }else {
            po.setStatus(SsaSplashScreenVideoStatus.VIDEO_COVER_FAILED.getCode());
        }
        extUposVideoDao.saveSelective(po);
    }

    @Override
    public void processMgkXcodeCallBack(CallBackUposVideoDto videoDto) {
        log.info("processMgkXcodeCallBack videoDto: [{}]", videoDto);
        Assert.notNull(videoDto,"回调信息不可为空");
        Assert.notNull(videoDto.getBizId(),"视频业务ID不可为空");
        Assert.notNull(videoDto.getProfile(),"profile不可为空");
        VideoXcodeDto before = videoDto.getBefore();
        this.validateVideo(before, "转码前");

        for (DefinitionTypeEnum definition : DefinitionTypeEnum.values()){
            if (!DefinitionTypeEnum.DEFAULT.equals(definition)){
                SsaUposVideoPo po = new SsaUposVideoPo();
                po.setBizId(videoDto.getBizId());
                po.setUposUrl(before.getUposUri());
                po.setWidth(before.getWidth());
                po.setHeight(before.getHeight());
                po.setMd5(before.getMd5());
                po.setSize(before.getSize());
                po.setDuration(SsaUtils.getVideoMilliDuration(before.getDuration()));
                po.setDefinitionType(definition.getCode());
                po.setSource(UposProfileEnum.getByProfile(videoDto.getProfile()).getCode());

                Map<Integer, VideoXcodeDto> definitionToXcodeMap = videoDto.getDefinitionToXcodeMap();
                VideoXcodeDto after = CollectionUtils.isEmpty(definitionToXcodeMap) ? null : definitionToXcodeMap.getOrDefault(definition.getCode(), null);

                if (videoDto.getCallbackSuccess()){
                    if (videoDto.getVideocovers() && after != null){
                        this.validateVideo(after, "转码后");
                        po.setXcodeUposUrl(after.getUposUri());
                        po.setXcodeMd5(after.getMd5());
                        po.setXcodeWidth(after.getWidth());
                        po.setXcodeHeight(after.getHeight());
                        po.setXcodeSize(after.getSize());
                        po.setXcodeDuration(SsaUtils.getVideoMilliDuration(after.getDuration()));
                        po.setStatus(SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode());
                    }else {
                        po.setStatus(SsaSplashScreenVideoStatus.TRANS_CODING.getCode());
                    }
                }else if (videoDto.getVideocovers()){
                    po.setStatus(SsaSplashScreenVideoStatus.TRANS_FAILED.getCode());
                }else {
                    po.setStatus(SsaSplashScreenVideoStatus.VIDEO_COVER_FAILED.getCode());
                }
                extUposVideoDao.saveSelective(po);
            }
        }

    }

    private void validateVideo(VideoXcodeDto dto, String tips) {
        Assert.notNull(dto, tips + "视频信息不可为空");
        Assert.hasText(dto.getUposUri(), tips + "的URI不可为空");
        Assert.isTrue(Utils.isPositive(dto.getWidth()), "视频"+tips+"的宽度不合法");
        Assert.isTrue(Utils.isPositive(dto.getHeight()), "视频"+tips+"的高度不合法");
        Assert.isTrue(Utils.isPositive(dto.getDuration()), "视频"+tips+"的时长不合法");
        Assert.isTrue(Utils.isPositive(dto.getSize()), "视频"+tips+"的大小不合法");
        Assert.hasText(dto.getMd5(), "视频"+tips+"的MD5不合法");
    }

    @Override
    public void updateFileNameAndAuth(Integer bizId, String fileName, String uposAuth) {
        SsaUposVideoPo updatePo = SsaUposVideoPo.builder()
                .fileName(fileName)
                .uposAuth(uposAuth)
                .build();
        SsaUposVideoPoExample example = new SsaUposVideoPoExample();
        example.or().andBizIdEqualTo(bizId);
        ssaUposVideoDao.updateByExampleSelective(updatePo, example);
    }
}
