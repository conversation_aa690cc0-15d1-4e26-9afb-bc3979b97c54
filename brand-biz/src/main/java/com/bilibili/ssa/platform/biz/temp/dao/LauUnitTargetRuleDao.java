package com.bilibili.ssa.platform.biz.temp.dao;

import com.bilibili.ssa.platform.biz.temp.po.LauUnitTargetRulePo;
import com.bilibili.ssa.platform.biz.temp.po.LauUnitTargetRulePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface LauUnitTargetRuleDao {
    long countByExample(LauUnitTargetRulePoExample example);

    int deleteByExample(LauUnitTargetRulePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(LauUnitTargetRulePo record);

    int insertBatch(List<LauUnitTargetRulePo> records);

    int insertUpdateBatch(List<LauUnitTargetRulePo> records);

    int insert(LauUnitTargetRulePo record);

    int insertUpdateSelective(LauUnitTargetRulePo record);

    int insertSelective(LauUnitTargetRulePo record);

    List<LauUnitTargetRulePo> selectByExampleWithBLOBs(LauUnitTargetRulePoExample example);

    List<LauUnitTargetRulePo> selectByExample(LauUnitTargetRulePoExample example);

    LauUnitTargetRulePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LauUnitTargetRulePo record, @Param("example") LauUnitTargetRulePoExample example);

    int updateByExampleWithBLOBs(@Param("record") LauUnitTargetRulePo record, @Param("example") LauUnitTargetRulePoExample example);

    int updateByExample(@Param("record") LauUnitTargetRulePo record, @Param("example") LauUnitTargetRulePoExample example);

    int updateByPrimaryKeySelective(LauUnitTargetRulePo record);

    int updateByPrimaryKeyWithBLOBs(LauUnitTargetRulePo record);

    int updateByPrimaryKey(LauUnitTargetRulePo record);
}