package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaSplashScreenDao {
    long countByExample(SsaSplashScreenPoExample example);

    int deleteByExample(SsaSplashScreenPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaSplashScreenPo record);

    int insertBatch(List<SsaSplashScreenPo> records);

    int insertUpdateBatch(List<SsaSplashScreenPo> records);

    int insert(SsaSplashScreenPo record);

    int insertUpdateSelective(SsaSplashScreenPo record);

    int insertSelective(SsaSplashScreenPo record);

    List<SsaSplashScreenPo> selectByExample(SsaSplashScreenPoExample example);

    SsaSplashScreenPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaSplashScreenPo record, @Param("example") SsaSplashScreenPoExample example);

    int updateByExample(@Param("record") SsaSplashScreenPo record, @Param("example") SsaSplashScreenPoExample example);

    int updateByPrimaryKeySelective(SsaSplashScreenPo record);

    int updateByPrimaryKey(SsaSplashScreenPo record);
}