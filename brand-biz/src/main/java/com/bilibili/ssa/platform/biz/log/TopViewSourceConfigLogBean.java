package com.bilibili.ssa.platform.biz.log;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.ssa.platform.common.enums.SsaLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(ssaLogFlag = SsaLogFlag.TOP_VIEW_SOURCE_CONFIG)
public class TopViewSourceConfigLogBean implements Serializable {

    private static final long serialVersionUID = -7937254895909543426L;

    @LogProperty("topView配置ID")
    private Integer id;
    @LogProperty("刊例周期ID")
    private Integer topViewCycleId;
    @LogProperty("闪屏轮播数")
    private Integer ssaRotationNum;
    @LogProperty("内容月频次限制")
    private Integer mFreqLimit;
    @LogProperty("商业月频次限制")
    private Integer busMFreqLimit;
    @LogProperty("外部刊例价")
    private Integer externalPrice;
    @LogProperty("内部刊例价")
    private Integer internalPrice;
    @LogProperty("业务方ID")
    private Integer businessSideId;
    @LogProperty("业务方名称")
    private String businessSideName;
    @LogProperty("内部cpm净单价")
    private Integer internalCpmPrice;
    @LogProperty("topView售卖类型")
    private Integer topViewType;
    @LogProperty("周末加收比例")
    private Integer weekendRaiseRatio;
    @LogProperty("营销节点加收比例")
    private Integer festivalRaiseRatio;
    @LogProperty("年龄定向加收")
    private Integer ageRaiseRatio;
    @LogProperty("性别定向加收")
    private Integer genderRaiseRatio;
    @LogProperty("ios定向加收")
    private Integer iosRaiseRatio;
    @LogProperty("分时定向加收")
    private Integer splitTimeRaiseRatio;
    @LogProperty("人群包定向加收")
    private Integer crowdPackRaiseRatio;

}
