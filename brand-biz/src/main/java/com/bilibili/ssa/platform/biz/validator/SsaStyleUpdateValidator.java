package com.bilibili.ssa.platform.biz.validator;

import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.SsaPdScheduleDto;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttPdScheduleBo;
import com.bilibili.ssa.platform.api.splash_screen.dto.QuerySplashScreenParamDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenService;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 闪屏样式更新校验
 *
 * <AUTHOR>
 * @date 2023/11/28
 */
@Service
public class SsaStyleUpdateValidator {

    @Autowired
    private SsaSplashScreenService ssaSplashScreenService;

    public void checkStyleChangedForPd(SsaPdScheduleDto newSchedule, GdSchedulePo oldSchedule) {
        boolean anyStyleChanged = isChanged(oldSchedule.getClickArea(), newSchedule.getClickArea())
                || isChanged(oldSchedule.getInteractStyle(), newSchedule.getInteractStyle())
                || isChanged(oldSchedule.getShowStyle(), newSchedule.getShowStyle())
                || isChanged(oldSchedule.getButtonStyle(), newSchedule.getButtonStyle())
                || isChanged(oldSchedule.getJumpAreaEffect(), newSchedule.getJumpAreaEffect())
                || isChanged(oldSchedule.getJumpAreaStyle(), newSchedule.getJumpAreaStyle());
        checkEffectiveCreative(anyStyleChanged, oldSchedule.getScheduleId());
    }

    public void checkStyleChangedForGdCpt(SsaPlusScheduleBo newSchedule, ScheduleDto oldSchedule) {
        boolean anyStyleChanged = isChanged(oldSchedule.getClickArea(), newSchedule.getClickArea())
                || isChanged(oldSchedule.getInteractStyle(), newSchedule.getInteractStyle())
                || isChanged(oldSchedule.getShowStyle(), newSchedule.getShowStyle())
                || isChanged(oldSchedule.getButtonStyle(), newSchedule.getButtonStyle())
                || isChanged(oldSchedule.getJumpAreaEffect(), newSchedule.getJumpAreaEffect())
                || isChanged(oldSchedule.getJumpAreaStyle(), newSchedule.getJumpAreaStyle())
                || isChanged(oldSchedule.getEffectiveType(), newSchedule.getEffectiveType());
        checkEffectiveCreative(anyStyleChanged, oldSchedule.getScheduleId());
    }

    private void checkEffectiveCreative(boolean anyStyleChanged, int scheduleId) {
        if (anyStyleChanged) {
            List<SsaSplashScreenDto> effectiveCreatives = ssaSplashScreenService.getSsaSplashScreensSimple(QuerySplashScreenParamDto.builder()
                    .gdScheduleIds(Collections.singletonList(scheduleId))
                    .statusList(SsaSplashScreenStatus.NOT_DELETED_STATUS)
                    .build());
            Assert.isTrue(CollectionUtils.isEmpty(effectiveCreatives), "排期下无创意时才能修改样式相关信息，请删除创意后重试");
        }
    }

    private boolean isChanged(Integer code1, Integer code2) {
        if (code1 == null) {
            code1 = 0;
        }
        if (code2 == null) {
            code2 = 0;
        }
        return !Objects.equals(code1, code2);
    }

    public void checkStyleChangedForSsaOttPd(SsaOttPdScheduleBo newSchedule, GdSchedulePo oldSchedule) {
        boolean anyStyleChanged = isChanged(oldSchedule.getOttScreenStyle(), newSchedule.getOttScreenStyle())
                || isChanged(oldSchedule.getOttScreenType(), newSchedule.getOttScreenType())
                || isChanged(oldSchedule.getShowStyle(), newSchedule.getShowStyle())
                || isChanged(oldSchedule.getAdType(), newSchedule.getAdType());
        checkEffectiveCreative(anyStyleChanged, oldSchedule.getScheduleId());
    }
}
