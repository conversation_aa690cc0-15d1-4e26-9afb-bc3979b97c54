package com.bilibili.ssa.platform.biz.po.business;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class ResSourceCpmAllocationPo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 资源位id
     */
    private Integer sourceId;

    /**
     * 日期
     */
    private Timestamp date;

    /**
     * cpt约的比例（分母1000）
     */
    private Integer cpt;

    /**
     * gd约的比例（分母1000）
     */
    private Integer gd;

    /**
     * 软删除: 0-有效, 1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public Integer getCpt() {
        return cpt;
    }

    public void setCpt(Integer cpt) {
        this.cpt = cpt;
    }

    public Integer getGd() {
        return gd;
    }

    public void setGd(Integer gd) {
        this.gd = gd;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}