package com.bilibili.ssa.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class SsaSplashScreenImagePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SsaSplashScreenImagePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdIsNull() {
            addCriterion("image_rule_id is null");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdIsNotNull() {
            addCriterion("image_rule_id is not null");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdEqualTo(Integer value) {
            addCriterion("image_rule_id =", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdNotEqualTo(Integer value) {
            addCriterion("image_rule_id <>", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdGreaterThan(Integer value) {
            addCriterion("image_rule_id >", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("image_rule_id >=", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdLessThan(Integer value) {
            addCriterion("image_rule_id <", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdLessThanOrEqualTo(Integer value) {
            addCriterion("image_rule_id <=", value, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdIn(List<Integer> values) {
            addCriterion("image_rule_id in", values, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdNotIn(List<Integer> values) {
            addCriterion("image_rule_id not in", values, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdBetween(Integer value1, Integer value2) {
            addCriterion("image_rule_id between", value1, value2, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andImageRuleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("image_rule_id not between", value1, value2, "imageRuleId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNull() {
            addCriterion("platform_id is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIsNotNull() {
            addCriterion("platform_id is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformIdEqualTo(Integer value) {
            addCriterion("platform_id =", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotEqualTo(Integer value) {
            addCriterion("platform_id <>", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThan(Integer value) {
            addCriterion("platform_id >", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform_id >=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThan(Integer value) {
            addCriterion("platform_id <", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdLessThanOrEqualTo(Integer value) {
            addCriterion("platform_id <=", value, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdIn(List<Integer> values) {
            addCriterion("platform_id in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotIn(List<Integer> values) {
            addCriterion("platform_id not in", values, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdBetween(Integer value1, Integer value2) {
            addCriterion("platform_id between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andPlatformIdNotBetween(Integer value1, Integer value2) {
            addCriterion("platform_id not between", value1, value2, "platformId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIsNull() {
            addCriterion("splash_screen_id is null");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIsNotNull() {
            addCriterion("splash_screen_id is not null");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdEqualTo(Integer value) {
            addCriterion("splash_screen_id =", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotEqualTo(Integer value) {
            addCriterion("splash_screen_id <>", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdGreaterThan(Integer value) {
            addCriterion("splash_screen_id >", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("splash_screen_id >=", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdLessThan(Integer value) {
            addCriterion("splash_screen_id <", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdLessThanOrEqualTo(Integer value) {
            addCriterion("splash_screen_id <=", value, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdIn(List<Integer> values) {
            addCriterion("splash_screen_id in", values, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotIn(List<Integer> values) {
            addCriterion("splash_screen_id not in", values, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdBetween(Integer value1, Integer value2) {
            addCriterion("splash_screen_id between", value1, value2, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andSplashScreenIdNotBetween(Integer value1, Integer value2) {
            addCriterion("splash_screen_id not between", value1, value2, "splashScreenId");
            return (Criteria) this;
        }

        public Criteria andWidthIsNull() {
            addCriterion("width is null");
            return (Criteria) this;
        }

        public Criteria andWidthIsNotNull() {
            addCriterion("width is not null");
            return (Criteria) this;
        }

        public Criteria andWidthEqualTo(Integer value) {
            addCriterion("width =", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotEqualTo(Integer value) {
            addCriterion("width <>", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThan(Integer value) {
            addCriterion("width >", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthGreaterThanOrEqualTo(Integer value) {
            addCriterion("width >=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThan(Integer value) {
            addCriterion("width <", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthLessThanOrEqualTo(Integer value) {
            addCriterion("width <=", value, "width");
            return (Criteria) this;
        }

        public Criteria andWidthIn(List<Integer> values) {
            addCriterion("width in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotIn(List<Integer> values) {
            addCriterion("width not in", values, "width");
            return (Criteria) this;
        }

        public Criteria andWidthBetween(Integer value1, Integer value2) {
            addCriterion("width between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andWidthNotBetween(Integer value1, Integer value2) {
            addCriterion("width not between", value1, value2, "width");
            return (Criteria) this;
        }

        public Criteria andHeightIsNull() {
            addCriterion("height is null");
            return (Criteria) this;
        }

        public Criteria andHeightIsNotNull() {
            addCriterion("height is not null");
            return (Criteria) this;
        }

        public Criteria andHeightEqualTo(Integer value) {
            addCriterion("height =", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotEqualTo(Integer value) {
            addCriterion("height <>", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThan(Integer value) {
            addCriterion("height >", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightGreaterThanOrEqualTo(Integer value) {
            addCriterion("height >=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThan(Integer value) {
            addCriterion("height <", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightLessThanOrEqualTo(Integer value) {
            addCriterion("height <=", value, "height");
            return (Criteria) this;
        }

        public Criteria andHeightIn(List<Integer> values) {
            addCriterion("height in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotIn(List<Integer> values) {
            addCriterion("height not in", values, "height");
            return (Criteria) this;
        }

        public Criteria andHeightBetween(Integer value1, Integer value2) {
            addCriterion("height between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andHeightNotBetween(Integer value1, Integer value2) {
            addCriterion("height not between", value1, value2, "height");
            return (Criteria) this;
        }

        public Criteria andUrlIsNull() {
            addCriterion("url is null");
            return (Criteria) this;
        }

        public Criteria andUrlIsNotNull() {
            addCriterion("url is not null");
            return (Criteria) this;
        }

        public Criteria andUrlEqualTo(String value) {
            addCriterion("url =", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotEqualTo(String value) {
            addCriterion("url <>", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThan(String value) {
            addCriterion("url >", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlGreaterThanOrEqualTo(String value) {
            addCriterion("url >=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThan(String value) {
            addCriterion("url <", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLessThanOrEqualTo(String value) {
            addCriterion("url <=", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlLike(String value) {
            addCriterion("url like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotLike(String value) {
            addCriterion("url not like", value, "url");
            return (Criteria) this;
        }

        public Criteria andUrlIn(List<String> values) {
            addCriterion("url in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotIn(List<String> values) {
            addCriterion("url not in", values, "url");
            return (Criteria) this;
        }

        public Criteria andUrlBetween(String value1, String value2) {
            addCriterion("url between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andUrlNotBetween(String value1, String value2) {
            addCriterion("url not between", value1, value2, "url");
            return (Criteria) this;
        }

        public Criteria andMd5IsNull() {
            addCriterion("md5 is null");
            return (Criteria) this;
        }

        public Criteria andMd5IsNotNull() {
            addCriterion("md5 is not null");
            return (Criteria) this;
        }

        public Criteria andMd5EqualTo(String value) {
            addCriterion("md5 =", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotEqualTo(String value) {
            addCriterion("md5 <>", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5GreaterThan(String value) {
            addCriterion("md5 >", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5GreaterThanOrEqualTo(String value) {
            addCriterion("md5 >=", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5LessThan(String value) {
            addCriterion("md5 <", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5LessThanOrEqualTo(String value) {
            addCriterion("md5 <=", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5Like(String value) {
            addCriterion("md5 like", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotLike(String value) {
            addCriterion("md5 not like", value, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5In(List<String> values) {
            addCriterion("md5 in", values, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotIn(List<String> values) {
            addCriterion("md5 not in", values, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5Between(String value1, String value2) {
            addCriterion("md5 between", value1, value2, "md5");
            return (Criteria) this;
        }

        public Criteria andMd5NotBetween(String value1, String value2) {
            addCriterion("md5 not between", value1, value2, "md5");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdIsNull() {
            addCriterion("schedule_group_id is null");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdIsNotNull() {
            addCriterion("schedule_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdEqualTo(Integer value) {
            addCriterion("schedule_group_id =", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdNotEqualTo(Integer value) {
            addCriterion("schedule_group_id <>", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdGreaterThan(Integer value) {
            addCriterion("schedule_group_id >", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("schedule_group_id >=", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdLessThan(Integer value) {
            addCriterion("schedule_group_id <", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("schedule_group_id <=", value, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdIn(List<Integer> values) {
            addCriterion("schedule_group_id in", values, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdNotIn(List<Integer> values) {
            addCriterion("schedule_group_id not in", values, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("schedule_group_id between", value1, value2, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andScheduleGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("schedule_group_id not between", value1, value2, "scheduleGroupId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdIsNull() {
            addCriterion("brand_creative_id is null");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdIsNotNull() {
            addCriterion("brand_creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdEqualTo(Integer value) {
            addCriterion("brand_creative_id =", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdNotEqualTo(Integer value) {
            addCriterion("brand_creative_id <>", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdGreaterThan(Integer value) {
            addCriterion("brand_creative_id >", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("brand_creative_id >=", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdLessThan(Integer value) {
            addCriterion("brand_creative_id <", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdLessThanOrEqualTo(Integer value) {
            addCriterion("brand_creative_id <=", value, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdIn(List<Integer> values) {
            addCriterion("brand_creative_id in", values, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdNotIn(List<Integer> values) {
            addCriterion("brand_creative_id not in", values, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdBetween(Integer value1, Integer value2) {
            addCriterion("brand_creative_id between", value1, value2, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andBrandCreativeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("brand_creative_id not between", value1, value2, "brandCreativeId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIsNull() {
            addCriterion("app_package_id is null");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIsNotNull() {
            addCriterion("app_package_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdEqualTo(Integer value) {
            addCriterion("app_package_id =", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotEqualTo(Integer value) {
            addCriterion("app_package_id <>", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdGreaterThan(Integer value) {
            addCriterion("app_package_id >", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("app_package_id >=", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdLessThan(Integer value) {
            addCriterion("app_package_id <", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdLessThanOrEqualTo(Integer value) {
            addCriterion("app_package_id <=", value, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdIn(List<Integer> values) {
            addCriterion("app_package_id in", values, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotIn(List<Integer> values) {
            addCriterion("app_package_id not in", values, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdBetween(Integer value1, Integer value2) {
            addCriterion("app_package_id between", value1, value2, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andAppPackageIdNotBetween(Integer value1, Integer value2) {
            addCriterion("app_package_id not between", value1, value2, "appPackageId");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlIsNull() {
            addCriterion("compressed_pic_url is null");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlIsNotNull() {
            addCriterion("compressed_pic_url is not null");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlEqualTo(String value) {
            addCriterion("compressed_pic_url =", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlNotEqualTo(String value) {
            addCriterion("compressed_pic_url <>", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlGreaterThan(String value) {
            addCriterion("compressed_pic_url >", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlGreaterThanOrEqualTo(String value) {
            addCriterion("compressed_pic_url >=", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlLessThan(String value) {
            addCriterion("compressed_pic_url <", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlLessThanOrEqualTo(String value) {
            addCriterion("compressed_pic_url <=", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlLike(String value) {
            addCriterion("compressed_pic_url like", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlNotLike(String value) {
            addCriterion("compressed_pic_url not like", value, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlIn(List<String> values) {
            addCriterion("compressed_pic_url in", values, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlNotIn(List<String> values) {
            addCriterion("compressed_pic_url not in", values, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlBetween(String value1, String value2) {
            addCriterion("compressed_pic_url between", value1, value2, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicUrlNotBetween(String value1, String value2) {
            addCriterion("compressed_pic_url not between", value1, value2, "compressedPicUrl");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5IsNull() {
            addCriterion("compressed_pic_md5 is null");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5IsNotNull() {
            addCriterion("compressed_pic_md5 is not null");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5EqualTo(String value) {
            addCriterion("compressed_pic_md5 =", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5NotEqualTo(String value) {
            addCriterion("compressed_pic_md5 <>", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5GreaterThan(String value) {
            addCriterion("compressed_pic_md5 >", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5GreaterThanOrEqualTo(String value) {
            addCriterion("compressed_pic_md5 >=", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5LessThan(String value) {
            addCriterion("compressed_pic_md5 <", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5LessThanOrEqualTo(String value) {
            addCriterion("compressed_pic_md5 <=", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5Like(String value) {
            addCriterion("compressed_pic_md5 like", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5NotLike(String value) {
            addCriterion("compressed_pic_md5 not like", value, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5In(List<String> values) {
            addCriterion("compressed_pic_md5 in", values, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5NotIn(List<String> values) {
            addCriterion("compressed_pic_md5 not in", values, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5Between(String value1, String value2) {
            addCriterion("compressed_pic_md5 between", value1, value2, "compressedPicMd5");
            return (Criteria) this;
        }

        public Criteria andCompressedPicMd5NotBetween(String value1, String value2) {
            addCriterion("compressed_pic_md5 not between", value1, value2, "compressedPicMd5");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}