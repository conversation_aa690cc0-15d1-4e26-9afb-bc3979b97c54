package com.bilibili.ssa.platform.biz.service.converter;

import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.ssa.platform.api.splash_screen.dto.second_page.SsaSecondPageInfoDto;
import com.bilibili.ssa.platform.common.enums.SsaEffectiveType;
import com.bilibili.ssa.platform.common.enums.SsaSecondPageInteractStyleEnum;
import com.bilibili.ssa.platform.common.enums.SsaSecondPageShowStyleEnum;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/19 17:44
 */
public class SecondPageStyleConverter {
    public static SsaSecondPageShowStyleEnum convertShowStyle(Integer videoPlayMode, Integer effectiveType) {
        SsaEffectiveType effectiveType1 = SsaEffectiveType.getByCodeWithoutEx(effectiveType);
        if (!Objects.equals(effectiveType1.getSecondShowStyle(), SsaSecondPageShowStyleEnum.NONE)) {
            //effectiveType优先级比较高，如果存在则高优使用
            //https://www.tapd.cn/67874887/prong/stories/view/1167874887004448078
            return effectiveType1.getSecondShowStyle();
        }
        SsaVideoPlayModeEnum videoPlayMode1 = SsaVideoPlayModeEnum.getByCode(videoPlayMode);
        return videoPlayMode1.getSecondShowStyle();
    }

    public static SsaSecondPageInteractStyleEnum convertInteractStyle(Integer videoPlayMode, Integer effectiveType, SsaSecondPageInfoDto secondPage) {
        SsaEffectiveType effectiveType1 = SsaEffectiveType.getByCodeWithoutEx(effectiveType);
        if (!Objects.equals(effectiveType1.getSecondInteractStyle(), SsaSecondPageInteractStyleEnum.NONE)) {
            //effectiveType优先级比较高，如果存在则高优使用
            //https://www.tapd.cn/67874887/prong/stories/view/1167874887004448078
            return effectiveType1.getSecondInteractStyle();
        }
        SsaVideoPlayModeEnum videoPlayMode1 = SsaVideoPlayModeEnum.getByCode(videoPlayMode);
        if (Objects.equals(videoPlayMode1, SsaVideoPlayModeEnum.IMMERSIVE_VIDEO) && Objects.nonNull(secondPage)) {
            return SsaSecondPageInteractStyleEnum.getByCode(secondPage.getSecondInteractStyle());
        }
        return SsaSecondPageInteractStyleEnum.NONE;
    }
}
