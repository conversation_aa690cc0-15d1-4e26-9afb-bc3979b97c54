package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaOrderPo;
import com.bilibili.ssa.platform.biz.po.SsaOrderPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaOrderDao {
    long countByExample(SsaOrderPoExample example);

    int deleteByExample(SsaOrderPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaOrderPo record);

    int insertBatch(List<SsaOrderPo> records);

    int insertUpdateBatch(List<SsaOrderPo> records);

    int insert(SsaOrderPo record);

    int insertUpdateSelective(SsaOrderPo record);

    int insertSelective(SsaOrderPo record);

    List<SsaOrderPo> selectByExample(SsaOrderPoExample example);

    SsaOrderPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaOrderPo record, @Param("example") SsaOrderPoExample example);

    int updateByExample(@Param("record") SsaOrderPo record, @Param("example") SsaOrderPoExample example);

    int updateByPrimaryKeySelective(SsaOrderPo record);

    int updateByPrimaryKey(SsaOrderPo record);
}