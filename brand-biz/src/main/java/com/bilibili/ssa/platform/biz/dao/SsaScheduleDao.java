package com.bilibili.ssa.platform.biz.dao;

import com.bilibili.ssa.platform.biz.po.SsaSchedulePo;
import com.bilibili.ssa.platform.biz.po.SsaSchedulePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SsaScheduleDao {
    long countByExample(SsaSchedulePoExample example);

    int deleteByExample(SsaSchedulePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(SsaSchedulePo record);

    int insertBatch(List<SsaSchedulePo> records);

    int insertUpdateBatch(List<SsaSchedulePo> records);

    int insert(SsaSchedulePo record);

    int insertUpdateSelective(SsaSchedulePo record);

    int insertSelective(SsaSchedulePo record);

    List<SsaSchedulePo> selectByExample(SsaSchedulePoExample example);

    SsaSchedulePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") SsaSchedulePo record, @Param("example") SsaSchedulePoExample example);

    int updateByExample(@Param("record") SsaSchedulePo record, @Param("example") SsaSchedulePoExample example);

    int updateByPrimaryKeySelective(SsaSchedulePo record);

    int updateByPrimaryKey(SsaSchedulePo record);
}