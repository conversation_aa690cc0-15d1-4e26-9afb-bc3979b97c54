package com.bilibili.ssa.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class SsaSplashScreenBrandCreativeMappingPo implements Serializable {
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 闪屏id
     */
    private Integer splashScreenId;

    /**
     * brad系统创意id
     */
    private Integer brandCreativeId;

    /**
     * 软删除0-有效1删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 资源位id
     */
    private Integer sourceId;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSplashScreenId() {
        return splashScreenId;
    }

    public void setSplashScreenId(Integer splashScreenId) {
        this.splashScreenId = splashScreenId;
    }

    public Integer getBrandCreativeId() {
        return brandCreativeId;
    }

    public void setBrandCreativeId(Integer brandCreativeId) {
        this.brandCreativeId = brandCreativeId;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }
}