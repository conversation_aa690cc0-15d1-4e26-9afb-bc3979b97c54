package com.bilibili.ssa.platform.biz.po.querydsl;

import javax.annotation.Generated;

/**
 * GdBlockingInfoPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class GdBlockingInfoPo {

    private String blockingInfo;

    private Integer blockingType;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer scheduleId;

    public String getBlockingInfo() {
        return blockingInfo;
    }

    public void setBlockingInfo(String blockingInfo) {
        this.blockingInfo = blockingInfo;
    }

    public Integer getBlockingType() {
        return blockingType;
    }

    public void setBlockingType(Integer blockingType) {
        this.blockingType = blockingType;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    @Override
    public String toString() {
         return "blockingInfo = " + blockingInfo + ", blockingType = " + blockingType + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", scheduleId = " + scheduleId;
    }

}

