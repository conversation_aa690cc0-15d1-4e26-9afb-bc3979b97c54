package com.bilibili.ssa.platform.biz.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.ssa.platform.api.location.dto.SourceConfigDto;
import com.bilibili.ssa.platform.api.location.dto.SsaGdPriceDTO;
import com.bilibili.ssa.platform.api.location.dto.SsaSourceAllInfoDto;
import com.bilibili.ssa.platform.api.location.dto.UpdateSsaSourceConfigDto;
import com.bilibili.ssa.platform.api.location.service.ISsaSourceService;
import com.bilibili.ssa.platform.soa.ISoaSsaSourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017年6月14日
 */
@Service
public class SoaSsaSourceServiceImpl implements ISoaSsaSourceService {
    @Autowired
    private ISsaSourceService ssaSourceService;

    @Override
    public void updateSourceConfig(Operator operator, UpdateSsaSourceConfigDto updateSourceConfigDto) {
        ssaSourceService.updateSourceConfig(operator, updateSourceConfigDto);
    }

    @Override
    public List<SsaSourceAllInfoDto> getSourcesByCycleId(Integer cycleId, Integer salesType) {
        return ssaSourceService.getSourcesByCycleId(cycleId, salesType);
    }
    
    @Override
    public void batchUpdateSourceConfigByCycleId(Operator operator, Integer cycleId, UpdateSsaSourceConfigDto updateSourceConfigDto) {
    	ssaSourceService.batchUpdateSourceConfigByCycleId(operator, cycleId, updateSourceConfigDto);
    }

    @Override
    public Map<Integer, List<SourceConfigDto>> getMapSourceConfigDtoByCycleIds(List<Integer> cycleIds) {
        return ssaSourceService.getMapSourceConfigDtoByCycleIds(cycleIds);
    }

    @Override
    public List<SsaGdPriceDTO> getSsaGdPriceByCycleId(Integer cycleId, Integer orderProduct) {
        return ssaSourceService.getSsaGdPriceByCycleId(cycleId, orderProduct);
    }

    @Override
    public void updateSsaGdPrice(Operator operator, SsaGdPriceDTO priceDTO) {
        ssaSourceService.updateSsaGdPrice(operator, priceDTO);
    }
}
