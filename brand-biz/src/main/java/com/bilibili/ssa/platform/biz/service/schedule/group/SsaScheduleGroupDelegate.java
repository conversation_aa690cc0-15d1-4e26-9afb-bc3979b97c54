/**
* <AUTHOR>
* @date  2018年5月23日
*/

package com.bilibili.ssa.platform.biz.service.schedule.group;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;
import com.bilibili.ssa.platform.api.schedule.group.dto.SsaScheduleGroupDto;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleGroupBrandMappingDao;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleGroupDao;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleTargetDao;
import com.bilibili.ssa.platform.biz.po.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class SsaScheduleGroupDelegate {

    @Autowired
    private SsaScheduleGroupDao scheduleGroupDao;
    @Autowired
    private SsaScheduleTargetDao scheduleTargetDao;
    @Autowired
    private SsaScheduleGroupBrandMappingDao ssaScheduleGroupBrandMappingDao;

    @Value("#{PropertySplitter.mapInt('${ssa.source.slot.group.map}')}")
    private Map<Integer, Integer> sourceToSlotGroupMap;

    @Value("#{PropertySplitter.mapInt('${ssa.source.platform.map}')}")
    private Map<Integer, Integer> sourceToPlatformMap;

    @Value("${ssa.brand.template.id}")
    private Integer brandTemplateId;
    @Value("${ssa.brand.stock.full.ratio:80}")

    private Integer brandStockFullRatio;

    public SsaScheduleGroupDto loadScheduleGroup(Integer id) {
        SsaScheduleGroupPo po = loadPo(id);

        SsaScheduleTargetDto target = getTargetByScheduleGroupId(po.getId());

        return poToDto(po, target);
    }

    private SsaScheduleGroupPo loadPo(Integer id) {
        Assert.notNull(id, "id不能为空");

        SsaScheduleGroupPo po = scheduleGroupDao.selectByPrimaryKey(id);

        Assert.notNull(po, "单元不存在");

        return po;
    }

    private SsaScheduleGroupDto poToDto(SsaScheduleGroupPo po, SsaScheduleTargetDto target) {
        SsaScheduleGroupDto dto = new SsaScheduleGroupDto();
        BeanUtils.copyProperties(po, dto);

        dto.setTarget(target);

        return dto;
    }

    private SsaScheduleTargetDto getTargetByScheduleGroupId(Integer id) {
        SsaScheduleTargetPoExample example = new SsaScheduleTargetPoExample();
        example.or()
        .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
        .andScheduleGroupIdEqualTo(id);

        List<SsaScheduleTargetPo> pos = scheduleTargetDao.selectByExample(example);

        return buildTargetDto(pos);
    }

    private SsaScheduleTargetDto buildTargetDto(List<SsaScheduleTargetPo> pos) {
        SsaScheduleTargetDto target = SsaScheduleTargetDto.getEmpty();

        if(CollectionUtils.isEmpty(pos)) {
            return target;
        }

        for(SsaScheduleTargetPo po: pos) {
            switch(TargetType.getByCode(po.getTargetType())) {
            case AREA:
                target.setArea(JSON.parseArray(po.getTargetItemIds(), Integer.class));
                break;
            case AGE:
                target.setAge(JSON.parseArray(po.getTargetItemIds(), Integer.class));
                break;
            case GENDER:
                target.setGender(JSON.parseArray(po.getTargetItemIds(), Integer.class));
                break;
            case CATEGORY:
                break;
            case KEYWORD:
                break;
            case NETWORK:
                break;
            case OS:
                break;
            default:
                break;
            }
        }

        return target;
    }

    public Map<Integer, Integer> getSourceIdBrandScheduleMap(Integer id) {
        SsaScheduleGroupBrandMappingPoExample example = new SsaScheduleGroupBrandMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andScheduleGroupIdEqualTo(id);

        List<SsaScheduleGroupBrandMappingPo> pos = ssaScheduleGroupBrandMappingDao.selectByExample(example);

        if(CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(SsaScheduleGroupBrandMappingPo::getSourceId, SsaScheduleGroupBrandMappingPo::getBrandScheduleId));
    }
}
