package com.bilibili.ssa.platform.biz.temp.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauUnitDatePo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 单元ID
     */
    private Integer unitId;

    /**
     * 计划id
     */
    private Integer campaignId;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 单元投放日期YYYY-MM-DD
     */
    private Timestamp unitDate;

    /**
     * 是否删除0否 1是
     */
    private Integer isDeleted;

    private Timestamp ctime;

    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}