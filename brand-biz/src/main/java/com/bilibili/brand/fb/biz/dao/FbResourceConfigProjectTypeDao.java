package com.bilibili.brand.fb.biz.dao;

import com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePo;
import com.bilibili.brand.fb.biz.po.FbResourceConfigProjectTypePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface FbResourceConfigProjectTypeDao {
    long countByExample(FbResourceConfigProjectTypePoExample example);

    int deleteByExample(FbResourceConfigProjectTypePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(FbResourceConfigProjectTypePo record);

    int insertBatch(List<FbResourceConfigProjectTypePo> records);

    int insertUpdateBatch(List<FbResourceConfigProjectTypePo> records);

    int insert(FbResourceConfigProjectTypePo record);

    int insertUpdateSelective(FbResourceConfigProjectTypePo record);

    int insertSelective(FbResourceConfigProjectTypePo record);

    List<FbResourceConfigProjectTypePo> selectByExample(FbResourceConfigProjectTypePoExample example);

    FbResourceConfigProjectTypePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FbResourceConfigProjectTypePo record, @Param("example") FbResourceConfigProjectTypePoExample example);

    int updateByExample(@Param("record") FbResourceConfigProjectTypePo record, @Param("example") FbResourceConfigProjectTypePoExample example);

    int updateByPrimaryKeySelective(FbResourceConfigProjectTypePo record);

    int updateByPrimaryKey(FbResourceConfigProjectTypePo record);
}