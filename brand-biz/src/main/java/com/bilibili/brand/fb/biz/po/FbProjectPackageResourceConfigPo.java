package com.bilibili.brand.fb.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FbProjectPackageResourceConfigPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 项目资源ID，对应fb_project_package表主键
     */
    private Long projectPackageId;

    /**
     * 资源刊例价ID，对应fb_resource_config表主键
     */
    private Long resourceConfigId;

    /**
     * 刊例总价
     */
    private Long price;

    /**
     * 净价总价
     */
    private Long netPrice;

    /**
     * 刊例单价
     */
    private Long unitPrice;

    /**
     * 净价单价
     */
    private Long unitNetPrice;

    /**
     * 单位曝光数量
     */
    private Long unitExposureCpm;

    /**
     * 单集时长(秒) 
     */
    private Integer unitEpisodeDuration;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除（软删除字段）: 1 - 已删除, 0 - 未删除
     */
    private Integer isDeleted;

    /**
     * 单集CPM
     */
    private Long unitEpisodeCpm;

    private static final long serialVersionUID = 1L;
}