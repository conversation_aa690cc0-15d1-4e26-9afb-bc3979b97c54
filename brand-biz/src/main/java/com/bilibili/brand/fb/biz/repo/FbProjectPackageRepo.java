package com.bilibili.brand.fb.biz.repo;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.fb.biz.convert.FbProjectPackageConverter;
import com.bilibili.brand.fb.biz.dao.FbProjectPackageDao;
import com.bilibili.brand.fb.biz.dao.FbProjectPackageResourceConfigDao;
import com.bilibili.brand.fb.biz.po.*;
import com.bilibili.brand.fb.biz.service.project.bo.FbProjectPackage;
import com.bilibili.brand.fb.biz.service.project.bo.FbProjectPackageDetail;
import com.bilibili.brand.fb.biz.service.project.bo.FbProjectPackageResourceBo;
import com.bilibili.brand.fb.biz.service.project.bo.QueryProjectPackage;
import com.bilibili.brand.fb.biz.service.resource.bo.FbResourceConfigBo;
import com.bilibili.cpt.platform.biz.utils.ExampleUtils;
import com.google.common.collect.Maps;
import javafx.util.Pair;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/3
 **/

@Repository
public class FbProjectPackageRepo {

    @Resource
    private FbProjectPackageDao projectPackageDao;

    @Resource
    private FbProjectPackageResourceConfigDao projectPackageResourceConfigDao;

    @Resource
    private FbResourceConfigRepo fbResourceConfigRepo;

    public FbProjectPackageDetail getProjectPackageDetail(Long id) {
        // 查询包信息
        FbProjectPackagePo projectPackagePo = projectPackageDao.selectByPrimaryKey(id);
        Assert.notNull(projectPackagePo, String.format("项目包「%s」不存在", id));
        FbProjectPackage projectPackage = FbProjectPackageConverter.MAPPER.poToBo(projectPackagePo);
        FbProjectPackageDetail detail = new FbProjectPackageDetail(projectPackage);
        // 查询资源包信息
        FbProjectPackageResourceConfigPoExample example = new FbProjectPackageResourceConfigPoExample();
        FbProjectPackageResourceConfigPoExample.Criteria criteria = example.createCriteria();
        criteria.andProjectPackageIdEqualTo(id);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<FbProjectPackageResourceConfigPo> resourceConfigPos = projectPackageResourceConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(resourceConfigPos)) return detail;
        List<Long> resourceIds = resourceConfigPos.stream().map(FbProjectPackageResourceConfigPo::getResourceConfigId).distinct().collect(Collectors.toList());
        List<FbResourceConfigBo> resourceConfigs = fbResourceConfigRepo.queryByIds(resourceIds);
        Map<Long, FbResourceConfigBo> resourceConfigMap = resourceConfigs.stream().collect(Collectors.toMap(FbResourceConfigBo::getId, Function.identity()));
        List<FbProjectPackageResourceBo> packageResources = resourceConfigPos.stream()
                .map(po -> FbProjectPackageConverter.MAPPER.resourcePoToBo(po, resourceConfigMap.getOrDefault(po.getResourceConfigId(), new FbProjectPackageResourceBo())))
                .map(FbProjectPackageResourceBo::fillPrice)
                .collect(Collectors.toList());
        detail.setProjectResources(packageResources);
        detail.fillPrice();
        return detail;
    }

    public List<FbProjectPackage> queryByCondition(QueryProjectPackage queryProjectPackage) {
        FbProjectPackagePoExample example = new FbProjectPackagePoExample();
        FbProjectPackagePoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ExampleUtils.notEmpty(queryProjectPackage.getProjectTypes(), criteria::andProjectTypeIn);
        ExampleUtils.notEmpty(queryProjectPackage.getProjectLevels(), criteria::andProjectLevelIn);
        ExampleUtils.notEmpty(queryProjectPackage.getGeneralIds(), criteria::andParentIdIn);
        ExampleUtils.notNull(queryProjectPackage.getType(), criteria::andTypeEqualTo);
        ExampleUtils.notNull(queryProjectPackage.getProjectName(), criteria::andProjectNameEqualTo);
        ExampleUtils.notNull(queryProjectPackage.getProjectNameLike(), criteria::andProjectNameLike);
        example.setOrderByClause("mtime desc");
        List<FbProjectPackagePo> packagePos = projectPackageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(packagePos)) {
            return Collections.emptyList();
        }
        return packagePos.stream().map(FbProjectPackageConverter.MAPPER::poToBo).collect(Collectors.toList());
    }

    public Map<Long, FbProjectPackageResourceConfigPo> getProjectPackageResourceConfig(Long projectPackageId) {
        FbProjectPackageResourceConfigPoExample example = new FbProjectPackageResourceConfigPoExample();
        FbProjectPackageResourceConfigPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andProjectPackageIdEqualTo(projectPackageId);
        List<FbProjectPackageResourceConfigPo> pos = projectPackageResourceConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Maps.newHashMap();
        }
        return pos.stream().collect(Collectors.toMap(FbProjectPackageResourceConfigPo::getResourceConfigId, Function.identity(), (v1, v2) -> v1));
    }
}
