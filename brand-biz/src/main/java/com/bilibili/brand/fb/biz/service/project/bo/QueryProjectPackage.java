package com.bilibili.brand.fb.biz.service.project.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/26
 **/

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class QueryProjectPackage {

    /**
     * 包类型: 1 - 通包, 2 - 客户包
     */
    private Integer type;

    /**
     * 项目类型: 1 - 整合营销-非游, 2 - 整合营销-游戏, 3 - 整合营销-互动营销, 4 - 零卖-游戏, 5 - 零卖-非游, 6 - OGV
     */
    private List<Integer> projectTypes;

    /**
     * 项目级别: S+, S, A+, A, B
     */
    private List<String> projectLevels;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目名称(模糊)
     */
    private String projectNameLike;

    /**
     * 通包id
     */
    private List<Long> generalIds;
}
