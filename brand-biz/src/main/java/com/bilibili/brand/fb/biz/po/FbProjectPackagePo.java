package com.bilibili.brand.fb.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FbProjectPackagePo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 通包ID
     */
    private Long parentId;

    /**
     * 包类型: 1 - 通包, 2 - 客户包
     */
    private Integer type;

    /**
     * 项目大类: 1 - 整合营销, 2 - 零卖, 3 - OGV
     */
    private Integer projectMajorType;

    /**
     * 项目类型: 1 - 整合营销-非游, 2 - 整合营销-游戏, 3 - 整合营销-互动营销, 4 - 零卖-游戏, 5 - 零卖-非游, 6 - OGV
     */
    private Integer projectType;

    /**
     * 营销分类: 1 - 分区生态+ 节点营销, 2 - 行业IP, 3 - 商业定制, 4 - 音乐, ...
     */
    private Integer marketingType;

    /**
     * 营销团队: 1 - PUGV整合营销组, 2 - OGV内容营销组, 3 - PUGV整合营销组/游戏商业生态部, 4 - PUGV整合营销组
     */
    private Integer marketingTeam;

    /**
     * 项目级别: S+, S, A+, A, B
     */
    private String projectLevel;

    /**
     * 立项部门: CRM资源提供方
     */
    private String projectDept;

    /**
     * 项目预估总收入
     */
    private Long totalIncome;

    /**
     * 项目预估非标收入
     */
    private Long fbIncome;

    /**
     * 立项ID
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 包状态: 1 - 待审核, 2 - 审核通过, 3 - 审核驳回
     */
    private Integer status;

    /**
     * 创建人名称
     */
    private String creator;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除（软删除字段）: 1 - 已删除, 0 - 未删除
     */
    private Integer isDeleted;

    /**
     * 客户ID
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    private static final long serialVersionUID = 1L;
}