package com.bilibili.brand.fb.biz.dao;

import com.bilibili.brand.fb.biz.po.FbProjectPackageResourceConfigPo;
import com.bilibili.brand.fb.biz.po.FbProjectPackageResourceConfigPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface FbProjectPackageResourceConfigDao {
    long countByExample(FbProjectPackageResourceConfigPoExample example);

    int deleteByExample(FbProjectPackageResourceConfigPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(FbProjectPackageResourceConfigPo record);

    int insertBatch(List<FbProjectPackageResourceConfigPo> records);

    int insertUpdateBatch(List<FbProjectPackageResourceConfigPo> records);

    int insert(FbProjectPackageResourceConfigPo record);

    int insertUpdateSelective(FbProjectPackageResourceConfigPo record);

    int insertSelective(FbProjectPackageResourceConfigPo record);

    List<FbProjectPackageResourceConfigPo> selectByExample(FbProjectPackageResourceConfigPoExample example);

    FbProjectPackageResourceConfigPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") FbProjectPackageResourceConfigPo record, @Param("example") FbProjectPackageResourceConfigPoExample example);

    int updateByExample(@Param("record") FbProjectPackageResourceConfigPo record, @Param("example") FbProjectPackageResourceConfigPoExample example);

    int updateByPrimaryKeySelective(FbProjectPackageResourceConfigPo record);

    int updateByPrimaryKey(FbProjectPackageResourceConfigPo record);
}