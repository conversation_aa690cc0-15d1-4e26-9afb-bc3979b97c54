package com.bilibili.brand.biz.inventory.bo.stock;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 锁量任务结果
 *
 * <AUTHOR>
 * @date 2025/3/7 15:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AllocateTaskStock {
    /**
     * 0：成功
     * <p>
     * 其他：失败
     */
    private Integer code;
    /**
     * 错误信息
     */
    private String message;
    /**
     * 排期ID
     */
    private Integer scheduleId;
    /**
     * 目标展示量，单位：cpm
     */
    private Long cpm;
}
