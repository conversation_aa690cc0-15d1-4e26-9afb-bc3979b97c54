package com.bilibili.brand.biz.deduction.handler;

import com.bilibili.brand.api.common.enums.FeeDeductionStatus;
import com.bilibili.brand.biz.deduction.dao.GdFeeDeductionInfoDao;
import com.bilibili.brand.biz.deduction.handler.bean.OrderConsumptionBean;
import com.bilibili.brand.biz.deduction.handler.bean.SourcePriceConfigBean;
import com.bilibili.brand.biz.deduction.po.GdFeeDeductionInfoPo;
import com.bilibili.brand.biz.deduction.po.GdFeeDeductionInfoPoExample;
import com.bilibili.brand.biz.rpc.grpc.client.CrmWalletGrpcClient;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public abstract class AbstractOrderDeductionHandler implements OrderDeductionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractOrderDeductionHandler.class);

    protected abstract List<OrderConsumptionBean> getOrderDailyConsumption(Timestamp date);

    protected abstract Map<Integer, BigDecimal> getSourcePriceMap(Timestamp date,
                                                                  List<SourcePriceConfigBean> sourcePriceConfigList);

    @Autowired
    private GdFeeDeductionInfoDao gdFeeDeductionInfoDao;

    @Autowired
    private IBusinessSideService businessSideService;

    @Autowired
    private CrmWalletGrpcClient crmWalletGrpcClient;

    @Override
    public void deductFee(Timestamp date) {
        Assert.notNull(date, "日期不可为空");

        // 查询日消耗
        List<OrderConsumptionBean> orderConsumptionList = getOrderDailyConsumption(date);
        if (CollectionUtils.isEmpty(orderConsumptionList)) {
            return;
        }

        // 过滤掉外部账户
        Set<Integer> internalAccountIdSet = businessSideService.getInternalBusinessSideAccountIds(
                orderConsumptionList.stream().map(OrderConsumptionBean::getAccountId)
                        .distinct()
                        .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(internalAccountIdSet)) {
            return;
        }

        orderConsumptionList = orderConsumptionList.stream()
                .filter(consumption -> internalAccountIdSet.contains(consumption.getAccountId()))
                .collect(Collectors.toList());

        // 查询cpm单价
        List<SourcePriceConfigBean> sourcePriceConfigList = orderConsumptionList.stream().map(orderConsumptionBean ->
                SourcePriceConfigBean.builder()
                        .sourceId(orderConsumptionBean.getSourceId())
                        .showStyle(orderConsumptionBean.getShowStyle())
                        .clickArea(orderConsumptionBean.getClickArea())
                        .build())
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, BigDecimal> sourcePriceMap = getSourcePriceMap(date, sourcePriceConfigList);
        if (CollectionUtils.isEmpty(sourcePriceMap)) {
            return;
        }

        // 每个账号按订单维度聚合扣款
        // Map<accountId, <orderId, <sourcePriceConfigHash, showCount>>>
        Map<Integer, Map<Integer, Map<Integer, Integer>>> accountMap = orderConsumptionList.stream()
                .collect(Collectors.groupingBy(OrderConsumptionBean::getAccountId,
                        Collectors.groupingBy(OrderConsumptionBean::getOrderId,
                                Collectors.groupingBy(orderConsumptionBean -> SourcePriceConfigBean.builder()
                                                .sourceId(orderConsumptionBean.getSourceId())
                                                .showStyle(orderConsumptionBean.getShowStyle())
                                                .clickArea(orderConsumptionBean.getClickArea())
                                                .build()
                                                .hashCode(),
                                        Collectors.summingInt(OrderConsumptionBean::getShowCount)))));

        for (Map.Entry<Integer, Map<Integer, Map<Integer, Integer>>> accountEntry : accountMap.entrySet()) {
            // 账号
            Integer accountId = accountEntry.getKey();
            for (Map.Entry<Integer, Map<Integer, Integer>> orderEntry : accountEntry.getValue().entrySet()) {
                // 订单
                try {
                    deductGroupByOrder(date, accountId, orderEntry.getKey(), orderEntry.getValue(), sourcePriceMap);
                } catch (Exception e) {
                    LOGGER.error("deductGroupByOrder error date {} accountId {} orderId {}", date, accountId, orderEntry.getKey(), e);
                }
            }
        }
    }

    private void deductGroupByOrder(Timestamp date, Integer accountId, Integer orderId,
                                    Map<Integer, Integer> orderSourceMap, Map<Integer, BigDecimal> sourcePriceMap) {
        BigDecimal orderCost = new BigDecimal(0);
        for (Map.Entry<Integer, Integer> sourceEntry : orderSourceMap.entrySet()) {
            // 资源位
            Integer sourcePriceConfigHash = sourceEntry.getKey();
            Integer showCount = sourceEntry.getValue();
            BigDecimal sourcePrice = sourcePriceMap.get(sourcePriceConfigHash);
            if (sourcePrice != null && sourcePrice.compareTo(BigDecimal.ZERO) > 0
                    && showCount != null && showCount > 0) {
                BigDecimal cost = sourcePrice.multiply(new BigDecimal(showCount))
                        .divide(new BigDecimal(1000), 2, BigDecimal.ROUND_HALF_DOWN);
                orderCost = orderCost.add(cost);
            } else {
                LOGGER.info("no enough deduction info accountId {} orderId {} sourcePriceConfigHash {} sourcePrice {}" +
                        "showCount {}", accountId, orderId, sourcePriceConfigHash, sourcePrice, showCount);
            }
        }
        if (orderCost.compareTo(BigDecimal.ZERO) <= 0) {
            LOGGER.info("deduction empty cost accountId {} orderId {}", accountId, orderId);
            return;
        }
        // 记录流水表
        GdFeeDeductionInfoPo deductionInfoPo = GdFeeDeductionInfoPo.builder()
                .accountId(accountId)
                .orderId(orderId)
                .groupTime(date)
                .cost(orderCost)
                .status(FeeDeductionStatus.NOT_PAID.getCode())
                .build();
        gdFeeDeductionInfoDao.insertSelective(deductionInfoPo);

        // 分订单扣款
        LOGGER.info("accountId {} orderId {} deductionInfoId {} orderCost {}", accountId, orderId,
                deductionInfoPo.getId(), orderCost);
        deductWallet(accountId, deductionInfoPo.getId(), orderCost);
    }

    private void deductWallet(Integer accountId, Integer deductInfoId, BigDecimal cost) {
        String serialNumber = deductCrmWallet(accountId, deductInfoId, cost);
        // 更新流水表
        if (StringUtils.hasText(serialNumber)) {
            GdFeeDeductionInfoPo updatePo = GdFeeDeductionInfoPo.builder()
                    .id(deductInfoId)
                    .status(FeeDeductionStatus.PAID.getCode())
                    .crmSerialNumber(serialNumber)
                    .build();
            gdFeeDeductionInfoDao.updateByPrimaryKeySelective(updatePo);
        }
    }

    private String deductCrmWallet(Integer accountId, Integer deductInfoId, BigDecimal cost) {
        Transaction transaction = null;
        try {
            transaction = Cat.newTransaction("SoaAccountWalletService", "deductWallet");
            String serialNumber = crmWalletGrpcClient.deduct(accountId, deductInfoId.toString(), cost);
            transaction.setStatus(Transaction.SUCCESS);
            return serialNumber;
        } catch (Exception e) {
            LOGGER.error("deductWallet error", e);
            if (transaction != null) {
                transaction.setStatus(e);
            }
            throw e;
        } finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
    }

    @Override
    public void handleFailDeduction() {
        int batchSize = 100;
        Integer lastMaxId = 0;
        while (true) {
            GdFeeDeductionInfoPoExample example = new GdFeeDeductionInfoPoExample();
            example.or().andStatusEqualTo(FeeDeductionStatus.NOT_PAID.getCode())
                    .andIdGreaterThan(lastMaxId);
            example.setOrderByClause("id");
            example.setLimit(batchSize);
            List<GdFeeDeductionInfoPo> poList = gdFeeDeductionInfoDao.selectByExample(example);
            if (CollectionUtils.isEmpty(poList)) {
                break;
            }
            lastMaxId = poList.get(poList.size() - 1).getId();

            for (GdFeeDeductionInfoPo deductionInfo : poList) {
                try {
                    processFailDeduction(deductionInfo);
                } catch (Exception e) {
                    LOGGER.error("processFailDeduction error", e);
                }
            }

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                LOGGER.error("InterruptedException", e);
            }
        }
    }

    private void processFailDeduction(GdFeeDeductionInfoPo deductionInfo) {
        if (deductionInfo.getCost() == null
                || deductionInfo.getCost().compareTo(BigDecimal.ZERO) <= 0) {
            gdFeeDeductionInfoDao.updateByPrimaryKeySelective(
                    GdFeeDeductionInfoPo.builder()
                            .id(deductionInfo.getId())
                            .status(FeeDeductionStatus.PAID.getCode())
                            .build());
            return;
        }
        deductWallet(deductionInfo.getAccountId(), deductionInfo.getId(), deductionInfo.getCost());
    }
}
