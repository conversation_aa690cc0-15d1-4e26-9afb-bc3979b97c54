package com.bilibili.brand.biz.creative.service;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.dao.GdCreativeMiniProgramDao;
import com.bilibili.brand.biz.creative.po.GdCreativeMiniProgramPo;
import com.bilibili.brand.biz.creative.po.GdCreativeMiniProgramPoExample;
import com.bilibili.brand.dto.creative.CreativeMiniProgramDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/16 15:51
 */
@Slf4j
@Service
public class CreativeMiniProgramService {
    @Autowired
    private GdCreativeMiniProgramDao creativeMiniProgramDao;

    /**
     * 插入或者编辑小程序
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCreativeMiniProgram(CreativeMiniProgramDto dto) {
        Assert.notNull(dto.getOrderProduct(), "orderProduct must not be null");
        Assert.notNull(dto.getCreativeId(), "creativeId must not be null");

        List<GdCreativeMiniProgramPo> poList = this.getCreativeMiniProgramPo(dto.getOrderProduct(),
                Lists.newArrayList(dto.getCreativeId()));

        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004523310
        //【品牌】TopView支持闪屏、首焦分开跳转-香奈儿3月商机
        //选择唤起类型是：应用或者小程序时，创意层级是可能发生删除的，比如从应用切换到小程序时
        GdCreativeMiniProgramPo oldPo = CollectionUtils.isEmpty(poList) ? null : poList.get(0);
        MiniProgramDto miniProgram = dto.getMiniProgram();
        boolean isInvalid = Objects.isNull(miniProgram) || !miniProgram.isValid();
        if (isInvalid) {
            //尝试删除旧记录
            if (Objects.nonNull(oldPo)) {
                deleteCreativeMiniProgram(oldPo.getId());
            }
            return;
        }

        GdCreativeMiniProgramPo po = GdCreativeMiniProgramPo.builder()
                .id(Objects.isNull(oldPo) ? null : oldPo.getId())
                .orderProduct(dto.getOrderProduct())
                .creativeId(dto.getCreativeId())
                .miniProgramId(miniProgram.getId())
                .miniProgramName(miniProgram.getName())
                .miniProgramPath(miniProgram.getPath())
                .build();
        if (Objects.isNull(oldPo)) {
            this.creativeMiniProgramDao.insertSelective(po);
        } else {
            this.creativeMiniProgramDao.updateByPrimaryKeySelective(po);
        }
    }

    /**
     * 查询小程序信息
     */
    public CreativeMiniProgramDto getCreativeMiniProgram(Integer orderProduct, Long creativeId) {
        Assert.notNull(orderProduct, "orderProduct must not be null");
        Assert.notNull(creativeId, "creativeId must not be null");
        return this.getCreativeMiniProgram(orderProduct, Lists.newArrayList(creativeId)).get(creativeId);
    }

    /**
     * 查询小程序信息
     */
    public Map<Long, CreativeMiniProgramDto> getCreativeMiniProgram(Integer orderProduct, List<Long> creativeIdList) {
        Assert.notNull(orderProduct, "orderProduct must not be null");
        Assert.notEmpty(creativeIdList, "creativeId must not be empty");
        List<GdCreativeMiniProgramPo> poList = this.getCreativeMiniProgramPo(orderProduct, creativeIdList);
        return poList.stream()
                .map(po -> CreativeMiniProgramDto.builder()
                        .creativeId(po.getCreativeId())
                        .orderProduct(po.getOrderProduct())
                        .miniProgram(MiniProgramDto.builder()
                                .id(po.getMiniProgramId())
                                .name(po.getMiniProgramName())
                                .path(po.getMiniProgramPath())
                                .build())
                        .build()
                )
                .collect(Collectors.toMap(CreativeMiniProgramDto::getCreativeId, Function.identity(), OptionalUtil.override()));
    }


    private List<GdCreativeMiniProgramPo> getCreativeMiniProgramPo(Integer orderProduct, List<Long> creativeIdList) {
        GdCreativeMiniProgramPoExample example = new GdCreativeMiniProgramPoExample();
        example.createCriteria()
                .andOrderProductEqualTo(orderProduct)
                .andCreativeIdIn(creativeIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return this.creativeMiniProgramDao.selectByExample(example);
    }

    private void deleteCreativeMiniProgram(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        this.creativeMiniProgramDao.updateByPrimaryKeySelective(GdCreativeMiniProgramPo.builder()
                .id(id)
                .isDeleted(IsDeleted.DELETED.getCode())
                .build());
    }
}
