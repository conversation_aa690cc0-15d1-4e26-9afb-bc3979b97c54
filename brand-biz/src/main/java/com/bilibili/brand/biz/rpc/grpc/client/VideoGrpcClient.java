package com.bilibili.brand.biz.rpc.grpc.client;

import com.alibaba.fastjson.JSONObject;
import com.bapis.pgc.servant.video.*;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.brand.biz.rpc.converter.VideoConverter;
import com.bilibili.brand.biz.rpc.dto.SaveBrandOgvDto;
import com.bilibili.brand.biz.rpc.dto.VideoMaterialDetailDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.component.ecode.ServerCode;
import pleiades.component.ecode.exception.ServerException;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5
 **/

@Slf4j
@Component
public class VideoGrpcClient {
    @RPCClient("ogv.video.servant")
    private VideoGrpc.VideoBlockingStub videoBlockingStub;

    public boolean existsCid(Long cid) {
        return !queryByCids(Lists.newArrayList(cid)).isEmpty();
    }

    public VideoMaterialDetailDto getByCid(Long cid) {
        return queryByCids(Lists.newArrayList(cid)).get(cid);
    }

    public Map<Long, VideoMaterialDetailDto> queryByCids(List<Long> cids) {
        log.info("[VideoGrpcClient] queryByCids request. [cids={}]", JSONObject.toJSONString(cids));
        if (CollectionUtils.isEmpty(cids)) {
            return Maps.newHashMap();
        }
        try {
            VideoMaterialReply videoMaterialByCids = videoBlockingStub
                    .getVideoMaterialByCids(CidsRequest.newBuilder().addAllCids(cids).build());
            Map<Long, VideoMaterialDetail> materialsMap = videoMaterialByCids.getVideoMaterialsMap();
            if (MapUtils.isEmpty(materialsMap)) {
                return Maps.newHashMap();
            }
            return materialsMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> VideoConverter.MAPPER.toVideoMaterialDetailDto(entry.getValue())));
        } catch(ServerException e) {
            log.error("[VideoGrpcClient] queryByCids failed. [msg={}]", e.getMessage(), e);
            if (Objects.equals(ServerCode.BAD_REQUEST.getCode(), e.getCode())) {
                throw new IllegalArgumentException("调用OGV服务端失败");
            }
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("[VideoGrpcClient] queryByCids, error", e);
            throw new RuntimeException(e);
        }
    }

    public void addBrandOgv(SaveBrandOgvDto saveBrandOgvDto) {
        batchAddBrandOgv(Lists.newArrayList(saveBrandOgvDto));
    }

    public void batchAddBrandOgv(List<SaveBrandOgvDto> addBrandOgvDtos) {
        log.info("[VideoGrpcClient] addBrandOgv request. [addBrandOgvDtos={}]", JSONObject.toJSONString(addBrandOgvDtos));
        if (CollectionUtils.isEmpty(addBrandOgvDtos)) {
            return ;
        }
        try {
            CommonReply commonReply = videoBlockingStub.withDeadlineAfter(10, TimeUnit.SECONDS)
                    .batchCustomAddVideoFeature(BatchCustomAddVideoFeatureRequest.newBuilder()
                            .addAllAddVideoFeatures(addBrandOgvDtos.stream().map(VideoConverter.MAPPER::toBatchAddGrpc).collect(Collectors.toList()))
                            .build());
        } catch(ServerException e) {
            log.error("[VideoGrpcClient] batchAddBrandOgv failed. [msg={}]", e.getMessage(), e);
            if (Objects.equals(ServerCode.BAD_REQUEST.getCode(), e.getCode())) {
                throw new IllegalArgumentException("调用OGV服务端失败");
            }
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("[VideoGrpcClient] batchAddBrandOgv, error", e);
            throw new RuntimeException(e);
        }
    }

    public void updateBrandOgv(SaveBrandOgvDto updateBrandOgvDto) {
        log.info("[VideoGrpcClient] updateBrandOgv request. [updateBrandOgvDto={}]", JSONObject.toJSONString(updateBrandOgvDto));
        try {
            CommonReply commonReply = videoBlockingStub.withDeadlineAfter(10, TimeUnit.SECONDS)
                    .customUpdateVideoFeature(VideoConverter.MAPPER.toBatchUpdateGrpc(updateBrandOgvDto));
        } catch (ServerException e) {
            log.error("[VideoGrpcClient] updateBrandOgv failed. [msg={}]", e.getMessage());
            if (Objects.equals(ServerCode.BAD_REQUEST.getCode(), e.getCode())) {
                throw new IllegalArgumentException("调用OGV服务端失败");
            }
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("[VideoGrpcClient] updateBrandOgv, error", e);
            throw new RuntimeException(e);
        }
    }

    public void deleteBrandOgv(Long cid, Long startTime, Long endTime) {
        log.info("[VideoGrpcClient] deleteBrandOgv request. [cid={}, start_time={}, end_time={}]", cid, startTime, endTime);
        try {
            CommonReply commonReply = videoBlockingStub.withDeadlineAfter(10, TimeUnit.SECONDS)
                    .customDeleteVideoFeature(CustomDeleteVideoFeatureRequest.newBuilder()
                            .setCid(cid)
                            .setStartTime(startTime)
                            .setEndTime(endTime)
                            .setFeatureType("BRAND_ADVERTISEMENT").build());
        } catch (ServerException e) {
            log.error("[VideoGrpcClient] deleteBrandOgv failed. [msg={}]", e.getMessage());
            if (Objects.equals(ServerCode.BAD_REQUEST.getCode(), e.getCode())) {
                throw new IllegalArgumentException("调用OGV服务端失败");
            }
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("[VideoGrpcClient] deleteBrandOgv, error", e);
            throw new RuntimeException(e);
        }
    }
}
