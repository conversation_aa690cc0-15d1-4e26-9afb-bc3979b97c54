package com.bilibili.brand.biz.cycle.processor;

import com.bilibili.adp.common.bean.Operator;

public interface CycleProcessor<C, U> {

    /**
     * 创建刊例周期
     */
    Integer processCreate(Operator operator, C newCycleDto);

    /**
     * 更新刊例周期
     */
    void processUpdate(Operator operator, U updateCycleDto);

    /**
     * 启用刊例周期
     */
    void processEnable(Operator operator, Integer cycleId, Integer orderProduct);

    /**
     * 删除刊例周期
     */
    void processDelete(Operator operator, Integer cycleId, Integer orderProduct);
}
