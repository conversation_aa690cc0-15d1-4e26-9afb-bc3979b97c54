package com.bilibili.brand.biz.config.business;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
@Getter
@Component
public class PreviewConfig {

    @Value("${creative.preview.max.count}")
    protected Integer creativePreviewMaxCount;

    @Value("${creative.preview.schedule.beginDate.limit}")
    private Integer creativePreviewScheduleBeginDateLimit;

    /**
     * 创意纬度预览时间
     */
    @Value("${creative.preview.minute:1440}")
    private Integer creativePreviewMinute;

    /**
     * 创意下单个mid预览时间
     */
    @Value("${creative.preview.minute.for.each.mid:20}")
    @Getter
    private Integer creativePreviewMinuteForEachMid;

    @Value("${creative.preview.ssa.content:操作成功，请在Wi-Fi状态下进行预览。关闭app及应用后台，3分钟后重新开启app即可预览创意素材。}")
    private String ssaContent;

    @Value("${creative.preview.ssa.content:操作成功，请关闭app及应用后台，并再次进入即可预览。}")
    private String otherContent;

}
