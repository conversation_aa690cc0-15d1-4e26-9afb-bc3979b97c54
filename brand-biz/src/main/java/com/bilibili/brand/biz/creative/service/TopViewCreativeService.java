package com.bilibili.brand.biz.creative.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.api.common.ShareState;
import com.bilibili.brand.api.common.bean.LaunchConstant;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.cpt.platform.biz.utils.ExampleUtils;
import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.creative.dto.*;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.service.IScheduleService;
import com.bilibili.brand.biz.common.ScheduleCompareProperties;
import com.bilibili.brand.biz.creative.convert.TopViewConvert;
import com.bilibili.brand.biz.log.bean.TopViewCreativeLogBean;
import com.bilibili.brand.biz.schedule.dao.GdTopViewDao;
import com.bilibili.brand.biz.schedule.po.GdTopViewPo;
import com.bilibili.brand.biz.schedule.po.GdTopViewPoExample;
import com.bilibili.cpt.platform.api.audit.dto.CptCreativeAuditDto;
import com.bilibili.cpt.platform.api.creative.dto.CptTemplateDto;
import com.bilibili.cpt.platform.api.schedule.dto.ScheduleDateDto;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.cpt.platform.biz.service.creative.BrandCptCreativeDelegate;
import com.bilibili.cpt.platform.biz.service.creative.CptCreativeValidator;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.enums.TopViewVideoEnum;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenVideoService;
import com.bilibili.ssa.platform.biz.component.SsaLock;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.SsaScheduleServiceDelegate;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import com.bilibili.utils.ArrayUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TopViewCreativeService implements ITopViewCreativeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopViewCreativeService.class);

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private SsaSplashScreenServiceDelegate ssaSplashScreenServiceDelegate;

    @Autowired
    private SsaScheduleServiceDelegate ssaScheduleServiceDelegate;

    @Autowired
    private SsaLock ssaLock;

    @Autowired
    private GdTopViewDao gdTopViewDao;

    @Autowired
    private BrandCptCreativeDelegate brandCptCreativeDelegate;

    @Autowired
    private IGdCreativeService gdCreativeService;

    @Autowired
    private IScheduleService scheduleService;

    @Autowired
    private IGdLogService gdLogService;

    @Autowired
    private IGdOrderService orderService;

    @Autowired
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;
    @Autowired
    private TopViewConvert topViewConvert;

    @Resource(name = "cachedExecutorWithDecorator")
    private Executor cachedExecutor;

    @Autowired
    private CptCreativeValidator creativeValidator;

    @Autowired
    private CreativeExtService creativeExtService;

    @Autowired
    private CreativeMiniProgramService creativeMiniProgramService;

    @Autowired
    private TopViewCreativeValidator topViewCreativeValidator;

    @Autowired
    private ConfigCenter configCenter;

    @Override
    public List<CptTemplateDto> getTemplateListByScheduleId(Integer scheduleId, Operator operator) {
        Assert.notNull(scheduleId, "排期ID不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator));

        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(scheduleId);
        Assert.notNull(topViewScheduleInfo, "排期不存在");
        Map<Integer, List<TemplateDto>> sourceTemplateMap = queryTemplateService.getSourceTemplateMapBySourceIds(
                Lists.newArrayList(topViewScheduleInfo.getNewIosHfSourceId(), topViewScheduleInfo.getNewAndroidHfSourceId()));
        if (CollectionUtils.isEmpty(sourceTemplateMap)) {
            return Collections.emptyList();
        }

        List<Integer> newIosTemplateIdList = sourceTemplateMap.getOrDefault(
                        topViewScheduleInfo.getNewIosHfSourceId(), Collections.emptyList())
                .stream().map(TemplateDto::getTemplateId)
                .collect(Collectors.toList());
        List<Integer> newAndroidTemplateIdList = sourceTemplateMap.getOrDefault(
                        topViewScheduleInfo.getNewAndroidHfSourceId(), Collections.emptyList())
                .stream().map(TemplateDto::getTemplateId)
                .collect(Collectors.toList());

        newIosTemplateIdList.retainAll(newAndroidTemplateIdList);

        List<CptTemplateDto> newTemplateDtos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(newIosTemplateIdList)) {
            Set<Integer> bannerTemplateSet = configCenter.getTopViewConfig().getTopViewBannerTemplateIdSet();
            Set<Integer> newTemplateIdSet = new HashSet<>(newIosTemplateIdList);
            Map<Integer, TemplateDto> newTemplateMap = sourceTemplateMap.values().stream()
                    .flatMap(Collection::stream)
                    .filter(templateDto -> newTemplateIdSet.contains(templateDto.getTemplateId())
                            && bannerTemplateSet.contains(templateDto.getTemplateId()))
                    .filter(templateDto -> isValidTemplate(templateDto, topViewScheduleInfo.getHfAdType()))
                    .collect(Collectors.toMap(TemplateDto::getTemplateId, Function.identity(), (p, q) -> p));
            newTemplateDtos = convertToCptTemplateDto(
                    new ArrayList<>(newTemplateMap.values()),
                    topViewScheduleInfo.getPromotionPurposeType(),
                    true,
                    topViewScheduleInfo.getTopViewVideoType());
        }

        return newTemplateDtos;
    }

    private boolean isValidTemplate(TemplateDto templateDto, Integer hfAdType) {
        if (SsaAdType.VIDEO.getCode().equals(hfAdType)) {
            return templateDto.getIsSupportVideo();
        }
        if (BannerShowType.ARCHIVE.getCode().equals(hfAdType)) {
            return templateDto.getIsSupportVideoId();
        }
        return !templateDto.getIsSupportVideo() && templateDto.getIsSupportImage();
    }

    private List<CptTemplateDto> convertToCptTemplateDto(List<TemplateDto> templateDtoList,
                                                         Integer promotionPurposeType,
                                                         boolean isNew,
                                                         int topviewVideoType) {

        return templateDtoList.stream()
                .filter(dto -> dto.getImageNum() <= 1)
                .map(dto -> {
                    CptTemplateDto cptTemplateDto = CptTemplateDto.builder().build();
                    BeanUtils.copyProperties(dto, cptTemplateDto);
                    cptTemplateDto.setButtonCopyDtos(dto.getButtonCopyDtos());
                    cptTemplateDto.setIsNew(isNew);
                    return cptTemplateDto;
                })
                .peek(dto -> {
                    dto.setSchedulePromotionPurposeType(promotionPurposeType);
                    dto.setIsSupportScheme(1);
                    if (topviewVideoType == TopViewVideoEnum.OUT_BOX_VIDEO.getCode()) {
                        dto.setExtraVideoHeight(IpVideoSizeEnum.TOP_VIEW_3D_OUT_BOX_VIDEO.getHeight());
                        dto.setExtraVideoWidth(IpVideoSizeEnum.TOP_VIEW_3D_OUT_BOX_VIDEO.getWidth());
                    }
                })
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class, timeout = 300)
    @Override
    public void createExternal(NewExternalTopViewDto newExternalTopViewDto, Operator operator) {
        LOGGER.info("createExternal newExternalTopViewDto [{}]", newExternalTopViewDto);
        TopViewCreativeValidator.TopViewCreativeValidationResult validationResult = this.topViewCreativeValidator
                .validateNew(newExternalTopViewDto, operator);
        ScheduleDto scheduleDto = validationResult.getSchedule();
        GdTopViewScheduleDto topViewScheduleDto = validationResult.getTopViewSchedule();
        // 锁定 账号/订单/排期
        List<RLock> allLock = new ArrayList<>();
        allLock.add(ssaLock.getLock(scheduleDto.getAccountId(), SsaConstants.OPERATOR_LOCK_SUFFIX));
        allLock.add(ssaLock.getLock(scheduleDto.getScheduleId(), CptConstants.GD_SCHEDULE_LOCK_SUFFIX));
        try {
            Integer ssaCreativeId = ssaSplashScreenServiceDelegate.saveExternalSsa(
                    toSsaNewExternalSplashScreenDto(newExternalTopViewDto, topViewScheduleDto, scheduleDto),
                    operator);

            Map<Integer, Long> newPlatformCreativeMap = new HashMap<>();
            if (newExternalTopViewDto.getNewHfTemplateId() != null && newExternalTopViewDto.getNewHfTemplateId() != 0) {
                newPlatformCreativeMap = brandCptCreativeDelegate.createForTopView(
                        topViewConvert.toNewCptCreativeDto(newExternalTopViewDto, scheduleDto, validationResult.getHfPlatformAppPackageMap()),
                        topViewScheduleDto,
                        operator,
                        true);
            }
            // 保存topView信息
            GdTopViewPo topViewPo = GdTopViewPo.builder()
                    .accountId(scheduleDto.getAccountId())
                    .gdOrderId(scheduleDto.getOrderId())
                    .gdScheduleId(scheduleDto.getScheduleId())
                    .ssaCreativeId(ssaCreativeId.longValue())
                    .newHfIosCreativeId(newPlatformCreativeMap.getOrDefault(PlatformType.IPHONE.getCode(), 0L))
                    .newHfAndroidCreativeId(newPlatformCreativeMap.getOrDefault(PlatformType.ANDROID.getCode(), 0L))
                    .creativeName(newExternalTopViewDto.getCreativeName())
                    .status(TopViewCreativeStatus.TO_BE_AUDIT.getCode())
                    .salesType(scheduleDto.getSalesType())
                    .version(1)
                    .isCustomizedHfJump(newExternalTopViewDto.getHfJump().getIsCustomizedHfJump())
                    .build();
            gdTopViewDao.insertSelective(topViewPo);
            // 回写topViewId到排期
            scheduleService.writeBackTopViewIdToSchedule(scheduleDto.getScheduleId(), topViewPo.getId(),
                    topViewScheduleDto.getSellingType());
            // 回写topViewId到创意
            ssaSplashScreenServiceDelegate.writeBackTopViewIdAndStatusToCreative(ssaCreativeId, topViewPo.getId(),
                    topViewScheduleDto.getSellingType(), SsaSplashScreenStatus.TO_BE_RE_AUDIT.getCode());

            //新版移动首焦16：9
            brandCptCreativeDelegate.writeBackTopViewIdToCreative(new ArrayList<>(newPlatformCreativeMap.values()),
                    topViewPo.getId(), null);

            //拓展信息
//            saveCreativeExt(topViewPo.getId(), newExternalTopViewDto.getProductLabel(), scheduleDto);
            //小程序
//            saveMiniProgram(topViewPo.getId(), newExternalTopViewDto.getMiniProgram(), scheduleDto);

            logForCreate(newExternalTopViewDto, topViewPo.getId(), scheduleDto, operator);
        } catch (ServiceException e) {
            log.warn("创建TopView创意时发生异常:{}", ExceptionUtils.getStackTrace(e));
            throw new IllegalArgumentException(e.getMessage());
        } finally {
            ssaLock.releaseBatchLock(allLock);
        }
    }

    private SsaNewExternalSplashScreenDto toSsaNewExternalSplashScreenDto(NewExternalTopViewDto newExternalTopViewDto,
                                                                          GdTopViewScheduleDto topViewScheduleDto,
                                                                          ScheduleDto scheduleDto) {

        List<com.bilibili.enums.PlatformType> scheduleOsTarget = this.queryScheduleService
                .getScheduleOsTarget(scheduleDto.getScheduleId(), false);
        List<SsaNewSplashScreenVersionControlDto> controlDtos = newExternalTopViewDto.getSsaNewSplashScreenVersionControlDtos();

        // null或者true使用默认
        if (BooleanUtils.isNotFalse(newExternalTopViewDto.getUseDefaultVersion())) {
            SplashScreenDynamicButtonBO splashScreenDynamicButtonBO = newExternalTopViewDto.getButtonBOS().get(0);
            controlDtos = topViewConvert.parseSsaVersionControl(
                    scheduleOsTarget
                            .stream()
                            .map(com.bilibili.enums.PlatformType::getCode)
                            .collect(Collectors.toList()),
                    topViewScheduleDto.getHfAdType(),
                    scheduleDto.getSsaVideoPlayMode(),
                    scheduleDto.getButtonStyle(),
                    splashScreenDynamicButtonBO.getGuideMaterialType(),
                    newExternalTopViewDto.getIsCustomizedNewHfBrandInfo(),
                    topViewScheduleDto.getSsaAdType(),
                    newExternalTopViewDto.getIsEnableLiveBooking(),
                    topViewScheduleDto.getTransitionMode());
        }

        SsaNewExternalSplashScreenDto dto = SsaNewExternalSplashScreenDto.builder()
                .copywriting(newExternalTopViewDto.getSsaCopyWriting())
                .isSkip(newExternalTopViewDto.getSsaIsSkip())
                .issuedTime(newExternalTopViewDto.getSsaIssuedTime())
                .showStyle(topViewScheduleDto.getSsaShowStyle())
                .title(newExternalTopViewDto.getCreativeName())
                .type(SsaSplashScreenType.OPERATE.getCode())
                .baseImageDtos(newExternalTopViewDto.getSsaBaseImageList())
                .ssaNewSplashScreenVersionControlDtos(controlDtos)
                .encryption(newExternalTopViewDto.getSsaEncryption())
                .ssaNewScheduleSplashScreenMappingDtos(newExternalTopViewDto.getSsaNewScheduleSplashScreenMappingDtos())
                .videoDto(newExternalTopViewDto.getSsaVideo())
                .eggVideoDto(newExternalTopViewDto.getEggVideo())
                .ssaCustomizedDTOS(newExternalTopViewDto.getSsaCustomizedDTOS())
                .splashScreenJumpDTOS(newExternalTopViewDto.getSplashScreenJumpDTOS())
                .clickArea(topViewScheduleDto.getClickArea())
                .buttonBOS(newExternalTopViewDto.getButtonBOS())
                .orderProduct(scheduleDto.getOrderProduct())
                .interactInstructions(newExternalTopViewDto.getInteractInstructions())
                .middlePage(newExternalTopViewDto.getMiddlePage())
                .build();
        BeanUtils.copyProperties(newExternalTopViewDto, dto);
        if (CollectionUtils.isEmpty(newExternalTopViewDto.getAppPackageIds())) {
            //兼容存量，运行一段时间后，可删除
            dto.setAppPackageIds(Lists.newArrayList(topViewScheduleDto.getIosAppPackageId(),
                    topViewScheduleDto.getAndroidAppPackageId()));
        } else {
            dto.setAppPackageIds(newExternalTopViewDto.getAppPackageIds());
        }
        return dto;
    }

    public SsaUpdateSplashScreenDto toSsaUpdateSplashScreenDto(UpdateExternalTopViewDto updateExternalTopViewDto,
                                                               Integer gdOrderId,
                                                               GdTopViewScheduleDto topViewScheduleDto,
                                                               Integer ssaCreativeId,
                                                               ScheduleDto scheduleDto) {
        List<SsaNewScheduleSplashScreenMappingDto> mappingDtos = updateExternalTopViewDto
                .getSsaNewScheduleSplashScreenMappingDtos()
                .stream()
                .peek(t -> {
                    t.setSplashScreenId(ssaCreativeId);
                    t.setGdScheduleId(topViewScheduleDto.getGdScheduleId());
                }).collect(Collectors.toList());

        List<com.bilibili.enums.PlatformType> scheduleOsTarget = this.queryScheduleService
                .getScheduleOsTarget(scheduleDto.getScheduleId(), false);

        List<SsaSplashScreenBaseImageDto> ssaBaseImageDtos = updateExternalTopViewDto.getSsaBaseImageDTOList().stream()
                .map(ssaBaseImage -> SsaSplashScreenBaseImageDto.builder()
                        .splashScreenId(ssaCreativeId)
                        .type(ssaBaseImage.getType())
                        .url(ssaBaseImage.getUrl())
                        .MD5(ssaBaseImage.getMd5())
                        .hash(ssaBaseImage.getHash())
                        .build()
                ).collect(Collectors.toList());
        // 前端传了用前端的
        List<SsaSplashScreenVersionControlDto> controlDtos = updateExternalTopViewDto.getSsaVersionControlDtos();
        if (BooleanUtils.isNotFalse(updateExternalTopViewDto.getUseDefaultVersion())) {
            SplashScreenDynamicButtonBO splashScreenDynamicButtonBO = updateExternalTopViewDto.getButtonBOS().get(0);
            List<SsaNewSplashScreenVersionControlDto> versionControlDtos = topViewConvert.parseSsaVersionControl(
                    scheduleOsTarget
                            .stream()
                            .map(com.bilibili.enums.PlatformType::getCode)
                            .collect(Collectors.toList()),
                    topViewScheduleDto.getHfAdType(),
                    scheduleDto.getSsaVideoPlayMode(),
                    scheduleDto.getButtonStyle(),
                    splashScreenDynamicButtonBO.getGuideMaterialType(),
                    updateExternalTopViewDto.getIsCustomizedNewHfBrandInfo(),
                    topViewScheduleDto.getSsaAdType(),
                    updateExternalTopViewDto.getIsEnableLiveBooking(),
                    topViewScheduleDto.getTransitionMode());
            controlDtos = versionControlDtos
                    .stream()
                    .map(versionControl ->
                            SsaSplashScreenVersionControlDto.builder()
                                    .splashScreenId(ssaCreativeId)
                                    .startVersion(versionControl.getStartVersion())
                                    .endVersion(versionControl.getEndVersion())
                                    .platformId(versionControl.getPlatformId())
                                    .build())
                    .collect(Collectors.toList());
        }

        SsaNewSplashScreenVideoDto ssaVideo = updateExternalTopViewDto.getSsaVideo();
        SsaUpdateSplashScreenVideoDto screenVideoDto = SsaUpdateSplashScreenVideoDto.builder()
                .splashScreenId(ssaCreativeId)
                .bizId(ssaVideo.getBizId())
                .uposUrl(ssaVideo.getUposUrl())
                .status(ssaVideo.getStatus())
                .uposAuth(ssaVideo.getUposAuth())
                .fileName(ssaVideo.getFileName())
                .build();
        SsaNewSplashScreenVideoDto eggVideo = updateExternalTopViewDto.getEggVideo();
        SsaUpdateSplashScreenVideoDto eggVideoDto = SsaUpdateSplashScreenVideoDto.builder()
                .splashScreenId(ssaCreativeId)
                .bizId(eggVideo.getBizId())
                .uposUrl(eggVideo.getUposUrl())
                .status(eggVideo.getStatus())
                .uposAuth(eggVideo.getUposAuth())
                .fileName(eggVideo.getFileName())
                .salesType(eggVideo.getSalesType())
                .orderProduct(eggVideo.getOrderProduct())
                .videoType(eggVideo.getVideoType())
                .build();

        SsaUpdateSplashScreenDto update = SsaUpdateSplashScreenDto.builder()
                .id(ssaCreativeId)
                .orderId(gdOrderId)
                .copyWriting(updateExternalTopViewDto.getSsaCopyWriting())
                .isSkip(updateExternalTopViewDto.getSsaIsSkip())
                .issuedTime(updateExternalTopViewDto.getSsaIssuedTime())
                .showStyle(topViewScheduleDto.getSsaShowStyle())
                .title(updateExternalTopViewDto.getCreativeName())
                .ssaBaseImageDtos(ssaBaseImageDtos)
                .ssaVersionControlDtos(controlDtos)
                .encryption(updateExternalTopViewDto.getSsaEncryption())
                .ssaScheduleMappingDtos(mappingDtos)
                .videoDto(screenVideoDto)
                .eggVideoDto(eggVideoDto)
                .splashScreenJumpDTOS(updateExternalTopViewDto.getSplashScreenJumpDTOS())
                .ssaCustomizedDTOS(updateExternalTopViewDto.getSsaCustomizedDTOS())
                .buttonBOS(updateExternalTopViewDto.getButtonBOS())
                .interactInstructions(updateExternalTopViewDto.getInteractInstructions())
                .middlePage(updateExternalTopViewDto.getMiddlePage())
                .build();

        BeanUtils.copyProperties(updateExternalTopViewDto, update);

        if (CollectionUtils.isEmpty(updateExternalTopViewDto.getAppPackageIds())) {
            //兼容存量，运行一段时间后，可删除
            update.setAppPackageIds(Lists.newArrayList(topViewScheduleDto.getIosAppPackageId(),
                    topViewScheduleDto.getAndroidAppPackageId()));
        } else {
            update.setAppPackageIds(updateExternalTopViewDto.getAppPackageIds());
        }
        return update;

    }


    private void logForCreate(NewExternalTopViewDto topViewDto, Integer topViewId, ScheduleDto scheduleDto,
                              Operator operator) {
        DateFormat df = new SimpleDateFormat(LaunchConstant.YYYY_MM_DD);
        TopViewCreativeLogBean logBean = new TopViewCreativeLogBean();
        BeanUtils.copyProperties(topViewDto, logBean);
        logBean.setTopViewId(topViewId);
        logBean.setLaunchDateStringList(scheduleDto.getScheduleDates().stream()
                .map(scheduleDateDto -> df.format(scheduleDateDto.getScheduleDate()))
                .collect(Collectors.toList()));
        logBean.setOrderId(scheduleDto.getOrderId());
        logBean.setScheduleId(scheduleDto.getScheduleId());
        logBean.setScheduleName(scheduleDto.getName());
        logBean.setCmMarkName(SsaCmMark.getByCode(topViewDto.getCmMark()).getDesc());
        ShareState shareState = ShareState.getByCode(topViewDto.getShareState());
        if (shareState == ShareState.CLOSE) {
            logBean.setShareTitle(null);
            logBean.setShareSubTitle(null);
            logBean.setShareImageUrl(null);
        } else {
            logBean.setShareStateDesc(shareState.getName());
        }
        logBean.setSsaIsSkipDesc(IsSkipEnum.getByCode(topViewDto.getSsaIsSkip()).getDesc());
        logBean.setSsaIssuedTimeDesc(SsaIssuedTimeType.getByCode(topViewDto.getSsaIssuedTime()).getDesc());
        logBean.setSsaVideoUrl(topViewDto.getSsaVideo() != null
                ? ssaSplashScreenVideoService.getActualVideoUrl(topViewDto.getSsaVideo().getUposUrl()) : null);
        logBean.setNewHfTitle(topViewDto.getNewHfTitle());
        logBean.setNewHfImageUrl(topViewDto.getNewHfImageUrl());
        logBean.setNewHfVideoUrl(topViewDto.getNewHfVideo() != null
                ? ssaSplashScreenVideoService.getActualVideoUrl(topViewDto.getNewHfVideo().getUposUrl()) : null);
        logBean.setNewHfBrandName(topViewDto.getNewHfBrandName());
        logBean.setNewHfFaceUrl(topViewDto.getNewHfFaceUrl());
        if (!CollectionUtils.isEmpty(topViewDto.getSsaBaseImageList())) {
            logBean.setBaseImageList(topViewDto.getSsaBaseImageList().stream().map(baseImageDto -> {
                TopViewCreativeLogBean.SplashScreenBaseImageLogBean bean =
                        new TopViewCreativeLogBean.SplashScreenBaseImageLogBean();
                bean.setType(baseImageDto.getType());
                bean.setUrl(baseImageDto.getUrl());
                return bean;
            }).collect(Collectors.toList()));
        }
        logBean.setStatusDesc(TopViewCreativeStatus.TO_BE_AUDIT.getDesc());
        gdLogService.insertLog(topViewId, GdLogFlag.TOP_VIEW_CREATIVE, LogOperateType.ADD_CREATIVE,
                operator, logBean);
    }

    @Transactional(rollbackFor = Exception.class, timeout = 300)
    @Override
    public void updateExternal(UpdateExternalTopViewDto updateExternalTopViewDto, Operator operator) {
        log.info("updateExternal topview request={}", JSON.toJSONString(updateExternalTopViewDto));
        TopViewCreativeValidator.TopViewCreativeValidationResult validationResult = this.topViewCreativeValidator
                .validateUpdate(updateExternalTopViewDto, operator);

        ScheduleDto scheduleDto = validationResult.getSchedule();
        GdTopViewPo topViewPo = validationResult.getTopView();
        GdTopViewScheduleDto topViewScheduleDto = validationResult.getTopViewSchedule();
        GdOrderDto orderDto = validationResult.getOrder();

        updateExternalTopViewDto.setScheduleId(scheduleDto.getScheduleId());
        // 锁定 账号/订单/排期/创意
        List<RLock> allLock = new ArrayList<>();
        allLock.add(ssaLock.getLock(topViewPo.getAccountId(), SsaConstants.OPERATOR_LOCK_SUFFIX));
        allLock.add(ssaLock.getLock(orderDto.getOrderId(), SsaConstants.ORDER_LOCK_SUFFIX));
        allLock.add(ssaLock.getLock(topViewPo.getGdScheduleId(), CptConstants.GD_SCHEDULE_LOCK_SUFFIX));
        allLock.add(ssaLock.getLock(topViewPo.getSsaCreativeId(), SsaConstants.SPLASH_SCREEN_LOCK_SUFFIX));
        try {
            ssaSplashScreenServiceDelegate.updateExternalSsa(
                    toSsaUpdateSplashScreenDto(updateExternalTopViewDto,
                            orderDto.getOrderId(), topViewScheduleDto,
                            topViewPo.getSsaCreativeId().intValue(),
                            scheduleDto),
                    operator);

            Map<Integer, Long> newPlatformCreativeMap;
            Assert.notNull(updateExternalTopViewDto, "新版首焦信息不能为空");
            newPlatformCreativeMap = brandCptCreativeDelegate.updateForTopView(
                    topViewConvert.toUpdateCptCreativeDto(updateExternalTopViewDto, scheduleDto, validationResult.getHfPlatformAppPackageMap()),
                    topViewScheduleDto,
                    topViewPo,
                    operator, true);

            Long newHfIosCreativeId = newPlatformCreativeMap.getOrDefault(PlatformType.IPHONE.getCode(), 0L);
            Long newHfAndroidCreativeId = newPlatformCreativeMap.getOrDefault(PlatformType.ANDROID.getCode(), 0L);

            //任何一个有效的首焦创意id
            Long anyValidHfCreativeId = 0L;
            List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
            if (Utils.isPositive(newHfIosCreativeId)) {
                hfCreativeIdList.add(newHfIosCreativeId);
                anyValidHfCreativeId = newHfIosCreativeId;
            }
            if (Utils.isPositive(newHfAndroidCreativeId)) {
                hfCreativeIdList.add(newHfAndroidCreativeId);
                anyValidHfCreativeId = newHfAndroidCreativeId;
            }

            SsaSplashScreenPo ssaCreativePo = ssaSplashScreenServiceDelegate.getSsaSplashScreenPoById(
                    topViewPo.getSsaCreativeId().intValue());
            GdCreativeDto gdCreativeDto = gdCreativeService.getGdCreativeDtoById(anyValidHfCreativeId);

            TopViewCreativeStatus newStatus = TopViewCreativeStatus.getFromSsaCreativeStatusAndHfAuditStatus(
                    ssaCreativePo.getStatus(), gdCreativeDto.getAuditStatus());
            GdTopViewPo updateTopViewPo = GdTopViewPo.builder()
                    .newHfIosCreativeId(newHfIosCreativeId)
                    .newHfAndroidCreativeId(newHfAndroidCreativeId)
                    .creativeName(updateExternalTopViewDto.getCreativeName())
                    .status(newStatus.getCode())
                    .version(topViewPo.getVersion() + 1)
                    .isCustomizedHfJump(updateExternalTopViewDto.getHfJump().getIsCustomizedHfJump())
                    .build();
            GdTopViewPoExample example = new GdTopViewPoExample();
            example.or().andIdEqualTo(topViewPo.getId())
                    .andVersionEqualTo(topViewPo.getVersion());
            gdTopViewDao.updateByExampleSelective(updateTopViewPo, example);
            // 回写闪屏topViewId
            ssaScheduleServiceDelegate.writeBackTopViewIdToSchedule(topViewPo.getGdScheduleId(), topViewPo.getId(),
                    topViewScheduleDto.getSellingType());
            // 更新各自创意状态
            if (newStatus == TopViewCreativeStatus.TO_BE_AUDIT) {
                ssaSplashScreenServiceDelegate.writeBackTopViewIdAndStatusToCreative(ssaCreativePo.getId(),
                        topViewPo.getId(), topViewScheduleDto.getSellingType(), SsaSplashScreenStatus.TO_BE_RE_AUDIT.getCode());
                brandCptCreativeDelegate.writeBackTopViewIdToCreative(hfCreativeIdList, topViewPo.getId(), AuditStatus.INIT.getCode());
            } else if (newStatus == TopViewCreativeStatus.AUDIT_REJECT) {
                ssaSplashScreenServiceDelegate.writeBackTopViewIdAndStatusToCreative(ssaCreativePo.getId(),
                        topViewPo.getId(), topViewScheduleDto.getSellingType(), SsaSplashScreenStatus.AUDIT_REJECT.getCode());
                brandCptCreativeDelegate.writeBackTopViewIdToCreative(hfCreativeIdList, topViewPo.getId(), AuditStatus.REJECT.getCode());
            }

            //拓展信息
//            saveCreativeExt(topViewPo.getId(), updateExternalTopViewDto.getProductLabel(), scheduleDto);
            //小程序
//            saveMiniProgram(topViewPo.getId(), updateExternalTopViewDto.getMiniProgram(), scheduleDto);

            logForUpdate(updateExternalTopViewDto, scheduleDto, orderDto, newStatus, operator);
        } catch (Exception e) {
            e.printStackTrace();
            throw new IllegalArgumentException(e.getMessage());
        } finally {
            ssaLock.releaseBatchLock(allLock);
        }
    }

    private void logForUpdate(UpdateExternalTopViewDto topViewDto, ScheduleDto scheduleDto, GdOrderDto orderDto,
                              TopViewCreativeStatus newStatus, Operator operator) {
        DateFormat df = new SimpleDateFormat(LaunchConstant.YYYY_MM_DD);
        TopViewCreativeLogBean logBean = new TopViewCreativeLogBean();
        BeanUtils.copyProperties(topViewDto, logBean);
        logBean.setTopViewId(topViewDto.getTopViewId());
        logBean.setLaunchDateStringList(scheduleDto.getScheduleDates().stream()
                .map(scheduleDateDto -> df.format(scheduleDateDto.getScheduleDate()))
                .collect(Collectors.toList()));
        logBean.setOrderId(scheduleDto.getOrderId());
        logBean.setOrderName(orderDto.getOrderName());
        logBean.setScheduleId(scheduleDto.getScheduleId());
        logBean.setScheduleName(scheduleDto.getName());
        logBean.setCmMarkName(SsaCmMark.getByCode(topViewDto.getCmMark()).getDesc());
        logBean.setJumpTypeDesc(GdJumpType.getByCode(topViewDto.getCptJumpDTOS().get(0)
                .getJumpType()).getDesc());
        ShareState shareState = ShareState.getByCode(topViewDto.getShareState());
        if (shareState == ShareState.CLOSE) {
            logBean.setShareTitle(null);
            logBean.setShareSubTitle(null);
            logBean.setShareImageUrl(null);
        } else {
            logBean.setShareStateDesc(shareState.getName());
        }
        logBean.setSsaIsSkipDesc(IsSkipEnum.getByCode(topViewDto.getSsaIsSkip()).getDesc());
        logBean.setSsaIssuedTimeDesc(SsaIssuedTimeType.getByCode(topViewDto.getSsaIssuedTime()).getDesc());
        logBean.setSsaVideoUrl(topViewDto.getSsaVideo() != null
                ? ssaSplashScreenVideoService.getActualVideoUrl(topViewDto.getSsaVideo().getUposUrl()) : null);
        logBean.setNewHfVideoUrl(topViewDto.getNewHfVideo() != null
                ? ssaSplashScreenVideoService.getActualVideoUrl(topViewDto.getNewHfVideo().getUposUrl()) : null);
        if (!CollectionUtils.isEmpty(topViewDto.getSsaBaseImageDTOList())) {
            logBean.setBaseImageList(topViewDto.getSsaBaseImageDTOList().stream().map(baseImageDto -> {
                TopViewCreativeLogBean.SplashScreenBaseImageLogBean bean =
                        new TopViewCreativeLogBean.SplashScreenBaseImageLogBean();
                bean.setType(baseImageDto.getType());
                bean.setUrl(baseImageDto.getUrl());
                return bean;
            }).collect(Collectors.toList()));
        }
        logBean.setStatusDesc(newStatus.getDesc());
        gdLogService.insertLog(topViewDto.getTopViewId(), GdLogFlag.TOP_VIEW_CREATIVE, LogOperateType.UPDATE_CREATIVE,
                operator, logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByGdScheduleId(Integer scheduleId, Operator operator) {
        Assert.notNull(scheduleId, "排期id不可为空");
        Assert.notNull(operator, "操作人不可为空");

        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andGdScheduleIdEqualTo(scheduleId);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(poList)) {
            delete(poList.get(0), operator);
        }
    }

    private void delete(GdTopViewPo topViewPo, Operator operator) {
        ssaSplashScreenServiceDelegate.deleted(topViewPo.getSsaCreativeId().intValue(), operator);
        try {
            List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
            if (Utils.isPositive(topViewPo.getNewHfIosCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfIosCreativeId());
            }
            if (Utils.isPositive(topViewPo.getNewHfAndroidCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfAndroidCreativeId());
            }
            if (Utils.isPositive(topViewPo.getHfIosCreativeId())) {
                hfCreativeIdList.add(topViewPo.getHfIosCreativeId());
            }
            if (Utils.isPositive(topViewPo.getHfAndroidCreativeId())) {
                hfCreativeIdList.add(topViewPo.getHfAndroidCreativeId());
            }
            if (!CollectionUtils.isEmpty(hfCreativeIdList)) {
                gdCreativeService.delete(hfCreativeIdList, operator.getOperatorId(), operator);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        GdTopViewPo updatePo = GdTopViewPo.builder()
                .id(topViewPo.getId())
                .status(TopViewCreativeStatus.DELETED.getCode())
                .version(topViewPo.getVersion() + 1)
                .build();
        gdTopViewDao.updateByPrimaryKeySelective(updatePo);

        // 移除排期上topViewId
        scheduleService.removeTopViewIdFromSchedule(topViewPo.getGdScheduleId());

        TopViewCreativeLogBean logBean = TopViewCreativeLogBean.builder()
                .topViewId(topViewPo.getId())
                .statusDesc(TopViewCreativeStatus.DELETED.getDesc())
                .build();
        gdLogService.insertLog(topViewPo.getId(), GdLogFlag.TOP_VIEW_CREATIVE, LogOperateType.DELETE_CREATIVE, operator,
                logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByTopViewId(Integer topViewId, Operator operator) {
        Assert.notNull(topViewId, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        delete(getGdTopViewPoById(topViewId), operator);
    }

    private GdTopViewPo getGdTopViewPoById(Integer topViewId) {
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(topViewId);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        Assert.notEmpty(poList, "TopView不存在");
        return poList.get(0);
    }

    private List<GdTopViewPo> getGdTopViewPoByIdList(List<Integer> topViewIdList) {
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdIn(topViewIdList);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        Assert.notEmpty(poList, "TopView不存在");
        return poList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByTopViewIdList(List<Integer> topViewIdList, Operator operator) {
        Assert.notEmpty(topViewIdList, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        for (Integer topViewId : topViewIdList) {
            deleteByTopViewId(topViewId, operator);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void pause(Integer topViewId, Operator operator) {
        Assert.notNull(topViewId, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        GdTopViewPo topViewPo = getGdTopViewPoById(topViewId);
        TopViewCreativeStatus oldStatus = TopViewCreativeStatus.getByCodeWithValidation(topViewPo.getStatus());
        Assert.isTrue(TopViewCreativeStatus.CAN_BE_PAUSE_STATUS.contains(oldStatus.getCode()),
                "状态为" + oldStatus.getDesc() + "的创意不可暂停");

        ssaSplashScreenServiceDelegate.pause(topViewPo.getSsaCreativeId().intValue(), operator);
        try {
//            if (topViewPo.getHfIosCreativeId() != null && topViewPo.getHfIosCreativeId() != 0) {
//                gdCreativeService.stop(Lists.newArrayList(topViewPo.getHfIosCreativeId(), topViewPo.getHfAndroidCreativeId()),
//                        operator.getOperatorId(), operator);
//            }
            List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
            if (Utils.isPositive(topViewPo.getNewHfIosCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfIosCreativeId());
            }
            if (Utils.isPositive(topViewPo.getNewHfAndroidCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfAndroidCreativeId());
            }
            if (!CollectionUtils.isEmpty(hfCreativeIdList)) {
                gdCreativeService.stop(hfCreativeIdList, operator.getOperatorId(), operator);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Integer newStatus = TopViewCreativeStatus.PAUSED.getCode();
        updateTopViewStatus(topViewPo.getId(), topViewPo.getStatus(), newStatus, topViewPo.getVersion(), operator);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void startUp(Integer topViewId, Operator operator) {
        Assert.notNull(topViewId, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        GdTopViewPo topViewPo = getGdTopViewPoById(topViewId);
        TopViewCreativeStatus oldStatus = TopViewCreativeStatus.getByCodeWithValidation(topViewPo.getStatus());
        Assert.isTrue(TopViewCreativeStatus.PAUSED == oldStatus,
                "状态为" + oldStatus.getDesc() + "的创意不可启动");

        Integer ssaCreativeStatus = ssaSplashScreenServiceDelegate.startUp(topViewPo.getSsaCreativeId().intValue(), operator);
        try {
            List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
            if (Utils.isPositive(topViewPo.getNewHfIosCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfIosCreativeId());
            }
            if (Utils.isPositive(topViewPo.getNewHfAndroidCreativeId())) {
                hfCreativeIdList.add(topViewPo.getNewHfAndroidCreativeId());
            }
            if (!CollectionUtils.isEmpty(hfCreativeIdList)) {
                List<Long> failedCreativeIdList = gdCreativeService.start(hfCreativeIdList, operator.getOperatorId(), operator);
                Assert.isTrue(CollectionUtils.isEmpty(failedCreativeIdList),
                        String.format("TopView创意[%d]，关联的首焦创意[%s]，因状态原因不支持启动，请核实后重试",
                                topViewId, failedCreativeIdList.stream()
                                        .map(Objects::toString)
                                        .collect(Collectors.joining(","))));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Integer newStatus = TopViewCreativeStatus.getFromSsaCreativeStatus(ssaCreativeStatus).getCode();
        updateTopViewStatus(topViewPo.getId(), topViewPo.getStatus(), newStatus, topViewPo.getVersion(), operator);
    }

    @Override
    public GdTopViewDto getTopViewInfoById(Integer topViewId) {
        Assert.notNull(topViewId, "TopViewId不可为空");

        GdTopViewPo po = gdTopViewDao.selectByPrimaryKey(topViewId);
        if (po == null
                || po.getIsDeleted() == IsDeleted.DELETED.getCode()) {
            return null;
        }
        return toGdTopViewDto(po);
    }

    private GdTopViewDto toGdTopViewDto(GdTopViewPo gdTopViewPo) {
        GdTopViewDto dto = new GdTopViewDto();
        BeanUtils.copyProperties(gdTopViewPo, dto);
        return dto;
    }

    @Override
    public Map<Integer, List<GdTopViewDto>> getTopViewInfoMapByScheduleIdList(List<Integer> scheduleIdList) {
        Assert.notEmpty(scheduleIdList, "排期id不可为空");

        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(TopViewCreativeStatus.NOT_DELETED_STATUS)
                .andGdScheduleIdIn(scheduleIdList);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyMap();
        }

        return poList.stream().map(this::toGdTopViewDto)
                .collect(Collectors.groupingBy(GdTopViewDto::getGdScheduleId));
    }

    @Override
    public Map<Integer, List<GdTopViewDto>> getTopViewInfoMapPage(QueryTopViewCreativeDto queryDto, Integer page, Integer size) {
        Assert.notNull(queryDto, "查询条件不可为空");
        Assert.notNull(page, "页码不可为空");
        Assert.notNull(size, "每页数量不可为空");

        GdTopViewPoExample example = new GdTopViewPoExample();
        GdTopViewPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (Objects.nonNull(queryDto.getAccountId())) {
            criteria.andAccountIdEqualTo(queryDto.getAccountId());
        }
        if (Objects.nonNull(queryDto.getTopViewId())) {
            criteria.andIdEqualTo(queryDto.getTopViewId());
        }
        if (Objects.nonNull(queryDto.getGdOrderId())) {
            criteria.andGdOrderIdEqualTo(queryDto.getGdOrderId());
        }
        if (Objects.nonNull(queryDto.getGdScheduleId())) {
            criteria.andGdScheduleIdEqualTo(queryDto.getGdScheduleId());
        }
        if (!CollectionUtils.isEmpty(queryDto.getScheduleIds())) {
            criteria.andGdScheduleIdIn(queryDto.getScheduleIds());
        }
        if (StringUtils.hasText(queryDto.getCreativeName())) {
            criteria.andCreativeNameLike("%" + queryDto.getCreativeName() + "%");
        }
        if (!CollectionUtils.isEmpty(queryDto.getStatusList())) {
            criteria.andStatusIn(queryDto.getStatusList());
        }
        if (Objects.nonNull(queryDto.getSalesType())) {
            criteria.andSalesTypeEqualTo(queryDto.getSalesType());
        }
        Page innerPage = Page.valueOf(page, size);
        example.setLimit(innerPage.getLimit());
        example.setOffset(innerPage.getOffset());
        if (StringUtils.hasText(queryDto.getOrderBy())) {
            example.setOrderByClause(queryDto.getOrderBy());
        }
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyMap();
        }

        return poList.stream().map(this::toGdTopViewDto)
                .collect(Collectors.groupingBy(GdTopViewDto::getGdScheduleId));
    }

    @Override
    public PageResult<TopViewCreativeListDto> queryForPage(QueryTopViewCreativeDto queryDto, Integer page, Integer size) {
        // 通过查询排期处理时间筛选
        List<Integer> salesTypes = Optional.ofNullable(queryDto.getSalesType())
                .map(Collections::singletonList)
                .orElse(SsaConstants.TOP_VIEW_SALES_TYPES);
        List<Integer> orderIds = ArrayUtils.asList(queryDto.getGdOrderId());
        if (!CollectionUtils.isEmpty(queryDto.getGdOrderIdList())) {
            orderIds.addAll(queryDto.getGdOrderIdList());
        }
        List<Integer> timeQueryScheduleIdList = new ArrayList<>();
        if (Objects.nonNull(queryDto.getFromTime()) && Objects.nonNull(queryDto.getToTime())) {
            QueryScheduleDto queryScheduleDto = QueryScheduleDto.builder()
                    .accountId(queryDto.getAccountId())
                    .orderIds(orderIds)
                    .statusList(Lists.newArrayList(1, 2))
                    .salesTypes(salesTypes)
                    .startTime(queryDto.getFromTime())
                    .endTime(queryDto.getToTime())
                    .scheduleIds(queryDto.getScheduleIds())
                    .build();
            List<ScheduleDto> scheduleDtoList = queryScheduleService.queryBaseSchedule(queryScheduleDto);
            if (CollectionUtils.isEmpty(scheduleDtoList)) {
                return PageResult.emptyPageResult();
            }
            timeQueryScheduleIdList = scheduleDtoList.stream()
                    .map(ScheduleDto::getScheduleId)
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 查询gd_top_view
        GdTopViewPoExample example = buildExample(queryDto, timeQueryScheduleIdList);
        List<GdTopViewPo> topViewPoList = gdTopViewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(topViewPoList)) {
            return PageResult.emptyPageResult();
        }

        // 查询ssa_splash_screen
        List<Integer> ssaCreativeIdList = topViewPoList.stream()
                .map(GdTopViewPo::getSsaCreativeId)
                .distinct()
                .map(Long::intValue)
                .collect(Collectors.toList());
        QuerySplashScreenParamDto querySplashScreenParamDto = QuerySplashScreenParamDto.builder()
                .splashScreenIdList(ssaCreativeIdList)
                .likeCreatorName(queryDto.getCreatorName())
                .build();
        PageResult<SsaSplashScreenDto> ssaSplashScreenPageResult = ssaSplashScreenServiceDelegate.getSsaSplashScreenPageResult(querySplashScreenParamDto, Page.valueOf(page, size));

        // 查询gd_creative
        Set<Integer> validTopViewIdSet = ssaSplashScreenPageResult.getRecords()
                .stream()
                .map(SsaSplashScreenDto::getTopViewId)
                .collect(Collectors.toSet());
        List<GdTopViewPo> validTopViewPoList = topViewPoList.stream()
                .filter(item -> validTopViewIdSet.contains(item.getId()))
                .collect(Collectors.toList());
        List<Long> hfCreativeIdList = new ArrayList<>(validTopViewPoList.size() * 2);
        for (GdTopViewPo topViewPo : validTopViewPoList) {
            hfCreativeIdList.add(topViewPo.getHfIosCreativeId());
            hfCreativeIdList.add(topViewPo.getHfAndroidCreativeId());
            hfCreativeIdList.add(topViewPo.getNewHfIosCreativeId());
            hfCreativeIdList.add(topViewPo.getNewHfAndroidCreativeId());
        }
        Map<Long, GdCreativeDto> validHfCreativeMap = gdCreativeService.getGdCreativesInCreativeIds(hfCreativeIdList);

        Map<Integer, SsaSplashScreenDto> validSsaCreativeMap = ssaSplashScreenPageResult.getRecords()
                .stream()
                .collect(Collectors.toMap(SsaSplashScreenDto::getId, Function.identity()));

        List<TopViewCreativeListDto> records = validTopViewPoList.stream()
                .map(topViewPo -> TopViewCreativeListDto.builder()
                        .topViewDto(toGdTopViewDto(topViewPo))
                        .ssaCreativeDto(validSsaCreativeMap.get(topViewPo.getSsaCreativeId().intValue()))
                        .hfIosCreativeDto(validHfCreativeMap.get(topViewPo.getHfIosCreativeId()))
                        .hfAndroidCreativeDto(validHfCreativeMap.get(topViewPo.getHfAndroidCreativeId()))
                        .newHfIosCreativeDto(validHfCreativeMap.get(topViewPo.getNewHfIosCreativeId()))
                        .newHfAndroidCreativeDto(validHfCreativeMap.get(topViewPo.getNewHfAndroidCreativeId()))
                        .build())
                .collect(Collectors.toList());
        return PageResult.<TopViewCreativeListDto>builder()
                .records(records)
                .total(ssaSplashScreenPageResult.getTotal())
                .build();
    }

    private GdTopViewPoExample buildExample(QueryTopViewCreativeDto queryDto, List<Integer> timeQueryScheduleIdList) {
        GdTopViewPoExample example = new GdTopViewPoExample();
        GdTopViewPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (queryDto.getAccountId() != null) {
            criteria.andAccountIdEqualTo(queryDto.getAccountId());
        }
        if (queryDto.getTopViewId() != null) {
            criteria.andIdEqualTo(queryDto.getTopViewId());
        }
        if (queryDto.getGdOrderId() != null) {
            criteria.andGdOrderIdEqualTo(queryDto.getGdOrderId());
        }
        if (!CollectionUtils.isEmpty(queryDto.getGdOrderIdList())) {
            criteria.andGdOrderIdIn(queryDto.getGdOrderIdList());
        }
        if (queryDto.getGdScheduleId() != null) {
            criteria.andGdScheduleIdEqualTo(queryDto.getGdScheduleId());
        }
        if (!StringUtils.isEmpty(queryDto.getCreativeName())) {
            criteria.andCreativeNameLike("%" + queryDto.getCreativeName() + "%");
        }
        if (!CollectionUtils.isEmpty(queryDto.getStatusList())) {
            criteria.andStatusIn(queryDto.getStatusList());
        }
        if (queryDto.getSalesType() != null) {
            criteria.andSalesTypeEqualTo(queryDto.getSalesType());
        }
        if (!CollectionUtils.isEmpty(timeQueryScheduleIdList)) {
            criteria.andGdScheduleIdIn(timeQueryScheduleIdList);
        } else {
            ExampleUtils.notEmpty(queryDto.getScheduleIds(), criteria::andGdScheduleIdIn);
        }

        return example;
    }

    @Override
    public TopViewCreativeDetailDto getCreativeDetailById(Integer topViewId) throws ServiceException {
        GdTopViewDto topViewDto = getTopViewInfoById(topViewId);

        SsaSplashScreenDetailDto ssaCreativeDetail = ssaSplashScreenServiceDelegate.getSsaSplashScreenDetailById(
                topViewDto.getSsaCreativeId().intValue());

        Function<Long, GdCreativeDto> getCreative = creativeId -> {
            if (Utils.isPositive(creativeId)) {
                return gdCreativeService.getGdCreativeDtoById(creativeId);
            } else {
                return null;
            }
        };

        GdCreativeDto hfIosCreative = getCreative.apply(topViewDto.getHfIosCreativeId());
        GdCreativeDto hfAndroidCreative = getCreative.apply(topViewDto.getHfAndroidCreativeId());
        GdCreativeDto newHfIosCreative = getCreative.apply(topViewDto.getNewHfIosCreativeId());
        GdCreativeDto newHfAndroidCreative = getCreative.apply(topViewDto.getNewHfAndroidCreativeId());

        //top_view和ssa使用相同的order_product，都来自于排期
//        CreativeMiniProgramDto creativeMiniProgram = this.creativeMiniProgramService.getCreativeMiniProgram(
//                ssaCreativeDetail.getOrderProduct(), topViewId.longValue());
        return TopViewCreativeDetailDto.builder()
                .topViewDto(topViewDto)
                .ssaCreativeDetail(ssaCreativeDetail)
                .hfIosCreativeDto(hfIosCreative)
                .hfAndroidCreativeDto(hfAndroidCreative)
                .newHfIosCreative(newHfIosCreative)
                .newHfAndroidCreative(newHfAndroidCreative)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditPass(List<TopViewAuditDto> auditDtoList, Operator operator) throws ServiceException {
        Assert.notEmpty(auditDtoList, "审核信息不可为空");

        for (TopViewAuditDto auditDto : auditDtoList) {
            try {
                auditPass(auditDto, operator);
            } catch (Exception e) {
                log.error("topView创意审核通过失败, auditDto={}", JSON.toJSONString(auditDto), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditPass(TopViewAuditDto auditDto, Operator operator) throws ServiceException {
        Assert.notNull(auditDto, "审核信息不可为空");
        Assert.notNull(auditDto.getTopViewId(), "TopViewId不可为空");
        Assert.notNull(auditDto.getVersion(), "创意版本号不可为空");

        GdTopViewPo topViewPo = getGdTopViewPoById(auditDto.getTopViewId());
        Assert.isTrue(auditDto.getVersion().equals(topViewPo.getVersion()), "创意已更新, 请刷新重试");
        TopViewCreativeStatus oldStatus = TopViewCreativeStatus.getByCodeWithValidation(topViewPo.getStatus());
        Assert.isTrue(TopViewCreativeStatus.CAN_BE_AUDIT_PASS_STATUS.contains(oldStatus.getCode()),
                "状态为" + oldStatus.getDesc() + "的创意不可被审核");

        // 闪屏
        Integer ssaCreativeStatus = ssaSplashScreenServiceDelegate.reAduitPass(topViewPo.getSsaCreativeId().intValue(), operator);

        // 首焦
        //新版16:9首焦

        if (Utils.isPositive(topViewPo.getNewHfIosCreativeId())) {
            brandCptCreativeDelegate.auditPassForTopView(operator, CptCreativeAuditDto.builder()
                    .gdCreativeId(topViewPo.getNewHfIosCreativeId())
                    .reason(auditDto.getReason())
                    .build());
        }

        if (Utils.isPositive(topViewPo.getNewHfAndroidCreativeId())) {
            brandCptCreativeDelegate.auditPassForTopView(operator, CptCreativeAuditDto.builder()
                    .gdCreativeId(topViewPo.getNewHfAndroidCreativeId())
                    .reason(auditDto.getReason())
                    .build());
        }

        // topView
        updateTopViewStatus(topViewPo.getId(), topViewPo.getStatus(),
                TopViewCreativeStatus.getFromSsaCreativeStatus(ssaCreativeStatus).getCode(), topViewPo.getVersion(), operator);
    }

    private void updateTopViewStatus(Integer topViewId, Integer oldStatus, Integer newStatus, Integer oldVersion,
                                     Operator operator) {
        GdTopViewPo updatePo = GdTopViewPo.builder()
                .status(newStatus)
                .version(oldVersion + 1)
                .build();
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(topViewId)
                .andStatusEqualTo(oldStatus)
                .andVersionEqualTo(oldVersion);
        int result = gdTopViewDao.updateByExampleSelective(updatePo, example);
        Assert.isTrue(result > 0, "创意已更新, 请刷新重试");

        logForUpdateStatus(topViewId, newStatus, operator);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void
    auditReject(List<TopViewAuditDto> auditDtoList, Operator operator) {
        Assert.notEmpty(auditDtoList, "审核信息不可为空");

        for (TopViewAuditDto auditDto : auditDtoList) {
            try {
                auditReject(auditDto, operator);
            } catch (Exception e) {
                log.error("topView创意审核拒绝失败, auditDto={}", JSON.toJSONString(auditDto), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditReject(TopViewAuditDto auditDto, Operator operator) {
        Assert.notNull(auditDto, "审核信息不可为空");
        Assert.notNull(auditDto.getTopViewId(), "TopViewId不可为空");
        Assert.notNull(auditDto.getVersion(), "创意版本号不可为空");
        Assert.hasText(auditDto.getReason(), "理由不可为空");

        GdTopViewPo topViewPo = getGdTopViewPoById(auditDto.getTopViewId());
        Assert.isTrue(auditDto.getVersion().equals(topViewPo.getVersion()), "创意已更新, 请刷新重试");
        TopViewCreativeStatus oldStatus = TopViewCreativeStatus.getByCodeWithValidation(topViewPo.getStatus());
        Assert.isTrue(TopViewCreativeStatus.CAN_BE_AUDIT_REJECT_STATUS.contains(oldStatus.getCode()),
                "状态为" + oldStatus.getDesc() + "的创意不可被审核");

        // 闪屏
        ssaSplashScreenServiceDelegate.aduitReject(topViewPo.getSsaCreativeId().intValue(),
                auditDto.getReason(), operator);

        // 首焦
        //新版16:9首焦
        if (Utils.isPositive(topViewPo.getNewHfIosCreativeId())) {
            brandCptCreativeDelegate.auditRejectForTopView(operator, CptCreativeAuditDto.builder()
                    .gdCreativeId(topViewPo.getNewHfIosCreativeId())
                    .reason(auditDto.getReason())
                    .build());
        }

        if (Utils.isPositive(topViewPo.getNewHfAndroidCreativeId())) {
            brandCptCreativeDelegate.auditRejectForTopView(operator, CptCreativeAuditDto.builder()
                    .gdCreativeId(topViewPo.getNewHfAndroidCreativeId())
                    .reason(auditDto.getReason())
                    .build());
        }

        // topView
        updateTopViewStatus(topViewPo.getId(), topViewPo.getStatus(), TopViewCreativeStatus.AUDIT_REJECT.getCode(),
                topViewPo.getVersion(), operator);
    }

    private void logForUpdateStatus(Integer topViewId, Integer status, Operator operator) {
        TopViewCreativeLogBean logBean = TopViewCreativeLogBean.builder()
                .topViewId(topViewId)
                .statusDesc(TopViewCreativeStatus.getByCodeWithValidation(status).getDesc())
                .build();
        gdLogService.insertLog(topViewId, GdLogFlag.TOP_VIEW_CREATIVE, LogOperateType.UPDATE_CREATIVE_STATUS, operator,
                logBean);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchComplete(List<Integer> topViewIdList, Operator operator) {
        Assert.notEmpty(topViewIdList, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        List<GdTopViewPo> poList = getGdTopViewPoByIdList(topViewIdList);
        poList.stream().filter(po -> TopViewCreativeStatus.CAN_COMPLETED_STATUS.contains(po.getStatus()))
                .forEach(po -> {
                    // 闪屏
                    ssaSplashScreenServiceDelegate.batchUpdateStatus(operator,
                            Lists.newArrayList(po.getSsaCreativeId().intValue()), SsaSplashScreenStatus.COMPLETED.getCode());

                    //新版首焦
                    List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
                    if (Utils.isPositive(po.getNewHfIosCreativeId())) {
                        hfCreativeIdList.add(po.getNewHfIosCreativeId());
                    }
                    if (Utils.isPositive(po.getNewHfAndroidCreativeId())) {
                        hfCreativeIdList.add(po.getNewHfAndroidCreativeId());
                    }
                    if (!CollectionUtils.isEmpty(hfCreativeIdList)) {
                        brandCptCreativeDelegate.batchUpdateStatus(hfCreativeIdList, LaunchStatus.FINISH);
                    }
                    // topView
                    updateTopViewStatus(po.getId(), po.getStatus(), TopViewCreativeStatus.COMPLETED.getCode(),
                            po.getVersion(), operator);
                });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchOnline(List<Integer> topViewIdList, Operator operator) {
        Assert.notEmpty(topViewIdList, "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        List<GdTopViewPo> poList = getGdTopViewPoByIdList(topViewIdList);
        poList.stream().filter(po -> TopViewCreativeStatus.CAN_ONLINE_STATUS.contains(po.getStatus()))
                .forEach(po -> {
                    // 闪屏
                    ssaSplashScreenServiceDelegate.batchUpdateStatus(operator,
                            Lists.newArrayList(po.getSsaCreativeId().intValue()), SsaSplashScreenStatus.ON_LINE.getCode());

                    //新版首焦
                    List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
                    if (Utils.isPositive(po.getNewHfIosCreativeId())) {
                        hfCreativeIdList.add(po.getNewHfIosCreativeId());
                    }
                    if (Utils.isPositive(po.getNewHfAndroidCreativeId())) {
                        hfCreativeIdList.add(po.getNewHfAndroidCreativeId());
                    }
                    if (!CollectionUtils.isEmpty(hfCreativeIdList)) {
                        brandCptCreativeDelegate.batchUpdateStatus(hfCreativeIdList, LaunchStatus.START);
                    }

                    // topView
                    updateTopViewStatus(po.getId(), po.getStatus(), TopViewCreativeStatus.ON_LINE.getCode(),
                            po.getVersion(), operator);
                });
    }

    @Override
    public Map<Integer, List<GdTopViewDto>> getTopViewInfoMapByOrderIdList(List<Integer> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Maps.newHashMap();
        }

        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(TopViewCreativeStatus.NOT_DELETED_STATUS)
                .andGdOrderIdIn(orderIdList);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Maps.newHashMap();
        }
        return poList.stream().map(this::toGdTopViewDto).collect(Collectors.groupingBy(GdTopViewDto::getGdOrderId));
    }

    @Override
    public void batchCreateExternal(List<NewExternalTopViewDto> newExternalTopViewDtos, Operator operator) {
        if (CollectionUtils.isEmpty(newExternalTopViewDtos)) {
            return;
        }
        List<Integer> scheduleIdList = newExternalTopViewDtos.stream()
                .map(NewExternalTopViewDto::getScheduleId)
                .collect(Collectors.toList());
        List<ScheduleDto> scheduleList = this.queryScheduleService.queryBaseSchedule(QueryScheduleDto.builder()
                .scheduleIds(scheduleIdList).build());
        List<GdTopViewScheduleDto> topViewScheduleList = this.queryScheduleService.getTopViewScheduleInfoList(scheduleIdList);

        Integer preOrderId = null;
        ScheduleDto preSchedule = null;
        for (ScheduleDto schedule : scheduleList) {
            if (Objects.isNull(preOrderId)) preOrderId = schedule.getOrderId();
            if (Objects.isNull(preSchedule)) preSchedule = schedule;
            Assert.isTrue(Objects.equals(schedule.getOrderId(), preOrderId), "批量创建创意的排期必须属于同一个订单");
            Assert.isTrue(Objects.equals(ScheduleCompareProperties.SSA_COMPARATOR
                    .compare(preSchedule, schedule), 0), "批量创建创意的排期相关属性必须一致");
            preOrderId = schedule.getOrderId();
            preSchedule = schedule;
            scheduleIdList.remove(schedule.getScheduleId());
        }

        Assert.isTrue(CollectionUtils.isEmpty(scheduleIdList), "存在无效的排期：" + JSONObject.toJSONString(scheduleIdList));

        GdTopViewScheduleDto preTopViewSchedule = null;
        for (GdTopViewScheduleDto topViewSchedule : topViewScheduleList) {
            if (Objects.isNull(preTopViewSchedule)) preTopViewSchedule = topViewSchedule;
            Assert.isTrue(Objects.equals(ScheduleCompareProperties.TOPVIEW_COMPARATOR
                    .compare(preTopViewSchedule, topViewSchedule), 0), "批量创建创意的排期首焦相关属性必须一致");
            preTopViewSchedule = topViewSchedule;
        }

        Map<Integer, ScheduleDto> scheduleMap = scheduleList.stream()
                .collect(Collectors.toMap(ScheduleDto::getScheduleId, Function.identity(), (s, t) -> t));
        Map<Integer, GdTopViewScheduleDto> topViewScheduleMap = topViewScheduleList.stream()
                .collect(Collectors.toMap(GdTopViewScheduleDto::getGdScheduleId, s -> s));

        GdOrderDto order = this.orderService.getOrderById(preOrderId);
        List<NewExternalTopViewDto> dtos = Lists.newArrayList();
        for (NewExternalTopViewDto newExternalTopViewDto : newExternalTopViewDtos) {
            ScheduleDto schedule = scheduleMap.get(newExternalTopViewDto.getScheduleId());
            GdTopViewScheduleDto topViewSchedule = topViewScheduleMap.get(newExternalTopViewDto.getScheduleId());
            if (Objects.isNull(schedule) || Objects.isNull(topViewSchedule)) {
                log.info("[TopViewCreativeService] batchCreateExternal not found,scheduleId={}",
                        newExternalTopViewDto.getScheduleId());
                continue;
            }

            Timestamp beginTime = Objects.isNull(schedule.getGdBeginTime())
                    ? schedule.getBeginDate() : schedule.getGdBeginTime();
            Timestamp endTime = Objects.isNull(schedule.getGdEndTime())
                    ? schedule.getEndDate() : schedule.getGdEndTime();

            newExternalTopViewDto.setHfSchedules(Lists.newArrayList(ScheduleDateDto.builder()
                    .beginDate(beginTime)
                    .endDate(endTime)
                    .build()));
            newExternalTopViewDto.setSsaNewScheduleSplashScreenMappingDtos(
                    Lists.newArrayList(SsaNewScheduleSplashScreenMappingDto.builder()
                            .gdScheduleId(schedule.getScheduleId())
                            .beginTime(beginTime)
                            .endTime(endTime)
                            .build()));
            //防止创意名称重复
            newExternalTopViewDto.setCreativeName(newExternalTopViewDto.getCreativeName() + "-" + schedule.getScheduleId());
            //提前校验闪屏数据
            SsaNewExternalSplashScreenDto newSsaDto = toSsaNewExternalSplashScreenDto(
                    newExternalTopViewDto, topViewSchedule, schedule);
            newSsaDto.setClickArea(schedule.getClickArea());
            newSsaDto.setSalesType(schedule.getSalesType());
            GdCreativeShareDto creativeShareDto = GdCreativeShareDto.builder()
                    .state(newSsaDto.getShareState())
                    .title(newSsaDto.getShareTitle())
                    .subTitle(newSsaDto.getShareSubTitle())
                    .imageUrl(newSsaDto.getShareImageUrl())
                    .imageHash(newSsaDto.getShareImageHash())
                    .build();
            //一定要前置校验（否则异步处理时无法兜底）
            //会被校验两次（createExternal方法调用闪屏方法时会被再次触发）
            this.ssaSplashScreenServiceDelegate.preValidate(newSsaDto, order, schedule, creativeShareDto, operator);
            //校验首焦创意素材信息
            this.creativeValidator.validateNewTopViewCreativeBasicInfo(topViewConvert.toNewCptCreativeDto(newExternalTopViewDto, schedule, null));
            dtos.add(newExternalTopViewDto);
        }

        ITopViewCreativeService currentProxy = (ITopViewCreativeService) AopContext.currentProxy();
        cachedExecutor.execute(() -> {
            LOGGER.info("[TopViewCreativeService] batchCreateExternal start");
            for (NewExternalTopViewDto newExternalTopViewDto : dtos) {
                int retryTimes = 0;
                //为了防止首焦视频转码中导致创建失败，因此选择重试一定次数，等待首焦视频转码成功
                while (retryTimes++ < 10) {
                    try {
                        //1、createExternal会加锁，因此没法批量并行，只能将当前批量操作交付给一个线程完成
                        //currentProxy让createExternal上的事务生效

                        //2、createExternal可能会抛出异常，但是在整个创建过程中，某些属性可能会被更改，比如闪屏的跳转链接以及button上的seq等属性，
                        //这些被改变的信息会反应到下次重试的过程中，导致数据错乱，因此每次都深copy一个
                        JSON jsonInfo = (JSON) JSON.toJSON(newExternalTopViewDto);
                        NewExternalTopViewDto newDto = JSON.toJavaObject(jsonInfo, NewExternalTopViewDto.class);
                        currentProxy.createExternal(newDto, operator);
                        break;
                    } catch (Exception e) {
                        LOGGER.error("[TopViewCreativeService] batchCreateExternal createExternal error,scheduleId={}",
                                newExternalTopViewDto.getScheduleId(), e);
                        //ignore
                        LockSupport.parkNanos(TimeUnit.SECONDS.toNanos(retryTimes * 4L));
                    }
                }
            }
            LOGGER.info("[TopViewCreativeService] batchCreateExternal end");
        });
    }

    @Override
    public Map<Integer, Integer> getDefaultPlatformVersion(Integer scheduleId, Integer guideMaterialType, Integer isCustomizedNewHfBrandInfo, Integer ssaAdType, Boolean isEnableLiveBooking) {
        ScheduleDto scheduleDto = queryScheduleService.getScheduleBaseInfoById(scheduleId);
        //根据设备定向转换平台类型
        List<com.bilibili.enums.PlatformType> allAvailablePlatforms = queryScheduleService.getScheduleOsTarget(scheduleId, false);
        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(scheduleId);
        allAvailablePlatforms.sort(Comparator.comparing(com.bilibili.enums.PlatformType::getCode));
        return topViewConvert.getDefaultVersionPlatformMap(topViewScheduleInfo.getHfAdType(), scheduleDto.getSsaVideoPlayMode(), scheduleDto.getButtonStyle(), guideMaterialType, isCustomizedNewHfBrandInfo, ssaAdType, isEnableLiveBooking);
    }

    //拓展信息，目前只有产品型号
    //已废弃，因为闪屏部分一定落，由于order_product相同，如果落topview_id，那么将来会出现topview_id和之前某个topview的splash_screen_id重复，
    //导致主键冲突，隐形挖坑了，因此就不单独落了，而且目前还没场景直接用到topview信息，都是直接用的闪屏部分或者首焦部分。
    @Deprecated
    private void saveCreativeExt(Integer topViewId, ProductLabelDto productLabel, ScheduleDto schedule) {
        this.creativeExtService.saveCreativeExt(CreativeExtHolderDto.builder()
                .orderId(schedule.getOrderId())
                .scheduleId(schedule.getScheduleId())
                .creativeId(topViewId.longValue())
                .orderProduct(schedule.getOrderProduct())
                .productLabel(productLabel)
                .build());
    }

    //小程序
    //已废弃，因为闪屏部分一定落，由于order_product相同，如果落topview_id，那么将来会出现topview_id和之前某个topview的splash_screen_id重复，
    //导致数据重复，隐形挖坑了，因此就不单独落了，而且目前还没场景直接用到topview信息，都是直接用的闪屏部分或者首焦部分。
//    @Deprecated
//    private void saveMiniProgram(Integer topViewId, MiniProgramDto miniProgram, ScheduleDto schedule) {
//        WakeAppType wakeAppType = WakeAppType.getByCode(schedule.getWakeAppType());
//        if (Objects.equals(wakeAppType, WakeAppType.MINI_PROGRAM)) {
//            this.creativeMiniProgramService.saveCreativeMiniProgram(CreativeMiniProgramDto.builder()
//                    .creativeId(topViewId.longValue())
//                    .orderProduct(schedule.getOrderProduct())
//                    .miniProgram(miniProgram)
//                    .build());
//        }
//    }

    @Override
    public List<GdTopViewDto> querySimple(Integer fromId, Integer size, Timestamp fromTime) {
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.createCriteria()
                .andIdGreaterThan(fromId)
                .andMtimeGreaterThanOrEqualTo(fromTime);
        example.setOrderByClause("id asc");
        example.setLimit(size);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        return poList.stream()
                .map(this::toGdTopViewDto)
                .collect(Collectors.toList());
    }
}
