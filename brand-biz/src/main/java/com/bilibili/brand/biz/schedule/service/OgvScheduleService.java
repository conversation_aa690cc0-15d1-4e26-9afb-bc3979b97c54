package com.bilibili.brand.biz.schedule.service;

import com.alibaba.fastjson2.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.SynCrmService;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.ogv.IResOgvService;
import com.bilibili.brand.api.resource.ogv.OgvEpisodeDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceQueryDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceStickerDto;
import com.bilibili.brand.api.resource.ogv.OgvSeasonDto;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.OgvCptScheduleCreateDto;
import com.bilibili.brand.api.schedule.dto.OgvCptScheduleUpdateDto;
import com.bilibili.brand.api.schedule.dto.OgvGdScheduleCreateDto;
import com.bilibili.brand.api.schedule.dto.OgvGdScheduleUpdateDto;
import com.bilibili.brand.api.schedule.dto.OgvScheduleCreateDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleOgvDto;
import com.bilibili.brand.api.schedule.dto.ScheduleTargetDto;
import com.bilibili.brand.api.schedule.dto.SplitDaysImpressDto;
import com.bilibili.brand.api.schedule.service.IOgvScheduleService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.bean.RedisKey;
import com.bilibili.brand.biz.cycle.CycleServiceFacade;
import com.bilibili.brand.biz.cycle.GdCycleService;
import com.bilibili.brand.biz.cycle.OgvPriceService;
import com.bilibili.brand.biz.cycle.dto.CycleDto;
import com.bilibili.brand.biz.cycle.dto.CycleQueryDto;
import com.bilibili.brand.biz.cycle.dto.OgvPriceDto;
import com.bilibili.brand.biz.cycle.dto.OgvPriceQueryDto;
import com.bilibili.brand.biz.helper.OgvHelper;
import com.bilibili.brand.biz.log.bean.ScheduleLogBean;
import com.bilibili.brand.biz.order.service.syn_crm.CptSynCrmService;
import com.bilibili.brand.biz.order.service.syn_crm.GdSynCrmService;
import com.bilibili.brand.biz.rpc.grpc.client.OttGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.PgcGrpcClient;
import com.bilibili.brand.biz.schedule.dao.GdFlowAllocationDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleOgvDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleOgvDaoExtension;
import com.bilibili.brand.biz.schedule.handler.ScheduleDateHandler;
import com.bilibili.brand.biz.schedule.handler.ScheduleTargetHandler;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleOgvPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleOgvPoExample;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePoExample;
import com.bilibili.brand.common.Constant;
import com.bilibili.brand.dto.cycle.PriceRaiseDto;
import com.bilibili.brand.exception.ConflictException;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.biz.service.schedule.CptScheduleDelegate;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.GdType;
import com.bilibili.cpt.platform.common.LogOperateType;
import com.bilibili.cpt.platform.common.ResourceType;
import com.bilibili.cpt.platform.common.ScheduleStyle;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.enums.*;
import com.bilibili.func.ThFunction;
import com.bilibili.enums.OgvEpisodeStatusEnum;
import com.bilibili.enums.OgvLaunchTypeEnum;
import com.bilibili.enums.OgvResourceStatusEnum;
import com.bilibili.enums.OgvResourceStickerEnum;
import com.bilibili.enums.OgvSeasonTypeEnum;
import com.bilibili.enums.OttMediaStatusEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import com.bilibili.ssa.platform.common.enums.SsaSourceType;
import com.bilibili.utils.DateUtil;
import com.bilibili.utils.TimeUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.function.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/13 10:44
 */
@Slf4j
@Service
public class OgvScheduleService implements IOgvScheduleService {
    @Autowired
    private GdScheduleDao scheduleDao;
    @Autowired
    private GdScheduleOgvDao scheduleOgvDao;
    @Autowired
    private GdScheduleOgvDaoExtension scheduleOgvDaoExtension;
    @Autowired
    private IGdOrderService orderService;
    @Autowired
    private IBusinessSideService businessSideService;
    @Autowired
    private GdFlowAllocationDao flowAllocationDao;
    @Autowired
    private PgcGrpcClient pgcGrpcClient;
    @Autowired
    private OttGrpcClient ottGrpcClient;
    @Autowired
    private IResOgvService resOgvService;
    @Autowired
    private ScheduleDateHandler scheduleDateHandler;
    @Autowired
    private ScheduleTargetHandler scheduleTargetHandler;
    @Autowired
    private IGdLogService logService;
    @Autowired
    private RedissonClient redissonClient;
    @Resource(type = CptSynCrmService.class)
    private SynCrmService cptSynCrmService;
    @Resource(type = GdSynCrmService.class)
    private SynCrmService gdSynCrmService;
    @Autowired
    private IQueryScheduleService queryScheduleService;
    @Autowired
    private IQueryTemplateService queryTemplateService;
    @Autowired
    private OgvPriceService ogvPriceService;
    @Autowired
    private GdCycleService cycleService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private OgvHelper ogvHelper;
    @Autowired
    private CycleServiceFacade cycleServiceFacade;
    @Autowired
    private CptScheduleDelegate cptScheduleDelegate;

    private static final Integer MAX_SEASON_COUNT = 200;

    @Transactional
    @Override
    public Integer createCptSchedule(OgvCptScheduleCreateDto dto, Operator operator) throws Exception {
        Assert.notEmpty(dto.getSeasonIds(), "season信息不能为空");
        log.info("[OgvScheduleService] createCptSchedule request={},operator={}",
                JSON.toJSONString(dto), JSON.toJSONString(operator));
        List<RLock> lockers = Lists.newArrayListWithCapacity(dto.getSeasonIds().size());
        try {
            this.lock(lockers, dto.getSeasonIds());
            ValidationResult validationResult = this.validateCpt(dto, operator);
            GdSchedulePo schedulePo = this.buildGdSchedulePo(dto, validationResult);
            this.scheduleDao.insertSelective(schedulePo);
            Integer scheduleId = schedulePo.getScheduleId();
            this.insertScheduleOgv(scheduleId, OgvLaunchTypeEnum.getByCode(dto.getOgvLaunchType()),
                    validationResult.getSeasonEpisodeMap());
            this.scheduleDateHandler.insertUpdateGdScheduleDate(scheduleId, validationResult.getDaysImpress());
            this.scheduleTargetHandler.addTarget(dto.getTargets(), scheduleId);
            this.cptSynCrmService.synCrm(validationResult.getOrder());
            logService.insertLog(scheduleId, GdLogFlag.SCHEDULE, LogOperateType.ADD_SCHEDULE, operator,
                    ScheduleLogBean.builder().id(scheduleId).name(schedulePo.getName()).build());
            return scheduleId;
        } catch (Exception e) {
            log.error("[OgvScheduleService] createCptSchedule error", e);
            throw e;
        } finally {
            this.unlock(lockers);
        }
    }

    @Transactional
    @Override
    public void updateCptSchedule(OgvCptScheduleUpdateDto dto, Operator operator) {
        Assert.notNull(dto.getScheduleId(), "排期id不能空");
        log.info("[OgvScheduleService] updateCptSchedule request={},operator={}",
                JSON.toJSONString(dto), JSON.toJSONString(operator));
        ScheduleDto scheduleDto = queryScheduleService.getScheduleBaseInfoById(dto.getScheduleId());
        GdOrderDto order = this.orderService.getOrderById(scheduleDto.getOrderId(), operator);
        //目前支持修改名字
        if (scheduleDto.getName().equals(dto.getName())) {
            return;
        }
        GdSchedulePo schedulePo = GdSchedulePo.builder()
                .scheduleId(scheduleDto.getScheduleId())
                .name(dto.getName())
                .build();
        this.scheduleDao.updateByPrimaryKeySelective(schedulePo);
        this.cptSynCrmService.synCrm(order);
        logService.insertLog(schedulePo.getScheduleId(), GdLogFlag.SCHEDULE, LogOperateType.UPDATE_SCHEDULE, operator,
                ScheduleLogBean.builder().id(schedulePo.getScheduleId()).name(schedulePo.getName()).build());
    }


    @Override
    public Integer createGdSchedule(OgvGdScheduleCreateDto dto, Operator operator) {
        log.info("[OgvScheduleService] createGdSchedule request={},operator={}",
                JSON.toJSONString(dto), JSON.toJSONString(operator));
        ValidationResult validationResult = this.validateGd(dto, operator);
        GdSchedulePo schedulePo = this.buildGdSchedulePo(dto, validationResult);
        this.scheduleDao.insertSelective(schedulePo);
        Integer scheduleId = schedulePo.getScheduleId();
        this.insertScheduleOgv(scheduleId, OgvLaunchTypeEnum.getByCode(dto.getOgvLaunchType()),
                validationResult.getSeasonEpisodeMap());
        this.scheduleDateHandler.insertUpdateGdScheduleDate(scheduleId, validationResult.getDaysImpress());
        this.scheduleTargetHandler.addTarget(dto.getTargets(), scheduleId);
        this.cptScheduleDelegate.insertUpdateCrowdPack(dto.getCrowdPackIds(), dto.getExcludeCrowdPackIds(), scheduleId);
//        this.saveFlowAllocation(schedulePo, validationResult.getSourceMap().keySet());
        this.gdSynCrmService.synCrm(validationResult.getOrder());
        logService.insertLog(scheduleId, GdLogFlag.SCHEDULE, LogOperateType.ADD_SCHEDULE, operator,
                ScheduleLogBean.builder().id(scheduleId).name(schedulePo.getName()).build());
        return scheduleId;
    }

    @Override
    public void updateGdSchedule(OgvGdScheduleUpdateDto dto, Operator operator) {
        log.info("[OgvScheduleService] updateGdSchedule request={},operator={}",
                JSON.toJSONString(dto), JSON.toJSONString(operator));
        Assert.notNull(dto.getScheduleId(), "排期id不能空");
        ScheduleDto scheduleDto = queryScheduleService.getScheduleBaseInfoById(dto.getScheduleId());
        GdOrderDto order = this.orderService.getOrderById(scheduleDto.getOrderId(), operator);
        //目前支持修改名字
        if (scheduleDto.getName().equals(dto.getName())) {
            return;
        }
        GdSchedulePo schedulePo = GdSchedulePo.builder()
                .scheduleId(scheduleDto.getScheduleId())
                .name(dto.getName())
                .build();
        this.scheduleDao.updateByPrimaryKeySelective(schedulePo);
        this.cptSynCrmService.synCrm(order);
        logService.insertLog(schedulePo.getScheduleId(), GdLogFlag.SCHEDULE, LogOperateType.UPDATE_SCHEDULE, operator,
                ScheduleLogBean.builder()
                        .id(schedulePo.getScheduleId())
                        .name(schedulePo.getName())
                        .build());
    }

    public void deleteScheduleOgv(Integer scheduleId) {
        log.info("[OgvScheduleService] deleteOgv scheduleId={}", scheduleId);
        Assert.notNull(scheduleId, "排期id错误");
        GdScheduleOgvPoExample example = new GdScheduleOgvPoExample();
        example.createCriteria()
                .andScheduleIdEqualTo(scheduleId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdScheduleOgvPo> scheduleOgvPos = this.scheduleOgvDao.selectByExample(example);
        if (CollectionUtils.isNotEmpty(scheduleOgvPos)) {
            example = new GdScheduleOgvPoExample();
            example.createCriteria()
                    .andIdIn(scheduleOgvPos.stream()
                            .map(GdScheduleOgvPo::getId)
                            .collect(Collectors.toList()));
            this.scheduleOgvDao.updateByExampleSelective(GdScheduleOgvPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), example);
        }
    }

    @Override
    public ScheduleOgvDto getScheduleOgv(Integer scheduleId) {
        return getScheduleOgv(Lists.newArrayList(scheduleId)).get(scheduleId);
    }

    @Override
    public Map<Integer, ScheduleOgvDto> getScheduleOgv(List<Integer> scheduleIdList) {
        Map<Integer, List<GdScheduleOgvPo>> scheduleOgvMap = this.selectScheduleOgvByScheduleId(scheduleIdList);
        Map<Integer, List<Integer>> schedulePlatformMap = this.getScheduleOsTarget(scheduleIdList);
        Function<Integer, Boolean> ottChecker = scheduleId -> {
            List<Integer> platformIdList = schedulePlatformMap.get(scheduleId);
            return Objects.nonNull(platformIdList) && platformIdList.contains(PlatformType.OTT.getCode());
        };

        //查询实际是OGV的排期
        Map<Integer, GdSchedulePo> scheduleMap = this.selectScheduleByScheduleId(Lists.newLinkedList(scheduleOgvMap.keySet()));
        Map<Integer, ScheduleOgvDto> result = Maps.newHashMap();
        scheduleOgvMap.forEach((scheduleId, scheduleOgvPoList) -> {
            GdSchedulePo schedule = scheduleMap.get(scheduleId);
            if (Objects.isNull(schedule)) return;
            //build seasons
            List<Long> seasonIdList = scheduleOgvPoList.stream()
                    .map(GdScheduleOgvPo::getSeasonId)
                    .distinct()
                    .collect(Collectors.toList());

            List<OgvSeasonDto> seasons = this.resOgvService.getOgvSeasonInResource(OgvResourceQueryDto.builder()
                    .seasonIdList(seasonIdList)
                    .allowDowngrade(true)
                    .appendEpisode(false)//一定为false，如果是指定集投放，下面会单独查询和赋值
                    .build());

            //ott业务补充逻辑：
            //虽然上述getOgvSeasonInResource方法会根据资源可投放平台追加OTT状态，但若一旦资源的可投放平台和排期平台定向不兼容，则会导致
            //错误。
            //比如，原资源可投放平台是iphone+ott，排期是ott，如果后来资源可投放平台修改为为iphone，那么此时getOgvSeasonInResource方法
            //并不会返回ott的状态，导致排期ott状态丢失。
            //其实上述case，在资源管理中会进行兼容性检查（不会出现修改后的可投放平台与排期不兼容，即修改后的可投放平台也一定包含目前所有排期
            //的平台定向），详见ResOgvService，但不确保后期这个逻辑是否会被产品剔除或者修改或者后期维护者不晓得这个逻辑，而导致此处有风险，
            //因此对于丢失的ott状态，在此处单独补充。
            //性能成本：目前基本上ott状态都是"准确"的（注意我此处说的是"准确"二字，即ott状态可有也可无，主要看排期），因此成本主要一个内存中
            //的filter而已
            boolean appendOttStatus = ottChecker.apply(scheduleId);
            Map<Long, OgvSeasonDto> ottAppendSeasonMap = seasons.stream()
                    //过滤出需要ott状态，但是ott状态为null的
                    .filter(season -> appendOttStatus && Objects.isNull(season.getOttStatus()))
                    .collect(Collectors.toMap(OgvSeasonDto::getSeasonId, Function.identity(), (s, t) -> t));
            if (!ottAppendSeasonMap.isEmpty()) {
                Map<Long, OttMediaStatusEnum> ottSeasonStatusMap = this.ottGrpcClient.querySeasonStatus(
                        Lists.newArrayList(ottAppendSeasonMap.keySet()));
                ottAppendSeasonMap.forEach((seasonId, season) -> {
                    OttMediaStatusEnum ottMediaStatus = ottSeasonStatusMap.getOrDefault(seasonId, OttMediaStatusEnum.INVALID);
                    season.setOttStatus(ottMediaStatus.getCode());
                    season.setOttStatusDesc(ottMediaStatus.getDesc());
                });
            }

            OgvLaunchTypeEnum launchType = OgvLaunchTypeEnum.getByCodeWithoutEx(scheduleOgvPoList.get(0).getLaunchType());
            if (Objects.equals(launchType, OgvLaunchTypeEnum.EPISODE)) {
                //指定集投放，则需要返回挂载的集信息
                Map<Long, Set<Long>> seasonEpisodeMap = scheduleOgvPoList.stream().collect(
                        Collectors.groupingBy(GdScheduleOgvPo::getSeasonId,
                                Collectors.mapping(GdScheduleOgvPo::getEpisodeId, Collectors.toSet())));
                Map<Long, OgvEpisodeDto> episodeDtoMap = this.ogvHelper.queryOgvEpisodeByEpisode(scheduleOgvPoList.stream()
                        .map(GdScheduleOgvPo::getEpisodeId)
                        .distinct()
                        .collect(Collectors.toList()), appendOttStatus);

                //append episodes
                seasons = seasons.stream().peek(season -> {
                    //排期实际挂载的episode，而不是当前season的全集
                    Set<Long> sEpisodeIdList = seasonEpisodeMap.get(season.getSeasonId());
                    List<OgvEpisodeDto> episodes = sEpisodeIdList.stream().map(episodeId -> {
                        OgvEpisodeDto episodeDto = episodeDtoMap.get(episodeId);
                        if (Objects.isNull(episodeDto)) {
                            //降级兜底
                            episodeDto = OgvEpisodeDto.builder()
                                    .seasonId(season.getSeasonId())
                                    .episodeId(episodeId)
                                    .title("")
                                    .longTitle("【该episode被降级，当前信息已不具备参考价值】")
                                    .status(OgvEpisodeStatusEnum.UNKNOWN.getCode())
                                    .statusDesc(OgvEpisodeStatusEnum.UNKNOWN.getDesc())
                                    .ottStatus(OttMediaStatusEnum.INVALID.getCode())
                                    .ottStatusDesc(OttMediaStatusEnum.INVALID.getDesc())
                                    .ord(0)
                                    .build();
                        }
                        return episodeDto;
                    }).sorted(Comparator.comparing(OgvEpisodeDto::getOrd)).collect(Collectors.toList());
                    season.setEpisodes(episodes);
                }).collect(Collectors.toList());
            }

            //builder stickers
            List<Integer> sourceIds = Arrays.stream(schedule.getSourceIds().split(","))
                    .map(Integer::valueOf).collect(Collectors.toList());
            //sourceStickerMap是source->sticker的映射，不同source对应的sticker可能是相同的，
            //比如iPhone和Android的前贴1（sourceId分别是5767和5778）
            Map<Integer, OgvResourceStickerDto> sourceStickerMap = this.resOgvService.getStickerBySourceId(sourceIds);
            //按照贴次id将资源位聚合（为什么要聚合一次，因为sourceStickerMap#value中的资源位只包含对应的key本身，如果同贴次且多端，那么就需要聚合到同一个贴次上）
            List<OgvResourceStickerDto> stickers = sourceStickerMap.values()
                    .stream()
                    .collect(Collectors.toMap(OgvResourceStickerDto::getId, Function.identity(), (s, t) -> {
                        //避免影响原数据，因此复制
                        OgvResourceStickerDto mergedSticker = new OgvResourceStickerDto();
                        BeanUtils.copyProperties(t, mergedSticker);
                        mergedSticker.setSources(ImmutableMap.<Integer, Integer>builder()
                                .putAll(s.getSources())
                                .putAll(t.getSources())
                                .build());
                        return mergedSticker;
                    }))
                    .values()
                    .stream()
                    .sorted(Comparator.comparing(OgvResourceStickerDto::getType).thenComparing(OgvResourceStickerDto::getSeq))
                    .collect(Collectors.toList());
            result.put(scheduleId, ScheduleOgvDto.builder()
                    .scheduleId(scheduleId)
                    .ogvLaunchType(launchType.getCode())
                    .seasons(seasons)
                    .stickers(stickers)
                    .build());
        });
        return result;
    }

    @Override
    public Map<Long, List<Integer>> getScheduleBySeason(List<Long> seasonIdList) {
        if (CollectionUtils.isEmpty(seasonIdList)) {
            return Maps.newHashMap();
        }
        GdScheduleOgvPoExample example = new GdScheduleOgvPoExample();
        example.createCriteria()
                .andSeasonIdIn(seasonIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdScheduleOgvPo> pos = this.scheduleOgvDao.selectByExample(example);
        return pos.stream().collect(
                Collectors.groupingBy(GdScheduleOgvPo::getSeasonId,
                        Collectors.mapping(GdScheduleOgvPo::getScheduleId,
                                Collectors.collectingAndThen(Collectors.toSet(), Lists::newArrayList))));
    }

    @Override
    public Map<Long, List<Integer>> getValidScheduleBySeason(List<Long> seasonIdList) {
        if (CollectionUtils.isEmpty(seasonIdList)) {
            return Maps.newHashMap();
        }
        List<GdScheduleOgvPo> pos = this.scheduleOgvDaoExtension.selectValidScheduleOgvBySeason(
                seasonIdList, new Timestamp(System.currentTimeMillis()));
        return pos.stream().collect(
                Collectors.groupingBy(GdScheduleOgvPo::getSeasonId,
                        Collectors.mapping(GdScheduleOgvPo::getScheduleId,
                                Collectors.collectingAndThen(Collectors.toSet(), Lists::newArrayList))));
    }

    @Override
    public Map<Long, List<Integer>> getScheduleByEpisode(List<Long> episodeIdList) {
        if (CollectionUtils.isEmpty(episodeIdList)) {
            return Maps.newHashMap();
        }
        GdScheduleOgvPoExample example = new GdScheduleOgvPoExample();
        example.createCriteria()
                .andEpisodeIdIn(episodeIdList)
                .andLaunchTypeEqualTo(OgvLaunchTypeEnum.EPISODE.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdScheduleOgvPo> pos = this.scheduleOgvDao.selectByExample(example);
        return pos.stream().collect(
                Collectors.groupingBy(GdScheduleOgvPo::getEpisodeId,
                        Collectors.mapping(GdScheduleOgvPo::getScheduleId,
                                Collectors.collectingAndThen(Collectors.toSet(), Lists::newArrayList))));
    }

    @Override
    public Map<Long, List<Integer>> getValidScheduleByEpisode(List<Long> episodeIdList) {
        if (CollectionUtils.isEmpty(episodeIdList)) {
            return Maps.newHashMap();
        }
        List<GdScheduleOgvPo> pos = this.scheduleOgvDaoExtension.selectValidScheduleOgvByEpisode(
                episodeIdList, new Timestamp(System.currentTimeMillis()));

        return pos.stream().collect(
                Collectors.groupingBy(GdScheduleOgvPo::getEpisodeId,
                        Collectors.mapping(GdScheduleOgvPo::getScheduleId,
                                Collectors.collectingAndThen(Collectors.toSet(), Lists::newArrayList))));
    }

    //查询episodeId对应的seasonId
    @Override
    public Long getSeasonIdInSchedule(Long episodeId) {
        if (Objects.isNull(episodeId)) {
            return null;
        }
        GdScheduleOgvPoExample example = new GdScheduleOgvPoExample();
        example.createCriteria()
                .andEpisodeIdEqualTo(episodeId)
                .andLaunchTypeEqualTo(OgvLaunchTypeEnum.EPISODE.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setLimit(1);//随便捞一条即可
        List<GdScheduleOgvPo> pos = this.scheduleOgvDao.selectByExample(example);
        return CollectionUtils.isEmpty(pos) ? null : pos.get(0).getSeasonId();
    }

    private Map<Integer, GdSchedulePo> selectScheduleByScheduleId(List<Integer> scheduleIdList) {
        if (CollectionUtils.isEmpty(scheduleIdList)) return Maps.newHashMap();
        GdSchedulePoExample example = new GdSchedulePoExample();
        example.createCriteria()
                .andScheduleIdIn(scheduleIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdSchedulePo> pos = this.scheduleDao.selectByExample(example);
        return pos.stream().collect(Collectors.toMap(GdSchedulePo::getScheduleId, Function.identity()));
    }

    private Map<Integer, List<GdScheduleOgvPo>> selectScheduleOgvByScheduleId(List<Integer> scheduleIdList) {
        if (CollectionUtils.isEmpty(scheduleIdList)) return Maps.newHashMap();
        GdScheduleOgvPoExample example = new GdScheduleOgvPoExample();
        example.createCriteria()
                .andScheduleIdIn(scheduleIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdScheduleOgvPo> pos = this.scheduleOgvDao.selectByExample(example);
        return pos.stream().collect(Collectors.groupingBy(GdScheduleOgvPo::getScheduleId));
    }

    private void saveFlowAllocation(GdSchedulePo schedulePo, Set<Integer> sourceIdList) {
        LocalDate startDate = schedulePo.getGdBeginTime().toLocalDateTime().toLocalDate();
        LocalDate endDate = schedulePo.getGdEndTime().toLocalDateTime().toLocalDate();
        for (; !startDate.isAfter(endDate); startDate = startDate.plusDays(1)) {
            for (Integer sourceId : sourceIdList) {
                GdFlowAllocationPo flowAllocation = GdFlowAllocationPo.builder()
                        .source(sourceId)
                        .cpms(Math.toIntExact(schedulePo.getTotalImpression()))
                        .launchDay(Timestamp.valueOf(LocalDateTime.of(startDate, LocalTime.MIN)))
                        .scheduleId(schedulePo.getScheduleId())
                        .gdType(GdType.BRAND.getCode())
                        .resourceType(ResourceType.OTHER.getCode())
                        .build();
                flowAllocationDao.insertSelective(flowAllocation);
            }
        }
    }


    private void insertScheduleOgv(Integer scheduleId, OgvLaunchTypeEnum launchType, Map<Long, Set<Long>> seasonEpisodeMap) {
        this.deleteScheduleOgv(scheduleId);
        //所有字段都要赋值
        BiFunction<Long, Long, GdScheduleOgvPo> scheduleOgvSupplier = (seasonId, episodeId) -> GdScheduleOgvPo.builder()
                .scheduleId(scheduleId)
                .seasonId(seasonId)
                .episodeId(episodeId)
                .launchType(launchType.getCode())
                .isDeleted(IsDeleted.VALID.getCode())
                .ctime(new Timestamp(System.currentTimeMillis()))
                .mtime(new Timestamp(System.currentTimeMillis()))
                .build();
        List<GdScheduleOgvPo> scheduleOgvPos;
        if (Objects.equals(launchType, OgvLaunchTypeEnum.EPISODE)) {
            scheduleOgvPos = seasonEpisodeMap.entrySet().stream()
                    .filter(entry -> Utils.isPositive(entry.getKey()))//season兜底，实际不大可能为0
                    .map(entry -> entry.getValue().stream()
                            .filter(Utils::isPositive)//episode兜底，实际不大可能为0
                            .map(episodeId -> scheduleOgvSupplier.apply(entry.getKey(), episodeId))
                            .collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
        } else {
            scheduleOgvPos = seasonEpisodeMap.keySet().stream()
                    .filter(Utils::isPositive)
                    .map(seasonId -> scheduleOgvSupplier.apply(seasonId, 0L))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(scheduleOgvPos)) {
            this.scheduleOgvDao.insertUpdateBatch(scheduleOgvPos);
        }
    }


    private GdSchedulePo buildGdSchedulePo(OgvScheduleCreateDto dto, ValidationResult validationResult) {
        GdSchedulePo po = GdSchedulePo.builder()
                .name(dto.getName())
                .orderId(validationResult.getOrder().getOrderId())
                .accountId(validationResult.getOrder().getAccountId())
                .gdBeginTime(dto.getBeginTime())
                .gdEndTime(dto.getEndTime())
                .beginDate(dto.getBeginTime())
                .endDate(dto.getEndTime())
                .promotionPurposeType(dto.getPromotionPurposeType())
                .scheduleType(ScheduleType.NEW_SCHEDULE.getCode())
                .templateId(validationResult.getTemplate().getTemplateId())
                .templateName(validationResult.getTemplate().getTemplateName())
                .sourceIds(validationResult.getSourceMap().keySet().stream()
                        .map(Objects::toString)
                        .collect(Collectors.joining(",")))
                .cycleId(validationResult.getCycle().getCycleId())
                .costPrice(0L)
                .externalPrice(0L)
                .internalPrice(0L)
                .status(SwitchStatus.STARTED.getCode())
                .businessSideId(validationResult.getBusinessSide().getId())
                .orderProduct(validationResult.getOrder().getProduct())
                .salesType(OrderProduct.getByCode(validationResult.getOrder().getProduct()).getSalesType().getCode())
                .resType(SsaSourceType.BUSINESS.getCode())
                .adType(SsaAdType.VIDEO.getCode())
                .scheduleStyle(ScheduleStyle.SCHEDULE_BY_DAY.getCode())
                .rotationRatio(0)
                .totalImpression(0)
                .frequencyLimit(1)//单次播放单集不出同1个广告主的贴片广告
                .unitId(this.snowflakeIdWorker.nextId())//AS会使用
                .build();

        OrderProduct orderProduct = OrderProduct.getByCode(validationResult.getOrder().getProduct());
        if (Objects.equals(orderProduct, OrderProduct.OGV_CPT)) {
            po.setRotationRatio(1000);//1轮
            //v1:
            //ogv-cpt同普通cpt类似，使用外部价格字段, 1w/(贴/天)
            // po.setExternalPrice(1000000L * validationResult.getDateSet().size());

            //v2:
            po.setExternalPrice(validationResult.getPriceHolder().getExternalPrice() * validationResult.getDateSet().size());
            po.setInternalPrice(validationResult.getPriceHolder().getInternalPrice() * validationResult.getDateSet().size());
        } else {
            OgvGdScheduleCreateDto gdDto = (OgvGdScheduleCreateDto) dto;
            po.setTotalImpression(gdDto.getTotalImpression());

            //v1:
            //po.setCostPrice(1000L);//只适用于ogv-gd；ogv-cpt不用，否则影响计费，10元/cpm
            //po.setExternalPrice(po.getTotalImpression() * po.getCostPrice());
            //po.setInternalPrice(po.getTotalImpression() * po.getCostPrice());

            //v2:
            po.setCostPrice(validationResult.getPriceHolder().getCpmPrice());//只适用于ogv-gd；ogv-cpt不用，否则影响计费，10元/cpm
            po.setExternalPrice(po.getTotalImpression() * po.getCostPrice());
            po.setInternalPrice(po.getTotalImpression() * po.getCostPrice());

            //ogv-gd 频控默认4
            po.setFrequencyLimit(4);

            //地域组id
            po.setAreaGroupId(gdDto.getAreaGroupId());
        }
        return po;
    }


    private ValidationResult validate(OgvScheduleCreateDto dto, Operator operator) {
        Assert.hasText(dto.getName(), "排期名称不能为空");
        Assert.notEmpty(dto.getSeasonIds(), "season信息不为空");
        Assert.isTrue(dto.getSeasonIds().size() <= MAX_SEASON_COUNT, "season数量不能超过" + MAX_SEASON_COUNT);
        PromotionPurposeType ppt = PromotionPurposeType.getByCode(dto.getPromotionPurposeType());
        Assert.isTrue(Objects.equals(ppt, PromotionPurposeType.OGV_STANDARD), "推广目的不支持");
        OgvLaunchTypeEnum ogvLaunchType = OgvLaunchTypeEnum.getByCode(dto.getOgvLaunchType());

        //check platform
        Assert.notEmpty(dto.getTargets(), "定向信息不能为空");
        List<Integer> platformIdList = dto.getTargets().stream()
                .filter(rule -> TargetType.OS.getCode().equals(rule.getRuleType()))
                .map(TargetRule::getValueIds)
                .flatMap(Collection::stream)
                .map(platformId -> PlatformType.getByCode(Constant.PLATFORM_MAP.get(platformId)).getCode())// error if invalid
                .distinct()
                .collect(Collectors.toList());
        Assert.notEmpty(platformIdList, "设备定向信息不能为空");
        //仅仅是OTT
        boolean isOtt = false;
        if (platformIdList.contains(PlatformType.OTT.getCode())) {
            Assert.isTrue(platformIdList.size() == 1, "OTT不支持和其他平台共选");
            isOtt = true;
        }
        Map<Long, Set<Long>> seasonEpisodeMap;
        if (Objects.equals(ogvLaunchType, OgvLaunchTypeEnum.EPISODE)) {
            Assert.notEmpty(dto.getEpisodeIds(), "episode信息不为空");
            //check episode exist
            Map<Long, OgvEpisodeDto> episodeMap = this.ogvHelper.queryOgvEpisodeByEpisode(dto.getEpisodeIds(), isOtt);
            String missedEpisodeInfo = dto.getEpisodeIds().stream()
                    .filter(episodeId -> !episodeMap.containsKey(episodeId))
                    .map(Object::toString)
                    .collect(Collectors.joining(","));
            Assert.isTrue(missedEpisodeInfo.isEmpty(), "下述episode不存在，请核实后重试：" + missedEpisodeInfo);

            //check episode blacklist
            Map<Long, Boolean> episodeAllowMap = this.ogvHelper.queryEpisodeAllows(dto.getEpisodeIds(), isOtt);
            String blackEpisodeInfo = dto.getEpisodeIds().stream()
                    .filter(episodeId -> !episodeAllowMap.getOrDefault(episodeId, false))
                    .map(episodeId -> String.format("%d-%s", episodeId, episodeMap.get(episodeId).getLongTitle()))
                    .collect(Collectors.joining(","));
            Assert.isTrue(blackEpisodeInfo.isEmpty(), "下述episode不可投放，请核实后重试：" + blackEpisodeInfo);
            seasonEpisodeMap = episodeMap.values().stream()
                    .collect(Collectors.groupingBy(OgvEpisodeDto::getSeasonId,
                            Collectors.mapping(OgvEpisodeDto::getEpisodeId, Collectors.toSet())));
        } else {
            seasonEpisodeMap = dto.getSeasonIds().stream().distinct()
                    .collect(Collectors.toMap(Function.identity(), seasonId -> Sets.newHashSet()));
            //check season whitelist
            Map<Long, Boolean> seasonAllowMap = this.ogvHelper.querySeasonAllows(Lists.newArrayList(seasonEpisodeMap.keySet()), isOtt);
            String blackSeasonInfo = seasonEpisodeMap.keySet().stream()
                    .filter(seasonId -> !seasonAllowMap.getOrDefault(seasonId, false))
                    .map(Objects::toString)
                    .collect(Collectors.joining(","));
            Assert.isTrue(blackSeasonInfo.isEmpty(), "下述season不可投放，请核实后重试：" + blackSeasonInfo);
        }

        //check season (includes seasons that from episode) in ORV_RESOURCE
        List<Long> seasonIdList = Lists.newArrayList(seasonEpisodeMap.keySet());
        PageResult<OgvResourceDto> ogvResourceList = this.resOgvService.getOgvResourceList(OgvResourceQueryDto.builder()
                .seasonIdList(seasonIdList)
                .statusList(Lists.newArrayList(OgvResourceStatusEnum.ONLINE.getCode()))
                .pageIndex(1)
                .pageSize(MAX_SEASON_COUNT)
                .build());
        List<OgvResourceDto> ogvResources = ogvResourceList.getRecords();
        Set<Long> validSeasonIdSet = ogvResources.stream()
                .filter(res -> Objects.nonNull(res.getSeason()))
                .map(res -> res.getSeason().getSeasonId())
                .collect(Collectors.toSet());
        String missedSeasonInfo = seasonIdList.stream()
                .filter(seasonId -> !validSeasonIdSet.contains(seasonId))
                .map(Object::toString)
                .collect(Collectors.joining(","));
        Assert.isTrue(missedSeasonInfo.isEmpty(), "下述season不存在，请核实后重试：" + missedSeasonInfo);


        //check time
        Assert.notNull(dto.getBeginTime(), "投放时间不能为空");
        Assert.notNull(dto.getEndTime(), "投放时间不能为空");
        Assert.isTrue(!dto.getEndTime().before(dto.getBeginTime()), "投放结束时间不能必须大于等于开始时间");
        List<Timestamp> launchDays = TimeUtils.getEachDay(dto.getBeginTime(), dto.getEndTime());
        Set<LocalDate> commonDateSet = this.resOgvService.getCommonLaunchTime(seasonIdList).stream()
                .map(mills -> new Timestamp(mills).toLocalDateTime().toLocalDate())
                .collect(Collectors.toSet());
        Set<LocalDate> dateSet = launchDays.stream()
                .map(day -> day.toLocalDateTime().toLocalDate())
                .collect(Collectors.toSet());
        Assert.isTrue(CollectionUtils.isSubCollection(dateSet, commonDateSet), "可投放时间信息有变动，请刷新后重试");
        List<SplitDaysImpressDto> daysImpress = launchDays.stream()
                .map(day -> SplitDaysImpressDto.builder()
                        .impressionCpm(0L)
                        .rotationNum(0)
                        .beginTime(day)
                        .endTime(com.bilibili.utils.TimeUtil.getEndOfDay(day))
                        .build())
                .collect(Collectors.toList());

        //所选的平台定向必须是共同可投放平台的子集
        List<Integer> commonPlatformIds = this.resOgvService.getCommonPlatform(seasonIdList);
        Assert.isTrue(CollectionUtils.isSubCollection(platformIdList, commonPlatformIds),
                "平台定向不是所选season所能支持的可投放平台的子集，请确认和修改后重试");

        //check order and account
        GdOrderDto order = this.orderService.getOrderById(dto.getOrderId(), operator);//error if invalid
        BusinessSideBaseDto businessSide = businessSideService.getBusinessSideByAccountId(order.getAccountId());

        //check template
        //ogv固定模板594
        TemplateDto template = this.queryTemplateService.getTemplateById(594);

        //check cycle
        List<Timestamp> times = TimeUtils.getEachDay(dto.getBeginTime(), dto.getEndTime());
        this.cycleService.checkCycleMatchTime(times, OrderProduct.OGV_CPT.getCode());
        CycleQueryDto queryDto = CycleQueryDto.builder()
                .cycleId(dto.getCycleId())
                .orderProduct(OrderProduct.OGV_CPT)
                .build();
        CycleDto cycle = this.cycleServiceFacade.getValidCycle(queryDto);
        log.info("[OgvScheduleService] cycle is {}", cycle);
        return ValidationResult.builder()
                .order(order)
                .businessSide(businessSide)
                .seasonEpisodeMap(seasonEpisodeMap)
                .platformIdList(platformIdList)
                .dateSet(dateSet)
                .daysImpress(daysImpress)
                .template(template)
                .cycle(cycle)
                .ogvResources(ogvResources)
                .build();
    }

    private ValidationResult validateCpt(OgvCptScheduleCreateDto dto, Operator operator) {
        ValidationResult validationResult = validate(dto, operator);
        Assert.isTrue(Objects.equals(validationResult.getOrder().getProduct(), OrderProduct.OGV_CPT.getCode()),
                "不匹配的订单类型");
        Map<Long, Set<Long>> seasonEpisodeMap = validationResult.getSeasonEpisodeMap();
        List<Long> seasonIdList = Lists.newArrayList(seasonEpisodeMap.keySet());

        //check sticker
        Assert.notEmpty(dto.getStickerIds(), "贴次不能为空");
        List<Integer> stickerIdList = dto.getStickerIds().stream().distinct().collect(Collectors.toList());
        Set<OgvResourceStickerDto> commonStickerSet = Sets.newHashSet(this.resOgvService.getCommonLaunchSticker(seasonIdList));
        Set<OgvResourceStickerDto> stickerSet = Sets.newHashSet(this.resOgvService.getStickerByStickerId(stickerIdList));
        Assert.isTrue(CollectionUtils.isSubCollection(stickerSet, commonStickerSet), "贴次信息有变动，请刷新后重试");

        //check source
        Map<Integer, SourceAllInfoDto> sourceMap = this.resOgvService.getSourceByPlatformAndSticker(
                validationResult.getPlatformIdList(), stickerIdList);

        //check conflict
        validateConflict(seasonEpisodeMap, sourceMap.keySet(), validationResult.getDateSet(),
                OgvLaunchTypeEnum.getByCode(dto.getOgvLaunchType()));


        //check price
        PriceHolder priceHolder = this.validatePrice(validationResult, stickerIdList, dto);

        validationResult.setSourceMap(sourceMap);
        validationResult.setPriceHolder(priceHolder);
        return validationResult;
    }

    private ValidationResult validateGd(OgvGdScheduleCreateDto dto, Operator operator) {
        ValidationResult validationResult = validate(dto, operator);
        Assert.isTrue(Objects.equals(validationResult.getOrder().getProduct(), OrderProduct.OGV_GD.getCode()),
                "不匹配的订单类型");
        Assert.isTrue(Utils.isPositive(dto.getTotalImpression()), "目标展示量不能为空");
        List<Integer> platformIdList = validationResult.getPlatformIdList();
        //OGV GD 占用所有资源位，实际引擎出的顺序是CPT>GD
        List<Long> seasonIdList = Lists.newArrayList(validationResult.getSeasonEpisodeMap().keySet());
        Map<Long, List<OgvResourceStickerDto>> stickers = this.resOgvService.getLaunchStickerBySeasonId(seasonIdList);
        Assert.notEmpty(stickers, "没有可适用的贴次");
        //校验是否具有相同的贴次
        List<OgvResourceStickerDto> baseStickers = null;
        for (Map.Entry<Long, List<OgvResourceStickerDto>> entry : stickers.entrySet()) {
            if (CollectionUtils.isEmpty(baseStickers)) {
                baseStickers = entry.getValue();
                baseStickers.sort(Comparator.comparing(OgvResourceStickerDto::getId));
                continue;
            }
            List<OgvResourceStickerDto> curStickers = entry.getValue();
            Assert.isTrue(curStickers.size() == baseStickers.size(), "每个season的可投放贴次必须相同");
            curStickers.sort(Comparator.comparing(OgvResourceStickerDto::getId));
            for (int i = 0; i < baseStickers.size(); i++) {
                Assert.isTrue(Objects.equals(baseStickers.get(i), curStickers.get(i)), "每个season的可投放贴次必须相同");
            }
        }
        List<Integer> stickerIdList = baseStickers.stream()
                .map(OgvResourceStickerDto::getId)
                .collect(Collectors.toList());
        Map<Integer, SourceAllInfoDto> sourceMap = this.resOgvService.getSourceByPlatformAndSticker(
                platformIdList, stickerIdList);

        //OGV-GD刊例价，avg(price)*cpm
        PriceHolder priceHolder = this.validatePrice(validationResult, stickerIdList, dto);

        validationResult.setSourceMap(sourceMap);
        validationResult.setPriceHolder(priceHolder);
        return validationResult;
    }


    private void validateConflict(Map<Long, Set<Long>> seasonEpisodeIdMap,
                                  Set<Integer> sourceIdSet, Set<LocalDate> dateSet,
                                  OgvLaunchTypeEnum ogvLaunchType) {
        GdScheduleOgvPoExample scheduleOgvPoExample = new GdScheduleOgvPoExample();
        scheduleOgvPoExample.createCriteria()
                .andSeasonIdIn(Lists.newArrayList(seasonEpisodeIdMap.keySet()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdScheduleOgvPo> scheduleOgvPos = this.scheduleOgvDao.selectByExample(scheduleOgvPoExample);
        if (scheduleOgvPos.isEmpty()) return;

        Timestamp startTime = dateSet.stream().min(LocalDate::compareTo)
                .map(d -> Timestamp.valueOf(LocalDateTime.of(d, LocalTime.MIN))).get();
        Timestamp endTime = dateSet.stream().max(LocalDate::compareTo)
                .map(d -> Timestamp.valueOf(LocalDateTime.of(d, LocalTime.MAX))).get();
        List<Integer> scheduleIdList = scheduleOgvPos.stream()
                .map(GdScheduleOgvPo::getScheduleId)
                .distinct().collect(Collectors.toList());

        //查询和当前新建排期时间有交集的排期
        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.createCriteria()
                .andScheduleIdIn(scheduleIdList)
                .andStatusIn(Lists.newArrayList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                .andOrderProductEqualTo(OrderProduct.OGV_CPT.getCode())//只排查OGV-CPT，OGV-GD不参与冲突
                .andGdBeginTimeLessThanOrEqualTo(endTime)
                .andGdEndTimeGreaterThanOrEqualTo(startTime)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Map<Integer, GdSchedulePo> scheduleMap = this.scheduleDao.selectByExample(schedulePoExample).stream()
                .collect(Collectors.toMap(GdSchedulePo::getScheduleId, Function.identity()));

        //是否是season维度投放
        boolean isSeasonLaunch = Objects.equals(OgvLaunchTypeEnum.SEASON, ogvLaunchType);
        //只需要遍历可能产生冲突的season
        Set<Long> existSeasonIdList = scheduleOgvPos.stream().map(GdScheduleOgvPo::getSeasonId).collect(Collectors.toSet());
        //冲突项
        List<ConflictItem> conflictItems = Lists.newArrayList();
        for (Long seasonId : existSeasonIdList) {
            ConflictItem conflictItem = ConflictItem.builder().seasonId(seasonId).schedules(Maps.newHashMap()).build();
            //如果value.size=1 && value[0]=0，说明是season维度的投放
            Map<Integer, Set<Long>> scheduleEpisodeMap = scheduleOgvPos.stream()
                    .filter(p -> Objects.equals(p.getSeasonId(), seasonId))
                    .collect(Collectors.groupingBy(GdScheduleOgvPo::getScheduleId,
                            Collectors.mapping(GdScheduleOgvPo::getEpisodeId, Collectors.toSet())));
            for (Map.Entry<Integer, Set<Long>> entry : scheduleEpisodeMap.entrySet()) {
                GdSchedulePo schedule = scheduleMap.get(entry.getKey());
                //可能为空，因为在查询排期时已经排除了不相交的
                if (Objects.isNull(schedule)) continue;
                Set<Integer> scheduleSourceIdList = Arrays.stream(schedule.getSourceIds().split(","))
                        .map(Integer::valueOf)
                        .collect(Collectors.toSet());
                Set<Integer> intersectionSourceIds = Sets.intersection(scheduleSourceIdList, sourceIdSet);
                //如果资源位不相交则肯定不冲突
                if (intersectionSourceIds.isEmpty()) continue;
                Set<LocalDate> scheduleDates = DateUtil.parseDates(schedule.getGdBeginTime(), schedule.getGdEndTime());
                //冲突的时间，理论上不会为空
                Set<LocalDate> intersectionDates = Sets.intersection(scheduleDates, dateSet);

                //走到这一步，说明该season存在日期、资源位冲突的可能
                Integer scheduleId = entry.getKey();
                Set<Long> scheduleEpisodeIdList = entry.getValue();
                boolean isScheduleSeasonLaunch = scheduleEpisodeIdList.size() == 1 && scheduleEpisodeIdList.contains(0L);
                if (isSeasonLaunch || isScheduleSeasonLaunch) {
                    //只要有一个全集投放，则无论另外一个是何种投放方式（season or episode），则注定会产生冲突
                    conflictItem.getSchedules().put(scheduleId, ScheduleConflictItem.builder()
                            .dates(intersectionDates)
                            .sourceIds(intersectionSourceIds)
                            .build());
                } else {
                    //说明二者都是指定集投放
                    //查询当前新建排期中，该season中被选中的episode，理论上讲至少有一个episode，兜底返回空Set
                    Set<Long> episodeIdList = seasonEpisodeIdMap.getOrDefault(seasonId, Sets.newHashSet());
                    Set<Long> intersectionEpisodes = Sets.intersection(episodeIdList, scheduleEpisodeIdList);
                    if (!intersectionEpisodes.isEmpty()) {
                        //episode产生了冲突
                        conflictItem.getSchedules().put(scheduleId, ScheduleConflictItem.builder()
                                .dates(intersectionDates)
                                .sourceIds(intersectionSourceIds)
                                .episodes(intersectionEpisodes)
                                .build());
                    }
                }
            }
            if (!conflictItem.getSchedules().isEmpty()) {
                conflictItems.add(conflictItem);
            }
        }

        if (conflictItems.isEmpty()) return;

        //build conflict msg
        List<Long> conflictSeasonIdList = conflictItems.stream().map(ConflictItem::getSeasonId).collect(Collectors.toList());
        Map<Long, OgvSeasonDto> seasonMap = this.pgcGrpcClient.queryOgvSeasons(conflictSeasonIdList, false);
        StringBuilder sb = new StringBuilder("冲突项提示：<br>");
        for (int index = 0; index < conflictItems.size(); index++) {
            ConflictItem conflictItem = conflictItems.get(index);
            //如果season不存在，剧名为null，可接收
            String header = String.format("%d、剧【%d-%s】存在以下冲突项<br>", index + 1, conflictItem.getSeasonId(),
                    seasonMap.getOrDefault(conflictItem.getSeasonId(), new OgvSeasonDto()).getTitle());
            sb.append(header);
            List<Map.Entry<Integer, ScheduleConflictItem>> entries = Lists.newArrayList(conflictItem.getSchedules().entrySet());
            for (int i = 0; i < entries.size(); i++) {
                Integer scheduleId = entries.get(i).getKey();
                ScheduleConflictItem rowItem = entries.get(i).getValue();
                //日期升序，直观点
                String conflictDates = String.join(",", TimeUtil.mergeDate(rowItem.getDates()));
                String conflictStickers = this.resOgvService.getStickerBySourceId(Lists.newArrayList(rowItem.getSourceIds()))
                        .values().stream().distinct()//不同资源位对应的逻辑贴次可能是一样的，因此去重
                        .map(OgvResourceStickerDto::getDesc)
                        .collect(Collectors.joining(","));
                String row = String.format("%d.%d、排期【%d】，日期【%s】，贴次【%s】", index + 1, i + 1, scheduleId,
                        conflictDates, conflictStickers);
                if (!CollectionUtils.isEmpty(rowItem.getEpisodes())) {
                    String conflictEpisodes = rowItem.getEpisodes().stream()
                            .sorted()//集升序，尽可能是递增
                            .map(Objects::toString)
                            .collect(Collectors.joining(","));
                    row = String.format("%s，集【%s】", row, conflictEpisodes);
                }
                sb.append(row).append("<br>");
            }
        }
        throw new ConflictException(sb.toString());
    }

    private PriceHolder validatePrice(ValidationResult validationResult, List<Integer> stickerIdList, OgvScheduleCreateDto dto) {
        boolean mock = false;
        //todo: append price
        if (mock) {
            return PriceHolder.builder()
                    .externalPrice(100L)
                    .internalPrice(100L)
                    .internalCpmPrice(100L)
                    .cpmPrice(100L)
                    .build();
        }
        List<OgvResourceDto> ogvResources = validationResult.getOgvResources();
        int maxPriceRows = 10000;
        OgvPriceQueryDto priceQueryDto = OgvPriceQueryDto.builder()
                .cycleId(validationResult.getCycle().getCycleId())
                .orderProduct(validationResult.getOrder().getProduct())
                .platformIdList(validationResult.getPlatformIdList())
                .levelList(ogvResources.stream()
                        .map(OgvResourceDto::getLevel)
                        .distinct().collect(Collectors.toList()))
                .seasonTypeList(ogvResources.stream()
                        .map(OgvResourceDto::getSeason)
                        .map(OgvSeasonDto::getType)
                        .distinct().collect(Collectors.toList()))
                .stickerIdList(stickerIdList)
                .statusList(Lists.newArrayList(Status.VALID.getCode()))
                .pageIndex(1)
                .pageSize(maxPriceRows)
                .build();
        PageResult<OgvPriceDto> pricePageResult = this.ogvPriceService.getPriceList(priceQueryDto);
        Assert.isTrue(pricePageResult.getTotal() <= maxPriceRows,
                "吼吼，您投放的season过于丰富，超出刊例价的极限了，请联系技术，谢谢~");
        log.info("[OgvScheduleService] pricePageResult original price:" + JSON.toJSONString(pricePageResult.getRecords()));

        BinaryOperator<PriceHolder> priceMerger = (p1, p2) -> {
            if (Objects.isNull(p1) || Objects.isNull(p2)) {
                return Objects.isNull(p1) ? p2 : p1;
            }
            return PriceHolder.builder()
                    .externalPrice(p1.getExternalPrice() + p2.getExternalPrice())
                    .internalPrice(p1.getInternalPrice() + p2.getInternalPrice())
                    .internalCpmPrice(p1.getInternalCpmPrice() + p2.getInternalCpmPrice())
                    .cpmPrice(p1.getCpmPrice() + p2.getCpmPrice())
                    .build();
        };

        //level_seasonType_platform_sticker
        String keyFormat = "%s_%d_%d_%d";
        Map<String, PriceHolder> priceMap = pricePageResult.getRecords().stream()
                .collect(Collectors.toMap(
                        p -> String.format(keyFormat, p.getLevel(), p.getSeasonType(), p.getPlatformId(), p.getStickerId()),
                        p -> {
                            BigDecimal ratio = this.calRaiseRatio(dto, validationResult, p);
                            //为了防止累加导致的价格溢出，先转成PriceHolder
                            //四舍五入，向上取整
                            PriceHolder priceHolder = PriceHolder.builder()
                                    .externalPrice(BigDecimal.valueOf(p.getExternalPrice()).multiply(ratio)
                                            .setScale(0, RoundingMode.HALF_UP).longValue())
                                    .internalPrice(BigDecimal.valueOf(p.getInternalPrice()).multiply(ratio)
                                            .setScale(0, RoundingMode.HALF_UP).longValue())
                                    .internalCpmPrice(Long.valueOf(p.getInternalCpmPrice()))//not_used
                                    .cpmPrice(BigDecimal.valueOf(p.getCpmPrice()).multiply(ratio)
                                            .setScale(0, RoundingMode.HALF_UP).longValue())
                                    .build();
                            //打印加价之后的各个价格（单价）
                            log.info("[OgvScheduleService] validatePrice, priceId={}, originalExternalPrice={}, " +
                                            "originalInternalPrice={}, originalCpmPrice={}, raisedExternalPrice={}, " +
                                            "raisedInternalPrice={}, raisedCpmPrice={}, raisedRatio={}",
                                    p.getId(), p.getExternalPrice(), p.getInternalPrice(), p.getCpmPrice(),
                                    priceHolder.getExternalPrice(), priceHolder.getInternalPrice(), priceHolder.getCpmPrice(), ratio);
                            return priceHolder;
                        },
                        //如果冲突，则将价格累加（基本不可能有重复的，但是为了防止迭代需求中突然增加了其他key导致冲突报错，因此merge下）
                        priceMerger));

        List<String> missedPriceSeasonInfo = Lists.newArrayList();

        Map<Long, List<PriceHolder>> seasonPriceMap = Maps.newHashMap();


        //以season维度计算刊例价
        for (OgvResourceDto ogvResource : ogvResources) {
            OgvSeasonDto season = ogvResource.getSeason();
            List<PriceHolder> priceHolders = Lists.newArrayList();
            for (Integer platformId : validationResult.getPlatformIdList()) {
                for (Integer stickerId : stickerIdList) {
                    String key = String.format(keyFormat, ogvResource.getLevel(), season.getType(), platformId, stickerId);
                    PriceHolder priceHolder = priceMap.get(key);
                    if (Objects.isNull(priceHolder)) {
                        log.info("[OgvScheduleService] price is missed for season. seasonId={}, level={}, seasonType={},"
                                        + " platformId={}, stickerId={}",
                                season.getSeasonId(), ogvResource.getLevel(), season.getType(), platformId, stickerId);
                        missedPriceSeasonInfo.add(String.format("%d：【%s,%s,%s,%s】", season.getSeasonId(),
                                ogvResource.getLevel(), OgvSeasonTypeEnum.getByCodeWithoutEx(season.getType()).getDesc(),
                                PlatformType.getByCode(platformId).getDesc(), OgvResourceStickerEnum.getById(stickerId).getDesc()));
                        continue;
                    }
                    priceHolders.add(priceHolder);
                }
            }
            seasonPriceMap.put(season.getSeasonId(), priceHolders);
        }

        Assert.isTrue(missedPriceSeasonInfo.isEmpty(), String.format("下述season没有找到匹配的刊例价，请核实后重试。%s",
                String.join("，", missedPriceSeasonInfo)));

        //计算出单价
        //OGV-CPT刊例价，sum(price)*days
        //OGV-GD刊例价，avg(price)*cpm

        PriceHolder priceHolder = seasonPriceMap.values().stream()
                .flatMap(Collection::stream)
                .reduce(priceMerger).get();
        OrderProduct orderProduct = OrderProduct.getByCode(validationResult.getOrder().getProduct());
        if (Objects.equals(orderProduct, OrderProduct.OGV_GD)) {
            Integer priceCounts = seasonPriceMap.values().stream().map(List::size).reduce(Integer::sum).get();
            BigDecimal unitCpmPrice = BigDecimal.valueOf(priceHolder.getCpmPrice())
                    .divide(BigDecimal.valueOf(priceCounts), RoundingMode.HALF_UP);
            priceHolder.setCpmPrice(unitCpmPrice.longValue());
        }
        return priceHolder;
    }

    /**
     * 基于season维度的并发控制，该方法完成后，请在finally中调用{@link #unlock(List<RLock>)}，尝试释放锁
     */
    private void lock(List<RLock> lockers, List<Long> seasonIdList) throws Exception {
        for (Long seasonId : seasonIdList) {
            RLock locker = redissonClient.getLock(RedisKey.SCHEDULE_SEASON_LOCK_PREFIX + seasonId);
            Assert.notNull(locker, String.format("season锁控制失败【seasonId=%d】，请稍后重试", seasonId));
            //一定要在tryLock之前保存locker，以防止由于网络等其他原因导致tryLock失败，但实际已上锁的可能，给unlock留个机会
            lockers.add(locker);
            //等待10秒，持有5分钟，防止OGV接口太慢
            Assert.isTrue(locker.tryLock(10, 300, TimeUnit.SECONDS),
                    String.format("season存在并发操作【seasonId=%d】，请稍后重试", seasonId));
        }
    }

    /**
     * 释放锁
     */
    private void unlock(List<RLock> lockers) {
        lockers.stream().filter(Objects::nonNull).forEach(Lock::unlock);
    }


    private Map<Integer, List<Integer>> getScheduleOsTarget(List<Integer> scheduleIdList) {
        List<ScheduleTargetDto> targets = queryScheduleService.getScheduleTargetDtoInScheduleIds(scheduleIdList);
        return targets.stream()
                .filter(t -> Objects.equals(t.getTargetType(), TargetType.OS.getCode()))
                .collect(Collectors.toMap(ScheduleTargetDto::getScheduleId,
                        t -> org.springframework.util.CollectionUtils.isEmpty(t.getTargetItemIds())
                                ? Lists.newArrayList(Constant.OGV_DEFAULT_PLATFORM_ID_LIST)
                                : t.getTargetItemIds().stream().map(Constant.PLATFORM_MAP::get).collect(Collectors.toList()),
                        (s, t) -> t));
    }

    /**
     * 计算加价系数
     *
     * @return 总系数（>=1）
     */
    private BigDecimal calRaiseRatio(OgvScheduleCreateDto dto, ValidationResult validationResult, OgvPriceDto price) {
        if (!(dto instanceof OgvGdScheduleCreateDto)) {
            //OGV-CPT不支持定向加收
            return BigDecimal.ONE;
        }
        OgvGdScheduleCreateDto gdScheduleCreateDto = (OgvGdScheduleCreateDto) dto;
        PriceRaiseDto raise = price.getRaise();
        List<TargetRule> targetRules = dto.getTargets();

        //基本定向加收断言
        Predicate<TargetType> basicTargetPredicate = targetType -> targetRules.stream()
                .anyMatch(t -> Objects.equals(t.getRuleType(), targetType.getCode()) && CollectionUtils.isNotEmpty(t.getValueIds()));

        //加收比例解析
        //raiseType：加收类型
        //bSupplier：是否参与加收
        //ratioSupplier：加收比例
        ThFunction<String, Supplier<Boolean>, Supplier<Integer>, Integer> raiseRatioSupplier = (raiseType, preSupplier, ratioSupplier) -> {
            if (preSupplier.get()) {
                Integer ratio = ratioSupplier.get();
                if (Utils.isPositive(ratio)) {
                    log.info("[OgvScheduleService] calRaiseRatio, priceId={}, raiseType={}, ratio={}",
                            price.getId(), raiseType, ratio);
                    return ratio;
                }
            }
            return 0;
        };
        int ratio = 0;
        ratio += raiseRatioSupplier.apply(TargetType.GENDER.getByName(),
                () -> basicTargetPredicate.test(TargetType.GENDER), raise::getGenderRaiseRatio);
        ratio += raiseRatioSupplier.apply(TargetType.AGE.getByName(),
                () -> basicTargetPredicate.test(TargetType.AGE), raise::getAgeRaiseRatio);
        ratio += raiseRatioSupplier.apply(TargetType.CROW_PACK.getByName(),
                () -> CollectionUtils.isNotEmpty(gdScheduleCreateDto.getCrowdPackIds()), raise::getCrowdPackRaiseRatio);
        ratio += raiseRatioSupplier.apply(TargetType.AREA.getByName(),
                () -> Utils.isPositive(gdScheduleCreateDto.getAreaGroupId()), () -> {
                    AreaGroupType areaGroupType = AreaGroupType.getByCode(gdScheduleCreateDto.getAreaGroupId());
                    if (Objects.equals(areaGroupType, AreaGroupType.MAJOR_CITY)) {
                        return raise.getMajorCityRaiseRatio();
                    } else if (Objects.equals(areaGroupType, AreaGroupType.CORE_CITY)) {
                        return raise.getCoreCityRaiseRatio();
                    }
                    return raise.getOtherCityRaiseRatio();
                });
        ratio += raiseRatioSupplier.apply(TargetType.OS.getByName(),
                () -> validationResult.getPlatformIdList().size() == 1 && validationResult.getPlatformIdList().contains(PlatformType.IPHONE.getCode()),
                raise::getIosRaiseRatio);
        log.info("[OgvScheduleService] calRaiseRatio, priceId={}, totalRatio={}", price.getId(), ratio);
        //虽然指定了HALF_UP，但由于是除100，因此小数位其实最多就是2位，因此没本质的影响
        return BigDecimal.valueOf(ratio + 100).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ValidationResult {
        GdOrderDto order;
        BusinessSideBaseDto businessSide;
        Map<Integer, SourceAllInfoDto> sourceMap;
        Map<Long, Set<Long>> seasonEpisodeMap;
        TemplateDto template;
        /**
         * @see PlatformType
         */
        List<Integer> platformIdList;
        Set<LocalDate> dateSet;
        List<SplitDaysImpressDto> daysImpress;
        List<OgvResourceDto> ogvResources;
        //cycle & price related
        CycleDto cycle;
        PriceHolder priceHolder;
    }

    /**
     * 以下资源已被预订，请修改后重试：
     * ogv资源id+剧集名称+集数+日期+贴片位次+已预订该资源的排期ID
     * ……（列出所有冲突项）”
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ConflictItem {
        Long seasonId;
        Map<Integer, ScheduleConflictItem> schedules;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ScheduleConflictItem {
        Set<LocalDate> dates;
        Set<Integer> sourceIds;
        //如果为空说明是season产生冲突，否则是二者指定集产生了冲突
        Set<Long> episodes;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class PriceHolder {
        private Long externalPrice;
        private Long internalPrice;
        //not used
        private Long internalCpmPrice;
        private Long cpmPrice;
    }
}
