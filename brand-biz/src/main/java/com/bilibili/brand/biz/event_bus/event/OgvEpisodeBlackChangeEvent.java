package com.bilibili.brand.biz.event_bus.event;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.bilibili.brand.api.resource.ogv.OgvEpisodeDto;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ogv episode 黑名单变更事件
 *
 * <AUTHOR>
 * @date 2023/9/17 19:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OgvEpisodeBlackChangeEvent extends BrandEvent implements Serializable {
    private static final long serialVersionUID = -1957148385001672443L;
    /**
     * episodeId
     *
     * @see OgvEpisodeDto#getEpisodeId()
     */
    private Long episodeId;

    /**
     * 变更类型
     */
    @JSONField(serializeFeatures = {JSONWriter.Feature.WriteEnumsUsingName})
    private BrandEvent.ActionType actionType;

    /**
     * 事件来源
     */
    @JSONField(serializeFeatures = {JSONWriter.Feature.WriteEnumsUsingName})
    private OgvEventSource source;
}
