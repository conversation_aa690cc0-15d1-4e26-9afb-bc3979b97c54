package com.bilibili.brand.biz.schedule.service;

import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.cpt.platform.common.BusinessSideType;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;

/**
 * 闪屏cpt处理逻辑
 *
 * <AUTHOR>
 * @date 2022/12/20
 */
@Service
public class SsaCptScheduleService {

    @Autowired
    private IGdOrderService gdOrderService;
    @Autowired
    private ISystemConfigService configService;
    @Autowired
    private ContractConfigHandler contractConfigHandler;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;

    public int getSsaCptOneRotationCpm(Integer orderId) {
        GdOrderDto order = gdOrderService.getOrderById(orderId);
        Integer cpmPerRotation = contractConfigHandler.getCpmPerRotation(OrderProduct.SSA_CPT_PLUS, order.getContractAccountId());
        if (cpmPerRotation == null) {
            cpmPerRotation = defaultCpmPerRotation(order);
        }
        return cpmPerRotation;
    }

    private int defaultCpmPerRotation(GdOrderDto order) {
        HashSet<Integer> specialAccountSet =
                new HashSet<>(configService.getValueReturnListInt(SystemConfigEnum.SSA_CPT_NEW_ONE_ROTATION_CPM_ACCOUNT_ID.getCode()));
        AccountBaseDto accountInfo = soaQueryAccountService.getAccountBaseDtoById(order.getAccountId());
        Integer cpmPerRotation;
        if (specialAccountSet.contains(order.getContractAccountId())
                || BusinessSideType.INERNAL == BusinessSideType.getByIsInner(accountInfo.getIsInner())) {
            cpmPerRotation = configService.getValueReturnInt(SystemConfigEnum.SSA_CPT_ONE_ROTATION_ORDER_CPM.getCode());
        } else {
            cpmPerRotation = configService.getValueReturnInt(SystemConfigEnum.SSA_CPT_NEW_ONE_ROTATION_ORDER_CPM.getCode());
        }
        return cpmPerRotation;
    }

}
