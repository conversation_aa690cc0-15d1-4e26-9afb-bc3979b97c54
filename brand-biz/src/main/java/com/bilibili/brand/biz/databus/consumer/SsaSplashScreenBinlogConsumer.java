package com.bilibili.brand.biz.databus.consumer;

import com.alibaba.fastjson2.JSON;
import com.bilibili.brand.biz.databus.dto.BinlogMsg;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import com.bilibili.brand.biz.event_bus.IBrandEventPublisher;
import com.bilibili.brand.biz.event_bus.event.SsaSplashScreenBinlogChangeEvent;
import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * ssa_splash_screen binlog
 * UAT:
 * https://uat-cloud.bilibili.co/dts/source/info?id=101138&
 * task_name=%E5%93%81%E7%89%8Cssa_splash_screen%E5%88%9B%E6%84%8FBinlog&sceneType=1&dataTaskType=dbm#sh/sh001/uat
 * <p>
 * PROD:
 *
 * <AUTHOR>
 * @date 2024/7/17 16:58
 */
@Slf4j
@DataBusConsumer("SsaSplashScreenBinlogConsumer")
public class SsaSplashScreenBinlogConsumer extends AbstractConsumer<BinlogMsg> {
    @Autowired
    private IBrandEventPublisher<BrandEvent> brandEventPublisher;

    @Override
    protected boolean ackIfNecessary() {
        return true;
    }

    @Override
    protected void doConsume(BinlogMsg binlog, ConsumeMessageContext ctx) throws Exception {
        String jsonMsg = JSON.toJSONString(binlog);
        log.info("[SsaSplashScreenBinlogConsumer] msg={}", jsonMsg);
        BrandEvent.ActionType actionType = BrandEvent.ActionType.getActionTypeWithSimple(binlog.getAction());
        if (Objects.isNull(actionType)) {
            return;
        }
        //默认采用了SmartMatch，因此可以自动将下换线转驼峰
        SsaSplashScreenPo newSsaSplashScreenPo = binlog.getNewData().to(SsaSplashScreenPo.class);
        SsaSplashScreenPo oldSsaSplashScreenPo = null;
        if (Objects.equals("update", binlog.getAction()) && Objects.nonNull(binlog.getOldData())) {
            oldSsaSplashScreenPo = binlog.getOldData().to(SsaSplashScreenPo.class);
        }

        this.brandEventPublisher.publish(SsaSplashScreenBinlogChangeEvent.builder()
                .newData(newSsaSplashScreenPo)
                .oldData(oldSsaSplashScreenPo)
                .actionType(actionType)
                .build());
    }
}
