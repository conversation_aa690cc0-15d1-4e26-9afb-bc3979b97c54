package com.bilibili.brand.biz.report.dto;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdTopViewPo;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.ssa.platform.biz.po.SsaCpmSchedulePo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataReportQueryContext {

    // key:id
    private volatile Map<Integer, ContractDto> crmContractMap = new ConcurrentHashMap<>();

    // key:orderId
    private volatile Map<Integer, FcOrderPo> fcOrderPoMap = new ConcurrentHashMap<>();

    // key:scheduleId
    private volatile Map<Integer, GdSchedulePo> gdSchedulePoMap = new ConcurrentHashMap<>();

    // key:scheduleId
    private volatile Map<Integer, SsaCpmSchedulePo> ssaCpmSchedulePoMap = new ConcurrentHashMap<>();

    // key:creativeId
    private volatile Map<Long, GdCreativePo> gdCreativePoMap = new ConcurrentHashMap<>();

    // key:id
    private volatile Map<Long, SsaSplashScreenPo> ssaSplashScreenPoMap = new ConcurrentHashMap<>();

    // key:ssaCreativeId
    private volatile Map<Long, GdTopViewPo> gdTopViewPoMap = new ConcurrentHashMap<>();


    public List<Integer> getOrderIdList(DataReportQueryDto queryDto) {
        return new ArrayList<>(fcOrderPoMap.keySet());
    }

    public List<Integer> getScheduleIdList(DataReportQueryDto queryDto) {
        List<Integer> orderIdList = getOrderIdList(queryDto);
        Set<Integer> orderIdSet = new HashSet<>(orderIdList);
        return gdSchedulePoMap.values()
                .stream()
                .filter(po -> orderIdSet.contains(po.getOrderId()))
                .map(GdSchedulePo::getScheduleId)
                .collect(Collectors.toList());
    }

    public List<Long> getCreativeIdList(DataReportQueryDto queryDto) {
        Set<Long> creativeIdSet = new HashSet<>();
        List<Integer> scheduleIdList = getScheduleIdList(queryDto);
        Set<Integer> scheduleIdSet = new HashSet<>(scheduleIdList);

        gdCreativePoMap.values()
                .stream()
                .filter(po -> scheduleIdSet.contains(po.getScheduleId()))
                .map(GdCreativePo::getCreativeId)
                .filter(Utils::isPositive)
                .forEach(creativeIdSet::add);

        ssaSplashScreenPoMap.values()
                .stream()
                .filter(po -> scheduleIdSet.contains(po.getGdScheduleId()))
                .map(po -> Long.valueOf(po.getId()))
                .filter(Utils::isPositive)
                .forEach(creativeIdSet::add);
        return new ArrayList<>(creativeIdSet);
    }

    public List<Long> getNewHfCreativeIdListBySsaCreativeIdList(List<Long> creativeIdList) {
        Set<Long> creativeIdSet = new HashSet<>();
        List<GdTopViewPo> gdTopViewPoList = gdTopViewPoMap.values()
                .stream()
                .filter(item -> creativeIdList.contains(item.getSsaCreativeId()))
                .collect(Collectors.toList());

        Stream.concat(
                        gdTopViewPoList.stream().map(GdTopViewPo::getNewHfAndroidCreativeId).filter(Utils::isPositive),
                        gdTopViewPoList.stream().map(GdTopViewPo::getNewHfIosCreativeId).filter(Utils::isPositive)
                )
                .forEach(creativeIdSet::add);
        return new ArrayList<>(creativeIdSet);
    }

    public List<Integer> getSalesTypeList() {
        Set<Integer> orderProduct = new HashSet<>();
        // 直接跟据订单来即可
        fcOrderPoMap.values().stream()
                .map(FcOrderPo::getProduct)
                .forEach(orderProduct::add);
        return orderProduct.stream()
                .map(product -> {
                    if (Objects.equals(product, OrderProduct.SEARCH_CPT.getCode())) {
                        return SalesType.SEARCH_CPT.getCode();
                    }
                    return OrderProduct.getByCode(product).getSalesType().getCode();
                }).collect(Collectors.toList());
    }
}
