package com.bilibili.brand.biz.schedule.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdOrderUpdatePo implements Serializable {
    /**
     * ID
     */
    private Integer id;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 变更时间
     */
    private Timestamp mtime;

    /**
     * 软删除 0-有效 1-删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}