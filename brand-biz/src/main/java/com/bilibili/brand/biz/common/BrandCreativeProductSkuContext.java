package com.bilibili.brand.biz.common;

import com.bilibili.brand.api.creative.dto.GdCreativeDto;
import com.bilibili.brand.api.creative.dto.GdTopViewDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.enums.BrandCreativeProductLinkageTypeEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDetailDto;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/31 21:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandCreativeProductSkuContext {
    //默认值都是空对象，免去频繁判null的操作
    @Builder.Default
    private GdCreativeDto creative = new GdCreativeDto();
    @Builder.Default
    private ScheduleDto schedule = new ScheduleDto();
    @Builder.Default
    private GdOrderDto order = new GdOrderDto();
    @Builder.Default
    private GdTopViewScheduleDto topViewSchedule = new GdTopViewScheduleDto();
    @Builder.Default
    private SsaSplashScreenDetailDto splashScreen = new SsaSplashScreenDetailDto();
    @Builder.Default
    private GdTopViewDto topView = new GdTopViewDto();
    @Builder.Default
    private List<PlatformType> platformList = Lists.newLinkedList();
    @Builder.Default
    private BrandCreativeProductLinkageTypeEnum linkageType = BrandCreativeProductLinkageTypeEnum.SELF;
    @Builder.Default
    private Long creativeId = 0L;
    private boolean deleted; //是否已删除
}
