package com.bilibili.brand.biz.order.adxdao;

import com.bilibili.brand.biz.order.po.AdxOrderLaunchTimePo;
import com.bilibili.brand.biz.order.po.AdxOrderLaunchTimePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AdxOrderLaunchTimeDao {
    long countByExample(AdxOrderLaunchTimePoExample example);

    int deleteByExample(AdxOrderLaunchTimePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AdxOrderLaunchTimePo record);

    int insertBatch(List<AdxOrderLaunchTimePo> records);

    int insertUpdateBatch(List<AdxOrderLaunchTimePo> records);

    int insert(AdxOrderLaunchTimePo record);

    int insertUpdateSelective(AdxOrderLaunchTimePo record);

    int insertSelective(AdxOrderLaunchTimePo record);

    List<AdxOrderLaunchTimePo> selectByExample(AdxOrderLaunchTimePoExample example);

    AdxOrderLaunchTimePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AdxOrderLaunchTimePo record, @Param("example") AdxOrderLaunchTimePoExample example);

    int updateByExample(@Param("record") AdxOrderLaunchTimePo record, @Param("example") AdxOrderLaunchTimePoExample example);

    int updateByPrimaryKeySelective(AdxOrderLaunchTimePo record);

    int updateByPrimaryKey(AdxOrderLaunchTimePo record);
}