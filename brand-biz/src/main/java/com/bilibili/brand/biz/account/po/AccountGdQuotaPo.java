/** 
* <AUTHOR> 
* @date  2017年6月13日
*/

package com.bilibili.brand.biz.account.po;

import com.bilibili.adp.common.annotation.DatabaseColumnName;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountGdQuotaPo {

	private Integer id;
	
	@DatabaseColumnName(value="用户id", needShowForLog=false)
	private Integer accountId;
	
	@DatabaseColumnName(value="默认GD配额", needShowForLog=true)
	private Long defaultGdQuota;
	
	@DatabaseColumnName(value="本月GD调整配额", needShowForLog=true)
	private Long currentMonthAdjustQuota;
	
	@DatabaseColumnName(value="下月GD调整配额", needShowForLog=true)
	private Long nextMonthAdjustQuota;
	
	@DatabaseColumnName(value="当前月份", needShowForLog=false)
	private Integer curMonth;
}
