package com.bilibili.brand.biz.rpc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/5
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class VideoCategoryLevelDto {
    //分类所属的三级分类(真实分类)
    private String category;
    //分类所属的一级分类(正片、非正片)
    private String firstCategory;
    //分类所属的二级分类(正片、衍生正片、预告片、看点、花絮、官方衍生、合作衍生、商业广告)
    private String secondCategory;
}
