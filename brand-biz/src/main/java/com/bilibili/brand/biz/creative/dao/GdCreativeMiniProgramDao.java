package com.bilibili.brand.biz.creative.dao;

import com.bilibili.brand.biz.creative.po.GdCreativeMiniProgramPo;
import com.bilibili.brand.biz.creative.po.GdCreativeMiniProgramPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface GdCreativeMiniProgramDao {
    long countByExample(GdCreativeMiniProgramPoExample example);

    int deleteByExample(GdCreativeMiniProgramPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(GdCreativeMiniProgramPo record);

    int insertBatch(List<GdCreativeMiniProgramPo> records);

    int insertUpdateBatch(List<GdCreativeMiniProgramPo> records);

    int insert(GdCreativeMiniProgramPo record);

    int insertUpdateSelective(GdCreativeMiniProgramPo record);

    int insertSelective(GdCreativeMiniProgramPo record);

    List<GdCreativeMiniProgramPo> selectByExample(GdCreativeMiniProgramPoExample example);

    GdCreativeMiniProgramPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GdCreativeMiniProgramPo record, @Param("example") GdCreativeMiniProgramPoExample example);

    int updateByExample(@Param("record") GdCreativeMiniProgramPo record, @Param("example") GdCreativeMiniProgramPoExample example);

    int updateByPrimaryKeySelective(GdCreativeMiniProgramPo record);

    int updateByPrimaryKey(GdCreativeMiniProgramPo record);
}