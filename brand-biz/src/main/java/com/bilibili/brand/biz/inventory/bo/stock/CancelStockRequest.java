package com.bilibili.brand.biz.inventory.bo.stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 释放库存请求
 *
 * <AUTHOR>
 * @date 2025/3/7 11:17
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class CancelStockRequest extends GoblinStockRequest {
    /**
     * 批量释放库存任务
     * <p>
     * ps: 目前只会有一个元素，后续升级时，需要投放和引擎同时改造
     */
    private List<CancelTask> tasks;
}
