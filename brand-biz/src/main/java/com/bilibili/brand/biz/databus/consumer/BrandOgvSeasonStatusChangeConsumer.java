package com.bilibili.brand.biz.databus.consumer;

import com.bilibili.brand.biz.databus.dto.OgvSeasonStatusChangeMsg;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import com.bilibili.brand.biz.event_bus.IBrandEventPublisher;
import com.bilibili.brand.biz.event_bus.event.OgvEventSource;
import com.bilibili.brand.biz.event_bus.event.OgvSeasonStatusChangeEvent;
import com.bilibili.business.cmpt.idatabus.client.spring.ConsumeMessageContext;
import com.bilibili.business.cmpt.idatabus.client.spring.annotion.DataBusConsumer;
import com.bilibili.enums.OgvSeasonStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Ogv Season 状态变更
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=695822359">Ogv Season 状态变更</a>
 * <a href="http://uat-cloud.bilibili.co/databus/consumer/detail
 * ?id=6424&zone=sh004&group=SeasonStatusChange-Uat-BrandOgvSeasonStatusChange-S#sh/sh001/uat">Ogv Season 状态变更</a>
 *
 * <AUTHOR>
 * @date 2023/9/17 15:17
 */
@Slf4j
@DataBusConsumer("BrandOgvSeasonStatusChangeConsumer")
public class BrandOgvSeasonStatusChangeConsumer extends AbstractConsumer<OgvSeasonStatusChangeMsg> {
    @Autowired
    private IBrandEventPublisher<BrandEvent> brandEventPublisher;

    @Override
    protected boolean ackIfNecessary() {
        return true;
    }

    @Override
    protected void doConsume(OgvSeasonStatusChangeMsg msg, ConsumeMessageContext ctx)
            throws Exception {
        if (!"SEASON".equals(msg.getEntityType())
                || !"STATUS_UPDATED".equals(msg.getEventType())
                || !StringUtils.isNumeric(msg.getEntityId())) {
            return;
        }

        //都使用if判断，只关心特定操作，对其他类型容错
        if ("ONLINE".equals(msg.getValue())) {
            brandEventPublisher.publish(OgvSeasonStatusChangeEvent.builder()
                    .seasonId(Long.valueOf(msg.getEntityId()))
                    .status(OgvSeasonStatusEnum.ONLINE)
                    .source(OgvEventSource.MAIN_SITE)
                    .build());
        } else if ("OFFLINE".equals(msg.getValue()) || "DELETE".equals(msg.getValue())) {
            brandEventPublisher.publish(OgvSeasonStatusChangeEvent.builder()
                    .seasonId(Long.valueOf(msg.getEntityId()))
                    .status(OgvSeasonStatusEnum.OFFLINE)
                    .source(OgvEventSource.MAIN_SITE)
                    .build());
        }
    }
}
