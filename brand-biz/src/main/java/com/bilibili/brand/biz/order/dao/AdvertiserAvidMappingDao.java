package com.bilibili.brand.biz.order.dao;

import com.bilibili.brand.biz.order.po.AdvertiserAvidMappingPo;
import com.bilibili.brand.biz.order.po.AdvertiserAvidMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AdvertiserAvidMappingDao {
    long countByExample(AdvertiserAvidMappingPoExample example);

    int deleteByExample(AdvertiserAvidMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AdvertiserAvidMappingPo record);

    int insertBatch(List<AdvertiserAvidMappingPo> records);

    int insertUpdateBatch(List<AdvertiserAvidMappingPo> records);

    int insert(AdvertiserAvidMappingPo record);

    int insertUpdateSelective(AdvertiserAvidMappingPo record);

    int insertSelective(AdvertiserAvidMappingPo record);

    List<AdvertiserAvidMappingPo> selectByExample(AdvertiserAvidMappingPoExample example);

    AdvertiserAvidMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AdvertiserAvidMappingPo record, @Param("example") AdvertiserAvidMappingPoExample example);

    int updateByExample(@Param("record") AdvertiserAvidMappingPo record, @Param("example") AdvertiserAvidMappingPoExample example);

    int updateByPrimaryKeySelective(AdvertiserAvidMappingPo record);

    int updateByPrimaryKey(AdvertiserAvidMappingPo record);
}