/**
 * 
 * <AUTHOR>
 * @date 2017/04/25
 *
 */
package com.bilibili.brand.biz.resource.po;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountGroupCmMarkPo {

	/** 自增ID **/
	private Integer id;

	/** 用户组ID **/
	private Integer accountGroupId;

	/** 角标ID **/
	private Integer cmMarkId;

	/** 软删除，0是有效，1是删除 **/
	private Integer isDeleted;

	/** 添加时间 **/
	private Date addTime;

	/** 更新时间 **/
	private Date updateTime;

	private Integer markType;

}
