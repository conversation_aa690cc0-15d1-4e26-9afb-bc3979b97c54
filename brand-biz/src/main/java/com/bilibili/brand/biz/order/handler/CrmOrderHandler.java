package com.bilibili.brand.biz.order.handler;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.crm.platform.api.order.dto.*;
import com.bilibili.crm.platform.api.statusmachine.order.enums.OrderBusStatus;
import com.bilibili.crm.platform.soa.ISoaCrmOrderService;
import com.bilibili.crm.platform.soa.dto.SoaCrmOrderDto;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Set;

/**
 * Created by fanwenbin on 2019/5/5.
 */
@Component
public class CrmOrderHandler {
    private final static Logger LOGGER = LoggerFactory.getLogger(CrmOrderHandler.class);
    @Autowired
    private ISoaCrmOrderService crmOrderService;

    //无法进行写操作的crm订单状态
    private static final Set<Integer> CAN_NOT_WRITE_STATUS_SET = Sets.newHashSet(OrderBusStatus.DELETED.getCode(),
            OrderBusStatus.COMPLETED.getCode());

    public Integer createCrmOrder(String orderName, Integer resourceType, Operator operator, Integer crmContractId, OrderProduct orderProduct) {
        try {
            switch (orderProduct) {
                case CPT:
                case TOP_FLOW_GD:
                case STANDARD_LIVE_CPT:
                    return crmOrderService.createOrder(NewOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .salesType(SalesType.CPT.getCode())
                            .build(), operator);
                case LIVE_CPT:
                    return crmOrderService.createLiveOrder(NewOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .salesType(SalesType.CPT.getCode())
                            .build(), operator);
                case GD_CPM:
                case FLY_GD:
                case OTT_GD:
                case PDB:
                case TOP_VIEW_GD_PLUS:
                case OGV_GD:
                case OGV_CPT:
                    return crmOrderService.createGdOrder(NewGdOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .type(orderProduct.getCrmOrderType())
                            .build(), operator);
                case SSA_CPT:
                case SSA_CPT_PLUS:
                case COMIC_SSA_CPT:
                    return crmOrderService.createSsaOrder(NewSsaOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .amount(0L)
                            .build(), operator);
                case SSA_GD:
                case SSA_GD_PLUS:
                case SSA_OTT_GD:
                case SSA_PDB:
                    return crmOrderService.createSsaGdOrder(NewSsaOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .amount(0L)
                            .crmOrderType(orderProduct.getCrmOrderType())
                            .build(), operator);
                case TOP_VIEW_CPT:
                case TOP_VIEW_CPT_PLUS:
                    return crmOrderService.createTopViewOrder(NewTopviewOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .amount(0L)
                            .build(), operator);
                case SEARCH_CPT:
                    return crmOrderService.createOrder(NewOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .salesType(SalesType.SEARCH_CPT.getCode())
                            .build(), operator);
                case SSA_OTT_CPT:
                    return crmOrderService.createBrandOrder(NewBrandOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .type(OrderProduct.SSA_OTT_CPT.getCrmOrderType())
                            .build(), operator);

                case SSA_CPM:
                case SSA_PD:
                case PD:
                    return crmOrderService.createBrandOrder(NewBrandOrderDto.builder()
                            .crmContractId(crmContractId)
                            .explanation(orderName)
                            .resourceType(resourceType)
                            .type(orderProduct.getCrmOrderType())
                            .build(), operator);
                case SSA_OTT_PD:
                default:
                    return 0;
            }
        } catch (Exception e) {
            LOGGER.error("crmOrderService.createOrder.error", e);
            throw e;
        }
    }

    public void updateCrmOrder(Integer crmOrderId, String orderName, OrderProduct orderProduct) {
        try {
            switch (orderProduct) {
                case CPT:
                case TOP_FLOW_GD:
                case SEARCH_CPT:
                case SSA_OTT_CPT:
                case SSA_OTT_GD:
                case LIVE_CPT:
                case TOP_VIEW_GD_PLUS:
                case OGV_CPT:
                case OGV_GD:
                    crmOrderService.updateOrderName(crmOrderId, orderName);
                    break;
                case GD_CPM:
                case FLY_GD:
                case OTT_GD:
                case PDB:
                    crmOrderService.updateGdOrderName(crmOrderId, orderName);
                    break;
                case SSA_CPT:
                case SSA_CPT_PLUS:
                case COMIC_SSA_CPT:
                    crmOrderService.updateSsaOrderName(crmOrderId, orderName);
                    break;
                case SSA_GD:
                case SSA_GD_PLUS:
                    crmOrderService.updateSsaGdOrderName(crmOrderId, orderName);
                    break;
                case TOP_VIEW_CPT:
                case TOP_VIEW_CPT_PLUS:
                    crmOrderService.updateTopViewOrderName(crmOrderId, orderName);
                    break;
                case SSA_CPM:
                case SSA_PD:
                case SSA_PDB:
                case PD:
                    crmOrderService.updateBrandOrderName(crmOrderId, orderProduct.getCrmOrderType(), orderName);
                case SSA_OTT_PD:
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("crmOrderService.updateGdOrderName.error", e);
            throw e;
        }
    }

    public void updateGdCrmOrder(UpdateGdOrderDto updateGdOrderDto, Operator operator) {
        try {
            crmOrderService.updateGdOrder(updateGdOrderDto, operator);
        } catch (Exception e) {
            LOGGER.error("CrmOrderHandler.updateGdCrmOrder.error：[{}]", ExceptionUtils.getStackTrace(e));
            throw e;
        }
    }

    public void forbiddenGdOrder(Integer crmOrderId, Operator operator, OrderProduct orderProduct) {
        try {
            switch (orderProduct) {
                case CPT:
                case TOP_FLOW_GD:
                case LIVE_CPT:
                case SSA_OTT_CPT:
                case SEARCH_CPT:
                case SSA_OTT_GD:
                    crmOrderService.forbiddenOrder(crmOrderId, operator);
                    break;
                case GD_CPM:
                case FLY_GD:
                case OTT_GD:
                case PDB:
                case TOP_VIEW_GD_PLUS:
                    crmOrderService.forbiddenGdOrder(crmOrderId, operator);
                    break;
                case SSA_CPT:
                case SSA_CPT_PLUS:
                case COMIC_SSA_CPT:
                    crmOrderService.forbiddenSsaOrder(crmOrderId, operator);
                    break;
                case SSA_GD:
                case SSA_GD_PLUS:
                    crmOrderService.forbiddenSsaGdOrder(crmOrderId, operator);
                    break;
                case TOP_VIEW_CPT:
                case TOP_VIEW_CPT_PLUS:
                    crmOrderService.forbiddenTopViewOrder(crmOrderId, operator);
                    break;
                case SSA_CPM:
                case SSA_PD:
                case PD:
                case SSA_PDB:
                    crmOrderService.forbiddenBrandOrder(crmOrderId, orderProduct.getCrmOrderType(), operator);
                case SSA_OTT_PD:
                    break;
            }

        } catch (Exception e) {
            LOGGER.error("crmOrderService.forbiddenGdOrder.error", e);
            throw e;
        }
    }

    public void deleteCrmOrder(Integer crmOrderId, OrderProduct orderProduct) {
        try {
            switch (orderProduct) {
                case CPT:
                case TOP_FLOW_GD:
                case SEARCH_CPT:
                case LIVE_CPT:
                case SSA_OTT_CPT:
                    crmOrderService.deleteOrder(crmOrderId);
                    break;
                case GD_CPM:
                case FLY_GD:
                case OTT_GD:
                case PDB:
                case TOP_VIEW_GD_PLUS:
                    crmOrderService.deleteGdOrder(crmOrderId);
                    break;
                case SSA_CPT:
                case SSA_CPT_PLUS:
                case COMIC_SSA_CPT:
                    crmOrderService.deleteSsaOrder(crmOrderId);
                    break;
                case SSA_GD:
                case SSA_OTT_GD:
                case SSA_GD_PLUS:
                    crmOrderService.deleteSsaGdOrder(crmOrderId);
                    break;
                case TOP_VIEW_CPT:
                case TOP_VIEW_CPT_PLUS:
                    crmOrderService.deleteTopViewOrder(crmOrderId);
                    break;
                case SSA_CPM:
                case SSA_PDB:
                case SSA_PD:
                case PD:
                    crmOrderService.deleteBrandOrder(crmOrderId, orderProduct.getCrmOrderType());
                case SSA_OTT_PD:
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("soaCrmOrderService.deleteGdOrder.error", e);
        }
    }

    /**
     * 校验crm订单状态是否是已完成或者已删除，如果是，则无法继续操作（尤其在订单编辑、排期创建、排期编辑时会作为前置检查条件）
     */
    public void validateCrmOrderBusStatus(Integer crmOrderId) {
        SoaCrmOrderDto crmOrderDto = crmOrderService.getOrderById(crmOrderId);
        Assert.notNull(crmOrderDto, String.format("CRM订单[%d]不存在", crmOrderId));
        Assert.isTrue(!CAN_NOT_WRITE_STATUS_SET.contains(crmOrderDto.getBusStatus()), String.format("CRM订单[%d]已完成或已删除，暂不支持操作，请核实后重试", crmOrderId));
    }

}
