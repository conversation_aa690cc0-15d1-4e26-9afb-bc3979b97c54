package com.bilibili.brand.biz.bfs;

import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.exception.BfsExceptionCode;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.bjcom.bfs.BFSClient;
import com.bilibili.bjcom.bfs.BFSUploadResult;
import com.bilibili.bjcom.bfs.exception.BFSUploadException;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/7/31 21:23
 */
@Slf4j
@Service
public class BrandBfsService implements IBfsService {

    private final static String HTTP_PREFIX = "http://";

    private final static String HTTPS_PREFIX = "https://";

    @Autowired
    private BFSClient brandBFSClient;

    /**
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成，配合{@link com.bilibili.enums.BrandBfsCategoryEnum}使用
     * @return
     * @throws ServiceException
     * @see com.bilibili.enums.BrandBfsCategoryEnum
     */
    @Override
    public BfsUploadResult upload(String category, String fileName, byte[] data)
            throws ServiceException {
        log.info("upload param category-{} fileName-{}", category, fileName);
        if (Strings.isNullOrEmpty(category)
                || Strings.isNullOrEmpty(fileName)
                || data == null || data.length < 1) {
            throw new ServiceException(BfsExceptionCode.REQUIRED_PARAM);
        }
        return basicUpload(category, fileName, data);
    }

    /**
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成，配合{@link com.bilibili.enums.BrandBfsCategoryEnum}使用
     * @return
     * @throws ServiceException
     * @see com.bilibili.enums.BrandBfsCategoryEnum
     */
    @Override
    public BfsUploadResult upload(String category, File file) throws ServiceException {
        log.info("upload param category-{} file-{}", category, file);
        if (Strings.isNullOrEmpty(category) || file == null) {
            throw new ServiceException(BfsExceptionCode.REQUIRED_PARAM);
        }
        try {
            String fileName = file.getName();
            byte[] fileData = FileUtils.readFileToByteArray(file);
            return basicUpload(category, fileName, fileData);
        } catch (IOException e) {
            log.error("BFS upload failed", e);
            throw new ServiceException(BfsExceptionCode.UPLOAD_FAILED);
        }
    }

    /**
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成，配合{@link com.bilibili.enums.BrandBfsCategoryEnum}使用
     * @return
     * @throws ServiceException
     * @see com.bilibili.enums.BrandBfsCategoryEnum
     */
    @Override
    public BfsUploadResult uploadWithOriginalFilename(String category, String fileName, byte[] data) throws ServiceException {
        log.info("upload param category-{} fileName-{}", category, fileName);
        if (Strings.isNullOrEmpty(category)
                || Strings.isNullOrEmpty(fileName)
                || data == null || data.length < 1) {
            throw new ServiceException(BfsExceptionCode.REQUIRED_PARAM);
        }
        return this.basicUploadWithOriginalFilename(category, fileName, data);
    }


    /**
     * @param category 文件分类，影响生成URL，由字母、数字、下划线组成，配合{@link com.bilibili.enums.BrandBfsCategoryEnum}使用
     * @return
     * @throws ServiceException
     * @see com.bilibili.enums.BrandBfsCategoryEnum
     */
    @Override
    public BfsUploadResult uploadWithOriginalFilename(String category, File file) throws ServiceException {
        log.info("upload param category-{} file-{}", category, file);
        if (Strings.isNullOrEmpty(category) || file == null) {
            throw new ServiceException(BfsExceptionCode.REQUIRED_PARAM);
        }
        try {
            String fileName = file.getName();
            byte[] fileData = FileUtils.readFileToByteArray(file);
            return this.basicUploadWithOriginalFilename(category, fileName, fileData);
        } catch (IOException e) {
            log.error("BFS upload failed", e);
            throw new ServiceException(BfsExceptionCode.UPLOAD_FAILED);
        }
    }

    @Override
    public void verifyUrl(String url, String hashCode) throws ServiceException {
        if (Strings.isNullOrEmpty(url) || Strings.isNullOrEmpty(hashCode)) {
            throw new ServiceException(BfsExceptionCode.REQUIRED_PARAM);
        }
        String decodeUrl = new String(Base64Utils.decodeFromString(hashCode));
        if (!url.equals(decodeUrl)) {
            throw new ServiceException(BfsExceptionCode.HASHCODE_ERROR);
        }
    }

    @Override
    public BfsUploadResult uploadWithSercurity(String category, String fileName, byte[] data)
            throws ServiceException {
        throw new UnsupportedOperationException();
    }

    @Override
    public BfsUploadResult uploadWithSercurity(String category, File file) throws ServiceException {
        throw new UnsupportedOperationException();
    }

    @Override
    public String getTokenByUrl(String url) throws ServiceException {
        throw new UnsupportedOperationException();
    }


    private BfsUploadResult basicUploadWithOriginalFilename(String category, String fileName, byte[] data)
            throws ServiceException {
        try {
            BFSUploadResult result = brandBFSClient.uploadWithOriginalFilename(category, fileName.toLowerCase(), data);
            String url = result.getLocation();
            url = url.replace(HTTP_PREFIX, HTTPS_PREFIX);
            return BfsUploadResult.builder().url(url).token("").hashCode(Base64Utils.encodeToString(url.getBytes())).md5(DigestUtils.md5DigestAsHex(data)).build();
        } catch (BFSUploadException e) {
            log.error("BFS upload failed", e);
            throw new ServiceException(BfsExceptionCode.UPLOAD_FAILED);
        }
    }

    private BfsUploadResult basicUpload(String category, String fileName, byte[] data)
            throws ServiceException {
        try {
            BFSUploadResult result = brandBFSClient.upload(category, fileName.toLowerCase(), data);
            String url = result.getLocation();
            url = url.replace(HTTP_PREFIX, HTTPS_PREFIX);
            return BfsUploadResult.builder().url(url).token("").hashCode(Base64Utils.encodeToString(url.getBytes())).md5(DigestUtils.md5DigestAsHex(data)).build();
        } catch (BFSUploadException e) {
            log.error("BFS upload failed", e);
            throw new ServiceException(BfsExceptionCode.UPLOAD_FAILED);
        }
    }
}
