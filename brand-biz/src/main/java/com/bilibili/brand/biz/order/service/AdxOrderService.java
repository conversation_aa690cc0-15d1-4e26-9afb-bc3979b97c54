package com.bilibili.brand.biz.order.service;

import cn.hutool.core.bean.BeanUtil;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.AdxOrderType;
import com.bilibili.brand.api.common.enums.ModifyType;
import com.bilibili.brand.api.common.enums.Module;
import com.bilibili.brand.api.log.bean.LogOperatorBean;
import com.bilibili.brand.api.log.service.ILogOperatorService;
import com.bilibili.brand.api.order.dto.AdxOrderCreativeBackupSettingDto;
import com.bilibili.brand.api.order.dto.AdxOrderDto;
import com.bilibili.brand.api.order.dto.BidderConfigDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IAdxOrderService;
import com.bilibili.brand.api.order.service.IBidderConfigService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.*;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.order.adxdao.AdxBidderSourceMappingDao;
import com.bilibili.brand.biz.order.adxdao.AdxOrderCustomDao;
import com.bilibili.brand.biz.order.adxdao.AdxOrderDao;
import com.bilibili.brand.biz.order.adxdao.AdxOrderLaunchTimeDao;
import com.bilibili.brand.biz.order.adxdao.AdxStatOrderDayDao;
import com.bilibili.brand.biz.order.po.*;
import com.bilibili.brand.platform.report.api.dto.StatScheduleDto;
import com.bilibili.brand.platform.report.biz.service.StatScheduleService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.BeanCopyUtil;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.common.CrmOrderResourceType;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2018/3/6.
 */
@Service
public class AdxOrderService implements IAdxOrderService {

    @Autowired
    private IBidderConfigService bidderConfigService;

    @Autowired
    private AdxOrderDao adxOrderDao;

    @Autowired
    private AdxOrderCustomDao adxOrderCustomDao;

    @Autowired
    private AdxOrderLaunchTimeDao adxOrderLaunchTimeDao;

    @Autowired
    private AdxBidderSourceMappingDao sourceMappingDao;

    @Autowired
    private AdxStatOrderDayDao adxStatOrderDayDao;

    @Autowired
    private ILogOperatorService logOperatorService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private StatScheduleService statScheduleService;
    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private ISoaCrmContractService crmContractService;

    private final static Logger LOGGER = LoggerFactory.getLogger(AdxOrderService.class);

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer create(AdxOrderDto adxOrderDto, Operator operator) throws ServiceException {
        Assert.notNull(adxOrderDto, "adxOrderDto is not null");
        Assert.notNull(adxOrderDto.getBidderId(), "bidder id is not null");
        Assert.hasText(adxOrderDto.getOrderName(), "订单名称不能为空");
        Assert.notNull(adxOrderDto.getOrderType(), "订单类型不能为空");
        AdxOrderType.getByCode(adxOrderDto.getOrderType());

        Assert.notNull(adxOrderDto.getContractNumber(), "合同号不能为空");
        Assert.notNull(adxOrderDto.getResourceType(), "资源类型不能为空");
//        Assert.isTrue(orderType.getCode().equals(AdxOrderType.SSA_CPT.getCode()), "目前只支持程序化闪屏");
        CrmOrderResourceType.getByCode(adxOrderDto.getResourceType());

        checkBidderIdWithDealId(adxOrderDto.getBidderId(), adxOrderDto.getDealId());

        AdxOrderPo adxOrderPo = BeanCopyUtil.transform(adxOrderDto, AdxOrderPo.class);
        adxOrderDao.insertSelective(adxOrderPo);

        LogOperatorBean logOperatorBean = LogOperatorBean.builder()
                .modifyType(ModifyType.ADD_ORDER)
                .module(Module.ORDER)
                .objId(adxOrderPo.getId())
                .obj(adxOrderPo)
                .build();
        logOperatorService.insertLog(operator, logOperatorBean);
        return adxOrderPo.getId();
    }

    public String checkBidderIdWithDealId(Integer bidderId, Long dealId) throws ServiceException {
        BidderConfigDto bidderConfigDto = bidderConfigService.load(bidderId);

        Assert.notNull(bidderConfigDto, "bidder不存在" + bidderId);
        if (Utils.isPositive(dealId)) {
            Assert.isTrue(checkAdxOrderIsExistByBidderIdAndDealId(bidderId, dealId),
                    "Dsp下的 dealId 已经存在");
        }
        return bidderConfigDto.getName();
    }

    @Override
    public Boolean checkAdxOrderIsExistByBidderIdAndDealId(Integer bidderId, Long dealId) {
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or().andDealIdEqualTo(dealId).andBidderIdEqualTo(bidderId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);
        return CollectionUtils.isEmpty(adxOrderPos);
    }

    @Override
    public AdxOrderDto getAdxOrderDtoByExample(Integer mappingOrderId) {
        Assert.notNull(mappingOrderId, "mappingOrderId is not null");
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or().andMappingOrderIdEqualTo(mappingOrderId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);
        AdxOrderDto adxOrderRecord = new AdxOrderDto();
        if (null != adxOrderPos && adxOrderPos.size() > 0) {
            adxOrderRecord = BeanCopyUtil.transform(adxOrderPos.get(0), AdxOrderDto.class);
        }
        return adxOrderRecord;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Integer mappingId, Operator operator) {
        try {
            Assert.notNull(mappingId, "mappingId can not be null");
            AdxOrderDto adxOrderDto = this.getAdxOrderDtoByExample(mappingId);
            Assert.notNull(adxOrderDto, "AdxSplash Record can not be null");

            AdxOrderPo delete = new AdxOrderPo();
            delete.setId(adxOrderDto.getId());
            delete.setIsDeleted(IsDeleted.DELETED.getCode());
            adxOrderDao.updateByPrimaryKeySelective(delete);


            LogOperatorBean logOperatorBean = LogOperatorBean.builder()
                    .modifyType(ModifyType.DELETE_ORDER)
                    .module(Module.ORDER)
                    .objId(adxOrderDto.getId())
                    .obj(adxOrderDto)
                    .build();
            logOperatorService.insertLog(operator, logOperatorBean);
        } catch (Exception e) {
            LOGGER.error("Delete adxOrder Exception! [{}]", e.getMessage());
        }

    }

    @Override
    public void updateSsaPDInfo(int bidderId, long dealId,
                                Integer showStyle, Long dayDeliverImpressionLimit) {

        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or().andBidderIdEqualTo(bidderId)
                .andDealIdEqualTo(dealId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);

        Assert.notEmpty(adxOrderPos, "AdxSplash Record can not be null");
        AdxOrderPo adxOrderPo = adxOrderPos.get(0);

        adxOrderPo.setDayDeliverImpressionLimit(dayDeliverImpressionLimit);
        if (showStyle != null) {
            adxOrderPo.setSsaShowStyle(showStyle);
        }
        adxOrderDao.updateByPrimaryKeySelective(adxOrderPo);

    }

    @Override
    public Integer updateAdxOrderInfo(AdxOrderDto adxOrderDto, Operator operator) {
        Assert.notNull(adxOrderDto.getBidderId(), "bidder id is null");
        Assert.notNull(adxOrderDto.getDealId(), "deal id is null");
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or().andBidderIdEqualTo(adxOrderDto.getBidderId())
                .andDealIdEqualTo(adxOrderDto.getDealId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);

        Assert.notEmpty(adxOrderPos, "Adx order Record is null");
        AdxOrderPo old = adxOrderPos.get(0);

        AdxOrderPo newOne = new AdxOrderPo();
        BeanUtils.copyProperties(adxOrderDto, newOne);
        newOne.setId(old.getId());

        adxOrderDao.updateByPrimaryKeySelective(newOne);
        return old.getId();
    }

    @Override
    public void deletePd(Integer bidderId, Long dealId) {
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or().andBidderIdEqualTo(bidderId)
                .andDealIdEqualTo(dealId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);

        Assert.notEmpty(adxOrderPos, "AdxSplash Record can not be null");
        AdxOrderPo adxOrderPo = adxOrderPos.get(0);
        adxOrderPo.setIsDeleted(IsDeleted.DELETED.getCode());
        adxOrderDao.updateByPrimaryKeySelective(adxOrderPo);

        AdxOrderLaunchTimePoExample timePoExample = new AdxOrderLaunchTimePoExample();
        timePoExample.or().andAdxOrderIdEqualTo(adxOrderPo.getId());
        adxOrderLaunchTimeDao.updateByExampleSelective(AdxOrderLaunchTimePo.builder()
                .isDeleted(IsDeleted.DELETED.getCode()).build(), timePoExample);
    }

    @Override
    public void deletePdb(Integer scheduleId) {
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or()
                .andMappingIdEqualTo(Long.valueOf(scheduleId))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        AdxOrderPo adxOrderPo = new AdxOrderPo();
        adxOrderPo.setIsDeleted(1);

        adxOrderDao.updateByExampleSelective(adxOrderPo, adxOrderPoExample);
    }

    /**
     * @see BidderConfigService#checkBidderTemplate(Integer, Integer)
     * @param bidderId
     * @param templateId
     */
    @Deprecated
    @Override
    public void checkBidderTemplate(Integer bidderId, Integer templateId) {
        Assert.notNull(bidderId, "检查dsp配置模板的dspId不能为空");
        Assert.notNull(templateId, "检查dsp配置模板的模板id不能为空");
        AdxBidderSourceMappingPoExample poExample = new AdxBidderSourceMappingPoExample();
        poExample.or().andBidderIdEqualTo(bidderId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxBidderSourceMappingPo> mappingPos = sourceMappingDao.selectByExample(poExample);

        Assert.notEmpty(mappingPos, "当前dsp" + bidderId + "上没有配置模板,请去adx后台配置");
        AtomicReference<Boolean> contains = new AtomicReference<>(false);
        mappingPos.forEach(mappingPo -> {
            if (mappingPo.getTemplateId().equals(templateId)) {
                contains.set(true);
            }
        });
        Assert.isTrue(contains.get(), "当前dsp不支持此模板,模板id" + templateId);
    }

    @Override
    public void setOrderCreativeBackup(List<AdxOrderCreativeBackupSettingDto> settings) {
        if (CollectionUtils.isEmpty(settings)) {
            return;
        }
        settings.stream()
                .filter(setting -> Utils.isPositive(setting.getOrderId())
                        && (setting.getBackupRatio() != null && setting.getBackupRatio() >= 0
                        && setting.getBackupRatio() <= 100))
                .forEach(setting -> {
                    AdxOrderPo order = AdxOrderPo.builder()
                            .id(setting.getOrderId())
                            .backupRatio(setting.getBackupRatio())
                            .build();
                    this.adxOrderDao.updateByPrimaryKeySelective(order);
                });
    }

    @Override
    public Map<Integer, List<AdxScheduleStatDto>> get(AdxOrderType orderType, Integer bidderId, List<Integer> scheduleIds) {
        Map<Integer, List<AdxScheduleStatDto>> statMap = new HashMap<>();
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return statMap;
        }
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or()
                .andMappingIdIn(scheduleIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                .andBidderIdEqualTo(bidderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = adxOrderDao.selectByExample(adxOrderPoExample);
        if (CollectionUtils.isEmpty(adxOrderPos)) {
            return statMap;
        }

        Map<Integer, Long> adxOrderId2ScheduleId = adxOrderPos.stream().collect(Collectors.toMap(AdxOrderPo::getId,
                AdxOrderPo::getMappingId));
        Map<Integer, Integer> adxScheduleId2OrderId = adxOrderPos.stream().collect(
                Collectors.toMap(o -> o.getMappingId().intValue(), AdxOrderPo::getId));

        List<AdxOrderStatDto> adxOrderStatDtos;
        if (AdxOrderType.SSA_PD == orderType) {
            adxOrderStatDtos = this.getAdxStatOrderDaysFromAdx(adxOrderPos.stream()
                    .map(AdxOrderPo::getId)
                    .collect(Collectors.toList()));
        } else {
            //取实际查到的排期，而不是参数中的排期
            adxOrderStatDtos = this.getAdxStatOrderDaysFromAdStat(Lists.newArrayList(adxScheduleId2OrderId.keySet()));
            adxOrderStatDtos.forEach(stat -> {
                //修正为adx的订单ID（品牌 scheduleId -->  ADX orderId）
                stat.setOrderId(adxScheduleId2OrderId.getOrDefault(stat.getScheduleId(), 0));
            });
        }

        //因为adx的测试数据很难造，所以在代码里mock
        //通过systemConfig配置来制造mock数据
        List<String> adxStatMockData = systemConfigService.getValueReturnList(SystemConfigEnum
                .ADX_STAT_MOCK_DATA.getCode());
        Map<Integer, List<Pair<Timestamp, Integer>>> scheduleId2GroupDateWithShow = new HashMap<>();
        adxStatMockData.forEach(mockData -> {
            String[] data = mockData.split("\\|");
            List<Pair<Timestamp, Integer>> GroupDateWithShow = scheduleId2GroupDateWithShow
                    .getOrDefault(Integer.valueOf(data[0]), Lists.newArrayList());
            GroupDateWithShow.add(Pair.of(TimeUtil.isoStrToTimestamp(data[1]),
                    Integer.valueOf(data[2])));
            scheduleId2GroupDateWithShow.put(Integer.valueOf(data[0]), GroupDateWithShow);
        });
        scheduleIds.forEach(scheduleId -> {
            if (scheduleId2GroupDateWithShow.containsKey(scheduleId)) {
                List<Pair<Timestamp, Integer>> groupDateWithShowList = scheduleId2GroupDateWithShow
                        .getOrDefault(scheduleId, Lists.newArrayList());
                adxOrderStatDtos.addAll(groupDateWithShowList.stream().map(groupDateWithShow ->
                        AdxOrderStatDto.builder()
                                .orderId(adxScheduleId2OrderId.get(scheduleId))
                                .actualShow(Long.valueOf(groupDateWithShow.getSecond()))
                                .actualClick(11L)
                                .groupDate(groupDateWithShow.getFirst())
                                .build()).collect(Collectors.toList()));
            }
        });

        adxOrderStatDtos.forEach(stat -> {
            Integer orderId = Math.toIntExact(stat.getOrderId());
            Integer mappingId = Math.toIntExact(adxOrderId2ScheduleId.getOrDefault(orderId, 0L));
            List<AdxScheduleStatDto> statDtos = statMap.getOrDefault(mappingId, new ArrayList<>());
            statDtos.add(AdxScheduleStatDto.builder()
                    .actualShow(Math.toIntExact(stat.getActualShow()))
                    .actualClick(Math.toIntExact(stat.getActualClick()))
                    .groupDate(stat.getGroupDate()).build());

            statMap.put(mappingId, statDtos);
        });

        return statMap;
    }

    @Override
    public void updateBidderSourceIdByMappingId(Long mappingId, String bidderSourceId, Operator operator) {
        Assert.notNull(mappingId, "mappingId can not be null");
        Assert.isTrue(StringUtils.hasLength(bidderSourceId), "bidderSourceId can not be null");
        Assert.notNull(operator, "operator can not be null");

        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or()
                .andMappingIdEqualTo(mappingId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = this.adxOrderDao.selectByExample(adxOrderPoExample);
        Assert.isTrue(!CollectionUtils.isEmpty(adxOrderPos), "订单不存在");

        AdxOrderPo po = adxOrderPos.get(0);
        AdxOrderPo toUpdatePo = new AdxOrderPo();
        toUpdatePo.setId(po.getId());
        toUpdatePo.setBidderSourceId(bidderSourceId);
        this.adxOrderDao.updateByPrimaryKeySelective(toUpdatePo);

        LogOperatorBean logOperatorBean = LogOperatorBean.builder()
                .modifyType(ModifyType.UPDATE_ORDER)
                .module(Module.ORDER)
                .objId(po.getId())
                .obj(AdxOrderDto.builder()
                        .id(po.getId())
                        .bidderSourceId(bidderSourceId)
                        .build())
                .build();
        logOperatorService.insertLog(operator, logOperatorBean);
    }

    @Override
    public AdxOrderDto getAdxOrderByMappingId(Integer bidderId, Integer mappingOrderId, Long mappingId) {
        Assert.notNull(bidderId, "bidderId can not be null");
        Assert.notNull(mappingOrderId, "mappingOrderId can not be null");
        Assert.notNull(mappingId, "mappingId can not be null");
        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or()
                .andMappingIdEqualTo(mappingId)
                .andMappingOrderIdEqualTo(mappingOrderId)
                .andBidderIdEqualTo(bidderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = this.adxOrderDao.selectByExample(adxOrderPoExample);
        return CollectionUtils.isEmpty(adxOrderPos)
                ? null : BeanCopyUtil.transform(adxOrderPos.get(0), AdxOrderDto.class);
    }

    @Override
    public List<AdxOrderDto> getAdxOrderByMappingIds(List<AdxOrderDto> mappingList) {
        if (CollectionUtils.isEmpty(mappingList)) {
            return Lists.newLinkedList();
        }
        List<AdxOrderPo> mappingPoList = BeanUtil.copyToList(mappingList, AdxOrderPo.class);
        List<AdxOrderPo> adxOrderPoList = adxOrderCustomDao.selectByMappingIdList(mappingPoList);
        return BeanUtil.copyToList(adxOrderPoList, AdxOrderDto.class);
    }

    @Override
    public void migrateOrderByMappingId(Long mappingId, GdOrderDto gdOrderDto, Operator operator) {
        Assert.notNull(gdOrderDto, "gdOrderDto can not be null");
        Assert.notNull(mappingId, "mappingId can not be null");
        Integer targetMappingOrderId = gdOrderDto.getOrderId();
        Assert.notNull(targetMappingOrderId, "targetOrderId can not be null");
        Assert.notNull(operator, "operator can not be null");

        AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
        adxOrderPoExample.or()
                .andMappingIdEqualTo(mappingId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxOrderPo> adxOrderPos = this.adxOrderDao.selectByExample(adxOrderPoExample);
        if (CollectionUtils.isEmpty(adxOrderPos)) {
            return;
        }
        ContractDto contract = crmContractService.getContractById(gdOrderDto.getCrmContractId());
        Assert.notNull(contract, "合同不存在");

        AdxOrderPo po = adxOrderPos.get(0);
        AdxOrderPo toUpdatePo = new AdxOrderPo();
        toUpdatePo.setId(po.getId());
        toUpdatePo.setMappingOrderId(targetMappingOrderId);
        toUpdatePo.setCrmOrderId(gdOrderDto.getCrmOrderId());
        toUpdatePo.setContractId(gdOrderDto.getCrmContractId());
        toUpdatePo.setContractNumber(contract.getContractNumber());
        toUpdatePo.setOrderName(gdOrderDto.getOrderName());
        toUpdatePo.setBidderId(gdOrderDto.getBidderId());

        this.adxOrderDao.updateByPrimaryKeySelective(toUpdatePo);

        LogOperatorBean logOperatorBean = LogOperatorBean.builder()
                .modifyType(ModifyType.UPDATE_ORDER)
                .module(Module.ORDER)
                .objId(po.getId())
                .obj(AdxOrderDto.builder()
                        .id(po.getId())
                        .mappingOrderId(targetMappingOrderId)
                        .build())
                .build();
        logOperatorService.insertLog(operator, logOperatorBean);
    }

    @Override
    public void insertUpdateAdxOrderLaunchTime(Integer adxOrderId, List<SplitDaysImpressDto> splitDaysImpress) {
        AdxOrderLaunchTimePoExample poExample = new AdxOrderLaunchTimePoExample();
        poExample.or().andAdxOrderIdEqualTo(adxOrderId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<AdxOrderLaunchTimePo> timePos = adxOrderLaunchTimeDao.selectByExample(poExample);
        if (!CollectionUtils.isEmpty(timePos)) {
            adxOrderLaunchTimeDao.updateByExampleSelective(AdxOrderLaunchTimePo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode()).build(), poExample);
        }
        for (SplitDaysImpressDto daysImpress : splitDaysImpress) {
            AdxOrderLaunchTimePo launchTimePo = AdxOrderLaunchTimePo.builder()
                    .adxOrderId(adxOrderId)
                    .startTime(daysImpress.getBeginTime())
                    .endTime(daysImpress.getEndTime())
                    .impression(daysImpress.getImpressionCpm())
                    .build();
            adxOrderLaunchTimeDao.insertUpdateSelective(launchTimePo);
        }
    }

    /**
     * 从adx库中查询
     *
     * @param orderIds adx_order的订单ID
     * @return
     */
    private List<AdxOrderStatDto> getAdxStatOrderDaysFromAdx(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Lists.newLinkedList();
        }
        //去除andBidderIdEqualTo(bidderId)校验，因为历史原因AdxStatOrderDay落的bidderId可能不是真实的dspId（比如闪屏PD）
        AdxStatOrderDayPoExample statOrderDayPoExample = new AdxStatOrderDayPoExample();
        statOrderDayPoExample.or()
                .andOrderIdIn(orderIds.stream().map(Long::valueOf).collect(Collectors.toList()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AdxStatOrderDayPo> statOrderDayPos = adxStatOrderDayDao.selectByExample(statOrderDayPoExample);
        return CollectionUtils.isEmpty(statOrderDayPos)
                ? Lists.newLinkedList()
                : statOrderDayPos.stream()
                .map(stat ->
                        AdxOrderStatDto.builder()
                                .orderId(stat.getOrderId().intValue())
                                .actualShow(Long.valueOf(stat.getShowCount()))
                                .actualClick(Long.valueOf(stat.getClickCount()))
                                .groupDate(stat.getGroupTime())
                                .build())
                .collect(Collectors.toList());
    }

    /**
     * 从ad_stat库中查询
     *
     * @param scheduleIds 品牌排期ID
     * @return
     */
    private List<AdxOrderStatDto> getAdxStatOrderDaysFromAdStat(List<Integer> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Lists.newLinkedList();
        }
        List<StatScheduleDto> statScheduleDtos;
        try {
            statScheduleDtos = statScheduleService.getInScheduleIdsGroupByDay(scheduleIds,
                    null,
                    Utils.getEndSecondOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)));
        } catch (ServiceException e) {
            LOGGER.error("getAdxStatOrderDaysFromAdStat error,scheduleIds:{}", scheduleIds, e);
            return Lists.newLinkedList();
        }
        return statScheduleDtos.stream()
                .map(stat ->
                        AdxOrderStatDto.builder()
                                .scheduleId(stat.getScheduleId())
                                .actualShow(stat.getShowCount())
                                .actualClick(Long.valueOf(stat.getClickCount()))
                                .groupDate(TimeUtil.isoStrToTimestamp(stat.getDate()))
                                .build())
                .collect(Collectors.toList());
    }


    /**
     * 临时刷新开始和结束时间
     */
    public void initBeginAndEndTime() {
        int limit = 1000;
        int startId = 0;
        while (true) {
            AdxOrderPoExample adxOrderPoExample = new AdxOrderPoExample();
            adxOrderPoExample.createCriteria()
                    .andIdGreaterThan(startId)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            adxOrderPoExample.setOrderByClause("id asc");
            adxOrderPoExample.setLimit(limit);
            List<AdxOrderPo> adxOrderPos = this.adxOrderDao.selectByExample(adxOrderPoExample);
            Map<Integer, List<AdxOrderPo>> mappingId2OrderMap = adxOrderPos.stream()
                    .filter(p -> Utils.isPositive(p.getMappingId()))
                    .collect(Collectors.groupingBy(p -> p.getMappingId().intValue()));

            if (!CollectionUtils.isEmpty(mappingId2OrderMap)) {
                List<ScheduleDto> schedules = this.queryScheduleService.queryBaseSchedule(QueryScheduleDto.builder()
                        .scheduleIds(Lists.newArrayList(mappingId2OrderMap.keySet()))
                        .build());
                schedules.stream()
                        .filter(scheduleDto -> mappingId2OrderMap.containsKey(scheduleDto.getScheduleId()))
                        .forEach(scheduleDto -> {
                            mappingId2OrderMap.get(scheduleDto.getScheduleId()).forEach(order -> {
                                AdxOrderPo updatePo = new AdxOrderPo();
                                updatePo.setId(order.getId());
                                updatePo.setBeginTime(Objects.nonNull(scheduleDto.getGdBeginTime()) ? scheduleDto.getGdBeginTime() : scheduleDto.getBeginDate());
                                updatePo.setEndTime(Objects.nonNull(scheduleDto.getGdEndTime()) ? scheduleDto.getGdEndTime() : scheduleDto.getEndDate());
                                this.adxOrderDao.updateByPrimaryKeySelective(updatePo);
                            });
                        });
            }

            if (adxOrderPos.size() < limit) {
                break;
            }

            startId = adxOrderPos.get(adxOrderPos.size() - 1).getId();
        }
    }
}
