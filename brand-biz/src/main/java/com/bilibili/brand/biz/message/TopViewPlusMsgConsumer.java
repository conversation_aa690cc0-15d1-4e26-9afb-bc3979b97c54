package com.bilibili.brand.biz.message;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.bean.RedisKey;
import com.bilibili.brand.biz.config.ExecutorConfiguration;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.ssa.platform.api.schedule.dto.TopViewPlusScheduleBo;
import com.bilibili.ssa.platform.biz.service.schedule.TopViewPlusScheduleService;
import com.bilibili.ssa.platform.common.enums.TopViewConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.task.TaskDecorator;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisConnectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2022/12/5 14:55
 */
@Slf4j
@Service
public class TopViewPlusMsgConsumer extends AbstractMsgProcessor<TopViewPlusScheduleBo, TopViewPlusScheduleBo>
        implements ApplicationListener<ContextRefreshedEvent> {

    @Autowired
    private TopViewPlusScheduleService scheduleService;

    @Resource(name = "lettuceConnectionFactory")
    private RedisConnectionFactory connectionFactory;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${topview.schedule.queue.nums:2}")
    private int queueNums;

    @Resource(name = "asyncTaskDecorator")
    private TaskDecorator asyncTaskDecorator;

    @Autowired
    private ExecutorConfiguration.ThreadPoolExecutorFactoryBean executorFactoryBean;

    @Override
    protected String getQueuePrefix() {
        return RedisKey.TOPVIEW_SCHEDULE_MSG_QUEUE_KEY;
    }

    @Override
    protected int getQueueNums() {
        return queueNums;
    }

    @Override
    protected int getIsolationSeq() {
        return TopViewConstants.ISOLATION_SEQ;
    }

    private void start() {
        RedisConnection connection = RedisConnectionUtils.getConnection(connectionFactory);
        Executor executor = executorFactoryBean.newFixedExecutor(getQueueNums());
        int nums = getQueueNums();
        for (int i = 0; i < nums; i++) {
            final int queueIndex = i;
            CompletableFuture.runAsync(() -> {
                try {
                    while (true) {
                        asyncTaskDecorator.decorate(() -> {
                            log.info("TopViewPlusMsgConsumer is running, queue index is {}", queueIndex);
                            String msg = null;
                            try {
                                RBlockingQueue<String> queue = redissonClient.getBlockingQueue(buildQueueKey(queueIndex));
                                if (!StringUtils.isEmpty(msg = queue.take())) {
                                    consume(GsonUtils.toObject(msg, TopViewPlusScheduleBo.class));
                                }
                            } catch (Exception ex) {
                                log.error("TopViewPlusMsgConsumer error,msg:{}", msg, ex);
                                //continue
                            }
                        }).run();
                    }
                } finally {
                    log.info("TopViewPlusMsgConsumer consume finished");
                    RedisConnectionUtils.releaseConnection(connection, connectionFactory);
                }
            }, executor).exceptionally(throwable -> {
                //捕捉异常,不会致使整个流程中断
                log.error("TopViewPlusMsgConsumer error，thread:{}", Thread.currentThread().getName(),
                        throwable);
                return null;
            });
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        //try {
        //    Thread.sleep(2000);
        //} catch (InterruptedException ex) {
        //    log.error("TopViewPlusMsgConsumer onApplicationEvent InterruptedException", ex);
        //}
        //start();
    }

    @Override
    public void consume(TopViewPlusScheduleBo topViewPlusScheduleBo) throws Exception {
        log.info("TopViewPlusMsgConsumer starts consume msg: {}", JSONObject.toJSONString(topViewPlusScheduleBo));
        OrderProduct orderProduct = OrderProduct.getByCode(topViewPlusScheduleBo.getSsaScheduleBo().getOrderProduct());
        if (orderProduct == OrderProduct.TOP_VIEW_GD_PLUS) {
            this.scheduleService.createTopViewGdPlusSchedule(topViewPlusScheduleBo);
        } else if (orderProduct == OrderProduct.TOP_VIEW_CPT_PLUS) {
            this.scheduleService.createTopViewCptPlusSchedule(topViewPlusScheduleBo);
        } else {
            log.warn("TopViewPlusMsgConsumer unknown orderProduct:{}", orderProduct);
        }
    }
}
