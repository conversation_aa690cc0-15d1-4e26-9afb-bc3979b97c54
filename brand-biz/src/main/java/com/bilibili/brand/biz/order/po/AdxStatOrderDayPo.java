package com.bilibili.brand.biz.order.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdxStatOrderDayPo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 出价方ID
     */
    private Integer bidderId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 时间序列-日期
     */
    private Timestamp groupTime;

    /**
     * 曝光数量
     */
    private Integer showCount;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * 应扣费计费（单位：毫分）
     */
    private Long chargedCostMilli;

    /**
     * 累计出价 单位（毫分）
     */
    private Long bidCostMilli;

    /**
     * 反作弊曝光量
     */
    private Integer acShowCount;

    /**
     * 反作弊点击次数
     */
    private Integer acClickCount;

    /**
     * 反作弊计费（单位：毫分）
     */
    private Long acCostMilli;

    /**
     * 反作弊累计出价 单位（毫分）
     */
    private Long acBidCostMilli;

    /**
     * 记录版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 最新小时曝光数量, 仅实时
     */
    private Integer hourShowCount;

    /**
     * 最新小时点击次数，仅实时
     */
    private Integer hourClickCount;

    /**
     * 最新小时应扣费计费（单位：毫分）
     */
    private Long hourChargedCostMilli;

    /**
     * 最新小时时间序列
     */
    private Timestamp latestGroupHour;

    /**
     * 最新小时未扣费计费（单位：毫分）
     */
    private Long hourUnchargedCostMilli;

    /**
     * 最新小时反作弊曝光数量, 仅实时
     */
    private Integer hourAcShowCount;

    /**
     * 最新小时反作弊点击次数，仅实时
     */
    private Integer hourAcClickCount;

    /**
     * 最新小时反作弊计费（单位：毫分）
     */
    private Long hourAcCostMilli;

    private static final long serialVersionUID = 1L;
}