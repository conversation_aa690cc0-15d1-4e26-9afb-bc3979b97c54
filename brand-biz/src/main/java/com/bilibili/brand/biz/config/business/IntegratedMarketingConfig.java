package com.bilibili.brand.biz.config.business;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/23
 */
@Component
@Data
public class IntegratedMarketingConfig {

    @Value("#{'${marketing.pageIds:5648}'.split(',')}")
    private List<Integer> pageIds;

    @Value("#{'${marketing.resourceIds:5649}'.split(',')}")
    private List<Integer> resourceIds;
    @Value("#{'${marketing.topic.sourceIds:5650,5651}'.split(',')}")
    private List<Integer> topicSourceIds;

    @Value("#{'${marketing.sourceIds:5650,5651}'.split(',')}")
    private List<Integer> sourceIds;
    public boolean isTopicCpt(Integer sourceId) {
        return topicSourceIds.contains(sourceId);
    }

    public boolean isIntegratedMarketing(Integer sourceId) {
        return sourceIds.contains(sourceId);
    }
}
