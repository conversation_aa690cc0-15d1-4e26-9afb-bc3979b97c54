package com.bilibili.brand.biz.account.service;

import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.bean.OrderBy;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.account.dto.*;
import com.bilibili.brand.api.account.service.IAccountGroupService;
import com.bilibili.brand.api.account.service.IAccountMidMappingService;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.common.exception.AccountExceptionCode;
import com.bilibili.brand.biz.account.acc_dao.AccountDao;
import com.bilibili.brand.biz.account.dao.*;
import com.bilibili.brand.biz.account.po.*;
import com.bilibili.brand.biz.account.pojo.AccAccountPo;
import com.bilibili.brand.biz.rpc.grpc.client.CrmWalletGrpcClient;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2017/2/28.
 */
@Service
public class QueryAccountService implements IQueryAccountService {
    private final static Logger LOGGER = LoggerFactory.getLogger(QueryAccountService.class);
    @Autowired
    private AccountDao accountDao;
    @Autowired
    private CrmWalletGrpcClient crmWalletGrpcClient;
    @Autowired
    private SpecialInfoDao specialInfoDao;
    @Autowired
    private LegalPersonIdCardDao legalPersonIdCardDao;
    @Autowired
    private RealNameMaterialDao realNameMaterialDao;
    @Autowired
    private AccountBiliUserDao accountBiliUserDao;
    @Autowired
    private IAccountGroupService accountGroupService;
    @Autowired
    private IBfsService bfsService;
    @Autowired
    private IAccountMidMappingService accountMidMappingService;
    @Autowired
    private IBusinessSideService businessSideService;

    @Override
    public AccountDto getAccount(Integer accountId) {
        Assert.notNull(accountId, "账号ID不可为空");
        return this.getAccountDto(accountId);
    }

    private AccountDto getAccountDto(Integer accountId) {
        AccountPo accountPo = this.getAccountPoById(accountId);
        AccountDto accountDto = new AccountDto();
        BeanUtils.copyProperties(accountPo, accountDto);
        accountDto.setIsAgent(accountPo.getIsAgent().equals(1));
        accountDto.setIsInner(accountPo.getIsInner());
        return accountDto;
    }

    protected AccountPo getAccountPoById(Integer accountId) {
        AccountPo accountPo = accountDao.get(accountId);
        Assert.notNull(accountPo, "账号不存在");
        return accountPo;
    }

    private List<AccountDto> convertAccountPosToAccountDtos(List<AccountPo> accountPos) {
        if (CollectionUtils.isEmpty(accountPos)) {
            return Collections.emptyList();
        }
        List<AccountDto> accountDtos = new ArrayList<>(accountPos.size());
        for (AccountPo accountPo : accountPos) {
            AccountDto accountDto = new AccountDto();
            BeanUtils.copyProperties(accountPo, accountDto);
            accountDto.setIsAgent(accountPo.getIsAgent().equals(1));
            accountDtos.add(accountDto);
        }
        return accountDtos;
    }

    @Override
    public List<AccountDto> getAccountDtosInAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        return this.convertAccountPosToAccountDtos(accountDao.getInIds(accountIds));
    }

    @Override
    public Map<Integer, AccountDto> getAccountDtoMapInAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        List<AccountDto> accountDtos = this.getAccountDtosInAccountIds(accountIds);
        if (CollectionUtils.isEmpty(accountDtos)) {
            return Collections.emptyMap();
        }
        Map<Integer, AccountDto> resultMap = new HashMap<>(accountDtos.size());
        for (AccountDto dto : accountDtos) {
            resultMap.put(dto.getAccountId(), dto);
        }
        return resultMap;
    }

    @Override
    public List<AccountDto> getAllAccountDtos() throws ServiceException {
        //数据量少直接查询能登陆品牌的账户
        List<BusinessSideDto> allBusinessSides = businessSideService.getAllBusinessSides();

        List<Integer> accountIds = allBusinessSides.stream()
                .map(BusinessSideDto::getAccountId)
                .collect(Collectors.toList());

        return this.convertAccountPosToAccountDtos(accountDao.getInIds(accountIds));
    }

    @Override
    public List<AccountDto> getAccountDtosByBiliUsername(String biliUsername) throws ServiceException {
        if (StringUtils.isEmpty(biliUsername)) {
            throw new ServiceException(AccountExceptionCode.REQUIRED_PARAM);
        }
        LOGGER.info("getAccountDtosByBiliUsername.biliUsername {}", biliUsername);
        List<Integer> accountIds = accountBiliUserDao.getAccountIdsByBiliUsername(biliUsername);
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyList();
        }
        return this.getAccountDtosInAccountIds(accountIds);
    }

    @Override
    public AccountAllInfoDto getAccountAllInfo(Integer accountId) throws ServiceException {
        AccountDto accountDto = this.getAccountDto(accountId);
        List<AccountDto> accountDtos = new ArrayList<>(1);
        accountDtos.add(accountDto);
        return this.getAllInfo(accountDtos).get(0);
    }


    @Override
    public PageResult<AccountDto> getAccountDtos(Integer page, Integer size) throws ServiceException {
        return this.getAccountDtos(page, size, null);
    }

    private PageResult<AccountDto> getAccountDtos(Integer page, Integer size, OrderBy orderBy) throws ServiceException {
        if (null == page || null == size) {
            throw new ServiceException(AccountExceptionCode.REQUIRED_PARAM);
        }
        Integer total = accountDao.getAllCount();
        return PageResult.<AccountDto>builder()
                .total(total)
                .records(this.convertAccountPosToAccountDtos(accountDao.getList(Page.valueOf(page, size), orderBy, null)))
                .build();
    }

    private List<AccountAllInfoDto> getAllInfo(List<AccountDto> accountDtos) throws ServiceException {
        if (CollectionUtils.isEmpty(accountDtos)) {
            return Collections.emptyList();
        }
        List<Integer> accountIds = new ArrayList<>(accountDtos.size());
        accountIds.addAll(accountDtos.stream()
                .map(AccountDto::getAccountId)
                .collect(Collectors.toList()));
        List<AccountAllInfoDto> accountAllInfoDtos = new ArrayList<>(accountDtos.size());
        Map<Integer, List<SpecialInfoDto>> specialInfoMap = this.getSpecialInfoDtoMapByAccountIds(accountIds);
        Map<Integer, List<LegalPersonIDCardDto>> legalPersonIDCardMap = this.getLegalPersonIDCardDtoMapByAccountIds(accountIds);
        Map<Integer, List<RealNameMaterialDto>> realNameMaterialMap = this.getRealNameMaterialDtoMapByAccountIds(accountIds);
        Map<Integer, WalletDto> walletDtoMap = this.getWalletDtoMap(accountIds);
        Map<Integer, List<AccountGroupDto>> accountGroupMap = accountGroupService.getValidAccountGroupMapByAccountIds(accountIds);
        Map<Integer, List<String>> saleMap = this.getBiliUserMapByAccoungIds(accountIds);
        Map<Integer, List<Long>> midsMap = accountMidMappingService.getMidsMapInAccountIds(accountIds);


        for (AccountDto accountDto : accountDtos) {
            AccountAllInfoDto accountAllInfoDto = AccountAllInfoDto.builder()
                    .accountDto(accountDto)
                    .walletDto(walletDtoMap.get(accountDto.getAccountId()))
                    .specialInfoDtos(specialInfoMap.get(accountDto.getAccountId()))
                    .legalPersonIDCardDtos(legalPersonIDCardMap.get(accountDto.getAccountId()))
                    .realNameMaterialDtos(realNameMaterialMap.get(accountDto.getAccountId()))
                    .accountGroupDtos(accountGroupMap.get(accountDto.getAccountId()))
                    .sales(saleMap.get(accountDto.getAccountId()))
                    .mids(midsMap.get(accountDto.getAccountId()))
                    .build();
            accountAllInfoDtos.add(accountAllInfoDto);
        }
        return accountAllInfoDtos;
    }

    @Override
    public Map<Integer, WalletDto> getWalletDtoMap(List<Integer> accountIds) {
        List<WalletDto> walletDtos = crmWalletGrpcClient.queryWalletByAccIds(accountIds);
        return walletDtos.stream()
                .collect(Collectors.toMap(WalletDto::getAccountId, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public AccountDto getAccountDtoByMid(Long mid) throws ServiceException {
        if (null == mid) {
            throw new ServiceException(AccountExceptionCode.REQUIRED_PARAM);
        }
        LOGGER.info("getAccountDtoByMid.mid {}", mid);
        AccountPo accountPo = accountDao.getByMid(mid);
        if (null == accountPo) {
            LOGGER.info("account does not exist, mid {}", mid);
            throw new ServiceException(AccountExceptionCode.NOT_EXIST_ACCOUNT);
        }
        AccountDto accountDto = new AccountDto();
        BeanUtils.copyProperties(accountPo, accountDto);
        accountDto.setIsAgent(accountPo.getIsAgent() == 1);
        return accountDto;
    }

    protected Map<Integer, List<SpecialInfoDto>> getSpecialInfoDtoMapByAccountIds(List<Integer> accountIds) throws ServiceException {
        List<SpecialInfoPo> pos = specialInfoDao.getByAccountIds(accountIds);
        Map<Integer, List<SpecialInfoDto>> map = new HashMap<>(accountIds.size());
        for (SpecialInfoPo po : pos) {
            List<SpecialInfoDto> dtos = null;
            if (map.containsKey(po.getAccountId())) {
                dtos = map.get(po.getAccountId());
            } else {
                dtos = new ArrayList<>();
            }
            SpecialInfoDto dto = new SpecialInfoDto();
            BeanUtils.copyProperties(po, dto);
            dto.setImageHashCode(Base64Utils.encodeToString(dto.getImageUrl().getBytes()));
            dto.setToken(bfsService.getTokenByUrl(dto.getImageUrl()));
            dtos.add(dto);
            map.put(po.getAccountId(), dtos);
        }
        return map;
    }

    protected Map<Integer, List<LegalPersonIDCardDto>> getLegalPersonIDCardDtoMapByAccountIds(List<Integer> accountIds) throws ServiceException {
        List<LegalPersonIdCardPo> pos = legalPersonIdCardDao.getByAccountIds(accountIds);
        Map<Integer, List<LegalPersonIDCardDto>> map = new HashMap<>(accountIds.size());
        for (LegalPersonIdCardPo po : pos) {
            List<LegalPersonIDCardDto> dtos = null;
            if (map.containsKey(po.getAccountId())) {
                dtos = map.get(po.getAccountId());
            } else {
                dtos = new ArrayList<>();
            }
            LegalPersonIDCardDto dto = new LegalPersonIDCardDto();
            BeanUtils.copyProperties(po, dto);
            dto.setImageHashCode(Base64Utils.encodeToString(dto.getIdCardUrl().getBytes()));
            dto.setToken(bfsService.getTokenByUrl(dto.getIdCardUrl()));
            dtos.add(dto);
            map.put(po.getAccountId(), dtos);
        }
        return map;
    }

    protected Map<Integer, List<RealNameMaterialDto>> getRealNameMaterialDtoMapByAccountIds(List<Integer> accountIds) throws ServiceException {
        List<RealNameMaterialPo> pos = realNameMaterialDao.getByAccountIds(accountIds);
        Map<Integer, List<RealNameMaterialDto>> map = new HashMap<>(accountIds.size());
        for (RealNameMaterialPo po : pos) {
            List<RealNameMaterialDto> dtos = null;
            if (map.containsKey(po.getAccountId())) {
                dtos = map.get(po.getAccountId());
            } else {
                dtos = new ArrayList<>();
            }
            RealNameMaterialDto dto = new RealNameMaterialDto();
            BeanUtils.copyProperties(po, dto);
            dto.setImageHashCode(Base64Utils.encodeToString(dto.getUrl().getBytes()));
            dto.setToken(bfsService.getTokenByUrl(dto.getUrl()));
            dtos.add(dto);
            map.put(po.getAccountId(), dtos);
        }
        return map;
    }

    @Override
    public Map<Integer, List<String>> getBiliUserMapByAccoungIds(List<Integer> accountIds) {
        List<AccountBiliUserPo> pos = accountBiliUserDao.getListByAccountIds(accountIds);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        Map<Integer, List<String>> resultMap = new HashMap<>();
        List<String> list = null;
        for (AccountBiliUserPo po : pos) {
            if (resultMap.containsKey(po.getAccountId())) {
                list = resultMap.get(po.getAccountId());
            } else {
                list = new ArrayList<>();
            }
            list.add(po.getBiliUsername());
            resultMap.put(po.getAccountId(), list);
        }
        return resultMap;
    }

    private AccAccountDto accPoToAccDto(AccAccountPo po) {
        AccAccountDto dto = new AccAccountDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

}
