package com.bilibili.brand.biz.config.paladin;

import com.bilibili.brand.exception.RemoteConfigException;
import com.bilibili.paladin.ConfigSource;
import com.bilibili.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.PostConstruct;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;

@Slf4j
public abstract class AbstractPaladinConfig {

    @Autowired
    @Qualifier("myConfigSource")
    private ConfigSource configSource;

    @Autowired
    @Qualifier("cachedExecutorWithDecorator")
    private Executor cachedExecutor;

    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    @PostConstruct
    public void init() {
        Optional<String> config = configSource.getNullableString(getConfigKey());
        if (!config.isPresent()) {
            throw new RemoteConfigException("配置不存在，请检查配置：" + getConfigKey());
        }
        refreshConfig(config.get());

        configSource.watch(getConfigKey(), event -> {
            Optional<String> newConfig = event.getNewValue();
            if (!newConfig.isPresent()) {
                throw new RemoteConfigException("配置不存在，请检查配置：" + getConfigKey());
            }
            refreshConfig(newConfig.get());
        }, cachedExecutor);
    }

    protected void refreshConfig(String configStr) {
        // 监听对象转换
        AbstractPaladinConfig newConfig = JsonUtils.toObject(configStr, this.getClass());
        log.info("监听到配置变更，configKey：{}，oldConfig：{}，newConfig：{}",
                getConfigKey(), JsonUtils.toJson(this), JsonUtils.toJson(newConfig));

        // 校验配置对象
        Set<ConstraintViolation<AbstractPaladinConfig>> validate = validator.validate(newConfig);
        if (!validate.isEmpty()) {
            throw new RemoteConfigException("配置校验失败，请检查配置：" + getConfigKey(), validate);
        }

        // 更新配置对象
        BeanUtils.copyProperties(newConfig, this);
    }

    protected abstract String getConfigKey();
}
