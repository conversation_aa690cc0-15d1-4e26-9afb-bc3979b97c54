package com.bilibili.brand.biz.creative.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.brand.biz.creative.po.querydsl.GdCrowdPackagePo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QGdCrowdPackage is a Querydsl query type for GdCrowdPackagePo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QGdCrowdPackage extends com.querydsl.sql.RelationalPathBase<GdCrowdPackagePo> {

    private static final long serialVersionUID = 1555681859;

    public static final QGdCrowdPackage gdCrowdPackage = new QGdCrowdPackage("gd_crowd_package");

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> endTime = createDateTime("endTime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath name = createString("name");

    public final StringPath operator = createString("operator");

    public final NumberPath<Long> packageId = createNumber("packageId", Long.class);

    public final NumberPath<Integer> scene = createNumber("scene", Integer.class);

    public final DateTimePath<java.sql.Timestamp> startTime = createDateTime("startTime", java.sql.Timestamp.class);

    public final NumberPath<Integer> userType = createNumber("userType", Integer.class);

    public final com.querydsl.sql.PrimaryKey<GdCrowdPackagePo> primary = createPrimaryKey(id);

    public QGdCrowdPackage(String variable) {
        super(GdCrowdPackagePo.class, forVariable(variable), "null", "gd_crowd_package");
        addMetadata();
    }

    public QGdCrowdPackage(String variable, String schema, String table) {
        super(GdCrowdPackagePo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QGdCrowdPackage(String variable, String schema) {
        super(GdCrowdPackagePo.class, forVariable(variable), schema, "gd_crowd_package");
        addMetadata();
    }

    public QGdCrowdPackage(Path<? extends GdCrowdPackagePo> path) {
        super(path.getType(), path.getMetadata(), "null", "gd_crowd_package");
        addMetadata();
    }

    public QGdCrowdPackage(PathMetadata metadata) {
        super(GdCrowdPackagePo.class, metadata, "null", "gd_crowd_package");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(endTime, ColumnMetadata.named("end_time").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(8).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(10).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(name, ColumnMetadata.named("name").withIndex(3).ofType(Types.VARCHAR).withSize(100).notNull());
        addMetadata(operator, ColumnMetadata.named("operator").withIndex(7).ofType(Types.VARCHAR).withSize(100).notNull());
        addMetadata(packageId, ColumnMetadata.named("package_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(scene, ColumnMetadata.named("scene").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(startTime, ColumnMetadata.named("start_time").withIndex(4).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(userType, ColumnMetadata.named("user_type").withIndex(11).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

