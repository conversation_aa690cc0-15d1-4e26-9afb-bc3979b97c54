package com.bilibili.brand.biz.converter;

import com.bilibili.brand.api.schedule.bo.information_flow.GdInventoryResultBo;
import com.bilibili.brand.api.schedule.bo.ssa.SsaCptInventoryResultBo;
import com.bilibili.brand.api.schedule.bo.ssa.SsaCptQueryInventoryBo;
import com.bilibili.brand.api.schedule.bo.ssa.SsaGdInventoryResultBo;
import com.bilibili.brand.api.schedule.bo.ssa.SsaGdQueryInventoryBo;
import com.bilibili.brand.api.schedule.dto.SplitDaysStockDto;
import com.bilibili.brand.api.schedule.dto.StockPriceDto;
import com.bilibili.brand.api.stock.dto.QueryStockDto;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.QueryInventoryTimeInfoBo;
import com.mysema.commons.lang.Pair;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 闪屏库存相关转换
 *
 * <AUTHOR>
 * @date 2023/7/13
 */
@Mapper
public interface SsaInventoryConverter {

    SsaInventoryConverter MAPPER = Mappers.getMapper(SsaInventoryConverter.class);

    @Mappings({
            @Mapping(target = "orderMode", source = "ssaCptOrderMode"),
            @Mapping(target = "dealSeq", source = "requestId"),
            @Mapping(target = "targets", source = "targetRules"),
            @Mapping(target = "timeInfos", source = "timeInfo"),
            @Mapping(target = "getOneRotationCpm", ignore = true),
    })
    SsaCptQueryInventoryBo toSsaCptQueryInventoryBo(QueryStockDto query);

    @Mappings({

            @Mapping(target = "dealSeq", source = "requestId"),
            @Mapping(target = "targets", source = "targetRules"),
            @Mapping(target = "timeInfos", source = "timeInfo"),
    })
    SsaGdQueryInventoryBo toSsaGdQueryInventoryBo(QueryStockDto query);

    @Mappings({
            @Mapping(target = "dealSeq", source = "requestId"),
            @Mapping(target = "errorMsg", ignore = true),
            @Mapping(target = "isDealFinish", source = "ssaCptInventoryResultBo.alreadyFinish"),
            @Mapping(target = "isSuccess", source = "ssaCptInventoryResultBo.alreadyFinish"),
            @Mapping(target = "prices", ignore = true),
            @Mapping(target = "splitDaysFlag", expression = "java(ssaCptInventoryResultBo.getInventoryDetails().size()>1?1:0)"),
            @Mapping(target = "splitDaysStocks", source = "ssaCptInventoryResultBo"),
            @Mapping(target = "stockCpm", ignore = true)
    })
    StockPriceDto toStockPriceDto(SsaCptInventoryResultBo ssaCptInventoryResultBo, String requestId);

    @Mappings({
            @Mapping(target = "dealSeq", source = "requestId"),
            @Mapping(target = "errorMsg", ignore = true),
            @Mapping(target = "isDealFinish", source = "ssaGdInventoryResultBo.alreadyFinish"),
            @Mapping(target = "isSuccess", source = "ssaGdInventoryResultBo.alreadyFinish"),
            @Mapping(target = "prices", ignore = true),
            @Mapping(target = "splitDaysFlag", expression = "java(ssaGdInventoryResultBo.getInventoryDetails().size()>1?1:0)"),
            @Mapping(target = "splitDaysStocks", source = "ssaGdInventoryResultBo"),
            @Mapping(target = "stockCpm", source = "ssaGdInventoryResultBo"),
    })
    StockPriceDto toStockPriceDto(SsaGdInventoryResultBo ssaGdInventoryResultBo, String requestId);

    default int sum(SsaGdInventoryResultBo ssaGdInventoryResultBo) {
        return ssaGdInventoryResultBo.getInventoryDetails().stream()
                .mapToInt(GdInventoryDetailBo::getInventories)
                .sum();
    }

    default List<SplitDaysStockDto> toSplitDaysStock(SsaCptInventoryResultBo ssaCptInventoryResultBo) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return ssaCptInventoryResultBo.getInventoryDetails().stream()
                .map(detail -> SplitDaysStockDto.builder()
                        .rotationNum(detail.getRounds())
                        .beginTime(dateTimeFormatter.format(detail.getBeginTime()))
                        .endTime(dateTimeFormatter.format(detail.getEndTime()))
                        .scheduleDate(dateFormatter.format(detail.getBeginTime()))
                        .ssaCptSplitTimeInventoryDtos(detail.getSsaCptSplitTimeInventoryDtos())
                        .build()
                ).collect(Collectors.toList());
    }

    default List<SplitDaysStockDto> toSplitDaysStock(SsaGdInventoryResultBo ssaGdInventoryResultBo) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return ssaGdInventoryResultBo.getInventoryDetails().stream()
                .map(detail -> SplitDaysStockDto.builder()
                        .stockCpm((long) detail.getInventories())
                        .beginTime(dateTimeFormatter.format(detail.getBeginTime()))
                        .endTime(dateTimeFormatter.format(detail.getEndTime()))
                        .build()
                ).collect(Collectors.toList());
    }

    default List<QueryInventoryTimeInfoBo> toTimeList(List<Pair<Timestamp, Timestamp>> timeInfo) {

        return timeInfo.stream()
                .map(timePair -> QueryInventoryTimeInfoBo.builder()
                        .beginTime(timePair.getFirst().toLocalDateTime())
                        .endTime(timePair.getSecond().toLocalDateTime())
                        .build()).collect(Collectors.toList());
    }

}
