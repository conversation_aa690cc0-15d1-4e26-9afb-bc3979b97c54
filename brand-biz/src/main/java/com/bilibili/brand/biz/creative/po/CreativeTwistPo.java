package com.bilibili.brand.biz.creative.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreativeTwistPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 创意ID
     */
    private Long creativeId;

    /**
     * ios扭动角度
     */
    private Float iosAngle;

    /**
     * android扭动角度
     */
    private Float androidAngle;

    /**
     * ios扭动加速度
     */
    private Float iosSpeed;

    /**
     * android扭动加速度
     */
    private Float androidSpeed;

    /**
     * 展示扭一扭交互lottie的开始时间，单位ms
     */
    private Integer startTime;

    /**
     * 展示扭一扭交互lottie的结束时间，单位ms
     */
    private Integer endTime;

    /**
     * 扭一扭lottie展示时间，单位ms
     */
    private Integer durTime;

    /**
     * 扭一扭lottie引导图
     */
    private String lottie;

    /**
     * 扭一扭引导交互提示文案
     */
    private String hint;

    /**
     * 交互结果，0：长按出彩蛋视频（废弃）；1：交互后出彩蛋视频；2：交互后直接跳转落地页；
     */
    private Integer actionType;

    /**
     * 资源类型（目前是拓展字段，默认0）0：inline，1：story
     */
    private Integer resourceType;

    /**
     * 软删，0：有效，1：已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * lottie的x轴坐标百分比0-100
     */
    private Integer coorX;

    /**
     * lottie的y轴坐标百分比0-100
     */
    private Integer coorY;

    /**
     * lottie展示风格：0、居中然后收到左下角 1、lottie常驻左下角 2、居中展示然后消失
     */
    private Integer showStyle;

    /**
     * 扭一扭可触发时间类型：0、播放时间进度（图片扭一扭不生效） 1、自然时间进度
     */
    private Integer timeType;

    /**
     * lottie类型，lottie文件使用lottie字段：0、默认效果 1、图片效果
     */
    private Integer lottieType;

    /**
     * 扭一扭功能交互lottie的结束时间，默认Integer.MAX_VALUE，单位ms
     */
    private Integer functionEndTime;

    /**
     * 扭一扭引导交互降级提示文案
     */
    private String degradeHint;

    /**
     * 扭一扭交互锚点位附加描述文案
     */
    private String anchorHint;

    /**
     * 交互类型：0、扭一扭 1、点击 2、扭一扭且点击
     */
    private Integer interactionType;

    private static final long serialVersionUID = 1L;
}