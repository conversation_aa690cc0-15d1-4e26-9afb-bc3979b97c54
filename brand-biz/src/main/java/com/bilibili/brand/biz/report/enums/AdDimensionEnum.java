package com.bilibili.brand.biz.report.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AdDimensionEnum {

    ORDER(0, "订单维度"),
    SCHEDULE(1, "排期维度"),
    CREATIVE(2, "创意维度"),
    ;

    private final Integer code;
    private final String desc;

    public static AdDimensionEnum getByCode(Integer code) {
        for (AdDimensionEnum adDimensionEnum : values()) {
            if (adDimensionEnum.getCode().equals(code)) {
                return adDimensionEnum;
            }
        }
        throw new RuntimeException("No AdDimensionEnum for code [" + code + "]");
    }
}
