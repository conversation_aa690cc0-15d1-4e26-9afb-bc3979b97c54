package com.bilibili.brand.biz.schedule.dao;

import com.bilibili.brand.biz.schedule.po.GdScheduleOgvPo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/17 18:04
 */
public interface GdScheduleOgvDaoExtension {


    /**
     * 根据season查询投放截止时间大于指定时间的信息。
     * 比如endTime=now()，用于查询投放中或者将来投放的排期
     */
    List<GdScheduleOgvPo> selectValidScheduleOgvBySeason(@Param("seasonIdList") List<Long> seasonIdList,
                                                         @Param("endTime") Timestamp endTime);

    /**
     * 根据episode查询投放截止时间大于指定时间的信息。
     * 比如endTime=now()，用于查询投放中或者将来投放的排期
     */
    List<GdScheduleOgvPo> selectValidScheduleOgvByEpisode(@Param("episodeIdList") List<Long> episodeIdList,
                                                          @Param("endTime") Timestamp endTime);
}
