package com.bilibili.brand.biz.resource.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.resource.bluekeyword.*;
import com.bilibili.brand.bean.RedisKey;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import com.bilibili.brand.biz.event_bus.IBrandEventPublisher;
import com.bilibili.brand.biz.event_bus.event.BlueKeywordConfigChangeEvent;
import com.bilibili.brand.biz.resource.converter.BlueKeywordConverter;
import com.bilibili.brand.biz.utils.RlockUtil;
import com.bilibili.cpt.platform.biz.dao.BrandBlueKeywordArchiveDao;
import com.bilibili.cpt.platform.biz.dao.BrandBlueKeywordDao;
import com.bilibili.cpt.platform.biz.dao.BrandBlueKeywordItemDao;
import com.bilibili.cpt.platform.biz.dao.BrandBlueKeywordDaoExtension;
import com.bilibili.cpt.platform.biz.po.*;
import com.bilibili.enums.BlueKeywordItemStatusEnum;
import com.bilibili.enums.BlueKeywordStatusEnum;
import com.bilibili.utils.BatchUtil;
import com.bilibili.utils.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/10 15:17
 */
@Slf4j
@Service
public class BlueKeywordService implements IBlueKeywordService {

    @Autowired
    private BrandBlueKeywordDao blueKeywordDao;

    @Autowired
    private BrandBlueKeywordItemDao blueKeywordItemDao;

    @Autowired
    private BrandBlueKeywordArchiveDao blueKeywordArchiveDao;

    @Autowired
    private BrandBlueKeywordDaoExtension blueKeywordItemDaoExtension;

    @Autowired
    private IBrandEventPublisher<BrandEvent> brandEventPublisher;


    private RLock getConfigLocker(Long configId) {
        String key = RedisKey.BLUE_KEYWORD_OPERATE_PREFIX + configId;
        //等待时间为0，拿不到锁则fast-fail
        return RlockUtil.getInstance()
                .getLock(key, 0, 3 * 60, "当前配置在操作中，请勿重试", TimeUnit.SECONDS);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveBlueKeywordConfig(BlueKeywordConfigDto config, Operator operator) throws Exception {
        log.info("[BlueKeywordService] saveBlueKeywordConfig, config={},operator={}",
                JSON.toJSONString(config), JSON.toJSONString(operator));
        this.basicValidate(operator);
        Assert.isTrue(Utils.isPositive(config.getId()), "配置id不能为空");
        Assert.hasText(config.getName(), "配置名称不能为空");
        Assert.notNull(config.getBeginTime(), "开始时间不能为空");
        Assert.notNull(config.getEndTime(), "结束时间不能为空");
        Assert.isTrue(!config.getBeginTime().after(config.getEndTime()), "开始时间不能晚于结束时间");

        RLock locker = null;
        try {
            locker = this.getConfigLocker(config.getId());
            BrandBlueKeywordPo configPo = this.getBrandBlueKeywordPo(config.getId());
            Assert.notNull(configPo, "配置不存在");
            BlueKeywordStatusEnum statusEnum = BlueKeywordStatusEnum.getByCode(configPo.getStatus());
            Integer status = configPo.getStatus();
            if (Objects.equals(statusEnum, BlueKeywordStatusEnum.INVALID)) {
                //如果原来是无效状态，说明是首次创建，因此此时需要流转到默认的启用状态
                status = BlueKeywordStatusEnum.ENABLE.getCode();
            }
            BrandBlueKeywordPo savePo = BrandBlueKeywordPo.builder()
                    .id(config.getId())
                    .name(config.getName())
                    .beginTime(Utils.getBeginOfDay(config.getBeginTime()))
                    .endTime(Utils.getEndOfDay(config.getEndTime()))
                    .status(status)
                    .bizScene(StringUtils.hasText(config.getBizScene()) ? config.getBizScene() : "brand")//默认品牌
                    .build();
            this.blueKeywordDao.updateByPrimaryKeySelective(savePo);
            //如果变更了明细，则尝试生效明细
            //为什么需要这个itemVersion版本，是否可以依赖最新的记录或者特定状态识别出哪些需要生效呢（--不能）？
            //因为：如果用户上传完一定点击保存，那么就可以依据上面的方式进行更新，但实际用户上传完不一定点击保存，直接关闭了窗口退出编辑了，
            //因此此时表里就会存有脏数据，如果下次用户仅仅是编辑个时间、名称等，那么岂不是把上次的脏数据应用到当前配置了嘛，这显然不合理
            BlueKeywordConfigItemDto oldItem = this.applyBlueKeywordConfigItem(config.getId(), config.getItemVersion());

            //发布变更事件
            this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                    .actionType(BrandEvent.ActionType.MODIFY)
                    .configId(config.getId())
                    .oldItem(oldItem)
                    .build());
            return config.getId();
        } finally {
            if (Objects.nonNull(locker)) {
                locker.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long enableBlueKeywordConfig(Long id, Operator operator) throws Exception {
        log.info("[BlueKeywordService] enableBlueKeywordConfig, configId={},operator={}",
                id, JSON.toJSONString(operator));
        this.basicValidate(operator);
        Assert.isTrue(Utils.isPositive(id), "配置id不能为空");

        RLock locker = null;
        try {
            locker = this.getConfigLocker(id);
            BrandBlueKeywordPo configPo = this.getVisibleBrandBlueKeywordPo(id);
            Assert.notNull(configPo, "配置不存在");

            BlueKeywordStatusEnum statusEnum = BlueKeywordStatusEnum.getByCode(configPo.getStatus());
            if (!Objects.equals(statusEnum, BlueKeywordStatusEnum.ENABLE)) {
                BrandBlueKeywordPo savePo = BrandBlueKeywordPo.builder()
                        .id(id)
                        .status(BlueKeywordStatusEnum.ENABLE.getCode())
                        .build();
                this.blueKeywordDao.updateByPrimaryKeySelective(savePo);

                //发布启用事件
                this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                        .actionType(BrandEvent.ActionType.ENABLE)
                        .configId(id)
                        .build());
            }
            return id;
        } finally {
            if (Objects.nonNull(locker)) {
                locker.unlock();
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long disableBlueKeywordConfig(Long id, Operator operator) throws Exception {
        log.info("[BlueKeywordService] disableBlueKeywordConfig, configId={},operator={}",
                id, JSON.toJSONString(operator));
        this.basicValidate(operator);
        Assert.isTrue(Utils.isPositive(id), "配置id不能为空");

        RLock locker = null;
        try {
            locker = this.getConfigLocker(id);
            BrandBlueKeywordPo configPo = this.getVisibleBrandBlueKeywordPo(id);
            Assert.notNull(configPo, "配置不存在");

            BlueKeywordStatusEnum statusEnum = BlueKeywordStatusEnum.getByCode(configPo.getStatus());
            if (!Objects.equals(statusEnum, BlueKeywordStatusEnum.DISABLE)) {
                BrandBlueKeywordPo savePo = BrandBlueKeywordPo.builder()
                        .id(id)
                        .status(BlueKeywordStatusEnum.DISABLE.getCode())
                        .build();
                this.blueKeywordDao.updateByPrimaryKeySelective(savePo);
                //发布禁用事件
                this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                        .actionType(BrandEvent.ActionType.DISABLE)
                        .configId(id)
                        .build());
            }
            return id;
        } finally {
            if (Objects.nonNull(locker)) {
                locker.unlock();
            }
        }
    }

    @Override
    public BlueKeywordConfigDto getVisibleBlueKeywordConfig(Long id) {
        BrandBlueKeywordPo configPo = this.getVisibleBrandBlueKeywordPo(id);
        Assert.notNull(configPo, "配置不存在");

        List<BrandBlueKeywordAggExtensionPo> keywordCountPoList = this.blueKeywordItemDaoExtension.selectBlueKeywordItemCount(
                Lists.newArrayList(id),
                Lists.newArrayList(BlueKeywordItemStatusEnum.VALID.getCode()));
        List<BrandBlueKeywordAggExtensionPo> archiveCountPoList = this.blueKeywordItemDaoExtension.selectBlueKeywordArchiveCount(
                Lists.newArrayList(id),
                Lists.newArrayList(BlueKeywordItemStatusEnum.VALID.getCode()));
        BrandBlueKeywordAggExtensionPo keywordCountPo = CollectionUtils.isEmpty(keywordCountPoList) ? null : keywordCountPoList.get(0);
        BrandBlueKeywordAggExtensionPo archiveCountPo = CollectionUtils.isEmpty(archiveCountPoList) ? null : archiveCountPoList.get(0);

        return BlueKeywordConverter.MAPPER.toBlueKeywordConfigDto(configPo, keywordCountPo, archiveCountPo);
    }

    @Override
    public PageResult<BlueKeywordConfigDto> getBlueKeywordConfigList(BlueKeywordConfigQueryDto query) {
        int pageIndex = NumberUtil.toValidPage(query.getPageIndex());
        int pageSize = NumberUtil.toValidPageSize(query.getPageSize(), 50, 1000);

        BrandBlueKeywordPoExample example = this.buildBrandBlueKeywordPoExample(query);

        long totalCount = this.blueKeywordDao.countByExample(example);
        example.setOrderByClause("mtime desc");
        example.setOffset(pageSize * (pageIndex - 1));
        example.setLimit(pageSize);

        List<BrandBlueKeywordPo> poList = this.blueKeywordDao.selectByExample(example);
        List<BlueKeywordConfigDto> configList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(poList)) {
            List<Long> idList = poList.stream().map(BrandBlueKeywordPo::getId).collect(Collectors.toList());

            Map<Long, BrandBlueKeywordAggExtensionPo> keywordCountPoMap = this.blueKeywordItemDaoExtension.selectBlueKeywordItemCount(
                            idList, Lists.newArrayList(BlueKeywordItemStatusEnum.VALID.getCode())).stream()
                    .collect(Collectors.toMap(BrandBlueKeywordAggExtensionPo::getConfigId, Function.identity()));

            Map<Long, BrandBlueKeywordAggExtensionPo> archiveCountPoMap = this.blueKeywordItemDaoExtension.selectBlueKeywordArchiveCount(
                            idList, Lists.newArrayList(BlueKeywordItemStatusEnum.VALID.getCode())).stream()
                    .collect(Collectors.toMap(BrandBlueKeywordAggExtensionPo::getConfigId, Function.identity()));

            configList = poList.stream()
                    .map(po -> BlueKeywordConverter.MAPPER.toBlueKeywordConfigDto(po,
                            keywordCountPoMap.get(po.getId()), archiveCountPoMap.get(po.getId())))
                    .collect(Collectors.toList());
        }
        return PageResult.<BlueKeywordConfigDto>builder()
                .records(configList)
                .total((int) totalCount)
                .build();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BlueKeywordConfigDto uploadBlueKeywordConfigItem(BlueKeywordConfigItemDto item, Operator operator) throws Exception {
        //item可能中的元素可能很多，因此就不打印日志了
        this.basicValidate(operator);

        List<Long> avidList = item.getAvidList();
        List<String> keywordList = item.getKeywordList();

        Assert.notNull(avidList, "稿件不能为null");
        Assert.notNull(keywordList, "关键词不能为null");

        //过滤和去重
        avidList = avidList.stream().filter(Utils::isPositive).distinct().collect(Collectors.toList());
        keywordList = keywordList.stream().filter(StringUtils::hasText).map(String::trim).distinct().collect(Collectors.toList());

        Assert.notEmpty(avidList, "稿件不能为空");
        Assert.notEmpty(keywordList, "关键词不能为空");


        RLock locker = null;
        try {
            Long configId = item.getConfigId();
            if (Utils.isPositive(configId)) {
                log.info("[BlueKeywordService] uploadBlueKeywordConfigItem get exists config, config_id={}", configId);
                //1、编辑时，上传
                //2、创建时，多次上传，此时已经存在configId，但是配置的状态可能是无效的，因此要查询所有状态的配置
                BrandBlueKeywordPo config = this.getBrandBlueKeywordPo(configId);
                Assert.notNull(config, "配置不存在");
            } else {
                //1、创建时，首次上传
                configId = initBlueKeywordConfig();
                log.info("[BlueKeywordService] uploadBlueKeywordConfigItem init config, config_id={}", configId);
            }
            locker = this.getConfigLocker(configId);

            final Long cId = configId;
            Integer version = this.getMaxItemVersion(configId) + 1;
            log.info("[BlueKeywordService] uploadBlueKeywordConfigItem current upload, config_id={}, version={}, avidList={}, " +
                    "keywordList={}", configId, version, JSON.toJSONString(avidList), JSON.toJSONString(keywordList));
            List<BrandBlueKeywordArchivePo> archivePoList = avidList.stream()
                    .map(avid ->//由于采用的是批量插入，因此所有字段必须赋值
                            BrandBlueKeywordArchivePo.builder()
                                    .configId(cId)
                                    .avid(avid)
                                    .status(BlueKeywordItemStatusEnum.INVALID.getCode())
                                    .version(version)
                                    .isDeleted(IsDeleted.VALID.getCode())
                                    .ctime(new Timestamp(System.currentTimeMillis()))
                                    .mtime(new Timestamp(System.currentTimeMillis()))
                                    .build())
                    .collect(Collectors.toList());
            BatchUtil.batch(archivePoList, subArchivePos -> {
                this.blueKeywordArchiveDao.insertBatch(subArchivePos);
                return Collections.emptyList();
            }, 200);
            List<BrandBlueKeywordItemPo> keywordPoList = keywordList.stream()
                    .map(kw -> //由于采用的是批量插入，因此所有字段必须赋值
                            BrandBlueKeywordItemPo.builder()
                                    .configId(cId)
                                    .keyword(kw)
                                    .status(BlueKeywordItemStatusEnum.INVALID.getCode())
                                    .version(version)
                                    .isDeleted(IsDeleted.VALID.getCode())
                                    .ctime(new Timestamp(System.currentTimeMillis()))
                                    .mtime(new Timestamp(System.currentTimeMillis()))
                                    .build())
                    .collect(Collectors.toList());
            BatchUtil.batch(keywordPoList, subKeywordPos -> {
                this.blueKeywordItemDao.insertBatch(subKeywordPos);
                return Collections.emptyList();
            }, 200);

            return BlueKeywordConfigDto.builder()
                    .id(configId)
                    .itemVersion(version)
                    .archiveCount(avidList.size())
                    .keywordCount(keywordList.size())
                    .build();
        } finally {
            if (Objects.nonNull(locker)) {
                locker.unlock();
            }
        }
    }

    @Override
    public Map<Long, BlueKeywordConfigItemDto> getBlueKeywordConfigItem(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Maps.newHashMap();
        }
        Map<Long, BlueKeywordConfigItemDto> result = Maps.newHashMap();
        Map<Long, List<String>> blueKeywordItemMap = this.getBlueKeywordItem(configIdList);
        Map<Long, List<Long>> blueKeywordArchiveMap = this.getBlueKeywordArchive(configIdList);
        for (Long configId : configIdList) {
            BlueKeywordConfigItemDto configItem = BlueKeywordConfigItemDto.builder()
                    .configId(configId)
                    .avidList(blueKeywordArchiveMap.getOrDefault(configId, Collections.emptyList()))
                    .keywordList(blueKeywordItemMap.getOrDefault(configId, Collections.emptyList()))
                    .build();
            result.put(configId, configItem);
        }
        return result;
    }

    @Override
    public Map<Long, List<BlueKeywordConfigDto>> getArchiveBlueKeywordConfig(List<Long> avidList) {
        Map<Long, List<Long>> archiveConfigIdMap = this.getBlueKeywordConfigIdByArchive(avidList);
        List<Long> configIdList = archiveConfigIdMap.values().stream()
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, List<BlueKeywordConfigDto>> result = Maps.newHashMap();
        if (CollectionUtils.isEmpty(configIdList)) {
            return result;
        }

        BlueKeywordConfigQueryDto query = BlueKeywordConfigQueryDto.builder()
                .idList(configIdList)
                .status(BlueKeywordStatusEnum.VISIBLE_STATUS)
                .build();
        BrandBlueKeywordPoExample example = this.buildBrandBlueKeywordPoExample(query);
        List<BrandBlueKeywordPo> poList = this.blueKeywordDao.selectByExample(example);
        Map<Long, BlueKeywordConfigDto> configMap = poList.stream()
                .map(po -> BlueKeywordConverter.MAPPER.toBlueKeywordConfigDto(po, null, null))
                .collect(Collectors.toMap(BlueKeywordConfigDto::getId, Function.identity()));

        archiveConfigIdMap.forEach((avid, cidList) -> {
            List<BlueKeywordConfigDto> configList = cidList.stream()
                    .map(configMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(configList)) {
                result.put(avid, configList);
            }
        });
        return result;
    }

    /**
     * 刷新小蓝词配置
     * 1、开始投放
     * 1.1、添加到archive-extra服务中
     * 1.2、构建缓存
     * <p>
     * 2、结束投放
     * 2.1、从archive-extra服务中移除
     * 2.2、从缓存中移除
     */
    @Override
    public void refreshBlueKeywordConfig() throws Exception {
        LocalDateTime now = LocalDateTime.now();
        Timestamp todayBegin = Utils.getBeginOfDay(Timestamp.valueOf(now));
        Timestamp todayEnd = Utils.getEndOfDay(Timestamp.valueOf(now));
        Timestamp yesterdayBegin = Utils.getBeginOfDay(Timestamp.valueOf(now.minusDays(1)));
        //投放时间是天维度，且开始和结束时间分别是00:00:00和23:59:59

        //投放开始：只需要判断投放开始时间是不是今天即可
        BlueKeywordConfigQueryDto startQuery = BlueKeywordConfigQueryDto.builder()
                .status(Lists.newArrayList(BlueKeywordStatusEnum.ENABLE.getCode()))
                .startBeginTime(todayBegin)
                .endBeginTime(todayEnd)
                .build();
        BrandBlueKeywordPoExample startExample = this.buildBrandBlueKeywordPoExample(startQuery);
        List<BrandBlueKeywordPo> startPoList = this.blueKeywordDao.selectByExample(startExample);
        for (BrandBlueKeywordPo po : startPoList) {
            log.info("[BlueKeywordService] refreshBlueKeywordConfig cur config, config={}", JSON.toJSONString(po));
            this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                    .actionType(BrandEvent.ActionType.START)
                    .configId(po.getId())
                    .build());
        }

        //投放结束：只需要判断投放结束时间是不是昨天即可
        BlueKeywordConfigQueryDto endQuery = BlueKeywordConfigQueryDto.builder()
                .status(Lists.newArrayList(BlueKeywordStatusEnum.ENABLE.getCode()))
                .startEndTime(yesterdayBegin)
                .endEndTime(todayBegin)
                .build();
        BrandBlueKeywordPoExample endExample = this.buildBrandBlueKeywordPoExample(endQuery);
        List<BrandBlueKeywordPo> endPoList = this.blueKeywordDao.selectByExample(endExample);
        for (BrandBlueKeywordPo po : endPoList) {
            this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                    .actionType(BrandEvent.ActionType.END)
                    .configId(po.getId())
                    .build());
        }
    }

    /**
     * 尝试刷新可能被遗漏的，一般用于用于数据补偿
     */
    public void refreshBlueKeywordConfig4Id(List<Long> configIdList) throws Exception {
        if (CollectionUtils.isEmpty(configIdList)) {
            return;
        }
        BlueKeywordConfigQueryDto enableQuery = BlueKeywordConfigQueryDto.builder()
                .status(Lists.newArrayList(BlueKeywordStatusEnum.ENABLE.getCode()))
                .idList(configIdList)
                .build();
        BrandBlueKeywordPoExample enableExample = this.buildBrandBlueKeywordPoExample(enableQuery);
        List<BrandBlueKeywordPo> enablePoList = this.blueKeywordDao.selectByExample(enableExample);
        for (BrandBlueKeywordPo po : enablePoList) {
            this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                    .actionType(BrandEvent.ActionType.ENABLE)
                    .configId(po.getId())
                    .build());
        }

        BlueKeywordConfigQueryDto disableQuery = BlueKeywordConfigQueryDto.builder()
                .status(Lists.newArrayList(BlueKeywordStatusEnum.DISABLE.getCode()))
                .idList(configIdList)
                .build();
        BrandBlueKeywordPoExample disableExample = this.buildBrandBlueKeywordPoExample(disableQuery);
        List<BrandBlueKeywordPo> disablePoList = this.blueKeywordDao.selectByExample(disableExample);
        for (BrandBlueKeywordPo po : disablePoList) {
            this.brandEventPublisher.publish(BlueKeywordConfigChangeEvent.builder()
                    .actionType(BrandEvent.ActionType.DISABLE)
                    .configId(po.getId())
                    .build());
        }
    }


    /**
     * 该方法不需要事务，原因：
     * 1、uploadBlueKeywordConfigItem和saveBlueKeywordConfig是两个独立的过程，只要其自身是事务的即可，即使saveBlueKeywordConfig失败，
     * uploadBlueKeywordConfigItem产生的数据也是不可见的
     * 2、uploadBlueKeywordConfigItem中的BatchUtil.batch会运行在独立的事务中，从而会导致事务隔离问题，表现是saveBlueKeywordConfig中无法更新稿件和关键词信息（因为不可见）
     */
    @Override
    public Long saveBlueKeywordConfigV2(BlueKeywordConfigDto config, BlueKeywordConfigItemDto item, Operator operator) throws Exception {
        log.info("[BlueKeywordService] saveBlueKeywordConfigV2, config={}, item={}, operator={}",
                JSON.toJSONString(config), JSON.toJSONString(item), JSON.toJSONString(operator));
        if (StringUtils.isEmpty(operator.getOperatorName())) {
            operator.setOperatorName("SYSTEM");
        }
        if (Utils.isPositive(config.getId())) {
            item.setConfigId(config.getId());
        }
        IBlueKeywordService blueKeywordService = (IBlueKeywordService) AopContext.currentProxy();
        BlueKeywordConfigDto tempConfig = blueKeywordService.uploadBlueKeywordConfigItem(item, operator);
        config.setId(tempConfig.getId());
        config.setItemVersion(tempConfig.getItemVersion());
        return blueKeywordService.saveBlueKeywordConfig(config, operator);
    }

    private void basicValidate(Operator operator) {
        Assert.notNull(operator, "非法操作");
        Assert.hasText(operator.getOperatorName(), "非法操作");
    }


    //查询可见状态的配置项
    private BrandBlueKeywordPo getVisibleBrandBlueKeywordPo(Long id) {
        BlueKeywordConfigQueryDto query = BlueKeywordConfigQueryDto.builder()
                .idList(Lists.newArrayList(id))
                .status(BlueKeywordStatusEnum.VISIBLE_STATUS)
                .build();
        BrandBlueKeywordPoExample example = this.buildBrandBlueKeywordPoExample(query);
        List<BrandBlueKeywordPo> poList = this.blueKeywordDao.selectByExample(example);
        return CollectionUtils.isEmpty(poList) ? null : poList.get(0);
    }

    //查询所有状态的配置项
    private BrandBlueKeywordPo getBrandBlueKeywordPo(Long id) {
        BlueKeywordConfigQueryDto query = BlueKeywordConfigQueryDto.builder()
                .idList(Lists.newArrayList(id))
                .build();
        BrandBlueKeywordPoExample example = this.buildBrandBlueKeywordPoExample(query);
        List<BrandBlueKeywordPo> poList = this.blueKeywordDao.selectByExample(example);
        return CollectionUtils.isEmpty(poList) ? null : poList.get(0);
    }

    private BrandBlueKeywordPoExample buildBrandBlueKeywordPoExample(BlueKeywordConfigQueryDto query) {
        BrandBlueKeywordPoExample example = new BrandBlueKeywordPoExample();
        BrandBlueKeywordPoExample.Criteria criteria = example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }
        if (!CollectionUtils.isEmpty(query.getStatus())) {
            criteria.andStatusIn(query.getStatus());
        }
        if (StringUtils.hasText(query.getName())) {
            criteria.andNameLike("%" + query.getName().trim() + "%");
        }
        if (Objects.nonNull(query.getStartBeginTime())) {
            criteria.andBeginTimeGreaterThanOrEqualTo(query.getStartBeginTime());
        }
        if (Objects.nonNull(query.getEndBeginTime())) {
            criteria.andBeginTimeLessThanOrEqualTo(query.getEndBeginTime());
        }
        if (Objects.nonNull(query.getStartEndTime())) {
            criteria.andEndTimeGreaterThanOrEqualTo(query.getStartEndTime());
        }
        if (Objects.nonNull(query.getEndEndTime())) {
            criteria.andEndTimeLessThanOrEqualTo(query.getEndEndTime());
        }
        return example;
    }


    /**
     * 查询指定配置id的，其明细信息中最大的版本号
     */
    private Integer getMaxItemVersion(Long configId) {
        BrandBlueKeywordItemPoExample example = new BrandBlueKeywordItemPoExample();
        example.createCriteria()
                .andConfigIdEqualTo(configId);
        example.setOrderByClause("version desc");
        example.setLimit(1);
        List<BrandBlueKeywordItemPo> itemPoList = this.blueKeywordItemDao.selectByExample(example);
        return CollectionUtils.isEmpty(itemPoList) ? 0 : itemPoList.get(0).getVersion();
    }


    /**
     * //生效明细
     *
     * @param configId
     * @param itemVersion
     * @return 返回旧明细
     */
    private BlueKeywordConfigItemDto applyBlueKeywordConfigItem(Long configId, Integer itemVersion) {
        BlueKeywordConfigItemDto.BlueKeywordConfigItemDtoBuilder builder = BlueKeywordConfigItemDto.builder()
                .configId(configId);
        if (!Utils.isPositive(itemVersion)) {
            return builder.build();
        }

        //1、尝试删除旧版本的关键词明细
        BrandBlueKeywordItemPoExample deleteKeywordExample = new BrandBlueKeywordItemPoExample();
        deleteKeywordExample.createCriteria()
                .andConfigIdEqualTo(configId)
                .andStatusEqualTo(BlueKeywordItemStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BrandBlueKeywordItemPo> keywordPoList = this.blueKeywordItemDao.selectByExample(deleteKeywordExample);
        if (!CollectionUtils.isEmpty(keywordPoList)) {
            List<String> keywordList = keywordPoList.stream().map(BrandBlueKeywordItemPo::getKeyword).collect(Collectors.toList());
            List<Long> idList = keywordPoList.stream().map(BrandBlueKeywordItemPo::getId).collect(Collectors.toList());
            log.info("[BlueKeywordService] applyBlueKeywordConfigItem delete old keywords, configId={}, idList={}, keywordList={}",
                    configId, JSON.toJSONString(idList), JSON.toJSONString(keywordList));

            builder.keywordList(keywordList);

            BrandBlueKeywordItemPo enablePo = BrandBlueKeywordItemPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build();
            deleteKeywordExample = new BrandBlueKeywordItemPoExample();
            deleteKeywordExample.createCriteria().andIdIn(idList);
            this.blueKeywordItemDao.updateByExampleSelective(enablePo, deleteKeywordExample);
        }

        //2、尝试删除旧版本的稿件明细
        BrandBlueKeywordArchivePoExample deleteArchiveExample = new BrandBlueKeywordArchivePoExample();
        deleteArchiveExample.createCriteria()
                .andConfigIdEqualTo(configId)
                .andStatusEqualTo(BlueKeywordItemStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BrandBlueKeywordArchivePo> archivePoList = this.blueKeywordArchiveDao.selectByExample(deleteArchiveExample);
        if (!CollectionUtils.isEmpty(archivePoList)) {
            List<Long> avidList = archivePoList.stream().map(BrandBlueKeywordArchivePo::getAvid).collect(Collectors.toList());
            List<Long> idList = archivePoList.stream().map(BrandBlueKeywordArchivePo::getId).collect(Collectors.toList());

            log.info("[BlueKeywordService] applyBlueKeywordConfigItem delete old archives, configId={}, idList={}, avidList={}",
                    configId, JSON.toJSONString(idList), JSON.toJSONString(avidList));

            builder.avidList(avidList);

            BrandBlueKeywordArchivePo enablePo = BrandBlueKeywordArchivePo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build();
            deleteArchiveExample = new BrandBlueKeywordArchivePoExample();
            deleteArchiveExample.createCriteria().andIdIn(idList);
            this.blueKeywordArchiveDao.updateByExampleSelective(enablePo, deleteArchiveExample);
        }


        //3、生效关键词最新版本
        BrandBlueKeywordItemPoExample enableKeywordExample = new BrandBlueKeywordItemPoExample();
        enableKeywordExample.createCriteria()
                .andConfigIdEqualTo(configId)
                .andVersionEqualTo(itemVersion) //指定版本（也是最新上传的版本）
                .andStatusEqualTo(BlueKeywordItemStatusEnum.INVALID.getCode()) //上传明细时状态是无效
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        keywordPoList = this.blueKeywordItemDao.selectByExample(enableKeywordExample);
        if (!CollectionUtils.isEmpty(keywordPoList)) {
            List<String> keywordList = keywordPoList.stream().map(BrandBlueKeywordItemPo::getKeyword).collect(Collectors.toList());
            List<Long> idList = keywordPoList.stream().map(BrandBlueKeywordItemPo::getId).collect(Collectors.toList());

            log.info("[BlueKeywordService] applyBlueKeywordConfigItem enable new keywords, configId={}, idList={}, keywordList={}",
                    configId, JSON.toJSONString(idList), JSON.toJSONString(keywordList));

            BrandBlueKeywordItemPo enablePo = BrandBlueKeywordItemPo.builder()
                    .status(BlueKeywordItemStatusEnum.VALID.getCode())
                    .build();
            enableKeywordExample = new BrandBlueKeywordItemPoExample();
            enableKeywordExample.createCriteria().andIdIn(idList);
            this.blueKeywordItemDao.updateByExampleSelective(enablePo, enableKeywordExample);
        }


        //4、生效稿件最新版本
        BrandBlueKeywordArchivePoExample enableArchiveExample = new BrandBlueKeywordArchivePoExample();
        enableArchiveExample.createCriteria()
                .andConfigIdEqualTo(configId)
                .andVersionEqualTo(itemVersion) //指定版本（也是最新上传的版本）
                .andStatusEqualTo(BlueKeywordItemStatusEnum.INVALID.getCode()) //上传明细时状态是无效
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        archivePoList = this.blueKeywordArchiveDao.selectByExample(enableArchiveExample);
        if (!CollectionUtils.isEmpty(archivePoList)) {
            List<Long> avidList = archivePoList.stream().map(BrandBlueKeywordArchivePo::getAvid).collect(Collectors.toList());
            List<Long> idList = archivePoList.stream().map(BrandBlueKeywordArchivePo::getId).collect(Collectors.toList());

            log.info("[BlueKeywordService] applyBlueKeywordConfigItem enable new archives, configId={}, idList={}, avidList={}",
                    configId, JSON.toJSONString(idList), JSON.toJSONString(avidList));

            BrandBlueKeywordArchivePo enablePo = BrandBlueKeywordArchivePo.builder()
                    .status(BlueKeywordItemStatusEnum.VALID.getCode())
                    .build();
            enableArchiveExample = new BrandBlueKeywordArchivePoExample();
            enableArchiveExample.createCriteria().andIdIn(idList);
            this.blueKeywordArchiveDao.updateByExampleSelective(enablePo, enableArchiveExample);
        }

        return builder.build();
    }

    /**
     * 查询配置项的关键词
     *
     * @param configIdList
     * @return
     */
    private Map<Long, List<String>> getBlueKeywordItem(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Maps.newHashMap();
        }

        BrandBlueKeywordItemPoExample example = new BrandBlueKeywordItemPoExample();
        example.createCriteria()
                .andConfigIdIn(configIdList)
                .andStatusEqualTo(BlueKeywordItemStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BrandBlueKeywordItemPo> itemPoList = this.blueKeywordItemDao.selectByExample(example);
        return itemPoList.stream()
                .collect(Collectors.groupingBy(BrandBlueKeywordItemPo::getConfigId,
                        Collectors.mapping(BrandBlueKeywordItemPo::getKeyword, Collectors.toList())));
    }

    /**
     * 查询配置项的稿件
     *
     * @param configIdList
     * @return
     */
    private Map<Long, List<Long>> getBlueKeywordArchive(List<Long> configIdList) {
        if (CollectionUtils.isEmpty(configIdList)) {
            return Maps.newHashMap();
        }

        BrandBlueKeywordArchivePoExample example = new BrandBlueKeywordArchivePoExample();
        example.createCriteria()
                .andConfigIdIn(configIdList)
                .andStatusEqualTo(BlueKeywordItemStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BrandBlueKeywordArchivePo> itemPoList = this.blueKeywordArchiveDao.selectByExample(example);
        return itemPoList.stream()
                .collect(Collectors.groupingBy(BrandBlueKeywordArchivePo::getConfigId,
                        Collectors.mapping(BrandBlueKeywordArchivePo::getAvid, Collectors.toList())));
    }

    /**
     * 根据稿件查询对应的配置项
     *
     * @param avidList
     * @return
     */
    private Map<Long, List<Long>> getBlueKeywordConfigIdByArchive(List<Long> avidList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Maps.newHashMap();
        }

        BrandBlueKeywordArchivePoExample example = new BrandBlueKeywordArchivePoExample();
        example.createCriteria()
                .andAvidIn(avidList)
                .andStatusEqualTo(BlueKeywordItemStatusEnum.VALID.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BrandBlueKeywordArchivePo> itemPoList = this.blueKeywordArchiveDao.selectByExample(example);
        return itemPoList.stream()
                .collect(Collectors.groupingBy(BrandBlueKeywordArchivePo::getAvid,
                        Collectors.mapping(BrandBlueKeywordArchivePo::getConfigId, Collectors.toList())));
    }

    //初始化一个空的配置项（无效状态）
    private Long initBlueKeywordConfig() {
        Timestamp defaultTimestamp = Utils.getTimestamp("2024-01-01", "yyyy-MM-dd");
        BrandBlueKeywordPo po = BrandBlueKeywordPo.builder()
                .name("SYSTEM")
                .beginTime(Utils.getBeginOfDay(defaultTimestamp))
                .endTime(Utils.getEndOfDay(defaultTimestamp))
                .status(BlueKeywordStatusEnum.INVALID.getCode())
                .build();
        this.blueKeywordDao.insertSelective(po);
        return po.getId();
    }
}
