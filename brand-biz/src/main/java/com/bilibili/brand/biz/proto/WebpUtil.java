package com.bilibili.brand.biz.proto;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.launch.dto.ImageResolution;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.CommandLine;
import org.apache.commons.exec.DefaultExecutor;
import org.apache.commons.exec.ExecuteResultHandler;
import org.apache.commons.exec.ExecuteWatchdog;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.Assert;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class WebpUtil {
    private static final String COMMAND_PATH;
    private static final String IMG2WEBP_COMMAND_PATH;
    private static final String WEBPINFO_COMMAND_PATH;

    private static final DefaultExecutor defaultExecutor;

    static {
        defaultExecutor = new DefaultExecutor();
        defaultExecutor.setWatchdog(new ExecuteWatchdog(3 * 60 * 1000));

        OsInfo osInfo = SystemUtil.getOsInfo();
        String pImg2webp = "img2webp";
        String pWebpinfo = "webpinfo";
        if (osInfo.isWindows()) {
            pImg2webp = "img2webp-windows.exe";
            pWebpinfo = "webpinfo-windows.exe";
        } else if (osInfo.isMac() || osInfo.isMacOsX()) {
            if (osInfo.getArch().equals("x86_64")) {
                pImg2webp = "img2webp-mac86";
                pWebpinfo = "webpinfo-mac86";
            } else {
                pImg2webp = "img2webp-mac";
                pWebpinfo = "webpinfo-mac";
            }
        }

        COMMAND_PATH = "data/temp/command/webp".replaceAll("/", Matcher.quoteReplacement(File.separator));
        IMG2WEBP_COMMAND_PATH = COMMAND_PATH + File.separator + pImg2webp;
        WEBPINFO_COMMAND_PATH = COMMAND_PATH + File.separator + pWebpinfo;

        InputStream img2webpInputStream = null;
        InputStream webpinfoInputStream = null;
        FileOutputStream img2webpFileOutputStream = null;
        FileOutputStream webpInfoFileOutputStream = null;

        try {
            ClassLoader classLoader = WebpUtil.class.getClassLoader();
            Utils.createDirIfNotExist(COMMAND_PATH);
            String filePath = "command/webp".replaceAll("/", Matcher.quoteReplacement(File.separator));
            img2webpInputStream = classLoader.getResourceAsStream(filePath + File.separator + pImg2webp);
            webpinfoInputStream = classLoader.getResourceAsStream(filePath + File.separator + pWebpinfo);
            img2webpFileOutputStream = new FileOutputStream(IMG2WEBP_COMMAND_PATH);
            webpInfoFileOutputStream = new FileOutputStream(WEBPINFO_COMMAND_PATH);
            assert img2webpInputStream != null;
            IOUtils.copy(img2webpInputStream, img2webpFileOutputStream);
            assert webpinfoInputStream != null;
            IOUtils.copy(webpinfoInputStream, webpInfoFileOutputStream);
            if (!osInfo.isWindows()) {
                RuntimeUtil.execForStr("chmod 777 -R " + IMG2WEBP_COMMAND_PATH);
                RuntimeUtil.execForStr("chmod 777 -R " + WEBPINFO_COMMAND_PATH);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (Objects.nonNull(img2webpInputStream)) {
                    img2webpInputStream.close();
                }
                if (Objects.nonNull(webpinfoInputStream)) {
                    webpinfoInputStream.close();
                }
                if (Objects.nonNull(img2webpFileOutputStream)) {
                    img2webpFileOutputStream.close();
                }
                if (Objects.nonNull(webpInfoFileOutputStream)) {
                    webpInfoFileOutputStream.close();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public static ImageResolution decodeWebp(File file) {
        try {
            String execResult = RuntimeUtil.execForStr(WEBPINFO_COMMAND_PATH, file.getAbsolutePath());
            Pattern pattern = Pattern.compile("Canvas size (\\d+) x (\\d+)");
            Matcher matcher = pattern.matcher(execResult);
            if (matcher.find()) {
                log.info("WebpService decodeWebp match，execResult：{}，matcher:{}", GsonUtils.toJson(execResult), matcher);
                int width = Integer.parseInt(matcher.group(1));
                int height = Integer.parseInt(matcher.group(2));
                return new ImageResolution(width, height);
            }
            log.info("WebpService decodeWebp not match，execResult：{}，matcher:{}", GsonUtils.toJson(execResult), matcher);
            return new ImageResolution(0, 0);
        } catch (Exception e) {
            log.error("WebpService decodeWebp error", e);
            return new ImageResolution(0, 0);
        }
    }

    public static void generateWebp(List<String> filePathList, String outPutFileName,
                                    Boolean enableMixed, Integer qualityRatio, Integer frameRate,
                                    ExecuteResultHandler resultHandler) throws IOException {
        Assert.notEmpty(filePathList, "文件路径列表不能为空");
        Assert.notNull(outPutFileName, "输出文件名不能为空");

        // 构建命令行参数
        CommandLine commandLine = new CommandLine(IMG2WEBP_COMMAND_PATH);
        commandLine.addArgument("-o");
        commandLine.addArgument(outPutFileName);
        commandLine.addArgument("-loop");
        commandLine.addArgument("" + 0);
        if (BooleanUtils.isTrue(enableMixed)) {
            commandLine.addArgument("-mixed");
        }
        filePathList.forEach(path -> {
            if (Utils.isPositive(qualityRatio)) {
                commandLine.addArgument("-lossy");
                commandLine.addArgument("-q");
                commandLine.addArgument("" + qualityRatio);
            }
            if (Utils.isPositive(frameRate)) {
                int frameDuration = 1000 / frameRate;
                commandLine.addArgument("-d");
                commandLine.addArgument("" + frameDuration);
            }
            commandLine.addArgument(FileUtil.normalize(path));
        });

        // 执行命令
        if (Objects.nonNull(resultHandler)) {
            defaultExecutor.execute(commandLine, resultHandler);
        } else {
            defaultExecutor.execute(commandLine);
        }
    }
}
