package com.bilibili.brand.biz.order.service;

import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.service.ICycleFrequencyService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * @link IGdOrderExtService
 */
@Slf4j
@Service
public class CycleFrequencyService implements ICycleFrequencyService {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Override
    public boolean isCycleFrequencyOrder(GdOrderExtDto gdOrderExtDto) {
        return Objects.nonNull(gdOrderExtDto) && BooleanUtils.isTrue(gdOrderExtDto.getEnableCycleFrequency());
    }

    @Override
    public void validateWhenCreate(Integer orderProduct, GdOrderExtDto createParam) {
        // 参数不能为空
        Assert.notNull(orderProduct, "orderProduct不能为空");

        // 启用周期频控
        if (this.isCycleFrequencyOrder(createParam)) {
            // 基础参数为空校验
            Assert.notNull(createParam.getCycleFrequencyBeginTime(), "周期频控开始时间不能为空");
            Assert.notNull(createParam.getCycleFrequencyEndTime(), "周期频控结束时间不能为空");
            Assert.notNull(createParam.getCycleFrequencyInterval(), "周期频控间隔不能为空");
            Assert.notNull(createParam.getCycleFrequencyLimit(), "周期频控次数不能为空");

            // 参数值校验
            Assert.isTrue(createParam.getCycleFrequencyBeginTime().compareTo(createParam.getCycleFrequencyEndTime()) < 0, "周期频控开始时间必须小于周期频控结束时间");
            Assert.isTrue(createParam.getCycleFrequencyInterval() <= TimeUtils.countDays(createParam.getCycleFrequencyBeginTime(), createParam.getCycleFrequencyEndTime()),
                    "频控单位不可超过订单周期范围");
            Assert.isTrue(createParam.getCycleFrequencyInterval() > 0, "周期频控间隔必须大于0");
            if (OrderProduct.SSA_PRODUCT_SET.contains(orderProduct) || OrderProduct.TOPVIEW_CODE_SET.contains(orderProduct)) {
                Assert.isTrue(createParam.getCycleFrequencyInterval() <= 14, "周期频控间隔必须小于等于14");
            } else {
                Assert.isTrue(createParam.getCycleFrequencyInterval() <= 7, "周期频控间隔必须小于等于7");
            }
            Assert.isTrue(createParam.getCycleFrequencyLimit() > 0, "周期频控次数必须大于0");
            Assert.isTrue(createParam.getCycleFrequencyLimit() <= 6, "周期频控次数必须小于等于6");
        }
    }

    @Override
    public void validateWhenUpdate(Integer orderProduct, GdOrderExtDto updateParam, GdOrderExtDto oldData) {
        // 1. 首先必须满足创建校验
        this.validateWhenCreate(orderProduct, updateParam);

        // 2. 其次必须满足更新校验
        if (Objects.isNull(oldData)) {
            Assert.isTrue(Objects.isNull(updateParam) || BooleanUtils.isNotTrue(updateParam.getEnableCycleFrequency()), "周期频控状态不可修改");
        } else {
            Assert.isTrue(Objects.nonNull(updateParam) && Objects.equals(updateParam.getEnableCycleFrequency(), oldData.getEnableCycleFrequency()), "周期频控状态不可修改");
        }
        if (this.isCycleFrequencyOrder(updateParam)) {
            Assert.isTrue(updateParam.getCycleFrequencyBeginTime().compareTo(oldData.getCycleFrequencyBeginTime()) == 0, "周期频控开始时间不可修改");
            Assert.isTrue(updateParam.getCycleFrequencyEndTime().compareTo(oldData.getCycleFrequencyEndTime()) >= 0, "周期频控结束时间不可早于原周期频控结束时间");
            Assert.isTrue(Objects.equals(updateParam.getCycleFrequencyInterval(), oldData.getCycleFrequencyInterval()), "周期频控间隔不可修改");
            Assert.isTrue(Objects.equals(updateParam.getCycleFrequencyLimit(), oldData.getCycleFrequencyLimit()), "周期频控次数不可修改");
        }
    }

    @Override
    public void validateInSchedule(GdOrderExtDto gdOrderExtDto, Timestamp startTime, Timestamp endTime) {
        if (this.isCycleFrequencyOrder(gdOrderExtDto)) {
            Assert.isTrue(startTime.compareTo(gdOrderExtDto.getCycleFrequencyBeginTime()) >= 0, "排期开始时间不在周期频控时间内");
            Assert.isTrue(endTime.compareTo(gdOrderExtDto.getCycleFrequencyEndTime()) <= 0, "排期结束时间不在周期频控时间内");
        }
    }

    @Override
    public int queryCycleFrequencyRaise() {
        Integer raiseRatio = this.systemConfigService.getValueReturnInt(SystemConfigEnum.CYCLE_FREQUENCY_CONTROL_RAISE_RATIO.getCode());
        Assert.isTrue(raiseRatio >= 0, "周期频控加收比例必须大于等于0");
        return raiseRatio;
    }
}
