package com.bilibili.brand.biz.order.dao;

import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.order.po.FcOrderPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface FcOrderDao {
    long countByExample(FcOrderPoExample example);

    int deleteByExample(FcOrderPoExample example);

    int deleteByPrimaryKey(Integer orderId);

    int insertUpdate(FcOrderPo record);

    int insertBatch(List<FcOrderPo> records);

    int insertUpdateBatch(List<FcOrderPo> records);

    int insert(FcOrderPo record);

    int insertUpdateSelective(FcOrderPo record);

    int insertSelective(FcOrderPo record);

    List<FcOrderPo> selectByExample(FcOrderPoExample example);

    FcOrderPo selectByPrimaryKey(Integer orderId);

    int updateByExampleSelective(@Param("record") FcOrderPo record, @Param("example") FcOrderPoExample example);

    int updateByExample(@Param("record") FcOrderPo record, @Param("example") FcOrderPoExample example);

    int updateByPrimaryKeySelective(FcOrderPo record);

    int updateByPrimaryKey(FcOrderPo record);
}