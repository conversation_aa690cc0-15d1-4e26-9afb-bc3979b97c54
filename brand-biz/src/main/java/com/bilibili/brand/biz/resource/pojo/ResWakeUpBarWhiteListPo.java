package com.bilibili.brand.biz.resource.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResWakeUpBarWhiteListPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * schema
     */
    private String appSchema;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 删除标识 0-有效 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}