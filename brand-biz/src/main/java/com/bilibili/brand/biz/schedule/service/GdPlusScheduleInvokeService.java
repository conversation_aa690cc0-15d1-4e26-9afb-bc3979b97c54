package com.bilibili.brand.biz.schedule.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.GoblinSceneTypeEnum;
import com.bilibili.brand.api.schedule.service.inventory.IGoblinInventoryService;
import com.bilibili.brand.biz.schedule.helper.TargetHelper;
import com.bilibili.brand.biz.template.BrandTemplateHelper;
import com.bilibili.enums.PlatformType;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.resource.price.IGdPriceService;
import com.bilibili.brand.api.resource.price.gd.PriceDto;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.*;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.stock.BrushStockDto;
import com.bilibili.brand.api.stock.dto.ResourceInfoBo;
import com.bilibili.brand.api.stock.dto.LocationDto;
import com.bilibili.brand.api.stock.dto.ssa.*;
import com.bilibili.brand.api.schedule.dto.SplitDaysStockDto;
import com.bilibili.brand.api.schedule.dto.StockDto;
import com.bilibili.brand.api.schedule.dto.StockPriceDto;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.utils.TargetUtil;
import com.bilibili.brand.biz.schedule.dao.GdFlowAllocationDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTargetRatioDao;
import com.bilibili.brand.biz.schedule.handler.ScheduleTargetHandler;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPo;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPoExample;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPoExample;
import com.bilibili.brand.biz.schedule.service.frequency.OttResourceService;
import com.bilibili.brand.common.Constant;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.GdType;
import com.bilibili.cpt.platform.common.ResourceType;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.ssa.platform.common.enums.BusinessType;
import com.bilibili.ssa.platform.common.enums.OpType;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description gd异步查询库存
 * <AUTHOR>
 * @Date 2020.10.21 22:58
 */
@Slf4j
@Service
@Deprecated
public class GdPlusScheduleInvokeService {

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private GdFlowAllocationDao gdFlowAllocationDao;

    @Value("${gd.schedule.invoke.url:domain/AllocationService/allocate}")
    private String queryStockUrl;

    @Value("${gd.schedule.query.discovery.addr.url:http://discovery.bilibili.co/discovery/nodes}")
    private String queryDiscoveryAddrUrl;

    @Value("${gd.schedule.query.discovery.url:/discovery/fetchs}")
    private String queryDiscoveryUrl;

    @Value("${top.flow.schedule.need.ratio:95}")
    private Long topFlowNeedRatio;


    private static final String HTTP = "http://";

    @Autowired
    private ScheduleTargetHandler targetHandler;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IGdPriceService cpmPriceService;

    @Autowired
    private GdScheduleTargetRatioDao targetRatioDao;

    @Autowired
    private OttResourceService ottFrequencyService;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private ScheduleLimitService limitService;

    @Resource(name = "completableFutureExecutorWithDecorator")
    private Executor completableFutureExecutor;

    @Autowired
    private BrandTemplateHelper brandTemplateHelper;

    @Autowired
    private TargetHelper targetHelper;

    @Autowired
    private IGoblinInventoryService goblinInventoryService;

    private static final List<Integer> brushTimes = Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10);

    private static final List<Integer> roundTimes = Lists.newArrayList(1, 2, 3, 4, 5);

    //注意 调用异步的方法和异步方法调用的有事务注解方法不能写到当前类，不然会失效
//    @Async
//    public void queryGdStockAsync(BestGroupStockParamDto query,
//                                  List<TargetRule> targetRules,
//                                  String dealSeq) throws ServiceException {
//
//        log.info("queryGdStockAsync bestGroupStockParamDto: [{}]", query);
//        log.info("queryGdStockAsync targetRules: [{}]", targetRules);
//
//        //查询库存
//        List<SplitDaysStockDto> splitDaysStocks;
//        List<PriceDto> prices;
//        StockPriceDto stockPriceDto;
//        try {
//            Map<Pair<Timestamp, Timestamp>, StockDto> stockMap = new HashMap<>();
//            queryGDStock(stockMap, query, dealSeq);
//            log.info("stockMap content is [{}]", JSON.toJSONString(stockMap));
//
//            //库存聚合
//            splitDaysStocks = getGdSplitDaysStockVo(stockMap);
//            log.info("queryGdStockAsync splitDaysStocks is [{}]", splitDaysStocks);
//
//            //起飞同稿件需要限制库存
//            splitDaysStocks = limitService.getFlyLimitCpmByTemplate(query.getPromotionPurposeType(),
//                    query.getTemplateId(),
//                    query.getGdType(),
//                    splitDaysStocks,
//                    query.getAvid(),
//                    query.getPlatformIds(),
//                    query.getSupportSourceCombineLaunch());
//
//            //计算价格
//            prices = cpmPriceService.getPrice(query, targetRules);
//            log.info("queryGdStockAsync prices is [{}]", prices);
//
//            stockPriceDto = StockPriceDto.builder()
//                    .dealSeq(dealSeq)
//                    .stockCpm(splitDaysStocks.stream()
//                            .mapToLong(SplitDaysStockDto::getStockCpm)
//                            .sum())
//                    .splitDaysStocks(splitDaysStocks)
//                    .isDealFinish(true)
//                    .prices(prices)
//                    .build();
//
//
//            limitUnderFrameIfNecessary(query.getTemplateId(), stockPriceDto);
//
//        } catch (Exception e) {
//            log.error("stockUpdateAsync error" + Throwables.getStackTraceAsString(e));
//            stockPriceDto = StockPriceDto.builder().stockCpm(0L).dealSeq(dealSeq)
//                    .isDealFinish(true).isSuccess(false).errorMsg(e.getMessage()).build();
//        }
//
//        redisService.setValue(dealSeq, stockPriceDto);
//    }

    private void limitUnderFrameIfNecessary(Integer templateId, StockPriceDto stockPriceDto) {
        Boolean limitUnderFrameLongImage = configCenter.getGdPlusConfig().getLimitUnderFrameLongImage();
        Integer underFrameLongImageTemplateId = configCenter.getGdPlusConfig().getUnderFrameLongImageTemplateId();
        Long underFrameLimitCpm = configCenter.getGdPlusConfig().getUnderFrameLimitCpm();

        if (limitUnderFrameLongImage && underFrameLongImageTemplateId.equals(templateId)) {

            Function<String, Timestamp> stringTimeToTimestamp = stringTime -> {
                try {
                    SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return new Timestamp(timeFormat.parse(stringTime).getTime());
                } catch (ParseException e) {
                    log.warn("框下长图限制日预约量转换日期时发生异常:{}", ExceptionUtils.getStackTrace(e));
                    throw new RuntimeException(e);
                }
            };

            List<Timestamp> timesList = stockPriceDto.getSplitDaysStocks().stream()
                    .map(info -> stringTimeToTimestamp.apply(info.getBeginTime()))
                    .sorted()
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(timesList)) {
                Timestamp begin = timesList.get(0);
                Timestamp end = timesList.get(timesList.size() - 1);

                List<ScheduleDto> schedules = queryScheduleService.querySchedule(QueryScheduleDto.builder()
                        .templateIds(Collections.singletonList(templateId))
                        .statusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                        .beginDate(begin)
                        .endDate(end)
                        .build());

                Map<Timestamp, List<ScheduleDto>> dateScheduleMap = schedules.stream().collect(Collectors.groupingBy(ScheduleDto::getBeginDate));

                stockPriceDto.getSplitDaysStocks().forEach(inventory -> {
                    Timestamp date = stringTimeToTimestamp.apply(inventory.getBeginTime());
                    List<ScheduleDto> specifyDateSchedules = dateScheduleMap.get(date);
                    int alreadyReserveInventory = 0;
                    if (!CollectionUtils.isEmpty(specifyDateSchedules)) {
                        alreadyReserveInventory = specifyDateSchedules.stream().mapToInt(ScheduleDto::getTotalImpression).sum();
                    }
                    long restInventory = underFrameLimitCpm - alreadyReserveInventory;
                    if (restInventory < 0) {
                        restInventory = 0;
                    }
                    log.info("框下长图预约情况，日期:{}，已约量：{}，剩余量:{}", inventory.getBeginTime(), alreadyReserveInventory,
                            restInventory);
                    inventory.setStockCpm(Math.min(restInventory, inventory.getStockCpm()));
                });

                stockPriceDto.setStockCpm(stockPriceDto.getSplitDaysStocks().stream()
                        .mapToLong(SplitDaysStockDto::getStockCpm)
                        .sum());
            }
        }
    }

//    public void queryGDStock(Map<Pair<Timestamp, Timestamp>, StockDto> stockResDtoMap,
//                             BestGroupStockParamDto query, String dealSeq) {
//        Map<Pair<Timestamp, Timestamp>, List<TargetRule>> targetMap = query.getTargetMap();
//        //多线程处理查询
//        CompletableFuture.allOf(query.getTimeInfo().stream()
//                        .map(timeInfo -> CompletableFuture.runAsync(() -> {
//
//                                    List<TargetRule> targetRules = targetMap.get(timeInfo);
//
//                                    targetRules = proccessTarget(query, timeInfo, targetRules);
//
//                                    List<StockTagGroupDto> tagGroups = targetHandler.getTagGroupDtoS(targetRules);
//
//                                    StockDto stock = query(query.getGdType(),
//                                            tagGroups,
//                                            query.isToday(),
//                                            dealSeq,
//                                            timeInfo.getFirst(),
//                                            query.getFrequencyLimit(),
//                                            query.isFistBrush(),
//                                            query.getTemplateId(),
//                                            query.getResourceType(),
//                                            true);
//
//                                    stockResDtoMap.put(timeInfo, stock);
//
//                                }, completableFutureExecutor)
//                                .whenComplete((result, exception) -> {
//                                    if (exception != null) {
//                                        log.warn("gd寻量发生异常:{}", ExceptionUtils.getStackTrace(exception));
//                                    }
//                                }))
//                        .toArray(CompletableFuture[]::new))
//                .join();
//    }

    private List<TargetRule> proccessTarget(BestGroupStockParamDto query,
                                            Pair<Timestamp, Timestamp> timeInfo,
                                            List<TargetRule> targetRules) {

        log.info("queryGDStock targetRules [{}]", targetRules);
        List<TargetRule> copiedTargetRules =
                GsonUtils.toList(GsonUtils.toJson(targetRules), TargetRule.class);

        //寻量逻辑包含起飞品牌大小卡，所以在寻量阶段都需要传source
        targetHelper.processOsTarget(query.getTemplateId(), copiedTargetRules, query.getPlatformIds(), true, query.getResourceType());

        return copiedTargetRules;
    }
//
//    public StockDto query(Integer gdType,
//                          List<StockTagGroupDto> groupDtos,
//                          boolean isToday,
//                          String dealSeq,
//                          Timestamp launchTime,
//                          Integer frequencyLimit,
//                          boolean firstBrush,
//                          Integer templateId,
//                          Integer resourceType,
//                          boolean isQueryRequest) {
//
//        List<StockTaskDto> taskDtoS = new ArrayList<>();
//        boolean isFlyCard = GdType.FLY.getCode().equals(gdType)
//                && (ResourceType.BIG_CARD.getCode() == resourceType
//                || ResourceType.SMALL_CARD.getCode() == resourceType);
//        //1、起飞gd因为频控是1所以查询多个刷次，只能在其中一个刷次出量
//        // 但是起飞的冷启动首刷已经固定了冷启动刷次,不需要重新加上刷次信息
//        //2、GD Story 前期只计算brush=1的库存，顾不传刷次
//        if (isFlyCard && !firstBrush) {
//            List<List<StockTagGroupDto>> lists;
//            if (ResourceType.BIG_CARD.getCode() == resourceType) {
//
//                //起飞大卡需要使用是个冷启动次数查询,然后返回库存最大的冷启刷次库存
//                lists = targetHandler.getTagGroupDtoSWithBrushTimes(groupDtos, brushTimes, resourceType);
//            } else {
//                //起飞小卡需要使用轮数查询,然后返回库存最大的轮数库存
//                lists = targetHandler.getTagGroupDtoSWithBrushTimes(groupDtos, roundTimes, resourceType);
//            }
//            lists.forEach(list -> {
//                StockAdInfoDto adInfoDto = StockAdInfoDto.builder()
//                        .tag_groups(list).build();
//                StockTaskDto taskReqDto = StockTaskDto.builder()
//                        .is_today_schedule(isToday)
//                        .force_update(false).op_type(OpType.QUERY.getCode())
//                        .ad_info(adInfoDto).build();
//                taskDtoS.add(taskReqDto);
//            });
//        } else {
//            StockAdInfoDto adInfoDto = StockAdInfoDto.builder()
//                    .tag_groups(groupDtos).build();
//            StockTaskDto taskReqDto = StockTaskDto.builder()
//                    .is_today_schedule(isToday)
//                    .force_update(false).op_type(OpType.QUERY.getCode())
//                    .ad_info(adInfoDto).build();
//            taskDtoS.add(taskReqDto);
//        }
//
//        GoblinSceneTypeEnum scene = getScene(templateId);
//
//        QueryStockRequestDto reqDto = QueryStockRequestDto.builder().request_id(dealSeq)
//                .date(TimeUtil.timestampToBasicDateStr(TimeUtils.getBeginOfDay(launchTime)))
//                .tasks(taskDtoS)
//                .scene(scene.getCode())
//                .build();
//
//        Integer gdMaxFrequency = this.configCenter.getGdPlusConfig().getGdMaxFrequency();
//
//        try {
//            //todo 部分逻辑在后续设备合并中重构
//            QueryStockResponseDto resDto = null;
//            if (isQueryRequest) {
//                resDto = doQueryGoblin(reqDto, launchTime, scene, dealSeq);
//                redisService.cacheGoblinRes(dealSeq, launchTime, resDto);
//            } else {
//                //暂时仅对起飞大卡生效这个逻辑
//                if (isFlyCard && ResourceType.BIG_CARD.getCode() == resourceType) {
//                    resDto = redisService.getGoblinRes(dealSeq, launchTime, QueryStockResponseDto.class);
//                }
//                if (resDto == null) {
//                    resDto = doQueryGoblin(reqDto, launchTime, scene, dealSeq);
//                } else {
//                    log.info("该库存信息从缓存中获取:{}", resDto);
//                    //信息流只有ios或android
//                    int iosInlineTargetId = 450;
//                    int androidInlineTargetId = 451;
//                    int osTargetId = groupDtos.stream().anyMatch(group -> group.getTag_group().contains(iosInlineTargetId)) ?
//                            iosInlineTargetId : androidInlineTargetId;
//
//                    resDto.getTasks().forEach(resTask -> {
//                        StockAdInfoDto resAdInfo = resTask.getAd_info();
//                        List<StockTagGroupDto> resTagGroups = resAdInfo.getTag_groups();
//                        resTagGroups.removeIf(resTagGroup -> {
//                            Set<Integer> resGroup = resTagGroup.getTag_group();
//                            return !resGroup.contains(osTargetId);
//                        });
//
//                        resAdInfo.setTotal_flow_impression(resTagGroups.stream().filter(group -> group.getFlow_impression() != null).mapToLong(StockTagGroupDto::getFlow_impression).sum());
//                    });
//                }
//            }
//
//            List<StockTaskDto> taskS;
//            if (resDto != null && !CollectionUtils.isEmpty(taskS = resDto.getTasks())) {
//                long maxImpression = 0L;
//                long androidMaxImpression = 0;
//                long iphoneMaxImpression = 0;
//                StockDto stockDto = new StockDto();
//                if (isFlyCard) {
//                    //起飞大卡需要返回每个冷启动刷次的库存
//                    if (ResourceType.BIG_CARD.getCode() == resourceType) {
//                        long brushImpression;
//                        List<BrushStockDto> brushStockDtoS = new ArrayList<>();
//                        for (StockTaskDto taskDto : taskS) {
//                            StockAdInfoDto info = taskDto.getAd_info();
//                            if (info != null) {
//                                brushImpression = info.getTotal_flow_impression() == null ?
//                                        0 : (info.getTotal_flow_impression() / 1000);
//                                maxImpression = Math.max(maxImpression, brushImpression);
//                                BrushStockDto brushStockDto = new BrushStockDto();
//
//                                brushStockDto.setBrushTime(targetHandler.getInlineTargetId(info.getTag_groups()
//                                        .get(0).getTag_group()));
//                                brushStockDto.setImpression(brushImpression);
//                                brushStockDtoS.add(brushStockDto);
//                            }
//                        }
//                        brushStockDtoS = brushStockDtoS.stream()
//                                .sorted(Comparator.comparing(BrushStockDto::getImpression).reversed())
//                                .collect(Collectors.toList());
//                        stockDto.setBrushStockDtoS(brushStockDtoS);
//                    }
//
//                    //起飞小卡需要返回每个轮次 + source的库存
//                    if (ResourceType.SMALL_CARD.getCode() == resourceType) {
//                        List<BrushStockDto> brushStockDtoS = new ArrayList<>();
//                        //遍历每一个轮次
//                        for (StockTaskDto taskDto : taskS) {
//                            StockAdInfoDto info = taskDto.getAd_info();
//                            //source映射库存对象
//                            Map<Integer, BrushStockDto> source2BrushStockDto = new HashMap<>();
//                            if (info != null) {
//                                List<StockTagGroupDto> tagGroupDtos = info.getTag_groups();
//                                if (!CollectionUtils.isEmpty(tagGroupDtos)) {
//                                    int round = targetHandler.getRoundTargetId(tagGroupDtos.get(0).getTag_group());
//                                    //遍历每一个小定向组，把他们按照资源位分类
//                                    tagGroupDtos.forEach(groupDto -> {
//                                        int source = targetHandler.getSourceTargetId(groupDto.getTag_group());
//                                        BrushStockDto brushStockDto = source2BrushStockDto.get(source);
//                                        //起飞小卡暂时先打对折
//                                        long brushImpression = groupDto.getFlow_impression() == null ?
//                                                0 : (groupDto.getFlow_impression() / (2 * 1000));
//                                        if (brushStockDto == null) {
//                                            brushStockDto = BrushStockDto.builder()
//                                                    .impression(brushImpression)
//                                                    .source(source).brushTime(round).build();
//                                        } else {
//                                            brushStockDto.setImpression(brushStockDto.getImpression() + brushImpression);
//                                        }
//                                        source2BrushStockDto.put(source, brushStockDto);
//                                    });
//                                }
//                                androidMaxImpression = Math.max(source2BrushStockDto.values()
//                                        .stream().filter(t -> PlatformType.ANDROID.getCode()
//                                                .equals(Constant.PROPHET_SMALL_CARD_SOURCE_2_PLATFORM.get(t.getSource())))
//                                        .map(BrushStockDto::getImpression).max(Comparator.comparing(Long::longValue))
//                                        .orElse(0L), androidMaxImpression);
//
//                                iphoneMaxImpression = Math.max(source2BrushStockDto.values()
//                                        .stream().filter(t -> PlatformType.IPHONE.getCode()
//                                                .equals(Constant.PROPHET_SMALL_CARD_SOURCE_2_PLATFORM.get(t.getSource())))
//                                        .map(BrushStockDto::getImpression).max(Comparator.comparing(Long::longValue))
//                                        .orElse(0L), iphoneMaxImpression);
//                                brushStockDtoS.addAll(source2BrushStockDto.values());
//                            }
//                        }
//                        brushStockDtoS = brushStockDtoS.stream()
//                                .sorted(Comparator.comparing(BrushStockDto::getImpression).reversed())
//                                .collect(Collectors.toList());
//                        stockDto.setBrushStockDtoS(brushStockDtoS);
//                        maxImpression = androidMaxImpression + iphoneMaxImpression;
//                    }
//                    //小卡需要返回每个source的库存
//                } else if (ResourceType.SMALL_CARD.getCode() == resourceType) {
//                    List<BrushStockDto> brushStockDtoS = new ArrayList<>();
//                    //遍历每一个轮次
//                    for (StockTaskDto taskDto : taskS) {
//                        StockAdInfoDto info = taskDto.getAd_info();
//                        //source映射库存对象
//                        Map<Integer, BrushStockDto> source2BrushStockDto = new HashMap<>();
//                        if (info != null) {
//                            List<StockTagGroupDto> tagGroupDtos = info.getTag_groups();
//                            if (!CollectionUtils.isEmpty(tagGroupDtos)) {
//                                //遍历每一个小定向组，把他们按照资源位分类
//                                tagGroupDtos.forEach(groupDto -> {
//                                    int source = targetHandler.getSourceTargetId(groupDto.getTag_group());
//                                    BrushStockDto brushStockDto = source2BrushStockDto.get(source);
//                                    long brushImpression = groupDto.getFlow_impression() == null ?
//                                            0 : (groupDto.getFlow_impression() / 1000);
//                                    if (brushStockDto == null) {
//                                        brushStockDto = BrushStockDto.builder()
//                                                .impression(brushImpression)
//                                                .source(source).brushTime(0).build();
//                                    } else {
//                                        brushStockDto.setImpression(brushStockDto.getImpression() + brushImpression);
//                                    }
//                                    source2BrushStockDto.put(source, brushStockDto);
//                                });
//                                log.info("get small card source2BrushStockDto is [{}]", source2BrushStockDto);
//                            }
//                            androidMaxImpression = Math.max(source2BrushStockDto.values()
//                                    .stream().filter(t -> PlatformType.ANDROID.getCode()
//                                            .equals(Constant.PROPHET_SMALL_CARD_SOURCE_2_PLATFORM.get(t.getSource())))
//                                    .map(BrushStockDto::getImpression).max(Comparator.comparing(Long::longValue))
//                                    .orElse(0L), androidMaxImpression);
//
//                            iphoneMaxImpression = Math.max(source2BrushStockDto.values()
//                                    .stream().filter(t -> PlatformType.IPHONE.getCode()
//                                            .equals(Constant.PROPHET_SMALL_CARD_SOURCE_2_PLATFORM.get(t.getSource())))
//                                    .map(BrushStockDto::getImpression).max(Comparator.comparing(Long::longValue))
//                                    .orElse(0L), iphoneMaxImpression);
//
//                            brushStockDtoS.addAll(source2BrushStockDto.values());
//                        }
//                    }
//                    brushStockDtoS = brushStockDtoS.stream()
//                            .sorted(Comparator.comparing(BrushStockDto::getImpression).reversed())
//                            .collect(Collectors.toList());
//                    maxImpression = androidMaxImpression + iphoneMaxImpression;
//                    maxImpression = Utils.isPositive(frequencyLimit)
//                            && frequencyLimit < gdMaxFrequency ?
//                            maxImpression * frequencyLimit / gdMaxFrequency
//                            : maxImpression;
//                    stockDto.setBrushStockDtoS(brushStockDtoS);
//                } else {
//                    //story 会走到这个分支
//                    StockAdInfoDto info = resDto.getTasks().get(0).getAd_info();
//                    maxImpression = info.getTotal_flow_impression() == null ?
//                            0 : (info.getTotal_flow_impression() / 1000);
//                    if (!firstBrush && !ottFrequencyService.isMatch(ResourceInfoBo.builder()
//                            .templateId(templateId).build())) {
//                        maxImpression = Utils.isPositive(frequencyLimit)
//                                && frequencyLimit < gdMaxFrequency ?
//                                maxImpression * frequencyLimit / gdMaxFrequency
//                                : maxImpression;
//                    }
//                    if (ottFrequencyService.isMatch(ResourceInfoBo.builder()
//                            .templateId(templateId).build())) {
//                        maxImpression = maxImpression / configCenter.getOttInlineConfig()
//                                .getOttGdInventoryDiscount();
//                    }
//                }
//                stockDto.setStockCpm(maxImpression);
//                return stockDto;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("invokeQueryGdStock", e);
//        }
//        return StockDto.builder().stockCpm(0L).build();
//    }

//    private QueryStockResponseDto doQueryGoblin(QueryStockRequestDto reqDto,
//                                                Timestamp launchTime,
//                                                GoblinSceneTypeEnum scene,
//                                                String dealSeq) {
//
//        log.info("查询库存请求，req:[{}]", GsonUtils.toJson(reqDto));
//        String queryAddress = this.queryStockUrl.replace("domain",
//                getAddress(launchTime.toLocalDateTime().getDayOfWeek().getValue(), scene.getAppid()));
//        log.info("执行寻量的地址为:{}", queryAddress);
//
//        QueryStockResponseDto resDto = OkHttpUtils.bodyPost(queryAddress)
//                .header("Content-Type", "application/json")
//                .bean(reqDto)
//                .callForObject(QueryStockResponseDto.class);
//        log.info("查询库存返回，res:[{}]", GsonUtils.toJson(resDto));
//
//        return resDto;
//    }

//    private GoblinSceneTypeEnum getScene(Integer templateId) {
//        if (templateId != null && configCenter.getGdPlusConfig().getUnderBoxTemplates().contains(templateId)) {
//            return GoblinSceneTypeEnum.UNDERFRAME;
//        }
//        if (templateId != null && configCenter.getGdPlusConfig().getSmallCardTemplateIds().contains(templateId)) {
//            return GoblinSceneTypeEnum.SMALL_CARD;
//        }
//        if (templateId != null && configCenter.getGdPlusConfig().getStoryGdPlusTemplateIds().contains(templateId)) {
//            return GoblinSceneTypeEnum.STORY;
//        }
//        if (templateId != null && configCenter.getGdPlusConfig().getPlayerDetailTemplateIds().contains(templateId)) {
//            return GoblinSceneTypeEnum.UNDERFRAME;
//        }
//        return GoblinSceneTypeEnum.INLINE;
//    }


//    //注意 调用异步的方法和异步方法调用的有事务注解方法不能写到当前类，不然会失效
//    public Map<Integer, Long> orderGDImpression(NewScheduleDto scheduleDto, boolean isForceUpdate,
//                                                Map<Integer, Integer> platformId2Schedule) {
//
//        Map<Integer, Long> platformId2Impression = new HashMap<>();
//        Map<Integer, StockDto> platformId2Stock = new HashMap<>();
//
//        platformId2Schedule.keySet().forEach(t -> {
//            List<TargetRule> targetRules = scheduleDto.getTargetRules();
//            targetRules = targetRules.stream().filter(x ->
//                            !TargetType.OS.getCode().equals(x.getRuleType()))
//                    .collect(Collectors.toList());
//            targetRules.add(TargetRule.builder()
//                    .ruleType(TargetType.OS.getCode())
//                    .valueIds(brandTemplateHelper.getGoblinPlatformTarget(t, scheduleDto.getTemplateId()))
//                    .build());
//            List<StockTagGroupDto> groupDtos = targetHandler.getTagGroupDtoS(targetRules);
//
//            StockDto stockDto = query(scheduleDto.getGdType(), groupDtos,
//                    IsValid.TRUE.getCode().equals(scheduleDto.getIsTodaySchedule()),
//                    scheduleDto.getDealSeq().toString(),
//                    scheduleDto.getBeginTime(),
//                    scheduleDto.getFrequencyLimit(),
//                    scheduleDto.isFirstBrush(),
//                    scheduleDto.getTemplateId(),
//                    scheduleDto.getResourceType(),
//                    false);
//
//            platformId2Impression.put(t, stockDto.getStockCpm());
//            platformId2Stock.put(t, stockDto);
//        });
//        long sumImpression = platformId2Impression.values().stream().mapToLong(t -> t).sum();
//        long totalImpression = scheduleDto.getTotalImpression();
//        if (!isForceUpdate) {
//            Assert.isTrue(sumImpression >= totalImpression,
//                    "可用库存为" + sumImpression + "cpm不足" + totalImpression + "cpm");
//        }
//        Assert.isTrue(sumImpression != 0, "goblin可用库存为" + sumImpression);
//        Map<Integer, Long> platformId2RealImpression = new HashMap<>();
//        AtomicLong used = new AtomicLong(0L);
//        platformId2Impression.forEach((k, v) -> {
//            long useTemp = totalImpression * v / sumImpression;
//            platformId2RealImpression.put(k, useTemp);
//            used.addAndGet(useTemp);
//        });
//        long left;
//        if ((left = totalImpression - used.get()) != 0) {
//            List<Integer> platformIds = new ArrayList<>(platformId2Schedule.keySet());
//            Integer platformId = platformIds.get(0);
//            platformId2RealImpression.put(platformId, platformId2RealImpression.get(platformId) + left);
//        }
//
//        Map<Integer, List<Integer>> platformId2Brush = new HashMap<>();
//        Map<Integer, List<Pair<Integer, Integer>>> platformId2SourceWithBrush = new HashMap<>();
//        if (GdType.FLY.getCode().equals(scheduleDto.getGdType()) && !scheduleDto.isFirstBrush()) {
//            //起飞的询量约量要带上冷启动刷次
//            if (ResourceType.BIG_CARD.getCode() == scheduleDto.getResourceType()) {
//                platformId2Stock.forEach((k, v) -> {
//                    List<BrushStockDto> brushStockDtoS = v.getBrushStockDtoS();
//                    List<Integer> availableBrush = new ArrayList<>();
//                    brushStockDtoS.forEach(t -> {
//                        if (t.getImpression() >= platformId2RealImpression.get(k)) {
//                            availableBrush.add(t.getBrushTime());
//                        }
//                    });
//                    Assert.notEmpty(availableBrush, "fly gd bigCard has no suit availableSourceWithBrush");
//                    platformId2Brush.put(k, availableBrush);
//                });
//            } else if (ResourceType.SMALL_CARD.getCode() == scheduleDto.getResourceType()) {
//                //起飞的小卡要带上刷次信息,因为它只能约一个刷次
//                platformId2Stock.forEach((k, v) -> {
//                    List<BrushStockDto> brushStockDtoS = v.getBrushStockDtoS();
//                    List<Pair<Integer, Integer>> availableSourceWithBrush = new ArrayList<>();
//                    brushStockDtoS.forEach(t -> {
//                        if (t.getImpression() >= platformId2RealImpression.get(k)) {
//                            availableSourceWithBrush.add(Pair.of(t.getSource(), t.getBrushTime()));
//                        }
//                    });
//                    Assert.notEmpty(availableSourceWithBrush, "fly gd smallCard has no suit availableSourceWithBrush");
//                    platformId2SourceWithBrush.put(k, availableSourceWithBrush);
//                });
//            }
//        } else if (ResourceType.SMALL_CARD.getCode() == scheduleDto.getResourceType()) {
//            platformId2Stock.forEach((k, v) -> {
//                List<BrushStockDto> brushStockDtoS = v.getBrushStockDtoS();
//                List<Pair<Integer, Integer>> availableSourceWithBrush = new ArrayList<>();
//                brushStockDtoS.forEach(t -> {
//                    if (t.getImpression() >= platformId2RealImpression.get(k)) {
//                        availableSourceWithBrush.add(Pair.of(t.getSource(), t.getBrushTime()));
//                    }
//                });
//                Assert.notEmpty(availableSourceWithBrush, "gd smallCard has no suit availableBrush");
//                platformId2SourceWithBrush.put(k, availableSourceWithBrush);
//            });
//        } else if (scheduleDto.isFirstBrush()) {
//            platformId2Stock.forEach((k, v) -> platformId2Brush.put(k,
//                    Lists.newArrayList(targetHandler.getTargetIdByTargetValue(1,
//                            TargetType.INLINE_SALES_TYPE.getCode()))));
//        }
//
//        for (Map.Entry<Integer, Long> entry : platformId2RealImpression.entrySet()) {
//            Integer platformId = entry.getKey();
//            Long v = entry.getValue();
//            List<TargetRule> targetRules = scheduleDto.getTargetRules();
//            targetRules = targetRules.stream().filter(x ->
//                            !TargetType.OS.getCode().equals(x.getRuleType()))
//                    .collect(Collectors.toList());
//            targetRules.add(TargetRule.builder().ruleType(TargetType.OS.getCode())
//                    .valueIds(Lists.newArrayList(brandTemplateHelper.getGoblinPlatformTarget(platformId,
//                            scheduleDto.getTemplateId())))
//                    .build());
//
//            List<StockTagGroupDto> groupDtos;
//            if (scheduleDto.isFirstBrush() || GdType.FLY.getCode().equals(scheduleDto.getGdType())) {
//                List<TargetRule> targetRulesCopy = targetRules.stream()
//                        .map(t -> new TargetRule()).collect(Collectors.toList());
//                Collections.copy(targetRulesCopy, targetRules);
//                targetRulesCopy = targetRulesCopy.stream().filter(t -> !TargetType.INLINE_SALES_TYPE.getCode()
//                        .equals(t.getRuleType())).collect(Collectors.toList());
//                groupDtos = targetHandler.getTagGroupDtoS(targetRulesCopy);
//            } else {
//                groupDtos = targetHandler.getTagGroupDtoS(targetRules);
//            }
//
//            StockAdDetailDto adDetailDto = StockAdDetailDto.builder()
//                    .ad_id(platformId2Schedule.get(platformId)).build();
//            StockAdInfoDto adInfoDto = StockAdInfoDto.builder()
//                    .total_flow_impression(v * 1000)
//                    .business_type(BusinessType.ALL.getCode())
//                    .ad_detail(adDetailDto)
//                    .tag_groups(!CollectionUtils.isEmpty(groupDtos) ? groupDtos
//                            : Lists.newArrayList(StockTagGroupDto.builder()
//                            .tag_group(new HashSet<>()).build())).build();
//
//            if (GdType.FLY.getCode().equals(scheduleDto.getGdType())
//                    && ResourceType.BIG_CARD.getCode() == scheduleDto.getResourceType()) {
//
//                adInfoDto.setCold_boot_tags(platformId2Brush.get(platformId));
//            } else if (scheduleDto.isFirstBrush()) {
//                adInfoDto.setCold_boot_tags(platformId2Brush.get(platformId));
//            }
//
//            if (ResourceType.SMALL_CARD.getCode() == scheduleDto.getResourceType()) {
//                List<Pair<Integer, Integer>> sourceWithBrush = platformId2SourceWithBrush.get(platformId);
//                adInfoDto.setLocations(sourceWithBrush.stream()
//                        .map(Pair::getSecond)
//                        .distinct()
//                        .map(round -> LocationDto.builder()
//                                .round_tag(round)
//                                .build())
//                        .collect(Collectors.toList()));
//            }
//
//            StockTaskDto taskReqDto = StockTaskDto.builder()
//                    .is_today_schedule(scheduleDto.getIsTodaySchedule() == 1)
//                    .force_update(isForceUpdate).op_type(OpType.LOCK.getCode())
//                    .ad_info(adInfoDto).build();
//
//            GoblinSceneTypeEnum scene = getScene(scheduleDto.getTemplateId());
//
//            QueryStockRequestDto reqDto = QueryStockRequestDto.builder()
//                    .request_id(scheduleDto.getDealSeq().toString())
//                    .date(TimeUtil.timestampToBasicDateStr(TimeUtils
//                            .getBeginOfDay(scheduleDto.getBeginTime())))
//                    .tasks(Lists.newArrayList(taskReqDto))
//                    .scene(scene.getCode())
//                    .build();
//
//            QueryStockResponseDto resDto = null;
//            try {
//                log.info("invokeOrderGD req:[{}]", reqDto);
//                resDto = OkHttpUtils.bodyPost(this.queryStockUrl
//                                .replace("domain", getAddress(scheduleDto.getBeginTime().toLocalDateTime()
//                                        .getDayOfWeek().getValue(), scene.getAppid())))
//                        .header("Content-Type", "application/json")
//                        .bean(reqDto)
//                        .callForObject(QueryStockResponseDto.class);
//
//                log.info("invokeOrderGD res:[{}]", resDto);
//            } catch (Exception e) {
//                e.printStackTrace();
//                log.info("invokeOrderGD" + e);
//            }
//
//            if (resDto != null && !CollectionUtils.isEmpty(resDto.getTasks())
//                    && (resDto.getCode() == null || resDto.getCode() == 0)) {
//                continue;
//            }
//            throw new IllegalArgumentException("锁定资源失败");
//        }
//        return platformId2RealImpression;
//    }

    public List<SplitDaysStockDto> getGdSplitDaysStockVo(Map<Pair<Timestamp, Timestamp>, StockDto> dayImpression) {
        List<SplitDaysStockDto> splitDaysStocks = new ArrayList<>();

        dayImpression.forEach((k, v) -> splitDaysStocks.add(SplitDaysStockDto.builder()
                .beginTime(TimeUtil.timestampToIsoTimeStr(k.getFirst()))
                .endTime(TimeUtil.timestampToIsoTimeStr(k.getSecond()))
                .stockCpm(v.getStockCpm()).build())
        );

        splitDaysStocks.sort((o1, o2) -> TimeUtil.compareTime(TimeUtil.isoDateStrToLocalDate(o1.getBeginTime()),
                TimeUtil.isoDateStrToLocalDate(o2.getBeginTime())));

        return splitDaysStocks;
    }
//
//    public void deleteImpression(Timestamp launchDate, Integer scheduleId, Integer templateId) {
//
//        StockAdDetailDto adDetailDto = StockAdDetailDto.builder().ad_id(scheduleId).build();
//        StockAdInfoDto adInfoDto = StockAdInfoDto.builder()
//                .ad_detail(adDetailDto)
//                .build();
//        StockTaskDto taskReqDto = StockTaskDto.builder()
//                .force_update(false).op_type(OpType.CANCEL.getCode())
//                .ad_info(adInfoDto).build();
//
//        GoblinSceneTypeEnum scene = getScene(templateId);
//
//        QueryStockRequestDto reqDto = QueryStockRequestDto.builder()
//                .request_id(String.valueOf(snowflakeIdWorker.nextId()))
//                .date(TimeUtil.timestampToBasicDateStr(TimeUtils
//                        .getBeginOfDay(launchDate)))
//                .tasks(Lists.newArrayList(taskReqDto))
//                .scene(scene.getCode())
//                .build();
//
//        try {
//            log.info("deleteOrderGd req:[{}]", reqDto);
//            QueryStockResponseDto resDto = OkHttpUtils.bodyPost(this.queryStockUrl
//                            .replace("domain", getAddress(launchDate.toLocalDateTime().getDayOfWeek().getValue(),
//                                    scene.getAppid())))
//                    .header("Content-Type", "application/json")
//                    .bean(reqDto)
//                    .callForObject(QueryStockResponseDto.class);
//
//            log.info("deleteOrderGd res:[{}]", resDto);
//
//            if (resDto != null && !CollectionUtils.isEmpty(resDto.getTasks())
//                    && (resDto.getCode() == null || resDto.getCode() == 0)) {
//                return;
//            }
//
//            throw new IllegalArgumentException("释放GD资源失败");
//        } catch (Exception e) {
//            log.info("deleteOrderGd 发生异常:{}", ExceptionUtils.getStackTrace(e));
//            throw e;
//        }
//    }

    public String getAddress(Integer dayOfWeek, String appId) {
        log.info("获取寻量地址参数 dayOfWeek:[{}] appId:[{}]", dayOfWeek, appId);
        QueryDiscoveryAddrResponseDto responseDto = OkHttpUtils.get(queryDiscoveryAddrUrl)
                .callForObject(QueryDiscoveryAddrResponseDto.class);
        log.info("queryDiscoveryAddrUrl res:[{}]", responseDto);
        if (responseDto == null || responseDto.getCode() != 0 || CollectionUtils.isEmpty(responseDto.getData())) {
            throw new IllegalArgumentException("查询Discovery地址失败!");
        }
        for (DiscoveryAddrResponseDto dto : responseDto.getData()) {
            if (dto.getStatus() == 0) {
                try {
                    QueryDiscoveryResponseDto res = OkHttpUtils.get(HTTP + dto.getAddr() + queryDiscoveryUrl)
                            .param("appid", appId)
                            .param("env", configCenter.getMetaDataConfig().getGoblinEnv())
                            .param("status", "1")
                            .callForObject(QueryDiscoveryResponseDto.class);
                    log.info("queryDiscoveryUrl res:[{}]", res);
                    if (res != null && res.getCode() == 0 && !CollectionUtils.isEmpty(res.getData())) {
                        QueryDiscoveryDataResponseDto dataResponseDto = res.getData().get(appId);
                        AppInstance instance = dataResponseDto.getInstances().stream().filter(t -> {
                            Map<String, Object> metadata = t.getMetadata();
                            String weight = (String) metadata.get("weight");
                            return dayOfWeek - 1 == Integer.parseInt(weight);
                        }).collect(Collectors.toList()).stream().findFirst().orElse(AppInstance.builder().build());
                        log.info("getAddress instance[{}]", instance);
                        return getHttpAddr(instance.getAddrs());
                    }
                } catch (Exception e) {
                    log.error("获取寻量地址发生异常:{}", ExceptionUtils.getStackTrace(e));
                }
            }
        }
        return "";
    }

    private String getHttpAddr(List<String> addresses) {
        log.info("getHttpAddr[{}]", addresses);
        if (CollectionUtils.isEmpty(addresses)) {
            return null;
        }
        String res = addresses.stream().filter(t -> t.startsWith("http")).collect(Collectors.toList())
                .stream().findFirst().orElse(null);
        log.info("getHttpAddr res [{}]", res);
        return res;
    }
//
//    public void batchRefreshHistory(Timestamp beginDate, Timestamp endDate, Integer resourceType) {
//        List<ScheduleDto> scheduleDtos = queryScheduleService
//                .querySchedule(QueryScheduleDto.builder().startTime(beginDate)
//                        .endTime(endDate)
//                        .salesTypes(Lists.newArrayList(SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode(),
//                                SalesType.FLY_PRE_PAY_GD_PLUS.getCode()))
//                        .statusList(Lists.newArrayList(1, 2)).build());
//        if (CollectionUtils.isEmpty(scheduleDtos)) {
//            log.info("batchRefreshGDHistory refresh scheduleDtoS is empty");
//            return;
//        }
//        List<Integer> scheduleIds = scheduleDtos.stream()
//                .map(ScheduleDto::getScheduleId).collect(Collectors.toList());
//        log.info("batchRefreshGDHistory refresh scheduleDtos maybe ids [{}]", scheduleIds);
//
//        GdFlowAllocationPoExample poExample = new GdFlowAllocationPoExample();
//        poExample.or().andScheduleIdIn(scheduleIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<GdFlowAllocationPo> allocationPos = gdFlowAllocationDao.selectByExample(poExample);
//        Map<Integer, GdFlowAllocationPo> allocationPoMap = new HashMap<>();
//        allocationPos.forEach(t -> allocationPoMap.put(t.getScheduleId(), t));
//
//        Set<Integer> dealScheduleIds;
//        if (ResourceType.SMALL_CARD.getCode() == resourceType) {
//            dealScheduleIds = allocationPos.stream()
//                    .filter(t -> Constant.GD_SMALL_CARD_SOURCE.contains(t.getSource()))
//                    .map(GdFlowAllocationPo::getScheduleId).collect(Collectors.toSet());
//        } else {
//            dealScheduleIds = allocationPos.stream()
//                    .filter(t -> Constant.GD_BIG_CARD_SOURCE.contains(t.getSource()))
//                    .map(GdFlowAllocationPo::getScheduleId).collect(Collectors.toSet());
//        }
//        scheduleDtos = scheduleDtos.stream()
//                .filter(t -> dealScheduleIds.contains(t.getScheduleId()))
//                .collect(Collectors.toList());
//        log.info("batchRefreshGDHistory refresh scheduleDtos final ids [{}]", dealScheduleIds);
//
//        List<NewScheduleDto> scheduleDtoS = new ArrayList<>();
//        scheduleDtos.forEach(t -> {
//            GdFlowAllocationPo allocationPo = allocationPoMap.get(t.getScheduleId());
//            NewScheduleDto scheduleDto = NewScheduleDto.builder().scheduleId(t.getScheduleId())
//                    .beginTime(t.getGdBeginTime()).endTime(t.getGdEndTime())
//                    .dealSeq(t.getUnitId()).crowdPackIds(t.getCrowdPackIds())
//                    .hour(t.getHour()).platformIds(Lists.newArrayList(t.getPlatformId()))
//                    .targetRules(scheduleTargetDtos2Rules(t.getTargets()))
//                    .isTodaySchedule(t.getIsTodaySchedule()).templateId(t.getTemplateId())
//                    .totalImpression(t.getTotalImpression().longValue())
//                    .gdType(allocationPo.getGdType()).resourceType(resourceType)
//                    .operator(Operator.builder().operatorId(t.getAccountId())
//                            .build()).supportSourceCombineLaunch(false).build();
//            scheduleDtoS.add(scheduleDto);
//        });
//
//        batchAdd(scheduleDtoS, 0, resourceType);
//    }

//    public void batchAdd(List<NewScheduleDto> scheduleDtoS, int count, int resourceType) {
//        List<NewScheduleDto> failScheduleDtoS = new ArrayList<>();
//        scheduleDtoS.forEach(t -> {
//            Map<Integer, Integer> platformId2Schedule = new HashMap<>();
//            Integer platformId = t.getPlatformIds().get(0);
//            platformId2Schedule.put(platformId, t.getScheduleId());
//            try {
//                try {
//                    orderGDImpression(t, true, platformId2Schedule);
//                } catch (Exception e) {
//                    log.error("orderGDImpression fail" + Throwables.getStackTraceAsString(e));
//                }
//
//                if (ResourceType.SMALL_CARD.getCode() == resourceType) {
//                    saveFlowAllocation(t, platformId, t.getTotalImpression(), platformId2Schedule);
//                    saveGdTargetRatio(t, platformId, t.getTotalImpression(), platformId2Schedule,
//                            0);
//                }
//            } catch (Exception e) {
//                log.error("batchAdd" + Throwables.getStackTraceAsString(e));
//                failScheduleDtoS.add(t);
//            }
//        });
//        if (!CollectionUtils.isEmpty(failScheduleDtoS) && count < 10) {
//            count++;
//            batchAdd(failScheduleDtoS, count, resourceType);
//        }
//    }

    private void saveFlowAllocation(NewScheduleDto newScheduleDto, Integer platformId,
                                    Long bookingCpm, Map<Integer, Integer> platform2ScheduleId) {
        GdFlowAllocationPoExample poExample = new GdFlowAllocationPoExample();
        poExample.or().andScheduleIdEqualTo(newScheduleDto.getScheduleId());
        List<GdFlowAllocationPo> gdFlowAllocationPos = gdFlowAllocationDao.selectByExample(poExample);
        if (!CollectionUtils.isEmpty(gdFlowAllocationPos)) {
            gdFlowAllocationDao.deleteByExample(poExample);
        }
        brandTemplateHelper.getSourceId(platformId,
                newScheduleDto.getTemplateId()).forEach(source -> {
            GdFlowAllocationPo flowAllocation = GdFlowAllocationPo.builder()
                    .source(source)
                    .cpms(Math.toIntExact(bookingCpm))
                    .launchDay(Utils.getBeginOfDay(newScheduleDto.getBeginTime()))
                    .scheduleId(platform2ScheduleId.get(platformId))
                    .gdType(newScheduleDto.getGdType())
                    .resourceType(newScheduleDto.getResourceType())
                    .build();
            log.info("saveFlowAllocation flowAllocation:[{}]", flowAllocation);
            gdFlowAllocationDao.insertSelective(flowAllocation);
        });
    }

    private void saveGdTargetRatio(NewScheduleDto newScheduleDto, Integer platformId,
                                   Long bookingCpm, Map<Integer, Integer> platform2ScheduleId,
                                   Integer targetKeyId) {
        GdScheduleTargetRatioPoExample poExample = new GdScheduleTargetRatioPoExample();
        poExample.or().andScheduleIdEqualTo(newScheduleDto.getScheduleId());
        List<GdScheduleTargetRatioPo> targetRatioPos = targetRatioDao.selectByExample(poExample);
        if (!CollectionUtils.isEmpty(targetRatioPos)) {
            targetRatioDao.deleteByExample(poExample);
        }
        brandTemplateHelper.getSourceId(platformId,
                newScheduleDto.getTemplateId()).forEach(source ->
                targetRatioDao.insertSelective(GdScheduleTargetRatioPo.builder()
                        //200是随便给的值,现在已经废弃了
                        .ratio(200)
                        .scheduleId(platform2ScheduleId.get(platformId))
                        .isGdPlus(newScheduleDto.getGdType())
                        .launchDay(Utils.getBeginOfDay(newScheduleDto.getBeginTime()))
                        .showCount(Math.toIntExact(bookingCpm)).innerTargetKeyId(targetKeyId)
                        .source(source)
                        .build()));
    }


    public List<TargetRule> scheduleTargetDtos2Rules(List<ScheduleTargetDto> scheduleTargets) {
        if (CollectionUtils.isEmpty(scheduleTargets)) {
            return new ArrayList<>();
        }
        return scheduleTargets.stream().map(t -> TargetRule.builder().ruleType(t.getTargetType())
                .valueIds(t.getTargetItemIds()).build()).collect(Collectors.toList());
    }

    public long getMaxOriginalStock(Timestamp date,
                                    Boolean isToday,
                                    Integer templateId,
                                    StockAdInfoDto adInfoDto) {
        log.info("getMaxOriginalStock date[{}], isToday [{}], templateId[{}], adInfoDto[{}]",
                date, isToday, templateId, adInfoDto);
        List<StockTaskDto> taskDtoS = new ArrayList<>();
        StockTaskDto taskReqDto = StockTaskDto.builder()
                .is_today_schedule(isToday)
                .force_update(false).op_type(OpType.QUERY_ORIGINAL.getCode())
                .ad_info(adInfoDto).build();
        taskDtoS.add(taskReqDto);

        GoblinSceneTypeEnum scene = goblinInventoryService.getScene(templateId);

        QueryStockRequestDto reqDto = QueryStockRequestDto.builder()
                .request_id(String.valueOf(snowflakeIdWorker.nextId()))
                .date(TimeUtil.timestampToBasicDateStr(TimeUtils.getBeginOfDay(date)))
                .tasks(taskDtoS)
                .scene(scene.getCode())
                .build();

        Long maxImpression = 0L;
        try {
            log.info("queryHourMaxImpression req:[{}]", reqDto);
            QueryStockResponseDto resDto = OkHttpUtils.bodyPost(this.queryStockUrl
                            .replace("domain", getAddress(date.toLocalDateTime().getDayOfWeek().getValue(),
                                    scene.getAppid())))
                    .header("Content-Type", "application/json")
                    .bean(reqDto)
                    .callForObject(QueryStockResponseDto.class);

            log.info("queryHourMaxImpression res:[{}]", resDto);

            if (resDto != null && !CollectionUtils.isEmpty(resDto.getTasks())) {
                StockAdInfoDto info = resDto.getTasks().get(0).getAd_info();
                maxImpression = info.getTotal_flow_impression();
                Assert.isTrue(Utils.isPositive(maxImpression), "查询goblin小时最大库存失败");
                maxImpression = info.getTotal_flow_impression() / 1000;
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("queryHourImpression" + e);
            throw new RuntimeException("查询goblin小时最大库存失败!");
        }

        Assert.isTrue(Utils.isPositive(maxImpression), "查询goblin小时最大库存失败");
        return maxImpression;
    }

//    public long queryAndCheckHourImpression(Timestamp beginTime,
//                                            Timestamp endTime,
//                                            Boolean isToday,
//                                            Integer templateId) {
//
//        //组装时间定向参数
//        List<TargetRule> targetRules = new ArrayList<>();
//        List<Integer> hours = new ArrayList<>();
//        for (int i = beginTime.toLocalDateTime().getHour();
//             i <= endTime.toLocalDateTime().getHour(); i++) {
//            hours.add(i);
//        }
//        targetRules.add(TargetRule.builder().ruleType(TargetType.HOUR.getCode())
//                .valueIds(hours).build());
//        List<StockTagGroupDto> groupDtos = targetHandler.getTagGroupDtoS(targetRules);
//
//        //拼接书否当日、定向、是否强制约量等信息
//        StockAdInfoDto adInfoDto = StockAdInfoDto.builder()
//                .tag_groups(groupDtos)
//                .build();
//        StockTaskDto taskReqDto = StockTaskDto.builder()
//                .is_today_schedule(isToday)
//                .force_update(false).op_type(OpType.QUERY_ORIGINAL.getCode())
//                .ad_info(adInfoDto).build();
//
//        List<StockTaskDto> taskDtoS = new ArrayList<>();
//        taskDtoS.add(taskReqDto);
//
//        //判断业务是框下还是大卡
//        GoblinSceneTypeEnum scene = getScene(templateId);
//
//        QueryStockRequestDto reqDto = QueryStockRequestDto.builder()
//                .request_id(String.valueOf(snowflakeIdWorker.nextId()))
//                .date(TimeUtil.timestampToBasicDateStr(TimeUtils.getBeginOfDay(beginTime)))
//                .tasks(taskDtoS)
//                .scene(scene.getCode())
//                .build();
//
//        long maxImpression = getMaxOriginalStock(beginTime, isToday, templateId, adInfoDto);
//
//        List<StockTaskDto> taskDtoList = new ArrayList<>();
//        StockTaskDto taskHourReqDto = StockTaskDto.builder()
//                .is_today_schedule(isToday)
//                .force_update(false).op_type(OpType.QUERY.getCode())
//                .ad_info(adInfoDto).build();
//        taskDtoList.add(taskHourReqDto);
//
//        QueryStockRequestDto reqHourDto = QueryStockRequestDto.builder()
//                .request_id(String.valueOf(snowflakeIdWorker.nextId()))
//                .date(TimeUtil.timestampToBasicDateStr(TimeUtils.getBeginOfDay(beginTime)))
//                .tasks(taskDtoList)
//                .scene(scene.getCode())
//                .build();
//
//        Long availableImpression = 0L;
//        try {
//            log.info("queryHourAvailableImpression req:[{}]", reqDto);
//            QueryStockResponseDto resDto = OkHttpUtils.bodyPost(this.queryStockUrl
//                            .replace("domain", getAddress(beginTime.toLocalDateTime()
//                                    .getDayOfWeek().getValue(), scene.getAppid())))
//                    .header("Content-Type", "application/json")
//                    .bean(reqHourDto)
//                    .callForObject(QueryStockResponseDto.class);
//            log.info("queryHourAvailableImpression res:[{}]", resDto);
//
//            if (resDto != null && !CollectionUtils.isEmpty(resDto.getTasks())) {
//                StockAdInfoDto info = resDto.getTasks().get(0).getAd_info();
//                availableImpression = info.getTotal_flow_impression();
//                Assert.isTrue(Utils.isPositive(availableImpression), "queryHourImpression fail");
//                availableImpression = info.getTotal_flow_impression() / 1000;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.info("queryHourImpression" + e);
//            throw new RuntimeException("查询goblin可用库存失败!");
//        }
//
//        Assert.isTrue(availableImpression * 100 / maxImpression >= topFlowNeedRatio,
//                "当前小时可用库存" + availableImpression + "不足,最大库存为" + maxImpression);
//
//        return availableImpression;
//    }


}


