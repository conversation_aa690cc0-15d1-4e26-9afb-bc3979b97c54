/** 
* <AUTHOR> 
* @date  2018年5月23日
*/ 

package com.bilibili.brand.biz.soa.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.soa.service.ISoaStockService;
import com.bilibili.brand.api.stock.dto.*;
import com.bilibili.brand.api.stock.dto.fly.*;
import com.bilibili.brand.api.stock.service.IStockService;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.schedule.service.FlyScheduleStockService;
import com.bilibili.cpt.platform.common.GdType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Service
public class SoaStockService implements ISoaStockService {

	@Autowired
	private IStockService stockService;

	@Autowired
	private FlyScheduleStockService flyScheduleStockService;

	@Override
	public QueryStockResDto queryStock(QueryStockDto queryStockDto) throws ServiceException {
		throw new ServiceException("已停服");
//		queryStockDto.setGdType(GdType.FLY.getCode());
//
//		return stockService.queryStock(queryStockDto);
	}

	@Deprecated
	@Override
	public void updateStock(UpdateStockDto updateStockDto) throws ServiceException {
		throw new ServiceException("已停服");
//		updateStockDto.setGdType(GdType.FLY.getCode());
//		stockService.updateStock(updateStockDto);
	}

	@Deprecated
	@Override
	public void lockStock(LockStockDto lockStockDto) throws ServiceException {
		throw new ServiceException("已停服");
//		lockStockDto.setGdType(GdType.FLY.getCode());
//		stockService.lockStock(lockStockDto);
	}

	@Override
	public void releaseStock(ReleaseStockDto releaseStockDto) throws ServiceException {
		stockService.releaseStock(releaseStockDto);
	}

	@Deprecated
	@Override
	public FlyStockPriceDto flyQueryStock(FlyStockTargetInfo flyStockTargetInfo) throws ServiceException {
		throw new ServiceException("已停服");
		//return stockService.flyQueryStock(flyStockTargetInfo);
	}


	@Override
	public FlyPriceResponseDto flyQueryTotalPrice(FlyPriceQueryDto flyPriceQueryDto) throws Exception {
		return flyScheduleStockService.getEveryDayPrices(flyPriceQueryDto);
	}

	@Deprecated
	@Override
	public void flyOperateSchedules(FlyOperatorSchedulesRequest request, Operator operator) throws Exception {
		throw new ServiceException("已停服");
//		flyScheduleStockService.flyOperateSchedules(request,operator);
	}

	@Override
	public void deleteBatchScheduleIds(List<Integer> unitIds,List<Integer> scheduleIds,Operator operator){
		flyScheduleStockService.deleteBatchScheduleIds(unitIds,scheduleIds,operator);
	}
}
