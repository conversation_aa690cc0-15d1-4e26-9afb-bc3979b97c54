package com.bilibili.brand.biz.stock;

public enum RequestStatusType {

		FAIL(-1, "FAIL"),
	    WAITING(0, "WAITING"),
	    SUCCESS(1, "SUCCESS");
		
	    private Integer code;
	    private String desc;

	    RequestStatusType(Integer code, String desc) {
	        this.desc = desc;
	        this.code = code;
	    }
	    
	    public final static RequestStatusType getByCode(int code) {
	        for (RequestStatusType requestStatusType : values()) {
	            if (requestStatusType.code == code) {
	                return requestStatusType;
	            }
	        }

	        return null;
	    }

	    public Integer getCode() {
	        return code;
	    }

	    public String getDesc() {
	        return desc;
	    }

}
