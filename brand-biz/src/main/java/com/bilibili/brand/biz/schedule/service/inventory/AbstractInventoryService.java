package com.bilibili.brand.biz.schedule.service.inventory;

import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.bo.InventoryContext;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.schedule.helper.TargetHelper;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.bilibili.utils.FuncUtil;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/7/26 10:24
 */
public abstract class AbstractInventoryService {
    @Autowired
    protected ISystemConfigService systemConfigService;
    @Autowired
    protected ConfigCenter configCenter;
    @Autowired
    protected TargetHelper targetHelper;

    /**
     * 是否支持mock库存
     */
    protected boolean isSupportsMockInventory(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnListInt(SystemConfigEnum.INVENTORY_MOCK_ORDER_WHITE_LIST.getCode()).contains(oId),
                () -> false);
    }

    /**
     * 查询mock的库存
     */
    protected Integer getMockInventory(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnInt(SystemConfigEnum.INVENTORY_MOCK_CPM.getCode()),
                () -> 0);
    }

    /**
     * 查询mock的轮数
     */
    protected Integer getMockRotation(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnInt(SystemConfigEnum.INVENTORY_MOCK_ROTATION.getCode()),
                () -> 0);
    }

    /**
     * 是否支持强制锁库存
     */
    protected boolean isForceUpdate(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> configCenter.getMetaDataConfig().getForceLockWhiteList().contains(oId),
                () -> false);
    }


    /**
     * 预处理上下文
     */
    protected void preProcessInventoryContext(InventoryContext ctx) {
        if (isSupportsMockInventory(ctx.getOrderId())) {
            ctx.setTodaySchedule(true);
            ctx.setForceUpdate(true);
        }

        if (isForceUpdate(ctx.getOrderId())) {
            ctx.setForceUpdate(true);
        }
    }
}
