package com.bilibili.brand.biz.component.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdCreativeComponentPo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 组件id
     */
    private Long componentId;

    /**
     * 组件类型，和组件表中的类型保持一致，防止后期可能会有关联多个组件表的场景而导致组件id冲突问题
     */
    private Integer componentType;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 软删除: 0-未删除,1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}