package com.bilibili.brand.biz.schedule.service;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.schedule.dto.BrandScheduleLiveDto;
import com.bilibili.brand.api.schedule.service.IBrandScheduleLiveService;
import com.bilibili.cpt.platform.biz.dao.BrandScheduleLiveDao;
import com.bilibili.cpt.platform.biz.po.BrandScheduleLivePo;
import com.bilibili.cpt.platform.biz.po.BrandScheduleLivePoExample;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/8 19:27
 */
@Slf4j
@Service
public class BrandScheduleLiveService implements IBrandScheduleLiveService {

    @Autowired
    private BrandScheduleLiveDao brandScheduleLiveDao;

    @Override
    public List<BrandScheduleLiveDto> getBrandScheduleLives(List<Integer> scheduleIdList) {
        if (CollectionUtils.isEmpty(scheduleIdList)) {
            return Lists.newLinkedList();
        }
        BrandScheduleLivePoExample example = new BrandScheduleLivePoExample();
        example.createCriteria()
                .andScheduleIdIn(scheduleIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return brandScheduleLiveDao.selectByExample(example)
                .stream()
                .map(po -> BrandScheduleLiveDto.builder()
                        .liveId(po.getLiveId())
                        .scheduleId(po.getScheduleId())
                        .build())
                .collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBrandScheduleLives(List<BrandScheduleLiveDto> scheduleLives) {
        if (CollectionUtils.isEmpty(scheduleLives)) {
            return;
        }

        List<Integer> scheduleIdList = scheduleLives.stream()
                .map(BrandScheduleLiveDto::getScheduleId)
                .distinct()
                .collect(Collectors.toList());
        deleteAllLiveBySchedule(scheduleIdList);

        List<BrandScheduleLivePo> brandScheduleLivePos = scheduleLives.stream()
                .map(dto -> BrandScheduleLivePo.builder()
                        .scheduleId(dto.getScheduleId())
                        .liveId(dto.getLiveId())
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(new Timestamp(System.currentTimeMillis()))
                        .mtime(new Timestamp(System.currentTimeMillis()))
                        .build())
                .collect(Collectors.toList());
        brandScheduleLiveDao.insertUpdateBatch(brandScheduleLivePos);
    }

    @Override
    public void saveBrandScheduleLives(List<Integer> scheduleIdList, List<Long> liveIdList) {
        if (CollectionUtils.isEmpty(scheduleIdList)) {
            return;
        }

        deleteAllLiveBySchedule(scheduleIdList);

        if (!CollectionUtils.isEmpty(liveIdList)) {
            List<BrandScheduleLivePo> brandScheduleLivePos = scheduleIdList.stream()
                    .map(scheduleId -> liveIdList.stream()
                            .map(liveId -> BrandScheduleLivePo.builder()
                                    .scheduleId(scheduleId)
                                    .liveId(liveId)
                                    .isDeleted(IsDeleted.VALID.getCode())
                                    .ctime(new Timestamp(System.currentTimeMillis()))
                                    .mtime(new Timestamp(System.currentTimeMillis()))
                                    .build())
                            .collect(Collectors.toList()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            brandScheduleLiveDao.insertUpdateBatch(brandScheduleLivePos);
        }
    }

    private void deleteAllLiveBySchedule(List<Integer> scheduleIdList) {
        BrandScheduleLivePoExample example = new BrandScheduleLivePoExample();
        example.createCriteria()
                .andScheduleIdIn(scheduleIdList);
        brandScheduleLiveDao.updateByExampleSelective(BrandScheduleLivePo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }
}
