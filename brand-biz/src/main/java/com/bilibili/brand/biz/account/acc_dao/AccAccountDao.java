package com.bilibili.brand.biz.account.acc_dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.account.pojo.AccAccountPo;
import com.bilibili.brand.biz.account.pojo.AccAccountPoExample;

public interface AccAccountDao {
    long countByExample(AccAccountPoExample example);

    int deleteByExample(AccAccountPoExample example);

    int deleteByPrimaryKey(Integer accountId);

    int insert(AccAccountPo record);

    int insertSelective(AccAccountPo record);

    List<AccAccountPo> selectByExample(AccAccountPoExample example);

    AccAccountPo selectByPrimaryKey(Integer accountId);

    int updateByExampleSelective(@Param("record") AccAccountPo record, @Param("example") AccAccountPoExample example);

    int updateByExample(@Param("record") AccAccountPo record, @Param("example") AccAccountPoExample example);

    int updateByPrimaryKeySelective(AccAccountPo record);

    int updateByPrimaryKey(AccAccountPo record);
}