package com.bilibili.brand.biz.log.bean;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.cpt.platform.biz.po.OgvBrandContentPo;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/09/05 20:48
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(gdLogFlag = GdLogFlag.OGV_BRAND_CONTENT)
public class OgvBrandContentLogBean implements Serializable {
    private static final long serialVersionUID = 1L;
    @LogProperty("稿件avid")
    private Long avid;

    @LogProperty("稿件cid")
    private Long cid;

    @LogProperty("广告开始时间")
    private String adStartTime;

    @LogProperty("广告结束时间")
    private String adEndTime;

    @LogProperty("品牌名称")
    private String brandName;

    public static OgvBrandContentLogBean fromPo(OgvBrandContentPo po) {
        OgvBrandContentLogBean logBean = new OgvBrandContentLogBean();
        BeanUtils.copyProperties(po, logBean);
        return logBean;
    }
}
