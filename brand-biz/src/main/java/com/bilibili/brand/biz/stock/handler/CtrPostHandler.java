package com.bilibili.brand.biz.stock.handler;

import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.http.invoker.HttpPostInvoker;
import com.bilibili.adp.http.invoker.ParameterizedTypeReference;
import com.bilibili.brand.biz.stock.CtrResponse;
import com.bilibili.brand.biz.stock.StockResponse;

import org.springframework.stereotype.Component;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/21.
 */
@Component
public class CtrPostHandler extends HttpPostInvoker<CtrResponse> {
    @Override
    public CtrResponse doPost(String url, Object o) throws SystemException {
        return super.doPost(url, o, new ParameterizedTypeReference<CtrResponse>() {
        });
    }
}
