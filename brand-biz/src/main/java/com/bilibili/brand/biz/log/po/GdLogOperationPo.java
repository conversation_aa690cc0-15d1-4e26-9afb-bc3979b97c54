package com.bilibili.brand.biz.log.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdLogOperationPo implements Serializable {
    private Long id;

    /**
     * 插入对象id
     */
    private Long objId;

    /**
     * 操作对象标识
     */
    private Integer objFlag;

    /**
     * 0 insert 1 delete 2 update 3 select
     */
    private Integer operateType;

    /**
     * 操作人ID或名字
     */
    private String operatorUsername;

    /**
     * 操作人IP地址
     */
    private String ip;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除:0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 操作结果
     */
    private String value;

    private static final long serialVersionUID = 1L;
}