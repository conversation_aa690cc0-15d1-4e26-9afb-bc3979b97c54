package com.bilibili.brand.biz.creative.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdCreativeHotManuscriptAuditPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * aid
     */
    private Long aid;

    /**
     * 审核状态 0-审核通过 -2-打回 -4锁定
     */
    private Integer state;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}