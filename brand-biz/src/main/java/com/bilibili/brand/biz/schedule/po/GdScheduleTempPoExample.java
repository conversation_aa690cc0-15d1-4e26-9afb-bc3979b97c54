package com.bilibili.brand.biz.schedule.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class GdScheduleTempPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GdScheduleTempPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionIsNull() {
            addCriterion("total_impression is null");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionIsNotNull() {
            addCriterion("total_impression is not null");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionEqualTo(Integer value) {
            addCriterion("total_impression =", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionNotEqualTo(Integer value) {
            addCriterion("total_impression <>", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionGreaterThan(Integer value) {
            addCriterion("total_impression >", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_impression >=", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionLessThan(Integer value) {
            addCriterion("total_impression <", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionLessThanOrEqualTo(Integer value) {
            addCriterion("total_impression <=", value, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionIn(List<Integer> values) {
            addCriterion("total_impression in", values, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionNotIn(List<Integer> values) {
            addCriterion("total_impression not in", values, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionBetween(Integer value1, Integer value2) {
            addCriterion("total_impression between", value1, value2, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andTotalImpressionNotBetween(Integer value1, Integer value2) {
            addCriterion("total_impression not between", value1, value2, "totalImpression");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNull() {
            addCriterion("external_price is null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNotNull() {
            addCriterion("external_price is not null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceEqualTo(Integer value) {
            addCriterion("external_price =", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotEqualTo(Integer value) {
            addCriterion("external_price <>", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThan(Integer value) {
            addCriterion("external_price >", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("external_price >=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThan(Integer value) {
            addCriterion("external_price <", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("external_price <=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIn(List<Integer> values) {
            addCriterion("external_price in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotIn(List<Integer> values) {
            addCriterion("external_price not in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("external_price between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("external_price not between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIsNull() {
            addCriterion("internal_price is null");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIsNotNull() {
            addCriterion("internal_price is not null");
            return (Criteria) this;
        }

        public Criteria andInternalPriceEqualTo(Integer value) {
            addCriterion("internal_price =", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotEqualTo(Integer value) {
            addCriterion("internal_price <>", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceGreaterThan(Integer value) {
            addCriterion("internal_price >", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("internal_price >=", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceLessThan(Integer value) {
            addCriterion("internal_price <", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("internal_price <=", value, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceIn(List<Integer> values) {
            addCriterion("internal_price in", values, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotIn(List<Integer> values) {
            addCriterion("internal_price not in", values, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("internal_price between", value1, value2, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andInternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("internal_price not between", value1, value2, "internalPrice");
            return (Criteria) this;
        }

        public Criteria andBeginDateIsNull() {
            addCriterion("begin_date is null");
            return (Criteria) this;
        }

        public Criteria andBeginDateIsNotNull() {
            addCriterion("begin_date is not null");
            return (Criteria) this;
        }

        public Criteria andBeginDateEqualTo(Timestamp value) {
            addCriterion("begin_date =", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateNotEqualTo(Timestamp value) {
            addCriterion("begin_date <>", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateGreaterThan(Timestamp value) {
            addCriterion("begin_date >", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("begin_date >=", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateLessThan(Timestamp value) {
            addCriterion("begin_date <", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("begin_date <=", value, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateIn(List<Timestamp> values) {
            addCriterion("begin_date in", values, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateNotIn(List<Timestamp> values) {
            addCriterion("begin_date not in", values, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_date between", value1, value2, "beginDate");
            return (Criteria) this;
        }

        public Criteria andBeginDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_date not between", value1, value2, "beginDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Timestamp value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Timestamp value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Timestamp value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Timestamp value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Timestamp> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Timestamp> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNull() {
            addCriterion("sales_type is null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIsNotNull() {
            addCriterion("sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andSalesTypeEqualTo(Integer value) {
            addCriterion("sales_type =", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotEqualTo(Integer value) {
            addCriterion("sales_type <>", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThan(Integer value) {
            addCriterion("sales_type >", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("sales_type >=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThan(Integer value) {
            addCriterion("sales_type <", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("sales_type <=", value, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeIn(List<Integer> values) {
            addCriterion("sales_type in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotIn(List<Integer> values) {
            addCriterion("sales_type not in", values, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("sales_type between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("sales_type not between", value1, value2, "salesType");
            return (Criteria) this;
        }

        public Criteria andDealSeqIsNull() {
            addCriterion("deal_seq is null");
            return (Criteria) this;
        }

        public Criteria andDealSeqIsNotNull() {
            addCriterion("deal_seq is not null");
            return (Criteria) this;
        }

        public Criteria andDealSeqEqualTo(Long value) {
            addCriterion("deal_seq =", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqNotEqualTo(Long value) {
            addCriterion("deal_seq <>", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqGreaterThan(Long value) {
            addCriterion("deal_seq >", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqGreaterThanOrEqualTo(Long value) {
            addCriterion("deal_seq >=", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqLessThan(Long value) {
            addCriterion("deal_seq <", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqLessThanOrEqualTo(Long value) {
            addCriterion("deal_seq <=", value, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqIn(List<Long> values) {
            addCriterion("deal_seq in", values, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqNotIn(List<Long> values) {
            addCriterion("deal_seq not in", values, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqBetween(Long value1, Long value2) {
            addCriterion("deal_seq between", value1, value2, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andDealSeqNotBetween(Long value1, Long value2) {
            addCriterion("deal_seq not between", value1, value2, "dealSeq");
            return (Criteria) this;
        }

        public Criteria andFailMsgIsNull() {
            addCriterion("fail_msg is null");
            return (Criteria) this;
        }

        public Criteria andFailMsgIsNotNull() {
            addCriterion("fail_msg is not null");
            return (Criteria) this;
        }

        public Criteria andFailMsgEqualTo(String value) {
            addCriterion("fail_msg =", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgNotEqualTo(String value) {
            addCriterion("fail_msg <>", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgGreaterThan(String value) {
            addCriterion("fail_msg >", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgGreaterThanOrEqualTo(String value) {
            addCriterion("fail_msg >=", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgLessThan(String value) {
            addCriterion("fail_msg <", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgLessThanOrEqualTo(String value) {
            addCriterion("fail_msg <=", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgLike(String value) {
            addCriterion("fail_msg like", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgNotLike(String value) {
            addCriterion("fail_msg not like", value, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgIn(List<String> values) {
            addCriterion("fail_msg in", values, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgNotIn(List<String> values) {
            addCriterion("fail_msg not in", values, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgBetween(String value1, String value2) {
            addCriterion("fail_msg between", value1, value2, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFailMsgNotBetween(String value1, String value2) {
            addCriterion("fail_msg not between", value1, value2, "failMsg");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdIsNull() {
            addCriterion("fly_unit_id is null");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdIsNotNull() {
            addCriterion("fly_unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdEqualTo(Integer value) {
            addCriterion("fly_unit_id =", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdNotEqualTo(Integer value) {
            addCriterion("fly_unit_id <>", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdGreaterThan(Integer value) {
            addCriterion("fly_unit_id >", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("fly_unit_id >=", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdLessThan(Integer value) {
            addCriterion("fly_unit_id <", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdLessThanOrEqualTo(Integer value) {
            addCriterion("fly_unit_id <=", value, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdIn(List<Integer> values) {
            addCriterion("fly_unit_id in", values, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdNotIn(List<Integer> values) {
            addCriterion("fly_unit_id not in", values, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdBetween(Integer value1, Integer value2) {
            addCriterion("fly_unit_id between", value1, value2, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andFlyUnitIdNotBetween(Integer value1, Integer value2) {
            addCriterion("fly_unit_id not between", value1, value2, "flyUnitId");
            return (Criteria) this;
        }

        public Criteria andRotationNumIsNull() {
            addCriterion("rotation_num is null");
            return (Criteria) this;
        }

        public Criteria andRotationNumIsNotNull() {
            addCriterion("rotation_num is not null");
            return (Criteria) this;
        }

        public Criteria andRotationNumEqualTo(Integer value) {
            addCriterion("rotation_num =", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumNotEqualTo(Integer value) {
            addCriterion("rotation_num <>", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumGreaterThan(Integer value) {
            addCriterion("rotation_num >", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("rotation_num >=", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumLessThan(Integer value) {
            addCriterion("rotation_num <", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumLessThanOrEqualTo(Integer value) {
            addCriterion("rotation_num <=", value, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumIn(List<Integer> values) {
            addCriterion("rotation_num in", values, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumNotIn(List<Integer> values) {
            addCriterion("rotation_num not in", values, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumBetween(Integer value1, Integer value2) {
            addCriterion("rotation_num between", value1, value2, "rotationNum");
            return (Criteria) this;
        }

        public Criteria andRotationNumNotBetween(Integer value1, Integer value2) {
            addCriterion("rotation_num not between", value1, value2, "rotationNum");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}