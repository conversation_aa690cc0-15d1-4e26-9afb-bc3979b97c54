package com.bilibili.brand.biz.deduction.dao;

import com.bilibili.brand.biz.deduction.po.GdFeeDeductionInfoPo;
import com.bilibili.brand.biz.deduction.po.GdFeeDeductionInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface GdFeeDeductionInfoDao {
    long countByExample(GdFeeDeductionInfoPoExample example);

    int deleteByExample(GdFeeDeductionInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(GdFeeDeductionInfoPo record);

    int insertBatch(List<GdFeeDeductionInfoPo> records);

    int insertUpdateBatch(List<GdFeeDeductionInfoPo> records);

    int insert(GdFeeDeductionInfoPo record);

    int insertUpdateSelective(GdFeeDeductionInfoPo record);

    int insertSelective(GdFeeDeductionInfoPo record);

    List<GdFeeDeductionInfoPo> selectByExample(GdFeeDeductionInfoPoExample example);

    GdFeeDeductionInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") GdFeeDeductionInfoPo record, @Param("example") GdFeeDeductionInfoPoExample example);

    int updateByExample(@Param("record") GdFeeDeductionInfoPo record, @Param("example") GdFeeDeductionInfoPoExample example);

    int updateByPrimaryKeySelective(GdFeeDeductionInfoPo record);

    int updateByPrimaryKey(GdFeeDeductionInfoPo record);
}