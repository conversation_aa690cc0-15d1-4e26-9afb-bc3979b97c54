package com.bilibili.brand.biz.creative.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.bean.IdName;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.creative.dto.CreativeExtDto;
import com.bilibili.brand.api.creative.dto.CreativeExtHolderDto;
import com.bilibili.brand.biz.converter.BrandCreativeConverter;
import com.bilibili.brand.biz.creative.dao.GdCreativeExtDao;
import com.bilibili.brand.biz.creative.po.GdCreativeExtPo;
import com.bilibili.brand.biz.creative.po.GdCreativeExtPoExample;
import com.bilibili.brand.biz.rpc.grpc.client.BDataServiceGrpcClient;
import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:37
 */
@Slf4j
@Service
public class CreativeExtService {
    @Autowired
    private GdCreativeExtDao creativeExtDao;
    @Autowired
    private BDataServiceGrpcClient bDataServiceGrpcClient;

    /**
     * 根据创意id查询对应拓展信息
     * for simple case
     */
    public CreativeExtDto getCreativeExt(Integer orderProduct, Long creativeId) {
        OrderProduct.getByCode(orderProduct);
        Assert.notNull(creativeId, "creative id must not be null");
        return BrandCreativeConverter.MAPPER.toCreativeExtDto(this.getCreativeExtPo(orderProduct, creativeId));
    }

    /**
     * 根据创意id查询对应拓展信息
     * for complex case
     */
    public CreativeExtHolderDto getCreativeExtHolder(Integer orderProduct, Long creativeId) {
        Map<Long, CreativeExtHolderDto> creativeExtHolder = this.getCreativeExtHolder(orderProduct,
                Lists.newArrayList(creativeId));
        CreativeExtHolderDto ext = creativeExtHolder.get(creativeId);
        return Objects.isNull(ext) ? CreativeExtHolderDto.builder().build() : ext;
    }

    /**
     * 根据创意id查询对应拓展信息
     * for complex case
     */
    public Map<Long, CreativeExtHolderDto> getCreativeExtHolder(Integer orderProduct, List<Long> creativeIdList) {
        OrderProduct.getByCode(orderProduct);
        Assert.notEmpty(creativeIdList, "creative id must not be empty");

        Map<Long, CreativeExtHolderDto> result = Maps.newHashMap();
        GdCreativeExtPoExample example = new GdCreativeExtPoExample();
        example.createCriteria()
                .andOrderProductEqualTo(orderProduct)
                .andCreativeIdIn(creativeIdList);
        List<GdCreativeExtPo> exts = this.creativeExtDao.selectByExample(example);
        if (CollectionUtils.isEmpty(exts)) {
            return result;
        }

        //1、产品型号
        List<Long> firstProductLabelIdList = exts.stream()
                .map(GdCreativeExtPo::getFirstProductLabelId)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, String> firstLabelMap = Maps.newHashMap();
        Map<Long, Map<Long, String>> secondLabelMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(firstProductLabelIdList)) {
            List<IdName> labels = this.bDataServiceGrpcClient.queryProductLabelList(firstProductLabelIdList);
            firstLabelMap = labels.stream().collect(Collectors.toMap(IdName::getId, IdName::getName, OptionalUtil.override()));
            secondLabelMap = labels.stream().collect(Collectors.toMap(IdName::getId,
                    first -> first.getChild().stream()
                            .collect(Collectors.toMap(IdName::getId, IdName::getName, OptionalUtil.override())),
                    OptionalUtil.override()));
        }

        for (GdCreativeExtPo ext : exts) {
            CreativeExtHolderDto.CreativeExtHolderDtoBuilder builder = CreativeExtHolderDto.builder()
                    .creativeId(ext.getCreativeId())
                    .orderProduct(ext.getOrderProduct());
            //1、产品型号
            if (Utils.isPositive(ext.getFirstProductLabelId())) {
                ProductLabelDto.ProductLabelDtoBuilder productLabelBuilder = ProductLabelDto.builder()
                        .firstProductLabelId(ext.getFirstProductLabelId())
                        .secondProductLabelId(ext.getSecondProductLabelId());
                String firstProductLabelName = firstLabelMap.get(ext.getFirstProductLabelId());
                Map<Long, String> slMap = secondLabelMap.get(ext.getFirstProductLabelId());
                String secondProductLabelName = Objects.nonNull(slMap) && slMap.containsKey(ext.getSecondProductLabelId()) ?
                        slMap.get(ext.getSecondProductLabelId()) : null;
                productLabelBuilder.firstProductLabelName(Objects.isNull(firstProductLabelName) ? "" : firstProductLabelName);
                productLabelBuilder.secondProductLabelName(Objects.isNull(secondProductLabelName) ? "" : secondProductLabelName);
                builder.productLabel(productLabelBuilder.build());
            }
            builder.interactStyle(ext.getInteractStyle());

            result.put(ext.getCreativeId(), builder.build());
        }
        return result;
    }

    /**
     * 保存创意拓展信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveCreativeExt(CreativeExtHolderDto ext) {
        if (Objects.isNull(ext)) {
            return;
        }
        OrderProduct.getByCode(ext.getOrderProduct());
        Assert.notNull(ext.getCreativeId(), "creative id must not be null");
//        Assert.notNull(ext.getOrderId(), "order id must not be null");
//        Assert.notNull(ext.getScheduleId(), "schedule id must not be null");
        GdCreativeExtPo.GdCreativeExtPoBuilder builder = GdCreativeExtPo.builder()
                .creativeId(ext.getCreativeId())
                .orderProduct(ext.getOrderProduct())
                .isDeleted(IsDeleted.VALID.getCode());
        //产品型号
        if (Objects.nonNull(ext.getProductLabel())) {
            ProductLabelDto productLabel = ext.getProductLabel();
            builder.firstProductLabelId(productLabel.getFirstProductLabelId());
            builder.secondProductLabelId(productLabel.getSecondProductLabelId());
        }

        //其他
        builder.interactStyle(ext.getInteractStyle());

        GdCreativeExtPo po = this.getCreativeExtPo(ext.getOrderProduct(), ext.getCreativeId());
        if (Objects.isNull(po)) {
            this.creativeExtDao.insertSelective(builder.build());
        } else {
            builder.id(po.getId());
            this.creativeExtDao.updateByPrimaryKeySelective(builder.build());
        }
    }

    private GdCreativeExtPo getCreativeExtPo(Integer orderProduct, Long creativeId) {
        GdCreativeExtPoExample example = new GdCreativeExtPoExample();
        example.createCriteria()
                .andOrderProductEqualTo(orderProduct)
                .andCreativeIdEqualTo(creativeId);
        List<GdCreativeExtPo> exts = this.creativeExtDao.selectByExample(example);
        return CollectionUtils.isEmpty(exts) ? null : exts.get(0);
    }

    /**
     * 删除拓展表信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCreativeExt(Integer orderProduct, Long creativeId) {
        OrderProduct.getByCode(orderProduct);
        Assert.notNull(creativeId, "creative id must not be null");
        GdCreativeExtPoExample example = new GdCreativeExtPoExample();
        example.createCriteria()
                .andOrderProductEqualTo(orderProduct)
                .andCreativeIdEqualTo(creativeId);
        this.creativeExtDao.updateByExampleSelective(GdCreativeExtPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }
}
