package com.bilibili.brand.biz.schedule.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OriginTag;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.AdxOrderType;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.order.dto.AdxOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IAdxOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Service
public class SsaPdbScheduleService {

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private GdOrderService gdOrderService;
    @Autowired
    private IAdxOrderService adxOrderService;
    @Autowired
    private ISoaCrmContractService crmContractService;

    @Autowired
    private ISystemConfigService systemConfigService;

    public void buildDealIdIfNecessary(GdOrderDto order, SsaPlusScheduleBo schedule) {
        if (isSSaPdb(order)) {
            schedule.setDealId(snowflakeIdWorker.nextId());
        }
    }

    public boolean isSSaPdb(GdOrderDto order) {
        return OrderProduct.SSA_GD_PLUS.getCode().equals(order.getProduct())
                && OriginTag.ADX.getCode().equals(order.getOriginTag());
    }

    public void buildDealGroupIdIfNecessary(SsaPlusScheduleBo schedule) {
        GdOrderDto order = gdOrderService.getOrderById(schedule.getOrderId());
        buildDealGroupIdIfNecessary(order, schedule);
    }

    public void buildDealGroupIdIfNecessary(GdOrderDto order, SsaPlusScheduleBo schedule) {

        if (isSSaPdb(order)) {
            schedule.setDealGroupId(getSnowFlakeId(schedule.getDealGroupId()));
        }
    }

    public void createAdxOrderIfNecessary(GdOrderDto order, SsaPlusScheduleBo schedule, Operator operator) throws ServiceException {

        if (isSSaPdb(order)) {
            ContractDto contractDto = crmContractService.getContractById(order.getCrmContractId());
            Assert.notNull(contractDto, "合同不存在");

            AdxOrderDto adxOrderDto = buildSsaPdbAdxOrder(order, schedule, contractDto);

            adxOrderService.create(adxOrderDto, operator);
        }
    }

    public void deletedAdxSsaPdbOrderIfNecessary(GdOrderDto order, Integer scheduleId) {
        if (isSSaPdb(order)) {
            adxOrderService.deletePdb(scheduleId);
        }
    }

    public void migrateAdxOrderIfNecessary(Integer scheduleId, GdOrderDto targetOrder, Operator operator) {
        if (isSSaPdb(targetOrder)) {
            adxOrderService.migrateOrderByMappingId(scheduleId.longValue(), targetOrder, operator);
        }
    }


    private AdxOrderDto buildSsaPdbAdxOrder(GdOrderDto order, SsaPlusScheduleBo schedule, ContractDto contract) {

        AdxOrderDto adxOrderDto = new AdxOrderDto();
        adxOrderDto.setSsaShowStyle(schedule.getShowStyle());
        adxOrderDto.setBidderId(order.getBidderId());
        adxOrderDto.setContractId(contract.getId());
        adxOrderDto.setContractNumber(contract.getContractNumber());
        adxOrderDto.setCrmOrderId(0);
        adxOrderDto.setOrderName(order.getOrderName());
        adxOrderDto.setOrderType(AdxOrderType.SSA_PDB.getCode());
        adxOrderDto.setDayDeliverImpressionLimit(0L);
        adxOrderDto.setMappingOrderId(order.getOrderId());
        adxOrderDto.setResourceType(order.getResourceType());
        adxOrderDto.setMappingId(Long.valueOf(schedule.getScheduleId()));
        adxOrderDto.setDealId(schedule.getDealId());
        adxOrderDto.setDealGroupId(schedule.getDealGroupId());
        adxOrderDto.setBidderSourceId(schedule.getBidderSourceId());
        adxOrderDto.setBeginTime(schedule.getBeginTime());
        adxOrderDto.setEndTime(schedule.getEndTime());

        Integer dayShowLimitRatio = systemConfigService.getValueReturnInt(SystemConfigEnum
                .SSA_PDB_DAY_SHOW_LIMIT_RATIO.getCode());
        adxOrderDto.setDayDeliverImpressionLimit(schedule.getTotalImpression() * 1000L  * dayShowLimitRatio /
                Utils.getDateSpace(schedule.getBeginTime(), schedule.getEndTime()));
        return adxOrderDto;
    }

    private long getSnowFlakeId(Long dealId) {
        if (!Utils.isPositive(dealId)) {
            dealId = snowflakeIdWorker.nextId();
        }
        return dealId;
    }
}
