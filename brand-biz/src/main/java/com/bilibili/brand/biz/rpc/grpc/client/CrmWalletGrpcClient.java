package com.bilibili.brand.biz.rpc.grpc.client;

import com.alibaba.fastjson2.JSONObject;
import com.bapis.ad.crm.wallet.CrmWalletBaseReply;
import com.bapis.ad.crm.wallet.CrmWalletReq;
import com.bapis.ad.crm.wallet.CrmWalletServiceGrpc;
import com.bapis.ad.crm.wallet.soa.wallet.ISoaAccountWalletServiceGrpc;
import com.bapis.ad.crm.wallet.soa.wallet.SoaAccountWalletServiceCommonResp;
import com.bapis.ad.crm.wallet.soa.wallet.SoaAccountWalletTradingDto;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.account.dto.WalletDto;
import com.bilibili.crm.platform.common.SystemType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CrmWalletGrpcClient {

    @RPCClient("sycpb.platform.crm-wallet")
    private CrmWalletServiceGrpc.CrmWalletServiceBlockingStub crmWalletServiceBlockingStub;

    @RPCClient("sycpb.platform.crm-wallet")
    private ISoaAccountWalletServiceGrpc.ISoaAccountWalletServiceBlockingStub iSoaAccountWalletServiceBlockingStub;

    public List<WalletDto> queryWalletByAccIds(List<Integer> accountIdList) {
        CrmWalletReq crmWalletReq = CrmWalletReq.newBuilder()
                .addAllAccountIds(accountIdList)
                .build();
        CrmWalletBaseReply crmWalletBaseReply = crmWalletServiceBlockingStub.queryWalletByAccIds(crmWalletReq);
        log.info("CrmWalletGrpcClient.queryWalletByAccIds，crmWalletReq:{}，crmWalletBaseReply:{}",
                JSONObject.toJSONString(crmWalletReq), JSONObject.toJSONString(crmWalletBaseReply));
        if (Objects.isNull(crmWalletBaseReply)
                || CollectionUtils.isEmpty(crmWalletBaseReply.getDataList())) {
            return new ArrayList<>();
        }
        return crmWalletBaseReply.getDataList()
                .stream()
                .map(item ->
                        WalletDto.builder()
                                .accountId(item.getAccountId())
                                .cash(Utils.fromFenToYuan(item.getCash()))
                                .redPacket(Utils.fromFenToYuan(item.getRedPacket()))
                                .totalCashConsume(Utils.fromFenToYuan(item.getTotalCashConsume()))
                                .totalCashRecharge(Utils.fromFenToYuan(item.getTotalCashRecharge()))
                                .totalRedPacketConsume(Utils.fromFenToYuan(item.getTotalRedPacketConsume()))
                                .totalRedPacketRecharge(Utils.fromFenToYuan(item.getTotalRedPacketRecharge()))
                                .build()
                ).collect(Collectors.toList());
    }

    public String deduct(Integer accountId, String businessId, BigDecimal amount) {
        SoaAccountWalletTradingDto soaAccountWalletTradingDto = SoaAccountWalletTradingDto.newBuilder()
                .setAccountId(accountId)
                .setSystemType(SystemType.BRAND.getCode())
                .setBusinessId(businessId)
                .setAmount(amount.doubleValue())
                .build();
        SoaAccountWalletServiceCommonResp soaAccountWalletServiceCommonResp = iSoaAccountWalletServiceBlockingStub.deduct(soaAccountWalletTradingDto);
        log.info("CrmWalletGrpcClient.deduct，soaAccountWalletTradingDto：{}，soaAccountWalletServiceCommonResp：{}",
                JSONObject.toJSONString(soaAccountWalletTradingDto), JSONObject.toJSONString(soaAccountWalletServiceCommonResp));
        if (Objects.isNull(soaAccountWalletServiceCommonResp)) {
            return null;
        }
        return soaAccountWalletServiceCommonResp.getData();
    }
}
