package com.bilibili.brand.biz.utils;

import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.enums.LaunchButtonTypeEnum;
import com.bilibili.adp.common.enums.PromotionPurposeType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.ScheduleStatus;
import com.bilibili.brand.api.creative.dto.GdCreativeDateDto;
import com.bilibili.brand.api.schedule.dto.GdScheduleDateDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.cpt.platform.common.ButtonCopyTypeEnum;
import com.bilibili.cpt.platform.common.LaunchStatus;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.common.util.Utils.HUNDRED;

/**
 * <AUTHOR>
 * @date 2016年9月28日
 */
public class LaunchUtil {

    /**
     * Calendar to string
     */
    public static String date2String(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        return formatter.format(date);
    }

    public static String dateTime2String(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(date);
    }

    public static String getHour() {
        Calendar now;
        SimpleDateFormat fmt;
        now = Calendar.getInstance();
        fmt = new SimpleDateFormat("H");
        return fmt.format(now.getTime());
    }


    public static String getTimestamp2String(Timestamp timestamp) throws ParseException {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        return sim.format(timestamp);
    }

    public static List<Timestamp> buildUnitDates(List<String> sourceList) throws ParseException {
        SimpleDateFormat sim = new SimpleDateFormat("yyyy-MM-dd");
        List<Timestamp> timestamps = new ArrayList<>(sourceList.size());
        for (String s : sourceList) {
            timestamps.add(new Timestamp(sim.parse(s).getTime()));
        }
        return timestamps;
    }

    public static List<LocalTime> getLocalTimeList(List<String> hourRange) {
        if (CollectionUtils.isEmpty(hourRange)) {
            return Collections.emptyList();
        }
        List<String> hourList = hourRange.stream().filter(d ->
                !org.apache.commons.lang3.StringUtils.isEmpty(d)).collect(Collectors.toList());
        List<LocalTime> localTimeList = new ArrayList<>();
        DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
        for (String s : hourList) {
            localTimeList.add(LocalTime.parse(s, hourFormatter));
        }
        return localTimeList;
    }

    //此方法只用于pd,前端必须要确保每一天的时段都是相同的
    public static String buildLaunchTime(Timestamp beginTime, Timestamp endTime) {
        LocalDateTime begin = beginTime.toLocalDateTime();
        LocalDateTime end = endTime.toLocalDateTime();
        List<List<String>> launchTimeList = new ArrayList<>();
        for(int j = 0; j<7; j++) {
            List<String> timeList = new ArrayList<>();
            for (int i = begin.getHour(); i < end.getHour() + 1; i++) {
                timeList.add(String.valueOf(i));
            }
            launchTimeList.add(timeList);
        }

        StringBuilder sb = new StringBuilder(7 * 24);
        for (List<String> hourList : launchTimeList) {
            Set<Integer> hourSet = hourList.stream().map(Integer::parseInt).collect(Collectors.toSet());
            for (int i = 0; i < 24; i++) {
                if (hourSet.contains(i)) {
                    sb.append(1);
                } else {
                    sb.append(0);
                }
            }
        }

        return sb.toString();
    }

    public static LocalDate buildUnitDate(String source) {
        if (StringUtils.isEmpty(source)) {
            return null;
        }
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return LocalDate.parse(source, fmt);
    }

    public static String bulidShowDateFromDateList(List<String> sourceList) throws ParseException {
        if (CollectionUtils.isEmpty(sourceList)) {
            return "";
        }
        if (sourceList.size() == 1) {
            return sourceList.get(0);
        } else {
            Collections.sort(sourceList);
            Set<String> allList = getEveryDay(sourceList.get(0), sourceList.get(sourceList.size() - 1), "yyyy-MM-dd");
            List<List<String>> satrtEndList = getStartEndList(Lists.newArrayList(allList), sourceList);
            StringBuilder sb = new StringBuilder();

            String startDate = "";
            String endDate = "";
            for (List<String> dateList : satrtEndList) {
                startDate = dateList.get(0);
                endDate = dateList.get(1);

                if (startDate.equals(endDate)) {
                    sb.append(startDate);
                } else {
                    sb.append(startDate).append("~").append(endDate);
                }

                sb.append(",");
            }

            sb.deleteCharAt(sb.lastIndexOf(","));
            return sb.toString();
        }
    }

    public static List<List<String>> getStartEndList(List<String> allList, List<String> sourceList) {
        List<String> temp = Lists.newArrayList();
        List<List<String>> targetList = Lists.newArrayList();
        String val = "";
        for (int i = 0; i < allList.size(); i++) {
            val = allList.get(i);
            if (sourceList.contains(val)) {
                if (temp.size() == 0) {
                    temp.add(val);
                }
                if (i == allList.size() - 1) {
                    temp.add(val);
                    targetList.add(temp);
                }
            } else {
                if (!CollectionUtils.isEmpty(temp)) {
                    temp.add(allList.get(i - 1));
                    targetList.add(temp);
                }
                if (temp != null) {
                    temp = Lists.newArrayList();
                }
            }
        }
        return targetList;
    }


    public static Set<String> getEveryDay(String startDate, String endDate, String dateFormat) {
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ofPattern(dateFormat));
        LocalDate end = LocalDate.parse(endDate, DateTimeFormatter.ofPattern(dateFormat));
        Period period = Period.between(start, end);
        int days = period.getDays();
        Set<String> dateSet = Collections.emptySet();
        if (days >= 0) {
            dateSet = Sets.newLinkedHashSetWithExpectedSize(days + 1);
            for (int i = 0; i < days + 1; i++) {
                LocalDate date = start.plusDays(i);
                dateSet.add(date.format(DateTimeFormatter.ofPattern(dateFormat)));
            }
        } else {
            dateSet = Sets.newLinkedHashSetWithExpectedSize(1 - days);
            for (int i = 0; i < 1 - days; i++) {
                LocalDate date = start.minusDays(i);//.plusDays(i);
                dateSet.add(date.format(DateTimeFormatter.ofPattern(dateFormat)));
            }
        }
        return dateSet;
    }

    public static String buildShowDate(List<GdCreativeDateDto> dateDtos) {
        if (CollectionUtils.isEmpty(dateDtos)) {
            return "";
        }
        return dateDtos.stream().map(dto -> Utils.getTimestamp2String(dto.getBeginTime(), "yyyy-MM-dd HH:mm:ss") + "~"
                + Utils.getTimestamp2String(dto.getEndTime(), "yyyy-MM-dd HH:mm:ss")).collect(Collectors.joining(","));
    }


    public static boolean scheduleIsStart(List<GdScheduleDateDto> gdScheduleDateDtos) {
        if (CollectionUtils.isEmpty(gdScheduleDateDtos)) {
            return false;
        }
        List<Timestamp> timestamps = gdScheduleDateDtos.stream().map(GdScheduleDateDto::getScheduleDate).collect(Collectors.toList());
        return Collections.min(timestamps).getTime() <= Utils.getToday().getTime();
    }

    public static boolean isScheduleDeletable(List<Timestamp> timestamps) {
        if (!CollectionUtils.isEmpty(timestamps)) {
            for (Timestamp timestamp : timestamps) {
                if (!timestamp.after(Utils.getToday())) {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean scheduleIsStart(ScheduleDto schedule) {
        if (!schedule.getIsTodaySchedule().equals(IsValid.TRUE.getCode())) {
            return scheduleIsStart(schedule.getScheduleDates());
        }

        if (CollectionUtils.isEmpty(schedule.getScheduleDates())) {
            return false;
        }

        Timestamp date = Utils.getBeginOfDay(schedule.getScheduleDates().get(0).getScheduleDate());
        Timestamp today = Utils.getToday();

        if (date.after(today)) {
            return false;
        }

        if (date.before(today)) {
            return true;
        }

        int curHour = Utils.getHour(Utils.getNow());

        return schedule.getHour() <= curHour;
    }

    public static boolean isScheduleDeletable(ScheduleDto schedule) {
        if (ScheduleStatus.DELETED.getCode() == schedule.getScheduleStatus()) {
            return false;
        }
        return !scheduleIsStart(schedule);
    }


    public static Integer converToScheduleStatus(LaunchStatus status) {
        switch (status) {
            case START:
                return ScheduleStatus.VALID.getCode();
            case STOP:
                return ScheduleStatus.PAUSED.getCode();
            case DELETE:
                return ScheduleStatus.DELETED.getCode();
            default:
                return null;
        }
    }

    public static Timestamp getMonthLastDayTimestamp(int month) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.MONTH, month - 1);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.HOUR, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return new Timestamp(c.getTimeInMillis());
    }

    public static Timestamp getMonthFirstDayTimestamp(int month) {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.MONTH, month - 1);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return new Timestamp(c.getTimeInMillis());
    }

    public static LaunchButtonTypeEnum getButtonType(PromotionPurposeType promotionPurposeType,
                                                     ButtonCopyTypeEnum buttonCopyTypeEnum, String jumpUrl) {
        if (PromotionPurposeType.LANDING_PAGE.equals(promotionPurposeType)) {
            if (Strings.isNullOrEmpty(jumpUrl)
                    || jumpUrl.startsWith(Constants.BILIBILI_SCHEME)) {
                //落地页
                return LaunchButtonTypeEnum.LANDING_PAGE;
            } else {
                //APP唤醒
                return LaunchButtonTypeEnum.APP_AWAKEN;
            }
        } else if (PromotionPurposeType.APP_DOWNLOAD.equals(promotionPurposeType)) {
            if (ButtonCopyTypeEnum.JUMP_LINK.equals(buttonCopyTypeEnum)) {
                if (Strings.isNullOrEmpty(jumpUrl)
                        || jumpUrl.startsWith(Constants.BILIBILI_SCHEME)) {
                    //落地页
                    return LaunchButtonTypeEnum.LANDING_PAGE;
                } else {
                    //APP唤醒
                    return LaunchButtonTypeEnum.APP_AWAKEN;
                }
            } else {
                //APP下载
                return LaunchButtonTypeEnum.APP_DOWNLOAD;
            }
        }

        return LaunchButtonTypeEnum.LANDING_PAGE;
    }

    public static BigDecimal getClickRate(Integer clickCount, Integer showCount) {
        if (showCount == null || 0 == showCount) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, RoundingMode.HALF_UP).multiply(HUNDRED);
    }

    public static BigDecimal getClickRate(Integer clickCount, Long showCount) {
        if (showCount == null || 0 == showCount) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, RoundingMode.HALF_UP).multiply(HUNDRED);
    }
}
