package com.bilibili.brand.biz.rpc.grpc.server;

import com.bapis.ad.brand.Pageable;
import com.bapis.ad.brand.Responser;
import com.bapis.ad.brand.resource.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.brand.api.comment.ICommentService;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigDto;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigItemDto;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigQueryDto;
import com.bilibili.brand.api.resource.bluekeyword.IBlueKeywordService;
import com.bilibili.brand.api.resource.crowd.BrandCrowdPackageDto;
import com.bilibili.brand.api.resource.crowd.ICrowdPackageService;
import com.bilibili.brand.api.resource.ogv.IResOgvService;
import com.bilibili.brand.api.resource.ogv.OgvEpisodeDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceStickerDto;
import com.bilibili.brand.biz.converter.GrpcCommonConverter;
import com.bilibili.brand.biz.converter.OgvResourceConverter;
import com.bilibili.brand.biz.resource.converter.BlueKeywordConverter;
import com.bilibili.brand.biz.resource.converter.BusinessAtmosphereConverter;
import com.bilibili.brand.biz.resource.converter.CrowdPackageConverter;
import com.bilibili.brand.biz.resource.dto.BusinessAtmosphereDto;
import com.bilibili.brand.biz.resource.service.BusinessAtmosphereService;
import com.bilibili.enums.BlueKeywordStatusEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.util.JsonFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import pleiades.venus.starter.rpc.server.RPCService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/5 11:50
 */
@Slf4j
@RPCService
public class BrandResourceGrpcServiceImpl extends BrandResourceServiceGrpc.BrandResourceServiceImplBase {
    @Autowired
    private IResOgvService resOgvService;
    @Autowired
    private BusinessAtmosphereService businessAtmosphereService;
    @Autowired
    private ICrowdPackageService crowdPackageService;
    @Autowired
    private IBlueKeywordService blueKeywordService;
    @Autowired
    private ICommentService commentService;

    @Override
    public void queryCrowdPackages(QueryCrowdPackagesReq request, StreamObserver<QueryCrowdPackagesRes> responseObserver) {
        QueryCrowdPackagesRes.Builder resBuilder = QueryCrowdPackagesRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            Page<BrandCrowdPackageDto> crowdPackageDtoPage = crowdPackageService.queryCrowdPackages(request.getName(),
                    request.getStartTime(),
                    request.getEndTime(),
                    null,
                    request.getPageable().getPageIndex(),
                    request.getPageable().getPageSize());

            resBuilder.setPageable(Pageable.newBuilder()
                            .setTotalCount(crowdPackageDtoPage.getTotal())
                            .build())
                    .addAllCrowdPackage(CrowdPackageConverter.MAPPER.toList(crowdPackageDtoPage.getRows()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] queryCrowdPackages error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] queryCrowdPackages error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void createCrowdPackage(CreateCrowdPackageReq request, StreamObserver<CreateCrowdPackageRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        CreateCrowdPackageRes.Builder resBuilder = CreateCrowdPackageRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            crowdPackageService.createCrowdPackage(request.getPackageId(),
                    request.getStartTime(),
                    request.getEndTime(),
                    request.getScene(),
                    operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] createCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] createCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void updateCrowdPackage(UpdateCrowdPackageReq request, StreamObserver<UpdateCrowdPackageRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        UpdateCrowdPackageRes.Builder resBuilder = UpdateCrowdPackageRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            crowdPackageService.updateCrowdPackage(request.getId(),
                    request.getStartTime(),
                    request.getEndTime(),
                    operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] updateCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] updateCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void deleteCrowdPackage(DeleteCrowdPackageReq request, StreamObserver<DeleteCrowdPackageRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        DeleteCrowdPackageRes.Builder resBuilder = DeleteCrowdPackageRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            crowdPackageService.deleteCrowdPackage(request.getId(), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] createCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] createCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void batchCreateCrowdPackage(BatchCreateCrowdPackageReq request, StreamObserver<CreateCrowdPackageRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        CreateCrowdPackageRes.Builder resBuilder = CreateCrowdPackageRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            crowdPackageService.batchCreateCrowdPackage(request.getPackageIdList(),
                    request.getStartTime(),
                    request.getEndTime(),
                    request.getSceneList(),
                    operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] batchCreateCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] batchCreateCrowdPackage error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void batchQueryCrowdPackages(BatchQueryCrowdPackagesReq request, StreamObserver<QueryCrowdPackagesRes> responseObserver) {
        QueryCrowdPackagesRes.Builder resBuilder = QueryCrowdPackagesRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            Page<BrandCrowdPackageDto> crowdPackageDtoPage = crowdPackageService.queryCrowdPackages(request.getName(),
                    request.getPackageIdList(),
                    request.getStartTime(),
                    request.getEndTime(),
                    null,
                    request.getPageable().getPageIndex(),
                    request.getPageable().getPageSize());

            resBuilder.setPageable(Pageable.newBuilder()
                            .setTotalCount(crowdPackageDtoPage.getTotal())
                            .build())
                    .addAllCrowdPackage(CrowdPackageConverter.MAPPER.toList(crowdPackageDtoPage.getRows()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] batchQueryCrowdPackages error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] batchQueryCrowdPackages error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void createOgvResource(CreateOgvResourceReq request, StreamObserver<CreateOgvResourceRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        CreateOgvResourceRes.Builder resBuilder = CreateOgvResourceRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            List<Long> idList = resOgvService.createOgvResource(OgvResourceConverter.MAPPER.toOgvResourceSaveDto(request), operator);
            resBuilder.addAllId(idList);
        } catch (IllegalArgumentException | ServiceException e) {
            log.error("[BrandResourceGrpcServiceImpl] createOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] createOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void updateOgvResource(UpdateOgvResourceReq request, StreamObserver<UpdateOgvResourceRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        UpdateOgvResourceRes.Builder resBuilder = UpdateOgvResourceRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            resOgvService.updateOgvResource(OgvResourceConverter.MAPPER.toOgvResourceSaveDto(request), operator);
        } catch (IllegalArgumentException | ServiceException e) {
            log.error("[BrandResourceGrpcServiceImpl] updateOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] updateOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void onlineOgvResource(OnlineOgvResourceReq request, StreamObserver<OnlineOgvResourceRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        OnlineOgvResourceRes.Builder resBuilder = OnlineOgvResourceRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            resOgvService.onlineOgvResource(request.getIdList(), operator);
        } catch (IllegalArgumentException | ServiceException e) {
            log.error("[BrandResourceGrpcServiceImpl] onlineOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] onlineOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void offlineOgvResource(OfflineOgvResourceReq request, StreamObserver<OfflineOgvResourceRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        OfflineOgvResourceRes.Builder resBuilder = OfflineOgvResourceRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            resOgvService.offlineOgvResource(request.getIdList(), operator);
        } catch (IllegalArgumentException | ServiceException e) {
            log.error("[BrandResourceGrpcServiceImpl] offlineOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] offlineOgvResource error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getOgvResourceDetail(GetOgvResourceDetailReq request, StreamObserver<GetOgvResourceDetailRes> responseObserver) {
        GetOgvResourceDetailRes.Builder resBuilder = GetOgvResourceDetailRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            OgvResourceDto detail = resOgvService.getOgvResourceDetail(request.getId(), true, false);
            OgvResource ogvResource = OgvResourceConverter.MAPPER.toOgvResource(detail);
            Assert.notNull(ogvResource, request.getId() + "不存在");
            resBuilder.setResource(ogvResource);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceDetail error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceDetail error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getOgvResourceList(GetOgvResourceListReq request, StreamObserver<GetOgvResourceListRes> responseObserver) {
        GetOgvResourceListRes.Builder resBuilder = GetOgvResourceListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            PageResult<OgvResourceDto> pageResult = resOgvService.getOgvResourceList(
                    OgvResourceConverter.MAPPER.toOgvResourceQueryDto(request));
            resBuilder.setPageable(Pageable.newBuilder().setTotalCount(pageResult.getTotal()).build())
                    .addAllResource(OgvResourceConverter.MAPPER.toOgvResource(pageResult.getRecords()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceList error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceList error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getOgvResourceSticker(GetOgvResourceStickerReq request, StreamObserver<GetOgvResourceStickerRes> responseObserver) {
        GetOgvResourceStickerRes.Builder resBuilder = GetOgvResourceStickerRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            List<OgvResourceStickerDto> stickers = resOgvService.getAllSticker();
            resBuilder.addAllSticker(OgvResourceConverter.MAPPER.toOgvResourceSticker(stickers));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceSticker error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvResourceSticker error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getOgvEpisode(GetOgvEpisodeReq request, StreamObserver<GetOgvEpisodeRes> responseObserver) {
        GetOgvEpisodeRes.Builder resBuilder = GetOgvEpisodeRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            List<OgvEpisodeDto> episodes = resOgvService.getOgvEpisode(request.getSeasonId());
            resBuilder.addAllEpisode(OgvResourceConverter.MAPPER.toOgvEpisode(episodes));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvEpisode error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getOgvEpisode error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBusinessAtmosphereList(GetBusinessAtmosphereListReq request,
                                          StreamObserver<GetBusinessAtmosphereListRes> responseObserver) {
        GetBusinessAtmosphereListRes.Builder resBuilder = GetBusinessAtmosphereListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            //BusinessAtmosphere
            reqJson = JsonFormat.printer().print(request);
            PageResult<BusinessAtmosphereDto> pageResult = businessAtmosphereService.getBusinessAtmosphereList(
                    request.getContentId(), request.getContentType(),
                    request.getPageable().getPageIndex(), request.getPageable().getPageSize());
            resBuilder.setPageable(Pageable.newBuilder().setTotalCount(pageResult.getTotal()).build())
                    .addAllAtmosphere(BusinessAtmosphereConverter.MAPPER.toBusinessAtmosphere(pageResult.getRecords()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getBusinessAtmosphereList error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getBusinessAtmosphereList error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }

    }

    @Override
    public void getCommentBlueKeyword(GetCommentBlueKeywordReq request, StreamObserver<GetCommentBlueKeywordRes> responseObserver) {
        GetCommentBlueKeywordRes.Builder resBuilder = GetCommentBlueKeywordRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            List<String> keywords = this.commentService.getCommentBlueKeyword(BlueKeywordConverter.MAPPER.toCommentBlueKeywordQueryDto(request));
            resBuilder.addAllKeyword(keywords);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getCommentBlueKeyword error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getCommentBlueKeyword error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void saveBlueKeywordConfig(SaveBlueKeywordConfigReq request, StreamObserver<SaveBlueKeywordConfigRes> responseObserver) {
        SaveBlueKeywordConfigRes.Builder resBuilder = SaveBlueKeywordConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BlueKeywordConfigDto configDto = BlueKeywordConverter.MAPPER.toBlueKeywordConfigDto(request.getConfig());
            Long id = this.blueKeywordService.saveBlueKeywordConfig(configDto, GrpcCommonConverter.MAPPER.toOperator(request.getRequester()));
            resBuilder.setId(id);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] saveBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] saveBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void enableBlueKeywordConfig(EnableBlueKeywordConfigReq request, StreamObserver<EnableBlueKeywordConfigRes> responseObserver) {
        EnableBlueKeywordConfigRes.Builder resBuilder = EnableBlueKeywordConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            this.blueKeywordService.enableBlueKeywordConfig(request.getId(), GrpcCommonConverter.MAPPER.toOperator(request.getRequester()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] enableBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] enableBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void disableBlueKeywordConfig(DisableBlueKeywordConfigReq request, StreamObserver<DisableBlueKeywordConfigRes> responseObserver) {
        DisableBlueKeywordConfigRes.Builder resBuilder = DisableBlueKeywordConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            this.blueKeywordService.disableBlueKeywordConfig(request.getId(), GrpcCommonConverter.MAPPER.toOperator(request.getRequester()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] disableBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] disableBlueKeywordConfig error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBlueKeywordConfigDetail(GetBlueKeywordConfigDetailReq request, StreamObserver<GetBlueKeywordConfigDetailRes> responseObserver) {
        GetBlueKeywordConfigDetailRes.Builder resBuilder = GetBlueKeywordConfigDetailRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BlueKeywordConfigDto config = this.blueKeywordService.getVisibleBlueKeywordConfig(request.getId());
            resBuilder.setConfig(BlueKeywordConverter.MAPPER.toBlueKeywordConfig(config));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigDetail error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigDetail error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBlueKeywordConfigList(GetBlueKeywordConfigListReq request, StreamObserver<GetBlueKeywordConfigListRes> responseObserver) {
        GetBlueKeywordConfigListRes.Builder resBuilder = GetBlueKeywordConfigListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BlueKeywordConfigQueryDto queryDto = BlueKeywordConfigQueryDto.builder()
                    .name(request.getName())
                    .status(BlueKeywordStatusEnum.VISIBLE_STATUS)
                    .pageIndex(request.getPageable().getPageIndex())
                    .pageSize(request.getPageable().getPageSize())
                    .build();
            PageResult<BlueKeywordConfigDto> pageResult = this.blueKeywordService.getBlueKeywordConfigList(queryDto);
            resBuilder.setPageable(Pageable.newBuilder().setTotalCount(pageResult.getTotal()).build())
                    .addAllConfig(BlueKeywordConverter.MAPPER.toBlueKeywordConfigList(pageResult.getRecords()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigList error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigList error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void uploadBlueKeywordConfigItem(UploadBlueKeywordConfigItemReq request, StreamObserver<UploadBlueKeywordConfigItemRes> responseObserver) {
        UploadBlueKeywordConfigItemRes.Builder resBuilder = UploadBlueKeywordConfigItemRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BlueKeywordConfigItemDto itemDto = BlueKeywordConverter.MAPPER.toBlueKeywordConfigItemDto(request.getItem());
            BlueKeywordConfigDto configDto = this.blueKeywordService.uploadBlueKeywordConfigItem(itemDto, GrpcCommonConverter.MAPPER.toOperator(request.getRequester()));
            resBuilder.setConfig(BlueKeywordConverter.MAPPER.toBlueKeywordConfig(configDto));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] uploadBlueKeywordConfigItem error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] uploadBlueKeywordConfigItem error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBlueKeywordConfigItem(GetBlueKeywordConfigItemReq request, StreamObserver<GetBlueKeywordConfigItemRes> responseObserver) {
        GetBlueKeywordConfigItemRes.Builder resBuilder = GetBlueKeywordConfigItemRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            Map<Long, BlueKeywordConfigItemDto> itemMap = this.blueKeywordService.getBlueKeywordConfigItem(request.getConfigIdList());
            resBuilder.addAllItem(BlueKeywordConverter.MAPPER.toBlueKeywordConfigItemList(itemMap.values()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigItem error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] getBlueKeywordConfigItem error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void saveBlueKeywordConfigV2(SaveBlueKeywordConfigV2Req request, StreamObserver<SaveBlueKeywordConfigV2Res> responseObserver) {
        SaveBlueKeywordConfigV2Res.Builder resBuilder = SaveBlueKeywordConfigV2Res.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BlueKeywordConfigDto configDto = BlueKeywordConverter.MAPPER.toBlueKeywordConfigDto(request.getConfig());
            BlueKeywordConfigItemDto itemDto = BlueKeywordConverter.MAPPER.toBlueKeywordConfigItemDto(request.getItem());
            Long id = this.blueKeywordService.saveBlueKeywordConfigV2(configDto, itemDto, GrpcCommonConverter.MAPPER.toOperator(request.getRequester()));
            resBuilder.setId(id);
        } catch (IllegalArgumentException e) {
            log.error("[BrandResourceGrpcServiceImpl] saveBlueKeywordConfigV2 error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandResourceGrpcServiceImpl] saveBlueKeywordConfigV2 error,request={},error:{}", reqJson, ExceptionUtils.getStackTrace(e));
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }
}
