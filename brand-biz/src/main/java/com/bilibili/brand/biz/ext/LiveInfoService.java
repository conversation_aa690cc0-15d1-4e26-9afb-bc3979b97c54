package com.bilibili.brand.biz.ext;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.ext.Dto.UpActReserveRelationInfoDto;
import com.bilibili.brand.api.ext.Dto.UpActReserveRelationRes;
import com.bilibili.brand.api.ext.ILiveInfoService;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020年9月25日
 */
@Slf4j
@Service
public class LiveInfoService implements ILiveInfoService {

    private static final Integer QUERY_LIMIT = 10;

    private static final String QUERY_UP_ACT_RESERVE_RELATION_INFO_URL = "http://api.bilibili.com/x/new-reserve/reserve/multi_info";

    @Autowired
    @Qualifier("completableFutureExecutorWithDecorator")
    private ThreadPoolTaskExecutor threadPool;

    @Override
    public Map<Long, UpActReserveRelationInfoDto> queryUpActReserve(List<Long> liveBookingIds) {
        List<Long> validLiveBookingIdList = liveBookingIds.stream()
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validLiveBookingIdList)) {
            return new HashMap<>();
        }

        List<UpActReserveRelationInfoDto> result = Lists.partition(liveBookingIds, QUERY_LIMIT)
                .stream()
                .map(list -> CompletableFuture.supplyAsync(() -> getReserveRelationInfoBos(list), threadPool).join())
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        return result.stream().collect(Collectors.toMap(UpActReserveRelationInfoDto::getSid, Function.identity()));
    }

    @Override
    public void validateLiveBookingIdList(List<Long> liveBookingIds) {
        List<Long> validLiveBookingIdList = liveBookingIds.stream()
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validLiveBookingIdList)) {
            return;
        }

        Map<Long, UpActReserveRelationInfoDto> upActReserveRelationInfoDtoMap = this.queryUpActReserve(validLiveBookingIdList);
        List<Long> invalidLiveBookingIdList = validLiveBookingIdList.stream()
                .filter(liveBookingId -> {
                    UpActReserveRelationInfoDto upActReserveRelationInfoDto = upActReserveRelationInfoDtoMap.get(liveBookingId);
                    return upActReserveRelationInfoDto == null || upActReserveRelationInfoDto.getState() != 100;
                })
                .collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(invalidLiveBookingIdList), "直播预约id列表中存在无效的直播预约id：" + invalidLiveBookingIdList);
    }

    /**
     * 查询预约状态接口文档
     * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=944841668">...</a>
     */
    private List<UpActReserveRelationInfoDto> getReserveRelationInfoBos(List<Long> liveBookingIds) {
        log.info("queryUpActReserveRelationInfoUrl req: {}", liveBookingIds);
        String ids = Joiner.on(",").join(liveBookingIds);

        UpActReserveRelationRes response;
        try {
            response = OkHttpUtils.get(QUERY_UP_ACT_RESERVE_RELATION_INFO_URL)
                    .param("sids", ids)
                    .callForObject(UpActReserveRelationRes.class);
            log.info("queryUpActReserveRelationInfoUrl res: {}", response);
            if (Objects.isNull(response)
                    || !Objects.equals(response.getCode(), 0)
                    || Objects.isNull(response.getData())
                    || MapUtils.isEmpty(response.getData().getList())) {
                return new ArrayList<>();
            }
            return new ArrayList<>(response.getData().getList().values());
        } catch (Exception e) {
            log.error("queryUpActReserveRelationInfoUrl error, liveBookingIds: {}", liveBookingIds, e);
            return new ArrayList<>();
        }
    }
}
