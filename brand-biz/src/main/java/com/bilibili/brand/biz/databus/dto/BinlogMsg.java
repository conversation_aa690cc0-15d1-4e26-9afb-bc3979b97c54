package com.bilibili.brand.biz.databus.dto;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * binlog公共类
 *
 * <AUTHOR>
 * @date 2023/10/26 16:10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BinlogMsg implements Serializable {

    private static final long serialVersionUID = -6542548834974503484L;
    /**
     * insert,update,delete
     */
    private String action;
    /**
     * 数据库名
     */
    private String schema;
    /**
     * 表名
     */
    private String table;
    /**
     * 表的主键字段，如果有多个，以逗号分隔，例如："id,field1"
     */
    private String pk_names;
    /**
     * canal 同步该条数据的时间戳
     */
    private Long msec;
    /**
     * canal 内部产生的数据序列号，递增的，可用于判断消息的先后顺序
     */
    private Long seq;

    /**
     * insert 的新数据， delete 的原数据， update 的新数据
     * 对于 text 类型字段，canal 解析获取到的是 []uint8 类型，序列化时会自动 base64.encode 编码，所以在下游读取时该字段需要进行 base64.decode 解码
     */
    @JSONField(name = "new")
    private JSONObject newData;

    /**
     * update 的原数据
     */
    @JSONField(name = "old")
    private JSONObject oldData;
}
