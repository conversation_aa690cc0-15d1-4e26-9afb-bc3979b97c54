package com.bilibili.brand.biz.cycle.processor;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.biz.cycle.OgvPriceService;
import com.bilibili.brand.biz.schedule.dao.OgvPriceDao;
import com.bilibili.brand.biz.schedule.po.OgvPricePo;
import com.bilibili.brand.biz.schedule.po.OgvPricePoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OgvCptGdCycleProcessor extends BaseGdCycleProcessor {

    @Autowired
    private OgvPriceService ogvPriceService;

    @Autowired
    private OgvPriceDao ogvPriceDao;

    @Override
    protected void businessProcessCreate(Integer sourceCycleId, Integer targetCycleId) {
        // 复制关联OGV刊例价表
        this.ogvPriceService.copyPriceWithCycle(sourceCycleId, targetCycleId);
    }

    @Override
    protected void businessProcessDelete(Integer cycleId, Integer orderProduct) {
        // 删除OGV刊例价表
        OgvPricePoExample updateOgvPricePoExample = new OgvPricePoExample();
        updateOgvPricePoExample.createCriteria()
                .andCycleIdEqualTo(cycleId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        OgvPricePo updateOgvPricePo = OgvPricePo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build();
        ogvPriceDao.updateByExampleSelective(updateOgvPricePo, updateOgvPricePoExample);
    }

    @Override
    OrderProduct supportOrderProduct() {
        return OrderProduct.OGV_CPT;
    }
}
