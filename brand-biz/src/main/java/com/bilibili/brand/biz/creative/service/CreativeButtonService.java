package com.bilibili.brand.biz.creative.service;

import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.dao.GdCreativeButtonCopyDao;
import com.bilibili.brand.biz.creative.dao.GdCreativeCustomizeButtonDao;
import com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPo;
import com.bilibili.brand.biz.creative.po.GdCreativeButtonCopyPoExample;
import com.bilibili.brand.biz.creative.po.GdCreativeCustomizeButtonPo;
import com.bilibili.brand.biz.creative.po.GdCreativeCustomizeButtonPoExample;
import com.bilibili.cpt.platform.api.creative.dto.ButtonDto;
import com.bilibili.cpt.platform.common.ButtonStyle;
import com.bilibili.cpt.platform.common.CategoryEnum;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/12 16:52
 */
@Service
@Slf4j
public class CreativeButtonService {

    @Autowired
    private GdCreativeButtonCopyDao gdCreativeButtonCopyDao;

    @Autowired
    private GdCreativeCustomizeButtonDao customizeButtonDao;

    @Autowired
    private ISoaLandingPageService soaLandingPageService;

    public void saveOrUpdateButtonCopy(Long gdCreativeId, Long boundId, CategoryEnum category, List<ButtonDto> buttonDtos) {
        GdCreativeButtonCopyPoExample selectExample = new GdCreativeButtonCopyPoExample();
        selectExample.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(gdCreativeId)
                .andBoundIdEqualTo(boundId)
                .andCategoryEqualTo(category.getCode());

        gdCreativeButtonCopyDao.updateByExampleSelective(GdCreativeButtonCopyPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), selectExample);

        GdCreativeCustomizeButtonPoExample customizeButtonPoExample = new GdCreativeCustomizeButtonPoExample();
        customizeButtonPoExample.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(gdCreativeId)
                .andBoundIdEqualTo(boundId)
                .andCategoryEqualTo(category.getCode());

        customizeButtonDao.updateByExampleSelective(GdCreativeCustomizeButtonPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), customizeButtonPoExample);

        if (CollectionUtils.isEmpty(buttonDtos)) {
            return;
        }

        List<GdCreativeButtonCopyPo> buttonCopyPos = new ArrayList<>();
        List<GdCreativeCustomizeButtonPo> customizeButtonPos = new ArrayList<>();
        Timestamp now = new Timestamp(System.currentTimeMillis());
        AtomicInteger seq = new AtomicInteger(0);
        buttonDtos.forEach(t -> {
            String actualJumpUrl = t.getJumpUrl();
//            //建站预发环境获得的数据会有predev-，线上无法访问，去除predev-，后续整合方法
//            if (GdJumpType.MGK_PAGE_ID.getCode().equals(t.getJumpType())) {
//                MgkLandingPageBean pageBean = soaLandingPageService.validatePageIdAndGetLandingPage(t.getJumpUrl());
//                Assert.notNull(pageBean, "不支持的落地页类型!");
//                Assert.isTrue(!StringUtils.isEmpty(pageBean.getLaunchUrl()), "不支持的落地页类型!");
//                actualJumpUrl = pageBean.getLaunchUrl().replace("predev-", "");
//            }

            if (ButtonStyle.SELECT.getCode() == t.getButtonStyle()) {
                GdCreativeButtonCopyPo po = GdCreativeButtonCopyPo.builder()
                        .creativeId(gdCreativeId)
                        .buttonCopyId(t.getButtonCopyId())
                        .jumpUrl(actualJumpUrl)
                        .buttonType(t.getButtonType())
                        .schemeUrl(StringUtils.isEmpty(t.getSchemeUrl()) ? "" : t.getSchemeUrl())
                        .mtime(now).ctime(now)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .seq(seq.incrementAndGet())
                        .imageId(0)
                        .buttonName(StringUtils.isEmpty(t.getButtonName()) ? "" : t.getButtonName())
                        .jumpType(t.getJumpType())
                        .extendUrl("")
                        .category(category.getCode())
                        .boundId(boundId)
                        .gameBaseId(Objects.isNull(t.getGameBaseId()) ? 0: t.getGameBaseId())
                        .trackadf(Objects.isNull(t.getTrackAdf()) ? "" : t.getTrackAdf())
                        .customizedUrl(Objects.isNull(t.getCustomizedUrl()) ? "" : t.getCustomizedUrl())
                        .build();
                buttonCopyPos.add(po);

            } else {
                GdCreativeCustomizeButtonPo buttonPo = GdCreativeCustomizeButtonPo.builder()
                        .buttonCopy(t.getButtonName())
                        .creativeId(gdCreativeId)
                        .seq(seq.incrementAndGet())
                        .jumpUrl(actualJumpUrl)
                        .jumpType(t.getJumpType())
                        .mtime(now)
                        .ctime(now)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .extremeTeamDataSourceId(t.getExtremeTeamDataSourceId() == null ? 0L : t.getExtremeTeamDataSourceId())
                        .category(category.getCode())
                        .boundId(boundId)
                        .build();
                customizeButtonPos.add(buttonPo);
            }
        });

        if (!CollectionUtils.isEmpty(buttonCopyPos)) {
            for (GdCreativeButtonCopyPo buttonCopyPo : buttonCopyPos) {
                gdCreativeButtonCopyDao.insertSelective(buttonCopyPo);
            }
        }
        if (!CollectionUtils.isEmpty(customizeButtonPos)) {
            for (GdCreativeCustomizeButtonPo customizeButtonPo : customizeButtonPos) {
                customizeButtonDao.insertSelective(customizeButtonPo);
            }
        }
    }

    public List<ButtonDto> getButtons(long creativeId, long boundId, CategoryEnum category) {
        Map<Long, List<ButtonDto>> buttons = getButtons(Lists.newArrayList(creativeId), boundId, category);
        return buttons.getOrDefault(creativeId, Lists.newArrayList());
    }


    /**
     * 查询指定类别的按钮
     *
     * @return
     */
    public List<ButtonDto> getButtons(Long creativeId, CategoryEnum category) {
        Map<Long, List<ButtonDto>> buttons = getButtons(Lists.newArrayList(creativeId), null, category);
        return buttons.getOrDefault(creativeId, Lists.newArrayList());
    }

    /**
     * 查询指定类别的按钮
     *
     * @return
     */
    public Map<Long, List<ButtonDto>> getButtons(List<Long> creativeIds, CategoryEnum category) {
        return getButtons(creativeIds, null, category);
    }

    /**
     * 该方法不具有实际的通用性，因为不同创意的boundId理论上都是不同的，实际上使用情况只有两种：
     * （1）查询创意层级的按钮，此时boundId=0（默认的形式）
     * （2）查询单个创意的指定类别的按钮，此时creativeIds.size=1
     * （3）查询创意的指定类别层级的按钮，此时boundId=null
     *
     * @return
     */
    private Map<Long, List<ButtonDto>> getButtons(List<Long> creativeIds, Long boundId, CategoryEnum category) {
        if (CollectionUtils.isEmpty(creativeIds) || category == null) {
            return Maps.newHashMap();
        }

        boolean validBoundId = boundId != null;
        List<ButtonDto> buttonDtos = new ArrayList<>();
        GdCreativeButtonCopyPoExample buttonCopyPoExample = new GdCreativeButtonCopyPoExample();
        GdCreativeButtonCopyPoExample.Criteria criteria = buttonCopyPoExample.createCriteria()
                .andCreativeIdIn(creativeIds)
                .andCategoryEqualTo(category.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (validBoundId) {
            criteria.andBoundIdEqualTo(boundId);
        }
        List<GdCreativeButtonCopyPo> copyPos = gdCreativeButtonCopyDao.selectByExample(buttonCopyPoExample);
        if (!CollectionUtils.isEmpty(copyPos)) {
            copyPos.forEach(t -> buttonDtos.add(this.convertDto(t)));
        }

        GdCreativeCustomizeButtonPoExample customizeButtonPoExample = new GdCreativeCustomizeButtonPoExample();
        GdCreativeCustomizeButtonPoExample.Criteria customizeCriteria = customizeButtonPoExample.createCriteria()
                .andCreativeIdIn(creativeIds)
                .andCategoryEqualTo(category.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (validBoundId) {
            customizeCriteria.andBoundIdEqualTo(boundId);
        }

        List<GdCreativeCustomizeButtonPo> customizeButtonPos = customizeButtonDao.selectByExample(customizeButtonPoExample);
        if (!CollectionUtils.isEmpty(customizeButtonPos)) {
            customizeButtonPos.forEach(t -> buttonDtos.add(this.convertDto(t)));
        }
        Map<Long, List<ButtonDto>> creativeButtons = buttonDtos.stream()
                .collect(Collectors.groupingBy(ButtonDto::getCreativeId));
        creativeButtons.values()
                .stream()
                .forEach(buttons -> Collections.sort(buttons, Comparator.comparing(ButtonDto::getSeq)));
        return creativeButtons;
    }

    private ButtonDto convertDto(GdCreativeButtonCopyPo po) {
        return ButtonDto.builder()
                .buttonCopyId(po.getButtonCopyId())
                .buttonName(po.getButtonName())
                .buttonType(po.getButtonType()).seq(po.getSeq())
                .buttonStyle(ButtonStyle.SELECT.getCode())
                .jumpType(po.getJumpType())
                .jumpUrl(po.getJumpUrl())
                .schemeUrl(po.getSchemeUrl())
                .seq(po.getSeq())
                .category(po.getCategory())
                .boundId(po.getBoundId())
                .creativeId(po.getCreativeId())
                .imageId(po.getImageId())
                .build();
    }

    private ButtonDto convertDto(GdCreativeCustomizeButtonPo po) {
        String originalUrl = po.getJumpUrl();
        if (GdJumpType.MGK_PAGE_ID.getCode().equals(po.getJumpType())) {
            originalUrl = GdJumpType.MGK_PAGE_ID.parseLaunchUrl(po.getJumpUrl());
        }
        return ButtonDto.builder()
                .buttonName(po.getButtonCopy())
                .seq(po.getSeq())
                .buttonStyle(ButtonStyle.CUSTOMIZE.getCode())
                .jumpType(po.getJumpType())
                .jumpUrl(originalUrl)
                .category(po.getCategory())
                .boundId(po.getBoundId())
                .creativeId(po.getCreativeId())
                .extremeTeamDataSourceId(po.getExtremeTeamDataSourceId())
                .build();
    }

    /**
     * 查询指定层级的选择式按钮
     *
     * @return
     */
    public Map<Long, ButtonDto> getButtonCopyByCreativeId(List<Long> creativeIds, CategoryEnum category) {
        if (CollectionUtils.isEmpty(creativeIds) || category == null) {
            return Maps.newHashMap();
        }
        GdCreativeButtonCopyPoExample example = new GdCreativeButtonCopyPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds)
                .andCategoryEqualTo(category.getCode());
        List<GdCreativeButtonCopyPo> pos = gdCreativeButtonCopyDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Maps.newHashMap();
        }
        return pos.stream()
                .collect(Collectors.toMap(GdCreativeButtonCopyPo::getCreativeId,
                        po -> this.convertDto(po), (p1, p2) -> p1));
    }
}
