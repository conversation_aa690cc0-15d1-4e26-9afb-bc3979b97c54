package com.bilibili.brand.biz.config.business;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/6/6
 */
@Component
@Getter
public class SsaOttConfig {
    @Value("${ssa.ott.schedule.max.frequency:2}")
    private Integer ssaOttMaxFrequency;

    //OTT闪屏视频最大大小，单位KB
    @Value("${ssa.ott.video.max.size:30720}")
    private Integer ssaOttVideoMaxSize;

    //OTT闪屏视频最小时长，单位秒
    @Value("${ssa.ott.video.min.duration:5}")
    private Integer ssaOttVideoMinDuration;

    //OTT闪屏视频最大时长，单位秒
    @Value("${ssa.ott.video.max.duration:15}")
    private Integer ssaOttVideoMaxDuration;
}
