package com.bilibili.brand.biz.creative.po.querydsl;

import javax.annotation.Generated;

/**
 * GdCrowdPackagePo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class GdCrowdPackagePo {

    private java.sql.Timestamp ctime;

    private java.sql.Timestamp endTime;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private String name;

    private String operator;

    private Long packageId;

    private Integer scene;

    private java.sql.Timestamp startTime;

    private Integer userType;

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public java.sql.Timestamp getEndTime() {
        return endTime;
    }

    public void setEndTime(java.sql.Timestamp endTime) {
        this.endTime = endTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getPackageId() {
        return packageId;
    }

    public void setPackageId(Long packageId) {
        this.packageId = packageId;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public java.sql.Timestamp getStartTime() {
        return startTime;
    }

    public void setStartTime(java.sql.Timestamp startTime) {
        this.startTime = startTime;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    @Override
    public String toString() {
         return "ctime = " + ctime + ", endTime = " + endTime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", name = " + name + ", operator = " + operator + ", packageId = " + packageId + ", scene = " + scene + ", startTime = " + startTime + ", userType = " + userType;
    }

}

