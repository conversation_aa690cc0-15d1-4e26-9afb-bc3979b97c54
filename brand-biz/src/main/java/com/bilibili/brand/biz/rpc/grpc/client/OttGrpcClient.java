package com.bilibili.brand.biz.rpc.grpc.client;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.bapis.ott.service.*;
import com.bilibili.enums.OttMediaStatusEnum;
import com.bilibili.utils.BatchUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/22 17:51
 */
@Slf4j
@Component
public class OttGrpcClient {
    @RPCClient("ott.service")
    private OTTServiceGrpc.OTTServiceBlockingStub ottServiceBlockingStub;


    /**
     * 查询OTT Season状态
     */
    public Map<Long, OttMediaStatusEnum> querySeasonStatus(List<Long> seasonIdList) {
        return doQueryMediaStatus(seasonIdList, MediaTp.Pgc);
    }

    /**
     * 查询OTT Episode状态
     */
    public Map<Long, OttMediaStatusEnum> queryEpisodeStatus(List<Long> episodeIdList) {
        return doQueryMediaStatus(episodeIdList, MediaTp.PgcVideo);
    }

    /**
     * 查询OTT 稿件状态
     */
    public Map<Long, OttMediaStatusEnum> queryArchiveStatus(List<Long> archiveIdList) {
        return doQueryMediaStatus(archiveIdList, MediaTp.Ugc);
    }

    /**
     * 查询OTT Media状态
     */
    public Map<Long, OttMediaStatusEnum> doQueryMediaStatus(List<Long> idList, MediaTp media) {
        if (CollectionUtils.isEmpty(idList)) return Maps.newHashMap();
        try {
            log.info("[OttGrpcClient] doQueryMediaStatus request,type={}, idList={}", media, JSONObject.toJSONString(idList));
            List<Pair<Long, OttMediaStatusEnum>> replies = BatchUtil.batch(idList, subIdList -> {
                MediaStatusBatchReply mediaStatusBatchReply = ottServiceBlockingStub
                        .withDeadlineAfter(10, TimeUnit.SECONDS)
                        .mediaStatusBatch(MediaStatusBatchReq.newBuilder()
                                .addAllItem(subIdList.stream()
                                        .map(id -> Media.newBuilder().setId(id).setType(media).build())
                                        .collect(Collectors.toList()))
                                .build());
                return mediaStatusBatchReply.getResultMap().entrySet().stream()
                        .map(en -> Pair.of(
                                //key={MediaTp#number}_{id}
                                Long.valueOf(en.getKey().split("_")[1]),
                                OttMediaStatusEnum.getByCodeWithoutEx(en.getValue().getStatus().getNumber())
                        ))
                        .collect(Collectors.toList());
            }, 100);
            Map<Long, OttMediaStatusEnum> res = replies.stream()
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue, (s, t) -> t));
            log.info("[OttGrpcClient] doQueryMediaStatus response, type={},res={}", media, JSON.toJSONString(res));
            return res;
        } catch (Exception e) {
            log.error("[OttGrpcClient] doQueryMediaStatus, error", e);
            throw new RuntimeException(e);
        }
    }


    /**
     * 查询Season是否可投放
     */
    public Map<Long, Boolean> querySeasonAllows(List<Long> seasonIdList) {
        return doQueryAllows(seasonIdList, "season");
    }

    /**
     * 查询Episode是否可投放
     */
    public Map<Long, Boolean> queryEpisodeAllows(List<Long> episodeIdList) {
        return doQueryAllows(episodeIdList, "episode");
    }

    public Map<Long, Boolean> doQueryAllows(List<Long> idList, String type) {
        if (CollectionUtils.isEmpty(idList)) return Maps.newHashMap();

        //mock
//        boolean mock = true;
//        if (mock) {
//            return idList.stream().collect(Collectors.toMap(Function.identity(), id -> true));
//        }

        try {
            log.info("[OttGrpcClient] doQueryAllows request, type={},idList={}", type, JSONObject.toJSONString(idList));
            List<Record> replies = BatchUtil.batch(idList, subIdList -> {
                CheckResourcePlacementAllowListReply allowListReply = ottServiceBlockingStub
                        .withDeadlineAfter(10, TimeUnit.SECONDS)
                        .checkResourcePlacementAllowList(
                                CheckResourcePlacementAllowListReq.newBuilder()
                                        .addAllRecord(subIdList.stream().map(id -> Record.newBuilder()
                                                .setId(id.toString())
                                                .setType(type)
                                                .build()).collect(Collectors.toList()))
                                        .setBusiness("cm")
                                        .setResourcePlacementCode("player_fragment")
                                        .build());
                return allowListReply.getRecordList();
            }, 50);
            Map<Long, Boolean> res = replies.stream()
                    .collect(Collectors.toMap(r -> Long.parseLong(r.getId()), r -> "allow".equals(r.getResult()), (s, t) -> t));
            log.info("[OttGrpcClient] doQueryAllows response, type={},res={}", type, JSON.toJSONString(res));
            return res;
        } catch (Exception e) {
            log.error("[OttGrpcClient] doQueryAllows, error", e);
            throw new RuntimeException(e);
        }
    }

    public ValidateAdCardResp validateAdCard(ValidateAdCardReq req) {
        return ottServiceBlockingStub
                .withDeadlineAfter(2000, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .validateAdCard(req);
    }
}
