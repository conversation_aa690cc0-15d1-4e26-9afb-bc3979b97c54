package com.bilibili.brand.biz.creative.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.creative.dto.CreativeDanmakuDto;
import com.bilibili.brand.biz.converter.BrandCreativeConverter;
import com.bilibili.cpt.platform.api.creative.dto.MgkVideoInfoDto;
import com.bilibili.cpt.platform.biz.dao.CreativeDanmakuDao;
import com.bilibili.cpt.platform.biz.po.CreativeDanmakuPo;
import com.bilibili.cpt.platform.biz.po.CreativeDanmakuPoExample;
import com.bilibili.enums.AnimationTypeEnum;
import com.bilibili.enums.DanmakuAtmosphereTypeEnum;
import com.bilibili.enums.TemplatePropertyEnum;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 【品牌广告】七夕连击互动样式-仅硬广
 * <a href="https://www.tapd.cn/67874887/prong/stories/view/1167874887004272197">七夕连击互动样式</a>
 * <a href="https://doc.weixin.qq.com/doc/w3_AVAAMQZxACUikEs3sI1Sna45ccRfy?scode=ANYAEAdoABEziil10QAVAAMQZxACU">七夕连击互动样式文档</a>
 *
 * <AUTHOR>
 * @date 2024/6/11 17:41
 */
@Slf4j
@Service
public class CreativeDanmakuService {
    @Autowired
    private CreativeDanmakuDao creativeDanmakuDao;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveCreativeDanmakuContext {
        private Integer templateId;
        private MgkVideoInfoDto mgkVideo;
        private CreativeDanmakuDto danmaku;
    }


    /**
     * 校验弹幕素材是否合法
     *
     * @param ctx 上下文
     * @return true 是弹幕场景且合法， false表示非弹幕场景
     */
    public boolean validate(SaveCreativeDanmakuContext ctx) {
        Integer templateId = ctx.getTemplateId();
        TemplatePropertyEnum templateProperty = TemplatePropertyEnum.getByTemplateIdWithoutEx(templateId);
        if (!Objects.equals(templateProperty, TemplatePropertyEnum.TEMPLATE_INLINE_DANMAKU_WITH_TWIST)) {
            return false;
        }

        CreativeDanmakuDto danmaku = ctx.getDanmaku();
        DanmakuAtmosphereTypeEnum atmosphereType = DanmakuAtmosphereTypeEnum.getByType(danmaku.getAtmosphereType());
        AnimationTypeEnum.getByType(danmaku.getBtnAnimType());
        Assert.hasText(danmaku.getBtnText(), "按钮文案不能为空");
        Assert.notEmpty(danmaku.getDmContents(), "弹幕文案不能为空");
        Assert.notNull(danmaku.getDmLeaderIcon(), "弹幕头图链接不能为空");
        Assert.hasText(danmaku.getDmLeaderIcon().getUrl(), "弹幕头图链接不能为空");
        Assert.hasText(danmaku.getDmLeaderIcon().getMd5(), "弹幕头图链接无效");
        Assert.hasText(danmaku.getDmTextColor(), "弹幕文案色值不能为空");
        Assert.hasText(danmaku.getDmBgColor(), "弹幕背景色值不能为空");
        if (!Objects.equals(atmosphereType, DanmakuAtmosphereTypeEnum.CUSTOMIZED)) {
            Assert.hasText(danmaku.getBtnAnimUrl(), "按钮动效链接不能为空");
            Assert.notNull(danmaku.getBtnIcon(), "按钮icon链接不能为空");
            Assert.hasText(danmaku.getBtnIcon().getUrl(), "按钮icon链接不能为空");
            Assert.hasText(danmaku.getBtnIcon().getMd5(), "按钮icon链接无效");
        }
        return true;
    }


    /**
     * 保存弹幕素材
     * 1、如果是非弹幕场景，则返回0（不会有任何异常）
     * 2、如果是弹幕场景，且对应记录不存在，则新建
     * 3、如果是弹幕场景，且对应记录已经存在，则覆盖
     *
     * @param ctx 上下文
     */
    public void saveCreativeDanmaku(SaveCreativeDanmakuContext ctx) {
        if (!this.validate(ctx)) {
            return;
        }

        CreativeDanmakuDto danmaku = ctx.getDanmaku();
        Assert.isTrue(Utils.isPositive(danmaku.getCreativeId()), "弹幕必须绑定具体的创意");
        CreativeDanmakuPo creativeDanmakuPo = BrandCreativeConverter.MAPPER.toCreativeDanmakuPo(danmaku);
        CreativeDanmakuPo oldCreativeDanmakuPo = this.getPoByCreativeId(danmaku.getCreativeId());
        if (Objects.nonNull(oldCreativeDanmakuPo)) {
            creativeDanmakuPo.setId(oldCreativeDanmakuPo.getId());
            this.creativeDanmakuDao.updateByPrimaryKeySelective(creativeDanmakuPo);
        } else {
            this.creativeDanmakuDao.insertSelective(creativeDanmakuPo);
        }
    }

    /**
     * 根据创意查询对应的弹幕信息
     */
    public Map<Long, CreativeDanmakuDto> getCreativeDanmakus(List<Long> creativeIdList) {
        if (CollectionUtils.isEmpty(creativeIdList)) {
            return Maps.newHashMap();
        }
        CreativeDanmakuPoExample example = new CreativeDanmakuPoExample();
        example.createCriteria()
                .andCreativeIdIn(creativeIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CreativeDanmakuPo> creativeDanmakuPos = this.creativeDanmakuDao.selectByExample(example);
        return creativeDanmakuPos.stream()
                .map(BrandCreativeConverter.MAPPER::toCreativeDanmakuDto)
                .collect(Collectors.toMap(CreativeDanmakuDto::getCreativeId, Function.identity(), OptionalUtil.override()));

    }

    private CreativeDanmakuPo getPoByCreativeId(Long creativeId) {
        if (!Utils.isPositive(creativeId)) {
            return null;
        }
        CreativeDanmakuPoExample example = new CreativeDanmakuPoExample();
        example.createCriteria()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CreativeDanmakuPo> creativeDanmakuPos = this.creativeDanmakuDao.selectByExample(example);
        return CollectionUtils.isEmpty(creativeDanmakuPos) ? null : creativeDanmakuPos.get(0);
    }

}
