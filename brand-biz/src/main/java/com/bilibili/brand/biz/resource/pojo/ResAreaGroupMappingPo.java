package com.bilibili.brand.biz.resource.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResAreaGroupMappingPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 地域组ID
     */
    private Integer areaGroupId;

    /**
     * 地域定向项的ID
     */
    private Integer areaTargetItemId;

    /**
     * 软删除,0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 状态: 1-启用 2-封停
     */
    private Integer state;

    private static final long serialVersionUID = 1L;
}