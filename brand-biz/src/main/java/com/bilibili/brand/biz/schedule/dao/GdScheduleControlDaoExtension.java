package com.bilibili.brand.biz.schedule.dao;

import com.bilibili.brand.biz.schedule.po.GdScheduleControlPo;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/17 10:41
 */
public interface GdScheduleControlDaoExtension {
    //endDay兼容不分时，endTime兼容分时
    List<GdScheduleControlPo> selectToDeletedScheduleControl(@Param("endDay") Timestamp endDay,
                                                             @Param("endTime") Timestamp endTime);
}
