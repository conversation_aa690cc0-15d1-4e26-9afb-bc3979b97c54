package com.bilibili.brand.biz.creative.service;

import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.creative.po.querydsl.GdCreativeMidPreviewPo;
import com.bilibili.brand.util.TimeUtil;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bilibili.brand.biz.creative.dao.querydsl.QGdCreativeMidPreview.gdCreativeMidPreview;

/**
 * <AUTHOR>
 * @date 2023/7/17
 */
@Service
public class CreativeMidPreviewService {

    @Autowired
    @Qualifier("businessAd")
    private BaseQueryFactory bqf;

    @Autowired
    private ConfigCenter configCenter;

    public String getContent(boolean isSsa) {
        if (isSsa) {
            return configCenter.getPreviewConfig().getSsaContent();
        }else{
            return configCenter.getPreviewConfig().getOtherContent();
        }
    }

    public void deleteExpiredPreviewMid() {
        List<Integer> expiredIds = bqf.select(gdCreativeMidPreview.id)
                .from(gdCreativeMidPreview)
                .where(gdCreativeMidPreview.previewTime.lt(TimeUtil.toTimestamp(LocalDateTime.now().minusMinutes(configCenter.getPreviewConfig().getCreativePreviewMinuteForEachMid())))
                        .and(gdCreativeMidPreview.isDeleted.eq(0)))
                .fetch();
        if (CollectionUtils.isEmpty(expiredIds)) {
            return;
        }

        CollectionHelper.processInBatches(expiredIds,
                100,
                ids -> bqf.update(gdCreativeMidPreview)
                        .set(gdCreativeMidPreview.isDeleted, 1)
                        .where(gdCreativeMidPreview.id.in(ids))
                        .execute());
    }

    @Transactional(rollbackFor = Exception.class)
    public void bindPreviewMidList(long creativeId, int salesType, List<Long> midList) {

        if (CollectionUtils.isEmpty(midList)) {
            return;
        }

        List<GdCreativeMidPreviewPo> existedPreviewMidList = query(creativeId, midList);

        Set<Long> midSet = existedPreviewMidList.stream()
                .map(GdCreativeMidPreviewPo::getMid)
                .collect(Collectors.toSet());

        List<Long> insertList = midList.stream()
                .filter(mid -> !midSet.contains(mid))
                .collect(Collectors.toList());

        Timestamp now = TimeUtil.toTimestamp(LocalDateTime.now());
        insertPreview(creativeId, salesType, now, insertList);
        updatePreview(now, existedPreviewMidList);
    }

    public void removeAllMidPreview(List<Long> creativeIds) {
        List<Integer> previewingIdList = bqf.select(gdCreativeMidPreview.id)
                .from(gdCreativeMidPreview)
                .where(gdCreativeMidPreview.creativeId.in(creativeIds)
                        .and(gdCreativeMidPreview.isDeleted.eq(0)))
                .fetch();

        if (!CollectionUtils.isEmpty(previewingIdList)) {
            bqf.update(gdCreativeMidPreview)
                    .set(gdCreativeMidPreview.isDeleted, 1)
                    .where(gdCreativeMidPreview.id.in(previewingIdList))
                    .execute();
        }
    }

    public List<Long> queryAllPreviewMid(long creativeId) {
        return bqf.select(gdCreativeMidPreview.mid)
                .from(gdCreativeMidPreview)
                .where(gdCreativeMidPreview.creativeId.eq(creativeId)
                        .and(gdCreativeMidPreview.isDeleted.eq(0)))
                .fetch();
    }

    private void insertPreview(long creativeId, int salesType, Timestamp previewTime, List<Long> midList) {

        if (CollectionUtils.isEmpty(midList)) {
            return;
        }
        List<GdCreativeMidPreviewPo> pos = midList.stream()
                .map(mid -> {
                    GdCreativeMidPreviewPo po = new GdCreativeMidPreviewPo();
                    po.setCreativeId(creativeId);
                    po.setMid(mid);
                    po.setSalesType(salesType);
                    po.setPreviewTime(previewTime);
                    return po;
                }).collect(Collectors.toList());
        bqf.insert(gdCreativeMidPreview).insertBeans(pos);
    }

    private void updatePreview(Timestamp previewTime, List<GdCreativeMidPreviewPo> existedPreviewMidList) {
        if (CollectionUtils.isEmpty(existedPreviewMidList)) {
            return;
        }

        List<GdCreativeMidPreviewPo> pos = existedPreviewMidList.stream()
                .peek(po -> {
                    po.setPreviewTime(previewTime);
                    po.setIsDeleted(0);
                    po.setMtime(null);
                })
                .collect(Collectors.toList());

        bqf.update(gdCreativeMidPreview).updateBeans(pos);
    }

    private List<GdCreativeMidPreviewPo> query(long creativeId, List<Long> midList) {
        return bqf.selectFrom(gdCreativeMidPreview)
                .where(gdCreativeMidPreview.creativeId.eq(creativeId)
                        .and(gdCreativeMidPreview.mid.in(midList)
                                .and(gdCreativeMidPreview.isDeleted.eq(0)))
                ).fetch();
    }

}
