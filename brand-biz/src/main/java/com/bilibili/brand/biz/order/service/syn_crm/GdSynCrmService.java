package com.bilibili.brand.biz.order.service.syn_crm;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.ShowPriorityType;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.order.service.GdOrderExtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/2/17
 */
@Service
public class GdSynCrmService extends BaseSynCrmService {

    @Autowired
    private GdOrderExtService gdOrderExtService;

    private final ThreadLocal<GdOrderExtDto> flyGdRangeOrder = new ThreadLocal<>();

    private static final Set<Integer> gdProducts = new HashSet<>(Arrays.asList(OrderProduct.SSA_OTT_GD.getCode(),
            OrderProduct.GD_CPM.getCode(), OrderProduct.TOP_VIEW_GD_PLUS.getCode(), OrderProduct.OGV_GD.getCode()));

    @Override
    public boolean isMatch(int orderProduct) {
        return gdProducts.contains(orderProduct);
    }

    @Override
    public void synCrm(GdOrderDto order) {

        GdOrderExtDto gdOrderExtInfo = gdOrderExtService.getGdOrderExtInfo(order.getOrderId());

        initFlyGdRangeOrderIfNecessary(gdOrderExtInfo);

        super.synCrm(order);
    }

    private void initFlyGdRangeOrderIfNecessary(GdOrderExtDto gdOrderExtInfo) {
        if (gdOrderExtService.isFlyGdRangeOrder(gdOrderExtInfo)) {
            flyGdRangeOrder.set(gdOrderExtInfo);
        } else {
            flyGdRangeOrder.remove();
        }
    }

    @Override
    public long calculateAmount(List<ScheduleDto> schedules) {

        if (isFlyGdRangeOrder()) {
            return CollectionUtils.isEmpty(schedules) ? 0 : flyGdRangeOrder.get().getOrderAmount();
        } else {
            return schedules.stream()
                    .mapToLong(schedule -> {
                                if (ShowPriorityType.PRIORITY_TYPES.contains(schedule.getShowPriority())) {
                                    return schedule.getExternalPrice();
                                }
                                if (Utils.isPositive(schedule.getPdbActualImpression())) {
                                    return (long) schedule.getCostPrice() * schedule.getPdbActualImpression();
                                }
                                return (long) schedule.getCostPrice() * schedule.getTotalImpression();
                            }
                    ).sum();
        }
    }

    @Override
    public long calculateInventory(List<ScheduleDto> schedules) {
        return schedules.stream().mapToLong(ScheduleDto::getTotalImpression).sum();
    }

    @Override
    protected Pair<Timestamp, Timestamp> getOrderTime(GdOrderDto order, List<ScheduleDto> schedules) {
        if (isFlyGdRangeOrder()) {
            return Pair.of(order.getBeginTime(), order.getEndTime());
        } else {
            return super.getOrderTime(order, schedules);
        }
    }

    @Override
    protected void finalProcess() {
        if (isFlyGdRangeOrder()) {
            flyGdRangeOrder.remove();
        }
    }

    private boolean isFlyGdRangeOrder() {
        return flyGdRangeOrder.get() != null;
    }
}
