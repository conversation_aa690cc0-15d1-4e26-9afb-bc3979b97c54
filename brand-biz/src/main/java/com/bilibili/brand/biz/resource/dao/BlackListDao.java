package com.bilibili.brand.biz.resource.dao;

import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.biz.resource.po.BlackListPo;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2016年10月25日
 */
public interface BlackListDao {

	BlackListPo getAppBlacklistByName(@Param("value") String name, @Param("type") Integer type);

	void batchSave(@Param("recordList")List<BlackListPo> records);

    int insert(
            @Param("entity") BlackListPo blackListEntity
    );

    BlackListPo load(
            @Param("blackId") Integer blackId
    );

	int update(
			@Param("entity") BlackListPo blackListPo
	);
	
	int updateStatus(
			@Param("blackId") Integer slotId,
			@Param("status") Integer status
	);
	Integer queryBlackListCountForPage(
			@Param("type")Integer type,
			@Param("status")Integer status
	);

	List<BlackListPo> queryBlackListByPage(
			@Param("type")Integer type,
			@Param("status")Integer status, 
			@Param("page")Page page
	);
	
}
