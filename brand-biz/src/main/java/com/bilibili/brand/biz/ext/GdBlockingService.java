package com.bilibili.brand.biz.ext;

import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseInsert;
import com.bilibili.bjcom.querydsl.clause.BaseUpdate;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.ext.IGdBlockingService;
import com.bilibili.brand.api.schedule.dto.BlockingInfoDetailDto;
import com.bilibili.brand.api.schedule.dto.BlockingInfoDto;
import com.bilibili.ssa.platform.biz.po.querydsl.GdBlockingInfoPo;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.biz.dao.querydsl.QGdBlockingInfo.gdBlockingInfo;

/**
 * <AUTHOR>
 * @date 2022/7/21
 */
@Service
public class GdBlockingService implements IGdBlockingService {

    @Autowired
    @Qualifier("businessAd")
    private BaseQueryFactory bqf;

    @Override
    public void saveOrUpdate(int scheduleId, int blockingType, List<BlockingInfoDetailDto> blockingInfos) {

        blockingInfos = blockingInfos.stream().distinct().collect(Collectors.toList());

        List<GdBlockingInfoPo> oldBlockingInfos = getBlockingInfos(scheduleId, blockingType);
        if (CollectionUtils.isEmpty(oldBlockingInfos)) {
            doSave(scheduleId, blockingType, blockingInfos);
        } else {
            doUpdate(scheduleId, blockingType, oldBlockingInfos, blockingInfos);
        }
    }

    @Override
    public List<BlockingInfoDto> queryBlockingInfo(int scheduleId) {
        List<GdBlockingInfoPo> blockingInfos = getBlockingInfos(scheduleId);
        if (CollectionUtils.isEmpty(blockingInfos)) {
            return new LinkedList<>();
        } else {

            Map<Integer, List<BlockingInfoDetailDto>> blockingMap = blockingInfos.stream()
                    .collect(Collectors.groupingBy(GdBlockingInfoPo::getBlockingType, Collectors.mapping(info -> {
                        BlockingInfoDetailDto blockingInfoDetailDto = new BlockingInfoDetailDto();
                        blockingInfoDetailDto.setInfo(info.getBlockingInfo());
                        return blockingInfoDetailDto;
                    }, Collectors.toList())));

            List<BlockingInfoDto> result = new LinkedList<>();
            blockingMap.forEach((blockingType,blockingInfoList)->{
                BlockingInfoDto blockingInfoDto = new BlockingInfoDto();
                blockingInfoDto.setType(blockingType);
                blockingInfoDto.setBlockingInfoList(blockingInfoList);

                result.add(blockingInfoDto);
            });
            return result;
        }
    }

    @Override
    public Map<Integer, List<BlockingInfoDto>> queryBlockingInfoMap(List<Integer> scheduleIds) {
        BooleanExpression booleanExpression = gdBlockingInfo.scheduleId.in(scheduleIds)
                .and(gdBlockingInfo.isDeleted.eq(IsDeleted.VALID.getCode()));

        List<GdBlockingInfoPo> blockingInfoDtos = bqf.selectFrom(gdBlockingInfo)
                .where(booleanExpression)
                .fetch(GdBlockingInfoPo.class);

        Map<Integer, List<GdBlockingInfoPo>> blockingMap =  blockingInfoDtos.stream()
                .collect(Collectors.groupingBy(GdBlockingInfoPo::getScheduleId));

        Map<Integer, List<BlockingInfoDto>> blockingInfoDtoMap = new HashMap<>();
        blockingMap.forEach((k, v)->{
            Map<Integer, List<BlockingInfoDetailDto>> blockingSubMap = v.stream()
                    .collect(Collectors.groupingBy(GdBlockingInfoPo::getBlockingType,
                            Collectors.mapping(info -> {
                                BlockingInfoDetailDto blockingInfoDetailDto = new BlockingInfoDetailDto();
                                blockingInfoDetailDto.setInfo(info.getBlockingInfo());
                                return blockingInfoDetailDto;
                            }, Collectors.toList())));

            List<BlockingInfoDto> result = new LinkedList<>();
            blockingSubMap.forEach((blockingType, list)->{
                BlockingInfoDto blockingInfoDto = new BlockingInfoDto();
                blockingInfoDto.setType(blockingType);
                blockingInfoDto.setBlockingInfoList(list);
                result.add(blockingInfoDto);
            });
            blockingInfoDtoMap.put(k, result);
        });

        return blockingInfoDtoMap;
    }

    private void doSave(int scheduleId, int blockingType, List<BlockingInfoDetailDto> blockingInfos) {
        List<GdBlockingInfoPo> pos = buildPos(scheduleId, blockingType, blockingInfos);
        BaseInsert baseInsert = bqf.baseInsert(gdBlockingInfo);
        baseInsert.insertBeans(pos);
    }

    private void doUpdate(int scheduleId, int blockingType, List<GdBlockingInfoPo> oldBlockingInfos,
                          List<BlockingInfoDetailDto> newBlockingInfos) {

        List<GdBlockingInfoPo> deleteInfos = oldBlockingInfos.stream()
                .filter(oldInfo -> newBlockingInfos.stream().noneMatch(newInfo -> newInfo.getInfo().equals(oldInfo.getBlockingInfo())))
                .collect(Collectors.toList());


        List<BlockingInfoDetailDto> insertInfos = newBlockingInfos.stream()
                .filter(newInfo -> oldBlockingInfos.stream().noneMatch(oldInfo -> oldInfo.getBlockingInfo().equals(newInfo.getInfo())))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(deleteInfos)) {
            deleteBlockingInfos(deleteInfos);
        }

        if (!CollectionUtils.isEmpty(insertInfos)) {
            doSave(scheduleId, blockingType, insertInfos);
        }
    }

    private List<GdBlockingInfoPo> buildPos(int scheduleId, int blockingType, List<BlockingInfoDetailDto> blockingInfos){
        return blockingInfos.stream()
                .map(info -> {
                    GdBlockingInfoPo po = new GdBlockingInfoPo();
                    po.setScheduleId(scheduleId);
                    po.setBlockingInfo(info.getInfo());
                    po.setBlockingType(blockingType);
                    return po;
                }).collect(Collectors.toList());
    }

    private void deleteBlockingInfos(List<GdBlockingInfoPo> blockingInfoPos) {
        BaseUpdate baseUpdate = bqf.baseUpdate(gdBlockingInfo);
        blockingInfoPos.forEach(info-> info.setIsDeleted(1));
        baseUpdate.updateBeans(blockingInfoPos);
    }

    private List<GdBlockingInfoPo> getBlockingInfos(int scheduleId, Integer blockingType) {
        BooleanExpression booleanExpression = gdBlockingInfo.scheduleId.eq(scheduleId)
                .and(gdBlockingInfo.isDeleted.eq(0));
        if (blockingType != null) {
            booleanExpression = booleanExpression.and(gdBlockingInfo.blockingType.eq(blockingType));
        }

        return bqf.selectFrom(gdBlockingInfo)
                .where(booleanExpression)
                .fetch(GdBlockingInfoPo.class);
    }

    private List<GdBlockingInfoPo> getBlockingInfos(int scheduleId) {
        return getBlockingInfos(scheduleId, null);
    }

}
