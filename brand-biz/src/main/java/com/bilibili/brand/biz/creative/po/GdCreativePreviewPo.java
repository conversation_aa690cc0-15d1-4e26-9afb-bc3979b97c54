package com.bilibili.brand.biz.creative.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdCreativePreviewPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 创意ID
     */
    private Long creativeId;

    /**
     * 生效时间
     */
    private Timestamp previewTime;

    /**
     * 售卖类型 21-GD 31-CPT 41-闪屏CPT 42-闪屏GD 43-topview
     */
    private Integer salesType;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 预览注册id
     */
    private Long registrationId;

    /**
     * 创意名
     */
    private String creativeName;

    private static final long serialVersionUID = 1L;
}