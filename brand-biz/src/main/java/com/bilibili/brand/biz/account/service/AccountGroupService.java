package com.bilibili.brand.biz.account.service;

import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.common.exception.AccountExceptionCode;
import com.bilibili.brand.biz.account.dao.AccAccountGroupPrivilegeMappingDao;
import com.bilibili.brand.biz.account.dao.AccountGroupDao;
import com.bilibili.brand.biz.account.dao.AccountGroupMappingDao;
import com.bilibili.brand.biz.account.po.AccAccountGroupPrivilegeMappingPo;
import com.bilibili.brand.biz.account.po.AccAccountGroupPrivilegeMappingPoExample;
import com.bilibili.brand.biz.account.po.AccountGroupMappingPo;
import com.bilibili.brand.biz.account.po.AccountGroupPo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.log.dto.OperationType;
import com.bilibili.brand.api.account.dto.AccountGroupDto;
import com.bilibili.brand.api.account.dto.AccountGroupPrivilegeDto;
import com.bilibili.brand.api.account.service.IAccountGroupService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017年2月9日
 */
@Service
public class AccountGroupService implements IAccountGroupService {

    private final static Logger LOGGER = LoggerFactory.getLogger(AccountGroupService.class);
    private final static int MAX_NAME_LENGTH = 16;
    private final static int MAX_DESC_LENGTH = 32;

    @Autowired
    private AccountGroupDao accountGroupDao;
    @Autowired
    private AccountGroupMappingDao accountGroupMappingDao;
    @Autowired
    private AccAccountGroupPrivilegeMappingDao accountGroupPrivilegeMappingDao;

    @Override
    public AccountGroupDto load(Integer accountGroupId) throws ServiceException {
        AccountGroupPo po = accountGroupDao.load(accountGroupId);
        if (po == null) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_NO_EXIST);
        }

        AccAccountGroupPrivilegeMappingPoExample pExample = new AccAccountGroupPrivilegeMappingPoExample();
        pExample.or().andAccountGroupIdEqualTo(accountGroupId);
        List<AccAccountGroupPrivilegeMappingPo> ppos = accountGroupPrivilegeMappingDao.selectByExample(pExample);
        AccountGroupDto dto = po2Dto(po);

        if(!CollectionUtils.isEmpty(ppos)) {
        	AccountGroupPrivilegeDto privilege = AccountGroupPrivilegeDto
        										.builder()
        										.accountGroupId(dto.getId())
        										.gdOrderNeedContractNumber(ppos.get(0).getGdOrderNeedContractNumber())
        										.build();
        	dto.setPrivilege(privilege);
        }
        else {
        	dto.setPrivilege(AccountGroupPrivilegeDto.builder().gdOrderNeedContractNumber(0).build());
        }

        return dto;
    }

    @Override
    public List<AccountGroupDto> getAccountGroupByStatus(Integer status) throws ServiceException {
        List<AccountGroupPo> pos = accountGroupDao.getByStatus(status);
        List<AccountGroupDto> dtos =  poList2DtoList(pos);

        List<Integer> accountGroupIds = dtos
        								.stream()
        								.map(dto -> dto.getId())
        								.collect(Collectors.toList());

        AccAccountGroupPrivilegeMappingPoExample pExample = new AccAccountGroupPrivilegeMappingPoExample();
        pExample.or().andAccountGroupIdIn(accountGroupIds);
        List<AccAccountGroupPrivilegeMappingPo> ppos = accountGroupPrivilegeMappingDao.selectByExample(pExample);
        Map<Integer, AccAccountGroupPrivilegeMappingPo> ppoMap = ppos
        															.stream()
        															.collect(Collectors.toMap(AccAccountGroupPrivilegeMappingPo::getAccountGroupId, po -> po));
        dtos.forEach(dto -> {
        	if(ppoMap.containsKey(dto.getId())) {
        		AccountGroupPrivilegeDto privilege = AccountGroupPrivilegeDto
						.builder()
						.accountGroupId(dto.getId())
						.gdOrderNeedContractNumber(ppoMap.get(dto.getId()).getGdOrderNeedContractNumber())
						.build();

        		dto.setPrivilege(privilege);
        	}
        });

        return dtos;
    }

    @Override
    public List<AccountGroupDto> getValidAccountGroupsInIds(List<Integer> accountGroupIds) {
        if (CollectionUtils.isEmpty(accountGroupIds)) {
            return Collections.emptyList();
        }
        List<AccountGroupPo> pos = accountGroupDao.getValidInIds(accountGroupIds);
        Map<Integer, AccountGroupPrivilegeDto> privilegeMap = getAccountGroupPrivilegeMapInAccountGroupIds(accountGroupIds);

        return poList2DtoList(pos, privilegeMap);
    }

    @Override
    public Map<Integer, String> getValidAccountGroupId2NameMapInIds(List<Integer> accountGroupIds) {
        List<AccountGroupDto> accountGroupDtos = getValidAccountGroupsInIds(accountGroupIds);
        return accountGroupDtos.stream().collect(Collectors.toMap(AccountGroupDto::getId, AccountGroupDto::getName));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int create(Operator operator, AccountGroupDto dto) throws ServiceException {
        LOGGER.info("AccountGroupService.create param[operator-{}, AccountGroupDto-{}].", operator, dto);
        validateOperator(operator);
        validateAccountGroupForCreate(dto);
        AccountGroupPo po = dto2Po(dto);
        po.setStatus(SwitchStatus.STARTED.getCode());
        int result = accountGroupDao.insert(po);
        if (result == 0) {
            LOGGER.info("AccountGroupDao.insert entity-{} failed.", po);
            throw new ServiceException(AccountExceptionCode.FAIL_OPERATION);
        }

        if(dto.getPrivilege() != null) {
        	AccountGroupPrivilegeDto privilege = dto.getPrivilege();
        	privilege.setAccountGroupId(po.getId());
        	createAccountGroupPrivilege(privilege, operator);
        }

        return po.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(Operator operator, AccountGroupDto dto) throws ServiceException {
        LOGGER.info("AccountGroupService.update param[operator-{}, AccountGroupDto-{}].", operator, dto);
        validateOperator(operator);
        validateAccountGroupForUpdate(dto);
        AccountGroupPo po = dto2Po(dto);
        int result = accountGroupDao.update(po);
        if (result != 1) {
            LOGGER.info("AccountGroupDao.update entity-{} failed.", po);
            throw new ServiceException(AccountExceptionCode.FAIL_OPERATION);
        }

        if(dto.getPrivilege() != null) {
        	AccountGroupPrivilegeDto agp = dto.getPrivilege();
        	agp.setAccountGroupId(dto.getId());
        	saveOrUpdatePrivilege(agp, operator);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateStatus(Operator operator, Integer accountGroupId, Integer status)
            throws ServiceException {
        LOGGER.info("AccountGroupService.updateStatus param[operator-{}, accountGroupId-{}, status-{}].", operator, accountGroupId, status);
        validateOperator(operator);
        if (accountGroupId == null || SwitchStatus.getByCode(status) == null || SwitchStatus.getByCode(status) == SwitchStatus.DELETE) {
            LOGGER.info("AccountGroupService.updateStatus param illegal.");
            throw new ServiceException(AccountExceptionCode.ILLEGAL_PARAM);
        }
        AccountGroupDto dto = this.load(accountGroupId);
        if (dto.getStatus() == status) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_STATUS_NO_ALERT);
        }
        if (SwitchStatus.STOPED.equals(SwitchStatus.getByCode(status))) {
            accountGroupMappingDao.deleteByAccountGroupId(accountGroupId);
        }
        int result = accountGroupDao.updateStatus(accountGroupId, status);
        if (result != 1) {
            LOGGER.info("AccountGroupDao.updateStatus param[accountGroupId-{}, status-{}] failed.", accountGroupId, status);
            throw new ServiceException(AccountExceptionCode.FAIL_OPERATION);
        }
        return result;
    }

    private void validateCommonAccountGroup(AccountGroupDto dto) throws ServiceException {
        if (dto == null) {
            throw new ServiceException(AccountExceptionCode.REQUIRED_PARAM);
        }
        if (Strings.isNullOrEmpty(StringUtils.trimWhitespace(dto.getName()))) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_NAME_IS_NULL);
        }
        if (dto.getName().length() > MAX_NAME_LENGTH) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_NAME_TOO_LONG);
        }
        if (Strings.nullToEmpty(dto.getDescription()).length() > MAX_DESC_LENGTH) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_DESC_TOO_LONG);
        }
    }

    private void validateAccountGroupForCreate(AccountGroupDto dto) throws ServiceException {
        this.validateCommonAccountGroup(dto);
        if (accountGroupDao.getByName(StringUtils.trimWhitespace(dto.getName()))!=null) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_NAME_EXIST);
        }
    }

    private void validateAccountGroupForUpdate(AccountGroupDto dto) throws ServiceException {
        this.validateCommonAccountGroup(dto);
        AccountGroupDto accountGroupDto = this.load(dto.getId());
        AccountGroupPo accountGroupPo = accountGroupDao.getByName(StringUtils.trimWhitespace(dto.getName()));
        if (accountGroupPo != null && !accountGroupDto.getId().equals(accountGroupPo.getId())) {
            throw new ServiceException(AccountExceptionCode.ACCOUNT_GROUP_NAME_EXIST);
        }
    }


    private AccountGroupPo dto2Po(AccountGroupDto dto){
        AccountGroupPo po = AccountGroupPo.builder().build();
        BeanUtils.copyProperties(dto, po);
        po.setName(StringUtils.trimWhitespace(dto.getName()));
        return po;
    }

    private AccountGroupDto po2Dto(AccountGroupPo po) {
        AccountGroupDto dto = AccountGroupDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private AccountGroupDto po2Dto(AccountGroupPo po, AccountGroupPrivilegeDto privilege) {
        AccountGroupDto dto = AccountGroupDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        dto.setPrivilege(privilege);

        return dto;
    }

    private List<AccountGroupDto> poList2DtoList(List<AccountGroupPo> pos) {
        List<AccountGroupDto> dtos = Collections.emptyList();
        if (!CollectionUtils.isEmpty(pos)) {
            dtos = pos.stream().map(po -> this.po2Dto(po)).collect(Collectors.toList());
        }
        return dtos;
    }

    private List<AccountGroupDto> poList2DtoList(List<AccountGroupPo> pos, Map<Integer, AccountGroupPrivilegeDto> privilegeMap) {
    	if(CollectionUtils.isEmpty(privilegeMap)) {
    		return poList2DtoList(pos);
    	}

        List<AccountGroupDto> dtos = Collections.emptyList();
        if (!CollectionUtils.isEmpty(pos)) {
            dtos = pos.stream().map(po -> this.po2Dto(po, privilegeMap.get(po.getId()))).collect(Collectors.toList());
        }
        return dtos;
    }

    private void validateOperator(Operator operator) throws ServiceException {
        if (Strings.isNullOrEmpty(operator.getOperatorName())
                || operator.getOperatorType() != OperatorType.OPERATING_PERSONNEL) {
            throw new ServiceException(AccountExceptionCode.ILLEGAL_PARAM);
        }
    }

    @Override
    public Map<Integer, AccountGroupDto> getValidAccountGroupMapInIds(List<Integer> accountGroupIds) {
        List<AccountGroupDto> accountGroupDtos = this.getValidAccountGroupsInIds(accountGroupIds);
        Map<Integer, AccountGroupDto> accountGroupMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(accountGroupDtos)) {
            accountGroupMap = accountGroupDtos.stream().collect(Collectors.toMap(AccountGroupDto::getId, Function.identity()));
        }

        return accountGroupMap;
    }

    @Override
    public Map<Integer, List<AccountGroupDto>> getValidAccountGroupMapByAccountIds(List<Integer> accountIds) {
        Assert.notEmpty(accountIds);
        Map<Integer, List<AccountGroupDto>> accountGroupMap = Collections.emptyMap();
        List<AccountGroupMappingPo> accountGroupMappingPos = accountGroupMappingDao.getAccountGroupMappingByAccountIds(accountIds);
        if (!CollectionUtils.isEmpty(accountGroupMappingPos)) {
            List<Integer> accountGroupIds = accountGroupMappingPos.stream().map(AccountGroupMappingPo::getAccountGroupId).collect(Collectors.toList());
            Map<Integer, AccountGroupDto> accountGroupDtoMap = this.getValidAccountGroupMapInIds(accountGroupIds);
            accountGroupMap = Maps.newHashMap();
            List<AccountGroupDto> accountGroupDtoList;
            for(AccountGroupMappingPo mappingPo: accountGroupMappingPos){
                if (accountGroupDtoMap.containsKey(mappingPo.getAccountGroupId())) {
                    if (accountGroupMap.containsKey(mappingPo.getAccountId())) {
                        accountGroupDtoList = accountGroupMap.get(mappingPo.getAccountId());
                        accountGroupDtoList.add(accountGroupDtoMap.get(mappingPo.getAccountGroupId()));
                        accountGroupMap.put(mappingPo.getAccountId(), accountGroupDtoList);
                    }else {
                        accountGroupDtoList = Lists.newArrayList(accountGroupDtoMap.get(mappingPo.getAccountGroupId()));
                        accountGroupMap.put(mappingPo.getAccountId(), accountGroupDtoList);
                    }
                }
            }
        }
        return accountGroupMap;
    }

    @Override
    public int createAccountGroupPrivilege(AccountGroupPrivilegeDto dto, Operator operator) {
    	LOGGER.info("createAccountGroupPrivilege AccountGroupPrivilegeDto:{}, operator:{}", dto, operator);
    	
    	Assert.notNull(dto.getAccountGroupId(), "用户组id不能为空");
    	AccAccountGroupPrivilegeMappingPo po = privilegeDtoToPo(dto);
    	accountGroupPrivilegeMappingDao.insertSelective(po);
    	
    	return po.getId();
    }
    
    @Override
    public void saveOrUpdatePrivilege(AccountGroupPrivilegeDto dto, Operator operator) {
    	LOGGER.info("saveOrUpdatePrivilege AccountGroupPrivilegeDto:{}, operator:{}", dto, operator);
    	Assert.notNull(dto.getAccountGroupId(), "用户组id不能为空");
    	
    	AccAccountGroupPrivilegeMappingPoExample example = new AccAccountGroupPrivilegeMappingPoExample();
    	example.or().andAccountGroupIdEqualTo(dto.getAccountGroupId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
    	
    	List<AccAccountGroupPrivilegeMappingPo> pos = accountGroupPrivilegeMappingDao.selectByExample(example);
    	
    	if(CollectionUtils.isEmpty(pos)) {
    		createAccountGroupPrivilege(dto, operator);
    	}
    	else {
    		AccAccountGroupPrivilegeMappingPo po = privilegeDtoToPo(dto);
    		AccAccountGroupPrivilegeMappingPo oldPo = pos.get(0);
    		po.setId(oldPo.getId());
    		
    		accountGroupPrivilegeMappingDao.updateByPrimaryKeySelective(po);
    	}
    }
    
    private AccAccountGroupPrivilegeMappingPo privilegeDtoToPo(AccountGroupPrivilegeDto dto) {
    	AccAccountGroupPrivilegeMappingPo po = new AccAccountGroupPrivilegeMappingPo();
    	BeanUtils.copyProperties(dto, po);
    	return po;
    }
    
    @Override
    public Map<Integer, AccountGroupPrivilegeDto> getAccountGroupPrivilegeMapInAccountGroupIds(List<Integer> accountGroupIds) {
    	AccAccountGroupPrivilegeMappingPoExample example = new AccAccountGroupPrivilegeMappingPoExample();
    	example.or().andAccountGroupIdIn(accountGroupIds);
    	
    	List<AccAccountGroupPrivilegeMappingPo> pos = accountGroupPrivilegeMappingDao.selectByExample(example);
    	
    	return pos
    			.stream()
    			.map(this::privilegePoToDto)
    			.collect(Collectors.toMap(AccountGroupPrivilegeDto::getAccountGroupId, dto -> dto));
    }

	private AccountGroupPrivilegeDto privilegePoToDto(AccAccountGroupPrivilegeMappingPo po) {
		AccountGroupPrivilegeDto dto = new AccountGroupPrivilegeDto();
		BeanUtils.copyProperties(po, dto);
		return dto;
	}
}
