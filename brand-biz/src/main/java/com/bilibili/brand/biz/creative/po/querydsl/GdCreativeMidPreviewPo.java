package com.bilibili.brand.biz.creative.po.querydsl;

import javax.annotation.Generated;

/**
 * GdCreativeMidPreviewPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class GdCreativeMidPreviewPo {

    private Long creativeId;

    private java.sql.Timestamp ctime;

    private Integer id;

    private Integer isDeleted;

    private Long mid;

    private java.sql.Timestamp mtime;

    private java.sql.Timestamp previewTime;

    private Integer salesType;

    public Long getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Long creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public java.sql.Timestamp getPreviewTime() {
        return previewTime;
    }

    public void setPreviewTime(java.sql.Timestamp previewTime) {
        this.previewTime = previewTime;
    }

    public Integer getSalesType() {
        return salesType;
    }

    public void setSalesType(Integer salesType) {
        this.salesType = salesType;
    }

    @Override
    public String toString() {
         return "creativeId = " + creativeId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mid = " + mid + ", mtime = " + mtime + ", previewTime = " + previewTime + ", salesType = " + salesType;
    }

}

