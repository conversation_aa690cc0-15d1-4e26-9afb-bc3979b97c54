package com.bilibili.brand.biz.inventory.impl.location;

import com.bilibili.cpt.platform.common.LocationType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/3/10 16:58
 */
@Slf4j
@Service
public class DefaultLocationStockService extends AbstractLocationStockService {
    /**
     * 资源位类型，该处返回的OTHER是指默认，即默认支持所有资源位，如果某资源位需要特殊处理，
     * 则继承{@link AbstractLocationStockService}类重写该方法
     */
    @Override
    public LocationType getLocationType() {
        return LocationType.DEFAULT;
    }
}
