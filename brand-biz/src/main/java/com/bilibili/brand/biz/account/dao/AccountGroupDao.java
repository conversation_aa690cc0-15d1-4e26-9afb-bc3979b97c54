package com.bilibili.brand.biz.account.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.account.po.AccountGroupPo;

/**
 * <AUTHOR>
 * @date 2017年2月9日
 */
public interface AccountGroupDao {

    AccountGroupPo load(@Param("id") Integer id);
    
    AccountGroupPo getByName(@Param("name") String name);

    List<AccountGroupPo> getByStatus(@Param("status") Integer status);

    List<AccountGroupPo> getValidInIds(@Param("ids") List<Integer> accountGroupIds);

    int insert(@Param("entity") AccountGroupPo AccountGroupPo);

    int update(@Param("entity") AccountGroupPo po);

    int updateStatus(@Param("id") Integer id, @Param("status") Integer status);

}
