package com.bilibili.brand.biz.log.bean;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/30.
 */

import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(gdLogFlag = GdLogFlag.ORDER)
public class OrderLogBean implements Serializable {
    private static final long serialVersionUID = -4386575865244148215L;

    @LogProperty("订单id")
    private Integer id;

    @LogProperty("订单名称")
    private String orderName;

    @LogProperty("资源类型")
    private String resourceType;

    @LogProperty("合同号")
    private Long crmContractNumber;

    @LogProperty("邀约广告-视频ID")
    private Long invitationVideoId;

    @LogProperty("投放开始时间")
    private String adStartTime;

    @LogProperty("投放结束时间")
    private String adEndTime;
}
