package com.bilibili.brand.biz.material;

import cn.hutool.core.io.file.FileNameUtil;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.launch.dto.BfsFile;
import com.bilibili.brand.api.launch.dto.ImageResolution;
import com.bilibili.brand.api.material.IWebpService;
import com.bilibili.brand.api.material.bo.WebpProcessResultBo;
import com.bilibili.brand.api.webp.WebpGenerateRequestDto;
import com.bilibili.brand.biz.bfs.BrandBfsService;
import com.bilibili.brand.biz.cache.RedisDao;
import com.bilibili.brand.biz.proto.WebpUtil;
import com.bilibili.cpt.platform.util.ZipUtil;
import com.bilibili.enums.BrandBfsCategoryEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.exec.DefaultExecuteResultHandler;
import org.apache.commons.exec.ExecuteException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WebpService implements IWebpService {

    @Autowired
    private BrandBfsService brandBfsService;

    @Autowired
    private RedisDao redisDao;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    @Qualifier("completableFutureExecutorWithDecorator")
    private Executor threadPool;

    @Override
    public WebpProcessResultBo generateWebpByImagesZip(MultipartFile multipartFile, WebpGenerateRequestDto webpGenerateRequestDto) {
        String dealSeq = webpGenerateRequestDto.getDealSeq();
        if (StringUtils.isNotEmpty(dealSeq)) {
            WebpProcessResultBo webpProcessResultBo = redisDao.getObject(dealSeq, WebpProcessResultBo.class);
            if (Objects.isNull(webpProcessResultBo)) {
                webpProcessResultBo = WebpProcessResultBo.builder()
                        .dealSeq(dealSeq)
                        .dealStatus(-1)
                        .dealMessage("已过期")
                        .build();
            }
            return webpProcessResultBo;
        }

        String prefix = FileNameUtil.getPrefix(multipartFile.getOriginalFilename());
        final String finalDealSeq = "webp_generate-" + snowflakeIdWorker.nextId();
        threadPool.execute(() -> {
            WebpProcessResultBo webpProcessResultBo;

            // 首先解压缩文件到data/temp/{finalDealSeq}目录
            List<String> sortedFilePathList;
            try {
                Utils.createDirIfNotExist("data/temp/" + finalDealSeq);
                File unzipped = ZipUtil.unZip(multipartFile.getInputStream(), "data/temp/" + finalDealSeq);
                File[] files = unzipped.listFiles();
                Assert.notNull(files, "压缩文件格式错误，请严格按照格式来压缩序列帧文件，所有图片放在压缩包第一层");
                sortedFilePathList = Arrays.stream(files)
                        .filter(Objects::nonNull)
                        .filter(file -> !file.isDirectory())
                        .sorted(Comparator.comparing(File::getName))
                        .map(File::getPath)
                        .collect(Collectors.toList());
                webpProcessResultBo = WebpProcessResultBo.builder()
                        .dealSeq(finalDealSeq)
                        .dealStatus(0)
                        .dealMessage("解压缩完毕，等待合成webp")
                        .build();
                redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
            } catch (Exception e) {
                log.error("解压缩失败，异常原因：", e);
                webpProcessResultBo = WebpProcessResultBo.builder()
                        .dealSeq(finalDealSeq)
                        .dealStatus(-1)
                        .dealMessage("解压缩失败，异常原因：" + e.getMessage())
                        .build();
                redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
                throw new RuntimeException(e);
            }

            // 合成webp
            try {
                // 回调函数
                DefaultExecuteResultHandler defaultExecuteResultHandler = new DefaultExecuteResultHandler() {
                    @SneakyThrows
                    @Override
                    public void onProcessComplete(int exitValue) {
                        super.onProcessComplete(exitValue);
                        log.info("onProcessComplete，exitValue={}", exitValue);

                        WebpProcessResultBo webpProcessResultBo = WebpProcessResultBo.builder()
                                .dealSeq(finalDealSeq)
                                .dealStatus(0)
                                .dealMessage("合成webp成功，等待上传webp")
                                .build();
                        redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);

                        File file = new File(finalDealSeq + ".webp");
                        BfsUploadResult bfsUploadResult = brandBfsService.upload(BrandBfsCategoryEnum.IMG.getName(), file);
                        ImageResolution imageResolution = WebpUtil.decodeWebp(file);
                        webpProcessResultBo = WebpProcessResultBo.builder()
                                .dealSeq(finalDealSeq)
                                .dealStatus(1)
                                .dealMessage("上传完毕")
                                .name(prefix + ".webp")
                                .url(bfsUploadResult.getUrl())
                                .md5(bfsUploadResult.getMd5())
                                .width(imageResolution.getWidth())
                                .height(imageResolution.getHeight())
                                .build();
                        redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
                    }

                    @Override
                    public void onProcessFailed(ExecuteException e) {
                        super.onProcessFailed(e);
                        log.error("onProcessFailed", e);

                        WebpProcessResultBo webpProcessResultBo = WebpProcessResultBo.builder()
                                .dealSeq(finalDealSeq)
                                .dealStatus(-1)
                                .dealMessage("合成webp失败，请稍后重试，异常原因：" + e.getMessage())
                                .build();
                        redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
                    }
                };

                // 真正执行合成操作
                WebpUtil.generateWebp(sortedFilePathList, finalDealSeq + ".webp",
                        webpGenerateRequestDto.getEnableMixed(), webpGenerateRequestDto.getQualityRatio(), webpGenerateRequestDto.getFrameRate(),
                        defaultExecuteResultHandler);
            } catch (Exception e) {
                log.error("合成webp文件失败，请稍后重试，异常原因：", e);
                webpProcessResultBo = WebpProcessResultBo.builder()
                        .dealSeq(finalDealSeq)
                        .dealStatus(-1)
                        .dealMessage("合成webp文件失败，请稍后重试，异常原因：" + e.getMessage())
                        .build();
                redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
                throw new RuntimeException(e);
            }
        });

        WebpProcessResultBo webpProcessResultBo = WebpProcessResultBo.builder()
                .dealSeq(finalDealSeq)
                .dealStatus(0)
                .dealMessage("等待处理中，请稍后")
                .build();
        redisDao.setString(finalDealSeq, GsonUtils.toJson(webpProcessResultBo), 300, true);
        return webpProcessResultBo;
    }

    @Override
    public WebpProcessResultBo uploadWebpDirectly(MultipartFile multipartFile) throws IOException, ServiceException {
        String originalFilename = multipartFile.getOriginalFilename();
        Assert.isTrue(StringUtils.endsWithIgnoreCase(originalFilename, ".webp"), "只支持上传webp格式的文件");

        final String finalDealSeq = "webp_upload-" + snowflakeIdWorker.nextId();
        File convFile = new File(finalDealSeq + ".webp");
        multipartFile.transferTo(convFile);
        BfsFile bfsFile = BfsFile.builder()
                .size(multipartFile.getSize())
                .mimeType(multipartFile.getContentType())
                .fileName(originalFilename)
                .bytes(multipartFile.getBytes())
                .inputStream(multipartFile.getInputStream())
                .build();
        BfsUploadResult result = brandBfsService.upload(BrandBfsCategoryEnum.IMG.getName(), bfsFile.getFileName(), bfsFile.getBytes());
        ImageResolution imageResolution = WebpUtil.decodeWebp(convFile);
        return WebpProcessResultBo.builder()
                .name(originalFilename)
                .url(result.getUrl())
                .md5(result.getMd5())
                .width(imageResolution.getWidth())
                .height(imageResolution.getHeight())
                .build();
    }
}
