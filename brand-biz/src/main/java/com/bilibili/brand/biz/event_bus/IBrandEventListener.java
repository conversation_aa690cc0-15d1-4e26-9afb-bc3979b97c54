package com.bilibili.brand.biz.event_bus;

import java.util.EventListener;

/**
 * <AUTHOR>
 * @date 2023/9/7 14:51
 */
public interface IBrandEventListener<E extends BrandEvent> extends EventListener {
    /**
     * Determine whether the event can be matched and consumed.
     * If so, return true; otherwise, return false
     */
    boolean match(E event);

    /**
     * If the {@link #match(BrandEvent)} method returns true, the event will be posted to this method.
     */
    void onEvent(E event);
}
