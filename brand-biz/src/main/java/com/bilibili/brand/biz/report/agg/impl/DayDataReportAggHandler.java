package com.bilibili.brand.biz.report.agg.impl;

import com.bilibili.brand.biz.report.dto.BaseReportDto;
import com.bilibili.brand.biz.report.dto.DataReportQueryDto;
import com.bilibili.brand.biz.report.enums.AggDimensionEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class DayDataReportAggHandler<Q extends BaseReportDto> extends AbstractDataReportAggHandler<Q> {

    @Override
    public List<Q> calculateSum(DataReportQueryDto queryDto, List<Q> data) {
        if (CollectionUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        // 默认跟据底表查询到的数据就是分天的，故无需进行处理
        return data;
    }

    @Override
    public Integer supportAggDimension() {
        return AggDimensionEnum.DAY.getCode();
    }
}
