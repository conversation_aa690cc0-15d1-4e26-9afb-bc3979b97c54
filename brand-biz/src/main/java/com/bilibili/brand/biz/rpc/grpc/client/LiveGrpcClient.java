package com.bilibili.brand.biz.rpc.grpc.client;

import com.bapis.live.xroom.*;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.live.dto.LiveRoomDto;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 直播
 * https://git.bilibili.co/bapis/bapis/-/blob/master/live/xroom/api.proto
 *
 * <AUTHOR>
 * @date 2024/1/3 20:22
 */
@Slf4j
@Component
public class LiveGrpcClient {
    @RPCClient("live.xroom")
    private RoomGrpc.RoomBlockingStub roomBlockingStub;

    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_TRACKID = "trackid";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_TRACKID = "__FROMTRACKID__";


    /**
     * 查询直播间信息
     */
    public LiveRoomDto queryLiveRoom(Long roomId) {
        if (!Utils.isPositive(roomId)) {
            return null;
        }
        return this.queryLiveRoom(Lists.newArrayList(roomId)).get(roomId);
    }

    /**
     * 查询直播间信息
     */
    public Map<Long, LiveRoomDto> queryLiveRoom(List<Long> roomIdList) {
        Map<Long, LiveRoomDto> result = Maps.newHashMap();
        roomIdList = Objects.isNull(roomIdList) ? null : roomIdList.stream().filter(Utils::isPositive).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roomIdList)) {
            return result;
        }
        try {
            RoomIDsReq request = RoomIDsReq.newBuilder()
                    .addAllRoomIds(roomIdList)
                    //要获取的房间信息维度 status:状态相关 show:展示相关 area:分区相关 anchor:主播相关 pendants: 获取类型角标
                    //show必传，否则无法返回标题和封面
                    //status必传，否则无法返回直播状态
                    //area必传，否则无法返回分区信息
                    .addAllAttrs(Lists.newArrayList("show", "status", "area"))
                    //switch=1，否则无法返回地址
                    .setPlayurl(PlayURLParams.newBuilder().setSwitch(1).build())
                    .build();
            log.info("[LiveGrpcClient] queryLiveRoom request={}", JsonFormat.printer().print(request));
            RoomIDsInfosResp infoReply = this.roomBlockingStub.getMultiple(request);
            log.info("[LiveGrpcClient] queryLiveRoom response={}", JsonFormat.printer().print(infoReply));
            Map<Long, Infos> listMap = infoReply.getListMap();
            Map<Long, LivePlayUrlData> playUrlMap = infoReply.getPlayUrlMap();
            listMap.forEach((roomId, info) -> result.put(roomId, buildLiveRoom(roomId, info, playUrlMap.get(roomId))));
            return result;
        } catch (Exception e) {
            log.info("[LiveGrpcClient] queryLiveRoom error, roomId={}", roomIdList, e);
            throw new RuntimeException(e);
        }
    }


    private LiveRoomDto buildLiveRoom(Long roomId, Infos infos, LivePlayUrlData playUrl) {
        LiveRoomDto.LiveRoomDtoBuilder builder = LiveRoomDto.builder().roomId(roomId);

        Optional.ofNullable(infos)
                .ifPresent(info -> {
                    RoomShowInfo show = info.getShow();
                    RoomStatusInfo status = info.getStatus();
                    RoomAreaInfo area = info.getArea();
                    builder.uid(info.getUid())
                            .title(show.getTitle())
                            .cover(show.getCover())
                            //直播间状态 0未开播，1直播中；2轮播中；
                            .status(Math.toIntExact(status.getLiveStatus()))
                            //隐藏状态 0不隐藏，1隐藏
                            .hiddenStatus(Math.toIntExact(status.getHiddenStatus()))
                            .areaId(Math.toIntExact(area.getAreaId()))
                            .areaName(area.getAreaName())
                            .areaParentId(Math.toIntExact(area.getParentAreaId()))
                            .areaParentName(area.getParentAreaName());
                });

        Optional.ofNullable(playUrl).ifPresent(pu -> {
            if (StringUtils.hasText(pu.getLink())) {
                // 跳转链接中增加主站trackid宏，trackid=__FROMTRACKID__
                String fLink = UriComponentsBuilder.fromUriString(pu.getLink())
                        .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_TRACKID, LIVE_ROOM_HIDDEN_PARAM_VALUE_TRACKID)
                        .build(false)
                        .toString();
                builder.link(fLink);
            }
        });
        return builder.build();
    }
}
