package com.bilibili.brand.biz.resource.converter;

import com.bapis.ad.brand.resource.BusinessAtmosphere;
import com.bilibili.brand.biz.databus.dto.SyBusinessAtmosphereMsg;
import com.bilibili.brand.biz.resource.dto.BusinessAtmosphereDto;
import com.bilibili.brand.util.TimeUtil;
import com.google.common.collect.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/24 11:28
 */
@Mapper(imports = {Lists.class, TimeUtil.class})
public interface BusinessAtmosphereConverter {
    BusinessAtmosphereConverter MAPPER = Mappers.getMapper(BusinessAtmosphereConverter.class);

    @Mapping(target = "seasonBeginTime", expression = "java(dto.getSeasonBeginTime().getTime())")
    @Mapping(target = "controlEndTime", expression = "java(dto.getControlEndTime().getTime())")
    BusinessAtmosphere toBusinessAtmosphere(BusinessAtmosphereDto dto);

    List<BusinessAtmosphere> toBusinessAtmosphere(List<BusinessAtmosphereDto> dtos);

    //固定1和2
    @Mapping(target = "consumerTypes", expression = "java(Lists.newArrayList(1,2))")
    @Mapping(target = "messageTime", expression = "java(System.currentTimeMillis())")
    //固定为5：品牌
    @Mapping(target = "firstBusinessType", constant = "5")
    @Mapping(target = "isTopCustomer", source = "isTop")
    @Mapping(target = "categoryFirstName", source = "firstCategoryName")
    @Mapping(target = "categorySecondName", source = "secondCategoryName")
    @Mapping(target = "controlEndTime", expression = "java(TimeUtil.timestampToIsoTimeStr(dto.getControlEndTime()))")
    SyBusinessAtmosphereMsg toSyBusinessAtmosphereMsg(BusinessAtmosphereDto dto);

}
