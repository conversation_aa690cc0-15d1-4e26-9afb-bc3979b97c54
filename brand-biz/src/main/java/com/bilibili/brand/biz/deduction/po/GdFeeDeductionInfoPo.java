package com.bilibili.brand.biz.deduction.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdFeeDeductionInfoPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 消费日期
     */
    private Timestamp groupTime;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * crm流水号
     */
    private String crmSerialNumber;

    /**
     * 消费(单位: 元)
     */
    private BigDecimal cost;

    /**
     * 状态 0-未扣费 1-已扣费 2-作废
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}