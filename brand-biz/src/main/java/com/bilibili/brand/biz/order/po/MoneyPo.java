package com.bilibili.brand.biz.order.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * CREATE TABLE `fc_order_money` (
 * `id` INT(11) UNSIGNED Auto_INCREMENT NOT NULL ,
 * `order_id` INT(11) NOT NULL DEFAULT 0 COMMENT '订单id',
 * `account_id` INT(11) NOT NULL DEFAULT 0 COMMENT '账户id',
 * `income_cash` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '已转现金（单位：分）',
 * `income_red_packet` BIGINT(20) NOT NULL DEFAULT 0 COMMENT '已转红包（单位：分）',
 * `status` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '状态 0 待审核 1 审核拒绝 2 审核通过',
 * `commont` VARCHAR(512) NOT NULL DEFAULT '' COMMENT'备注',
 * `operator_id` INT(11) NOT NULL DEFAULT 0 COMMENT '操作人id',
 * `version` INT(11) NOT NULL DEFAULT 0 COMMENT '版本',
 * `ctime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
 * `mtime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 * `is_deleted` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '软删除，0是有效，1是删除',
 * PRIMARY KEY (`id`),
 * INDEX `ix_order_id` (`order_id` ASC),
 * INDEX `ix_account_id` (`account_id` ASC),
 * INDEX `ix_ctime` (`ctime` ASC),
 * INDEX `ix_mtime` (`mtime` ASC)
 * )
 * ENGINE = InnoDB
 * DEFAULT CHARACTER SET = utf8
 * COMMENT = '订单入账表';
 * Created by fanwenbin on 2016/10/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MoneyPo {
    private Integer id;
    private Integer orderId;
    private Integer accountId;
    private Long incomeCash;
    private Long incomeRedPacket;
    private Integer status;
    private String commont;
    private String operator;
    private Integer version;
    private Timestamp ctime;
}
