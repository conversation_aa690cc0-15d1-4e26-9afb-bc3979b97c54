package com.bilibili.brand.biz.creative.service;

import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.brand.api.creative.dto.QrInfoDto;
import com.bilibili.brand.biz.creative.po.querydsl.GdCreativeQrInfoPo;
import com.querydsl.core.BooleanBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.brand.biz.creative.dao.querydsl.QGdCreativeQrInfo.gdCreativeQrInfo;

/**
 * <AUTHOR>
 * @date 2022/4/2
 */
@Service
public class GdCreativeQrInfoService {

    @Autowired
    @Qualifier("businessAd")
    private BaseQueryFactory bqf;

    public Map<Long,QrInfoDto> queryQrInfos(List<Long> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyMap();
        }
        List<GdCreativeQrInfoPo> res = bqf.selectFrom(gdCreativeQrInfo)
                .where(gdCreativeQrInfo.creativeId.in(creativeIds)
                        .and(gdCreativeQrInfo.isDeleted.eq(0)))
                .fetch(GdCreativeQrInfoPo.class);
        if (CollectionUtils.isEmpty(res)) {
            return new HashMap<>();
        }
        return res.stream().
                collect(Collectors.toMap(GdCreativeQrInfoPo::getCreativeId,
                        po -> QrInfoDto.builder()
                                .qrAnimationUrl(po.getQrAnimationUrl())
                                .qrFrameUrl(po.getQrFrameUrl())
                                .build(),
                        (o1, o2) -> o1));
    }

    public QrInfoDto queryQrInfo(long creativeId) {
        return bqf.selectFrom(gdCreativeQrInfo)
                .where(gdCreativeQrInfo.creativeId.eq(creativeId)
                        .and(gdCreativeQrInfo.isDeleted.eq(0)))
                .fetchFirst(QrInfoDto.class);
    }

    public void saveOrUpdateQrInfo(long creativeId, QrInfoDto qrInfo) {

        if (qrInfo == null) {
            return;
        }

        if (isExisted(creativeId)) {
            update(creativeId, qrInfo);
        }else {
            insert(creativeId, qrInfo);
        }
    }

    private void insert(long creativeId, QrInfoDto qrInfo) {
        bqf.insert(gdCreativeQrInfo)
                .setIfNotNull(gdCreativeQrInfo.creativeId, creativeId)
                .setIfNotNull(gdCreativeQrInfo.qrFrameUrl, qrInfo.getQrFrameUrl())
                .setIfNotNull(gdCreativeQrInfo.qrAnimationUrl, qrInfo.getQrAnimationUrl())
                .execute();
    }

    private void update(long creativeId, QrInfoDto qrInfo) {
        bqf.update(gdCreativeQrInfo)
                .set(gdCreativeQrInfo.qrFrameUrl, qrInfo.getQrFrameUrl())
                .set(gdCreativeQrInfo.qrAnimationUrl, qrInfo.getQrAnimationUrl())
                .where(gdCreativeQrInfo.creativeId.eq(creativeId)
                        .and(gdCreativeQrInfo.isDeleted.eq(0)))
                .execute();
    }

    private boolean isExisted(long creativeId){
        long count = bqf.selectFrom(gdCreativeQrInfo)
                .where(
                        new BooleanBuilder(gdCreativeQrInfo.creativeId.eq(creativeId)
                                .and(gdCreativeQrInfo.isDeleted.eq(0)))
                )
                .fetchCount();
        return count > 0;
    }
}
