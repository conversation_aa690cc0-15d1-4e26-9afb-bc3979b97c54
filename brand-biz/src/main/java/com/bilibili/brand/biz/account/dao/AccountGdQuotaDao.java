/** 
* <AUTHOR> 
* @date  2017年6月13日
*/ 

package com.bilibili.brand.biz.account.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.biz.account.po.AccountGdQuotaPo;

public interface AccountGdQuotaDao {

	List<AccountGdQuotaPo> getList(@Param("page") Page page);
	int getAllCount();
	AccountGdQuotaPo getById(@Param("id")Integer id);
	int insert(AccountGdQuotaPo po);
	void update(AccountGdQuotaPo po);
	void delete(@Param("id")Integer id);
	AccountGdQuotaPo getByAccountId(@Param("accountId")Integer accountId);
	List<AccountGdQuotaPo> getInAccountIds(@Param("accountIds")List<Integer> accountIds);
}
