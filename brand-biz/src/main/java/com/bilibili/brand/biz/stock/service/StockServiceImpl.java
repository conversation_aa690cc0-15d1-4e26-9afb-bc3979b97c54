package com.bilibili.brand.biz.stock.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.AreaSubType;
import com.bilibili.brand.api.common.exception.LaunchExceptionCode;
import com.bilibili.brand.api.common.exception.StockExceptionCode;
import com.bilibili.brand.api.dmp.IScheduleCrowdPackService;
import com.bilibili.brand.api.resource.target_lau.dto.ResTargetItemDto;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.BestGroupStockParamDto;
import com.bilibili.brand.api.schedule.dto.ScheduleTargetDto;
import com.bilibili.brand.api.stock.dto.*;
import com.bilibili.brand.api.stock.dto.fly.FlyStockPriceDto;
import com.bilibili.brand.api.stock.dto.fly.FlyStockTargetInfo;
import com.bilibili.brand.api.stock.dto.fly.FlyTargetingInfo;
import com.bilibili.brand.api.stock.service.IStockService;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.resource.service.ResTargetItemService;
import com.bilibili.brand.biz.schedule.service.ScheduleStockService;
import com.bilibili.brand.biz.schedule.service.frequency.ResourceService;
import com.bilibili.brand.biz.stock.RequestStatusType;
import com.bilibili.brand.biz.stock.dao.RequestRecordDao;
import com.bilibili.brand.biz.stock.dao.StockAllocationDao;
import com.bilibili.brand.biz.stock.delegate.StockDelegate;
import com.bilibili.brand.biz.stock.po.RequesRecordPo;
import com.bilibili.brand.biz.stock.po.StockAllocationPo;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.GdType;
import com.bilibili.cpt.platform.common.SplitDaysFlagEnum;
import com.google.common.base.Throwables;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.internal.util.Assert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StockServiceImpl implements IStockService {
    private final static Logger LOGGER = LoggerFactory.getLogger(StockServiceImpl.class);

    @Autowired
    StockDelegate stockDelegate;

    @Autowired
    private RequestRecordDao requestRecordDao;

    @Autowired
    private StockAllocationDao stockAllocationDao;

    @Value("${gd.schedule.crow.pack.frequent:3}")
    private Integer CROWED_PACK_FREQUENCY;

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ScheduleStockService scheduleStockService;

    @Autowired
    private ResTargetItemService resTargetItemService;
//    @Autowired
//    private IResourceTypeService resourceTypeService;
    @Autowired
    private ResourceService resourceService;
    @Autowired
    private IScheduleCrowdPackService crowdPackService;

    @Override
    public QueryStockResDto queryStock(QueryStockDto queryStockDto) throws ServiceException {
        throw new ServiceException("已暂停服务");
//        queryStockDto.setResourceType(resourceTypeService.getResourceType(queryStockDto.getSlotGroupId(),
//                queryStockDto.getTemplateId()));
//        QueryStockResDto stock = stockDelegate.queryStock(queryStockDto);
//        LOGGER.info("stockDelegate.queryStock dto {}, stock {}", queryStockDto, stock);
//        if (null == queryStockDto.getFrequencyLimit()) {
//            return stock;
//        }
//
//        Assert.isTrue(queryStockDto.getFrequencyLimit() >= 0, "频次不能小于0");
//        if(!CollectionUtils.isEmpty(queryStockDto.getExcludeCrowdPackIds())){
//            long crowedPackCount = crowdPackService.getCrowedPackCount(queryStockDto);
//            Map<LocalDate, Long> dayImpression = stock.getDayImpression();
//            AtomicLong totalImpression = new AtomicLong();
//            dayImpression.forEach((k,v)->{
//                if(0 == crowedPackCount){
//                    v = 0L;
//                }else if(v >= crowedPackCount * CROWED_PACK_FREQUENCY /1000){
//                   v = crowedPackCount * CROWED_PACK_FREQUENCY / 1000;
//                }
//                dayImpression.put(k, v);
//                totalImpression.addAndGet(v);
//            });
//            stock.setTotalImpression(totalImpression.get());
//        }
//
//        return calculateStock(queryStockDto, stock);
    }

    private QueryStockResDto calculateStock(QueryStockDto queryStockDto, QueryStockResDto stock) {

        long totalStock = 0;

        Map<LocalDate, Long> dayImpression = stock.getDayImpression();
        for (Map.Entry<LocalDate, Long> entry : dayImpression.entrySet()){
            if(entry.getValue() == null){
                entry.setValue(0L);
            }else {

                LocalDate date = entry.getKey();

                int maxFrequency = getMaxFrequency(queryStockDto, date);

                long thisDayStock = entry.getValue();
                if (queryStockDto.getFrequencyLimit() < maxFrequency) {
                    thisDayStock = thisDayStock * queryStockDto.getFrequencyLimit() / maxFrequency;
                    entry.setValue(thisDayStock);
                }

                totalStock += thisDayStock;
            }
        }
        return QueryStockResDto.builder()
                .totalImpression(totalStock)
                .dayImpression(dayImpression)
                .build();
    }

    private int getMaxFrequency(QueryStockDto queryStockDto, LocalDate date) {

        ResourceInfoBo resourceInfoBo = ResourceInfoBo.builder()
                .slotGroupId(queryStockDto.getSlotGroupId())
                .templateId(queryStockDto.getTemplateId())
                .queryDate(date)
                .build();

        return resourceService.getMaxFrequency(resourceInfoBo);
    }


    @Deprecated
    @Override
    public void updateStock(UpdateStockDto updateStockDto) throws ServiceException {
//        updateStockDto.setResourceType(resourceTypeService.getResourceType(updateStockDto.getSlotGroupId(),
//                0));
//        String methodParam = JSONObject.toJSONString(updateStockDto);
//        RequesRecordPo requesRecordPo = RequesRecordPo.builder().method_name("updateStock")
//                .method_parameter(methodParam.length() > 1048 ? methodParam.substring(0, 1047) : methodParam)
//                .request_status(RequestStatusType.SUCCESS.getCode()).request_count(1).build();
//        requestRecordDao.insertRequestRecord(requesRecordPo);
//        try {
//            stockDelegate.updateStock(updateStockDto);
//        } catch (SystemException e) {
//            LOGGER.error("stockDelegate.updateStock.error", e);
//            throw new ServiceException(e.getCode(), e.getMessage());
//        }
    }

    @Deprecated
    @Override
    public void lockStock(LockStockDto lockStockDto) throws ServiceException {
//        lockStockDto.setResourceType(resourceTypeService.getResourceType(lockStockDto.getSlotGroupId(),
//                0));
//        String methodParam = JSONObject.toJSONString(lockStockDto);
//        RequesRecordPo requesRecordPo = RequesRecordPo.builder().method_name("lockStock")
//                .method_parameter(methodParam.length() > 1048 ? methodParam.substring(0, 1047) : methodParam)
//                .request_status(RequestStatusType.SUCCESS.getCode()).request_count(1).build();
//        requestRecordDao.insertRequestRecord(requesRecordPo);
//        try {
//            stockDelegate.lockStock(lockStockDto);
//        } catch (Exception e) {
//            LOGGER.error("stockDelegate.lockStock.error", e);
//            throw new ServiceException(e.getMessage());
//        }
    }

    @Override
    public void releaseStock(ReleaseStockDto releaseStockDto) throws ServiceException {
        String methodParam = JSONObject.toJSONString(releaseStockDto);
        RequesRecordPo requesRecordPo = RequesRecordPo.builder().method_name("releaseStock")
                .method_parameter(methodParam.length() > 1048 ? methodParam.substring(0, 1047) : methodParam)
                .request_status(RequestStatusType.SUCCESS.getCode()).request_count(1).build();
        requestRecordDao.insertRequestRecord(requesRecordPo);
        try {
            stockDelegate.releaseStock(releaseStockDto);
        } catch (Exception e) {
            LOGGER.error("stockDelegate.releaseStock.error", e);
            throw new ServiceException(Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public List<StockAllocationDto> queryAllocationByScheduleIdGroupByTime(Integer scheduleId)
            throws ServiceException {
        if (scheduleId == null) {
            throw new ServiceException(StockExceptionCode.REQUIRED_PARAM);
        }
        List<StockAllocationPo> pos = stockAllocationDao.queryAllocationByScheduleId(scheduleId);
        List<StockAllocationDto> dtos = Collections.emptyList();
        if (!CollectionUtils.isEmpty(pos)) {
            Map<Timestamp, Integer> timeCpmCountMap = pos.stream().collect(Collectors.groupingBy(StockAllocationPo::getLaunchDay,
                    Collectors.reducing(0, StockAllocationPo::getCpmCount, Integer::sum)));
            Set<Timestamp> timStampSet = timeCpmCountMap.keySet();
            dtos = timStampSet.stream().map(t -> StockAllocationDto.builder().launchDay(t).cpmCount(timeCpmCountMap.get(t)).build()).collect(Collectors.toList());
            dtos.sort(Comparator.comparing(StockAllocationDto::getLaunchDay));
        }
        return dtos;
    }

    @Override
    public Map<Integer, Integer> queryHasLaunchedCPMByScheduleIds(List<Integer> scheduleIds) throws ServiceException {
        LOGGER.info("queryHasLaunchedCPMByScheduleIds scheduleIds:{}", scheduleIds);
        List<StockAllocationPo> pos;

        if (CollectionUtils.isEmpty(scheduleIds)) {
            LOGGER.error("queryHasLaunchedCPMByScheduleIds scheduleIds is empty");
            throw new ServiceException(StockExceptionCode.REQUIRED_PARAM);
        }

        pos = stockAllocationDao.queryAllocationByScheduleIdsAndStartTimeAndEndTime(scheduleIds, null, Utils.getTimestamp2String(Utils.getToday()));

        if (CollectionUtils.isEmpty(pos)) {
            return scheduleIds.stream().collect(Collectors.toMap(id -> id, id -> 0));
        }

        Map<Integer, Integer> result = pos
                .stream()
                .collect(
                        Collectors.groupingBy(
                                StockAllocationPo::getScheduleId,
                                Collectors.summingInt(StockAllocationPo::getCpmCount)
                        )
                );

        if (scheduleIds.size() != result.keySet().size()) {
            scheduleIds.forEach(id -> {
                if (!result.containsKey(id)) {
                    result.put(id, 0);
                }
            });
        }

        return result;
    }

    @Override
    public Map<Integer, List<StockAllocationDto>> queryAllocationByScheduleIds(List<Integer> scheduleIds) {
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return new HashMap<>();
        }
        List<StockAllocationPo> allocationPos = stockAllocationDao.queryAllocationByScheduleIds(scheduleIds);
        if (CollectionUtils.isEmpty(allocationPos)) {
            return new HashMap<>();
        }
        return allocationPos.stream()
                .map(this::convertPo2Dto)
                .collect(Collectors.groupingBy(StockAllocationDto::getScheduleId, Collectors.mapping(Function.identity(), Collectors.toList())));
    }

    private StockAllocationDto convertPo2Dto(StockAllocationPo po) {
        return StockAllocationDto.builder()
                .scheduleId(po.getScheduleId())
                .cpmCount(po.getCpmCount())
                .launchDay(po.getLaunchDay())
                .build();
    }

    @Override
    public FlyStockPriceDto flyQueryStock(FlyStockTargetInfo flyStockTargetInfo) throws ServiceException {
        throw new ServiceException("已暂停服务");
//        //获取处理序列
//        String dealSeq = flyStockTargetInfo.getDealSeq();
//
//        //查询缓存如果命中则直接返回
//        if(!StringUtils.isEmpty(dealSeq)){
//            FlyStockPriceDto stockPriceDto = redisService.getFlyValue(dealSeq);
//            if(stockPriceDto != null) {
//                return stockPriceDto;
//            }
//            return FlyStockPriceDto.builder().isDealFinish(false).dealSeq(dealSeq).build();
//        }else {
//            dealSeq = String.valueOf(snowflakeIdWorker.nextId());
//        }
//
//        //异步处理库存查询
//
//        LOGGER.info("fly flyStockTargetInfo="+ JSON.toJSONString(flyStockTargetInfo));
//
//        List<TargetRule> targetRules = this.buildFlyTargetRuleList(flyStockTargetInfo
//                .getTargetingInfo());
//        BestGroupStockParamDto paramDto = this.getStockParamDto(flyStockTargetInfo);
//        scheduleStockService.flyStockUpdateAsync(paramDto, targetRules, dealSeq);
//
//        return FlyStockPriceDto.builder().dealSeq(dealSeq).isDealFinish(false).build();
    }

    public List<TargetRule> buildFlyTargetRuleList(FlyTargetingInfo targetingInfo) {
        if (targetingInfo == null) {
            return Collections.emptyList();
        }

        List<TargetRule> targetRuleList = new ArrayList<>();
        for (TargetType targetType : TargetType.values()) {
            TargetRule targetRule = new TargetRule();
            switch (targetType) {
                case AREA:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getArea());
                    targetRuleList.add(targetRule);
                    break;
                case GENDER:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getGender());
                    targetRuleList.add(targetRule);
                    break;
                case AGE:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getAge());
                    targetRuleList.add(targetRule);
                    break;
                case INLINE_SALES_TYPE:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getInlineSalesType());
                    targetRuleList.add(targetRule);
                    break;
                default:
                    break;
            }
        }
        return targetRuleList;
    }

    public BestGroupStockParamDto getStockParamDto(FlyStockTargetInfo stockTargetInfo) throws ServiceException {
        List<Pair<Timestamp, Timestamp>> timeInfo = new ArrayList<>();
        if(stockTargetInfo.isToday()){
            timeInfo.add(Pair.of(TimeUtil.localDateTimeToTimestamp(Utils.getToday().toLocalDateTime()
                    .plusHours(stockTargetInfo.getHour())), Utils.getEndOfDay(Utils.getToday())));
        }else {
            stockTargetInfo.getScheduleDate()
                    .forEach(t -> timeInfo.add(Pair
                            .of(Utils.getBeginOfDay(t), Utils.getEndOfDay(t))));
        }

        Map<Pair<Timestamp, Timestamp>, List<TargetRule>> targetMap = new HashMap<>();
        Map<Timestamp, Pair<Timestamp, Timestamp>> timeInfoMap = new HashMap<>();
        List<TargetRule> targetRules = buildTargetRuleList(stockTargetInfo.getTargetingInfo());
        timeInfo.forEach(t->{
            List<TargetRule> temp = new ArrayList<>(targetRules);
            List<Integer> hours = new ArrayList<>();
            for(int i = t.getFirst().toLocalDateTime().getHour();
                i < t.getSecond().toLocalDateTime().getHour() + 1; i++){
                hours.add(i);
            }
            if(hours.size() != 24) {
                temp.add(TargetRule.builder().ruleType(TargetType.HOUR.getCode())
                        .valueIds(hours).build());
            }
            targetMap.put(t, temp);
            timeInfoMap.put(Utils.getBeginOfDay(t.getFirst()), t);
        });

        return BestGroupStockParamDto.builder()
                .requestId(UUID.randomUUID().toString())
                .accountId(stockTargetInfo.getAccountId())
                .templateId(stockTargetInfo.getTemplateId())
                .platformIds(stockTargetInfo.getPlatformIds())
                .scheduleTargetDtos(this.getTargetingCondition(stockTargetInfo.getTargetingInfo().getAge(),
                        stockTargetInfo.getTargetingInfo().getArea(),
                        stockTargetInfo.getTargetingInfo().getGender(),
                        stockTargetInfo.getTargetingInfo().getInlineSalesType()))
                .dates(stockTargetInfo.getScheduleDate())
                .isToday(stockTargetInfo.isToday())
                .hour(stockTargetInfo.getHour())
                .creativeStyle(null)
                .splitDaysFlag(SplitDaysFlagEnum.OPEN.getCode())
                .frequencyLimit(1)
                .frequencyUnit(1)
                .areaGroupId(stockTargetInfo.getAreaGroupId())
                .scheduleIds(stockTargetInfo.getScheduleIds())
                .gdType(GdType.FLY.getCode())
                .targetMap(targetMap)
                .timeInfo(timeInfo)
                .timeInfoMap(timeInfoMap)
                .fistBrush(false)
                .build();
    }

    public List<ScheduleTargetDto> getTargetingCondition(List<Integer> ages, List<Integer> areas,
                                                         List<Integer> genders,List<Integer> inlineSalesType) throws ServiceException {
        List<ScheduleTargetDto> scheduleTargetList = new ArrayList<>(6);

        if (!CollectionUtils.isEmpty(areas)) {
            List<ResTargetItemDto> targetItems = resTargetItemService.getTargetItemsInIds(areas);
            if (CollectionUtils.isEmpty(targetItems)) {
                throw new ServiceException(LaunchExceptionCode.GD_AREA_TARGET_RULE_PARAM_INVALID);
            } else {
                Map<Integer, List<ResTargetItemDto>> targetSubTypeMap = targetItems.stream()
                        .collect(Collectors.groupingBy(ResTargetItemDto::getSubType));
                if (targetSubTypeMap.containsKey(AreaSubType.PROVINCE.getCode())
                        && targetSubTypeMap.containsKey(AreaSubType.CITY.getCode())) {
                    areas = targetSubTypeMap.get(AreaSubType.CITY.getCode()).stream().map(ResTargetItemDto::getId)
                            .collect(Collectors.toList());
                }
            }
            scheduleTargetList.add(
                    ScheduleTargetDto.builder().targetType(TargetType.AREA.getCode()).targetItemIds(areas).build());
        }
        if (!CollectionUtils.isEmpty(ages)) {
            scheduleTargetList.add(ScheduleTargetDto.builder().targetType(TargetType.AGE.getCode())
                    .targetItemIds(ages).build());
        }
        if (!CollectionUtils.isEmpty(genders)) {
            scheduleTargetList.add(ScheduleTargetDto.builder().targetType(TargetType.GENDER.getCode())
                    .targetItemIds(genders).build());
        }
        if (!CollectionUtils.isEmpty(inlineSalesType)) {
            scheduleTargetList.add(ScheduleTargetDto.builder().targetType(TargetType.INLINE_SALES_TYPE.getCode())
                    .targetItemIds(inlineSalesType).build());
        }
        return scheduleTargetList;
    }

    public List<TargetRule> buildTargetRuleList(FlyTargetingInfo targetingInfo) {
        if (targetingInfo == null) {
            return Collections.emptyList();
        }
        List<TargetRule> targetRuleList = new ArrayList<>();
        for (TargetType targetType : TargetType.values()) {
            TargetRule targetRule = new TargetRule();
            switch (targetType) {
                case AREA:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getArea());
                    targetRuleList.add(targetRule);
                    break;
                case GENDER:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getGender());
                    targetRuleList.add(targetRule);
                    break;
                case OS:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getOs());
                    targetRuleList.add(targetRule);
                    break;
                case AGE:
                    targetRule.setRuleType(targetType.getCode());
                    targetRule.setValueIds(targetingInfo.getAge());
                    targetRuleList.add(targetRule);
                    break;
                default:
                    break;
            }
        }
        return targetRuleList;
    }

}
