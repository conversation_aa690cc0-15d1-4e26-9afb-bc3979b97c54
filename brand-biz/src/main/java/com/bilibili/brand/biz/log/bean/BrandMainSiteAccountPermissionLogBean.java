package com.bilibili.brand.biz.log.bean;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/10/24 18:55
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(gdLogFlag = GdLogFlag.BRAND_MAIN_SITE_ACCOUNT_PERMISSION)
public class BrandMainSiteAccountPermissionLogBean implements Serializable {
    private static final long serialVersionUID = -1979755311164829494L;
    @LogProperty("mid")
    private Long mid;
    @LogProperty("用户昵称")
    private String userName;
    @LogProperty("权限")
    private String permission;
}
