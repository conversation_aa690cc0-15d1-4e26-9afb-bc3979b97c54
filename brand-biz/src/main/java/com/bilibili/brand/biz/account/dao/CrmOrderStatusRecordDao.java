package com.bilibili.brand.biz.account.dao;

import com.bilibili.brand.biz.account.po.CrmOrderStatusRecordPo;
import com.bilibili.brand.biz.account.po.CrmOrderStatusRecordPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface CrmOrderStatusRecordDao {
    long countByExample(CrmOrderStatusRecordPoExample example);

    int deleteByExample(CrmOrderStatusRecordPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmOrderStatusRecordPo record);

    int insertBatch(List<CrmOrderStatusRecordPo> records);

    int insertUpdateBatch(List<CrmOrderStatusRecordPo> records);

    int insert(CrmOrderStatusRecordPo record);

    int insertUpdateSelective(CrmOrderStatusRecordPo record);

    int insertSelective(CrmOrderStatusRecordPo record);

    List<CrmOrderStatusRecordPo> selectByExample(CrmOrderStatusRecordPoExample example);

    CrmOrderStatusRecordPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmOrderStatusRecordPo record, @Param("example") CrmOrderStatusRecordPoExample example);

    int updateByExample(@Param("record") CrmOrderStatusRecordPo record, @Param("example") CrmOrderStatusRecordPoExample example);

    int updateByPrimaryKeySelective(CrmOrderStatusRecordPo record);

    int updateByPrimaryKey(CrmOrderStatusRecordPo record);
}