package com.bilibili.brand.biz.resource.ssa.archive;

import com.bapis.archive.service.ArcReply;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.common.enums.SsaArchiveTranscodingStatusEnum;
import com.bilibili.brand.api.resource.ssa.archive.ISsaArchiveTranscodingService;
import com.bilibili.brand.api.resource.ssa.archive.bo.ArchiveTranscodingBo;
import com.bilibili.brand.api.resource.ssa.archive.bo.ArchiveTranscodingCallbackResultBo;
import com.bilibili.brand.api.resource.ssa.archive.bo.TranscodeArchiveBo;
import com.bilibili.brand.biz.resource.converter.SsaArchiveConverter;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.dto.SendTranscodeArchiveRes;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.bilibili.brand.biz.rpc.http.TranscodeArchiveHttpService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenArchiveInfoService;
import com.bilibili.ssa.platform.biz.dao.SsaArchiveTranscodingDao;
import com.bilibili.ssa.platform.biz.po.SsaArchiveTranscodingPo;
import com.bilibili.ssa.platform.biz.po.SsaArchiveTranscodingPoExample;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenArchiveInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/27
 */
@Slf4j
@Service
public class SsaArchiveTranscodingService implements ISsaArchiveTranscodingService {

    @Autowired
    private SsaArchiveTranscodingDao ssaArchiveTranscodingDao;
    @Autowired
    private TranscodeArchiveHttpService transcodeArchiveHttpService;
    @Autowired
    private ArchiveGrpcClient archiveGrpcClient;
    @Autowired
    private SsaSplashScreenArchiveInfoService ssaSplashScreenArchiveInfoService;

    @Value("${ssa.archive.check.autoplay:false}")
    private Boolean checkArchiveAutoPlay;

    private final int successCode = 0;


    @Override
    public void transcodeArchive(TranscodeArchiveBo transcodeArchiveBo) {

        checkArchiveAutoPlay(transcodeArchiveBo.getAid());

        checkAndUpdateCidIfNecessary(transcodeArchiveBo.getAid(), transcodeArchiveBo.getCid());

        SendTranscodeArchiveRes videoCloudResponse = transcodeArchiveHttpService.sendTranscodeReq(transcodeArchiveBo.getCid());
        Assert.isTrue(videoCloudResponse.getCode() == successCode, "发送转码请求到视频云发生错误");

        ssaArchiveTranscodingDao.insertSelective(SsaArchiveConverter.MAPPER.toPo(transcodeArchiveBo, videoCloudResponse.getData().getFlowid()));
    }

    private void checkArchiveAutoPlay(long aid) {
        if (checkArchiveAutoPlay) {
            ArchiveInfoBo archiveInfoBo = archiveGrpcClient.queryArchiveInfo(aid);
            Assert.isTrue(archiveInfoBo.getAutoplay() == 1, "该稿件不支持自动播放");
        }
    }

    private void checkAndUpdateCidIfNecessary(long aid, long cid) {
        SsaArchiveTranscodingPoExample example = new SsaArchiveTranscodingPoExample();
        example.or()
                .andAidEqualTo(aid)
                .andIsDeletedEqualTo(0);
        List<SsaArchiveTranscodingPo> archives = ssaArchiveTranscodingDao.selectByExample(example);

        if (CollectionUtils.isEmpty(archives)) {
            return;
        }

        Assert.isTrue(archives.size() == 1, "数据异常，一个稿件只能有一条有效数据");

        SsaArchiveTranscodingPo archiveTranscoding = archives.get(0);
        Assert.isTrue(archiveTranscoding.getCid() != cid, "该稿件已经存在转码成功的数据，无需再次转码");

        deleteOldCid(archiveTranscoding.getId());

    }

    private void deleteOldCid(int id) {
        ssaArchiveTranscodingDao.updateByPrimaryKeySelective(SsaArchiveTranscodingPo.builder()
                .id(id)
                .isDeleted(1)
                .build());
    }

    @Override
    public void processArchiveTranscodingResult(ArchiveTranscodingCallbackResultBo callbackResultBo) {

        SsaArchiveTranscodingPo updatePo;
        SsaArchiveTranscodingPoExample example = new SsaArchiveTranscodingPoExample();
        example.or()
                .andCidEqualTo(callbackResultBo.getData().getCid())
                .andIsDeletedEqualTo(0);
        if (callbackResultBo.getCode() == successCode) {
            updatePo = SsaArchiveConverter.MAPPER.toPo(callbackResultBo.getData());
            updatePo.setProcessStatus(SsaArchiveTranscodingStatusEnum.SUCCESS.getCode());
        }else {
            updatePo = new SsaArchiveTranscodingPo();
            updatePo.setProcessStatus(SsaArchiveTranscodingStatusEnum.FAIL.getCode());
        }
        ssaArchiveTranscodingDao.updateByExampleSelective(updatePo, example);
    }

    @Override
    public PageResult<ArchiveTranscodingBo> queryTranscodingArchives(String name, Long aid, Integer status, Integer page, Integer pageSize) {
        SsaArchiveTranscodingPoExample example = new SsaArchiveTranscodingPoExample();
        SsaArchiveTranscodingPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(0);
        if (aid != null) {
            criteria.andAidEqualTo(aid);
        }
        if (StringUtils.isNotBlank(name)) {
            criteria.andVideoNameLike(name + "%");
        }
        if (status != null) {
            criteria.andProcessStatusEqualTo(status);
        }
        example.setOffset((page - 1) * pageSize);
        example.setLimit(pageSize);
        example.setOrderByClause("mtime,id desc");

        long count = ssaArchiveTranscodingDao.countByExample(example);
        if (count == 0) {
            return new PageResult<>();
        }

        List<SsaArchiveTranscodingPo> ssaArchiveTranscodingPos = ssaArchiveTranscodingDao.selectByExample(example);

        return new PageResult<>(((int) count), SsaArchiveConverter.MAPPER.toBos(ssaArchiveTranscodingPos));
    }

    @Override
    public ArchiveTranscodingBo queryTranscodingArchive(Integer id) {

        SsaArchiveTranscodingPo po = ssaArchiveTranscodingDao.selectByPrimaryKey(id);

        return SsaArchiveConverter.MAPPER.toBo(po);

    }

    @Override
    public String deleteTranscodingArchive(Integer id, boolean confirmed) {

        if (!confirmed) {
            List<Integer> ssaCreativeIds = ssaSplashScreenArchiveInfoService.queryRelateSsaCreativeIds(id);
            boolean hasRelateSsaCreatives = !CollectionUtils.isEmpty(ssaCreativeIds);
            if (hasRelateSsaCreatives) {
                return String.format("该稿件目前在[%s]创意中投放，请确认是否删除！", ssaCreativeIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
        }

        SsaArchiveTranscodingPo po = new SsaArchiveTranscodingPo();
        po.setId(id);
        po.setIsDeleted(1);

        ssaArchiveTranscodingDao.updateByPrimaryKeySelective(po);
        return "";
    }
}
