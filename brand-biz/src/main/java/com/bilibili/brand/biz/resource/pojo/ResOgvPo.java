package com.bilibili.brand.biz.resource.pojo;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResOgvPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * season id
     */
    private Long seasonId;

    /**
     * 资源可投放开始时间
     */
    private Timestamp beginTime;

    /**
     * 资源可投放结束时间
     */
    private Timestamp endTime;

    /**
     * 资源级别,S,A,B,C
     */
    private String resLevel;

    /**
     * 资源状态，1：上架，2：下架
     */
    private Integer resStatus;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 可投放的平台id，多个使用英文逗号分隔，0:web,1:iPhone,2:Android,10:ott
     */
    private String platformId;

    private static final long serialVersionUID = 1L;
}