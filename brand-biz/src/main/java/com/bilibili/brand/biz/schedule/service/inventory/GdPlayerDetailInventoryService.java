package com.bilibili.brand.biz.schedule.service.inventory;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.booking.dto.BookingItemDto;
import com.bilibili.brand.api.booking.dto.BookingQueryDto;
import com.bilibili.brand.api.booking.dto.BookingTimeQueryDto;
import com.bilibili.brand.api.booking.dto.CptScheduleSplitTimeDayDto;
import com.bilibili.brand.api.booking.dto.CptScheduleSplitTimeDto;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.schedule.bo.information_flow.QueryInventoryBo;
import com.bilibili.brand.api.schedule.dto.NewScheduleDto;
import com.bilibili.brand.api.stock.dto.ResourceInfoBo;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.brand.biz.schedule.service.frequency.ResourceService;
import com.bilibili.brand.common.Constant;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.CptBookingStatus;
import com.bilibili.cpt.platform.common.ResourceType;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.enums.GdDisplayModeEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 协议:
 * 复用枚举值 scene = 2: 含义从 underframe 改为播放页场景(包含框下 & 相关推荐), 投放端依旧路由到 goblin-underframe 应用
 * 三个定向:
 * 新增-1300 资源位-相关推荐-ios
 * 新增-1301 资源位-相关推荐-安卓
 * 复用-1190 首刷定向(当日首次进入)
 */
@Slf4j
@Service
public class GdPlayerDetailInventoryService extends GdBaseInventoryService {

    @Autowired
    private IResourceBookingService resourceBookingService;

    @Autowired
    private QueryScheduleService queryScheduleService;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ConfigCenter configCenter;

    @Override
    protected void setValueBeforeQuery(QueryInventoryBo queryInventoryBo) {
        /**
         * 解决双端定向时，平台定向丢失问题
         * @see com.bilibili.brand.biz.utils.TargetUtil#processOsTarget(int, java.util.List, java.util.List, boolean)
         */
        queryInventoryBo.setNeedOsSourceWhenCommonLaunch(true);
    }

    @Override
    protected void setValueBeforeProcessTargets(NewScheduleDto scheduleDto) {
        /**
         * 解决Story双端定向时，平台定向丢失问题
         * @see com.bilibili.brand.biz.utils.TargetUtil#processOsTarget(int, java.util.List, java.util.List, boolean)
         */
        scheduleDto.setNeedOsSourceWhenCommonLaunch(true);
    }

    /**
     * 播放详情页与CPT库存互斥逻辑，库存折扣
     *
     * @see com.bilibili.brand.biz.schedule.service.ScheduleStockService#getSplitDaysStock4Brand
     */
    @Override
    protected <Detail extends GdInventoryDetailBo> void postProcessEachInventoryInfo(QueryInventoryBo queryInventoryBo, Detail inventoryDetail) {
        // 剩余库存优先进行频控处理
        int inventories = inventoryDetail.getInventories();
        int maxFrequency = resourceService.getMaxFrequency(ResourceInfoBo.builder()
                .templateId(queryInventoryBo.getTemplateId())
                .build());
        if (queryInventoryBo.getFrequencyLimit() < maxFrequency) {
            inventories = inventories * queryInventoryBo.getFrequencyLimit() / maxFrequency;
        }

        // 资源位互斥逻辑
        List<Integer> sourceIdList = queryInventoryBo.getPlatformIds()
                .stream()
                .filter(platform -> !Objects.equals(platform, PlatformType.IPAD.getCode()))
                .flatMap(item -> Constant.PLATFORM_2_PLAYER_DETAIL_SOURCE.get(item).stream())
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sourceIdList)) {
            boolean isFirstBrush = Objects.equals(queryInventoryBo.getDisplayMode(), GdDisplayModeEnum.FIRST_BRUSH.getCode());
            Timestamp beginTime = TimeUtil.toTimestamp(inventoryDetail.getBeginTime());
            Timestamp endTime = TimeUtil.toTimestamp(inventoryDetail.getEndTime());
            List<Timestamp> realDates = TimeUtils.getEachDay(beginTime, endTime);

            // 查询CPT分时预约
            BookingTimeQueryDto bookingTimeQueryDto = BookingTimeQueryDto.builder()
                    .sourceIds(sourceIdList)
                    .groupDates(realDates)
                    .excludesPastDate(false)
                    .status(CptBookingStatus.SCHEDULE_LOCKED_LIST)
                    .build();
            List<CptScheduleSplitTimeDto> splitTimeList = this.resourceBookingService.querySplitTimeList(bookingTimeQueryDto, Operator.SYSTEM);
            // key1: sourceId, key2: day, value: hour count
            Map<Integer, Map<String, Integer>> sourceDayHourCountMap = splitTimeList.stream()
                    .collect(Collectors.toMap(
                            CptScheduleSplitTimeDto::getSourceId,
                            splitTime -> splitTime.getDayDtoList().stream()
                                    .collect(Collectors.toMap(
                                            CptScheduleSplitTimeDayDto::getDay,
                                            std -> std.getHourDtoList().size()
                                    ))
                    ));

            // 查询CPT天预约
            BookingQueryDto bookingQueryDto = BookingQueryDto.builder()
                    .sourceIds(sourceIdList)
                    .groupDates(realDates)
                    .status(CptBookingStatus.SCHEDULE_LOCKED_LIST)
                    .timeType(1)
                    .build();
            List<BookingItemDto> bookingItemDtoList = this.resourceBookingService.queryResourceBooking(bookingQueryDto);
            // key1: sourceId, key2: day, value: day count
            Map<Integer, Map<String, Integer>> sourceDayCountMap = bookingItemDtoList.stream()
                    .collect(Collectors.groupingBy(
                            BookingItemDto::getSourceId,
                            Collectors.toMap(
                                    bookingItem -> TimeUtil.timestampToIsoDateStr(bookingItem.getGroupDate()),
                                    bookingItem -> 1,
                                    Integer::sum,
                                    HashMap::new
                            )
                    ));

            // 计算dayTotalHalfHourCountMap
            Map<String, Integer> dayTotalHalfHourCountMap = new HashMap<>();
            for (Timestamp day : realDates) {
                for (Integer sourceId : sourceIdList) {
                    String key = TimeUtil.timestampToIsoDateStr(day);
                    Map<String, Integer> dayHourCountMap = sourceDayHourCountMap.getOrDefault(sourceId, new HashMap<>());
                    Map<String, Integer> dayCountMap = sourceDayCountMap.getOrDefault(sourceId, new HashMap<>());

                    Integer dayHourCount = dayHourCountMap.getOrDefault(key, 0);
                    Integer dayCount = dayCountMap.getOrDefault(key, 0);

                    if (isFirstBrush) {
                        Assert.isTrue(dayHourCount <= 6, String.format("%s已经存在分时CPT排期（分时时长超过3小时）", key));
                        Assert.isTrue(dayCount == 0, String.format("%s已经存在CPT排期", key));
                    }

                    // 折换成半小时
                    int curDayHourCount = (dayCount * 48) + dayHourCount;
                    Integer oldDayHourCount = dayTotalHalfHourCountMap.getOrDefault(key, 0);
                    // dayTotalHalfHourCountMap保存day总的分时数最大值
                    dayTotalHalfHourCountMap.put(key, Math.max(oldDayHourCount, curDayHourCount));
                }
            }

            // 播放详情页半小时对应的cpm数量，如果是单端，则折算库存
            int halfHourCpm = Math.toIntExact(this.configCenter.getGdPlusConfig().getPlayerDetailHalfHourCpm());
            if (queryInventoryBo.getPlatformIds() != null && queryInventoryBo.getPlatformIds().size() == 1) {
                halfHourCpm = halfHourCpm / 2;
            }
            for (Timestamp day : realDates) {
                String key = TimeUtil.timestampToIsoDateStr(day);
                Integer dayTotalHalfHours = dayTotalHalfHourCountMap.getOrDefault(key, 0);
                inventories = Math.max(0, inventories - dayTotalHalfHours * halfHourCpm);
            }
        }

        // set最终剩余库存
        inventoryDetail.setInventories(inventories);
    }

    @Override
    public int getResourceType() {
        return ResourceType.PLAYER_DETAIL.getCode();
    }
}
