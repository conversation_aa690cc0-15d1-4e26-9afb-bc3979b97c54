package com.bilibili.brand.biz.config;

import com.bilibili.adp.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
public enum ConfigType {

    COMMON(0, "系统默认"),
    SSA_PRELOAD(1, "闪屏预加载");

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    public void check(Integer code) throws ServiceException {
        for (ConfigType configType : ConfigType.values()){
            if(configType.getCode().equals(code)){
                return;
            }
        }
        throw new ServiceException("不存在"+ code + "对应的配置类型枚举");
    }

}
