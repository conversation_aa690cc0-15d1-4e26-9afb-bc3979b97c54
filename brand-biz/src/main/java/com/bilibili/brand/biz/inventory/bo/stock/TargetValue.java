package com.bilibili.brand.biz.inventory.bo.stock;

import com.bilibili.brand.api.resource.targetmeta.TargetType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/3/11 15:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetValue {
    private TargetType targetType;
    /**
     * 业务使用的定向id，可能和{@link itemIds}&{@link targetIds}一致，也可能不一致
     */
    private Set<Integer> valueIds;

    /**
     * res_target_item表的主键id
     */
    private Set<Integer> itemIds;

    /**
     * res_target_item表的mapping_content
     */
    private Set<Integer> targetIds;
}
