package com.bilibili.brand.biz.account.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.account.po.RealNameMaterialPo;

/**
 * <AUTHOR>
 * @date 2017年2月27日
 */
public interface RealNameMaterialDao {
    
    List<RealNameMaterialPo> getByAccountId(@Param("accountId") Integer accountId);

    List<RealNameMaterialPo> getByAccountIds(@Param("accountIds") List<Integer> accountIds);

    int batchSave(@Param("poList") List<RealNameMaterialPo> poList);
    
    void deleteByAccountId(@Param("accountId") Integer accountId);
}
