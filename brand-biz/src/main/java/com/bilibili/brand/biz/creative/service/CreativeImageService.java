package com.bilibili.brand.biz.creative.service;

import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.dao.GdCreativeImageDao;
import com.bilibili.brand.biz.creative.po.GdCreativeImagePo;
import com.bilibili.brand.biz.creative.po.GdCreativeImagePoExample;
import com.bilibili.cpt.platform.api.creative.dto.ImageDto;
import com.bilibili.cpt.platform.common.CategoryEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/12 21:35
 */
@Service
@Slf4j
public class CreativeImageService {
    @Autowired
    private GdCreativeImageDao creativeImageDao;
    @Autowired
    private CreativeButtonService creativeButtonService;


    public List<ImageDto> getImageByCreative(long creativeId) {
        Map<Long, List<ImageDto>> images = this.getImageByCreative(Lists.newArrayList(creativeId));
        return images.getOrDefault(creativeId, Lists.newArrayList());
    }

    public Map<Long, List<ImageDto>> getImageByCreative(List<Long> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Maps.newHashMap();
        }
        GdCreativeImagePoExample example = new GdCreativeImagePoExample();
        example.or().andCreativeIdIn(creativeIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdCreativeImagePo> imagePos = this.creativeImageDao.selectByExample(example);
        if (CollectionUtils.isEmpty(imagePos)) {
            return Maps.newHashMap();
        }
        Map<Long, List<ImageDto>> listMap = imagePos.stream()
                .map(po -> this.convert2ImageDto(po))
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ImageDto::getCreativeId));
        listMap.values().forEach(value -> value.sort(Comparator.comparing(ImageDto::getSeq)));
        return listMap;
    }

    private ImageDto convert2ImageDto(GdCreativeImagePo po) {
        if (Objects.isNull(po)) {
            return null;
        }
        ImageDto dto = new ImageDto();
        BeanUtils.copyProperties(po, dto);
        if (!StringUtils.isEmpty(po.getGifImageUrl())) {
            dto.setIsGif(true);
            dto.setImageUrl(po.getGifImageUrl());
            dto.setImageMd5(po.getGifImageUrlHash());
            dto.setCoverUrl(po.getImageUrl());
        }
        dto.setButtons(this.creativeButtonService.getButtons(po.getCreativeId(), po.getId().longValue(), CategoryEnum.IMAGE));
        return dto;
    }
}
