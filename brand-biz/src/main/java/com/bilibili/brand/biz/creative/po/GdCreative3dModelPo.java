package com.bilibili.brand.biz.creative.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdCreative3dModelPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 业务场景，用于表示该模型正在被哪种业务使用，1、story天降礼盒
     */
    private Integer bizScene;

    /**
     * 跳转时机，特效起播后多长时间进行跳转，单位ms
     */
    private Integer jumpDelay;

    /**
     * 模型列表，包含file_name、type、url、md5
     */
    private String modelList;

    /**
     * 模型参数，包含generation_duration、falling_duration、removal_delay、gravity_factor、box_count、gravity_affected、collider_box（width、height、depth）
     */
    private String params;

    /**
     * 是否删除，0、否 1、是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}