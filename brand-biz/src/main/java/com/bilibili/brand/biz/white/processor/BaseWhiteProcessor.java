package com.bilibili.brand.biz.white.processor;

import com.bilibili.brand.api.resource.system.ISystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public abstract class BaseWhiteProcessor {

    @Autowired
    protected ISystemConfigService systemConfigService;

    public boolean isWhite(String identity) {
        List<String> whiteList = queryWhiteList();
        if (CollectionUtils.isEmpty(whiteList)) {
            return false;
        }
        return whiteList.contains(identity);
    }

    public abstract List<String> queryWhiteList();

    public abstract String supportedConfigKey();
}
