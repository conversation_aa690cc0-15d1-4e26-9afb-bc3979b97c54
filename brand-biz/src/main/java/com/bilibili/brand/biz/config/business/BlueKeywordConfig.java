package com.bilibili.brand.biz.config.business;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/14 11:26
 */
@Component
@Data
public class BlueKeywordConfig implements Serializable {
    private static final long serialVersionUID = -8542254214313854574L;

    //品牌小蓝词评论缓存开关
    @Value("${brand.blue.keyword.comment.id.cache.enable:false}")
    private boolean enableCommentIdCache;

    //品牌小蓝词评论缓存时长，单位秒
    @Value("${brand.blue.keyword.comment.id.cache.duration:300}")
    private Integer commentIdCacheDuration;

    //品牌小蓝词评论最大匹配数
    @Value("${brand.blue.keyword.comment.match.limit:2}")
    private Integer commentKeywordMatchLimit;
}
