package com.bilibili.brand.biz.databus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Ogv Season 状态变更
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=695822359">Season状态变更事件</a>
 *
 * <AUTHOR>
 * @date 2023/9/17 15:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OgvSeasonStatusChangeMsg implements Serializable {
    private static final long serialVersionUID = 9104480247409305109L;

    /**
     * Season Id
     */
    private String entityId;
    /**
     * SEASON
     */
    private String entityType;
    /**
     * 固定值：STATUS_UPDATED
     */
    private String eventType;
    /**
     * 业务状态发生改变的时间（yyyyMMDD hh:mm:ss）
     */
    private String time;
    /**
     * 改变后状态
     * 如上下架动作，可能包含以下几种状态
     * ONLINE/OFFLINE/DELETE
     */
    private String value;

    /**
     * 变更之前的值，具体值参考{@link #value}
     */
    private String originalValue;
}
