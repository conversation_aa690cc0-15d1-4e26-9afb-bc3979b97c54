package com.bilibili.brand.biz.creative.dao;

import com.bilibili.brand.biz.creative.po.GdCreativeImagePo;
import com.bilibili.brand.biz.creative.po.GdCreativeImagePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface GdCreativeImageDao {
    long countByExample(GdCreativeImagePoExample example);

    int deleteByExample(GdCreativeImagePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(GdCreativeImagePo record);

    int insertBatch(List<GdCreativeImagePo> records);

    int insertUpdateBatch(List<GdCreativeImagePo> records);

    int insert(GdCreativeImagePo record);

    int insertUpdateSelective(GdCreativeImagePo record);

    int insertSelective(GdCreativeImagePo record);

    List<GdCreativeImagePo> selectByExample(GdCreativeImagePoExample example);

    GdCreativeImagePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") GdCreativeImagePo record, @Param("example") GdCreativeImagePoExample example);

    int updateByExample(@Param("record") GdCreativeImagePo record, @Param("example") GdCreativeImagePoExample example);

    int updateByPrimaryKeySelective(GdCreativeImagePo record);

    int updateByPrimaryKey(GdCreativeImagePo record);
}