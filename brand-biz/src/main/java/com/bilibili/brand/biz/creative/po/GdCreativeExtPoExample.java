package com.bilibili.brand.biz.creative.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class GdCreativeExtPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GdCreativeExtPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNull() {
            addCriterion("creative_id is null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIsNotNull() {
            addCriterion("creative_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeIdEqualTo(Long value) {
            addCriterion("creative_id =", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotEqualTo(Long value) {
            addCriterion("creative_id <>", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThan(Long value) {
            addCriterion("creative_id >", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("creative_id >=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThan(Long value) {
            addCriterion("creative_id <", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdLessThanOrEqualTo(Long value) {
            addCriterion("creative_id <=", value, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdIn(List<Long> values) {
            addCriterion("creative_id in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotIn(List<Long> values) {
            addCriterion("creative_id not in", values, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdBetween(Long value1, Long value2) {
            addCriterion("creative_id between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andCreativeIdNotBetween(Long value1, Long value2) {
            addCriterion("creative_id not between", value1, value2, "creativeId");
            return (Criteria) this;
        }

        public Criteria andOrderProductIsNull() {
            addCriterion("order_product is null");
            return (Criteria) this;
        }

        public Criteria andOrderProductIsNotNull() {
            addCriterion("order_product is not null");
            return (Criteria) this;
        }

        public Criteria andOrderProductEqualTo(Integer value) {
            addCriterion("order_product =", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductNotEqualTo(Integer value) {
            addCriterion("order_product <>", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductGreaterThan(Integer value) {
            addCriterion("order_product >", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_product >=", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductLessThan(Integer value) {
            addCriterion("order_product <", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductLessThanOrEqualTo(Integer value) {
            addCriterion("order_product <=", value, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductIn(List<Integer> values) {
            addCriterion("order_product in", values, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductNotIn(List<Integer> values) {
            addCriterion("order_product not in", values, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductBetween(Integer value1, Integer value2) {
            addCriterion("order_product between", value1, value2, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andOrderProductNotBetween(Integer value1, Integer value2) {
            addCriterion("order_product not between", value1, value2, "orderProduct");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdIsNull() {
            addCriterion("first_product_label_id is null");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdIsNotNull() {
            addCriterion("first_product_label_id is not null");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdEqualTo(Long value) {
            addCriterion("first_product_label_id =", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdNotEqualTo(Long value) {
            addCriterion("first_product_label_id <>", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdGreaterThan(Long value) {
            addCriterion("first_product_label_id >", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("first_product_label_id >=", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdLessThan(Long value) {
            addCriterion("first_product_label_id <", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdLessThanOrEqualTo(Long value) {
            addCriterion("first_product_label_id <=", value, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdIn(List<Long> values) {
            addCriterion("first_product_label_id in", values, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdNotIn(List<Long> values) {
            addCriterion("first_product_label_id not in", values, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdBetween(Long value1, Long value2) {
            addCriterion("first_product_label_id between", value1, value2, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andFirstProductLabelIdNotBetween(Long value1, Long value2) {
            addCriterion("first_product_label_id not between", value1, value2, "firstProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdIsNull() {
            addCriterion("second_product_label_id is null");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdIsNotNull() {
            addCriterion("second_product_label_id is not null");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdEqualTo(Long value) {
            addCriterion("second_product_label_id =", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdNotEqualTo(Long value) {
            addCriterion("second_product_label_id <>", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdGreaterThan(Long value) {
            addCriterion("second_product_label_id >", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdGreaterThanOrEqualTo(Long value) {
            addCriterion("second_product_label_id >=", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdLessThan(Long value) {
            addCriterion("second_product_label_id <", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdLessThanOrEqualTo(Long value) {
            addCriterion("second_product_label_id <=", value, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdIn(List<Long> values) {
            addCriterion("second_product_label_id in", values, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdNotIn(List<Long> values) {
            addCriterion("second_product_label_id not in", values, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdBetween(Long value1, Long value2) {
            addCriterion("second_product_label_id between", value1, value2, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andSecondProductLabelIdNotBetween(Long value1, Long value2) {
            addCriterion("second_product_label_id not between", value1, value2, "secondProductLabelId");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andInteractStyleIsNull() {
            addCriterion("interact_style is null");
            return (Criteria) this;
        }

        public Criteria andInteractStyleIsNotNull() {
            addCriterion("interact_style is not null");
            return (Criteria) this;
        }

        public Criteria andInteractStyleEqualTo(Integer value) {
            addCriterion("interact_style =", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleNotEqualTo(Integer value) {
            addCriterion("interact_style <>", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleGreaterThan(Integer value) {
            addCriterion("interact_style >", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleGreaterThanOrEqualTo(Integer value) {
            addCriterion("interact_style >=", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleLessThan(Integer value) {
            addCriterion("interact_style <", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleLessThanOrEqualTo(Integer value) {
            addCriterion("interact_style <=", value, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleIn(List<Integer> values) {
            addCriterion("interact_style in", values, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleNotIn(List<Integer> values) {
            addCriterion("interact_style not in", values, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleBetween(Integer value1, Integer value2) {
            addCriterion("interact_style between", value1, value2, "interactStyle");
            return (Criteria) this;
        }

        public Criteria andInteractStyleNotBetween(Integer value1, Integer value2) {
            addCriterion("interact_style not between", value1, value2, "interactStyle");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}