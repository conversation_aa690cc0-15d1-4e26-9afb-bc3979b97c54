package com.bilibili.brand.biz.inventory.bo.stock;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 锁量响应
 *
 * <AUTHOR>
 * @date 2025/3/7 16:32
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class AllocateStockResponse extends GoblinStockResponse {
    /**
     * 批量查询库存结果，{@link AllocateTaskStock#getScheduleId()}和{@link AllocateTask#getScheduleId()}一一映射
     *
     * @see AllocateTask
     */
    private List<AllocateTaskStock> taskStocks;
}
