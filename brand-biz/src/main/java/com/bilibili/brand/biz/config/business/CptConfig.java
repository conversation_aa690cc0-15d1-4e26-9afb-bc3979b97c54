package com.bilibili.brand.biz.config.business;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

@Data
@Component
public class CptConfig implements Serializable {

    private static final long serialVersionUID = -8670245928943016772L;
    /**
     * 【品牌】相关推荐新增投放方案 - cpt
     * https://www.tapd.cn/67874887/prong/stories/view/1167874887004251131
     */
    @Value("#{'${resource.cpt.player.detail.cm.mark.ids:1,123}'.split(',')}")
    private List<Integer> playerDetailCmMarks;

    @Value("#{'${resource.booking.validate.skip.source.ids:3163,3171}'.split(',')}")
    private List<Integer> skipBookingValidateSourceIds;

    @Value("#{'${cpt.dynamic.source.ids:3163,3171}'.split(',')}")
    private List<Integer> dynamicSourceIds;

    @Value("${cpt.dynamic.pic.template.id:671}")
    private Integer dynamicPicTemplate;

    @Value("${cpt.dynamic.video.template.id:670}")
    private Integer dynamicVideoTemplate;

    //热门位原生模版id
    @Value("#{'${resource.hot.cpt.origin.template.ids:642}'.split(',')}")
    private List<Integer> hotCptOriginTemplateIds;

    @Value("#{'${resource.hot.cpt.need.filter.origin.mark.ids:104}'.split(',')}")
    private List<Integer> needFilterOriginMarkIds;

    /**
     * 【品牌】直播间右下角业务大卡-品牌广告接入需求
     * https://www.tapd.cn/67874887/prong/stories/view/1167874887004575696
     */
    @Value("#{'${resource.cpt.live.room.cm.mark.ids:1,9}'.split(',')}")
    private List<Integer> liveRoomCmMarks;
}
