package com.bilibili.brand.biz.config.business;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/21
 */
@Data
@Component
public class RobotConfig {
    @Value("#{'${gd.info.robot.skip.products:22}'.split(',')}")
    private List<Integer> skipOrderProducts;

    public boolean keepThisOrderProduct(Integer orderProduct) {
        return !skipOrderProducts.contains(orderProduct);
    }

}
