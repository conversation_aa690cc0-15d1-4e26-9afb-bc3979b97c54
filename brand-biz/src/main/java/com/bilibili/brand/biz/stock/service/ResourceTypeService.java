//package com.bilibili.brand.biz.stock.service;
//
//import com.bilibili.brand.api.stock.service.IResourceTypeService;
//import com.bilibili.brand.biz.config.business.ConfigCenter;
//import com.bilibili.brand.biz.schedule.service.frequency.ResourceService;
//import com.bilibili.cpt.platform.common.ResourceType;
//import com.google.common.collect.Lists;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.LinkedList;
//import java.util.List;
//
///**
// * 已经被另外的类替代
// * @see ResourceService
// * <AUTHOR>
// * @date 2022/4/11
// */
//@Service
//@Deprecated
//public class ResourceTypeService implements IResourceTypeService {
//    @Autowired
//    private ConfigCenter configCenter;
//
//    @Value("#{'${resource.ott.gd.slotGroupId.ids}'.split(',')}")
//    private List<Integer> ottSlotGroupIds;
//
//    @Value("#{'${resource.ssa.gd.plus.slotGroupId.ids:126,127,128}'.split(',')}")
//    private List<Integer> ssaGdSlotGroupIds;
//
//    @Value("#{'${gd.small.card.slot.group}'.split(',')}")
//    private List<Integer> smallCardSlotGroupIds;
//
//    private final List<ResourceTypeConfig> configs = new LinkedList<>();
//
//    @PostConstruct
//    public void init() {
//        configs.add(new ResourceTypeConfig(ottSlotGroupIds,
//                Lists.newArrayList(-1),
//                ResourceType.OTHER.getCode()));
//
//        configs.add(new ResourceTypeConfig(ssaGdSlotGroupIds,
//                Lists.newArrayList(-1),
//                ResourceType.OTHER.getCode()));
//
//        configs.add(new ResourceTypeConfig(Lists.newArrayList(-1),
//                configCenter.getGdPlusConfig().getStoryGdPlusTemplateIds(),
//                ResourceType.OTHER.getCode()));
//
//        //小卡
//        configs.add(new ResourceTypeConfig(smallCardSlotGroupIds,
//                configCenter.getGdPlusConfig().getSmallCardTemplateIds(),
//                ResourceType.SMALL_CARD.getCode()));
//
//        configs.add(new ResourceTypeConfig(Lists.newArrayList(-1),
//                configCenter.getGdPlusConfig().getBigCardTemplateIds(),
//                ResourceType.BIG_CARD.getCode()));
//    }
//
//    @Override
//    public int getResourceType(int slotGroupId, Integer templateId) {
//        if (!CollectionUtils.isEmpty(configs)) {
//            for (ResourceTypeConfig resourceTypeConfig : configs) {
//                if (resourceTypeConfig.getSlotGroupIds().contains(slotGroupId)) {
//                    return resourceTypeConfig.getResourceType();
//                }
//                if (resourceTypeConfig.getTemplateIds().contains(templateId)) {
//                    return resourceTypeConfig.getResourceType();
//                }
//            }
//        }
//        return ResourceType.OTHER.getCode();
//    }
//
//    @Data
//    @AllArgsConstructor
//    private static class ResourceTypeConfig {
//        private List<Integer> slotGroupIds;
//
//        private List<Integer> templateIds;
//
//        private int resourceType;
//    }
//
//}
