package com.bilibili.brand.biz.event_bus.listener;

import com.bilibili.brand.api.mbm.dto.BrandMainSiteAccountConfigDto;
import com.bilibili.brand.api.mbm.dto.BrandMainSiteAccountPermissionDto;
import com.bilibili.brand.api.mbm.dto.ExtraAccountPermissionDto;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import com.bilibili.brand.biz.event_bus.IBrandEventListener;
import com.bilibili.brand.biz.event_bus.event.BrandMainSiteAccountPermissionChangeEvent;
import com.bilibili.brand.biz.rpc.grpc.client.AccountExtraGrpcClient;
import com.bilibili.enums.BrandMainSiteAccountPermissionEnum;
import com.bilibili.enums.BrandMainSiteAccountPermissionStatusEnum;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 品牌主站账号权限配置变化监听器
 * <a href="https://www.tapd.cn/********/prong/stories/view/11********004409070">【商业】商业特殊账号空间页不可见</a>
 * <a href="https://doc.weixin.qq.com/doc/w3_AVAAMQZxACUyRLGMHagSVCHNolYuD?scode=ANYAEAdoABEg4L8dH1AVAAMQZxACU">数据交互文档</a>
 * <a href="https://main-infra.bilibili.co/#/gatewayManage/biz">extra平台</a>
 *
 * <AUTHOR>
 * @date 2024/10/24 19:19
 */
@Slf4j
@Component
public class MainSiteAccountPermissionEventListener implements IBrandEventListener<BrandEvent> {
    @Autowired
    private AccountExtraGrpcClient accountExtraGrpcClient;
    //品牌主站账号权限配置管理
    public static final String BUSINESS_KEY_BRAND_MS_ACCOUNT_MANAGE = "brand_ms_permission_manage";

    @Override
    public boolean match(BrandEvent event) {
        return (event instanceof BrandMainSiteAccountPermissionChangeEvent);
    }

    @Override
    public void onEvent(BrandEvent event) {
        BrandMainSiteAccountPermissionChangeEvent permissionChangeEvent = (BrandMainSiteAccountPermissionChangeEvent) event;
        BrandMainSiteAccountConfigDto accountConfig = permissionChangeEvent.getAccountConfig();
        BrandEvent.ActionType actionType = permissionChangeEvent.getActionType();
        if (Objects.equals(actionType, BrandEvent.ActionType.REMOVE)) {
            removeAccountPermissionFromExtra(Lists.newArrayList(permissionChangeEvent.getAccountConfig().getMid()));
        } else if (Objects.equals(actionType, BrandEvent.ActionType.ADD)
                || Objects.equals(actionType, BrandEvent.ActionType.MODIFY)
                || Objects.equals(actionType, BrandEvent.ActionType.REFRESH)) {
            List<BrandMainSiteAccountPermissionDto> permissions = accountConfig.getPermissions();
            //解析每种权限的有效性（状态+时间）
            Map<Integer, Integer> openPermissionMap = Collections.emptyMap();
            if (!CollectionUtils.isEmpty(permissions)) {
                Timestamp now = new Timestamp(System.currentTimeMillis());
                openPermissionMap = permissions.stream().collect(Collectors.toMap(BrandMainSiteAccountPermissionDto::getCode,
                        p -> {
                            //忽略了时间相等的极端case
                            boolean opened = Objects.equals(BrandMainSiteAccountPermissionStatusEnum.OPENED.getCode(), p.getStatus())
                                    && now.before(p.getEndTime()) && now.after(p.getBeginTime());
                            return opened ? 1 : 0;
                        }, OptionalUtil.override()));
            }
            ExtraAccountPermissionDto extraAccountPermission = ExtraAccountPermissionDto.builder()
                    .mid(accountConfig.getMid())
                    .spaceHidden(openPermissionMap.getOrDefault(BrandMainSiteAccountPermissionEnum.SPACE_HIDDEN.getCode(), 0))
                    .build();
            addAccountPermission2Extra(Lists.newArrayList(extraAccountPermission));
        }
    }

    /**
     * 将权限变更发送到account-extra，供主站评论使用
     *
     * @return 返回加入失败的账号
     */
    public List<Long> addAccountPermission2Extra(List<ExtraAccountPermissionDto> extraAccountPermissionList) {
        if (CollectionUtils.isEmpty(extraAccountPermissionList)) {
            return Collections.emptyList();
        }
        Map<Long, Object> permissionMap = extraAccountPermissionList.stream()
                .collect(Collectors.toMap(ExtraAccountPermissionDto::getMid, Function.identity(), OptionalUtil.override()));
        List<Long> failedMidList = accountExtraGrpcClient.addExtra(BUSINESS_KEY_BRAND_MS_ACCOUNT_MANAGE, permissionMap);
        if (!CollectionUtils.isEmpty(failedMidList)) {
            log.info("[MainSiteAccountPermissionEventListener] addExtra4AccountPermissionManage error, failedMidList={}", failedMidList);
        }
        return failedMidList;
    }

    /**
     * 将账号从account-extra中移除
     *
     * @return 返回移除失败的账号
     */
    public List<Long> removeAccountPermissionFromExtra(List<Long> midList) {
        if (CollectionUtils.isEmpty(midList)) {
            return Collections.emptyList();
        }
        List<Long> failedMidList = accountExtraGrpcClient.removeExtra(BUSINESS_KEY_BRAND_MS_ACCOUNT_MANAGE, midList);
        if (!CollectionUtils.isEmpty(failedMidList)) {
            log.info("[MainSiteAccountPermissionEventListener] removeExtra4AccountPermissionManage error, failedMidList={}", failedMidList);
        }
        return failedMidList;
    }
}
