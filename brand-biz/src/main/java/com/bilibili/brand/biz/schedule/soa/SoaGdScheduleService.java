package com.bilibili.brand.biz.schedule.soa;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.PutLocation;
import com.bilibili.brand.api.common.enums.ShowPriorityType;
import com.bilibili.brand.api.common.enums.SsaDisplayModeEnum;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.service.IGdOrderExtService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.slot.ISlotService;
import com.bilibili.brand.api.resource.slot.Slot;
import com.bilibili.brand.api.resource.slot_group.IResSlotGroupService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.GdScheduleDateDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleOgvDto;
import com.bilibili.brand.api.schedule.service.IOgvScheduleService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.soa.dto.GdScheduleDailyDto;
import com.bilibili.brand.api.schedule.soa.dto.GdScheduleDto;
import com.bilibili.brand.api.schedule.soa.service.ISoaGdScheduleService;
import com.bilibili.brand.biz.cycle.GdCycleService;
import com.bilibili.brand.biz.cycle.dto.GdCycleDto;
import com.bilibili.brand.dto.cycle.CycleDto;
import com.bilibili.brand.platform.report.api.dto.StatScheduleDto;
import com.bilibili.brand.platform.report.api.service.IStatCreativeService;
import com.bilibili.brand.platform.report.api.service.IStatScheduleService;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.enums.WakeAppType;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.service.ISsaCycleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import com.bilibili.ssa.platform.common.enums.SsaShowStyleType;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.bilibili.utils.TimeUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2017/7/14.
 */
@Slf4j
@Service
public class SoaGdScheduleService implements ISoaGdScheduleService {

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IStatScheduleService statScheduleService;

    @Autowired
    private IResSlotGroupService resSlotGroupService;

    @Autowired
    private ISlotService slotService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private IGdOrderExtService gdOrderExtService;

    @Autowired
    private IStatCreativeService statCreativeService;

    @Autowired
    private ISsaSplashScreenService ssaSplashScreenService;

    @Autowired
    private IOgvScheduleService ogvScheduleService;

    @Autowired
    private GdCycleService gdCycleService;

    @Autowired
    private ISsaCycleService ssaCycleService;

    @Override
    public List<GdScheduleDailyDto> getGdScheduleDailyDtosByCrmOrderId(Integer crmOrderId) throws ServiceException {
        long startTime = System.currentTimeMillis();
        log.info("getGdScheduleDailyDtosByCrmOrderId by orderId:[{}] begin", crmOrderId);
        GdOrderDto gdOrderDto = gdOrderService.getOrderByCrmOrderId(crmOrderId);
        List<ScheduleDto> scheduleDtos = queryScheduleService.getSchedulesByOrderId(gdOrderDto.getOrderId()).stream()
                .filter(dto -> !dto.getStatus().equals(SwitchStatus.DELETE.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scheduleDtos)) {
            return Collections.emptyList();
        }
        List<Integer> scheduleIds = scheduleDtos.stream().map(ScheduleDto::getScheduleId).collect(Collectors.toList());

        //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002936919
        //topview类型，其首焦和闪屏共用一个排期，且排期维度的展点消数据是首焦和闪屏的累加和，因此数据产生了失真
        //CRM侧针对topview类型只关心闪屏的曝光（参与计收），顾只返回闪屏素材的曝光量
        //闪屏部分的SalesType分别是和订单类型对应的76和95
        //首焦部分的SalesType统一是43
        Map<Integer, List<StatScheduleDto>> statScheduleMap;
        if (OrderProduct.TOPVIEW_CODE_SET.contains(gdOrderDto.getProduct())) {
            //闪屏部分
            statScheduleMap = statScheduleService.getMapInScheduleIdsGroupByDay(scheduleIds,
                    OrderProduct.getByCode(gdOrderDto.getProduct()).getSalesType().getCode());
        } else {
            statScheduleMap = statScheduleService.getMapInScheduleIdsGroupByDay(scheduleIds);
        }

        //adx mock的数据
        Map<Integer, Integer> scheduleId2ShowMockData = getMockStatData();

        List<GdScheduleDailyDto> result = new ArrayList<>();
        for (ScheduleDto scheduleDto : scheduleDtos) {
            if (scheduleDto.getScheduleDates() == null){
                continue;
            }

            List<StatScheduleDto> statScheduleDtos = statScheduleMap.getOrDefault(scheduleDto.getScheduleId(), new ArrayList<>());
            //(yyyy-MM-dd, StatCampaignDto)
//            Map<String, StatScheduleDto> tempStatMap = statScheduleDtos.stream()
//                    .collect(Collectors.toMap(StatScheduleDto::getDate, one -> one));

            Map<String, List<StatScheduleDto>> tempStatMap = statScheduleDtos.stream()
                    .collect(Collectors.groupingBy(StatScheduleDto::getDate));
            Map<String, StatScheduleDto> map = new HashMap<>();
            tempStatMap.forEach((k, v) -> {
                StatScheduleDto statScheduleDto = v.stream().reduce(new StatScheduleDto(), StatScheduleDto::merge);
                map.put(k, statScheduleDto);
            });

            for (GdScheduleDateDto dateDto : scheduleDto.getScheduleDates()) {
                String day = TimeUtil.timestampToString(dateDto.getScheduleDate());

                GdScheduleDailyDto dto = GdScheduleDailyDto.builder()
                        .crmOrderId(crmOrderId)
                        .scheduleName(scheduleDto.getName())
                        .scheduleId(scheduleDto.getScheduleId())
                        .sourceId(scheduleDto.getSlotGroupId())//GD自2018年始只会有广告位组
                        .launchDate(day)
                        .targetCpm(scheduleDto.getTotalImpression())
                        .actualCpm(0)
                        .costPrice(scheduleDto.getCostPrice())
                        .templateId(scheduleDto.getTemplateId())
                        .amount(scheduleDto.getCostPrice() == 0 ? scheduleDto.getExternalPrice() : (long) scheduleDto.getCostPrice() * scheduleDto.getTotalImpression())
                        .build();
                if(Utils.isPositive(scheduleDto.getPdbActualImpression())){
                    dto.setTargetCpm(scheduleDto.getPdbActualImpression());
                }

                if (tempStatMap.containsKey(day)) {
                    StatScheduleDto statScheduleDto = map.get(day);
                    dto.setActualCpm(Math.toIntExact(statScheduleDto.getShowCount() / 1000));
                }

                //mock的曝光数据
                if(scheduleId2ShowMockData.containsKey(scheduleDto.getScheduleId())){
                    dto.setActualCpm(scheduleId2ShowMockData.get(scheduleDto.getScheduleId()));
                }

                result.add(dto);
            }
        }

        addVirtualScheduleDailyIfNecessary(gdOrderDto, result);
        long endTime = System.currentTimeMillis();
        log.info("getGdScheduleDailyDtosByCrmOrderId by orderId:[{}] end. spend time is [{}]",
                crmOrderId, endTime-startTime);
        return result;
    }

    @Override
    public List<GdScheduleDailyDto> getGdScheduleDailyDtosBySchedules(List<Integer> scheduleIds) throws ServiceException {
        if (CollectionUtils.isEmpty(scheduleIds)){
            return new ArrayList<>();
        }
        Map<Integer, List<StatScheduleDto>> statScheduleMap = statScheduleService.getMapInScheduleIdsGroupByDay(scheduleIds);

        List<GdScheduleDailyDto> result = new ArrayList<>();
        List<ScheduleDto> gdScheduleDtoList = queryScheduleService.getSchedulesInIds(scheduleIds);

        //adx mock的数据
        Map<Integer, Integer> scheduleId2ShowMockData = getMockStatData();

        for (ScheduleDto scheduleDto : gdScheduleDtoList) {
            if (scheduleDto.getScheduleDates() == null){
                continue;
            }

            List<StatScheduleDto> statScheduleDtos = statScheduleMap.getOrDefault(scheduleDto.getScheduleId(), new ArrayList<>());

            //(yyyy-MM-dd, StatCampaignDto)
            Map<String, StatScheduleDto> tempMap = statScheduleDtos.stream()
                    .collect(Collectors.toMap(StatScheduleDto::getDate, one -> one));
            for (GdScheduleDateDto dateDto : scheduleDto.getScheduleDates()) {
                String day = TimeUtil.timestampToString(dateDto.getScheduleDate());

                GdScheduleDailyDto dto = GdScheduleDailyDto.builder()
                        .scheduleName(scheduleDto.getName())
                        .scheduleId(scheduleDto.getScheduleId())
                        .sourceId(scheduleDto.getSlotGroupId())//GD自2018年始只会有广告位组
                        .launchDate(day)
                        .actualCpm(0)
                        .costPrice(scheduleDto.getCostPrice())
                        .targetCpm(scheduleDto.getTotalImpression())
                        .build();
                if(Utils.isPositive(scheduleDto.getPdbActualImpression())){
                    dto.setTargetCpm(scheduleDto.getPdbActualImpression());
                }

                if (tempMap.containsKey(day)){
                    StatScheduleDto statScheduleDto = tempMap.get(day);
                    dto.setActualCpm(Math.toIntExact(statScheduleDto.getShowCount()/1000));
                }

                //mock的曝光数据
                if(scheduleId2ShowMockData.containsKey(scheduleDto.getScheduleId())){
                    dto.setActualCpm(scheduleId2ShowMockData.get(scheduleDto.getScheduleId()));
                }
                result.add(dto);
            }
        }
        return result;
    }

    @Override
    public List<GdScheduleDto> getGdScheduleDtosByCrmOrderId(Integer crmOrderId) throws ServiceException {
        GdOrderDto gdOrderDto = gdOrderService.getOrderByCrmOrderId(crmOrderId);

        List<ScheduleDto> scheduleDtos = queryScheduleService.getSchedulesByOrderId(gdOrderDto.getOrderId())
                .stream()
                .filter(dto -> !dto.getStatus().equals(SwitchStatus.DELETE.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scheduleDtos)) {
            return Collections.emptyList();
        }

        AtomicReference<Map<Integer, Long>> statScheduleMap = new AtomicReference<>(Maps.newHashMap());
        List<Integer> scheduleIds = scheduleDtos.stream().map(ScheduleDto::getScheduleId).collect(Collectors.toList());


        //返回刊例周期
        Map<Integer, CycleDto> cycleMap;


        //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002936919
        //topview类型，其首焦和闪屏共用一个排期，且排期维度的展点消数据是首焦和闪屏的累加和，因此数据产生了失真
        //CRM侧针对topview类型只关心闪屏的曝光（参与计收），顾只返回闪屏素材的曝光量
        //闪屏部分的SalesType分别是和订单类型对应的76和95
        //首焦部分的SalesType统一是43
        if (OrderProduct.TOPVIEW_CODE_SET.contains(gdOrderDto.getProduct()) || OrderProduct.SSA_CODE_SET.contains(gdOrderDto.getProduct())) {
            List<StatScheduleDto> stateScheduleList = statScheduleService.getInScheduleIdsGroupByTime(scheduleIds,
                    null,
                    Utils.getEndOfDay(Utils.getSomeDayAgo(Utils.getNow(), 1)),
                    OrderProduct.getByCode(gdOrderDto.getProduct()).getSalesType().getCode());
            statScheduleMap.set(stateScheduleList.stream()
                    .collect(Collectors.toMap(StatScheduleDto::getScheduleId, StatScheduleDto::getShowCount, Long::sum)));
            Map<Integer, SsaCycleDto> ssaCycleMap = this.ssaCycleService.getCycleDtoMapInIds(scheduleDtos.stream()
                    .map(ScheduleDto::getCycleId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList()));
            cycleMap = ssaCycleMap.values().stream()
                    .collect(Collectors.toMap(SsaCycleDto::getId, c -> CycleDto.builder().id(c.getId()).name(c.getName()).build()));
        } else {
            statScheduleMap.set(statScheduleService.getMapInScheduleIdsGroupByTime(scheduleIds).entrySet()
                    .stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getShowCount())));
            Map<Integer, GdCycleDto> gdCycleMap = this.gdCycleService.getCyclesById(scheduleDtos.stream()
                    .map(ScheduleDto::getCycleId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList()));
            cycleMap = gdCycleMap.values().stream()
                    .collect(Collectors.toMap(GdCycleDto::getId, c -> CycleDto.builder().id(c.getId()).name(c.getName()).build()));
        }

        Set<Integer> slotIdSet = scheduleDtos.stream()
                .filter(dto -> dto.getPutLocation().equals(PutLocation.SLOT.getCode()))
                .map(ScheduleDto::getSlotId)
                .collect(Collectors.toSet());

        Map<Integer, String> slotNameMap = slotService.getSlotList(slotIdSet).stream().collect(Collectors.toMap(Slot::getSlotId, Slot::getSlotName));

        List<Integer> slotGroupIds = scheduleDtos.stream()
                .filter(dto -> dto.getPutLocation().equals(PutLocation.SLOT_GROUP.getCode()))
                .map(ScheduleDto::getSlotGroupId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, String> slotGroupNameMap = resSlotGroupService.getSlotGroupNameMapInIds(slotGroupIds);

        //adx mock的数据
        Map<Integer, Integer> scheduleId2ShowMockData = getMockStatData();

        //如果是OGV订单则查询OGV信息
        //【CRM】合同管理：新增”OGV标版GD、OGV标版CPT“订单类型同步及计收；
        //https://www.tapd.bilibili.co/20065741/prong/stories/view/1120065741003063582
        Map<Integer, ScheduleOgvDto> scheduleOgvMap = OrderProduct.isOgv(gdOrderDto.getProduct()) ?
                this.ogvScheduleService.getScheduleOgv(scheduleIds) : Maps.newHashMap();

        List<GdScheduleDto> res = scheduleDtos.stream().map(dto -> {
            String sourceName;
            if (dto.getPutLocation().equals(PutLocation.SLOT_GROUP.getCode())) {
                sourceName = slotGroupNameMap.getOrDefault(dto.getSlotGroupId(), "");
            } else {
                sourceName = slotNameMap.getOrDefault(dto.getSlotId(), "");
            }
            GdScheduleDto scheduleDto = GdScheduleDto.builder()
                    .scheduleId(dto.getScheduleId())
                    .scheduleName(dto.getName())
                    .sourceName(sourceName)
                    .beginDate(dto.getGdBeginTime() == null ? dto.getBeginDate() : dto.getGdBeginTime())
                    .endDate(dto.getGdEndTime() == null ? dto.getEndDate() : dto.getGdEndTime())
                    .amount(dto.getCostPrice() == 0 ? Utils.fromFenToYuan(dto.getExternalPrice()) : Utils.fromFenToYuan((long) dto.getCostPrice() * dto.getTotalImpression()))
                    .showPriority(dto.getShowPriority())
                    .targetCpm(Objects.equals(dto.getShowPriority(), ShowPriorityType.BUY_OUT_AND_SHOW_BY_LIVE_STATUS.getCode()) ? null : dto.getTotalImpression())
                    .actualCpm(Math.toIntExact(statScheduleMap.get().getOrDefault(dto.getScheduleId(), 0L) / 1000))
                    .costPrice(dto.getCostPrice())
                    .templateName(dto.getTemplateName())
                    .targetDto(dto.getTargetDto())
                    .launchInnerJump(dto.getLaunchInnerJump())
                    .ogv(scheduleOgvMap.get(dto.getScheduleId()))
                    .build();
            if(Utils.isPositive(dto.getPdbActualImpression())){
                scheduleDto.setTargetCpm(dto.getPdbActualImpression());
                scheduleDto.setAmount(Utils.fromFenToYuan((long) dto.getCostPrice() * dto.getPdbActualImpression()));
            }
            if (SalesType.getByCode(dto.getSalesType()) == SalesType.SSA_GD_PLUS
                    || SalesType.getByCode(dto.getSalesType()) == SalesType.SSA_GD
                    || SalesType.getByCode(dto.getSalesType()) == SalesType.TOP_VIEW_GD_PLUS) {
                scheduleDto.setSsaStyle(SsaShowStyleType.getByCode(dto.getShowStyle()).getScreenStyle().getDesc());
                scheduleDto.setSsaType(SsaAdType.getByCode(dto.getAdType()).getDesc());
                scheduleDto.setSsaOrientation(SsaShowStyleType.getByCode(dto.getShowStyle()).getScreenOrientation().getDesc());
                scheduleDto.setDifferenceCpm(scheduleDto.getTargetCpm() - scheduleDto.getActualCpm());
                scheduleDto.setEndDate(dto.getGdEndTime() == null ? TimeUtil.getEndOfDay(dto.getEndDate()) : dto.getGdEndTime());
            }

            scheduleDto.setDisplayMode(SsaDisplayModeEnum.getByCode(dto.getSsaDisplayMode()).getDesc());

            //mock的曝光数据
            if(scheduleId2ShowMockData.containsKey(scheduleDto.getScheduleId())){
                scheduleDto.setActualCpm(scheduleId2ShowMockData.get(scheduleDto.getScheduleId()));
            }

            CycleDto cycle = cycleMap.get(dto.getCycleId());
            scheduleDto.setCycle(CycleDto.builder().id(dto.getCycleId()).name(Objects.nonNull(cycle) ? cycle.getName() : "").build());
            scheduleDto.setWakeAppTypeDesc(WakeAppType.getByCode(dto.getWakeAppType()).getDesc());

            return scheduleDto;
        }).collect(Collectors.toList());

        addVirtualScheduleIfNecessary(gdOrderDto, res);

        return res;
    }

    private void addVirtualScheduleIfNecessary(GdOrderDto gdOrderDto, List<GdScheduleDto> scheduleList) {
        if (!CollectionUtils.isEmpty(scheduleList) && gdOrderDto.getProduct().equals(OrderProduct.GD_CPM.getCode())) {
            GdOrderExtDto gdOrderExtDto = gdOrderExtService.getGdOrderExtInfo(gdOrderDto.getOrderId());
            if (gdOrderExtService.isFlyGdRangeOrder(gdOrderExtDto)) {

                BigDecimal allAmount = new BigDecimal("0");
                int allCpm = 0;
                for (GdScheduleDto scheduleDto : scheduleList) {
                    allAmount = allAmount.add(scheduleDto.getAmount());
                    allCpm += scheduleDto.getTargetCpm();
                }

                GdScheduleDto virtualSchedule = GdScheduleDto.builder()
                        .scheduleName(gdOrderDto.getOrderName() + "虚拟排期")
                        //虚拟排期日期为订单日期的最后一天
                        .beginDate(TimeUtils.getBeginOfDay(gdOrderDto.getEndTime()))
                        .endDate(gdOrderDto.getEndTime())
                        .sourceName("虚拟位次")
                        .amount(Utils.fromFenToYuan(gdOrderExtDto.getOrderAmount()).subtract(allAmount))
                        .targetCpm(gdOrderExtDto.getOrderCpm() - allCpm)
                        .actualCpm(0)
                        .differenceCpm(0)
                        .build();

                scheduleList.add(virtualSchedule);
            }
        }
    }

    private void addVirtualScheduleDailyIfNecessary(GdOrderDto gdOrderDto, List<GdScheduleDailyDto> scheduleList) {
        if (!CollectionUtils.isEmpty(scheduleList) && gdOrderDto.getProduct().equals(OrderProduct.GD_CPM.getCode())) {
            GdOrderExtDto gdOrderExtDto = gdOrderExtService.getGdOrderExtInfo(gdOrderDto.getOrderId());
            if (gdOrderExtService.isFlyGdRangeOrder(gdOrderExtDto)) {
                BigDecimal allAmount = new BigDecimal("0");
                int allCpm = 0;
                for (GdScheduleDailyDto scheduleDto : scheduleList) {
                    // 单位是分
                    allAmount = allAmount.add(new BigDecimal(scheduleDto.getAmount()));
                    allCpm += scheduleDto.getTargetCpm();
                }

                int remainCpm = gdOrderExtDto.getOrderCpm() - allCpm;
                // 单位是分
                BigDecimal remainAmount = new BigDecimal(gdOrderExtDto.getOrderAmount()).subtract(allAmount);
                GdScheduleDailyDto virtualSchedule = GdScheduleDailyDto.builder()
                        .crmOrderId(gdOrderDto.getCrmOrderId())
                        .scheduleName(gdOrderDto.getOrderName() + "虚拟排期")
                        //虚拟排期日期为订单日期的最后一天
                        .launchDate(TimeUtils.getTimestamp2DayString(gdOrderDto.getEndTime()))
                        .sourceName("虚拟位次")
                        .amount(remainAmount.longValue())
                        .targetCpm(remainCpm)
                        .actualCpm(0)
                        .costPrice(Objects.equals(remainCpm, 0) ? 0L : remainAmount.divide(new BigDecimal(remainCpm), 2, RoundingMode.HALF_UP).longValue())
                        .build();

                scheduleList.add(virtualSchedule);
            }
        }
    }

    @Override
    public List<GdScheduleDto> getGdScheduleDtosByScheduleIds(List<Integer> scheduleId) {
        List<GdScheduleDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(scheduleId)){
            return new ArrayList<>();
        }
        List<ScheduleDto> gdScheduleDtoList = null;
        try {
            gdScheduleDtoList = queryScheduleService.getSchedulesInIds(scheduleId);
        } catch (ServiceException e) {
           log.error("querySheduleService.getSchedulesInIds failed", e);
        }
        if (CollectionUtils.isEmpty(gdScheduleDtoList)){
            return new ArrayList<>();
        }
        gdScheduleDtoList.forEach(dto ->{
            result.add(GdScheduleDto.builder()
                    .costPrice(dto.getCostPrice())
                    .scheduleId(dto.getScheduleId())
                    .targetDto(dto.getTargetDto())
                    .launchInnerJump(dto.getLaunchInnerJump())
                    .scheduleName(dto.getName())
                    .build());
        });
        return result;
    }

    //pdb和闪屏pdb
    private Map<Integer, Integer> getMockStatData(){
        //因为adx的测试数据很难造，所以在代码里mock
        //通过systemConfig配置来制造mock数据
        List<String> adxStatMockData = systemConfigService.getValueReturnList(SystemConfigEnum
                .ADX_STAT_MOCK_DATA.getCode());
        Map<Integer, Integer> scheduleId2Show = new HashMap<>();
        adxStatMockData.forEach(mockData->{
            String[] data= mockData.split("\\|");
            scheduleId2Show.put(Integer.valueOf(data[0]), Integer.valueOf(data[2]));
        });
        return scheduleId2Show;
    }

}
