package com.bilibili.brand.biz.account.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * CREATE TABLE `acc_account` (
 * `account_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '账号ID',
 * `username` varchar(32) NOT NULL DEFAULT '' COMMENT '用户名',
 * `mobile` varchar(16) NOT NULL DEFAULT '' COMMENT '手机号码',
 * `password_strength` tinyint(4) NOT NULL COMMENT '密码强度',
 * `salt` varchar(128) NOT NULL DEFAULT '' COMMENT '盐',
 * `salt_password` varchar(64) NOT NULL DEFAULT '' COMMENT '加盐密码',
 * `status` tinyint(4) NOT NULL COMMENT '状态',
 * `crm_customer_id` int(11) NOT NULL DEFAULT '0' COMMENT 'crm customer主键',
 * `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
 * `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
 * `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除，0是有效，1是删除',
 * `order_type` tinyint(4) NOT NULL DEFAULT '-1' COMMENT '账户订单类型(0预付款 1后付款 -1未开通)',
 * `mid` int(11) NOT NULL DEFAULT '0' COMMENT '主站账号id',
 * `account_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0客户（空绑定账号） 1主站账号',
 * `active_time` datetime DEFAULT NULL COMMENT '激活时间（默认为空）',
 * `name` varchar(32) NOT NULL DEFAULT '' COMMENT '真实姓名（企业用户为公司名）',
 * `icp_record_number` varchar(64) NOT NULL DEFAULT '' COMMENT 'icp备案号',
 * `icp_info_image` varchar(255) NOT NULL DEFAULT '' COMMENT 'icp截图url',
 * `brand_domain` varchar(45) NOT NULL DEFAULT '' COMMENT '推广域名',
 * `user_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户属性 0个人用户 1 机构用户',
 * `ad_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '广告系统状态（-1未激活0允许 1禁止）',
 * `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号(mvcc)',
 * `category_first_id` int(11) NOT NULL DEFAULT '0' COMMENT '行业一级分类id',
 * `category_second_id` int(11) NOT NULL DEFAULT '0' COMMENT '行业二级分类id',
 * PRIMARY KEY (`account_id`),
 * KEY `ix_mid` (`mid`)
 * ) ENGINE=InnoDB AUTO_INCREMENT=10033 DEFAULT CHARSET=utf8;
 * Created by fanwenbin on 16/9/9.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountPo {
    private Integer accountId;
    private String username;
    private String mobile;
    private Integer status;
    private Long mid;
    private Integer accountType;
    private Timestamp activeTime;
    private String name;
    private String icpRecordNumber;
    private String icpInfoImage;
    private String brandDomain;
    private Integer userType;
    private Integer adStatus;
    private Integer version;
    private Integer categoryFirstId;
    private Integer categorySecondId;
    private String remark;
    private Integer isAgent;
    private Integer gdStatus;
    private Integer dependencyAgentId;
    private String companyName;
    private Integer isInner;
}
