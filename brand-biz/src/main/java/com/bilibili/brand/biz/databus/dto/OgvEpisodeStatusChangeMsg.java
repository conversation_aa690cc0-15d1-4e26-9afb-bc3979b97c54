package com.bilibili.brand.biz.databus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Ogv Episode 状态变更
 * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=695822359">Episode状态变更事件</a>
 * <p>
 * {
 * "entityChange":{
 * "entityId":"747723",
 * "entityType":"EPISODE",
 * "eventType":"STATUS_UPDATED",
 * "originValue":"ONLINE",
 * "payLoad":{
 * "aid":313057707,
 * "cid":1111691370,
 * "offlineRelated":false,
 * "seasonType":3,
 * "sectionId":93769
 * },
 * "value":"OFFLINE"
 * },
 * "entityId":"44473",
 * "entityType":"SEASON",
 * "time":"2023-05-05 16:00:11"
 * }
 *
 * <AUTHOR>
 * @date 2023/9/17 14:56
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OgvEpisodeStatusChangeMsg implements Serializable {
    private static final long serialVersionUID = 3906510397476601217L;

    /**
     * episode事件内容
     */
    private EpisodeChangeBody entityChange;
    /**
     * Season Id
     */
    private String entityId;
    /**
     * SEASON
     */
    private String entityType;
    /**
     * 业务状态发生改变的时间（yyyyMMDD hh:mm:ss）
     */
    private String time;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EpisodeChangeBody implements Serializable {
        private static final long serialVersionUID = -7360722924752609174L;
        /**
         * Episode Id
         */
        private String entityId;
        /**
         * EPISODE
         * PS:商业只关心EPISODE，而且已确认该消息只会存在EPISODE变更的消息
         */
        private String entityType;
        /**
         * 固定值：STATUS_UPDATED
         */
        private String eventType;
        /**
         * 改变后状态
         * 如上下架动作，可能包含以下几种状态
         * ONLINE/OFFLINE/DELETE
         */
        private String value;

        /**
         * 变更之前的值，具体值参考{@link #value}
         */
        private String originalValue;

        /**
         * 对象中包含相应的业务扩展信息，例如aid，cid 等
         * payload seasontype 为
         * ANIMATE(1, "番剧"),
         * MOVIE(2, "电影"),
         * DOCUMENTARY(3, "纪录片"),
         * CN_ANIMATE(4, "国漫"),
         * TV(5, "电视剧"),
         * VARIETY(7, "综艺");
         * <p>
         * PS:商业暂时不关心
         */
//    private PayLoad payLoad;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayLoad implements Serializable {
        private static final long serialVersionUID = 6980727609463433292L;
        private Long aid;
        private Long ci;
        private Boolean offlineRelated;
        private Integer seasonType;
        private Integer sectionId;
    }

}
