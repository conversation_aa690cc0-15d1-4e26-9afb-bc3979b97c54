package com.bilibili.brand.biz.schedule.context;

import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleDatePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo;
import com.bilibili.cpt.platform.biz.po.CptCrowdPackPo;
import com.bilibili.ssa.platform.biz.po.querydsl.GdBlockingInfoPo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@SuperBuilder
public class ScheduleContext implements Serializable {

    protected GdOrderDto gdOrderDto;

    protected GdOrderExtDto gdOrderExtDto;

    protected GdSchedulePo gdSchedulePo;

    protected List<GdBlockingInfoPo> gdBlockingInfoPoList;

    protected List<CptCrowdPackPo> cptCrowdPackPoList;

    protected List<GdScheduleDatePo> gdScheduleDatePoList;

    protected List<GdFlowAllocationPo> gdFlowAllocationPoList;

    protected List<GdScheduleTargetRatioPo> gdScheduleTargetRatioPoList;

    protected List<GdScheduleTargetPo> gdScheduleTargetPoList;
}
