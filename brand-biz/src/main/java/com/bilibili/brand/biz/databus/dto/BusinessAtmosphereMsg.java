package com.bilibili.brand.biz.databus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商业氛围Databus消息体
 *
 * <AUTHOR>
 * @date 2023/10/25 20:22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessAtmosphereMsg implements Serializable {
    private static final long serialVersionUID = 3611314714907096832L;
    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 内容类型，2：ogv，此时content_id是avid，5：ogv标板视频，此时content_id是season_id
     */
    private Integer contentType;

    /**
     * 一级商业类型，2：ogv
     */
    private Integer firstBusinessType;

    /**
     * 二级商业类型，6:ogv稿件,7:ogv标版视频
     */
    private Integer secondBusinessType;

    /**
     * 剧集开播时间（毫秒）
     */
    private Long seasonBeginTime;
}
