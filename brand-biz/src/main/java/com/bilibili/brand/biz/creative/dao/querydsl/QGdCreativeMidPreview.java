package com.bilibili.brand.biz.creative.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.brand.biz.creative.po.querydsl.GdCreativeMidPreviewPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QGdCreativeMidPreview is a Querydsl query type for GdCreativeMidPreviewPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QGdCreativeMidPreview extends com.querydsl.sql.RelationalPathBase<GdCreativeMidPreviewPo> {

    private static final long serialVersionUID = -832283879;

    public static final QGdCreativeMidPreview gdCreativeMidPreview = new QGdCreativeMidPreview("gd_creative_mid_preview");

    public final NumberPath<Long> creativeId = createNumber("creativeId", Long.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final NumberPath<Long> mid = createNumber("mid", Long.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> previewTime = createDateTime("previewTime", java.sql.Timestamp.class);

    public final NumberPath<Integer> salesType = createNumber("salesType", Integer.class);

    public final com.querydsl.sql.PrimaryKey<GdCreativeMidPreviewPo> primary = createPrimaryKey(id);

    public QGdCreativeMidPreview(String variable) {
        super(GdCreativeMidPreviewPo.class, forVariable(variable), "null", "gd_creative_mid_preview");
        addMetadata();
    }

    public QGdCreativeMidPreview(String variable, String schema, String table) {
        super(GdCreativeMidPreviewPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QGdCreativeMidPreview(String variable, String schema) {
        super(GdCreativeMidPreviewPo.class, forVariable(variable), schema, "gd_creative_mid_preview");
        addMetadata();
    }

    public QGdCreativeMidPreview(Path<? extends GdCreativeMidPreviewPo> path) {
        super(path.getType(), path.getMetadata(), "null", "gd_creative_mid_preview");
        addMetadata();
    }

    public QGdCreativeMidPreview(PathMetadata metadata) {
        super(GdCreativeMidPreviewPo.class, metadata, "null", "gd_creative_mid_preview");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mid, ColumnMetadata.named("mid").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(previewTime, ColumnMetadata.named("preview_time").withIndex(4).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(salesType, ColumnMetadata.named("sales_type").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

