package com.bilibili.brand.biz.resource.service;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.account.service.IAccountGroupMappingService;
import com.bilibili.brand.api.common.exception.ResourceExceptionCode;
import com.bilibili.brand.api.resource.cm.mark.CmMarkDto;
import com.bilibili.brand.api.resource.cm.mark.ICmMarkService;
import com.bilibili.brand.biz.resource.dao.AccountGroupCmMarkDao;
import com.bilibili.brand.biz.resource.dao.CmMarkDao;
import com.bilibili.brand.biz.resource.po.AccountGroupCmMarkPo;
import com.bilibili.brand.biz.resource.po.CmMarkPo;
import com.bilibili.cpt.platform.common.MarkType;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import com.bilibili.location.api.service.IBusMarkService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.google.common.base.Preconditions.checkState;

@Slf4j
@Service
public class CmMarkServiceImpl implements ICmMarkService {
	private final static Logger LOGGER = LoggerFactory.getLogger(CmMarkServiceImpl.class);
	
	private final static CmMarkDto DEFAULT_MARK = CmMarkDto.builder().id(1).name("广告").build();
	@Autowired
	private IAccountGroupMappingService accountGroupMappingService;

	@Autowired
	private CmMarkDao cmMarkDao;

	@Autowired
	private IBusMarkService busMarkService;

	@Autowired
	private AccountGroupCmMarkDao accountGroupCmMarkDao;

	@Override
	public List<CmMarkDto> getAll() {
		List<CmMarkPo> entities = cmMarkDao.getAllCmMarkList();
		checkState(entities!=null);
        return entities.stream().map(this::getCmMarkDto).collect(Collectors.toList());
	}

	@Override
	public CmMarkDto load(Integer cmMarkId) throws ServiceException {
	    
	    if (cmMarkId == null) {
            throw new ServiceException(ResourceExceptionCode.REQUIRED_PARAM);
        }
	    
		CmMarkPo entity = cmMarkDao.load(cmMarkId);
		if (entity == null) {
            throw new ServiceException(ResourceExceptionCode.CM_MARK_NO_EXIST);
        }

		return getCmMarkDto(entity);
	}

	@Override
	public Map<Integer, CmMarkDto> getCmMarkMapByAccountId(Integer accountId, Integer salesType) throws ServiceException {
	    if (accountId == null) {
            throw new ServiceException(ResourceExceptionCode.REQUIRED_PARAM);
        }
	    
		List<Integer> accountGroupIds = accountGroupMappingService.getAccountGroupIdsByAccountId(accountId);
		if (CollectionUtils.isEmpty(accountGroupIds)) {
            LOGGER.warn("getCmMarkMapByAccount: getAccountGroupIdsByAccountId result is null , add a default cm mark.");
            return getDefaultCmMarkMap();
        }
		
		List<AccountGroupCmMarkPo> cmMarkPos = accountGroupCmMarkDao.getAccountGroupCmMarkByAccountGroupIds(accountGroupIds);
		if (CollectionUtils.isEmpty(cmMarkPos)) {
            LOGGER.warn("getCmMarkMapByAccount: getCmMarkIdsByAccountGroupIds result is null , add a default cm mark.");
		    return getDefaultCmMarkMap();
		}
		log.info("getCmMarkMapByAccountId  salesType : [{}], cmMarkPos : [{}], accountGroupIds [{}]",
				salesType, cmMarkPos, accountGroupIds);
		if(salesType != null && SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode() == salesType){
			//gd+只支持新版的广告标
			cmMarkPos = cmMarkPos.stream().filter(t->
					MarkType.BUS_MARK.getCode().equals(t.getMarkType()))
					.collect(Collectors.toList());
		}else if(salesType != null && SalesType.CPT.getCode() == salesType){
			//老版cpt不支持新版商业标
			cmMarkPos = cmMarkPos.stream()
					.filter(t-> t.getCmMarkId() != 1 || !MarkType.BUS_MARK.getCode().equals(t.getMarkType()))
					.collect(Collectors.toList());
		}
		log.info("getCmMarkMapByAccountId filter cmMarkPos: [{}]", cmMarkPos);

		List<Integer> cmMarkIds = cmMarkPos.stream()
				.filter(t-> MarkType.CM_MARK.getCode().equals(t.getMarkType()))
				.map(AccountGroupCmMarkPo::getCmMarkId).collect(Collectors.toList());

		List<Integer> busMarkIds = cmMarkPos.stream()
				.filter(t-> MarkType.BUS_MARK.getCode().equals(t.getMarkType()))
				.map(AccountGroupCmMarkPo::getCmMarkId).collect(Collectors.toList());
		log.info("getCmMarkMapByAccountId cmMarkIds: [{}], busMarkIds [{}]", cmMarkIds, busMarkIds);
		List<CmMarkPo> markPos = null;
		if(!CollectionUtils.isEmpty(cmMarkIds)){
			markPos = cmMarkDao.getCmMarkList(cmMarkIds);
		}
		Map<Integer, BusMarkDto> busMarkMap = null;
		if(!CollectionUtils.isEmpty(busMarkIds)){
			busMarkMap = busMarkService.getBusMarkMap(busMarkIds);
		}

        if (CollectionUtils.isEmpty(markPos) && CollectionUtils.isEmpty(busMarkMap)) {
            LOGGER.warn("getCmMarkMapByAccount: get null or empty cm mark, add a default cm mark.");
            return getDefaultCmMarkMap();
        }
		Map<Integer, CmMarkDto> map = new HashMap<>();
        if(!CollectionUtils.isEmpty(markPos)){
			map = markPos.stream().collect(Collectors.toMap(CmMarkPo::getId, this::getCmMarkDto));
		}

		Map<Integer, CmMarkDto> map1 = new HashMap<>();
		if(!CollectionUtils.isEmpty(busMarkMap)){
			map1 = busMarkMap.values().stream()
					.collect(Collectors.toMap(BusMarkDto::getId, this::getBusMarkDto));
		}
		map.putAll(map1);

        return map;
	}

    private Map<Integer, CmMarkDto> getDefaultCmMarkMap() {
        Map<Integer, CmMarkDto> cmMarkDtoMap = Maps.newHashMap();
        cmMarkDtoMap.put(DEFAULT_MARK.getId(), DEFAULT_MARK);
        return cmMarkDtoMap;
    }


	public List<CmMarkDto> getCmMarkListByCmMarkIds(List<Integer> cmMarkIds) throws ServiceException {
	    if (CollectionUtils.isEmpty(cmMarkIds)) {
            throw new ServiceException(ResourceExceptionCode.REQUIRED_PARAM);
        }
	    
		List<CmMarkPo> entities = cmMarkDao.getCmMarkList(cmMarkIds);
		if (CollectionUtils.isEmpty(entities)) {
            return Collections.emptyList();
        }

		return entities.stream().map(this::getCmMarkDto).collect(Collectors.toList());
	}

	private CmMarkDto getCmMarkDto(CmMarkPo cmMarkPo) {
		return CmMarkDto.builder().id(cmMarkPo.getId()).markType(MarkType.CM_MARK.getCode()).name(cmMarkPo.getName()).build();
	}

	private CmMarkDto getBusMarkDto(BusMarkDto cmMarkPo) {
		return CmMarkDto.builder().id(cmMarkPo.getId()).markType(MarkType.BUS_MARK.getCode()).name(cmMarkPo.getName()).build();
	}
	
}
