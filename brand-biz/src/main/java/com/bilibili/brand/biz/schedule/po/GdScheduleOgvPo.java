package com.bilibili.brand.biz.schedule.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdScheduleOgvPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 排期id
     */
    private Integer scheduleId;

    /**
     * 投放类型，0:season,1:episode
     */
    private Integer launchType;

    /**
     * episode id,如果launch_type=0，则该字段为0
     */
    private Long episodeId;

    /**
     * season id，无论launch_type如何，该字段始终有效
     */
    private Long seasonId;

    /**
     * 软删
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}