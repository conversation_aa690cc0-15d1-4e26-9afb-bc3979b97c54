package com.bilibili.brand.biz.deduction.handler.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderConsumptionBean {
    private Integer accountId;
    private Integer orderId;
    private Integer sourceId;
    private Integer showCount;
    // 闪屏刊例价异价
    private Integer showStyle;
    private Integer clickArea;
}
