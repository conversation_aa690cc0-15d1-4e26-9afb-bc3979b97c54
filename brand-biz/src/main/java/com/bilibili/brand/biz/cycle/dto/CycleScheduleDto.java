package com.bilibili.brand.biz.cycle.dto;

import com.bilibili.adp.common.util.Utils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/7/24
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CycleScheduleDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id 无特殊含义
     */
    private Long id;
    /**
     * 排期ID
     */
    private Integer cycleId;

    /**
     * 订单类型
     */
    private Integer orderProduct;

    /**
     * 定时开刊时间
     */
    private Timestamp executionTime;

    public void validate() {
        Assert.isTrue(Utils.isPositive(cycleId), "刊例周期不能为空");
        Assert.isTrue(Utils.isPositive(orderProduct), "订单类型不能为空");
        Assert.notNull(executionTime, "定时执行时间不能为空");
    }

    public boolean needUpdate(CycleScheduleDto old) {
        return Objects.equals(old.getExecutionTime(), this.executionTime);
    }
}
