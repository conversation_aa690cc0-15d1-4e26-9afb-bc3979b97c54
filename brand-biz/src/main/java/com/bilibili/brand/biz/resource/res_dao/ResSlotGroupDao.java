package com.bilibili.brand.biz.resource.res_dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.resource.pojo.ResSlotGroupPo;
import com.bilibili.brand.biz.resource.pojo.ResSlotGroupPoExample;

public interface ResSlotGroupDao {
    long countByExample(ResSlotGroupPoExample example);

    int deleteByExample(ResSlotGroupPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(ResSlotGroupPo record);

    int insertSelective(ResSlotGroupPo record);

    List<ResSlotGroupPo> selectByExample(ResSlotGroupPoExample example);

    ResSlotGroupPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") ResSlotGroupPo record, @Param("example") ResSlotGroupPoExample example);

    int updateByExample(@Param("record") ResSlotGroupPo record, @Param("example") ResSlotGroupPoExample example);

    int updateByPrimaryKeySelective(ResSlotGroupPo record);

    int updateByPrimaryKey(ResSlotGroupPo record);
}