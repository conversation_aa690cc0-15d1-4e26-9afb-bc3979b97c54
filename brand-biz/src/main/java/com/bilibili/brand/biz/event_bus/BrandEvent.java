package com.bilibili.brand.biz.event_bus;

import java.util.EventObject;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/9/7 14:48
 */
public abstract class BrandEvent extends EventObject {
    private static final long serialVersionUID = 918587240336690753L;

    /**
     * Constructs a prototypical Event.
     *
     * @param source The object on which the Event initially occurred.
     * @throws IllegalArgumentException if source is null.
     */
    public BrandEvent(Object source) {
        super(source);
    }

    public BrandEvent() {
        super("");
    }


    public enum ActionType {
        //添加
        ADD,
        //修改
        MODIFY,
        //删除
        REMOVE,
        //禁止
        DISABLE,
        //启用
        ENABLE,
        //开始
        START,
        //结束
        END,
        //刷新（也许数据本身没有任何变化）
        REFRESH;

        public static BrandEvent.ActionType getActionTypeWithSimple(String action) {
            if (Objects.equals(action, "insert")) {
                return BrandEvent.ActionType.ADD;
            }
            if (Objects.equals(action, "update")) {
                return BrandEvent.ActionType.MODIFY;
            }
            if (Objects.equals(action, "delete")) {
                return BrandEvent.ActionType.REMOVE;
            }
            return null;
        }
    }
}
