package com.bilibili.brand.biz.creative.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.enums.AppPlatformType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.common.enums.SsaTransitionModeEnum;
import com.bilibili.brand.api.creative.dto.NewExternalTopViewDto;
import com.bilibili.brand.api.creative.dto.TopViewHfJumpDto;
import com.bilibili.brand.api.creative.dto.TransitionVideoDto;
import com.bilibili.brand.api.creative.dto.UpdateExternalTopViewDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.material.IIPVideoService;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.schedule.dao.GdTopViewDao;
import com.bilibili.brand.biz.schedule.po.GdTopViewPo;
import com.bilibili.brand.biz.schedule.po.GdTopViewPoExample;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.cpt.platform.api.creative.dto.CptJumpDTO;
import com.bilibili.enums.GdJumpType;
import com.bilibili.enums.WakeAppType;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenJumpDTO;
import com.bilibili.ssa.platform.biz.enumerate.PlatformType;
import com.bilibili.ssa.platform.common.enums.SsaJumpType;
import com.bilibili.ssa.platform.common.enums.TopViewCreativeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/29 17:04
 */
@Slf4j
@Component
public class TopViewCreativeValidator {

    @Autowired
    private ISoaAppPackageService soaAppPackageService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private GdTopViewDao gdTopViewDao;

    @Autowired
    private IGdOrderService orderService;

    @Autowired
    private IIPVideoService ipVideoService;

    public TopViewCreativeValidationResult validateNew(NewExternalTopViewDto dto, Operator operator) {
        Assert.notNull(dto, "创意信息不可为空");
        Assert.notNull(dto.getScheduleId(), "排期id不可为空");
        Assert.notNull(operator, "操作人不可为空");
        ScheduleDto scheduleDto;
        try {
            scheduleDto = queryScheduleService.getScheduleById(dto.getScheduleId());
        } catch (ServiceException e) {
            throw new IllegalArgumentException(e.getMessage());
        }
        Assert.isTrue(scheduleDto.getAccountId().equals(operator.getOperatorId()), "只能操作自己的订单");
        GdTopViewScheduleDto topViewScheduleDto = queryScheduleService.getTopViewScheduleInfo(dto.getScheduleId());
        Assert.notNull(topViewScheduleDto, "TopView排期不存在");

        SsaJumpType ssaJumpType = SsaJumpType.getByCode(dto.getJumpType());

        //1、校验落落地页下载
        Map<Integer, Integer> ssaPlatformAppPackageMap = this.validatePackage(true, ssaJumpType.getCode(), dto.getAppPackageIds(),
                operator.getOperatorId(), topViewScheduleDto);
        TopViewHfJumpDto hfJump = dto.getHfJump();
        Map<Integer, Integer> hfPlatformAppPackageMap = this.validatePackage(false, hfJump.getJumpType().getCode(),
                hfJump.getAppPackageIds(), operator.getOperatorId(), topViewScheduleDto);

        //2、校验跳转信息

        validateJump(WakeAppType.getByCode(scheduleDto.getWakeAppType()), WakeAppType.getByCode(dto.getWakeAppType()),
                WakeAppType.getByCode(hfJump.getWakeAppType()), dto.getMiniProgram(), hfJump.getMiniProgram(),
                dto.getButtonBOS().get(0).getSplashScreenJumpDTOS(), hfJump.getJumps());

        //3、校验过渡视频
        validateTransitionVideo(dto.getTransitionVideo(), topViewScheduleDto);

        return TopViewCreativeValidationResult.builder()
                .schedule(scheduleDto)
                .topViewSchedule(topViewScheduleDto)
                .ssaPlatformAppPackageMap(ssaPlatformAppPackageMap)
                .hfPlatformAppPackageMap(hfPlatformAppPackageMap)
                .build();
    }

    public TopViewCreativeValidationResult validateUpdate(UpdateExternalTopViewDto dto, Operator operator) {
        Assert.notNull(dto, "创意信息不可为空");
        Assert.notNull(dto.getTopViewId(), "TopViewId不可为空");
        Assert.notNull(operator, "操作人不可为空");

        GdTopViewPo topViewPo = getGdTopViewPoById(dto.getTopViewId());
        TopViewCreativeStatus oldStatus = TopViewCreativeStatus.getByCodeWithValidation(topViewPo.getStatus());
        Assert.isTrue(!TopViewCreativeStatus.CAN_NOT_MODIFY_STATUS.contains(topViewPo.getStatus()),
                "状态为" + oldStatus.getDesc() + "的创意不可编辑");

        ScheduleDto scheduleDto;
        try {
            scheduleDto = queryScheduleService.getScheduleById(topViewPo.getGdScheduleId());
        } catch (ServiceException e) {
            throw new IllegalArgumentException(e.getMessage());
        }

        Assert.isTrue(scheduleDto.getAccountId().equals(operator.getOperatorId()), "只能操作自己的订单");
        GdOrderDto orderDto = orderService.getOrderById(topViewPo.getGdOrderId());
        GdTopViewScheduleDto topViewScheduleDto = queryScheduleService.getTopViewScheduleInfo(topViewPo.getGdScheduleId());
        Assert.notNull(topViewScheduleDto, "TopView排期不存在");
        SsaJumpType ssaJumpType = SsaJumpType.getByCode(dto.getJumpType());
        //1、校验落落地页下载
        Map<Integer, Integer> ssaPlatformAppPackageMap = this.validatePackage(true, ssaJumpType.getCode(), dto.getAppPackageIds(),
                operator.getOperatorId(), topViewScheduleDto);

        TopViewHfJumpDto hfJump = dto.getHfJump();
        Map<Integer, Integer> hfPlatformAppPackageMap = this.validatePackage(false, hfJump.getJumpType().getCode(),
                hfJump.getAppPackageIds(), operator.getOperatorId(), topViewScheduleDto);

        //2、校验跳转信息
        validateJump(WakeAppType.getByCode(scheduleDto.getWakeAppType()), WakeAppType.getByCode(dto.getWakeAppType()),
                WakeAppType.getByCode(hfJump.getWakeAppType()), dto.getMiniProgram(), hfJump.getMiniProgram(),
                dto.getButtonBOS().get(0).getSplashScreenJumpDTOS(), hfJump.getJumps());

        //3、校验过渡视频
        validateTransitionVideo(dto.getTransitionVideo(), topViewScheduleDto);

        return TopViewCreativeValidationResult.builder()
                .order(orderDto)
                .topView(topViewPo)
                .schedule(scheduleDto)
                .topViewSchedule(topViewScheduleDto)
                .ssaPlatformAppPackageMap(ssaPlatformAppPackageMap)
                .hfPlatformAppPackageMap(hfPlatformAppPackageMap)
                .build();
    }

    private GdTopViewPo getGdTopViewPoById(Integer topViewId) {
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(topViewId);
        List<GdTopViewPo> poList = gdTopViewDao.selectByExample(example);
        Assert.notEmpty(poList, "TopView不存在");
        return poList.get(0);
    }

    private Map<Integer, Integer> validatePackage(boolean isSsa, Integer jumpType, List<Integer> appPackageIds,
                                                  Integer accountId, GdTopViewScheduleDto topViewSchedule) {
        Map<Integer, Integer> platformAppPackageMap = new HashMap<>(2);
        boolean isH5Download = isSsa ? Objects.equals(jumpType, SsaJumpType.H5_DOWNLOAD.getCode())
                : Objects.equals(jumpType, GdJumpType.H5_DOWNLOAD.getCode());
        //【品牌】TopView创意层级投放应用下载&投放交互优化
        //https://www.tapd.cn/********/prong/stories/view/**********004170445
        if (isH5Download) {
            if (!CollectionUtils.isEmpty(appPackageIds)) {
                //处理应用包, 排除无效的应用包选项，比如游戏推广时安卓会跳转到游戏中心，而不是单独的应用包，此时AppPackageId=0
                //https://www.tapd.bilibili.co/********/prong/stories/view/**********003002772
                List<Integer> validApps = appPackageIds.stream().filter(Utils::isPositive).collect(Collectors.toList());
                Map<Integer, AppPackageDto> appPackageMap = soaAppPackageService.getAllMapInPrimaryKeys(validApps);
                Assert.notEmpty(appPackageMap, "存在无效的APP Id");
                for (Integer appPackageId : validApps) {
                    AppPackageDto appPackageDto = appPackageMap.get(appPackageId);
                    Assert.notNull(appPackageDto, "存在无效的APP Id");
                    Assert.isTrue(accountId.equals(appPackageDto.getAccountId()),
                            "该AppId:[" + appPackageId + "]不属于您");
                    Assert.isTrue(AppPackageStatus.VALID.getCode() == appPackageDto.getStatus(),
                            "该AppId:[" + appPackageId + "]状态为无效");

                    AppPlatformType appPlatformType = AppPlatformType.getByCode(appPackageDto.getPlatform());
                    Integer platformTypeCode = PlatformType.getByAppPlatform(appPlatformType).getCode();
                    platformAppPackageMap.put(platformTypeCode, appPackageId);
                }
            } else {
                //兼容存量逻辑
                platformAppPackageMap.put(PlatformType.IPHONE.getCode(), topViewSchedule.getIosAppPackageId());
                platformAppPackageMap.put(PlatformType.ANDROID.getCode(), topViewSchedule.getAndroidAppPackageId());
            }
        }
        return platformAppPackageMap;
    }


    //https://www.tapd.cn/********/prong/stories/view/**********004523310
    //【品牌】TopView支持闪屏、首焦分开跳转-香奈儿3月商机
    private void validateJump(WakeAppType scheduleWakeAppType, WakeAppType ssaWakeAppType, WakeAppType hfWakeAppType,
                              MiniProgramDto ssaMiniProgram, MiniProgramDto hfMiniProgram, List<SplashScreenJumpDTO> ssaJumpList,
                              List<CptJumpDTO> hfJumpList) {
        BiFunction<String, String, Boolean> packageChecker = (packageName, schemaUrl) -> StringUtils.hasText(packageName)
                || StringUtils.hasText(schemaUrl);
        if (Objects.equals(scheduleWakeAppType, WakeAppType.NO)) {
            //唤起应用和小程序必须都为空

            Assert.isTrue(Objects.isNull(ssaMiniProgram) || !ssaMiniProgram.isValid(), "当前排期不支持唤起小程序");
            Assert.isTrue(Objects.isNull(hfMiniProgram) || !hfMiniProgram.isValid(), "当前排期不支持唤起小程序");

            boolean ssaNoPackage = ssaJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
            Assert.isTrue(ssaNoPackage, "当前排期不支持唤起应用");
            boolean hfNoPackage = hfJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
            Assert.isTrue(hfNoPackage, "当前排期不支持唤起应用");

        } else if (Objects.equals(scheduleWakeAppType, WakeAppType.APP)) {
            //唤起应用可以为空（可唤可不唤）
            //唤起小程序必须为空

            Assert.isTrue(Objects.isNull(ssaMiniProgram) || !ssaMiniProgram.isValid(), "当前排期不支持唤起小程序");
            Assert.isTrue(Objects.isNull(hfMiniProgram) || !hfMiniProgram.isValid(), "当前排期不支持唤起小程序");

        } else if (Objects.equals(scheduleWakeAppType, WakeAppType.MINI_PROGRAM)) {
            //唤起应用必须为空
            //唤起小程序必须存在

            Assert.isTrue(Objects.nonNull(ssaMiniProgram) && ssaMiniProgram.isValid(), "当前排期唤起小程序信息必填");
            Assert.isTrue(Objects.nonNull(hfMiniProgram) && hfMiniProgram.isValid(), "当前排期唤起小程序信息必填");

            boolean ssaNoPackage = ssaJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
            Assert.isTrue(ssaNoPackage, "当前排期不支持唤起应用");
            boolean hfNoPackage = hfJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
            Assert.isTrue(hfNoPackage, "当前排期不支持唤起应用");

        } else if (Objects.equals(scheduleWakeAppType, WakeAppType.APP_OR_MINI_PROGRAM)) {
            //可能是唤起应用，也可能是唤起小程序，二者一定互斥

            //闪屏部分
            if (Objects.equals(ssaWakeAppType, WakeAppType.APP)) {
                //app可以为空，但不能选择小程序
                Assert.isTrue(Objects.isNull(ssaMiniProgram) || !ssaMiniProgram.isValid(), "当前唤起选项不支持唤起小程序，请选择后重试");
            } else {
                //否则一定认为是小程序
                boolean ssaNoPackage = ssaJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
                Assert.isTrue(ssaNoPackage, "当前唤起选项不支持唤起应用");
                Assert.isTrue(Objects.nonNull(ssaMiniProgram) && ssaMiniProgram.isValid(), "当前唤起选项仅支持唤起小程序，请选择后重试");
            }

            //首焦部分
            if (Objects.equals(hfWakeAppType, WakeAppType.APP)) {
                //app可以为空，但不能选择小程序
                Assert.isTrue(Objects.isNull(hfMiniProgram) || !hfMiniProgram.isValid(), "当前唤起选项不支持唤起小程序，请选择后重试");
            } else {
                //否则一定认为是小程序
                boolean hfNoPackage = hfJumpList.stream().noneMatch(jump -> packageChecker.apply(jump.getPackageName(), jump.getSchemeUrl()));
                Assert.isTrue(hfNoPackage, "当前唤起选项不支持唤起应用");
                Assert.isTrue(Objects.nonNull(hfMiniProgram) && hfMiniProgram.isValid(), "当前唤起选项仅支持唤起小程序，请选择后重试");
            }
        }
    }

    /**
     * 校验过渡视频
     */
    private void validateTransitionVideo(TransitionVideoDto transitionVideo, GdTopViewScheduleDto topViewSchedule) {
        if (transitionVideo == null || transitionVideo.getBizId() == null) {
            return;
        }

        // 只有当排期的过渡形式为【自定义过渡】时，才需要校验过渡视频
        if (!SsaTransitionModeEnum.isCustom(topViewSchedule.getTransitionMode())) {
            return;
        }

        IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(transitionVideo.getBizId());
        Assert.notNull(ipVideoBo, "过渡视频不存在");

        // 校验时长：必须小于1s (1000ms)
        Assert.isTrue(ipVideoBo.getDuration() < 1000,
                      "过渡视频时长必须小于1s，当前时长：" + ipVideoBo.getDuration() + "ms");

        // 校验大小：必须小于3MB
        long maxSize = 3 * 1024 * 1024; // 3MB
        Assert.isTrue(ipVideoBo.getSize() <= maxSize,
                      "过渡视频大小必须小于3MB，当前大小：" + (ipVideoBo.getSize() / 1024 / 1024) + "MB");
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TopViewCreativeValidationResult {
        private Map<Integer, Integer> ssaPlatformAppPackageMap;
        private GdOrderDto order;
        private ScheduleDto schedule;
        private GdTopViewScheduleDto topViewSchedule;
        private GdTopViewPo topView;
        private Map<Integer, Integer> hfPlatformAppPackageMap;
    }
}
