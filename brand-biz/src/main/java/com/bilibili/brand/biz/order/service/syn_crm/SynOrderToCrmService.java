package com.bilibili.brand.biz.order.service.syn_crm;

import com.bilibili.brand.api.common.SynCrmService;
import com.bilibili.brand.api.common.enums.GdOrderStatus;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.brand.biz.utils.BrandLittleAssistantUtil;
import com.bilibili.cpt.platform.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.LockSupport;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Slf4j
@Service
public class SynOrderToCrmService {

    @Autowired
    private GdOrderService orderService;

    @Autowired
    private List<SynCrmService> services;

    public void synCrm(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        Map<Integer, GdOrderDto> orderMap = orderService.getOrderMapInOrderIds(orderIds);

        Map<Integer, List<GdOrderDto>> productOrderMap = orderMap.values().stream()
                .filter(order -> GdOrderStatus.UPDATE_ORDER_STATUS_LIST.contains(order.getGdOrderStatus()))
                .collect(Collectors.groupingBy(GdOrderDto::getProduct));

        productOrderMap.forEach((orderProduct, orders) -> {

            SynCrmService matchService = findMatchService(orderProduct);
            if (matchService == null) {
                return;
            }
            orders.forEach(orderDto -> {
                //错误重试：防止网络抖动导致的失败
                for (int i = 0; i < 5; i++) {
                    try {
                        //超出重试后ignore异常：方式因为数据本身的错误导致，导致job被阻塞
                        matchService.synCrm(orderDto);
                        break;
                    } catch (Exception e) {
                        log.error("[SynOrderToCrmService]:synCrm error,orderId:{},retry:{}", orderDto.getOrderId(), i, e);
                        //todo:如何感知失败的case
                        LockSupport.parkNanos(TimeUnit.MILLISECONDS.toNanos(500));
                        if (i == 4) {
                            BrandLittleAssistantUtil.sendWithMarkdown(
                                    BrandLittleAssistantUtil.GroupKey.BRAND_DEVELOPER_GROUP_KEY,
                                    String.format("同步订单信息到CRM失败，订单ID=%d，机器IP=%s",
                                            orderDto.getOrderId(), IpUtil.getIp()));
                        }
                    }
                }
            });
        });
    }

    private SynCrmService findMatchService(Integer orderProduct) {
        if (orderProduct == null) {
            return null;
        }

        return services.stream()
                .filter(service -> service.isMatch(orderProduct))
                .findAny()
                .orElse(null);
    }

}
