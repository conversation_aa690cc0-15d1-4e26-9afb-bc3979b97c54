package com.bilibili.brand.biz.utils;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description cpt配置工具类
 * <AUTHOR>
 * @Date 2020.05.28 15:53
 */
@Service
public class CptConfigUtil {
    /*
     * 可以跳过校验的universal链接列表
     */
    @Value("${cpt.skip.valid.urls}")
    private String skipValidUrls;

    /*
     * universalLink-AppSchema
     */
    @Value("${cpt.url.mapping.universal.app}")
    private String mappingUniversals;


    public boolean containSkipValidUrl(String url){
        if(StringUtils.isEmpty(url)){
            return false;
        }
        String[] skipValidUrlList = skipValidUrls.split(",");
        for(String shipUrl : skipValidUrlList){
            if(url.contains(shipUrl)){
                return true;
            }
        }
        return false;
    }

    public String getAppSchema(String universalDomain){
        if(StringUtils.isEmpty(universalDomain)){
            return "";
        }
        Map<String, String> mappingUniversalMap = new HashMap<>();
        String[] temp = mappingUniversals.split(",");
        for(String mapping : temp){
            String[] mappings = mapping.split("\\|");
            mappingUniversalMap.put(mappings[0], mappings[1]);
        }
        for (Map.Entry<String, String> entry : mappingUniversalMap.entrySet()){
            if(universalDomain.contains(entry.getKey())){
                return entry.getValue();
            }
        }
       return "";
    }
}
