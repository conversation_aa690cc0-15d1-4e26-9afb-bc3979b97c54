package com.bilibili.brand.biz.account.service;

import com.bilibili.adp.common.bean.MineInfo;
import com.bilibili.brand.api.common.enums.AccountAdStatus;
import com.bilibili.brand.api.common.enums.AccountStatus;
import com.bilibili.brand.api.common.exception.AccountExceptionCode;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.TokenUtil;
import com.bilibili.brand.biz.account.dao.AccountBiliUserDao;
import com.bilibili.brand.biz.account.po.AccountBiliUserPo;
import com.bilibili.brand.api.account.dto.AccountDto;
import com.bilibili.brand.api.account.dto.LoginInfoDto;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.account.service.ISessionService;
import com.nimbusds.jose.JOSEException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * Created by fanwenbin on 2017/2/10.
 */
@Service
public class SessionService implements ISessionService {
    private final static Logger LOGGER = LoggerFactory.getLogger(SessionService.class);
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private AccountBiliUserDao accountBiliUserDao;
    @Override
    public LoginInfoDto biliUserLogin(Integer accountId, String biliUsername, boolean isAdmin, SystemType systemType) throws ServiceException {
        if (null == accountId || StringUtils.isEmpty(biliUsername) || null == systemType) {
            LOGGER.info("AccountService.biliUserLogin param is null");
            throw new ServiceException(AccountExceptionCode.REQUIRED_PARAM);
        }
        if (!isAdmin) {
            List<AccountBiliUserPo> list = accountBiliUserDao.getListByBiliUsernameAndAccountId(biliUsername, accountId);
            if (CollectionUtils.isEmpty(list)) {
                throw new ServiceException(AccountExceptionCode.HAS_NO_AUTH);
            }
        }
        AccountDto account = queryAccountService.getAccount(accountId);
        if (null == account) {
            LOGGER.info("illegal accountId{}", accountId);
            throw new ServiceException(AccountExceptionCode.NOT_EXIST_ACCOUNT);
        }
        validateLoginValid(account, systemType);

        MineInfo mineInfo = MineInfo.builder()
                .accountId(account.getAccountId())
                .accountName(account.getUsername())
                .proxyId(0L)
                .proxyName(biliUsername)
                .type(OperatorType.BILIBILIER.getCode())
                .build();
        return this.buildLoginInfo(mineInfo);
    }

    private LoginInfoDto buildLoginInfo(MineInfo mineInfo) throws ServiceException {
        Assert.isTrue(!MineInfo.validateParamIsNull(mineInfo));
        String token;
        try {
            token = TokenUtil.createToken(mineInfo);
        } catch (JOSEException e) {
            LOGGER.error("TokenUtil.createToken.error", e);
            throw new ServiceException(AccountExceptionCode.SYSTEM_ERROR);
        }
        return LoginInfoDto.builder().accountId(mineInfo.getAccountId()).accessToken(token).build();
    }

    private void validateLoginValid(AccountDto account, SystemType systemType) {
        Assert.notNull(account);
        Assert.notNull(systemType);
        Assert.isTrue(AccountStatus.ON.equals(AccountStatus.getByCode(account.getStatus())), "对不起，该客户账号尚未启用，请联系运营人员！");
        if (SystemType.CPM.equals(systemType)){
            AccountAdStatus adStatus = AccountAdStatus.getByCode(account.getAdStatus());
            Assert.isTrue(AccountAdStatus.ENABLE.equals(adStatus), "对不起，该客户账号未设置允许投放效果广告, 请联系运营人员！");
        }else if (SystemType.GD.equals(systemType)){
            AccountAdStatus gdStatus = AccountAdStatus.getByCode(account.getGdStatus());
            Assert.isTrue(AccountAdStatus.ENABLE.equals(gdStatus), "对不起，该客户账号未设置允许投放品牌广告, 请联系运营人员！");
        }else {
            Assert.isTrue(false, String.format("不支持的该系统类型(%s)登录", systemType.getDesc()));
        }
    }
}
