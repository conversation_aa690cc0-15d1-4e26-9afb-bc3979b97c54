package com.bilibili.brand.biz.utils;

import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.ssa.platform.biz.bean.CoverFormattedUrlDto;
import com.google.gson.reflect.TypeToken;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 *  <a href="https://info.bilibili.co/pages/viewpage.action?pageId=822414128">bvc获取封面对应文档</a>
 */
@Component
public class OssStorageUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(OssStorageUtil.class);

    @Value("${oss.bvc.video.cover.api.url:http://uat-bvcflow-executor-go.bilibili.co}")
    private String videoCoverApiUrl;

    public CoverFormattedUrlDto getVideoCoverUrl(String ossKey) {
        Assert.hasText(ossKey, "ossKey不可为空");
        try {
            String path = String.format("/api/call/info/Query/%s/videocovers_format", getOssFileName(ossKey));
            CoverFormattedUrlDto response = OkHttpUtils.formPost(videoCoverApiUrl + path)
                    .callForObject(new TypeToken<CoverFormattedUrlDto>() {
                    });
            if (response == null
                    || response.getData() == null
                    || response.getData().getCount() == null) {
                LOGGER.error("getVideoCoverUrl error ossKey {}", ossKey);
                throw new RuntimeException("调用OSS服务获取封面URL失败");
            }

            return response;
        } catch (Exception e) {
            LOGGER.error("getVideoCoverUrl error ossKey {}", ossKey, e);
            throw new RuntimeException("调用OSS服务获取封面URL失败");
        }
    }

    private String getOssFileName(String ossKey) {
        String[] strArray = ossKey.split("/");
        return strArray[strArray.length - 1].split("\\.")[0];
    }
}
