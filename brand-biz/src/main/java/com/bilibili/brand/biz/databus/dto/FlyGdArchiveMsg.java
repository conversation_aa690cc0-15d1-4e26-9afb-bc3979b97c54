package com.bilibili.brand.biz.databus.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/26 13:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlyGdArchiveMsg implements Serializable {
    private static final long serialVersionUID = -3070460308667818570L;
    /**
     * 稿件avid
     */
    private Long av_id;

    /**
     * 消息目的 1去除淘口令/京东口令 2舆情管控
     * 起飞稿件默认传1
     */
    private List<Integer> target_type;
}
