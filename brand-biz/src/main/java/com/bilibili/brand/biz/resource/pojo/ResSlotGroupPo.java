package com.bilibili.brand.biz.resource.pojo;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class ResSlotGroupPo implements Serializable {
    private Integer id;

    /**
     * 广告位组名称
     */
    private String slotGroupName;

    /**
     * 所属渠道ID
     */
    private Integer channelId;

    /**
     * 广告位集合
     */
    private String slotIds;

    /**
     * 模板集合
     */
    private String templateIds;

    /**
     * 状态（1-有效，2-无效）
     */
    private Integer status;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSlotGroupName() {
        return slotGroupName;
    }

    public void setSlotGroupName(String slotGroupName) {
        this.slotGroupName = slotGroupName;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getSlotIds() {
        return slotIds;
    }

    public void setSlotIds(String slotIds) {
        this.slotIds = slotIds;
    }

    public String getTemplateIds() {
        return templateIds;
    }

    public void setTemplateIds(String templateIds) {
        this.templateIds = templateIds;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }
}