package com.bilibili.brand.biz.resource.dao;

import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.resource.po.ChannelPo;

import java.util.List;

/**
 * Created by walker on 16/9/13.
 */
public interface ChannelDao {

    int insert(@Param("entity") ChannelPo channelEntity);

    ChannelPo load(@Param("channelId") Integer channelId);

    List<ChannelPo> query();

    List<ChannelPo> getAll();

    ChannelPo getById(@Param("id") Integer id);

    void update(@Param("entity") ChannelPo po);
    
    List<ChannelPo> getAllValid();
}
