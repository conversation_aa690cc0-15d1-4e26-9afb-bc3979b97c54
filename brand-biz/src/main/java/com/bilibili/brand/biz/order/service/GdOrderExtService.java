package com.bilibili.brand.biz.order.service;

import cn.hutool.core.collection.CollUtil;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.GdOrderLaunchTimeType;
import com.bilibili.brand.api.common.enums.GdOrderSource;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.ShowPriorityType;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.dto.UpdateOrderDto;
import com.bilibili.brand.api.order.service.ICycleFrequencyService;
import com.bilibili.brand.api.order.service.IGdOrderExtService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.order.converter.GdOrderExtConverter;
import com.bilibili.brand.biz.order.dao.FcGdOrderExtDao;
import com.bilibili.brand.biz.order.po.FcGdOrderExtPo;
import com.bilibili.brand.biz.order.po.FcGdOrderExtPoExample;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.brand.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
@Slf4j
@Service
public class GdOrderExtService implements IGdOrderExtService {

    @Autowired
    private FcGdOrderExtDao fcGdOrderExtDao;

    @Autowired
    private ICycleFrequencyService cycleFrequencyService;

    @Autowired
    private QueryScheduleService queryScheduleService;

    @Override
    public void saveGdOrderExtInfo(int orderId, GdOrderExtDto gdOrderExtDto) {
        // mapper转换
        FcGdOrderExtPo updatePo = GdOrderExtConverter.MAPPER.toPo(orderId, gdOrderExtDto);
        updatePo.setOrderId(orderId);

        // 查询旧数据
        FcGdOrderExtPo oldPo = this.queryByOrderId(orderId);

        // 不存在旧数据，新增
        if (Objects.isNull(oldPo)) {
            this.fcGdOrderExtDao.insertSelective(updatePo);
            return;
        }

        // 存在旧数据，更新；
        updatePo.setId(oldPo.getId());
        this.fcGdOrderExtDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public GdOrderExtDto getGdOrderExtInfo(int orderId) {
        // 查询数据
        FcGdOrderExtPo po = this.queryByOrderId(orderId);

        // mapper转换
        return GdOrderExtConverter.MAPPER.toDto(po);
    }

    @Override
    public void checkGdOrderExtCreateInfo(Integer orderProduct, GdOrderExtDto gdOrderExtDto) {
        // 校验基本信息
        Assert.notNull(gdOrderExtDto, "订单额外信息不能为空");

        // 按天投放
        if (gdOrderExtDto.getOrderLaunchTimeType() == GdOrderLaunchTimeType.DAY.getCode()) {
            Assert.isTrue(gdOrderExtDto.getOrderCpm() == null || gdOrderExtDto.getOrderCpm() == 0, "按天投放时不支持填写预定量");
            Assert.isTrue(gdOrderExtDto.getOrderAmount() == null || gdOrderExtDto.getOrderAmount() == 0, "按天投放时不支持填写订单金额");
        }

        // 按周期投放
        if (gdOrderExtDto.getOrderLaunchTimeType() == GdOrderLaunchTimeType.RANG.getCode()) {
            Assert.isTrue(gdOrderExtDto.getOrderCpm() != null && gdOrderExtDto.getOrderCpm() > 0, "周期投放时预定量不能为空");
            Assert.isTrue(gdOrderExtDto.getOrderAmount() != null && gdOrderExtDto.getOrderAmount() > 0, "周期投放时订单金额不能为空");
        }

        // 周期频控
        this.cycleFrequencyService.validateWhenCreate(orderProduct, gdOrderExtDto);
    }

    @Override
    public void checkGdOrderUpdateInfo(Integer orderProduct, GdOrderExtDto oldOrderExt, UpdateOrderDto updateOrderDto) {
        if (!isFlyGdRangeOrder(oldOrderExt)) {
            return;
        }
        GdOrderExtDto updateParam = updateOrderDto.getGdOrderExtDto();

        if (updateParam != null) {
            try {
                List<ScheduleDto> schedules = this.queryScheduleService.getValidSchedulesByOrderId(oldOrderExt.getOrderId());
                Long amount = schedules.stream()
                        .mapToLong(scheduleDto -> {
                                    if (ShowPriorityType.PRIORITY_TYPES.contains(scheduleDto.getShowPriority())) {
                                        return scheduleDto.getExternalPrice();
                                    }
                                    if (Utils.isPositive(scheduleDto.getPdbActualImpression())) {
                                        return (long) scheduleDto.getCostPrice() * scheduleDto.getPdbActualImpression();
                                    }
                                    return (long) scheduleDto.getCostPrice() * scheduleDto.getTotalImpression();
                                }
                        ).sum();
                long totalImpression = schedules.stream().mapToLong(ScheduleDto::getTotalImpression).sum();

                List<Timestamp> timeList = schedules.stream()
                        .map(ScheduleDto::getBeginDate)
                        .sorted(Timestamp::compareTo)
                        .collect(Collectors.toList());

                if (!CollectionUtils.isEmpty(timeList)) {
                    Timestamp endDate = timeList.get(timeList.size() - 1);
                    String endTime = updateOrderDto.getEndTime();
                    Timestamp endTimestamp = TimeUtil.isoStrToTimestamp(endTime);
                    Assert.isTrue(endTimestamp != null && !endTimestamp.before(endDate), "修改订单时结束时间不能小于当前已有排期的投放时间");
                }

                Assert.isTrue(updateParam.getOrderAmount() >= amount, "修改订单时订单金额不能小于当前排期金额总量");
                Assert.isTrue(updateParam.getOrderCpm() >= totalImpression, "修改订单时订单预定量不能小于当前排期预定总量");
            } catch (ServiceException e) {
                log.error("checkGdOrderExtUpdateInfo 获取排期时发生异常:{}", ExceptionUtils.getStackTrace(e));
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public boolean isFlyGdRangeOrder(GdOrderExtDto gdOrderExtDto) {
        return this.isFlyGdOrder(gdOrderExtDto)
                && Objects.equals(gdOrderExtDto.getOrderLaunchTimeType(), GdOrderLaunchTimeType.RANG.getCode());
    }

    @Override
    public boolean isFlyGdOrder(GdOrderExtDto gdOrderExtDto) {
        return gdOrderExtDto != null
                && Objects.equals(gdOrderExtDto.getOrderSource(), GdOrderSource.FLY.getCode());
    }

    @Override
    public boolean isTopFlowOrder(GdOrderExtDto gdOrderExtDto) {
        return gdOrderExtDto != null
                && Objects.equals(gdOrderExtDto.getOrderSource(), GdOrderSource.TOP_FLOW.getCode());
    }

    @Override
    public boolean isFlyGdOrder(int orderId) {
        return this.isFlyGdOrder(getGdOrderExtInfo(orderId));
    }

    @Override
    public boolean isCycleFrequencyOrder(GdOrderExtDto gdOrderExtDto) {
        return this.cycleFrequencyService.isCycleFrequencyOrder(gdOrderExtDto);
    }

    @Override
    public List<GdOrderExtDto> getGdOrderExtInfoList(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return new ArrayList<>();
        }
        FcGdOrderExtPoExample example = new FcGdOrderExtPoExample();
        example.createCriteria()
                .andOrderIdIn(orderIds)
                .andIsDeletedEqualTo((byte) IsDeleted.VALID.getCode());

        List<FcGdOrderExtPo> fcGdOrderExtPoList = this.fcGdOrderExtDao.selectByExample(example);
        return fcGdOrderExtPoList.stream().map(GdOrderExtConverter.MAPPER::toDto).collect(Collectors.toList());
    }

    private FcGdOrderExtPo queryByOrderId(int orderId) {
        FcGdOrderExtPoExample example = new FcGdOrderExtPoExample();
        example.createCriteria()
                .andOrderIdEqualTo(orderId)
                .andIsDeletedEqualTo((byte) IsDeleted.VALID.getCode());
        List<FcGdOrderExtPo> res = this.fcGdOrderExtDao.selectByExample(example);
        return CollUtil.getFirst(res);
    }
}
