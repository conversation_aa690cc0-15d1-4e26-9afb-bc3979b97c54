package com.bilibili.brand.biz.log.bean;

import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/14 17:36
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(gdLogFlag = GdLogFlag.BRAND_AD_SHOW)
public class AdShowUserLogBean implements Serializable {
    private static final long serialVersionUID = -6547660158414361539L;
    @LogProperty("订单id")
    private Integer orderId;
    @LogProperty("mid列表")
    private String midList;
}
