package com.bilibili.brand.biz.schedule.service;

import com.bilibili.brand.api.schedule.bo.TempScheduleBo;
import com.bilibili.brand.api.schedule.service.IGdTempScheduleService;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTempDao;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPoExample;
import com.bilibili.cpt.platform.common.FinishFlags;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
@Service
public class GdTempScheduleService implements IGdTempScheduleService {

    @Autowired
    private GdScheduleTempDao tempDao;

    @Override
    public int saveTempSchedule(TempScheduleBo tempSchedule) {

        GdScheduleTempPo po = new GdScheduleTempPo();
        BeanUtils.copyProperties(tempSchedule, po);
        tempDao.insertSelective(po);
        return po.getId();
    }

    @Override
    public void updateTempScheduleToFail(int id, String errorMsg) {
        GdScheduleTempPo po = new GdScheduleTempPo();
        po.setStatus(FinishFlags.FAIL.getCode());
        po.setFailMsg(errorMsg == null ? "" : (errorMsg.length() > 1000 ? errorMsg.substring(0, 1000) : errorMsg));
        po.setId(id);
        tempDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void updateTempScheduleToFail(Long dealSeq, Timestamp beginTime, String errorMsg) {
        List<GdScheduleTempPo> tempPos = queryTempSchedules(dealSeq, beginTime);
        if (CollectionUtils.isEmpty(tempPos)) {
            return;
        }

        GdScheduleTempPo po = new GdScheduleTempPo();
        po.setStatus(FinishFlags.FAIL.getCode());
        po.setFailMsg(errorMsg == null ? "" : (errorMsg.length() > 1000 ? errorMsg.substring(0, 1000) : errorMsg));
        GdScheduleTempPoExample poExample = new GdScheduleTempPoExample();
        poExample.or().andDealSeqEqualTo(dealSeq);
        tempDao.updateByExampleSelective(po, poExample);
    }

    @Override
    public void deleteSchedule(Long dealSeq) {
        GdScheduleTempPoExample poExample = new GdScheduleTempPoExample();
        poExample.or()
                .andDealSeqEqualTo(dealSeq);
        tempDao.deleteByExample(poExample);
    }

    @Override
    public void deleteSchedule(int id) {
        tempDao.deleteByPrimaryKey(id);
    }

    private List<GdScheduleTempPo> queryTempSchedules(Long dealSeq, Timestamp timestamp){
        GdScheduleTempPoExample poExample = new GdScheduleTempPoExample();
        GdScheduleTempPoExample.Criteria criteria = poExample.or()
                .andDealSeqEqualTo(dealSeq)
                .andBeginDateEqualTo(timestamp);
        return tempDao.selectByExample(poExample);
    }
}
