package com.bilibili.brand.biz.deduction.handler;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.biz.deduction.handler.bean.OrderConsumptionBean;
import com.bilibili.brand.biz.deduction.handler.bean.SourcePriceConfigBean;
import com.bilibili.brand.biz.utils.OperationUtil;
import com.bilibili.brand.platform.report.biz.dao.SplashStatScreenSourceDayDao;
import com.bilibili.brand.platform.report.biz.po.SplashStatScreenSourceDayPo;
import com.bilibili.brand.platform.report.biz.po.SplashStatScreenSourceDayPoExample;
import com.bilibili.cpt.platform.common.ButtonInteractStyleEnum;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.SsaSourceAllInfoDto;
import com.bilibili.ssa.platform.api.location.service.ISsaCycleService;
import com.bilibili.ssa.platform.api.location.service.ISsaSourceService;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component("ssaCptOrderDeductionHandler")
public class SsaCptOrderDeductionHandler extends AbstractOrderDeductionHandler {

    private static final int PAGE_SIZE = 100;

    @Autowired
    private SplashStatScreenSourceDayDao splashStatScreenSourceDayDao;

    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;

    @Autowired
    private ISsaCycleService ssaCycleService;

    @Autowired
    private ISsaSourceService ssaSourceService;

    @Override
    protected List<OrderConsumptionBean> getOrderDailyConsumption(Timestamp date) {
        if (date == null) {
            return Collections.emptyList();
        }

        // 查询创意日报表数据
        List<SplashStatScreenSourceDayPo> poList = getSsaCptCreativeDayStatList(date);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        // 查询创意信息数据
        List<SsaSplashScreenPo> splashScreenPoList = getSplashScreenList(poList.stream()
                .map(SplashStatScreenSourceDayPo::getScreenId)
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(splashScreenPoList)) {
            return Collections.emptyList();
        }
        Map<Integer, SsaSplashScreenPo> splashScreenPoMap = splashScreenPoList.stream()
                .collect(Collectors.toMap(SsaSplashScreenPo::getId, Function.identity()));

        return poList.stream().map(statPo -> {
            SsaSplashScreenPo splashScreenPo = splashScreenPoMap.getOrDefault(statPo.getScreenId(),
                    SsaSplashScreenPo.builder()
                            .build());
            return OrderConsumptionBean.builder()
                    .accountId(splashScreenPo.getAccountId())
                    .orderId(splashScreenPo.getGdOrderId())
                    .sourceId(statPo.getSourceId())
                    .showCount(Integer.parseInt(String.valueOf(statPo.getShowCount())))
                    .showStyle(splashScreenPo.getShowStyle())
                    .clickArea(splashScreenPo.getClickArea())
                    .build();
        }).collect(Collectors.toList());
    }

    private List<SplashStatScreenSourceDayPo> getSsaCptCreativeDayStatList(Timestamp date) {
        // batch select
        SplashStatScreenSourceDayPoExample example = new SplashStatScreenSourceDayPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andGroupTimeEqualTo(date)
                .andSalesTypeEqualTo(SalesType.SSA_CPT.getCode());
        long total = splashStatScreenSourceDayDao.countByExample(example);
        if (total == 0) {
            return Collections.emptyList();
        }

        List<SplashStatScreenSourceDayPo> statList = new ArrayList<>((int) total);
        int totalPage = (int) ((total + PAGE_SIZE - 1) / PAGE_SIZE);
        for (int i = 1; i <= totalPage; i++) {
            Page pageBean = Page.valueOf(i, PAGE_SIZE);
            example.setLimit(pageBean.getLimit());
            example.setOffset(pageBean.getOffset());
            List<SplashStatScreenSourceDayPo> pagePoList = splashStatScreenSourceDayDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(pagePoList)) {
                statList.addAll(pagePoList);
            }
        }
        return statList;
    }

    private List<SsaSplashScreenPo> getSplashScreenList(List<Integer> screenIdList) {
        // batch select
        return OperationUtil.batchOperateWithListResult(screenIdList, 100, subScreenIdList -> {
            SsaSplashScreenPoExample example = new SsaSplashScreenPoExample();
            example.or().andIdIn(subScreenIdList)
                    .andSalesTypeEqualTo(SalesType.SSA_CPT.getCode());
            return ssaSplashScreenDao.selectByExample(example);
        });
    }

    @Override
    protected Map<Integer, BigDecimal> getSourcePriceMap(Timestamp date,
                                                         List<SourcePriceConfigBean> sourcePriceConfigList) {
        if (date == null
                || CollectionUtils.isEmpty(sourcePriceConfigList)) {
            return Collections.emptyMap();
        }

        // 根据日期查询刊例
        SsaCycleDto ssaCycleDto = ssaCycleService.getCycleDtoByTime(date, SsaAdType.IMAGE.getCode(),
                OrderProduct.SSA_CPT.getCode());
        if (ssaCycleDto == null) {
            return Collections.emptyMap();
        }
        // 查询刊例资源位配置cpm价
        List<SsaSourceAllInfoDto> sourceAllInfoList = ssaSourceService.getSourcesByCycleId(ssaCycleDto.getId(),
                SalesType.SSA_CPT.getCode());
        if (CollectionUtils.isEmpty(sourceAllInfoList)) {
            return Collections.emptyMap();
        }

        Set<Integer> sourcePriceConfigHashSet = sourcePriceConfigList.stream().map(SourcePriceConfigBean::hashCode)
                .collect(Collectors.toSet());
        //todo 临时逻辑
        return sourceAllInfoList.stream().filter(t-> (ButtonInteractStyleEnum.CLICK.getCode().equals(t.getInteractStyle())
                && OrderProduct.SSA_CPT.getCode().equals(t.getOrderProduct()))).filter(sourceAllInfoDto ->
                sourcePriceConfigHashSet.contains(SourcePriceConfigBean.builder()
                        .sourceId(sourceAllInfoDto.getSourceId())
                        .showStyle(sourceAllInfoDto.getShowStyle())
                        .clickArea(sourceAllInfoDto.getClickArea())
                        .build()
                        .hashCode()))
                .collect(Collectors.toMap(sourceAllInfoDto -> SourcePriceConfigBean.builder()
                                .sourceId(sourceAllInfoDto.getSourceId())
                                .showStyle(sourceAllInfoDto.getShowStyle())
                                .clickArea(sourceAllInfoDto.getClickArea())
                                .build()
                                .hashCode(),
                        SsaSourceAllInfoDto::getInternalCpmPrice));
    }
}
