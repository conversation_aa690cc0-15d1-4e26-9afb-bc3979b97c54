package com.bilibili.brand.biz.component.service;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.ComponentType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.component.ComponentDto;
import com.bilibili.brand.api.component.ComponentMarkDto;
import com.bilibili.brand.api.component.QueryComponentDto;
import com.bilibili.brand.api.component.UpdateComponentDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.component.dao.GdComponentDao;
import com.bilibili.brand.biz.component.po.GdComponentPo;
import com.bilibili.brand.biz.component.po.GdComponentPoExample;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/9 21:00
 */
@Slf4j
@Service
public class ComponentService {
    @Autowired
    private GdComponentDao componentDao;
    @Autowired
    private CreativeComponentService creativeComponentService;
    @Autowired
    private ISystemConfigService systemConfigService;

    @Transactional
    public Long createComponent(Operator operator, ComponentDto component) {
        ComponentType componentType = ComponentType.getByCode(component.getComponentType());
        componentType.check(component);
        GdComponentPo componentPo = GdComponentPo.builder()
                .componentType(componentType.getCode())
                .componentName(component.getComponentName())
                .accountId(operator.getOperatorId())
                .imageUrl(component.getImageUrl())
                .imageMd5(component.getImageMd5())
                .videoId(component.getVideoId())
                .videoUrl(component.getVideoUrl())
                .videoMd5(component.getVideoMd5())
                .videoDuration(component.getVideoDuration())
                .showCloseButton(component.getShowCloseButton())
                .jumpUrl(component.getJumpUrl())
                .schemaUrl(component.getSchemaUrl())
                .markUrl(component.getMarkUrl())
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        this.componentDao.insertSelective(componentPo);
        return componentPo.getComponentId();
    }


    @Transactional
    public String updateComponent(Operator operator, UpdateComponentDto updateComponent) {
        ComponentDto component = updateComponent.getComponent();
        ComponentType componentType = ComponentType.getByCode(component.getComponentType());
        componentType.check(component);
        GdComponentPo componentPo = this.getComponentById(operator, component.getComponentId());
        Assert.notNull(componentPo, "component not found,component id is " + component.getComponentId());
        if (!updateComponent.isDoubleConfirm()) {
            String result = this.creativeComponentService.checkCreativeComponentRelation(component.getComponentId());
            if (!StringUtils.isEmpty(result)) {
                return result;
            }
        }
        GdComponentPo toApplyComponent = GdComponentPo.builder()
                .componentId(component.getComponentId())
                .componentType(component.getComponentType())
                .componentName(component.getComponentName())
                .imageUrl(component.getImageUrl())
                .imageMd5(component.getImageMd5())
                .videoId(component.getVideoId())
                .videoUrl(component.getVideoUrl())
                .videoMd5(component.getVideoMd5())
                .videoDuration(component.getVideoDuration())
                .showCloseButton(component.getShowCloseButton())
                .jumpUrl(component.getJumpUrl())
                .schemaUrl(component.getSchemaUrl())
                .markUrl(component.getMarkUrl())
                .build();
        this.componentDao.updateByPrimaryKeySelective(toApplyComponent);
        this.creativeComponentService.notifyCreative2Audit(operator, component.getComponentId());
        return "";
    }

    public ComponentDto getComponent(Operator operator, Long componentId) {
        GdComponentPo component = this.getComponentById(operator, componentId);
        return this.convert2Dto(component);
    }

    public List<ComponentDto> getComponents(Operator operator, List<Long> componentIds) {
        List<GdComponentPo> components = this.getComponentById(operator, componentIds);
        return components.stream().map(this::convert2Dto).collect(Collectors.toList());
    }

    public PageResult<ComponentDto> queryComponent(Operator operator, QueryComponentDto query) {
        int page = query.getPage() == null ? 1 : query.getPage();
        int size = query.getSize() == null ? 100 : query.getSize();

        GdComponentPoExample example = new GdComponentPoExample();
        GdComponentPoExample.Criteria criteria = example.createCriteria()
                .andAccountIdEqualTo(operator.getOperatorId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (Objects.nonNull(query.getComponentType())) {
            criteria.andComponentTypeEqualTo(query.getComponentType());
        }
        if (!CollectionUtils.isEmpty(query.getComponentIds())) {
            criteria.andComponentIdIn(query.getComponentIds());
        }
        if (StringUtils.hasLength(query.getComponentName())) {
            criteria.andComponentNameLike(query.getComponentName() + "%");
        }
        long count = this.componentDao.countByExample(example);
        if (count == 0) {
            return PageResult.<ComponentDto>builder()
                    .total(0)
                    .records(Lists.newLinkedList())
                    .build();
        }
        example.setLimit(size);
        example.setOffset((page - 1) * size);
        example.setOrderByClause("mtime desc");

        List<GdComponentPo> componentPos = this.componentDao.selectByExample(example);
        List<ComponentDto> componentList = (Objects.isNull(componentPos) ?
                Lists.<GdComponentPo>newLinkedList() : componentPos)
                .stream()
                .map(this::convert2Dto)
                .collect(Collectors.toList());
        return PageResult.<ComponentDto>builder()
                .total((int) count)
                .records(componentList)
                .build();
    }

    /**
     * 查询组件标
     *
     * @return
     */
    public List<ComponentMarkDto> queryComponentMark() {
        String value = this.systemConfigService.getValue(SystemConfigEnum.GD_COMPONENT_MARK.getCode());
        if (StringUtils.isEmpty(value)) {
            return Lists.newArrayList();
        }
        return JSONObject.parseArray(value, ComponentMarkDto.class);
    }

    private GdComponentPo getComponentById(Operator operator, Long componentId) {
        Assert.notNull(Utils.isPositive(componentId), "component id 无效");
        List<GdComponentPo> components = this.getComponentById(operator, Lists.newArrayList(componentId));
        return CollectionUtils.isEmpty(components) ? null : components.get(0);
    }

    private List<GdComponentPo> getComponentById(Operator operator, List<Long> componentIds) {
        if (CollectionUtils.isEmpty(componentIds)) {
            return Lists.newLinkedList();
        }
        GdComponentPoExample example = new GdComponentPoExample();
        GdComponentPoExample.Criteria criteria = example.createCriteria()
                .andComponentIdIn(componentIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (Objects.nonNull(operator)) {
            criteria.andAccountIdEqualTo(operator.getOperatorId());
        }
        List<GdComponentPo> componentPos = this.componentDao.selectByExample(example);
        return Objects.isNull(componentPos) ? Lists.newLinkedList() : componentPos;
    }

    private ComponentDto convert2Dto(GdComponentPo po) {
        if (Objects.isNull(po)) {
            return null;
        }
        ComponentDto dto = new ComponentDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }
}
