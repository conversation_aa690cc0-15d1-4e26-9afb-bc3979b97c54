package com.bilibili.brand.biz.schedule.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.booking.dto.*;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.*;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.config.business.GdPlusConfig;
import com.bilibili.brand.biz.inventory.ILocationStockService;
import com.bilibili.brand.biz.inventory.bo.stock.*;
import com.bilibili.brand.biz.inventory.impl.location.LocationStockServiceFactory;
import com.bilibili.brand.common.Constant;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.enums.GdDisplayModeEnum;
import com.bilibili.enums.GdInlineSalesTypeEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.price.IGdPriceService;
import com.bilibili.brand.api.resource.price.gd.PriceDto;
import com.bilibili.brand.api.schedule.bo.information_flow.QueryInventoryBo;
import com.bilibili.brand.api.schedule.bo.information_flow.GdInventoryResultBo;
import com.bilibili.brand.api.schedule.service.IScheduleService;
import com.bilibili.brand.api.schedule.service.inventory.IGdPlusInventoryService;
import com.bilibili.brand.api.stock.dto.ResourceInfoBo;
import com.bilibili.brand.api.stock.dto.fly.FlyPriceDto;
import com.bilibili.brand.api.stock.dto.fly.FlySplitDaysStockDto;
import com.bilibili.brand.api.stock.dto.fly.FlyStockPriceDto;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.converter.InventoryConverter;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTempDao;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPoExample;
import com.bilibili.brand.biz.schedule.service.frequency.GdBigCardResourceService;
import com.bilibili.brand.biz.schedule.service.frequency.ResourceService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.QueryInventoryTimeInfoBo;
import com.bilibili.utils.TraceUtils;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.brand.common.Constant.FLY_PLATFORM_IMPRESSION_RATIO;

/**
 * @Description 异步查询库存
 * <AUTHOR>
 * @Date 2020.10.21 22:58
 */
@Slf4j
@Service
public class ScheduleStockService {
    private final static Logger LOGGER = LoggerFactory.getLogger(ScheduleStockService.class);
    @Autowired
    private IScheduleService scheduleService;

    @Autowired
    private IGdPriceService cpmPriceService;

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private ScheduleLimitService limitService;

    @Autowired
    private FlyScheduleStockService flyScheduleStockService;

    @Autowired
    private IGdOrderService gdOrderService;

    //动态模板
    @Value("#{'${resource.dynamic.template.ids:171,142}'.split(',')}")
    private List<Integer> dynamicTemplateIds;

    @Autowired
    private GdPlusScheduleInvokeService invokeService;

    @Autowired
    private GdBigCardResourceService gdBigCardFrequencyService;

    @Autowired
    private FlyGdService flyGdService;

    @Autowired
    private GdScheduleTempDao tempDao;

    @Autowired
    private ConfigCenter configCenter;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private IGdPlusInventoryService gdPlusInventoryService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IResourceBookingService resourceBookingService;

    @Autowired
    private LocationStockServiceFactory locationStockServiceFactory;

    @Async
    public void flyStockUpdateAsync(BestGroupStockParamDto query,
                                    List<TargetRule> targetRules,
                                    String dealSeq) throws ServiceException {
        throw new ServiceException("已暂停服务");
//        LOGGER.info("fly paramDto=" + JSON.toJSONString(query));
//        LOGGER.info("fly targetRules=" + JSON.toJSONString(targetRules));
//        List<FlySplitDaysStockDto> splitDaysStocks;
//        Map<Integer, GroupStockDto> groupStockDtoMap = new HashMap<>();
//        if (gdBigCardFrequencyService.isMatch(ResourceInfoBo.builder()
//                .templateId(query.getTemplateId()).build())) {
//            //查询库存
//            Map<Pair<Timestamp, Timestamp>, StockDto> stockMap = new HashMap<>();
//            invokeService.queryGDStock(stockMap, query, dealSeq);
//            log.info("stockMap content is [{}]", stockMap);
//
//            //计算库存
//            List<SplitDaysStockDto> gdSplitStocks = invokeService.getGdSplitDaysStockVo(stockMap);
//            log.info("flyStockUpdateAsync gdSplitStocks[{}]", gdSplitStocks);
//            splitDaysStocks = gdSplitStocks.stream().map(t -> FlySplitDaysStockDto.builder().stockCpm(t.getStockCpm())
//                            .scheduleDate(Utils.getBeginOfDay(TimeUtil.isoTimeStr2Timestamp(t.getBeginTime()))).build())
//                    .collect(Collectors.toList());
//
//            //mock一下每端的数据,反正起飞那边只拿总的数据
//            log.info("flyStockUpdateAsync splitDaysStocks[{}]", splitDaysStocks);
//            Map<LocalDate, Long> dayImpression = splitDaysStocks.stream()
//                    .collect(Collectors.toMap(t -> TimeUtil.timestampToLocalDate(t.getScheduleDate()),
//                            FlySplitDaysStockDto::getStockCpm));
//            if (query.getPlatformIds().size() == 1) {
//                Integer platform = query.getPlatformIds().get(0);
//                groupStockDtoMap.put(platform, GroupStockDto.builder().platformId(platform)
//                        .platformName(PlatformType.getByCode(platform).getDesc())
//                        .stockCpm(dayImpression.values().stream().mapToLong(t -> t).sum())
//                        .splitDaysFlag(1).templateId(query.getTemplateId())
//                        .stockImpression(dayImpression).build());
//            } else {
//                Map<Integer, GroupStockDto> finalGroupStockDtoMap = groupStockDtoMap;
//                query.getPlatformIds().forEach(platform -> {
//                    Map<LocalDate, Long> impressionMap = new HashMap<>();
//                    dayImpression.forEach((k, v) -> impressionMap.put(k,
//                            v * FLY_PLATFORM_IMPRESSION_RATIO.get(platform) / 100));
//                    finalGroupStockDtoMap.put(platform, GroupStockDto.builder().platformId(platform)
//                            .platformName(PlatformType.getByCode(platform).getDesc())
//                            .splitDaysFlag(1).templateId(query.getTemplateId())
//                            .stockCpm(impressionMap.values().stream().mapToLong(t -> t).sum())
//                            .stockImpression(impressionMap).build());
//                });
//                groupStockDtoMap = finalGroupStockDtoMap;
//            }
//        } else {
//            groupStockDtoMap = scheduleService.queryFlyBestGroupStock(query);
//            LOGGER.info("fly groupStockDtoMap=" + JSON.toJSONString(groupStockDtoMap));
//            //计算库存
//            splitDaysStocks = flyScheduleStockService.getSplitDaysStockVo(groupStockDtoMap, query);
//        }
//        //计算每日价格
//        Map<Timestamp, List<FlyPriceDto>> datePriceMap = flyScheduleStockService.getPrice(query, targetRules);
//        splitDaysStocks.forEach(o -> o.setPrices(datePriceMap.get(o.getScheduleDate())));
//        FlyStockPriceDto stockPriceDto = FlyStockPriceDto.builder()
//                .dealSeq(dealSeq)
//                .splitDaysStocks(splitDaysStocks)
//                .isDealFinish(true)
//                .build();
//        if (!CollectionUtils.isEmpty(splitDaysStocks)) {
//            long sumCpm = splitDaysStocks.stream().mapToLong(FlySplitDaysStockDto::getStockCpm).sum();
//            stockPriceDto.setStockCpm(sumCpm);
//        }
//        //FlyStockPriceDto存入缓存
//        redisService.setFlyValue(dealSeq, stockPriceDto);
//        //groupStockDtoMap每日各平台的余量
//        redisService.setFlyExtValue(dealSeq, groupStockDtoMap);
//        //datePriceMap每日各平台的价格
//        Map<String, List<FlyPriceDto>> datePriceMap2 = new HashMap<>();
//        datePriceMap.forEach((k, v) -> datePriceMap2.put(Utils.getTimestamp2String(k), v));
//        redisService.setFlyExt2Value(dealSeq, datePriceMap2);
    }

    //注意 调用异步的方法和异步方法调用的有事务注解方法不能写到当前类，不然会失效
    @Async
    public void doQueryInventory(BestGroupStockParamDto bestGroupStockParamDto,
                                 List<TargetRule> targetRules,
                                 String dealSeq) throws ServiceException {

        setNecessaryInfo(bestGroupStockParamDto);

        Integer templateId = bestGroupStockParamDto.getTemplateId();

        if (configCenter.getGdPlusConfig().queryInventoryFromGoblin(templateId)) {

            boolean needMergeOs = configCenter.getMetaDataConfig().needMergeOs(bestGroupStockParamDto.getGdType(),
                    templateId,
                    bestGroupStockParamDto.getAccountId());

            if (needMergeOs) {
                doQuery(bestGroupStockParamDto, dealSeq);
            } else {
                log.info("doQueryInventory not supports merge, template_id={}", templateId);
                throw new ServiceException("当前模板不支持投放，模板id=" + templateId);
//                invokeService.queryGdStockAsync(bestGroupStockParamDto, targetRules, dealSeq);
            }
        } else {
            throw new ServiceException("当前模板不支持投放，模板id=" + templateId);
            //queryInventoryFromBrand(bestGroupStockParamDto, targetRules, dealSeq);
        }
    }

    private void doQuery(BestGroupStockParamDto bestGroupStockParamDto,
                         String dealSeq) {

        QueryInventoryBo queryInventory = InventoryConverter.MAPPER.toQueryInventory(bestGroupStockParamDto, dealSeq);

        try {
            ResourceType resourceType = ResourceType.getByCodeWithValidation(bestGroupStockParamDto.getResourceType());
            GdInventoryResultBo inventoryResultBo;
            if (resourceType.isSupportNewStock() && configCenter.getMetaDataConfig().isUseNewStockSwitch()) {
                inventoryResultBo = queryStockWithNew(bestGroupStockParamDto, dealSeq);
            } else {
                inventoryResultBo = gdPlusInventoryService.queryInventory(queryInventory);
            }

//            List<PriceDto> prices = cpmPriceService.getPrice(bestGroupStockParamDto, queryInventory.getTargets());

            StockPriceDto stockPriceDto = InventoryConverter.MAPPER.toStockPriceDto(inventoryResultBo, dealSeq);

            redisService.setValue(dealSeq, stockPriceDto);
        } catch (Exception e) {
            log.info("查询库存时发生异常:{}", ExceptionUtils.getStackTrace(e));
            redisService.setValue(dealSeq, StockPriceDto.builder()
                    .isSuccess(false)
                    .isDealFinish(true)
                    .errorMsg(e.getMessage())
                    .build());
        }
    }


    private void queryInventoryFromBrand(BestGroupStockParamDto bestGroupStockParamDto,
                                         List<TargetRule> targetRules,
                                         String dealSeq) {

        StockPriceDto stockPriceDto;
        try {
            //计算库存
            List<SplitDaysStockDto> splitDaysStocks = this.getSplitDaysStock4Brand(bestGroupStockParamDto);

            bestGroupStockParamDto.setIsDynamicTemplate(dynamicTemplateIds.contains(bestGroupStockParamDto.getTemplateId()));
            //计算价格
            List<PriceDto> prices = cpmPriceService.getPrice(bestGroupStockParamDto, targetRules);

            stockPriceDto = StockPriceDto.builder()
                    .stockCpm(0L)
                    .prices(prices)
                    .splitDaysFlag(bestGroupStockParamDto.getSplitDaysFlag())
                    .dealSeq(dealSeq)
                    .splitDaysStocks(splitDaysStocks)
                    .isDealFinish(true)
                    .build();

            long sumCpm = splitDaysStocks.stream().mapToLong(SplitDaysStockDto::getStockCpm).sum();
            stockPriceDto.setStockCpm(sumCpm);
        } catch (Exception e) {
            log.error("stockUpdateAsync error" + Throwables.getStackTraceAsString(e));
            stockPriceDto = StockPriceDto.builder()
                    .stockCpm(0L)
                    .prices(null)
                    .splitDaysFlag(bestGroupStockParamDto.getSplitDaysFlag())
                    .dealSeq(dealSeq)
                    .isDealFinish(true)
                    .isSuccess(false)
                    .errorMsg(e.getMessage())
                    .build();
        }

        redisService.setValue(dealSeq, stockPriceDto);
    }


    private void setNecessaryInfo(BestGroupStockParamDto bestGroupStockParamDto) {

        flyGdService.setGdTypeIfNecessary(bestGroupStockParamDto);

//        int resourceType = resourceService.getResourceType(ResourceInfoBo.builder()
//                .templateId(bestGroupStockParamDto.getTemplateId())
//                .build());
//
//        bestGroupStockParamDto.setResourceType(resourceType);

        if (bestGroupStockParamDto.getOrderId() != null) {
            GdOrderDto gdOrderDto = gdOrderService.getOrderById(bestGroupStockParamDto.getOrderId());
            bestGroupStockParamDto.setOrderProduct(gdOrderDto.getProduct());
        }
    }


    //注意 调用异步的方法和异步方法调用的有事务注解方法不能写到当前类，不然会失效
    @Async
    public void batchCreateAsync(NewScheduleDto newScheduleDto,
                                 List<GdScheduleTempPo> tempPos,
                                 Operator operator) {

        try {
            tempPos.forEach(t -> tempDao.insertUpdateSelective(t));

            scheduleService.createGdSchedule(newScheduleDto, operator);

        } catch (Exception exception) {

            updateTempScheduleToFail(newScheduleDto.getDealSeq(), exception.getMessage());

            log.warn("批量创建排期时发生异常:{}", ExceptionUtils.getStackTrace(exception));
        }
    }

    private List<GdScheduleTempPo> queryTempSchedules(Long dealSeq) {
        GdScheduleTempPoExample poExample = new GdScheduleTempPoExample();
        poExample.or().andDealSeqEqualTo(dealSeq);
        return tempDao.selectByExample(poExample);
    }

    private void updateTempScheduleToFail(Long dealSeq, String errorMsg) {
        List<GdScheduleTempPo> tempPos = queryTempSchedules(dealSeq);
        if (CollectionUtils.isEmpty(tempPos)) {
            return;
        }

        GdScheduleTempPo po = new GdScheduleTempPo();
        po.setStatus(FinishFlags.FAIL.getCode());
        po.setFailMsg(errorMsg == null ? "" : (errorMsg.length() > 1000 ? errorMsg.substring(0, 1000) : errorMsg));
        GdScheduleTempPoExample poExample = new GdScheduleTempPoExample();
        poExample.or().andDealSeqEqualTo(dealSeq);
        tempDao.updateByExampleSelective(po, poExample);
    }


    public void update(UpdateScheduleDto updateScheduleDto, Operator operator) throws ServiceException {
        scheduleService.updateSchedule(updateScheduleDto, operator);
    }

    private List<SplitDaysStockDto> getSplitDaysStockVo(Map<Integer, GroupStockDto> groupStockDtoMap,
                                                        BestGroupStockParamDto paramDto) {
        List<SplitDaysStockDto> splitDaysStocks = new ArrayList<>();
        Map<LocalDate, Long> dayImpression = new HashMap<>();
        for (Map.Entry<Integer, GroupStockDto> entry : groupStockDtoMap.entrySet()) {
            entry.getValue().getStockImpression().forEach((k, v)
                    -> dayImpression.put(k, dayImpression.getOrDefault(k, 0L) + v));
        }

        //根据inline日限制调整库存
        Map<Timestamp, Long> stocks = new HashMap<>();
        dayImpression.forEach((k, v) -> stocks.put(TimeUtil.localDateToTimestamp(k), v));
        Map<Timestamp, Long> stockImpression = limitService
                .getDaysCpmLimitByTemplateForAdd(paramDto.getTemplateId(), paramDto.getDates(), stocks);

        //todo 按照库存时间曲线返回库存
        Map<Timestamp, Pair<Timestamp, Timestamp>> timeInfoMap = paramDto.getTimeInfoMap();
        stockImpression.forEach((k, v) -> {
            Pair<Timestamp, Timestamp> timePair = timeInfoMap.get(k);
            if (timePair == null) {
                splitDaysStocks.add(SplitDaysStockDto.builder().scheduleDate(TimeUtil
                                .timestampToIsoDateStr(k))
                        .stockCpm(v).build());
            } else {
                int hour = timePair.getSecond().toLocalDateTime().getHour()
                        - timePair.getFirst().toLocalDateTime().getHour() + 1;
                splitDaysStocks.add(SplitDaysStockDto.builder().scheduleDate(TimeUtil.timestampToIsoDateStr(k))
                        .beginTime(TimeUtil.timestampToIsoTimeStr(timePair.getFirst()))
                        .endTime(TimeUtil.timestampToIsoTimeStr(timePair.getSecond()))
                        .stockCpm(v * hour / 24).build());
            }
        });

        splitDaysStocks.sort((o1, o2) -> TimeUtil.compareTime(TimeUtil.isoDateStrToLocalDate(o1.getScheduleDate()),
                TimeUtil.isoDateStrToLocalDate(o2.getScheduleDate())));

        return splitDaysStocks;
    }


    public void delete(Operator operator, List<Integer> scheduleIds) throws ServiceException {
        scheduleService.deleteScheduleBatch(scheduleIds, operator);
    }


    private List<SplitDaysStockDto> getSplitDaysStock4Brand(BestGroupStockParamDto param) throws ServiceException {
        boolean isPlayerDetail = Objects.equals(param.getResourceType(), ResourceType.PLAYER_DETAIL.getCode());
        boolean isFirstBrush = isPlayerDetail && Objects.equals(param.getDisplayMode(), GdDisplayModeEnum.FIRST_BRUSH.getCode());
        Map<Integer, Map<String, Integer>> sourceDayHalfHourCountMap = Maps.newHashMap();
        Map<String, Integer> dayTotalHalfHourCountMap = Maps.newHashMap();
        if (isPlayerDetail) {
            List<Timestamp> realDates = param.getDates().stream().map(Utils::getBeginOfDay).distinct().collect(Collectors.toList());
            Timestamp beginDate = param.getDates().stream().min(Timestamp::compareTo).get();
            Timestamp endDate = Timestamp.valueOf(param.getDates().stream()
                    .map(ts -> TimeUtil.endOfDay(ts.toLocalDateTime())).max(LocalDateTime::compareTo).get());
            List<Integer> sourceIdList = param.getPlatformIds()
                    .stream()
                    .map(Constant.PLAYER_DETAIL_PLATFORM_SOURCE_MAP::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Assert.notEmpty(sourceIdList, "资源位为空");


            //查询CPT的分时预约
            List<CptScheduleSplitTimeDto> splitTimeList = this.resourceBookingService.querySplitTimeList(
                    BookingTimeQueryDto.builder()
                            .sourceIds(sourceIdList)
                            .groupDates(realDates)
                            .excludesPastDate(false)
                            .status(CptBookingStatus.SCHEDULE_LOCKED_LIST)
                            .build(), Operator.SYSTEM);

            Map<Integer, Map<String, Integer>> sourceDaySeqCountMap = splitTimeList.stream()
                    .collect(Collectors.toMap(CptScheduleSplitTimeDto::getSourceId,
                            splitTime -> splitTime.getDayDtoList().stream()
                                    .collect(Collectors.toMap(CptScheduleSplitTimeDayDto::getDay,
                                            std -> std.getHourDtoList().size()))));

            //查询CPT天预约
            List<BookingItemDto> bookingItems = this.resourceBookingService.queryResourceBooking(BookingQueryDto.builder()
                    .sourceIds(sourceIdList)
                    .groupDates(realDates)
                    .status(CptBookingStatus.SCHEDULE_LOCKED_LIST)
                    .timeType(1)
                    .build());
            Map<Integer, Map<String, Integer>> sourceDayCountMap = Maps.newHashMap();
            for (BookingItemDto bookingItem : bookingItems) {
                Map<String, Integer> dayCountMap = sourceDayCountMap.get(bookingItem.getSourceId());
                if (Objects.isNull(dayCountMap)) {
                    sourceDayCountMap.put(bookingItem.getSourceId(), dayCountMap = Maps.newHashMap());
                }
                String day = TimeUtil.timestampToIsoDateStr(bookingItem.getGroupDate());
                dayCountMap.put(day, dayCountMap.getOrDefault(day, 0) + 1);
            }

            for (Timestamp day : realDates) {
                for (Integer sourceId : sourceIdList) {
                    Map<String, Integer> dayCountMap = sourceDayCountMap.getOrDefault(sourceId, Maps.newHashMap());
                    Map<String, Integer> daySeqCountMap = sourceDaySeqCountMap.getOrDefault(sourceId, Maps.newHashMap());
                    Map<String, Integer> dayHalfHourCountMap = sourceDayHalfHourCountMap.get(sourceId);
                    if (Objects.isNull(dayHalfHourCountMap)) {
                        sourceDayCountMap.put(sourceId, dayHalfHourCountMap = Maps.newHashMap());
                    }
                    String key = TimeUtil.timestampToIsoDateStr(day);
                    Integer dayCount = dayCountMap.getOrDefault(key, 0);
                    Integer daySeqCount = daySeqCountMap.getOrDefault(key, 0);
                    if (isFirstBrush) {
                        Assert.isTrue(daySeqCount <= 6, String.format("%s已经存在分时CPT排期（分时时长超过3小时）", key));
                        Assert.isTrue(dayCount == 0, String.format("%s已经存在CPT排期", key));
                    }
                    int curDaySeqCount = (dayCount * 48) + daySeqCount;//折换成半小时
                    dayHalfHourCountMap.put(key, curDaySeqCount);//折换成半小时
                    //取分端的分时数最大即可
                    dayTotalHalfHourCountMap.put(key, Math.max(dayTotalHalfHourCountMap.getOrDefault(key, 0), curDaySeqCount));
                }
            }

            if (isFirstBrush) {
                QueryScheduleDto queryScheduleDto = QueryScheduleDto.builder()
                        .statusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                        .beginDate(beginDate)
                        .endDate(endDate)
                        .timeContains(false)
                        .orderProducts(Lists.newArrayList(OrderProduct.GD_CPM.getCode()))
                        .sourceIds(sourceIdList)//首刷不支持自定义设备定向，因此这个资源位就是iphone+android
                        .build();

                List<ScheduleDto> schedules = this.queryScheduleService.queryBaseSchedule(queryScheduleDto);
                if (!CollectionUtils.isEmpty(schedules)) {
                    List<String> conflictDays = schedules.stream()
                            .filter(s -> realDates.contains(s.getBeginDate()))//排期询量时可以传间断性的日期，因此需要过滤下
                            .filter(t -> Objects.equals(t.getDisplayMode(), GdDisplayModeEnum.FIRST_BRUSH.getCode()))
                            .map(ScheduleDto::getBeginDate)
                            .map(TimeUtil::timestampToIsoDateStr)
                            .distinct()
                            .collect(Collectors.toList());
                    Assert.isTrue(conflictDays.isEmpty(), "下述日期已经存在首刷，请核实后重试，日期：" + String.join("、", conflictDays));
                }
            }
        }

        log.info("getSplitDaysStock4Brand, sourceDayHalfHourCountMap={}", JSON.toJSONString(sourceDayHalfHourCountMap));
        log.info("getSplitDaysStock4Brand, dayTotalHalfHourCountMap={}", JSON.toJSONString(dayTotalHalfHourCountMap));
        List<SplitDaysStockDto> splitDaysStocks = this.getSplitDaysStock4BrandSimple(param);
        Long halfHourCpm = this.configCenter.getGdPlusConfig().getPlayerDetailHalfHourCpm();
        if (param.getPlatformIds().size() == 1) {
            //如果是单端，则折算库存
            halfHourCpm = halfHourCpm / 2;
        }
        for (SplitDaysStockDto dayStock : splitDaysStocks) {
            Integer halfHours = dayTotalHalfHourCountMap.getOrDefault(dayStock.getScheduleDate(), 0);
            long usedCptCpm = halfHours * halfHourCpm;
            long leftCpm = (Objects.isNull(dayStock.getStockCpm()) || dayStock.getStockCpm() <= usedCptCpm)
                    ? 0L : (dayStock.getStockCpm() - usedCptCpm);
            if (isFirstBrush) {
                Long playerDetailFirstBrushLimitCpm = this.configCenter.getGdPlusConfig().getPlayerDetailFirstBrushLimitCpm();
                Assert.isTrue(leftCpm >= playerDetailFirstBrushLimitCpm, dayStock.getScheduleDate() + "首刷库存不足");
                dayStock.setStockCpm(playerDetailFirstBrushLimitCpm);
            } else {
                dayStock.setStockCpm(leftCpm);
            }
        }
        return splitDaysStocks;
    }

    public List<SplitDaysStockDto> getSplitDaysStock4BrandSimple(BestGroupStockParamDto param) throws ServiceException {
        Map<Integer, GroupStockDto> groupStockDtoMap = scheduleService.queryBestGroupStock(param);
        List<SplitDaysStockDto> splitDaysStock = getSplitDaysStockVo(groupStockDtoMap, param);
        log.info("getSplitDaysStock4BrandSimple, splitDaysStock={}", JSON.toJSONString(splitDaysStock));
        return splitDaysStock;
    }

    private GdInventoryResultBo queryStockWithNew(BestGroupStockParamDto bestGroupStockParamDto, String dealSeq) throws Exception {
        ResourceType resourceType = ResourceType.getByCodeWithValidation(bestGroupStockParamDto.getResourceType());
        LocationType locationType = resourceType.getLocationType();
        ILocationStockService locationStockService = locationStockServiceFactory.getLocationStockService(locationType);

        GdPlusConfig gdPlusConfig = configCenter.getGdPlusConfig();

        boolean isZeroInline = gdPlusConfig.isBigCardZeroFlushTemplate(bestGroupStockParamDto.getTemplateId());

        ArrayList<TargetRule> targetRules = Lists.newArrayList(bestGroupStockParamDto.getTargetRules());
        //清理脏的os定向
        targetRules.removeIf(tt -> Objects.equals(tt.getRuleType(), TargetType.OS.getCode()));
        targetRules.add(TargetRule.builder()
                .ruleType(TargetType.OS.getCode())
                .valueIds(bestGroupStockParamDto.getPlatformIds())
                .build());

        List<QueryTask> queryTasks = bestGroupStockParamDto.getTimeInfo().stream()
                .map(timePair -> QueryTask.builder()
                        .taskId(TraceUtils.genTraceId())
                        .location(locationType)
                        .date(timePair.getFirst().toLocalDateTime().toLocalDate())
                        .beginTime(timePair.getFirst().toLocalDateTime())
                        .endTime(timePair.getSecond().toLocalDateTime())
                        .target(Target.builder()
                                .whiteCrowds(bestGroupStockParamDto.getCrowdPackIds())
                                .blackCrowds(bestGroupStockParamDto.getExcludeCrowdPackIds())
                                .build())
                        .targetRules(targetRules)
                        .supportSourceCombineLaunch(bestGroupStockParamDto.getSupportSourceCombineLaunch())
                        .frequency(Frequency.builder()
                                .unit(FrequencyUnit.DAY)
                                .limit(bestGroupStockParamDto.getFrequencyLimit())
                                .build())
                        .avid(bestGroupStockParamDto.getAvid())
                        .extra(Extra.builder()
                                .topCard(isZeroInline)
                                .build())
                        .displayMode(GdDisplayModeEnum.getByCode(bestGroupStockParamDto.getDisplayMode()))
                        .accountId(bestGroupStockParamDto.getAccountId())
                        .orderId(bestGroupStockParamDto.getOrderId())
                        .templateId(bestGroupStockParamDto.getTemplateId())
                        .gdType(GdType.getByCodeWithDefault(bestGroupStockParamDto.getGdType()))
                        .orderProduct(OrderProduct.getByCode(bestGroupStockParamDto.getOrderProduct()))
                        .ppt(PromotionPurposeType.getByCode(bestGroupStockParamDto.getPromotionPurposeType()))
                        .build())
                .collect(Collectors.toList());

        QueryStockResponse queryStockResponse = locationStockService.queryStock(
                StockContext.builder()
                        .supportsMock(true)
                        .build(),
                QueryStockRequest.builder()
                        .requestId(dealSeq)
                        .tasks(queryTasks)
                        .build());
        Map<String, QueryTask> requestTaskMap = queryTasks.stream()
                .collect(Collectors.toMap(QueryTask::getTaskId, Function.identity()));
        Map<String, QueryTaskStock> responseTaskMap = queryStockResponse.getTaskStocks().stream()
                .collect(Collectors.toMap(QueryTaskStock::getTaskId, Function.identity()));
        List<GdInventoryDetailBo> inventoryDetailBos = requestTaskMap.entrySet().stream()
                .map(entry -> {
                    QueryTaskStock stock = responseTaskMap.get(entry.getKey());
                    QueryTask task = entry.getValue();
                    return GdInventoryDetailBo.builder()
                            .inventories(Math.toIntExact(stock.getTotalCpm()))
                            .requestId(Long.parseLong(dealSeq))
                            .beginTime(task.getBeginTime())
                            .endTime(task.getEndTime())
                            .build();
                })
                .sorted(Comparator.comparing(GdInventoryDetailBo::getBeginTime))
                .collect(Collectors.toList());

        return GdInventoryResultBo.builder()
                .inventoryDetails(inventoryDetailBos)
                .build();
    }
}


