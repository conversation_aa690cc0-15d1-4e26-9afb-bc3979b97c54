package com.bilibili.brand.biz.resource.pojo;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ResTargetPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ResTargetPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andIsInputIsNull() {
            addCriterion("is_input is null");
            return (Criteria) this;
        }

        public Criteria andIsInputIsNotNull() {
            addCriterion("is_input is not null");
            return (Criteria) this;
        }

        public Criteria andIsInputEqualTo(Integer value) {
            addCriterion("is_input =", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputNotEqualTo(Integer value) {
            addCriterion("is_input <>", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputGreaterThan(Integer value) {
            addCriterion("is_input >", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_input >=", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputLessThan(Integer value) {
            addCriterion("is_input <", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputLessThanOrEqualTo(Integer value) {
            addCriterion("is_input <=", value, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputIn(List<Integer> values) {
            addCriterion("is_input in", values, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputNotIn(List<Integer> values) {
            addCriterion("is_input not in", values, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputBetween(Integer value1, Integer value2) {
            addCriterion("is_input between", value1, value2, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsInputNotBetween(Integer value1, Integer value2) {
            addCriterion("is_input not between", value1, value2, "isInput");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedIsNull() {
            addCriterion("is_multi_group_selected is null");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedIsNotNull() {
            addCriterion("is_multi_group_selected is not null");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedEqualTo(Integer value) {
            addCriterion("is_multi_group_selected =", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedNotEqualTo(Integer value) {
            addCriterion("is_multi_group_selected <>", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedGreaterThan(Integer value) {
            addCriterion("is_multi_group_selected >", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_multi_group_selected >=", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedLessThan(Integer value) {
            addCriterion("is_multi_group_selected <", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedLessThanOrEqualTo(Integer value) {
            addCriterion("is_multi_group_selected <=", value, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedIn(List<Integer> values) {
            addCriterion("is_multi_group_selected in", values, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedNotIn(List<Integer> values) {
            addCriterion("is_multi_group_selected not in", values, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedBetween(Integer value1, Integer value2) {
            addCriterion("is_multi_group_selected between", value1, value2, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andIsMultiGroupSelectedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_multi_group_selected not between", value1, value2, "isMultiGroupSelected");
            return (Criteria) this;
        }

        public Criteria andHasMappingIsNull() {
            addCriterion("has_mapping is null");
            return (Criteria) this;
        }

        public Criteria andHasMappingIsNotNull() {
            addCriterion("has_mapping is not null");
            return (Criteria) this;
        }

        public Criteria andHasMappingEqualTo(Integer value) {
            addCriterion("has_mapping =", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingNotEqualTo(Integer value) {
            addCriterion("has_mapping <>", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingGreaterThan(Integer value) {
            addCriterion("has_mapping >", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingGreaterThanOrEqualTo(Integer value) {
            addCriterion("has_mapping >=", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingLessThan(Integer value) {
            addCriterion("has_mapping <", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingLessThanOrEqualTo(Integer value) {
            addCriterion("has_mapping <=", value, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingIn(List<Integer> values) {
            addCriterion("has_mapping in", values, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingNotIn(List<Integer> values) {
            addCriterion("has_mapping not in", values, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingBetween(Integer value1, Integer value2) {
            addCriterion("has_mapping between", value1, value2, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andHasMappingNotBetween(Integer value1, Integer value2) {
            addCriterion("has_mapping not between", value1, value2, "hasMapping");
            return (Criteria) this;
        }

        public Criteria andMappingTypeIsNull() {
            addCriterion("mapping_type is null");
            return (Criteria) this;
        }

        public Criteria andMappingTypeIsNotNull() {
            addCriterion("mapping_type is not null");
            return (Criteria) this;
        }

        public Criteria andMappingTypeEqualTo(Integer value) {
            addCriterion("mapping_type =", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeNotEqualTo(Integer value) {
            addCriterion("mapping_type <>", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeGreaterThan(Integer value) {
            addCriterion("mapping_type >", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("mapping_type >=", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeLessThan(Integer value) {
            addCriterion("mapping_type <", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeLessThanOrEqualTo(Integer value) {
            addCriterion("mapping_type <=", value, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeIn(List<Integer> values) {
            addCriterion("mapping_type in", values, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeNotIn(List<Integer> values) {
            addCriterion("mapping_type not in", values, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeBetween(Integer value1, Integer value2) {
            addCriterion("mapping_type between", value1, value2, "mappingType");
            return (Criteria) this;
        }

        public Criteria andMappingTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("mapping_type not between", value1, value2, "mappingType");
            return (Criteria) this;
        }

        public Criteria andSortOrderIsNull() {
            addCriterion("sort_order is null");
            return (Criteria) this;
        }

        public Criteria andSortOrderIsNotNull() {
            addCriterion("sort_order is not null");
            return (Criteria) this;
        }

        public Criteria andSortOrderEqualTo(Integer value) {
            addCriterion("sort_order =", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotEqualTo(Integer value) {
            addCriterion("sort_order <>", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderGreaterThan(Integer value) {
            addCriterion("sort_order >", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderGreaterThanOrEqualTo(Integer value) {
            addCriterion("sort_order >=", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderLessThan(Integer value) {
            addCriterion("sort_order <", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderLessThanOrEqualTo(Integer value) {
            addCriterion("sort_order <=", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderIn(List<Integer> values) {
            addCriterion("sort_order in", values, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotIn(List<Integer> values) {
            addCriterion("sort_order not in", values, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderBetween(Integer value1, Integer value2) {
            addCriterion("sort_order between", value1, value2, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotBetween(Integer value1, Integer value2) {
            addCriterion("sort_order not between", value1, value2, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}