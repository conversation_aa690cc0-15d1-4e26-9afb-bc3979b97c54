package com.bilibili.brand.biz.message;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.GdMessageTypeEnum;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.GdMessageDto;
import com.bilibili.brand.bean.RedisKey;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.ssa.platform.api.schedule.dto.SplitDaysImpressBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;
import com.bilibili.ssa.platform.api.schedule.dto.TopViewPlusScheduleBo;
import com.bilibili.ssa.platform.common.enums.TopViewConstants;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/12/5 11:56
 */
@Slf4j
@Service
public class TopViewPlusMsgProducer extends AbstractMsgProcessor<TopViewPlusScheduleBo, TopViewPlusScheduleBo> {
    @Autowired
    private RedissonClient redissonClient;

    @Value("${topview.schedule.queue.nums:2}")
    private int queueNums;

    @Override
    protected String getQueuePrefix() {
        return RedisKey.TOPVIEW_SCHEDULE_MSG_QUEUE_KEY;
    }

    @Override
    protected int getQueueNums() {
        return queueNums;
    }

    @Override
    protected int getIsolationSeq() {
        return TopViewConstants.ISOLATION_SEQ;
    }

    @Override
    public void produce(TopViewPlusScheduleBo msg) throws Exception {
        log.info("TopViewPlusMsgProducer productMessage:{}", GsonUtils.toJson(msg));
        SsaPlusScheduleBo ssaScheduleBo = msg.getSsaScheduleBo();
        ssaScheduleBo.getImpressBos().forEach(t -> {
            TopViewPlusScheduleBo targetMsgObject = genMsgObject(msg, t);
            String date = t.getScheduleDate();
            if (Objects.isNull(date)) {
                //less case
                date = TimeUtil.timestampToIsoDateStr(
                        Utils.getBeginOfDay(targetMsgObject.getSsaScheduleBo().getBeginTime()));
            }
            try {
                RBlockingQueue<String> queue = redissonClient.getBlockingQueue(getQueue(date));
                SsaScheduleMsg topViewMsg = new SsaScheduleMsg();
                topViewMsg.setOrderProduct(targetMsgObject.getSsaScheduleBo().getOrderProduct());
                topViewMsg.setMsg(GsonUtils.toJson(targetMsgObject));

                GdMessageDto gdMessage = buildGdMessage(topViewMsg);

                if (!queue.offer(GsonUtils.toJson(gdMessage))) {
                    log.warn("TopViewPlusMsgProducer productMessage fail");
                }
            } catch (Exception ex) {
                log.error("TopViewPlusMsgProducer productMessage err", ex);
                throw ex;
            }
        });
    }

    private GdMessageDto buildGdMessage(SsaScheduleMsg topViewMsg) {
        GdMessageDto gdMessage = new GdMessageDto();
        gdMessage.setType(GdMessageTypeEnum.CREATE_SCHEDULE);
        gdMessage.setData(GsonUtils.toJson(topViewMsg));
        return gdMessage;
    }

    private TopViewPlusScheduleBo genMsgObject(TopViewPlusScheduleBo source, SplitDaysImpressBo daysImpress) {
        SsaPlusScheduleBo sourceScheduleBo = source.getSsaScheduleBo();
        OrderProduct orderProduct = OrderProduct.getByCode(sourceScheduleBo.getOrderProduct());

        SsaPlusScheduleBo targetSchedule = new SsaPlusScheduleBo();
        BeanUtils.copyProperties(sourceScheduleBo, targetSchedule);//light copy
        targetSchedule.setImpressBos(null);
        targetSchedule.setBeginTime(TimeUtil.isoTimeStr2Timestamp(daysImpress.getBeginTime()));
        targetSchedule.setEndTime(TimeUtil.isoTimeStr2Timestamp(daysImpress.getEndTime()));
        targetSchedule.setLaunchDate(TimeUtils.getBeginOfDay(targetSchedule.getBeginTime()));
        if (Objects.nonNull(daysImpress.getImpressionCpm())) {
            targetSchedule.setTotalImpression(Math.toIntExact(daysImpress.getImpressionCpm()));
        }
        targetSchedule.setRotationNum(daysImpress.getRotationNum());
        SplitDaysImpressBo targetDaysImpress = new SplitDaysImpressBo();
        BeanUtils.copyProperties(daysImpress, targetDaysImpress);
        targetSchedule.setImpressBo(targetDaysImpress);
        SsaScheduleTargetDto targetDto = new SsaScheduleTargetDto();
        if (Objects.nonNull(sourceScheduleBo.getTargetDto())) {
            BeanUtils.copyProperties(sourceScheduleBo.getTargetDto(), targetDto);
        }
        List<TargetRule> targetRules = new ArrayList<>();
        if (!CollectionUtils.isEmpty(sourceScheduleBo.getTargetRules())) {
            sourceScheduleBo.getTargetRules()
                    .stream().forEach(tt -> {
                        TargetRule rule = new TargetRule();
                        BeanUtils.copyProperties(tt, rule);
                        targetRules.add(rule);
                    });
        }
        if (OrderProduct.TOP_VIEW_GD_PLUS == orderProduct) {
            //TOP_VIEW_GD支持分时包段
            List<Integer> hours = new ArrayList<>();
            for (int i = targetSchedule.getBeginTime().toLocalDateTime().getHour();
                 i < targetSchedule.getEndTime().toLocalDateTime().getHour() + 1; i++) {
                hours.add(i);
            }
            if (hours.size() != 24) {
                //增加分时定向
                targetDto.setHour(hours);
                targetRules.add(TargetRule.builder()
                        .ruleType(TargetType.HOUR.getCode())
                        .valueIds(hours)
                        .build());
            }
        }
        targetSchedule.setTargetRules(targetRules);
        targetSchedule.setTargetDto(targetDto);

        return TopViewPlusScheduleBo.builder()
                .ssaScheduleBo(targetSchedule)
                .sellingType(source.getSellingType())
                .build();
    }
}
