package com.bilibili.brand.biz.inventory.impl.cleaner;

import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.biz.inventory.ITargetCleaner;
import com.bilibili.cpt.platform.common.LocationType;
import com.bilibili.brand.biz.inventory.bo.stock.StockContext;
import com.bilibili.brand.biz.inventory.bo.stock.Task;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.enums.GdDisplayModeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 大卡: cold_boot_cnt = 1 当天第一次冷启
 * story: cold_boot_cnt = 1 当天第一次进story
 * 相关推荐 cold_boot_cnt = 1 当天第一次进相关推荐
 * repeated int32 boot_cnt = 7; // 当天第几次进入该场景: 目前仅支持取值 1, 覆盖大卡, story, 相关推荐
 * <p>
 * 小卡: 首刷: brush=1, 常规 3≤brush
 * ott: 首刷: brush=1, 优选 2≤brush≤5, 常规 6≤brush
 * repeated int32 brush_type = 8; // 展示方式: 0:常规, 1:首刷, 3:优选
 * 补充知识点：
 * <ul>
 *     <li>冷启动和刷次是独立的概念，用户每天可能会冷启动多次，每次冷启动后刷次会重新计数</li>
 *     <li>闪屏没有刷次的概念，因此闪屏的首刷其实就是当天首次冷启动</li>
 * </ul>
 *
 * <AUTHOR>
 * @date 2025/3/11 20:23
 */
@Slf4j
@Component
public class DisplayModeTargetCleaner implements ITargetCleaner {

    @Override
    public boolean supports(TargetType targetType) {
        return Objects.equals(TargetType.DISPLAY_MODE, targetType);
    }

    @Override
    public List<Integer> clean(StockContext context, Task task, @Nullable TargetRule targetRule) {

        LocationType location = task.getLocation();

        if (Objects.equals(location, LocationType.OTT_INLINE)) {
            if (!Objects.equals(task.getDisplayMode(), GdDisplayModeEnum.NORMAL)) {
                //只返回首刷
                return Lists.newArrayList(task.getDisplayMode().getCode());
            }
        } else if (Objects.equals(location, LocationType.SMALL_CARD)) {
            LocalDateTime beginTime = task.getBeginTime();
            LocalDateTime endTime = task.getEndTime();
            boolean isFullDay = (endTime.getHour() - beginTime.getHour()) == 23
                    && (endTime.getMinute() - beginTime.getMinute()) == 59
                    && (endTime.getSecond() - beginTime.getSecond()) == 59;
            //一定要加个常规，否则认为全部
            List<Integer> targetIdList = Lists.newArrayList(GdDisplayModeEnum.COMMON.getCode());
            if (!isFullDay) {
                //https://www.tapd.cn/67874887/prong/stories/view/1167874887004507838
                //【品牌广告】信息流小卡分时投放升级
                targetIdList.add(GdDisplayModeEnum.FIRST_BRUSH.getCode());
            }
            return targetIdList;
        } else if (Objects.equals(location, LocationType.BIG_CARD)
                || Objects.equals(location, LocationType.STORY)
                || Objects.equals(location, LocationType.RECOMMEND)) {
            if (Objects.equals(task.getDisplayMode(), GdDisplayModeEnum.FIRST_BRUSH)) {
                //只返回首刷
                return Lists.newArrayList(task.getDisplayMode().getCode());
            }
        }
        //默认空
        return Lists.newLinkedList();
    }
}
