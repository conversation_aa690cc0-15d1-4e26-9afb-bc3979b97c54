package com.bilibili.brand.biz.report.query.impl;

import com.bilibili.brand.biz.report.converter.DataReportResultConverter;
import com.bilibili.brand.biz.report.dto.DataReportResultDto;
import com.bilibili.brand.biz.report.dto.OrderReportDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseOrderDataReportQueryHandler extends AbstractDataReportQueryHandler<OrderReportDto> {

    @Override
    protected List<DataReportResultDto> convertToDataReportResultDto(List<OrderReportDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return new ArrayList<>();
        }
        return records.stream()
                .map(DataReportResultConverter.MAPPER::toDataReportResultDto)
                .collect(Collectors.toList());
    }
}
