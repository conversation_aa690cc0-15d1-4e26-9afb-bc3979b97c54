package com.bilibili.brand.biz.schedule.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.account.dto.AccountGdQuotaDto;
import com.bilibili.brand.api.account.service.IAccountGdQuotaService;
import com.bilibili.brand.api.common.enums.FrequencyUnit;
import com.bilibili.brand.api.common.enums.ShowPriorityType;
import com.bilibili.brand.api.common.exception.ScheduleExceptionCode;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.GdScheduleDateDto;
import com.bilibili.brand.api.schedule.dto.NewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IGdPlusScheduleValidate;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.ssa.platform.common.enums.SystemConfig;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 校验逻辑copy自
 * @see ScheduleServiceDelegate
 *
 * <AUTHOR>
 * @date 2023/2/13
 */
@Service
@Slf4j
public class GdPlusScheduleValidate implements IGdPlusScheduleValidate {
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IAccountGdQuotaService accountGdQuotaService;
    @Autowired
    private QueryScheduleService querySheduleService;
    @Override
    public void checkCreateScheduleBaseInfo(NewScheduleDto newScheduleDto) {
        boolean paramsCorrect;
        try {
            paramsCorrect = validateNewScheduleDto(newScheduleDto);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        Assert.isTrue(paramsCorrect, "创建排期参数错误");
    }

    @Override
    public void validateQotaMargin(long expectTotalPrice, Integer accountId, List<Timestamp> dates) {

        if (accountId == null) {
            return;
        }

        try {
            validateQotaMargin(expectTotalPrice, accountId, dates, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private boolean validateNewScheduleDto(NewScheduleDto newScheduleDto) throws ServiceException {
        if (null == newScheduleDto) {
            return true;
        }
        if (null == newScheduleDto.getOrderId()
                || StringUtils.isEmpty(newScheduleDto.getName())
                || null == newScheduleDto.getTotalImpression()
                || newScheduleDto.getTotalImpression() <= 0
                || null == newScheduleDto.getSpeedMode()
                || CollectionUtils.isEmpty(newScheduleDto.getScheduleDates())
                || null == newScheduleDto.getTemplateId()
                || CollectionUtils.isEmpty(newScheduleDto.getPlatformIds())
                || Strings.isNullOrEmpty(newScheduleDto.getRequestId())) {
            return true;
        }

        if (newScheduleDto.getFrequencyUnit() != null) {
            FrequencyUnit.getByCode(newScheduleDto.getFrequencyUnit());
        }

        if (newScheduleDto.getFrequencyLimit() != null && newScheduleDto.getFrequencyLimit() < 1) {
            return true;
        }

        this.sortScheduleDates(newScheduleDto.getScheduleDates());

        if (newScheduleDto.getIsTodaySchedule().equals(IsValid.TRUE.getCode())) {
            validateTodayScheduleDateAndHour(newScheduleDto.getScheduleDates(),
                    ShowPriorityType.PRIORITY_TYPES.contains(newScheduleDto.getShowPriority()) ?
                            newScheduleDto.getHourRange().get(0).getHour() : newScheduleDto.getHour(),
                    newScheduleDto.getShowPriority());
        } else {
            this.validateTimeSegmentRange(newScheduleDto.getScheduleDates());
        }

        return true;
    }

    private void validateTodayScheduleDateAndHour(List<Timestamp> dates, int hour, Integer showPriority) throws ServiceException {

        if (CollectionUtils.isEmpty(dates)) {
            throw new ServiceException(ScheduleExceptionCode.CREATE_TODAY_SCHEDULE_TIME_NOT_NULL);
        }

        if (dates.size() > 1 && ShowPriorityType.PRIORITY_TYPES.contains(showPriority)) {
            throw new ServiceException(ScheduleExceptionCode.TODAY_SCHEDULE_ONLY_ONE_DAY);
        }

        Timestamp curDate = Utils.getBeginOfDay(dates.get(0));
        Timestamp today = Utils.getToday();

        if (!curDate.equals(today)) {
            throw new ServiceException(ScheduleExceptionCode.TODAY_SCHEDULE_ONLY_ONE_DAY);
        }

        validateTodayScheduleHour(hour);
    }

    private void validateTodayScheduleHour(int hour) throws ServiceException {
        int curHour = Utils.getHour(Utils.getNow());

        if (hour - curHour < 1 || hour > 23) {
            throw new ServiceException(ScheduleExceptionCode.CREATE_TODAY_SCHEDULE_TIME_INVALID.getCode(),
                    String.format(ScheduleExceptionCode.CREATE_TODAY_SCHEDULE_TIME_INVALID.getMessage(), curHour + 1));
        }
    }

    private void validateTimeSegmentRange(List<Timestamp> scheduleDates) throws ServiceException {
        if (CollectionUtils.isEmpty(scheduleDates)) {
            return;
        }
        this.sortScheduleDates(scheduleDates);
        int minStep = Integer.parseInt(systemConfigService.getValueByItem(SystemConfig.GD_SCHEDULE_MIN_STEP_SIZE));
        int maxStep = Integer.parseInt(systemConfigService.getValueByItem(SystemConfig.GD_SCHEDULE_MAX_STEP_SIZE));
        if (scheduleDates.get(0).getTime() < Utils.getSomeDayAfter(Utils.getToday(), minStep).getTime()) {
            throw new ServiceException(ScheduleExceptionCode.TIME_SEGMENT_OUT_OF_RANGE);
        }
        if (scheduleDates.get(scheduleDates.size() - 1).getTime()
                > Utils.getSomeDayAfter(Utils.getToday(), maxStep).getTime()) {
            throw new ServiceException(ScheduleExceptionCode.TIME_SEGMENT_OUT_OF_RANGE);
        }
    }

    private void sortScheduleDates(List<Timestamp> scheduleDates) {
        Collections.sort(scheduleDates);
    }

    public void validateQotaMargin(long expectTotalPrice, int accountId,
                                    List<Timestamp> dates, Integer skipScheduleId) throws ServiceException {
        log.info("validateQotaMargin expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                expectTotalPrice, accountId, dates, skipScheduleId);

        BigDecimal expectTotalPriceYuan = Utils.fromFenToYuan(expectTotalPrice);

        Map<Integer, List<Timestamp>> dateMap = Utils.getDateMapByMonth(dates);

        log.info("validateQotaMargin expectTotalPriceYuan:{}, dateMap:{}," +
                        " expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                expectTotalPriceYuan, dateMap, expectTotalPrice, accountId, dates, skipScheduleId);

        List<Integer> curAndNextMonth = Utils.getMonthByOffsets(Arrays.asList(0, 1));
        Integer curMonth = curAndNextMonth.get(0);
        Integer nextMonth = curAndNextMonth.get(1);
        log.info("validateQotaMargin curMonth:{}, nextMonth:{}, expectTotalPrice:{}," +
                        " accountId:{}, dates:{}, skipScheduleId:{}",
                curMonth, nextMonth, expectTotalPrice, accountId, dates, skipScheduleId);

        BigDecimal curMonthExpectTotalPrice = expectTotalPriceYuan.multiply(BigDecimal
                .valueOf(1.0 * dateMap.getOrDefault(curMonth, Collections.emptyList()).size() / dates.size()));
        BigDecimal nextMonthExpectTotalPrice = expectTotalPriceYuan.multiply(BigDecimal
                .valueOf(1.0 * dateMap.getOrDefault(nextMonth, Collections.emptyList()).size() / dates.size()));

        log.info("validateQotaMargin curMonthExpectTotalPrice:{}," +
                        " nextMonthExpectTotalPrice:{}, expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                curMonthExpectTotalPrice, nextMonthExpectTotalPrice, expectTotalPrice, accountId, dates, skipScheduleId);

        AccountGdQuotaDto accountGdQuotaDto = accountGdQuotaService.getByAccountId(accountId);
        log.info("validateQotaMargin accountGdQuotaDto:{}," +
                        " expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                accountGdQuotaDto, expectTotalPrice, accountId, dates, skipScheduleId);

        if (accountGdQuotaDto == null) {
            log.error("validateQotaMargin the account(id:{}) is not exist in the accountGdQuota.", accountId);
            if (curMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0) {
                throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_CURRENT_MONTH_EMPTY);
            } else {
                throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_NEXT_MONTH_EMPTY);
            }
        }

        if (curMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0 && accountGdQuotaDto.getDefaultGdQuota()
                .add(accountGdQuotaDto.getCurrentMonthAdjustQuota()).compareTo(curMonthExpectTotalPrice) < 0) {
            log.error("validateQotaMargin current month total quota is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_CURRENT_MONTH_EMPTY);
        }

        if (nextMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0 && accountGdQuotaDto.getDefaultGdQuota()
                .add(accountGdQuotaDto.getNextMonthAdjustQuota()).compareTo(nextMonthExpectTotalPrice) < 0) {
            log.error("validateQotaMargin next month total quota is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_NEXT_MONTH_EMPTY);
        }

        Map<Integer, List<ScheduleDto>> scheduleMap = querySheduleService
                .getQuotaMarginByAccountIdAndMonths(accountId, dates);
        log.info("validateQotaMargin scheduleMap:{}, expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                scheduleMap, expectTotalPrice, accountId, dates, skipScheduleId);

        if (skipScheduleId != null) {
            scheduleMap.entrySet().forEach(e -> e.setValue(e.getValue()
                    .stream()
                    .filter(v -> !v.getScheduleId().equals(skipScheduleId))
                    .collect(Collectors.toList())));
        }

        log.info("validateQotaMargin scheduleMap:{}, expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                scheduleMap, expectTotalPrice, accountId, dates, skipScheduleId);

        Map<Integer, BigDecimal> quotaOccupyMap = Maps.newHashMap();

        for (Map.Entry<Integer, List<ScheduleDto>> e : scheduleMap.entrySet()) {
            quotaOccupyMap.put(e.getKey(), e.getValue()
                    .stream()
                    .map(v -> getScheduleTotlePriceByMonth(v, e.getKey()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        log.info("validateQotaMargin quotaOccupyMap:{}, expectTotalPrice:{}," +
                        " accountId:{}, dates:{}, skipScheduleId:{}",
                quotaOccupyMap, expectTotalPrice, accountId, dates, skipScheduleId);

        BigDecimal curMonthQuotaMargin = accountGdQuotaDto.getDefaultGdQuota()
                .add(accountGdQuotaDto.getCurrentMonthAdjustQuota())
                .subtract(quotaOccupyMap.getOrDefault(curMonth, BigDecimal.ZERO));

        BigDecimal nextMonthQuotaMargin = accountGdQuotaDto.getDefaultGdQuota()
                .add(accountGdQuotaDto.getNextMonthAdjustQuota())
                .subtract(quotaOccupyMap.getOrDefault(nextMonth, BigDecimal.ZERO));

        log.info("validateQotaMargin curMonthQuotaMargin:{}, " +
                        "nextMonthQuotaMargin:{}, expectTotalPrice:{}, accountId:{}, dates:{}, skipScheduleId:{}",
                curMonthQuotaMargin, nextMonthQuotaMargin,
                expectTotalPrice, accountId, dates, skipScheduleId);

        if (curMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0
                && curMonthQuotaMargin.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("validateQotaMargin current month quota margin is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_CURRENT_MONTH_EMPTY);
        }

        if (nextMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0
                && nextMonthQuotaMargin.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("validateQotaMargin next month quota margin is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_SURPLUS_NEXT_MONTH_EMPTY);
        }

        if (curMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0
                && curMonthQuotaMargin.compareTo(curMonthExpectTotalPrice) < 0) {
            log.error("validateQotaMargin current month quota margin is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_CURRENT_MONTH_NOT_ENOUGH.getCode(),
                    String.format("%s, 本月还剩余%.2f元",
                            ScheduleExceptionCode.QUOTA_CURRENT_MONTH_NOT_ENOUGH.getMessage(),
                            curMonthQuotaMargin.doubleValue()));
        }

        if (nextMonthExpectTotalPrice.compareTo(BigDecimal.ZERO) > 0
                && nextMonthQuotaMargin.compareTo(nextMonthExpectTotalPrice) < 0) {
            log.error("validateQotaMargin next month quota margin is not enough");
            throw new ServiceException(ScheduleExceptionCode.QUOTA_NEXT_MONTH_NOT_ENOUGH.getCode(),
                    String.format("%s, 下月还剩余%.2f元",
                            ScheduleExceptionCode.QUOTA_NEXT_MONTH_NOT_ENOUGH.getMessage(),
                            nextMonthQuotaMargin.doubleValue()));
        }

    }

    private BigDecimal getScheduleTotlePriceByMonth(ScheduleDto dto, int month) {
        double count = 0.0;

        if (CollectionUtils.isEmpty(dto.getScheduleDates())) {
            return BigDecimal.ZERO;
        }

        for (GdScheduleDateDto dateDto : dto.getScheduleDates()) {
            if (Utils.getMonth(dateDto.getScheduleDate()) == month) {
                count++;
            }
        }

        return Utils.fromFenToYuan(count / dto.getScheduleDates().size()
                * dto.getCostPrice() * dto.getTotalImpression());
    }
}
