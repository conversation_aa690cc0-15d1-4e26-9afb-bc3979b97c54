package com.bilibili.brand.biz.resource.query;

import java.util.List;

/**
 * Created by zhongyuan on 2016/9/29.
 */
public class CpmPricingPoQuery {

    private List<Integer> pricingTypeList;

    private List<Integer> statusList;

    private Boolean isDeleted;

    public List<Integer> getPricingTypeList() {
        return pricingTypeList;
    }

    public CpmPricingPoQuery setPricingTypeList(List<Integer> pricingTypeList) {
        this.pricingTypeList = pricingTypeList;
        return this;
    }

    public List<Integer> getStatusList() {
        return statusList;
    }

    public CpmPricingPoQuery setStatusList(List<Integer> statusList) {
        this.statusList = statusList;
        return this;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public CpmPricingPoQuery setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
        return this;
    }
}
