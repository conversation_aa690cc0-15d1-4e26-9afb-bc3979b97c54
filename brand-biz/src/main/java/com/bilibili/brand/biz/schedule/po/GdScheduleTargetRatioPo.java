package com.bilibili.brand.biz.schedule.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdScheduleTargetRatioPo implements Serializable {
    /**
     * 自增
     */
    private Integer id;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 资源id
     */
    private Integer source;

    /**
     * 定向,提供给引擎使用
     */
    private Integer targetKeyId;

    /**
     * 比率
     */
    private Integer ratio;

    /**
     * 时间序列,如2016-10-26 00:00:00
     */
    private Timestamp launchDay;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 变更时间
     */
    private Timestamp mtime;

    /**
     * 排期id
     */
    private Integer scheduleId;

    /**
     * 定向，提供给品牌系统使用
     */
    private Integer innerTargetKeyId;

    /**
     * 曝光量，提供给引擎使用
     */
    private Integer showCount;

    /**
     * 是否GD+ 0-否 1-是
     */
    private Integer isGdPlus;

    private static final long serialVersionUID = 1L;
}