package com.bilibili.brand.biz.log.bean;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/8/30 17:20
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@LogFlag(gdLogFlag = GdLogFlag.SCHEDULE_CONTROL_COMPLETION)
public class ScheduleControlCompletionLogBean implements Serializable {
    private static final long serialVersionUID = -5683327712873546132L;
    @LogProperty("日志id")
    @ExcelResources(title = "ID")
    private Long logId;

    @LogProperty("排期id")
    @ExcelResources(title = "排期ID")
    private Integer scheduleId;

    @LogProperty("期望完成率")
    @ExcelResources(title = "期望完成率")
    private Float expectCompletionRatio;

    @LogProperty("操作人")
    @ExcelResources(title = "操作人")
    private String operatorName;

    @LogProperty("操作时间")
    @ExcelResources(title = "操作时间")
    private Timestamp mtime;

    @LogProperty("操作备注")
    @ExcelResources(title = "备注")
    private String remark;
}
