package com.bilibili.brand.biz.resource.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResTargetItemPo implements Serializable {
    /**
     * 定向ID
     */
    private Integer id;

    /**
     * 定向类型
     */
    private Integer type;

    /**
     * 定向名称
     */
    private String name;

    /**
     * 定向父ID
     */
    private Integer parentId;

    /**
     * 排序顺序
     */
    private Integer sortOrder;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 变更时间
     */
    private Timestamp mtime;

    /**
     * 10-地域分类 11-国家 12-省份 13-城市等
     */
    private Integer subType;

    /**
     * 映射内容
     */
    private String mappingContent;

    private static final long serialVersionUID = 1L;
}