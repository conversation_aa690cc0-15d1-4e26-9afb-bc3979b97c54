package com.bilibili.brand.biz.creative.convert;

import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.api.common.enums.SsaTransitionModeEnum;
import com.bilibili.brand.api.common.enums.SsaVersion;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.cpt.NewTopViewCptCreativeDto;
import com.bilibili.brand.api.cpt.UpdateTopviewCptCreativeDto;
import com.bilibili.brand.api.creative.dto.NewExternalTopViewDto;
import com.bilibili.brand.api.creative.dto.TopViewHfJumpDto;
import com.bilibili.brand.api.creative.dto.UpdateExternalTopViewDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.utils.Md5Util;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeDto;
import com.bilibili.cpt.platform.biz.enumerate.MaterialType;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewSplashScreenImageDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewSplashScreenVersionControlDto;
import com.bilibili.ssa.platform.biz.handler.WebpHandler;
import com.bilibili.ssa.platform.common.enums.BannerShowType;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import com.bilibili.ssa.platform.common.enums.SsaButtonStyle;
import com.bilibili.ssa.platform.common.enums.SsaGuideMaterialTypeEnum;
import com.google.common.collect.ImmutableMap;
import io.swagger.models.auth.In;
import javafx.util.Pair;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.common.enums.SsaConstants.MAX_VERSION;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020.12.03 12:11
 */
@Component
public class TopViewConvert {

    @Autowired
    private WebpHandler webpHandler;

    @Value("${topView.startVersion.iphone:5270000}")
    private Integer topViewStartVersionIphone;

    @Value("${topView.startVersion.android:5270000}")
    private Integer topViewStartVersionAndroid;

    @Value("${topVideo.startVersion.iphone:75800000}")
    private Integer topVideoStartVersionIphone;

    @Value("${topVideo.startVersion.android:7580000}")
    private Integer topVideoStartVersionAndroid;

    @Value("${eggVideo.startVersion.iphone:77900000}")
    private Integer eggVideoStartVersionIphone;

    @Value("${eggVideo.startVersion.android:7790000}")
    private Integer eggVideoStartVersionAndroid;

    @Value("${brandCard.startVersion.iphone:77300000}")
    private Integer brandCardStartVersionIphone;

    @Value("${brandCard.startVersion.android:7730000}")
    private Integer brandCardStartVersionAndroid;


    public NewTopViewCptCreativeDto toNewCptCreativeDto(NewExternalTopViewDto newExternalTopViewDto,
                                                        ScheduleDto scheduleDto, Map<Integer, Integer> platformAppPackageMap) {
        TopViewHfJumpDto hfJump = newExternalTopViewDto.getHfJump();
        NewTopViewCptCreativeDto creativeDto = NewTopViewCptCreativeDto.builder()
                .gdScheduleId(newExternalTopViewDto.getScheduleId())
                .creativeName(newExternalTopViewDto.getCreativeName())
                .videoId(0L)
                .cmMark(newExternalTopViewDto.getCmMark())
                .isIdfaEncrypted(newExternalTopViewDto.getHfIsIdfaEncrypted())
                .shareState(newExternalTopViewDto.getShareState())
                .shareTitle(newExternalTopViewDto.getShareTitle())
                .shareSubTitle(newExternalTopViewDto.getShareSubTitle())
                .shareImageUrl(newExternalTopViewDto.getShareImageUrl())
                .shareImageHash(newExternalTopViewDto.getShareImageHash())
                .cptJumpDTOS(hfJump.getJumps())
                .hfAndroidCustomizedClickUrl(newExternalTopViewDto.getHfAndroidCustomizedClickUrl())
                .hfAndroidCustomizedImpUrl(newExternalTopViewDto.getHfAndroidCustomizedImpUrl())
                .hfIOSCustomizedClickUrl(newExternalTopViewDto.getHfIosCustomizedClickUrl())
                .hfIOSCustomizedImpUrl(newExternalTopViewDto.getHfIosCustomizedImpUrl())
                .timeTarget(newExternalTopViewDto.getTimeTarget())
                .scheduleDates(newExternalTopViewDto.getHfSchedules())
                .hfIpVideoId(newExternalTopViewDto.getHfIpVideoId())
                .title(newExternalTopViewDto.getNewHfTitle())
                .imageUrl(newExternalTopViewDto.getNewHfImageUrl())
                .imageHash(newExternalTopViewDto.getNewHfImageHash())
                .imageMd5(Md5Util.getMd5FromHash(newExternalTopViewDto.getNewHfImageHash()))
                .imageJumpType(newExternalTopViewDto.getNewHfImageJumpType())
                .imageJumpUrl(newExternalTopViewDto.getNewHfImageJumpUrl())
                .extImageUrl(newExternalTopViewDto.getNewHfExtImageUrl())
                .extImageMd5(Md5Util.getMd5FromHash(newExternalTopViewDto.getNewHfExtImageHash()))
                .templateId(newExternalTopViewDto.getNewHfTemplateId())
                .videoDto(newExternalTopViewDto.getNewHfVideo())
                .isCustomizedBrandInfo(newExternalTopViewDto.getIsCustomizedNewHfBrandInfo())
                .brandName(newExternalTopViewDto.getNewHfBrandName())
                .faceUrl(newExternalTopViewDto.getNewHfFaceUrl())
                .faceMd5(newExternalTopViewDto.getNewHfFaceMd5())
                .manuscriptInfo(newExternalTopViewDto.getManuscriptInfo())
                .platformAppPackageMap(platformAppPackageMap)
                .hfAndroidCustomizedClickUrlList(newExternalTopViewDto.getHfAndroidCustomizedClickUrlList())
                .hfIosCustomizedClickUrlList(newExternalTopViewDto.getHfIosCustomizedClickUrlList())
                .hfCustomizedClickUrlList(newExternalTopViewDto.getHfCustomizedClickUrlList())
                .build();

        //尝试将转存webp（解决带宽占用高问题），如果失败则使用原图兜底
        if (!this.webpHandler.isWebp(creativeDto.getImageUrl())) {
            BfsUploadResult uploadResult = this.webpHandler.turnToWebpWithSafety(
                    SsaNewSplashScreenImageDto.builder()
                            .url(creativeDto.getImageUrl())
                            .width(0)
                            .height(0)
                            .build());
            if (Objects.nonNull(uploadResult)) {
                creativeDto.setImageUrl(uploadResult.getUrl());
                creativeDto.setImageMd5(uploadResult.getMd5());
                creativeDto.setImageHash(MaterialType.IMAGE.getHash(
                        CptCreativeDto.builder()
                                .imageUrl(creativeDto.getImageUrl())
                                .imageMd5(creativeDto.getImageMd5())
                                .templateId(creativeDto.getTemplateId())
                                .build()));
            }
        }

        if (Objects.equals(scheduleDto.getPromotionPurposeType(), PromotionPurposeType.LANDING_PAGE.getCode())) {
            //topview创意只有在推广目的是落地页时才会落首焦创意小程序信息
            creativeDto.setMiniProgram(hfJump.getMiniProgram());
        }

        return creativeDto;
    }

    public UpdateTopviewCptCreativeDto toUpdateCptCreativeDto(UpdateExternalTopViewDto updateExternalTopViewDto,
                                                              ScheduleDto scheduleDto, Map<Integer, Integer> platformAppPackageMap) {
        TopViewHfJumpDto hfJump = updateExternalTopViewDto.getHfJump();
        UpdateTopviewCptCreativeDto creativeDto = UpdateTopviewCptCreativeDto.builder()
                .creativeName(updateExternalTopViewDto.getCreativeName())
                .videoId(0L)
                .extImageMd5("")
                .cmMark(updateExternalTopViewDto.getCmMark())
                .isIdfaEncrypted(updateExternalTopViewDto.getHfIsIdfaEncrypted())
                .cptJumpDTOS(hfJump.getJumps())
                .hfAndroidCustomizedClickUrl(updateExternalTopViewDto.getHfAndroidCustomizedClickUrl())
                .hfAndroidCustomizedImpUrl(updateExternalTopViewDto.getHfAndroidCustomizedImpUrl())
                .hfIOSCustomizedClickUrl(updateExternalTopViewDto.getHfIosCustomizedClickUrl())
                .hfIOSCustomizedImpUrl(updateExternalTopViewDto.getHfIosCustomizedImpUrl())
                .shareState(updateExternalTopViewDto.getShareState())
                .shareTitle(updateExternalTopViewDto.getShareTitle())
                .shareSubTitle(updateExternalTopViewDto.getShareSubTitle())
                .shareImageUrl(updateExternalTopViewDto.getShareImageUrl())
                .shareImageHash(updateExternalTopViewDto.getShareImageHash())
                .scheduleDates(updateExternalTopViewDto.getHfSchedules())
                .timeTarget(updateExternalTopViewDto.getTimeTarget())
                .hfIpVideoId(updateExternalTopViewDto.getHfIpVideoId())
                .templateId(updateExternalTopViewDto.getNewHfTemplateId())
                .title(updateExternalTopViewDto.getNewHfTitle())
                .imageUrl(updateExternalTopViewDto.getNewHfImageUrl())
                .imageHash(updateExternalTopViewDto.getNewHfImageHash())
                .imageMd5(Md5Util.getMd5FromHash(updateExternalTopViewDto.getNewHfImageHash()))
                .imageJumpType(updateExternalTopViewDto.getNewHfImageJumpType())
                .imageJumpUrl(updateExternalTopViewDto.getNewHfImageJumpUrl())
                .extImageUrl(updateExternalTopViewDto.getNewHfExtImageUrl())
                .extImageMd5(Md5Util.getMd5FromHash(updateExternalTopViewDto.getNewHfExtImageHash()))
                .videoDto(updateExternalTopViewDto.getNewHfVideo())
                .isCustomizedBrandInfo(updateExternalTopViewDto.getIsCustomizedNewHfBrandInfo())
                .brandName(updateExternalTopViewDto.getNewHfBrandName())
                .faceUrl(updateExternalTopViewDto.getNewHfFaceUrl())
                .faceMd5(updateExternalTopViewDto.getNewHfFaceMd5())
                .manuscriptInfo(updateExternalTopViewDto.getManuscriptInfo())
                .platformAppPackageMap(platformAppPackageMap)
                .hfAndroidCustomizedClickUrlList(updateExternalTopViewDto.getHfAndroidCustomizedClickUrlList())
                .hfIosCustomizedClickUrlList(updateExternalTopViewDto.getHfIosCustomizedClickUrlList())
                .hfCustomizedClickUrlList(updateExternalTopViewDto.getHfCustomizedClickUrlList())
                .build();

        //尝试将转存webp（解决带宽占用高问题），如果失败则使用原图兜底
        if (!this.webpHandler.isWebp(creativeDto.getImageUrl())) {
            BfsUploadResult uploadResult = this.webpHandler.turnToWebpWithSafety(
                    SsaNewSplashScreenImageDto.builder()
                            .url(creativeDto.getImageUrl())
                            .width(0)
                            .height(0)
                            .build());
            if (Objects.nonNull(uploadResult)) {
                creativeDto.setImageUrl(uploadResult.getUrl());
                creativeDto.setImageMd5(uploadResult.getMd5());
                creativeDto.setImageHash(MaterialType.IMAGE.getHash(
                        CptCreativeDto.builder()
                                .imageUrl(creativeDto.getImageUrl())
                                .imageMd5(creativeDto.getImageMd5())
                                .templateId(creativeDto.getTemplateId())
                                .build()));
            }
        }

        if (Objects.equals(scheduleDto.getPromotionPurposeType(), PromotionPurposeType.LANDING_PAGE.getCode())) {
            //topview创意只有在推广目的是落地页时才会落首焦创意小程序信息
            creativeDto.setMiniProgram(hfJump.getMiniProgram());
        }

        return creativeDto;
    }

    public List<SsaNewSplashScreenVersionControlDto> parseSsaVersionControl(List<Integer> platforms, Integer hfAdType,
                                                                            Integer topViewVideoPlayMode, Integer buttonStyle,
                                                                            Integer guideMaterialType, Integer isCustomizedNewHfBrandInfo, Integer ssaAdType,
                                                                            Boolean isEnableLiveBooking, Integer transitionMode) {
        Map<Integer, Integer> versionMap = getDefaultVersionPlatformMap(hfAdType, topViewVideoPlayMode, buttonStyle, guideMaterialType, isCustomizedNewHfBrandInfo, ssaAdType, isEnableLiveBooking, transitionMode);

        return platforms.stream()
                .filter(versionMap::containsKey)
                .map(id -> SsaNewSplashScreenVersionControlDto.builder()
                        .platformId(id)
                        .startVersion(versionMap.get(id))
                        .endVersion(MAX_VERSION)
                        .build())
                .collect(Collectors.toList());
    }

    public Map<Integer, Integer> getDefaultVersionPlatformMap(Integer hfAdType, Integer topViewVideoPlayMode,
                                                              Integer buttonStyle, Integer guideMaterialType,
                                                              Integer isCustomizedNewHfBrandInfo, Integer ssaAdType,
                                                              Boolean isEnableLiveBooking, Integer transitionMode) {
        Map<Integer, Integer> versionMap;

        // 过渡视频功能版本控制（优先级最高）
        if (SsaTransitionModeEnum.isCustom(transitionMode)) {
            versionMap = ImmutableMap.of(
                    PlatformType.ANDROID.getCode(), 8470001,
                    PlatformType.IPHONE.getCode(), 84700001);
        } else if (BooleanUtils.isTrue(isEnableLiveBooking)) {
            versionMap = SsaVersion.SSA_LIVE_BOOKING_VERSION.stream()
                    .filter(pair -> Objects.equals(PlatformType.IPHONE.getCode(), pair.getKey())
                            || Objects.equals(PlatformType.ANDROID.getCode(), pair.getKey()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        } else if (Objects.equals(isCustomizedNewHfBrandInfo, YesOrNoEnum.YES.getCode())) {
            versionMap = SsaVersion.TOP_VIEW_NEW_HF_CUSTOM_BRAND_INFO_VERSION.stream()
                    .filter(pair -> Objects.equals(PlatformType.IPHONE.getCode(), pair.getKey())
                            || Objects.equals(PlatformType.ANDROID.getCode(), pair.getKey()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        } else if (Objects.equals(ssaAdType, SsaAdType.IMAGE.getCode())) {
            versionMap = SsaVersion.TOP_VIEW_IMAGE_SPLASH_VERSION.stream()
                    .filter(pair -> Objects.equals(PlatformType.IPHONE.getCode(), pair.getKey())
                            || Objects.equals(PlatformType.ANDROID.getCode(), pair.getKey()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        } else if (SsaVideoPlayModeEnum.isAutoContinuePlay(topViewVideoPlayMode)) {
            //https://www.tapd.cn/67874887/prong/stories/view/1167874887004394974
            versionMap = ImmutableMap.of(PlatformType.IPHONE.getCode(), 82200000,
                    PlatformType.ANDROID.getCode(), 8220000);
        } else if (SsaButtonStyle.isProductButton(buttonStyle)) {
            versionMap = SsaButtonStyle.TWIST_PRODUCT_CLICK_BUTTON.getVersions().stream()
                    .filter(pair -> Objects.equals(PlatformType.IPHONE.getCode(), pair.getKey())
                            || Objects.equals(PlatformType.ANDROID.getCode(), pair.getKey()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        } else if (Objects.equals(guideMaterialType, SsaGuideMaterialTypeEnum.IMAGE.getCode())) {
            versionMap = SsaVersion.SSA_CUSTOMIZED_GUIDE_WEBP_VERSION.stream()
                    .filter(pair -> Objects.equals(PlatformType.IPHONE.getCode(), pair.getKey())
                            || Objects.equals(PlatformType.ANDROID.getCode(), pair.getKey()))
                    .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        } else if (Objects.equals(topViewVideoPlayMode, SsaVideoPlayModeEnum.EASTER_EGG.getCode())) {
            versionMap = ImmutableMap.of(
                    PlatformType.IPHONE.getCode(), eggVideoStartVersionIphone,
                    PlatformType.ANDROID.getCode(), eggVideoStartVersionAndroid);
        } else if (SsaButtonStyle.isBrandCardButton(buttonStyle)) {
            // 品牌卡
            versionMap = ImmutableMap.of(
                    PlatformType.IPHONE.getCode(), brandCardStartVersionIphone,
                    PlatformType.ANDROID.getCode(), brandCardStartVersionAndroid);
        } else if (Objects.equals(hfAdType, BannerShowType.ARCHIVE.getCode())) {
            versionMap = ImmutableMap.of(
                    PlatformType.IPHONE.getCode(), topViewStartVersionIphone,
                    PlatformType.ANDROID.getCode(), topViewStartVersionAndroid);
        } else {
            versionMap = ImmutableMap.of(
                    PlatformType.IPHONE.getCode(), topVideoStartVersionIphone,
                    PlatformType.ANDROID.getCode(), topVideoStartVersionAndroid);
        }
        return versionMap;
    }


}
