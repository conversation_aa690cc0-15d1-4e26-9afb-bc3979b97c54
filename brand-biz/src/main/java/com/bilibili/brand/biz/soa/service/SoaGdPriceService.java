/**
 * <AUTHOR>
 * @date 2018年1月3日
 */

package com.bilibili.brand.biz.soa.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.resource.price.IGdPriceService;
import com.bilibili.brand.api.resource.price.gd.GdPriceInfoDTO;
import com.bilibili.brand.api.soa.service.ISoaGdPriceService;
import com.bilibili.cpt.platform.common.GdSalesType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SoaGdPriceService implements ISoaGdPriceService {

    @Autowired
    private IGdPriceService cpmPriceService;


    @Deprecated
    @Override
    public List<GdPriceInfoDTO> queryGdPriceInfoDTO() {
        return cpmPriceService.queryGdPriceInfoDTO(null,null);
    }

    @Override
    public void update(GdPriceInfoDTO infoDTO, Operator operator) throws Exception {
        cpmPriceService.updateOrInsert(infoDTO, operator);
    }

    @Override
    public List<GdPriceInfoDTO> queryGdPriceInfoDTO(Integer cycleId, List<GdSalesType> gdSalesType) {
        return cpmPriceService.queryGdPriceInfoDTO(cycleId, gdSalesType);
    }
}
