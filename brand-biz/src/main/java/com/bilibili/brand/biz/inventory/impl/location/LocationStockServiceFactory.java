package com.bilibili.brand.biz.inventory.impl.location;

import com.bilibili.brand.biz.inventory.ILocationStockService;
import com.bilibili.cpt.platform.common.LocationType;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/10 15:50
 */
@Service
public class LocationStockServiceFactory {

    private final Map<LocationType, ILocationStockService> locationStockServiceMap;

    public LocationStockServiceFactory(List<ILocationStockService> locationStockServices) {
        this.locationStockServiceMap = locationStockServices.stream()
                //如果出现相同的ResourceType则直接报错
                .collect(Collectors.toMap(ILocationStockService::getLocationType, Function.identity()));
    }

    public ILocationStockService getLocationStockService(LocationType locationType) {
        ILocationStockService locationStockService = locationStockServiceMap.get(locationType);
        if (Objects.nonNull(locationStockService)) {
            return locationStockService;
        }
        //如果没有明确的实现，则统一路由到默认
        return locationStockServiceMap.get(LocationType.DEFAULT);
    }
}
