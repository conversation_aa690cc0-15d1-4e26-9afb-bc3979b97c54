package com.bilibili.brand.biz.soa.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.slot.ISlotService;
import com.bilibili.brand.api.resource.slot.Slot;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.soa.dto.GdScheduleFlowDto;
import com.bilibili.brand.api.soa.dto.GdScheduleFlowQueryDto;
import com.bilibili.brand.api.soa.dto.TargetRatioDto;
import com.bilibili.brand.api.soa.service.ISoaBackdoorGdFlowAllocationService;
import com.bilibili.brand.biz.schedule.dao.GdFlowAllocationDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTargetRatioDao;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPo;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPoExample;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetRatioPoExample;
import com.bilibili.brand.platform.report.api.dto.StatScheduleDto;
import com.bilibili.brand.platform.report.api.service.IStatScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2019/10/23.
 */
@Service
public class SoaBackdoorGdFlowAllocationService implements ISoaBackdoorGdFlowAllocationService {
    @Autowired
    private IQueryScheduleService querySheduleService;
    @Autowired
    private GdFlowAllocationDao gdFlowAllocationDao;
    @Autowired
    private GdScheduleTargetRatioDao gdScheduleTargetRatioDao;

    @Autowired
    private IStatScheduleService statScheduleService;

    @Autowired
    private ISlotService slotService;
    @Autowired
    private IGdOrderService gdOrderService;

    @Override
    public List<GdScheduleFlowDto> query(GdScheduleFlowQueryDto gdScheduleFlowQueryDto) throws ServiceException {
        Assert.notNull(gdScheduleFlowQueryDto, "gdScheduleFlowQueryDto can not be null");
        Assert.notNull(gdScheduleFlowQueryDto.getLaunchDay(), "launchDay can not be null");

        GdFlowAllocationPoExample gdFlowAllocationPoExample = new GdFlowAllocationPoExample();
        gdFlowAllocationPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andLaunchDayEqualTo(gdScheduleFlowQueryDto.getLaunchDay());
        List<GdFlowAllocationPo> gdFlowAllocationPos = gdFlowAllocationDao.selectByExample(gdFlowAllocationPoExample);

        Map<Integer, GdFlowAllocationPo> gdFlowAllocationPoMap = gdFlowAllocationPos.stream().collect(Collectors.toMap(GdFlowAllocationPo::getScheduleId, Function.identity(), (key1, key2) -> key2));
        List<Integer> scheduleIds = gdFlowAllocationPos.stream().map(GdFlowAllocationPo::getScheduleId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scheduleIds)) {
            return Collections.emptyList();
        }

        List<ScheduleDto> scheduleDtos = querySheduleService.getSchedulesInIds(scheduleIds);
        if (CollectionUtils.isEmpty(scheduleDtos)) {
            return Collections.emptyList();
        }

        List<StatScheduleDto> statScheduleDtos = statScheduleService.getInScheduleIdsGroupByTime(scheduleIds, Utils.getBeginOfDay(gdScheduleFlowQueryDto.getLaunchDay()), Utils.getEndOfDay(gdScheduleFlowQueryDto.getLaunchDay()));
        Map<Integer, StatScheduleDto> statScheduleDtoMap = statScheduleDtos.stream().collect(Collectors.toMap(StatScheduleDto::getScheduleId, Function.identity(), (key1, key2) -> key2));

        List<Slot> slots = slotService.getSlotList(gdFlowAllocationPos.stream().map(GdFlowAllocationPo::getSource).collect(Collectors.toSet()));
        Map<Integer, String> slotNameMap = slots.stream().collect(Collectors.toMap(Slot::getSlotId, Slot::getSlotName, (key1, key2) -> key2));

        Map<Integer, String> orderNameMap = gdOrderService.getOrderId2NameMap(scheduleDtos.stream().map(ScheduleDto::getOrderId).collect(Collectors.toList()));

        GdScheduleTargetRatioPoExample gdScheduleTargetRatioPoExample = new GdScheduleTargetRatioPoExample();
        gdScheduleTargetRatioPoExample.or().andScheduleIdIn(scheduleIds);
        Map<Integer, List<GdScheduleTargetRatioPo>> gdScheduleTargetRatioMap = gdScheduleTargetRatioDao.selectByExample(gdScheduleTargetRatioPoExample)
                .stream().collect(Collectors.groupingBy(GdScheduleTargetRatioPo::getScheduleId, Collectors.mapping(Function.identity(), Collectors.toList())));
        List<GdScheduleFlowDto> gdScheduleFlowDtos = scheduleDtos.stream().map(scheduleDto -> {
            Long showCount = 0L;
            Integer clickCount = 0;
            BigDecimal clickRate = new BigDecimal(0);
            if (statScheduleDtoMap.containsKey(scheduleDto.getScheduleId())) {
                showCount = statScheduleDtoMap.get(scheduleDto.getScheduleId()).getShowCount();
                clickCount = statScheduleDtoMap.get(scheduleDto.getScheduleId()).getClickCount();
                clickRate = statScheduleDtoMap.get(scheduleDto.getScheduleId()).getClickRate();
            }
            GdScheduleFlowDto gdScheduleFlowDto = new GdScheduleFlowDto();
            gdScheduleFlowDto.setAccountId(scheduleDto.getAccountId());
            gdScheduleFlowDto.setOrderName(orderNameMap.getOrDefault(scheduleDto.getOrderId(), ""));
            gdScheduleFlowDto.setOrderId(scheduleDto.getOrderId());
            gdScheduleFlowDto.setScheduleId(scheduleDto.getScheduleId());
            gdScheduleFlowDto.setScheduleName(scheduleDto.getName());
            gdScheduleFlowDto.setFrequencyLimit(scheduleDto.getFrequencyLimit());
            gdScheduleFlowDto.setTemplateName(scheduleDto.getTemplateName());
            gdScheduleFlowDto.setTemplateId(scheduleDto.getTemplateId());
            gdScheduleFlowDto.setDayImpression(gdFlowAllocationPoMap.getOrDefault(scheduleDto.getScheduleId(), new GdFlowAllocationPo()).getCpms());
            gdScheduleFlowDto.setShowAccount(Integer.parseInt(String.valueOf(showCount)));
            gdScheduleFlowDto.setClickCount(clickCount);
            gdScheduleFlowDto.setClickRate(clickRate);
            gdScheduleFlowDto.setSource(gdFlowAllocationPoMap.getOrDefault(scheduleDto.getScheduleId(), new GdFlowAllocationPo()).getSource());
            gdScheduleFlowDto.setSourceName(slotNameMap.getOrDefault(gdFlowAllocationPoMap.getOrDefault(scheduleDto.getScheduleId(), new GdFlowAllocationPo()).getSource(), ""));
            gdScheduleFlowDto.setTargetRatios(gdScheduleTargetRatioMap.getOrDefault(scheduleDto.getScheduleId(), new ArrayList<>())
                    .stream().map(gdScheduleTargetRatioPo -> TargetRatioDto.builder()
                            .targetKeyId(gdScheduleTargetRatioPo.getTargetKeyId())
                            .ratio(gdScheduleTargetRatioPo.getRatio()).build()).collect(Collectors.toList()));
            gdScheduleFlowDto.setCompleteRatio(new BigDecimal(showCount)
                    .multiply(new BigDecimal(100))
                    .divide(new BigDecimal(gdFlowAllocationPoMap.get(scheduleDto.getScheduleId()).getCpms() * 1000),
                            1, BigDecimal.ROUND_HALF_UP));

            return gdScheduleFlowDto;
        }).collect(Collectors.toList());
        return gdScheduleFlowDtos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doScheduleSpeedUp(Integer scheduleId, Integer ratio, Timestamp launchDay) {
        Assert.notNull(scheduleId, "scheduleId can not be null");
        Assert.notNull(ratio, "ratio can not be null");
        Assert.notNull(launchDay, "launchDay can not be null");
        GdScheduleTargetRatioPoExample gdScheduleTargetRatioPoExample = new GdScheduleTargetRatioPoExample();
        gdScheduleTargetRatioPoExample.or().andScheduleIdEqualTo(scheduleId).andLaunchDayEqualTo(launchDay);

        GdScheduleTargetRatioPo update = new GdScheduleTargetRatioPo();
        update.setRatio(ratio);
        gdScheduleTargetRatioDao.updateByExampleSelective(update, gdScheduleTargetRatioPoExample);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doTargetRatioSpeedUp(Integer scheduleId, Integer targetKeyId, Integer ratio, Timestamp launchDay) {
        Assert.notNull(scheduleId, "scheduleId can not be null");
        Assert.notNull(ratio, "ratio can not be null");
        Assert.notNull(launchDay, "launchDay can not be null");
        Assert.notNull(targetKeyId, "targetKeyId can not be null");
        GdScheduleTargetRatioPoExample gdScheduleTargetRatioPoExample = new GdScheduleTargetRatioPoExample();
        gdScheduleTargetRatioPoExample.or().andTargetKeyIdEqualTo(targetKeyId).andScheduleIdEqualTo(scheduleId).andLaunchDayEqualTo(launchDay);

        GdScheduleTargetRatioPo update = new GdScheduleTargetRatioPo();
        update.setRatio(ratio);
        gdScheduleTargetRatioDao.updateByExampleSelective(update, gdScheduleTargetRatioPoExample);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void doTransfer(Integer scheduleId, Integer sourceId) {
        Assert.notNull(scheduleId, "scheduleId can not be null");
        Assert.notNull(sourceId, "sourceId can not be null");
        GdFlowAllocationPoExample gdFlowAllocationExample = new GdFlowAllocationPoExample();
        gdFlowAllocationExample.or().andScheduleIdEqualTo(scheduleId);
        GdFlowAllocationPo gdFlowAllocationPo = new GdFlowAllocationPo();
        gdFlowAllocationPo.setSource(sourceId);
        gdFlowAllocationDao.updateByExampleSelective(gdFlowAllocationPo, gdFlowAllocationExample);

        GdScheduleTargetRatioPoExample gdScheduleTargetRatioPoExample = new GdScheduleTargetRatioPoExample();
        gdScheduleTargetRatioPoExample.or().andScheduleIdEqualTo(scheduleId);

        GdScheduleTargetRatioPo update = new GdScheduleTargetRatioPo();
        update.setSource(sourceId);
        gdScheduleTargetRatioDao.updateByExampleSelective(update, gdScheduleTargetRatioPoExample);
    }
}
