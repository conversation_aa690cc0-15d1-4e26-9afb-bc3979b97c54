package com.bilibili.brand.biz.resource.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.bilibili.brand.biz.resource.po.SlotGroupPo;
import com.bilibili.brand.biz.resource.query.SlotGroupPoQuery;

/**
 * Created by z<PERSON><PERSON> on 2016/9/30.
 *
 * <AUTHOR>
 */
public interface SlotGroupDao {

    SlotGroupPo load(
            @Param("id") Integer id
    );

    List<SlotGroupPo> query(
            @Param("query") SlotGroupPoQuery query
    );

	int insert(
			@Param("entity") SlotGroupPo slotGroupPo
	);
	
	int updateStatus(
			@Param("slotGroupId") Integer slotGroupId,
			@Param("status") Integer status
	);

	int updateSlotIdsOfSlotGroup(
			@Param("slotGroupId") Integer slotGroupId, 
			@Param("slotIds") String slotIds
	);
	
	int updateTemplateIdsOfSlotGroup(
			@Param("slotGroupId") Integer slotGroupId, 
			@Param("templateIds") String templateIds
	);

	List<SlotGroupPo> getSlotGroupList();
	
	List<SlotGroupPo> getSlotGroupListBySlotGroupIds(@Param("slotGroupIds") List<Integer> slotGroupIds);
	
	int update(@Param("entity") SlotGroupPo slotGroupPo);
}
