package com.bilibili.brand.biz.log.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.biz.log.dao.GdLogOperationDao;
import com.bilibili.brand.biz.log.po.GdLogOperationPo;
import com.bilibili.brand.biz.log.po.GdLogOperationPoExample;
import com.bilibili.cpt.platform.api.log.dto.CptLogOperationDto;
import com.bilibili.cpt.platform.biz.component.ClasspathPackageScanner;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.brand.annotation.LogFlag;
import com.bilibili.cpt.platform.common.LogOperateType;
import com.bilibili.brand.annotation.LogProperty;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2019/4/24.
 */
@Service
public class GdLogService implements IGdLogService {
    private final static Logger LOGGER = LoggerFactory.getLogger(GdLogService.class);
    private final static String LOG_BEAN_PATH = "com.bilibili.brand.biz.log.bean";
    private final Map<GdLogFlag, Class<?>> logFlag2ClassMap = Maps.newHashMap();
    @Autowired
    private GdLogOperationDao gdLogOperationDao;

    @PostConstruct
    public void init() {
        ClasspathPackageScanner scan = new ClasspathPackageScanner(LOG_BEAN_PATH);
        try {
            List<String> nameList = scan.getFullyQualifiedClassNameList();
            for (String name : nameList) {
                if (name.endsWith("LogBeanBuilder")) {
                    continue;
                }
                Class<?> clazz = Class.forName(name);
                if (clazz.isAnnotationPresent(LogFlag.class)) {
                    LogFlag logFlag = clazz.getAnnotation(LogFlag.class);
                    logFlag2ClassMap.put(logFlag.gdLogFlag(), clazz);
                }
            }
        } catch (ClassNotFoundException | IOException e) {
            LOGGER.error("获取LogBean失败", e);
        }
    }

    @Override
    public void insertLog(Long objId, GdLogFlag gdLogFlag, LogOperateType operationType, Operator operator, Object value) {
        Assert.notNull(objId, "操作对象ID不可为空");
        Assert.notNull(gdLogFlag, "CPT日志Flag不可为空");
        Assert.notNull(operationType, "操作类型不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(value, "操作内容不可为空");
        try {
            GdLogOperationPo po = new GdLogOperationPo();
            po.setObjId(objId);
            po.setObjFlag(gdLogFlag.getCode());
            po.setOperateType(operationType.getCode());
            po.setOperatorUsername(StringUtils.isEmpty(operator.getBilibiliUserName()) ? operator.getOperatorName() : operator.getBilibiliUserName());
            //IP存在多个情况时会被逗号和空格分割。比如xx.xx.xx.xx, yy.yy.yy.yy
            po.setIp(StringUtils.isEmpty(operator.getIp()) ? "" : operator.getIp().split(",")[0]);
            po.setValue(BeanUtils.isSimpleValueType(value.getClass()) ? value.toString() : JSON.toJSONString(value));

            gdLogOperationDao.insertSelective(po);
        } catch (Exception e) {
            LOGGER.error("insertLog.error", e);
        }
    }

    @Override
    public void insertLog(Integer objId, GdLogFlag gdLogFlag, LogOperateType operationType, Operator operator, Object value) {
        this.insertLog(Long.valueOf(objId.toString()), gdLogFlag, operationType, operator, value);
    }

    @Override
    public PageResult<CptLogOperationDto> getLogsByObjId(String objId, GdLogFlag logFlag,
                                                         LogOperateType logOperateType,
                                                         Integer page, Integer size) throws ServiceException {
        return doQuery(Lists.newArrayList(Long.valueOf(objId)), logFlag,
                Objects.nonNull(logOperateType) ? Lists.newArrayList(logOperateType) : null,
                page, size, null, null, false);
    }

    @Override
    public PageResult<CptLogOperationDto> getLogsByObjId(List<Long> objIdList, GdLogFlag logFlag,
                                                         List<LogOperateType> logOperateTypes, Integer page,
                                                         Integer size, boolean isReturnObj) throws ServiceException {
        return doQuery(objIdList, logFlag, logOperateTypes, page, size, null, null, isReturnObj);
    }

    @Override
    public PageResult<CptLogOperationDto> getLogsByObjId(List<Long> objIdList,
                                                         GdLogFlag logFlag,
                                                         List<LogOperateType> logOperateTypes,
                                                         Integer page,
                                                         Integer size,
                                                         Timestamp fromTime,
                                                         Timestamp toTime,
                                                         boolean isReturnObj) throws ServiceException {
        return doQuery(objIdList, logFlag, logOperateTypes, page, size, fromTime, toTime, isReturnObj);
    }


    private PageResult<CptLogOperationDto> doQuery(List<Long> objIdList, GdLogFlag logFlag,
                                                   List<LogOperateType> logOperateTypes,
                                                   Integer page, Integer size, Timestamp fromTime, Timestamp toTime,
                                                   boolean isReturnObj){
        Assert.notNull(page,"page required");
        Assert.notNull(size,"size required");
        Assert.notNull(logFlag,"logFlag required");
        Page pageBean = Page.valueOf(page, size);

        GdLogOperationPoExample example = new GdLogOperationPoExample();
        GdLogOperationPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andObjFlagEqualTo(logFlag.getCode());
        if (!CollectionUtils.isEmpty(logOperateTypes)) {
            criteria.andOperateTypeIn(logOperateTypes.stream().map(LogOperateType::getCode).collect(Collectors.toList()));
        }
        if (!CollectionUtils.isEmpty(objIdList)) {
            criteria.andObjIdIn(objIdList);
        }
        if (Objects.nonNull(fromTime)) {
            criteria.andMtimeGreaterThanOrEqualTo(fromTime);
        }
        if (Objects.nonNull(toTime)) {
            criteria.andMtimeLessThanOrEqualTo(toTime);
        }
        example.setOrderByClause("ctime desc");
        long total = gdLogOperationDao.countByExample(example);
        if (total == 0) {
            return PageResult.<CptLogOperationDto>builder().records(Collections.emptyList()).total(0).build();
        }
        example.setOffset(pageBean.getOffset());
        example.setLimit(pageBean.getLimit());
        List<GdLogOperationPo> pos = gdLogOperationDao.selectByExampleWithBLOBs(example);
        return PageResult.<CptLogOperationDto>builder().records(this.cptLogOperationPos2Dtos(pos, isReturnObj)).total((int) total).build();
    }

    private List<CptLogOperationDto> cptLogOperationPos2Dtos(List<GdLogOperationPo> pos, boolean isReturnObj) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        } else {
            List<CptLogOperationDto> dtos = Lists.newArrayListWithCapacity(pos.size());
            try {
                for (GdLogOperationPo po : pos) {
                    dtos.add(CptLogOperationDto.builder()
                            .id(po.getId())
                            .objId(po.getObjId().toString())
                            .objFlag(GdLogFlag.getByCode(po.getObjFlag()).getDesc())
                            .operateType(LogOperateType.getByCode(po.getOperateType()).getDesc())
                            .operatorUsername(po.getOperatorUsername())
                            .ctime(TimeUtils.getTimestamp2String(po.getCtime()))
                            .value(getLogPropertyValue(GdLogFlag.getByCode(po.getObjFlag()), po.getValue()))
                            .valueObj(isReturnObj ? getValueObj(GdLogFlag.getByCode(po.getObjFlag()), po.getValue()) : null)
                            .build());
                }
            } catch (Exception e) {
                LOGGER.error("日志解析失败", e);
            }
            return dtos;
        }
    }

    private Object getValueObj(GdLogFlag logFlag, String value) {
        Class<?> clazz = logFlag2ClassMap.get(logFlag);
        if (clazz == null) {
            //如果没有反序列化的目标则返回原生日志内容
            return null;
        }
        return JSON.parseObject(value, clazz);
    }

    private String getLogPropertyValue(GdLogFlag logFlag, String value) throws IllegalAccessException, NoSuchMethodException, InvocationTargetException, InstantiationException {
        Class<?> clazz = logFlag2ClassMap.get(logFlag);
        if (clazz == null) {
            //如果没有反序列化的目标则返回原生日志内容
            return value;
        }
        return getObjectValue(JSON.parseObject(value, clazz));
    }

    private String getObjectValue(Object obj) throws InvocationTargetException, IllegalAccessException, NoSuchMethodException {
        if (obj == null) {
            return "";
        } else {
            Class clazz = obj.getClass();
            Field[] fields = clazz.getDeclaredFields();
            StringBuilder sb = new StringBuilder();
            for (Field field : fields) {
                if (field.isAnnotationPresent(LogProperty.class)) {
                    LogProperty logProperty = field.getAnnotation(LogProperty.class);
                    Method m = clazz.getMethod("get" + getMethodName(field.getName()));
                    Object vo = m.invoke(obj);
                    if (vo != null) {
                        sb.append(logProperty.value() + ": " + vo + "<br/>");
                    }
                }
            }
            return sb.toString();
        }
    }

    private String getMethodName(String fildeName) {
        byte[] items = fildeName.getBytes();
        items[0] = (byte) ((char) items[0] - 'a' + 'A');
        return new String(items);
    }
}
