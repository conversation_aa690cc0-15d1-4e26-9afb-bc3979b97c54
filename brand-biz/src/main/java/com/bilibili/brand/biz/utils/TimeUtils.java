package com.bilibili.brand.biz.utils;

import java.time.LocalTime;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/6
 **/
public class TimeUtils {

    public static long timeStringToSeconds(String timeString) {
        String[] timeParts = timeString.split(":");
        int hours = Integer.parseInt(timeParts[0]);
        int minutes = Integer.parseInt(timeParts[1]);
        int seconds = Integer.parseInt(timeParts[2]);
        return hours * 3600L + minutes * 60L + seconds;
    }

    public static String secondsToTimeString(long totalSeconds) {
        int hours = (int) (totalSeconds / 3600);
        int minutes = (int) ((totalSeconds % 3600) / 60);
        int seconds = (int) (totalSeconds % 60);
        LocalTime time = LocalTime.of(hours, minutes, seconds);
        return time.toString();
    }

    public static void main(String[] args) {
        String timeString = "60:04:00";
        long totalSeconds = timeStringToSeconds(timeString);
        System.out.println("Total seconds: " + totalSeconds);

        String convertedTime = secondsToTimeString(totalSeconds);
        System.out.println("Converted time: " + convertedTime);
    }
}
