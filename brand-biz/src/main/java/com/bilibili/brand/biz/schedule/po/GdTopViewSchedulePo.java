package com.bilibili.brand.biz.schedule.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdTopViewSchedulePo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 排期id
     */
    private Integer gdScheduleId;

    /**
     * 推广目的 2-落地页 4-应用下载
     */
    private Integer promotionPurposeType;

    /**
     * TopView售卖方式  0-常规TopView 1-首刷TopView
     */
    private Integer sellingType;

    /**
     * 闪屏资源类型 1-商业资源 2-内容资源
     */
    private Integer ssaResType;

    /**
     * 闪屏媒体类型 1-视频
     */
    private Integer ssaAdType;

    /**
     * 闪屏全半屏样式 1-全屏 2-半屏
     */
    private Integer ssaScreenStyle;

    /**
     * 闪屏展示样式 3-横屏视频 4-竖屏视频
     */
    private Integer ssaShowStyle;

    /**
     * 1-视频 2-图文
     */
    private Integer hfAdType;

    /**
     * andoird首焦资源位id
     */
    private Integer androidHfSourceId;

    /**
     * ios首焦资源位id
     */
    private Integer iosHfSourceId;

    /**
     * android App包ID
     */
    private Integer androidAppPackageId;

    /**
     * ios App包ID
     */
    private Integer iosAppPackageId;

    /**
     * 删除标识 0-未删除 1-已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    private Integer clickArea;

    /**
     * 新版16:9首焦ios资源位id
     */
    private Integer newIosHfSourceId;

    /**
     * 新版16:9首焦安卓资源位id
     */
    private Integer newAndroidHfSourceId;

    /**
     * topView视频类型，0：普通视频，1:3D出框视频
     */
    private Integer topViewVideoType;

    /**
     * 过渡形式：0-普通过渡 1-自定义过渡
     */
    private Integer transitionMode;

    private static final long serialVersionUID = 1L;
}