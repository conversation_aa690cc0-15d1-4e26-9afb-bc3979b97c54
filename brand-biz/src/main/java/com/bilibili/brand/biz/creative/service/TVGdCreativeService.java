package com.bilibili.brand.biz.creative.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.creative.po.GdCreativePoExample;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePoExample;
import com.bilibili.cpt.report.platform.api.creative.dto.VideoPlayStatCreativeDto;
import com.bilibili.cpt.report.platform.api.creative.service.IVideoPlayStatCreativeService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by walker on 16/9/1.
 */
@Slf4j
@Service
public class TVGdCreativeService {

    @Autowired
    private GdScheduleDao gdScheduleDao;

    @Autowired
    private GdCreativeDao gdCreativeDao;

    @Autowired
    private IVideoPlayStatCreativeService videoPlayStatCreativeService;

    public void refreshTvPlayCreativeStatus() {
        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.or().andStatusIn(Lists.newArrayList(1, 2))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBeginDateGreaterThanOrEqualTo(Utils.getBeginOfDay(TimeUtil.localDateToTimestamp(LocalDate.now())))
                .andSalesTypeEqualTo(SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode())
                .andSupportPlayLaunchEqualTo(IsValid.TRUE.getCode());
        List<GdSchedulePo> schedulePos = gdScheduleDao.selectByExample(schedulePoExample);
        log.info("refreshTvPlayCreativeStatus need deal schedule size is:[{}], schedule ids[{}]",
                schedulePos.size(), schedulePos.stream().map(GdSchedulePo::getScheduleId).collect(Collectors.toList()));
        if(CollectionUtils.isEmpty(schedulePos)){
            return;
        }

        List<Integer> scheduleIds = schedulePos.stream().map(GdSchedulePo::getScheduleId).collect(Collectors.toList());
        GdCreativePoExample creativePoExample = new GdCreativePoExample();
        creativePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusIn(Lists.newArrayList(1, 2))
                .andSalesTypeEqualTo(SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode())
                .andScheduleIdIn(scheduleIds);
        List<GdCreativePo> creativePos = gdCreativeDao.selectByExample(creativePoExample);
        List<Long> creativeIds = creativePos.stream()
                .map(GdCreativePo::getCreativeId).collect(Collectors.toList());
        log.info("refreshTvPlayCreativeStatus need deal creativePos size is:[{}], creative ids[{}]",
                creativePos.size(), creativeIds);
        if(CollectionUtils.isEmpty(creativePos)){
            return;
        }

        Map<Integer, Integer> schedule2playCount = schedulePos.stream()
                .collect(Collectors.toMap(GdSchedulePo::getScheduleId, GdSchedulePo::getPlayCount));

        Map<Long, Integer> creative2schedule = creativePos.stream()
                .collect(Collectors.toMap(GdCreativePo::getCreativeId, GdCreativePo::getScheduleId));

        Map<Long, GdCreativePo> creative2info = creativePos.stream()
                .collect(Collectors.toMap(GdCreativePo::getCreativeId, t->t));

        //按播放数售卖的tv创意，在播放数达到预约值时需要完结创意
        Map<Long, VideoPlayStatCreativeDto> creativeStats = videoPlayStatCreativeService.getCreativeStat(creativeIds);
        creativeStats.forEach((k, v)->{
            log.info("refreshTvPlayCreativeStatus VideoPlayStatCreativeDto={}", JSON.toJSONString(v));
            Integer scheduleId = creative2schedule.get(k);
            Integer playCount = schedule2playCount.get(scheduleId) * 1000;
            if(v.getPlayCount() >= playCount){
                GdCreativePo gdCreativePo = creative2info.get(k);
                gdCreativePo.setStatus(4);
                gdCreativeDao.updateByPrimaryKeySelective(gdCreativePo);
            }
        });

    }

}
