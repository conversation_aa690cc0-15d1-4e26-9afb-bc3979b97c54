package com.bilibili.brand.biz.config;


import java.io.IOException;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
@ComponentScan
public class RedissonConfig {
    @Value("${redis.cluster.nodes}")
    private String redisNodes;

    @Value("${redis.timeout}")
    private Integer timeout;
    @Value("${redis.maxIdle}")
    private Integer redisMaxIdle;

    @Value("${redis.minIdle}")
    private Integer redisMinIdle;

    @Bean(destroyMethod = "shutdown")
    RedissonClient redisson() throws IOException {
        Config config = new Config();
        config.useClusterServers()
                .setMasterConnectionPoolSize(redisMaxIdle)
                .setMasterConnectionMinimumIdleSize(redisMinIdle)
                .setConnectTimeout(timeout)
                .addNodeAddress(redisNodes.split(","));
        return Redisson.create(config);
    }
}
