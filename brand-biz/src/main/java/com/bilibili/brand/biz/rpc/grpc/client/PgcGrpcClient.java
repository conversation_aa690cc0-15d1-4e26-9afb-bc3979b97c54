package com.bilibili.brand.biz.rpc.grpc.client;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.bapis.pgc.servant.delivery.CheckResourcePlacementAllowListReply;
import com.bapis.pgc.servant.delivery.CheckResourcePlacementAllowListReq;
import com.bapis.pgc.servant.delivery.DeliveryGrpc;
import com.bapis.pgc.servant.delivery.Record;
import com.bapis.pgc.servant.season.season.*;
import com.bapis.pgc.service.season.episode.AidInfosReply;
import com.bapis.pgc.service.season.episode.EpAidReq;
import com.bapis.pgc.service.season.episode.EpisodeGrpc;
import com.bapis.pgc.service.season.episode.EpisodeInfoProto;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.resource.ogv.OgvEpisodeDto;
import com.bilibili.brand.api.resource.ogv.OgvSeasonDto;
import com.bilibili.brand.biz.rpc.converter.PgcConverter;
import com.bilibili.brand.biz.rpc.dto.PgcInfoBo;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.utils.BatchUtil;
import com.google.common.collect.Maps;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/5/30
 */
@Slf4j
@Component
public class PgcGrpcClient {
    @RPCClient("season.service")
    private EpisodeGrpc.EpisodeBlockingStub episodeBlockingStub;
    @RPCClient("ogv.season.servant")
    private SeasonGrpc.SeasonBlockingStub seasonBlockingStub;

    @RPCClient("ogv.operation.servant")
    private DeliveryGrpc.DeliveryBlockingStub deliveryBlockingStub;

    public PgcInfoBo queryPgcByAid(long aid) {
        try {
            log.info("查询pgc信息，请求为：aid={}", aid);
            AidInfosReply aidInfosReply = episodeBlockingStub.listByAids2(EpAidReq.newBuilder()
                    .addAid2S(aid)
                    .build());

            EpisodeInfoProto episodeInfoProto = aidInfosReply.getInfosMap().get(aid);
            log.info("查询pgc信息，返回为：episodeInfoProto={}", GsonUtils.toJson(episodeInfoProto));
            return PgcConverter.MAPPER.toBo(episodeInfoProto);
        } catch (Exception e) {
            log.error("查询pgc信息时发生异常,e:{}", ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询指定season的信息
     * 1、50一批次
     * 2、不包含集信息
     *
     * @param seasonIdList
     * @param appendEpisode true:返回episode，false:不返回episode
     */
    public Map<Long, OgvSeasonDto> queryOgvSeasons(List<Long> seasonIdList, boolean appendEpisode) {
        List<OgvSeasonDto> seasonDtoList = BatchUtil.batch(seasonIdList, subList -> {
            try {
                log.info("[PgcGrpcClient] queryOgvSeasons request, seasonIdList={}", JSONObject.toJSONString(subList));
                BatchSeasonDetailsReply seasonDetailsReply = seasonBlockingStub
                        .withDeadlineAfter(10, TimeUnit.SECONDS)
                        .getBatchSeasonDetails(
                                BatchSeasonDetailsReq.newBuilder()
                                        .addAllSeasonId(subList)
                                        .build());
                List<SeasonDetailReply> seasonsList = seasonDetailsReply.getSeasonsList();
                List<OgvSeasonDto> seasons = seasonsList.stream().map(PgcConverter.MAPPER::toOgvSeasonDto).collect(Collectors.toList());
                String res = seasons.stream()
                        .map(season -> String.format("%d:%d", season.getSeasonId(), season.getStatus()))
                        .collect(Collectors.joining(","));
                log.info("[PgcGrpcClient] queryOgvSeasons response, seasons={}", res);
                return seasons;
            } catch (Exception e) {
                log.error("[PgcGrpcClient] queryOgvSeasons error", e);
                throw new RuntimeException(e);
            }
        }, 50);
        return seasonDtoList.stream()
                .peek(season -> season.setEpisodes(appendEpisode ? queryOgvEpisodesBySeason(season.getSeasonId()) : Collections.emptyList()))
                .collect(Collectors.toMap(OgvSeasonDto::getSeasonId, Function.identity(), (s, t) -> t));
    }


    /**
     * 查询指定episode的信息
     * 1、50一批次
     */
    public Map<Long, OgvEpisodeDto> queryOgvEpisodesByEpisode(List<Long> episodeIdList) {
        List<OgvEpisodeDto> episodeDtoList = BatchUtil.batch(episodeIdList, subList -> {
            try {
                log.info("[PgcGrpcClient] queryOgvEpisodes request, episodeIdList={}", JSONObject.toJSONString(subList));
                EpisodeDetailsReply episodeDetailsReply = seasonBlockingStub
                        .withDeadlineAfter(10, TimeUnit.SECONDS)
                        .getEpisodeDetails(
                                EpisodeDetailsReq.newBuilder()
                                        .addAllEpisodeIds(subList.stream().map(Long::intValue).collect(Collectors.toList()))
                                        .build());
                List<Episode> episodesList = episodeDetailsReply.getEpisodesList();
                List<OgvEpisodeDto> episodes = episodesList.stream().map(PgcConverter.MAPPER::toOgvEpisodeDto).collect(Collectors.toList());
                String res = episodes.stream()
                        .map(episode -> String.format("%d:%d", episode.getEpisodeId(), episode.getStatus()))
                        .collect(Collectors.joining(","));
                log.info("[PgcGrpcClient] queryOgvEpisodes response, episodes={}", res);
                return episodes;
            } catch (Exception e) {
                log.error("[PgcGrpcClient] queryOgvEpisodes error", e);
                throw new RuntimeException(e);
            }
        }, 50);
        return episodeDtoList.stream()
                .collect(Collectors.toMap(OgvEpisodeDto::getEpisodeId, Function.identity(), (s, t) -> t));
    }


    /**
     * 查询指定season的仅仅是正片的集信息
     */
    public List<OgvEpisodeDto> queryOgvEpisodesBySeason(Long seasonId) {
        if (!Utils.isPositive(seasonId)) {
            return Collections.emptyList();
        }
        try {
            log.info("[PgcGrpcClient] queryOgvEpisodesBySeason request, seasonId={}", seasonId);
            SeasonDetailReply seasonDetailReply = seasonBlockingStub
                    .withDeadlineAfter(10, TimeUnit.SECONDS)
                    .getSeasonDetail(SeasonDetailReq.newBuilder()
                            .setSeasonId(seasonId)
                            .build());
            List<SectionDetail> seasonsList = seasonDetailReply.getSectionsList();
            List<OgvEpisodeDto> episodes = seasonsList.stream()
                    //商业仅关心正片
                    .filter(section -> section.getSectionType() == 0 && section.getEpisodesCount() > 0)
                    //section_type=0的只有1条，因此限制1条
                    .limit(1)
                    .map(SectionDetail::getEpisodesList)
                    .flatMap(Collection::stream)
                    .map(PgcConverter.MAPPER::toOgvEpisodeDto)
                    .collect(Collectors.toList());
            String res = episodes.stream()
                    .map(episode -> String.format("%d:%d", episode.getEpisodeId(), episode.getStatus()))
                    .collect(Collectors.joining(","));
            log.info("[PgcGrpcClient] queryOgvEpisodesBySeason response, episodes={}", res);
            return episodes;
        } catch (StatusRuntimeException e) {
            log.error("[PgcGrpcClient] queryOgvEpisodesBySeason StatusRuntimeException error", e);
            if ("-404".equals(e.getStatus().getDescription())) {
                throw new IllegalArgumentException("season 不存在");
            }
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("[PgcGrpcClient] queryOgvEpisodesBySeason error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询Season是否可投放
     */
    public Map<Long, Boolean> querySeasonAllows(List<Long> seasonIdList) {
        return doQueryAllows(seasonIdList, "season");
    }

    /**
     * 查询Episode是否可投放
     */
    public Map<Long, Boolean> queryEpisodeAllows(List<Long> episodeIdList) {
        return doQueryAllows(episodeIdList, "episode");
    }

    public Map<Long, Boolean> doQueryAllows(List<Long> idList, String type) {
        if (CollectionUtils.isEmpty(idList)) return Maps.newHashMap();

        //mock
//        boolean mock = true;
//        if (mock) {
//            return idList.stream().collect(Collectors.toMap(Function.identity(), id -> true));
//        }

        try {
            log.info("[PgcGrpcClient] doQueryAllows request, type={},idList={}", type, JSONObject.toJSONString(idList));
            List<Record> replies = BatchUtil.batch(idList, subIdList -> {
                CheckResourcePlacementAllowListReply allowListReply = deliveryBlockingStub
                        .withDeadlineAfter(10, TimeUnit.SECONDS)
                        .checkResourcePlacementAllowList(
                                CheckResourcePlacementAllowListReq.newBuilder()
                                        .addAllRecord(subIdList.stream().map(id -> Record.newBuilder()
                                                .setId(id.toString())
                                                .setType(type)
                                                .build()).collect(Collectors.toList()))
                                        .setBusiness("cm")
                                        .setResourcePlacementCode("player_fragment")
                                        .setDisableCache(true)
                                        .build());
                return allowListReply.getRecordList();
            }, 50);
            Map<Long, Boolean> res = replies.stream()
                    .collect(Collectors.toMap(r -> Long.parseLong(r.getId()), r -> "allow".equals(r.getResult()), (s, t) -> t));
            log.info("[PgcGrpcClient] doQueryAllows response, type={},res={}", type, JSON.toJSONString(res));
            return res;
        } catch (Exception e) {
            log.error("[PgcGrpcClient] doQueryAllows, error", e);
            throw new RuntimeException(e);
        }
    }
}
