package com.bilibili.brand.biz.schedule.dao;

import com.bilibili.brand.biz.schedule.po.GdScheduleControlPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleControlPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface GdScheduleControlDao {
    long countByExample(GdScheduleControlPoExample example);

    int deleteByExample(GdScheduleControlPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(GdScheduleControlPo record);

    int insertBatch(List<GdScheduleControlPo> records);

    int insertUpdateBatch(List<GdScheduleControlPo> records);

    int insert(GdScheduleControlPo record);

    int insertUpdateSelective(GdScheduleControlPo record);

    int insertSelective(GdScheduleControlPo record);

    List<GdScheduleControlPo> selectByExample(GdScheduleControlPoExample example);

    GdScheduleControlPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") GdScheduleControlPo record, @Param("example") GdScheduleControlPoExample example);

    int updateByExample(@Param("record") GdScheduleControlPo record, @Param("example") GdScheduleControlPoExample example);

    int updateByPrimaryKeySelective(GdScheduleControlPo record);

    int updateByPrimaryKey(GdScheduleControlPo record);
}