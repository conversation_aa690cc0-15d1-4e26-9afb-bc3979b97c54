package com.bilibili.brand.biz.inventory.impl.location;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.target_lau.IResTargetItemService;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.inventory.ILocationStockService;
import com.bilibili.brand.biz.inventory.IStockService;
import com.bilibili.brand.biz.inventory.ITargetCleaner;
import com.bilibili.brand.biz.inventory.bo.stock.*;
import com.bilibili.brand.biz.schedule.service.frequency.ResourceService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.biz.utils.ExampleUtils;
import com.bilibili.cpt.platform.common.GdType;
import com.bilibili.cpt.platform.common.LocationType;
import com.bilibili.enums.GdDisplayModeEnum;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.bilibili.utils.FuncUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/7 21:18
 */
@Slf4j
public abstract class AbstractLocationStockService implements ILocationStockService {

    @Autowired
    private IStockService stockService;
    @Autowired
    protected ISystemConfigService systemConfigService;
    @Autowired
    protected ConfigCenter configCenter;
    @Autowired
    protected IResTargetItemService resTargetItemService;
    @Autowired
    private List<ITargetCleaner> targetCleaners;
    @Autowired
    protected ResourceService resourceService;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    private final List<TargetType> SUPPORTS_TARGET_TYPES = Lists.newArrayList(
            TargetType.OS, TargetType.GENDER, TargetType.AGE, TargetType.AREA, TargetType.DISPLAY_MODE);

    /**
     * mock查询库存，当下面的代码返回true时，会调用该方法
     * <pre>
     *     {@code context.isSupportsMock() && isSupportsMockStock(orderId)}
     * </pre>
     * 有更复杂的mock场景，可以自定义实现类覆盖该方法
     * <p>
     * 注意：命中mock的情况下，不会走前处理和后处理方法{@link #decorateQueryStockRequest(StockContext, QueryStockRequest)}
     * 和 {@link #decorateQueryStockResponse(StockContext, QueryStockRequest, QueryStockResponse)}
     *
     * @see #queryStock(StockContext, QueryStockRequest)
     */
    protected List<QueryTaskStock> mockQueryTaskStock(StockContext context, List<QueryTask> tasks) {
        return tasks.stream()
                .map(task -> {
                    Long totalCpm = getMockStock(task.getOrderId());
                    List<Integer> platforms = task.getTarget().getPlatforms();
                    Long cpmPerPlatform = totalCpm / platforms.size();
                    return QueryTaskStock.builder()
                            .taskId(task.getTaskId())
                            .totalCpm(totalCpm)
                            .platformStocks(platforms.stream()
                                    .map(platform -> PlatformStock.builder()
                                            .platform(platform)
                                            .cpm(cpmPerPlatform)
                                            .build())
                                    .collect(Collectors.toList()))
                            .build();
                }).collect(Collectors.toList());
    }

    /**
     * 在询量之前的装饰操作，给实现类提供一次修改业务逻辑的机会
     *
     * @return 默认返回{@param request}，如果自定义了实现，切勿返回null
     */
    protected QueryStockRequest decorateQueryStockRequest(StockContext context, QueryStockRequest request) {

        return request;
    }

    /**
     * 在询量之后的装饰操作，给实现类提供一次修改业务逻辑的机会
     * <p>
     * 比如CPT场景，需要要将库存折算成轮数，此时实现类覆盖该方法，实现{@link QueryTaskStock#rotation}赋值
     *
     * @return 默认返回{@param response}，如果自定义了实现，切勿返回null
     */
    protected QueryStockResponse decorateQueryStockResponse(StockContext context, QueryStockRequest request,
                                                            QueryStockResponse response) {
        return response;
    }

    @Override
    public QueryStockResponse queryStock(StockContext context, QueryStockRequest request) throws Exception {
        appendRequestId(request);

        log.info("[AbstractLocationStockService] queryStock original_request={}, context={}", JSON.toJSONString(request), JSON.toJSONString(context));

        List<QueryTask> mockQueryTasks = Lists.newLinkedList();
        List<QueryTask> doQueryTasks = Lists.newArrayList();
        for (QueryTask task : request.getTasks()) {
            Assert.notNull(task.getLocation(), "location不能为空");
            if (context.isSupportsMock() && isSupportsMockStock(task.getOrderId())) {
                mockQueryTasks.add(task);
                continue;
            }
            processTime(task);
            processTarget(context, task);
            doQueryTasks.add(task);
        }

        log.info("[AbstractLocationStockService] queryStock processed_request={}, context={}", JSON.toJSONString(request), JSON.toJSONString(context));

        List<QueryTaskStock> taskStocks = Lists.newArrayListWithCapacity(request.getTasks().size());

        if (!mockQueryTasks.isEmpty()) {
            taskStocks.addAll(mockQueryTaskStock(context, mockQueryTasks));
        }

        if (!doQueryTasks.isEmpty()) {
            /*
             * 如果没有mock的task，则直接使用原生request，少创建一次对象
             */
            QueryStockRequest doQueryStockRequest = mockQueryTasks.isEmpty() ? request :
                    QueryStockRequest.builder()
                            .requestId(request.getRequestId())
                            .tasks(doQueryTasks)
                            .build();

            doQueryStockRequest = decorateQueryStockRequest(context, doQueryStockRequest);

            log.info("[AbstractLocationStockService] queryStock decorated_request={}, context={}", JSON.toJSONString(doQueryStockRequest), JSON.toJSONString(context));

            QueryStockResponse doQueryStockResponse = stockService.queryStock(doQueryStockRequest);
            doQueryStockResponse = decorateQueryStockResponse(context, doQueryStockRequest, doQueryStockResponse);

            log.info("[AbstractLocationStockService] queryStock decorated_response={}, context={}", JSON.toJSONString(doQueryStockResponse), JSON.toJSONString(context));

            taskStocks.addAll(doQueryStockResponse.getTaskStocks());
        }
        return QueryStockResponse.builder()
                .code(0)
                .requestId(request.getRequestId())
                .taskStocks(taskStocks)
                .build();
    }

    /**
     * 在锁量之前的装饰操作，给实现类提供一次修改业务逻辑的机会
     *
     * @return 默认返回{@param request}，如果自定义了实现，切勿返回null
     */
    protected AllocateStockRequest decorateAllocateStockRequest(StockContext context, AllocateStockRequest request) {
        return request;
    }

    /**
     * 在锁量之后的装饰操作，给实现类提供一次修改业务逻辑的机会
     *
     * @return 默认返回{@param response}，如果自定义了实现，切勿返回null
     */
    protected AllocateStockResponse decorateAllocateStockResponse(StockContext context, AllocateStockRequest request,
                                                                  AllocateStockResponse response) {
        return response;
    }

    @Override
    public AllocateStockResponse allocateStock(StockContext context, AllocateStockRequest request) throws Exception {
        appendRequestId(request);

        log.info("[AbstractLocationStockService] allocateStock original_request={}, context={}", JSON.toJSONString(request), JSON.toJSONString(context));

        for (AllocateTask task : request.getTasks()) {
            Assert.notNull(task.getLocation(), "location不能为空");
            processTime(task);
            processTarget(context, task);
        }
        log.info("[AbstractLocationStockService] allocateStock processed_request={}, context={}", JSON.toJSONString(request), JSON.toJSONString(context));

        AllocateStockRequest allocateStockRequest = decorateAllocateStockRequest(context, request);
        log.info("[AbstractLocationStockService] allocateStock decorated_request={}, context={}", JSON.toJSONString(allocateStockRequest), JSON.toJSONString(context));

        AllocateStockResponse allocateStockResponse = stockService.allocateStock(allocateStockRequest);
        allocateStockResponse = decorateAllocateStockResponse(context, allocateStockRequest, allocateStockResponse);
        log.info("[AbstractLocationStockService] allocateStock decorated_response={}, context={}", JSON.toJSONString(allocateStockResponse), JSON.toJSONString(context));

        return allocateStockResponse;
    }

    /**
     * 在释放库存之前的装饰操作，给实现类提供一次修改业务逻辑的机会
     *
     * @return 默认返回{@param request}，如果自定义了实现，切勿返回null
     */
    protected CancelStockRequest decorateCancelStockRequest(StockContext context, CancelStockRequest request) {
        return request;
    }

    /**
     * 在释放库存之后的装饰操作，给实现类提供一次修改业务逻辑的机会
     *
     * @return 默认返回{@param response}，如果自定义了实现，切勿返回null
     */
    protected CancelStockResponse decorateCancelStockResponse(StockContext context, CancelStockRequest request,
                                                              CancelStockResponse response) {
        return response;
    }

    @Override
    public CancelStockResponse cancelStock(StockContext context, CancelStockRequest request) throws Exception {
        appendRequestId(request);
        CancelStockRequest cancelStockRequest = decorateCancelStockRequest(context, request);
        CancelStockResponse cancelStockResponse = stockService.cancelStock(cancelStockRequest);
        return decorateCancelStockResponse(context, cancelStockRequest, cancelStockResponse);
    }

    /**
     * 是否支持mock库存
     */
    protected boolean isSupportsMockStock(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnListInt(SystemConfigEnum.INVENTORY_MOCK_ORDER_WHITE_LIST.getCode())
                        .contains(oId),
                () -> false);
    }

    /**
     * 查询mock的库存
     */
    protected Long getMockStock(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnLong(SystemConfigEnum.INVENTORY_MOCK_CPM.getCode()),
                () -> 0L);
    }

    /**
     * 查询mock的轮数
     */
    protected Integer getMockRotation(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> this.systemConfigService.getValueReturnInt(SystemConfigEnum.INVENTORY_MOCK_ROTATION.getCode()),
                () -> 0);
    }

    /**
     * 是否支持强制锁库存
     */
    protected boolean isForceUpdate(Integer orderId) {
        return FuncUtil.invokeWithoutEx(orderId,
                oId -> configCenter.getMetaDataConfig().getForceLockWhiteList().contains(oId),
                () -> false);
    }

    protected boolean isUvLogic(Integer promotion, Integer gdType) {
        if (Objects.equals(promotion, PromotionPurposeType.LIVE.getCode())) {
            //直播间都走pv逻辑
            return false;
        }
        return GdType.isFly(gdType);
    }

    /**
     * goblin返回的是pv，pv/uv近似为4，所以设定信息流最大可展示次数为4
     */
    protected long pvToUv(long inventory) {
        return inventory / this.configCenter.getGdPlusConfig().getGdMaxFrequency();
    }


    private void appendRequestId(GoblinStockRequest request) {
        if (!StringUtils.hasText(request.getRequestId())) {
            request.setRequestId(String.valueOf(snowflakeIdWorker.nextId()));
        }
    }

    private void processTarget(StockContext context, Task task) {
        //一定不能为空，至少会存在设备定向
        Assert.notEmpty(task.getTargetRules(), "定向信息不能为空");

        Target target = task.getTarget();
        if (Objects.isNull(target)) {
            task.setTarget(target = new Target());
        }

        List<TargetRule> targetRules = task.getTargetRules();
        Map<Integer, TargetRule> targetRuleMap = targetRules.stream()
                //如果存在相同定向的记录则直接报错
                .collect(Collectors.toMap(TargetRule::getRuleType, Function.identity()));

        Map<TargetType, List<Integer>> targetValueMap = Maps.newHashMap();
        for (TargetType targetType : SUPPORTS_TARGET_TYPES) {
            List<Integer> targets = targetCleaners.stream()
                    .filter(cleaner -> cleaner.supports(targetType))
                    .findFirst()
                    .map(cleaner -> cleaner.clean(context, task, targetRuleMap.get(targetType.getCode())))
                    .orElse(null);
            if (CollectionUtils.isNotEmpty(targets)) {
                targetValueMap.put(targetType, targets);
            }
        }

        ExampleUtils.notEmpty(targetValueMap.get(TargetType.OS), target::setPlatforms);
        ExampleUtils.notEmpty(targetValueMap.get(TargetType.GENDER), target::setGenders);
        ExampleUtils.notEmpty(targetValueMap.get(TargetType.AGE), target::setAges);
        ExampleUtils.notEmpty(targetValueMap.get(TargetType.AREA), target::setAreas);
        ExampleUtils.notEmpty(targetValueMap.get(TargetType.DISPLAY_MODE), target::setBrushTypes);

        LocationType location = task.getLocation();
        if (Objects.equals(location, LocationType.BIG_CARD)
                || Objects.equals(location, LocationType.STORY)
                || Objects.equals(location, LocationType.RECOMMEND)) {
            //大卡，story、相关推荐等点位，如果选择了首刷，此时首刷的含义是希望当天首次冷启的首刷，因此需要添加冷启动次数定向1
            if (CollectionUtils.isNotEmpty(target.getBrushTypes())
                    && target.getBrushTypes().contains(GdDisplayModeEnum.FIRST_BRUSH.getCode())) {
                target.setBootCounts(Lists.newArrayList(1));
            }
        }
    }

    private void processTime(Task task) {
        Assert.notNull(task.getDate(), "日期不能为空");
        Assert.notNull(task.getBeginTime(), "开始时间不能为空");
        Assert.notNull(task.getEndTime(), "结束时间不能为空");
        LocalDate localDate = task.getDate();
        LocalDateTime beginTime = task.getBeginTime();
        LocalDateTime endTime = task.getEndTime();
        Assert.isTrue(localDate.equals(beginTime.toLocalDate()) && localDate.equals(endTime.toLocalDate()),
                "日期和时间段不一致");
        Assert.isTrue(endTime.isAfter(beginTime), "结束时间必须大于开始时间");
    }
}
