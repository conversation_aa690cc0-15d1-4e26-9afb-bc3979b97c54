package com.bilibili.brand.biz.report.query.impl;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.report.dto.DataReportQueryContext;
import com.bilibili.brand.biz.report.dto.DataReportQueryDto;
import com.bilibili.brand.biz.report.dto.OrderReportDto;
import com.bilibili.brand.biz.report.enums.AdDimensionEnum;
import com.bilibili.brand.biz.schedule.po.GdTopViewPo;
import com.bilibili.brand.platform.charging.po.AdStatOrderDayPo;
import com.bilibili.brand.platform.charging.po.AdStatOrderDayPoExample;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.report.platform.api.creative.dto.VideoPlayStatCreativeDto;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.utils.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DefaultOrderDataReportQueryHandler extends BaseOrderDataReportQueryHandler {

    @Override
    public Integer supportAdDimension() {
        return AdDimensionEnum.ORDER.getCode();
    }

    @Override
    protected PageResult<OrderReportDto> doQuery(DataReportQueryContext context, DataReportQueryDto queryDto) {
        List<Integer> orderIdList = context.getOrderIdList(queryDto);
        List<Integer> salesTypeList = context.getSalesTypeList();
        log.info("DefaultOrderDataReportQueryHandler，orderIdList：{}，salesTypeList：{}", orderIdList, salesTypeList);
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new PageResult<>(0, new ArrayList<>());
        }


        // 所有的订单创意映射信息
        Map<Integer, Set<Long>> orderIdToCreativeListMap = context.getGdCreativePoMap()
                .values()
                .stream()
                .collect(Collectors.groupingBy(GdCreativePo::getOrderId, Collectors.mapping(GdCreativePo::getCreativeId, Collectors.toSet())));
        Map<Integer, Set<Long>> ssaOrderIdToCreativeListMap = context.getSsaSplashScreenPoMap()
                .values()
                .stream()
                .collect(Collectors.groupingBy(SsaSplashScreenPo::getGdOrderId, Collectors.mapping(item -> NumberUtil.toValidLong(item.getId()), Collectors.toSet())));
        Map<Integer, Set<Long>> newHfAndroidOrderIdToCreativeIdListMap = context.getGdTopViewPoMap()
                .values()
                .stream()
                .collect(Collectors.groupingBy(GdTopViewPo::getGdOrderId, Collectors.mapping(GdTopViewPo::getNewHfAndroidCreativeId, Collectors.toSet())));
        Map<Integer, Set<Long>> newHfIosOrderIdToCreativeIdListMap = context.getGdTopViewPoMap()
                .values()
                .stream()
                .collect(Collectors.groupingBy(GdTopViewPo::getGdOrderId, Collectors.mapping(GdTopViewPo::getNewHfIosCreativeId, Collectors.toSet())));


        // 查询非首焦订单报表信息
        CompletableFuture<Long> adStatOrderDayCountFuture = CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(orderIdList)) {
                return 0L;
            }
            AdStatOrderDayPoExample queryExample = new AdStatOrderDayPoExample();
            queryExample.createCriteria()
                    .andOrderIdIn(orderIdList)
                    .andGroupTimeBetween(queryDto.getFromTime(), queryDto.getToTime())
                    .andSalesTypeIn(salesTypeList)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            return adStatOrderDayCustomDao.countByExample(queryDto.getAggDimension(), queryExample);
        }, executor);
        CompletableFuture<List<AdStatOrderDayPo>> adStatOrderDayFuture = CompletableFuture.supplyAsync(() -> {
            if (CollectionUtils.isEmpty(orderIdList)) {
                return new ArrayList<>();
            }
            AdStatOrderDayPoExample queryExample = new AdStatOrderDayPoExample();
            Page page = Page.valueOf(queryDto.getPage(), queryDto.getPageSize());
            queryExample.setLimit(page.getLimit());
            queryExample.setOffset(page.getOffset());
            queryExample.createCriteria()
                    .andOrderIdIn(orderIdList)
                    .andGroupTimeBetween(queryDto.getFromTime(), queryDto.getToTime())
                    .andSalesTypeIn(salesTypeList)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            return adStatOrderDayCustomDao.selectByExample(queryDto.getAggDimension(), queryExample);
        }, executor);


        // 查询首焦订单报表信息
        CompletableFuture<List<AdStatOrderDayPo>> newHfAdStatOrderDayFuture = adStatOrderDayFuture.thenApplyAsync(adStatOrderDayPoList -> {
            // TOP_VIEW_CPT、TOP_VIEW_GD有首焦创意信息
            Set<Integer> validSalesTypeSet = Sets.newHashSet(
                    SalesType.TOP_VIEW_PLUS.getCode(),
                    SalesType.TOP_VIEW_GD_PLUS.getCode()
            );
            List<Integer> validOrderIdList = adStatOrderDayPoList.stream()
                    .filter(po -> validSalesTypeSet.contains(po.getSalesType()))
                    .map(AdStatOrderDayPo::getOrderId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validOrderIdList)) {
                return new ArrayList<>();
            }
            AdStatOrderDayPoExample newHfAdStatOrderDayPoQueryExample = new AdStatOrderDayPoExample();
            newHfAdStatOrderDayPoQueryExample.createCriteria()
                    .andOrderIdIn(validOrderIdList)
                    .andGroupTimeBetween(queryDto.getFromTime(), queryDto.getToTime())
                    .andSalesTypeEqualTo(SalesType.TOP_VIEW_CPT.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            return adStatOrderDayDao.selectByExample(newHfAdStatOrderDayPoQueryExample);
        }, executor);


        // 查询视频播放量信息，为了加快查询速度，必须对创意ID进行筛选
        CompletableFuture<Map<Long, VideoPlayStatCreativeDto>> videoPlayStatFuture = adStatOrderDayFuture.thenApplyAsync(adStatOrderDayPoList -> {
            Set<Integer> validSalesTypeSet = Sets.newHashSet(
                    SalesType.GD.getCode(),
                    SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode(),
                    SalesType.SEARCH_CPT.getCode(),
                    SalesType.CPT.getCode(),
                    SalesType.TOP_VIEW_PLUS.getCode(),
                    SalesType.TOP_VIEW_GD_PLUS.getCode()
            );
            List<Integer> validOrderIdList = adStatOrderDayPoList.stream()
                    .filter(po -> validSalesTypeSet.contains(po.getSalesType()))
                    .map(AdStatOrderDayPo::getOrderId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(validOrderIdList)) {
                return new HashMap<>();
            }

            List<Long> validCreativeIdList = new ArrayList<>();
            for (Integer orderId : validOrderIdList) {
                validCreativeIdList.addAll(orderIdToCreativeListMap.getOrDefault(orderId, new HashSet<>()));
                validCreativeIdList.addAll(ssaOrderIdToCreativeListMap.getOrDefault(orderId, new HashSet<>()));
                validCreativeIdList.addAll(newHfAndroidOrderIdToCreativeIdListMap.getOrDefault(orderId, new HashSet<>()));
                validCreativeIdList.addAll(newHfIosOrderIdToCreativeIdListMap.getOrDefault(orderId, new HashSet<>()));
            }
            if (CollectionUtils.isEmpty(validCreativeIdList)) {
                return new HashMap<>();
            }

            List<CompletableFuture<Map<Long, VideoPlayStatCreativeDto>>> videoPlayStatCreativeFutureList = Lists.partition(validCreativeIdList, BATCH_SIZE)
                    .stream()
                    .map(list -> CompletableFuture.supplyAsync(() -> videoPlayStatCreativeService.getCreativeStat(list, queryDto.getFromTime(), queryDto.getToTime())))
                    .collect(Collectors.toList());
            CompletableFuture.allOf(videoPlayStatCreativeFutureList.toArray(new CompletableFuture[0])).join();

            Map<Long, VideoPlayStatCreativeDto> result = new HashMap<>();
            videoPlayStatCreativeFutureList.forEach(future -> result.putAll(future.join()));
            return result;
        }, executor);


        // 等待所有任务执行完成
        CompletableFuture.allOf(adStatOrderDayCountFuture, adStatOrderDayFuture, newHfAdStatOrderDayFuture, videoPlayStatFuture).join();


        // 获取任务执行结果
        int count = Math.toIntExact(adStatOrderDayCountFuture.join());
        List<AdStatOrderDayPo> adStatOrderDayPoList = adStatOrderDayFuture.join();
        List<AdStatOrderDayPo> newHfAdStatOrderDayPoList = newHfAdStatOrderDayFuture.join();
        Map<Integer, Map<Timestamp, AdStatOrderDayPo>> newHfAdStatOrderDayPoMap = newHfAdStatOrderDayPoList.stream()
                .collect(Collectors.groupingBy(AdStatOrderDayPo::getOrderId, Collectors.toMap(AdStatOrderDayPo::getGroupTime, Function.identity())));
        Map<Long, VideoPlayStatCreativeDto> videoPlayStatCreativeDtoMap = videoPlayStatFuture.join();


        // 构建订单报表查询结果
        List<OrderReportDto> records = adStatOrderDayPoList.stream()
                .map(po -> buildOrderReportDto(context, queryDto, po, newHfAdStatOrderDayPoMap,
                        videoPlayStatCreativeDtoMap, orderIdToCreativeListMap, ssaOrderIdToCreativeListMap, newHfAndroidOrderIdToCreativeIdListMap,
                        newHfIosOrderIdToCreativeIdListMap))
                .collect(Collectors.toList());
        return new PageResult<>(count, records);
    }

    private OrderReportDto buildOrderReportDto(DataReportQueryContext context, DataReportQueryDto queryDto,
                                               AdStatOrderDayPo po, Map<Integer, Map<Timestamp, AdStatOrderDayPo>> newHfAdStatOrderDayPoMap,
                                               Map<Long, VideoPlayStatCreativeDto> videoPlayStatCreativeDtoMap, Map<Integer, Set<Long>> orderIdToCreativeListMap,
                                               Map<Integer, Set<Long>> ssaOrderIdToCreativeListMap, Map<Integer, Set<Long>> newHfAndroidOrderIdToCreativeIdListMap,
                                               Map<Integer, Set<Long>> newHfIosOrderIdToCreativeIdListMap) {
        OrderReportDto dto = new OrderReportDto();

        // set日期
        List<Timestamp> groupTimeList = Arrays.stream(StringUtils.split(po.getGroupTimes(), ","))
                .map(Timestamp::valueOf)
                .sorted()
                .collect(Collectors.toList());
        dto.setDate(TimeUtil.timestampToIsoDateStr(groupTimeList.get(0)) + " ~ " + TimeUtil.timestampToIsoDateStr(groupTimeList.get(groupTimeList.size() - 1)));

        // set基础创意信息，不关联其他表
        dto.setShowCount(NumberUtil.toValidLong(po.getShowCount()));
        dto.setClickCount(NumberUtil.toValidLong(po.getClickCount()));

        // 关联id
        FcOrderPo fcOrderPo = context.getFcOrderPoMap().get(NumberUtil.toValidInt(po.getOrderId()));
        if (Objects.nonNull(fcOrderPo)) {
            dto.setCrmContractId(NumberUtil.toValidInt(fcOrderPo.getCrmContractId()));
        }
        dto.setOrderId(NumberUtil.toValidInt(po.getOrderId()));

        // set 普通稿件播放量信息
        // videoPlayStatCreativeDtoMap是创意维度的数据，需要通过订单创意关联关系聚合为排期维度的数据
        Set<Long> creativeIdList = orderIdToCreativeListMap.getOrDefault(po.getOrderId(), new HashSet<>());
        creativeIdList.addAll(ssaOrderIdToCreativeListMap.getOrDefault(po.getOrderId(), new HashSet<>()));
        List<VideoPlayStatCreativeDto> videoPlayStatCreativeDtoList = creativeIdList.stream()
                .map(videoPlayStatCreativeDtoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(videoPlayStatCreativeDtoList)) {
            Long playCount = videoPlayStatCreativeDtoList.stream()
                    .flatMap(videoPlayStatCreativeDto -> groupTimeList.stream().map(videoPlayStatCreativeDto::getDayPlayCountWithNull))
                    .filter(Objects::nonNull)
                    .reduce(Long::sum).orElse(null);
            dto.setPlayCount(playCount);
        }

        // set 首焦稿件播放量信息
        Set<Long> newHfCreativeIdList = newHfAndroidOrderIdToCreativeIdListMap.getOrDefault(po.getOrderId(), new HashSet<>());
        newHfCreativeIdList.addAll(newHfIosOrderIdToCreativeIdListMap.getOrDefault(po.getOrderId(), new HashSet<>()));
        videoPlayStatCreativeDtoList = newHfCreativeIdList.stream()
                .map(videoPlayStatCreativeDtoMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(videoPlayStatCreativeDtoList)) {
            Long hfPlayCount = videoPlayStatCreativeDtoList.stream()
                    .flatMap(videoPlayStatCreativeDto -> groupTimeList.stream().map(videoPlayStatCreativeDto::getDayPlayCountWithNull))
                    .filter(Objects::nonNull)
                    .reduce(Long::sum).orElse(null);
            dto.setHfPlayCount(hfPlayCount);
        }

        // set首焦信息
        Map<Timestamp, AdStatOrderDayPo> newHfAdStatOrderDayPoTimeMap = newHfAdStatOrderDayPoMap.getOrDefault(po.getOrderId(), new HashMap<>());
        for (Map.Entry<Timestamp, AdStatOrderDayPo> entry : newHfAdStatOrderDayPoTimeMap.entrySet()) {
            Timestamp key = entry.getKey();
            if (groupTimeList.contains(key)) {
                AdStatOrderDayPo value = entry.getValue();
                dto.setHfClickCount(NumberUtil.toValidLong(dto.getHfClickCount()) + NumberUtil.toValidLong(value.getClickCount()));
                dto.setHfShowCount(NumberUtil.toValidLong(dto.getHfShowCount()) + NumberUtil.toValidLong(value.getShowCount()));
            }
        }
        return dto;
    }
}
