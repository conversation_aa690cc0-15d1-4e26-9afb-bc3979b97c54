package com.bilibili.brand.biz.account.po;

import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年2月27日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryCategoryPo {
    /**自增ID**/
    private Integer id;
    
    /**行业名**/
    private String name;
    
    /**层级**/
    private Integer level;
    
    /**父级ID**/
    private Integer parentId;
    
    /**创建时间**/
    private Timestamp addTime;
    
    /**更新时间**/
    private Timestamp updateTime;
    
    /**是否删除: 0-否 1-是**/
    private Integer isDeleted;
}
