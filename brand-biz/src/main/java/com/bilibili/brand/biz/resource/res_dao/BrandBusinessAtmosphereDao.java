package com.bilibili.brand.biz.resource.res_dao;

import com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePo;
import com.bilibili.brand.biz.resource.pojo.BrandBusinessAtmospherePoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BrandBusinessAtmosphereDao {
    long countByExample(BrandBusinessAtmospherePoExample example);

    int deleteByExample(BrandBusinessAtmospherePoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BrandBusinessAtmospherePo record);

    int insertBatch(List<BrandBusinessAtmospherePo> records);

    int insertUpdateBatch(List<BrandBusinessAtmospherePo> records);

    int insert(BrandBusinessAtmospherePo record);

    int insertUpdateSelective(BrandBusinessAtmospherePo record);

    int insertSelective(BrandBusinessAtmospherePo record);

    List<BrandBusinessAtmospherePo> selectByExample(BrandBusinessAtmospherePoExample example);

    BrandBusinessAtmospherePo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BrandBusinessAtmospherePo record, @Param("example") BrandBusinessAtmospherePoExample example);

    int updateByExample(@Param("record") BrandBusinessAtmospherePo record, @Param("example") BrandBusinessAtmospherePoExample example);

    int updateByPrimaryKeySelective(BrandBusinessAtmospherePo record);

    int updateByPrimaryKey(BrandBusinessAtmospherePo record);
}