package com.bilibili.brand.biz.creative.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.brand.biz.creative.po.querydsl.GdContractAccountConfigPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QGdContractAccountConfig is a Querydsl query type for GdContractAccountConfigPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QGdContractAccountConfig extends com.querydsl.sql.RelationalPathBase<GdContractAccountConfigPo> {

    private static final long serialVersionUID = -*********;

    public static final QGdContractAccountConfig gdContractAccountConfig = new QGdContractAccountConfig("gd_contract_account_config");

    public final StringPath configContent = createString("configContent");

    public final NumberPath<Integer> contractAccountId = createNumber("contractAccountId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final com.querydsl.sql.PrimaryKey<GdContractAccountConfigPo> primary = createPrimaryKey(id);

    public QGdContractAccountConfig(String variable) {
        super(GdContractAccountConfigPo.class, forVariable(variable), "null", "gd_contract_account_config");
        addMetadata();
    }

    public QGdContractAccountConfig(String variable, String schema, String table) {
        super(GdContractAccountConfigPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QGdContractAccountConfig(String variable, String schema) {
        super(GdContractAccountConfigPo.class, forVariable(variable), schema, "gd_contract_account_config");
        addMetadata();
    }

    public QGdContractAccountConfig(Path<? extends GdContractAccountConfigPo> path) {
        super(path.getType(), path.getMetadata(), "null", "gd_contract_account_config");
        addMetadata();
    }

    public QGdContractAccountConfig(PathMetadata metadata) {
        super(GdContractAccountConfigPo.class, metadata, "null", "gd_contract_account_config");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(configContent, ColumnMetadata.named("config_content").withIndex(3).ofType(Types.VARCHAR).withSize(2000).notNull());
        addMetadata(contractAccountId, ColumnMetadata.named("contract_account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(5).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
    }

}

