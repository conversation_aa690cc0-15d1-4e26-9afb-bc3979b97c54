package com.bilibili.brand.biz.schedule.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class GdPricePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public GdPricePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNull() {
            addCriterion("template_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIsNotNull() {
            addCriterion("template_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateIdEqualTo(Integer value) {
            addCriterion("template_id =", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotEqualTo(Integer value) {
            addCriterion("template_id <>", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThan(Integer value) {
            addCriterion("template_id >", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("template_id >=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThan(Integer value) {
            addCriterion("template_id <", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdLessThanOrEqualTo(Integer value) {
            addCriterion("template_id <=", value, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdIn(List<Integer> values) {
            addCriterion("template_id in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotIn(List<Integer> values) {
            addCriterion("template_id not in", values, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdBetween(Integer value1, Integer value2) {
            addCriterion("template_id between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("template_id not between", value1, value2, "templateId");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNull() {
            addCriterion("template_name is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIsNotNull() {
            addCriterion("template_name is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNameEqualTo(String value) {
            addCriterion("template_name =", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotEqualTo(String value) {
            addCriterion("template_name <>", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThan(String value) {
            addCriterion("template_name >", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameGreaterThanOrEqualTo(String value) {
            addCriterion("template_name >=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThan(String value) {
            addCriterion("template_name <", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLessThanOrEqualTo(String value) {
            addCriterion("template_name <=", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameLike(String value) {
            addCriterion("template_name like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotLike(String value) {
            addCriterion("template_name not like", value, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameIn(List<String> values) {
            addCriterion("template_name in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotIn(List<String> values) {
            addCriterion("template_name not in", values, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameBetween(String value1, String value2) {
            addCriterion("template_name between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andTemplateNameNotBetween(String value1, String value2) {
            addCriterion("template_name not between", value1, value2, "templateName");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeIsNull() {
            addCriterion("inline_sales_type is null");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeIsNotNull() {
            addCriterion("inline_sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeEqualTo(Integer value) {
            addCriterion("inline_sales_type =", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeNotEqualTo(Integer value) {
            addCriterion("inline_sales_type <>", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeGreaterThan(Integer value) {
            addCriterion("inline_sales_type >", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("inline_sales_type >=", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeLessThan(Integer value) {
            addCriterion("inline_sales_type <", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("inline_sales_type <=", value, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeIn(List<Integer> values) {
            addCriterion("inline_sales_type in", values, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeNotIn(List<Integer> values) {
            addCriterion("inline_sales_type not in", values, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("inline_sales_type between", value1, value2, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andInlineSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("inline_sales_type not between", value1, value2, "inlineSalesType");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleIsNull() {
            addCriterion("creative_style is null");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleIsNotNull() {
            addCriterion("creative_style is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleEqualTo(Integer value) {
            addCriterion("creative_style =", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNotEqualTo(Integer value) {
            addCriterion("creative_style <>", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleGreaterThan(Integer value) {
            addCriterion("creative_style >", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleGreaterThanOrEqualTo(Integer value) {
            addCriterion("creative_style >=", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleLessThan(Integer value) {
            addCriterion("creative_style <", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleLessThanOrEqualTo(Integer value) {
            addCriterion("creative_style <=", value, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleIn(List<Integer> values) {
            addCriterion("creative_style in", values, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNotIn(List<Integer> values) {
            addCriterion("creative_style not in", values, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleBetween(Integer value1, Integer value2) {
            addCriterion("creative_style between", value1, value2, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNotBetween(Integer value1, Integer value2) {
            addCriterion("creative_style not between", value1, value2, "creativeStyle");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameIsNull() {
            addCriterion("creative_style_name is null");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameIsNotNull() {
            addCriterion("creative_style_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameEqualTo(String value) {
            addCriterion("creative_style_name =", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameNotEqualTo(String value) {
            addCriterion("creative_style_name <>", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameGreaterThan(String value) {
            addCriterion("creative_style_name >", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameGreaterThanOrEqualTo(String value) {
            addCriterion("creative_style_name >=", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameLessThan(String value) {
            addCriterion("creative_style_name <", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameLessThanOrEqualTo(String value) {
            addCriterion("creative_style_name <=", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameLike(String value) {
            addCriterion("creative_style_name like", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameNotLike(String value) {
            addCriterion("creative_style_name not like", value, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameIn(List<String> values) {
            addCriterion("creative_style_name in", values, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameNotIn(List<String> values) {
            addCriterion("creative_style_name not in", values, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameBetween(String value1, String value2) {
            addCriterion("creative_style_name between", value1, value2, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andCreativeStyleNameNotBetween(String value1, String value2) {
            addCriterion("creative_style_name not between", value1, value2, "creativeStyleName");
            return (Criteria) this;
        }

        public Criteria andBasePriceIsNull() {
            addCriterion("base_price is null");
            return (Criteria) this;
        }

        public Criteria andBasePriceIsNotNull() {
            addCriterion("base_price is not null");
            return (Criteria) this;
        }

        public Criteria andBasePriceEqualTo(Integer value) {
            addCriterion("base_price =", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceNotEqualTo(Integer value) {
            addCriterion("base_price <>", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceGreaterThan(Integer value) {
            addCriterion("base_price >", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("base_price >=", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceLessThan(Integer value) {
            addCriterion("base_price <", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceLessThanOrEqualTo(Integer value) {
            addCriterion("base_price <=", value, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceIn(List<Integer> values) {
            addCriterion("base_price in", values, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceNotIn(List<Integer> values) {
            addCriterion("base_price not in", values, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceBetween(Integer value1, Integer value2) {
            addCriterion("base_price between", value1, value2, "basePrice");
            return (Criteria) this;
        }

        public Criteria andBasePriceNotBetween(Integer value1, Integer value2) {
            addCriterion("base_price not between", value1, value2, "basePrice");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeIsNull() {
            addCriterion("gd_sales_type is null");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeIsNotNull() {
            addCriterion("gd_sales_type is not null");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeEqualTo(Integer value) {
            addCriterion("gd_sales_type =", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeNotEqualTo(Integer value) {
            addCriterion("gd_sales_type <>", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeGreaterThan(Integer value) {
            addCriterion("gd_sales_type >", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("gd_sales_type >=", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeLessThan(Integer value) {
            addCriterion("gd_sales_type <", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("gd_sales_type <=", value, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeIn(List<Integer> values) {
            addCriterion("gd_sales_type in", values, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeNotIn(List<Integer> values) {
            addCriterion("gd_sales_type not in", values, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeBetween(Integer value1, Integer value2) {
            addCriterion("gd_sales_type between", value1, value2, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andGdSalesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("gd_sales_type not between", value1, value2, "gdSalesType");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceIsNull() {
            addCriterion("next_stage_price is null");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceIsNotNull() {
            addCriterion("next_stage_price is not null");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceEqualTo(Integer value) {
            addCriterion("next_stage_price =", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceNotEqualTo(Integer value) {
            addCriterion("next_stage_price <>", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceGreaterThan(Integer value) {
            addCriterion("next_stage_price >", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("next_stage_price >=", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceLessThan(Integer value) {
            addCriterion("next_stage_price <", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceLessThanOrEqualTo(Integer value) {
            addCriterion("next_stage_price <=", value, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceIn(List<Integer> values) {
            addCriterion("next_stage_price in", values, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceNotIn(List<Integer> values) {
            addCriterion("next_stage_price not in", values, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceBetween(Integer value1, Integer value2) {
            addCriterion("next_stage_price between", value1, value2, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andNextStagePriceNotBetween(Integer value1, Integer value2) {
            addCriterion("next_stage_price not between", value1, value2, "nextStagePrice");
            return (Criteria) this;
        }

        public Criteria andCycleIdIsNull() {
            addCriterion("cycle_id is null");
            return (Criteria) this;
        }

        public Criteria andCycleIdIsNotNull() {
            addCriterion("cycle_id is not null");
            return (Criteria) this;
        }

        public Criteria andCycleIdEqualTo(Integer value) {
            addCriterion("cycle_id =", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdNotEqualTo(Integer value) {
            addCriterion("cycle_id <>", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdGreaterThan(Integer value) {
            addCriterion("cycle_id >", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("cycle_id >=", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdLessThan(Integer value) {
            addCriterion("cycle_id <", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdLessThanOrEqualTo(Integer value) {
            addCriterion("cycle_id <=", value, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdIn(List<Integer> values) {
            addCriterion("cycle_id in", values, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdNotIn(List<Integer> values) {
            addCriterion("cycle_id not in", values, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdBetween(Integer value1, Integer value2) {
            addCriterion("cycle_id between", value1, value2, "cycleId");
            return (Criteria) this;
        }

        public Criteria andCycleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("cycle_id not between", value1, value2, "cycleId");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIsNull() {
            addCriterion("raise_info is null");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIsNotNull() {
            addCriterion("raise_info is not null");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoEqualTo(String value) {
            addCriterion("raise_info =", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotEqualTo(String value) {
            addCriterion("raise_info <>", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoGreaterThan(String value) {
            addCriterion("raise_info >", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoGreaterThanOrEqualTo(String value) {
            addCriterion("raise_info >=", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLessThan(String value) {
            addCriterion("raise_info <", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLessThanOrEqualTo(String value) {
            addCriterion("raise_info <=", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoLike(String value) {
            addCriterion("raise_info like", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotLike(String value) {
            addCriterion("raise_info not like", value, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoIn(List<String> values) {
            addCriterion("raise_info in", values, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotIn(List<String> values) {
            addCriterion("raise_info not in", values, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoBetween(String value1, String value2) {
            addCriterion("raise_info between", value1, value2, "raiseInfo");
            return (Criteria) this;
        }

        public Criteria andRaiseInfoNotBetween(String value1, String value2) {
            addCriterion("raise_info not between", value1, value2, "raiseInfo");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}