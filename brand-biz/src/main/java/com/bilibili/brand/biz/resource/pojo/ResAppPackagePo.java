package com.bilibili.brand.biz.resource.pojo;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class ResAppPackagePo implements Serializable {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 应用包名称
     */
    private String name;

    /**
     * 应用包链接
     */
    private String url;

    /**
     * 应用包的包名称
     */
    private String packageName;

    /**
     * 应用名称
     */
    private String appName;

    /**
     * 适应系统 1-IOS, 2-Android
     */
    private Integer platform;

    /**
     * 版本号
     */
    private String version;

    /**
     * 应用包大小（单位字节）
     */
    private Integer size;

    /**
     * 应用包的MD5
     */
    private String md5;

    /**
     * 图片url
     */
    private String iconUrl;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 软删除 0-有效, 1-删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}