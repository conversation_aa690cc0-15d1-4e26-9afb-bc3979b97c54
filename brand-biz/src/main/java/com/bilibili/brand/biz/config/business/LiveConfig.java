package com.bilibili.brand.biz.config.business;

import com.bilibili.brand.api.common.enums.ShowPriorityType;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/9
 */
@Component
@Data
public class LiveConfig {

    @Value("#{'${cpt.live.unsupport.all.day.sources}'.split(',')}")
    private List<Integer> unsupportBookingAllDaySourceIds;

    @Value("#{'${cpt.live.support.room.templates}'.split(',')}")
    private List<Integer> onlySupportRoomTemplates;

    //直播Story直播中模版
    @Value("#{'${resource.live.story.living.template.ids:387}'.split(',')}")
    private List<Integer> liveStoryLivingTemplateIds;

    //直播inline模版（完整模板）
    @Value("#{'${resource.live.inline.template.ids:517,518,519,520}'.split(',')}")
    private List<Integer> liveInlineTemplateIds;

    //直播小卡模版（完整模板）
    @Value("#{'${resource.live.small.template.ids:516}'.split(',')}")
    private List<Integer> liveSmallTemplateIds;

    //直播小卡直播中模版（完整模板的子集）
    @Value("#{'${resource.live.small.living.template.ids:516,266}'.split(',')}")
    private List<Integer> liveSmallLivingTemplateIds;

    //直播inline直播中模版（完整模板的子集）
    @Value("#{'${resource.live.inline.living.template.ids:517,518,248}'.split(',')}")
    private List<Integer> liveInlineLivingTemplateIds;

    //直播inline预约模版（完整模板的子集）
    @Value("#{'${resource.live.inline.booking.template.ids:519,520,327,328}'.split(',')}")
    private List<Integer> liveInlineBookingTemplateIds;


    //支持包段的模板ID
    @Value("#{'${resource.live.book_time_period.template.ids:517,518}'.split(',')}")
    private List<Integer> liveSupportsBookTimePeriodTemplateIds;

    /**
     * 248：直播，大卡
     * 266：直播，小卡
     * 327、328：直播预约，大卡
     * 325、326：直播预约，大卡
     */
    //直播预约模板
    //旧的直播预约模板325,326,327,328（已废弃）
    @Value("#{'${resource.live.booking.template.ids}'.split(',')}")
    private List<Integer> oldLiveBookingTemplateIds;

//    //248,327,328
//    @Value("#{'${resource.top.flow.template.ids}'.split(',')}")
//    private List<Integer> oldTopFlowTemplateIds;
//
//    //266
//    @Value("#{'${resource.small.card.top.flow.template.ids}'.split(',')}")
//    private List<Integer> oldSmallCardTopFlowTemplateIds;


    public boolean needCheckLiveCptBooking(int sourceId) {
        return unsupportBookingAllDaySourceIds.contains(sourceId);
    }

    /**
     * 获取预约模板
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
     */
    public List<Integer> getLiveBookingTemplates() {
        List<Integer> result = new ArrayList<>();
        result.addAll(liveInlineBookingTemplateIds);
        result.addAll(oldLiveBookingTemplateIds);//oldLiveBookingTemplateIds兼容
        return result;
    }

    /**
     * 获取优先和控量参数
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
     */
    public ShowPriorityType getShowPriority(int templateId) {
        if (liveSupportsBookTimePeriodTemplateIds.contains(templateId)) {
            return ShowPriorityType.BUY_OUT_AND_SHOW_BY_LIVE_STATUS;
        }
        return ShowPriorityType.NORMAL;
    }

    /**
     * 是否支持直播中
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
     */
    public boolean isSupportsLivingTemplate(int templateId) {
        return liveSmallLivingTemplateIds.contains(templateId)
                || liveInlineLivingTemplateIds.contains(templateId)
                || liveStoryLivingTemplateIds.contains(templateId);
    }


    /**
     * 是否支持预约
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
     */
    public boolean isSupportsBookingTemplate(int templateId) {
        //oldLiveBookingTemplateIds兼容
        //liveInlineLivingTemplateIds直播中的模板也支持预约
        return liveInlineBookingTemplateIds.contains(templateId)
                || liveInlineLivingTemplateIds.contains(templateId)
                || oldLiveBookingTemplateIds.contains(templateId);
    }
}
