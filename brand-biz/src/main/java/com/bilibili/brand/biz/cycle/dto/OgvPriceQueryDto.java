package com.bilibili.brand.biz.cycle.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/13 11:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OgvPriceQueryDto implements Serializable {
    private static final long serialVersionUID = -4526525947047418922L;
    private Integer cycleId;
    private Integer orderProduct;
    private List<Integer> platformIdList;
    private List<String> levelList;
    private List<Integer> seasonTypeList;
    private List<Integer> stickerIdList;
    private List<Integer> statusList;
    private Integer pageIndex;
    private Integer pageSize;
}
