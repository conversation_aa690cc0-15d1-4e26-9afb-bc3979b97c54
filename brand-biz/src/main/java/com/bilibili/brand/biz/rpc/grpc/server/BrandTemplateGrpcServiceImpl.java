package com.bilibili.brand.biz.rpc.grpc.server;

import com.bapis.ad.brand.Pageable;
import com.bapis.ad.brand.Responser;
import com.bapis.ad.brand.template.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.template.IBrandTemplateLabelService;
import com.bilibili.brand.api.template.dto.*;
import com.bilibili.brand.biz.converter.GrpcCommonConverter;
import com.bilibili.brand.biz.rpc.converter.TemplateLabelConverter;
import com.google.common.collect.Lists;
import com.google.protobuf.util.JsonFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import pleiades.venus.starter.rpc.server.RPCService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/27 17:42
 */
@Slf4j
@RPCService
public class BrandTemplateGrpcServiceImpl extends BrandTemplateServiceGrpc.BrandTemplateServiceImplBase {
    @Autowired
    private IBrandTemplateLabelService brandTemplateLabelService;

    @Override
    public void addBrandTemplate(AddBrandTemplateReq request, StreamObserver<AddBrandTemplateRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        AddBrandTemplateRes.Builder resBuilder = AddBrandTemplateRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            brandTemplateLabelService.addBrandTemplate(Lists.newArrayList(request.getTemplateIdList()), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] addBrandTemplate error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] saveBrandTemplateLabel error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void saveBrandTemplateLabel(SaveBrandTemplateLabelReq request, StreamObserver<SaveBrandTemplateLabelRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        SaveBrandTemplateLabelRes.Builder resBuilder = SaveBrandTemplateLabelRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BrandTemplateLabelDto brandTemplateLabelDto = TemplateLabelConverter.MAPPER.toBrandTemplateLabelDto(request.getTemplateLabel());
            brandTemplateLabelService.saveBrandTemplateLabel(brandTemplateLabelDto, operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] saveBrandTemplateLabel error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] saveBrandTemplateLabel error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBrandTemplateLabelList(GetBrandTemplateLabelListReq request, StreamObserver<GetBrandTemplateLabelListRes> responseObserver) {
        GetBrandTemplateLabelListRes.Builder resBuilder = GetBrandTemplateLabelListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BrandTemplateLabelQueryDto brandTemplateLabelQueryDto = BrandTemplateLabelQueryDto.builder()
                    .pageIndex(request.getPageable().getPageIndex())
                    .pageSize(request.getPageable().getPageSize())
                    .labelKey(request.getLabelKey())
                    .strictMatch(request.getStrictMatch())
                    .includeRelation(request.getIncludeRelation())
                    .build();
            PageResult<BrandTemplateLabelDto> pageResult = brandTemplateLabelService.getBrandTemplateLabelList(brandTemplateLabelQueryDto);
            resBuilder.setPageable(Pageable.newBuilder().setTotalCount(pageResult.getTotal()).build())
                    .addAllLabel(TemplateLabelConverter.MAPPER.toBrandTemplateLabelList(pageResult.getRecords()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] getBrandTemplateLabelList error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] getBrandTemplateLabelList error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void saveBrandTemplateLabelRelation(SaveBrandTemplateLabelRelationReq request, StreamObserver<SaveBrandTemplateLabelRelationRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        SaveBrandTemplateLabelRelationRes.Builder resBuilder = SaveBrandTemplateLabelRelationRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            List<BrandTemplateLabelTemplateRelationDto> relations = TemplateLabelConverter.MAPPER.toBrandTemplateLabelTemplateRelationDtoList(request.getRelationList());
            brandTemplateLabelService.saveBrandTemplateLabelRelation(request.getTemplateIdList(), relations, operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] saveBrandTemplateLabelRelation error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] saveBrandTemplateLabelRelation error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void copyBrandTemplateLabelRelation(CopyBrandTemplateLabelRelationReq request, StreamObserver<CopyBrandTemplateLabelRelationRes> responseObserver) {
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        CopyBrandTemplateLabelRelationRes.Builder resBuilder = CopyBrandTemplateLabelRelationRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            brandTemplateLabelService.copyBrandTemplateLabelRelation(request.getSourceTemplateId(), request.getTargetTemplateIdList(), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] copyBrandTemplateLabelRelation error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] copyBrandTemplateLabelRelation error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void getBrandTemplateList(GetBrandTemplateListReq request, StreamObserver<GetBrandTemplateListRes> responseObserver) {
        GetBrandTemplateListRes.Builder resBuilder = GetBrandTemplateListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            BrandTemplateQueryDto brandTemplateLabelTemplateQueryDto = BrandTemplateQueryDto.builder()
                    .pageIndex(request.getPageable().getPageIndex())
                    .pageSize(request.getPageable().getPageSize())
                    .templateIdList(request.getTemplateIdList())
                    .includeRelation(request.getIncludeRelation())
                    .build();
            PageResult<BrandTemplateDto> pageResult = brandTemplateLabelService.getBrandTemplateList(brandTemplateLabelTemplateQueryDto);
            resBuilder.setPageable(Pageable.newBuilder().setTotalCount(pageResult.getTotal()).build())
                    .addAllTemplate(TemplateLabelConverter.MAPPER.toBrandTemplateList(pageResult.getRecords()));
        } catch (IllegalArgumentException e) {
            log.error("[BrandTemplateGrpcServiceImpl] getBrandTemplateList error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandTemplateGrpcServiceImpl] getBrandTemplateList error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }
}
