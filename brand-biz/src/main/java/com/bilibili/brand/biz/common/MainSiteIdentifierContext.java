package com.bilibili.brand.biz.common;

import com.bilibili.brand.api.common.bean.MainSiteIdentifier;

/**
 * <AUTHOR>
 * @date 2023/7/21
 */
public class MainSiteIdentifierContext {
    private static final ThreadLocal<MainSiteIdentifier> identifierThreadLocal = new ThreadLocal<>();


    public static MainSiteIdentifier getIdentifier() {
        return identifierThreadLocal.get();
    }

    public static void setIdentifier(MainSiteIdentifier identifier) {
        identifierThreadLocal.set(identifier);
    }

    public static void clear() {
        identifierThreadLocal.remove();

    }
}
