package com.bilibili.brand.biz.config.business;

import com.bilibili.brand.common.Constant;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * story gd 相关配置信息
 *
 * <AUTHOR>
 * @date 2022/1/13
 */
@Component
@Data
@Slf4j
public class GdPlusConfig {

    @Value("#{'${small.card.gd.template.ids}'.split(',')}")
    private List<Integer> smallCardTemplateIds;

    @Value("${gd.small.card.max.frequency:4}")
    private Integer gdSmallCardFrequency;

    @Value("#{'${big.card.gd.template.ids}'.split(',')}")
    private List<Integer> bigCardTemplateIds;

    @Value("${gd.big.card.max.frequency:3}")
    private Integer gdBigCardFrequency;

    /**
     * gd 下story的模板
     */
    @Value("#{'${gd.story.template.ids:209,210,419}'.split(',')}")
    private List<Integer> storyGdPlusTemplateIds;

    /**
     * gd 下story支持稿件的模板
     */
    @Value("#{'${gd.story.manuscript.template.ids:209,210}'.split(',')}")
    private List<Integer> storyGdPlusManuscriptTemplateIds;

    @Value("${story.gd.max.frequency:10}")
    private Integer storyGdMaxFrequency;

    /**
     * 支持开启企业空间的模板
     */
    @Value("#{'${resource.support.enterprise.space.templates:209,210}'.split(',')}")
    private List<Integer> supportEnterpriseSpaceTemplateIds;

    /**
     * story跳过gd频控小于3的加价逻辑的模板ids
     */
    @Value("#{'${gd.skip.cpm.premium.templates:209,210,419}'.split(',')}")
    private List<Integer> skipCpmPremiumTemplateIds;

    /**
     * storygd指定商业标
     */
    @Value("#{'${story.gd.mark.ids:54,72}'.split(',')}")
    private List<Integer> storyGdMarkId;

    /**
     * gd下不支持卡片跳转链接的模板
     */
    @Value("#{'${gd.unsupport.schemal.url.teamplates:419}'.split(',')}")
    private List<Integer> unsupportSchemeUrlTamplates;

    /**
     * 在gd下直播推广目的起飞模板id列表（需要走起飞的uv寻量逻辑）
     */
    @Value("#{'${gd.fly.live.teamplates}'.split(',')}")
    private List<Integer> flyGdLiveTemplates;

    @Value("#{new java.util.HashSet('${gd.fly.ogv.teamplates:521,522}')}")
    private List<Integer> flyGdOgvTemplates;

    /**
     * 需要到goblin查询量的模板id列表
     */
    @Value("#{'${gd.query.goblin.templates}'.split(',')}")
    private List<Integer> queryInventoryFromGoblinTemplates;

    /**
     * 跳过goblin查询量的模板id列表
     */
    @Value("#{'${gd.query.goblin.template.blacklist}'.split(',')}")
    private List<Integer> queryInventoryFromGoblinTemplateBlackList;

    /**
     * gd框下模板
     */
    @Value("#{'${gd.under.box.templates}'.split(',')}")
    private List<Integer> underBoxTemplates;

    /**
     * gd框下广告标
     */
    @Value("#{'${gd.under.box.busMarkIds}'.split(',')}")
    private List<Integer> underBoxBusMarkIds;


    /**
     * gd只出默认广告标1的模板
     */
    @Value("#{'${gd.default.busMark.templates}'.split(',')}")
    private List<Integer> defaultBusMarkIdTemplates;

    /**
     * gd动态模板
     */
    @Value("#{'${gd.dynamic.templates:171,142}'.split(',')}")
    private List<Integer> dynamicTemplates;

    /**
     * gd动态广告标
     */
    @Value("#{'${gd.dynamic.busMarkIds:1,9}'.split(',')}")
    private List<Integer> dynamicMarkIds;

    /**
     * gd OTT模板
     */
    @Value("#{'${gd.ott.templates}'.split(',')}")
    private List<Integer> ottTemplates;

    /**
     * gd OTT广告标
     */
    @Value("#{'${gd.ott.busMarkIds}'.split(',')}")
    private List<Integer> ottMarkIds;

    /**
     * gd单端最低约量，单位cpm
     */
    @Value("#{'${gd.minimum.booking.inventory:10}'.split(',')}")
    private Long minimumBookingInventory;

    /*
    * inline 五刷
    * */
    @Value("#{'${big.card.fifth.gd.template.ids:243,252}'.split(',')}")
    private List<Integer> bigCardFifthTemplateIds;


    @Deprecated
    @Value("#{'${resource.top.flow.template.ids}'.split(',')}")
    private List<Integer> topFlowTemplateIds;

    @Deprecated
    @Value("#{'${resource.small.card.top.flow.template.ids}'.split(',')}")
    private List<Integer> smallCardTopFlowTemplateIds;

    @Value("#{'${resource.story.schema.original.template.ids:530}'.split(',')}")
    private List<Integer> storyOriginalSchemaTemplateIds;

    /**
     * 必须开启企业空间的模板
     */
    @Value("#{'${resource.support.enterprise.space.must.templates:530}'.split(',')}")
    private List<Integer> supportEnterpriseSpaceMustTemplateIds;

    /**
     * 包段可预约比例
     */
    @Value("${top.flow.schedule.need.ratio:95}")
    private Long topFlowNeedRatio;

    /**
     * 是否限制框下长图
     */
    @Value("${gd.under.frame.limit.long.image:true}")
    private Boolean limitUnderFrameLongImage;

    @Value("${gd.under.frame.long.image.template:413}")
    private Integer underFrameLongImageTemplateId;

    @Value("${gd.under.frame.limit.cpm:60000}")
    private Long underFrameLimitCpm;

    @Value("#{'${gd.supports.creative.split.platform.template.ids:133}'.split(',')}")
    private Set<Integer> supportCreativeSplitPlatformGdTemplates;


    @Value("${gd.schedule.max.frequency:4}")
    private Integer gdMaxFrequency;

    @Value("#{'${resource.player.detail.template.ids:22,23,73,652}'.split(',')}")
    private List<Integer> playerDetailTemplateIds;

    /**
     * 播放详情页广告标
     */
    @Value("#{'${gd.player.detail.busMarkIds:1,105}'.split(',')}")
    private List<Integer> playDetailMarkIds;

    @Value("${gd.player.detail.first.brush.limit.cpm:65000}")
    private Long playerDetailFirstBrushLimitCpm;

    @Value("${gd.player.detail.half.hour.cpm:2000}")
    private Long playerDetailHalfHourCpm;

    @Value("#{'${big.card.zero.gd.template.ids:721,722,724,725,726,728,729,730}'.split(',')}")
    private List<Integer> bigCardZeroTemplateIds;

    @Value("${gd.fly.small.card.ratio:36}")
    private int gdFlySmallCardRatio;

//    public boolean isStoryGd(Integer templateId) {
//        if (templateId == null) {
//            return false;
//        }
//        return storyGdPlusTemplateIds.contains(templateId);
//    }

    public boolean isLaunchStoryGdManuscript(Integer templateId) {
        if (templateId == null) {
            return false;
        }
        return storyGdPlusManuscriptTemplateIds.contains(templateId);
    }

    public boolean queryInventoryFromGoblin(Integer template) {
        return queryInventoryFromGoblinTemplates.contains(template);
    }

    public boolean inQueryInventoryFromGoblinTemplateBlackList(Integer template) {
        return queryInventoryFromGoblinTemplateBlackList.contains(template);
    }

    /**
     * 获取引擎约量平台定向参数
     */
//    public List<Integer> getGoblinPlatformTarget(Integer platformId, Integer templateId) {
//        log.info("getGoblinPlatformTarget platformId [{}] templateId[{}]", platformId, templateId);
//        if (underBoxTemplates.contains(templateId)) {
//            return Constant.PLATFORM_2_UNDER_BOX_TARGET.get(platformId);
//        }
//        if (smallCardTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_SMALL_CARD_TARGET.get(platformId);
//        }
//        if (bigCardFifthTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_FIFTH_MAPPING.get(platformId);
//        }
//        //todo:确认是否全切
//        if (storyGdPlusTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_STORY_TARGET.get(platformId);
//        }
//        if (playerDetailTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_PLAYER_DETAIL_TARGET.get(platformId);
//        }
//        if (bigCardZeroTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_ZERO_TARGET.get(platformId);
//        }
//        return Constant.PLATFORM2MAPPING.get(platformId);
//
//    }

    /**
     * 获取平台对应资源位id
     */
//    public List<Integer> getSourceId(Integer platformId, Integer templateId) {
//        if (underBoxTemplates.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_UNDER_BOX_SOURCE.get(platformId));
//        }
//        if (smallCardTemplateIds.contains(templateId)) {
//            return Constant.PLATFORM_2_SMALL_SOURCE.get(platformId);
//        }
//        if (bigCardFifthTemplateIds.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_FIFTH_SOURCE.get(platformId));
//        }
//
//        if (storyGdPlusTemplateIds.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_STORY_SOURCE.get(platformId));
//        }
//        if (playerDetailTemplateIds.contains(templateId)){
//            return Lists.newArrayList(Constant.PLATFORM_2_PLAYER_DETAIL_SOURCE.get(platformId));
//        }
//        if (ottTemplates.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_OTT_PAUSE_SOURCE.get(platformId));
//        }
//        if (bigCardZeroTemplateIds.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_ZERO_SOURCE.get(platformId));
//        }
//        return Lists.newArrayList(Constant.PLATFORM2SOURCE.get(platformId));
//    }

    /**
     * 获取平台对应资源位id
     */
//    public List<Integer> getPdSourceId(Integer platformId, Integer templateId) {
//        if (underBoxTemplates.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_UNDER_BOX_SOURCE.get(platformId));
//        }
//        if (smallCardTemplateIds.contains(templateId)) {
//            return Constant.PD_PLATFORM_2_SMALL_SOURCE.get(platformId);
//        }
//        if (bigCardFifthTemplateIds.contains(templateId)) {
//            return Lists.newArrayList(Constant.PLATFORM_2_FIFTH_SOURCE.get(platformId));
//        }
//        return Lists.newArrayList(Constant.PLATFORM2SOURCE.get(platformId));
//    }

    /**
     * 是否是story 原生样式的模板
     */
    public boolean isStoryOriginalSchemaTemplate(Integer templateId){
        return this.storyOriginalSchemaTemplateIds.contains(templateId);
    }

    /**
     * NORMAL(0, "常规（跳转）"),
     * ORIGINAL(1, "原生（唤起原生，比如story点击标题则唤起原生样式）");
     */
    public int getStoryCreativeSchemaMode(Integer templateId){
        return isStoryOriginalSchemaTemplate(templateId) ? 1 : 0;
    }

    /**
     * 查询企业空间开启模式
     *
     * @param templateId
     * @return 0：不支持启用空间 1：可选 2：只能开启 3：只能关闭
     */
    public int getSupportEnterpriseSpaceMode(Integer templateId) {
        if (this.supportEnterpriseSpaceMustTemplateIds.contains(templateId)) {
            return 2;
        }

        if (this.supportEnterpriseSpaceTemplateIds.contains(templateId)) {
            return 1;
        }

        return 0;
    }

    public boolean isBigCardZeroFlushTemplate(Integer templateId) {
        if (templateId == null) {
            return false;
        }
        return bigCardZeroTemplateIds.contains(templateId);
    }
}
