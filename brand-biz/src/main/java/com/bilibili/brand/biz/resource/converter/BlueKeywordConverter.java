package com.bilibili.brand.biz.resource.converter;

import com.bapis.ad.brand.resource.BlueKeywordConfig;
import com.bapis.ad.brand.resource.BlueKeywordConfigItem;
import com.bapis.ad.brand.resource.GetCommentBlueKeywordReq;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigDto;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigItemDto;
import com.bilibili.brand.api.comment.CommentBlueKeywordQueryDto;
import com.bilibili.cpt.platform.biz.po.BrandBlueKeywordAggExtensionPo;
import com.bilibili.cpt.platform.biz.po.BrandBlueKeywordPo;
import com.bilibili.enums.BlueKeywordStatusEnum;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 22:02
 */
@Mapper(collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        imports = {Timestamp.class, BlueKeywordStatusEnum.class})
public interface BlueKeywordConverter {
    BlueKeywordConverter MAPPER = Mappers.getMapper(BlueKeywordConverter.class);

    CommentBlueKeywordQueryDto toCommentBlueKeywordQueryDto(GetCommentBlueKeywordReq req);

    @Mapping(target = "beginTime", expression = "java(new Timestamp(config.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(new Timestamp(config.getEndTime()))")
    BlueKeywordConfigDto toBlueKeywordConfigDto(BlueKeywordConfig config);

    @Mapping(target = "beginTime", expression = "java(dto.getBeginTime() == null ? 0 : dto.getBeginTime().getTime())")
    @Mapping(target = "endTime", expression = "java(dto.getEndTime() == null ? 0: dto.getEndTime().getTime())")
    @Mapping(target = "name", source = "name", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "statusDesc", source = "statusDesc", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    @Mapping(target = "bizScene", source = "bizScene", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
    BlueKeywordConfig toBlueKeywordConfig(BlueKeywordConfigDto dto);

    List<BlueKeywordConfig> toBlueKeywordConfigList(List<BlueKeywordConfigDto> list);

    @Mapping(target = "avidList", source = "avidList")
    @Mapping(target = "keywordList", source = "keywordList")
    BlueKeywordConfigItemDto toBlueKeywordConfigItemDto(BlueKeywordConfigItem item);

    @Mapping(target = "avidList", source = "avidList")
    @Mapping(target = "keywordList", source = "keywordList")
    BlueKeywordConfigItem toBlueKeywordConfigItem(BlueKeywordConfigItemDto dto);

    List<BlueKeywordConfigItem> toBlueKeywordConfigItemList(Collection<BlueKeywordConfigItemDto> list);

    @Mapping(target = "statusDesc", expression = "java(BlueKeywordStatusEnum.getByCodeWithoutEx(po.getStatus()).getDesc())")
    @Mapping(target = "keywordCount", source = "keywordAggPo.count")
    @Mapping(target = "archiveCount", source = "archiveAggPo.count")
    BlueKeywordConfigDto toBlueKeywordConfigDto(BrandBlueKeywordPo po,
                                                BrandBlueKeywordAggExtensionPo keywordAggPo,
                                                BrandBlueKeywordAggExtensionPo archiveAggPo);
}
