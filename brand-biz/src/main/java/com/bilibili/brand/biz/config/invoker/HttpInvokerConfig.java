package com.bilibili.brand.biz.config.invoker;

import com.alibaba.csp.sentinel.adapter.apache.httpclient.SentinelApacheHttpClientBuilder;
import com.alibaba.csp.sentinel.adapter.apache.httpclient.config.SentinelApacheHttpClientConfig;
import com.bilibili.brand.biz.config.degrade.DegradeApacheHttpClientFallback;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.remoting.httpinvoker.HttpComponentsHttpInvokerRequestExecutor;
import org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean;
import org.springframework.remoting.httpinvoker.HttpInvokerRequestExecutor;

/**
 * <AUTHOR>
 * @date 2023/12/15
 */
@Configuration
public class HttpInvokerConfig {

    @Value("${crm.service.url}")
    private String crmUrl;

    @Bean
    public HttpInvokerRequestExecutor httpInvokerRequestExecutor() {
        SentinelApacheHttpClientConfig config = new SentinelApacheHttpClientConfig();
        config.setFallback(new DegradeApacheHttpClientFallback());
        SentinelApacheHttpClientBuilder sentinelApacheHttpClientBuilder = new SentinelApacheHttpClientBuilder(config);
        return new HttpComponentsHttpInvokerRequestExecutor(sentinelApacheHttpClientBuilder.build());
    }

    @Bean
    public HttpInvokerProxyFactoryBean crmContractService(HttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmUrl + "/crmContractService");
        bean.setServiceInterface(ISoaCrmContractService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }
}
