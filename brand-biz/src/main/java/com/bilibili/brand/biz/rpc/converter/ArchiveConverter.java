package com.bilibili.brand.biz.rpc.converter;

import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArcReply;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Mapper
public interface ArchiveConverter {

    ArchiveConverter MAPPER = Mappers.getMapper(ArchiveConverter.class);

    @Mappings({
            @Mapping(source = "aid", target = "aid"),
            @Mapping(source = "firstCid", target = "cid"),
            @Mapping(source = "rights.autoplay", target = "autoplay"),
            @Mapping(source = "author.mid", target = "mid"),
            @Mapping(source = "attribute", target = "type"),
            @Mapping(source = "dimension.width", target = "width"),
            @Mapping(source = "dimension.height", target = "height"),
            @Mapping(source = "pic", target = "cover"),
            @Mapping(source = "state", target = "state"),
            @Mapping(source = "duration", target = "duration"),
            @Mapping(target = "publishTime", expression = "java(arc.getPubDate() * 1000)"),
            @Mapping(source = "typeIDV2", target = "secondPartitionId"),
    })
    ArchiveInfoBo toBo(Arc arc);


}
