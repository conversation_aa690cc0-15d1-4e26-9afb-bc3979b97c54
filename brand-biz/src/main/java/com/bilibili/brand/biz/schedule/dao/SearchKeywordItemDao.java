package com.bilibili.brand.biz.schedule.dao;

import com.bilibili.brand.biz.schedule.po.SearchKeywordItemPo;
import com.bilibili.brand.biz.schedule.po.SearchKeywordItemPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface SearchKeywordItemDao {
    long countByExample(SearchKeywordItemPoExample example);

    int deleteByExample(SearchKeywordItemPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(SearchKeywordItemPo record);

    int insertBatch(List<SearchKeywordItemPo> records);

    int insertUpdateBatch(List<SearchKeywordItemPo> records);

    int insert(SearchKeywordItemPo record);

    int insertUpdateSelective(SearchKeywordItemPo record);

    int insertSelective(SearchKeywordItemPo record);

    List<SearchKeywordItemPo> selectByExample(SearchKeywordItemPoExample example);

    SearchKeywordItemPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") SearchKeywordItemPo record, @Param("example") SearchKeywordItemPoExample example);

    int updateByExample(@Param("record") SearchKeywordItemPo record, @Param("example") SearchKeywordItemPoExample example);

    int updateByPrimaryKeySelective(SearchKeywordItemPo record);

    int updateByPrimaryKey(SearchKeywordItemPo record);
}