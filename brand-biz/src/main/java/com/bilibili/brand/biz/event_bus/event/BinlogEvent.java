package com.bilibili.brand.biz.event_bus.event;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/25 11:41
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BinlogEvent<T> extends BrandEvent implements Serializable {
    private static final long serialVersionUID = -1903451625572435723L;
    /**
     * 变更类型
     * ADD:insert
     * MODIFY:update
     * REMOVE:delete
     */
    @JSONField(serializeFeatures = {JSONWriter.Feature.WriteEnumsUsingName})
    private BrandEvent.ActionType actionType;
    /**
     * 数据库名
     */
    private String schema;
    /**
     * 表名
     */
    private String table;
    /**
     * 表的主键字段，如果有多个，以逗号分隔，例如："id,field1"
     */
    private String pk_names;
    /**
     * canal 同步该条数据的时间戳
     */
    private Long msec;
    /**
     * canal 内部产生的数据序列号，递增的，可用于判断消息的先后顺序
     */
    private Long seq;

    /**
     * insert 的新数据， delete 的原数据， update 的新数据
     * 对于 text 类型字段，canal 解析获取到的是 []uint8 类型，序列化时会自动 base64.encode 编码，所以在下游读取时该字段需要进行 base64.decode 解码
     */
    private T newData;

    /**
     * update 的原数据
     */
    private T oldData;
}
