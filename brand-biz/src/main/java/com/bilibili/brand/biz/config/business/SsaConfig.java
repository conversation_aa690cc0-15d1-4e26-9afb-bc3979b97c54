package com.bilibili.brand.biz.config.business;

import com.bilibili.brand.api.common.enums.SsaButtonTwistType;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/12
 */
@Component
@Getter
public class SsaConfig {

    @Value("${ssa.queue.size:7}")
    private int ssaQueueSize;

    /**
     * webp图片url
     */
    @Value("${brand.bfs.webpSuffix}")
    private String webpSuffix;

    /**
     * 闪屏实时下发webp支持最大size
     */
    @Value("${ssa.real.time.pic.max.size:600}")
    private Integer realTimeDeliveryCompressPicMaxKbSize;

    /**
     * 闪屏实时下发视频支持最大size
     */
    @Value("${ssa.real.time.video.max.size:300}")
    private Integer realTimeDeliveryVideoMaxKbSize;

    /**
     * 沉浸闪屏第二屏滑动距离
     */
    @Value("${ssa.second.page.slide.distance:60}")
    private Integer secondSlideDistance;


    /**
     * 闪屏扭一扭限定扭动方式，x轴
     * @see  SsaButtonTwistType
     */
    @Value("${ssa.twist.x.type:0}")
    private Integer twistXType;

    /**
     * 闪屏扭一扭限定扭动方式，y轴
     * @see  SsaButtonTwistType
     */
    @Value("${ssa.twist.y.type:0}")
    private Integer twistYType;

    /**
     * 闪屏扭一扭限定扭动方式，z轴
     * @see  SsaButtonTwistType
     */
    @Value("${ssa.twist.z.type:0}")
    private Integer twistZType;

    /**
     * 稿件闪屏story模板
     */
    @Value("${ssa.story.template:429}")
    private Integer ssaStoryTemplate;

    /**
     * 稿件闪屏story广告标
     */
    @Value("${ssa.story.mark:137}")
    private Integer ssaStoryMark;

    /**
     * 品牌卡片右下角引导图
     */
    @Value("${ssa.brand.card.guide.image.url:https://i0.hdslb.com/bfs/sycp/creative_img/202403/319cab3944670b04b09f9a71d48b94e4.webp}")
    private String brandCardGuideImageUrl;

    /**
     * 品牌卡片右下角引导图md5
     */
    @Value("${ssa.brand.card.guide.image.md5:df646174fb50394f98dfc47e47a60211}")
    private String brandCardGuideImageMd5;

    /**
     * 彩蛋点击小手图片url
     * 只有大彩蛋有
     */
    @Value("${ssa.click.egg.guide.image.url:https://i0.hdslb.com/bfs/sycp/creative_img/202405/df3287494737f6b7e1d6b90b0750c031.json}")
    private String clickEggGuideImageUrl;

    /**
     * 彩蛋点击小手图片md5
     */
    @Value("${ssa.click.egg.guide.image.md5:2725e6feeeae118ce5e00d0a4458f3a2}")
    private String clickEggGuideImageMd5;

    /**
     * 彩蛋点击小手引导时长
     */
    @Value("${ssa.click.egg.guide.show.duration:667}")
    private Integer clickEggGuideShowDuration;

    /**
     * 彩蛋素材1 宽度
     */
    @Value("${ssa.click.egg.big.width:100}")
    private Integer bigClickEggWidth;

    /**
     * 彩蛋素材2 宽度
     */
    @Value("${ssa.click.egg.small.width:85}")
    private Integer smallClickEggWidth;

    /**
     * 1号彩蛋 引导展示时长
     */
    @Value("${ssa.click.egg.guide.first.show.time:500}")
    private Integer firstClickEggGuideShowTime;

    /**
     * 2号彩蛋 引导展示时长
     */
    @Value("${ssa.click.egg.guide.first.show.time:1500}")
    private Integer secondClickEggGuideShowTime;

    /**
     * ======================
     * 惊喜气球参数
     * 点击彩蛋-惊喜气球引导小手展示时间
     */
    @Value("${ssa.click.egg.balloon.guide.show.time:1667}")
    private Integer balloonGuideShowTime;

    /**
     * 点击彩蛋-惊喜气球引导小手URL
     */
    @Value("${ssa.click.egg.balloon.guide.image.url:https://i0.hdslb.com/bfs/sycp/creative_img/202406/056f9e7846d3f454777fc71a9cd3ed04.json}")
    private String balloonGuideImageUrl;

    /**
     * 点击彩蛋-惊喜气球引导小手MD5
     */
    @Value("${ssa.click.egg.balloon.guide.image.md5:44f90c1e67251258b80e0e85bcd0963e}")
    private String balloonGuideImageMd5;

    /**
     * 点击彩蛋-惊喜气球顶部气球素材URL
     */
    @Value("${ssa.click.egg.balloon.image.url:https://i0.hdslb.com/bfs/sycp/creative_img/202406/b940fcfaf54e33e2b76a20c24a150969.png}")
    private String balloonImageUrl;

    /**
     * 点击彩蛋-惊喜气球顶部气球素材MD5
     */
    @Value("${ssa.click.egg.balloon.image.md5:a30b49889112802df40a01851c130474}")
    private String balloonImageMd5;

    @Value("${ssa.cpt.split.time.inventory:20000}")
    private Integer ssaCptSplitTimeCpm;

    @Value("${ssa.cpt.split.time.each.hour.percent:26,27,27,20}")
    private List<Integer> ssaCptSplitTimeEachHourPercentageList;

    @Value("${ssa.cpt.first.brush.inventory.gap:10}")
    private Integer ssaCptFirstBrushInventoryGapCpm;

    //https://www.tapd.cn/67874887/prong/stories/view/1167874887004394974
    //maxSize=15M，maxDuration=10s
    @Value("${ssa.auto.continue.play.video.max.size:10485760}")
    private Integer autoContinuePlayVideoMaxSize;

    @Value("${ssa.auto.continue.play.video.max.duration:15}")
    private Integer autoContinuePlayVideoMaxDuration;

}
