package com.bilibili.brand.biz.rpc.grpc.server;

import com.bapis.ad.brand.Pageable;
import com.bapis.ad.brand.Responser;
import com.bapis.ad.brand.operation.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.operation.ContractAccountConfigDto;
import com.bilibili.brand.api.operation.IContractAccountConfigService;
import com.bilibili.brand.biz.converter.GrpcCommonConverter;
import com.bilibili.brand.biz.rpc.converter.OperationConverter;
import com.google.protobuf.util.JsonFormat;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import pleiades.venus.starter.rpc.server.RPCService;

/**
 * <AUTHOR>
 * @date 2024/1/9
 */
@Slf4j
@RPCService
public class BrandOperationGrpcServiceImpl extends BrandOperationServiceGrpc.BrandOperationServiceImplBase {

    @Autowired
    private IContractAccountConfigService contractAccountConfigService;

    @Override
    public void getContractAccountConfigList(GetContractAccountConfigListReq request, StreamObserver<GetContractAccountConfigListRes> responseObserver) {
        GetContractAccountConfigListRes.Builder resBuilder = GetContractAccountConfigListRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            PageResult<ContractAccountConfigDto> pageResult = contractAccountConfigService.queryConfigList(request.getContractAccountIdsList(),
                    request.getPageable().getPageIndex(),
                    request.getPageable().getPageSize());

            resBuilder.addAllConfigList(OperationConverter.MAPPER.toContractAccountConfig(pageResult.getRecords()))
                    .setPageable(Pageable.newBuilder()
                            .setTotalCount(pageResult.getTotal())
                            .build())
                    .build();
        } catch (IllegalArgumentException e) {
            log.error("[BrandOperationGrpcServiceImpl] getContractAccountConfigList error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandOperationGrpcServiceImpl] getContractAccountConfigList error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void addContractAccountConfig(AddContractAccountConfigReq request, StreamObserver<AddContractAccountConfigRes> responseObserver) {
        AddContractAccountConfigRes.Builder resBuilder = AddContractAccountConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            contractAccountConfigService.addConfig(OperationConverter.MAPPER.toAddContractAccountConfigDto(request), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandOperationGrpcServiceImpl] addContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandOperationGrpcServiceImpl] addContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void updateContractAccountConfig(UpdateContractAccountConfigReq request, StreamObserver<UpdateContractAccountConfigRes> responseObserver) {
        UpdateContractAccountConfigRes.Builder resBuilder = UpdateContractAccountConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            contractAccountConfigService.updateConfig(OperationConverter.MAPPER.toUpdateContractAccountConfigDto(request), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandOperationGrpcServiceImpl] updateContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandOperationGrpcServiceImpl] updateContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void deleteContractAccountConfig(DeleteContractAccountConfigReq request, StreamObserver<DeleteContractAccountConfigRes> responseObserver) {
        DeleteContractAccountConfigRes.Builder resBuilder = DeleteContractAccountConfigRes.newBuilder();
        Responser.Builder responserBuilder = resBuilder.getResponserBuilder();
        Operator operator = GrpcCommonConverter.MAPPER.toOperator(request.getRequester());
        String reqJson = null;
        try {
            reqJson = JsonFormat.printer().print(request);
            contractAccountConfigService.deleteConfig(request.getContractAccountId(), operator);
        } catch (IllegalArgumentException e) {
            log.error("[BrandOperationGrpcServiceImpl] deleteContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(1);
            responserBuilder.setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("[BrandOperationGrpcServiceImpl] deleteContractAccountConfig error,request={}", reqJson, e);
            responserBuilder.setCode(2);
            responserBuilder.setMsg("internal error");
        } finally {
            responseObserver.onNext(resBuilder.build());
            responseObserver.onCompleted();
        }
    }
}
