package com.bilibili.brand.biz.material;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.material.IIPVideoService;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.api.material.bo.VideoCompoundBo;
import com.bilibili.brand.biz.creative.dao.GdCreativeIpVideoDao;
import com.bilibili.brand.biz.creative.po.GdCreativeIpVideoPo;
import com.bilibili.brand.biz.creative.po.GdCreativeIpVideoPoExample;
import com.bilibili.brand.biz.material.Dto.CompoundVideoReq;
import com.bilibili.brand.biz.material.Dto.CompoundVideoRes;
import com.bilibili.cpt.platform.biz.enumerate.IpVideoTypeEnum;
import com.bilibili.cpt.platform.biz.utils.ExampleUtils;
import com.bilibili.cpt.platform.common.VideoDealStatus;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description ip视频服务
 * <AUTHOR>
 * @Date 2021.06.11 12:04
 */
@Slf4j
@Service
public class IPVideoService implements IIPVideoService {

    @Value("${ip.video.compound.url:http://bvcflow-driver3.bilibili.co/?r=state&method=init}")
    private String compoundUrl;

    @Value("${ip.video.compound.profile:ssainline/mask}")
    private String profile;

    @Value("${ip.video.compound.http.pre:https://upos-sz-static.bilivideo.com/}")
    private String httpPre;

    private static final String UPOS = "upos://";

    @Autowired
    private GdCreativeIpVideoDao ipVideoDao;

    @Override
    public PageResult<IPVideoBo> getIPVideoBoList(String name, Integer page,
                                                  Integer size, Integer dealStatus,
                                                  Integer width, Integer height, List<Integer> videoTypeList,
                                                  Long maxSize, Long maxDuration, boolean strictMatch) {
        GdCreativeIpVideoPoExample poExample = new GdCreativeIpVideoPoExample();
        GdCreativeIpVideoPoExample.Criteria criteria = poExample.or();
        if (!StringUtils.isEmpty(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        ExampleUtils.notNull(dealStatus, criteria::andDealStatusEqualTo);
        if (strictMatch) {
            ExampleUtils.notNull(width, criteria::andWidthEqualTo);
            ExampleUtils.notNull(height, criteria::andHeightEqualTo);
        }
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (!CollectionUtils.isEmpty(videoTypeList)) {
            criteria.andVideoTypeIn(videoTypeList);
        }
        if (maxSize != null){
            criteria.andSizeLessThanOrEqualTo(maxSize);
        }
        if (maxDuration != null) {
            criteria.andDurationLessThanOrEqualTo(maxDuration.intValue());
        }
        long count = ipVideoDao.countByExample(poExample);
        if(count == 0){
            return PageResult.emptyPageResult();
        }

        Page pageBean = Page.valueOf(page, size);
        poExample.setOrderByClause("mtime desc");
        poExample.setLimit(pageBean.getLimit());
        poExample.setOffset(pageBean.getOffset());
        List<GdCreativeIpVideoPo> ipVideoPos = ipVideoDao.selectByExample(poExample);

        return PageResult.<IPVideoBo>builder().records(ipVideoPos.stream().map(t->{
            IPVideoBo videoBo = new IPVideoBo();
            BeanUtils.copyProperties(t, videoBo);
            videoBo.setVideoUrl(t.getCompoundVideoUrl().replace(UPOS, httpPre));
            videoBo.setVideoMd5(t.getCompoundVideoMd5());
            videoBo.setVideoName(t.getName());
            videoBo.setId(t.getId());
            return videoBo;
        }).collect(Collectors.toList())).total(Math.toIntExact(count)).build();
    }

    @Override
    public void compoundIPVideo(VideoCompoundBo videoCompoundBo) {

        CompoundVideoReq req = buildCompoundReq(videoCompoundBo);

        CompoundVideoRes res = sendCompoundVideoReq(req);

        //视频云的回调写在ssa-portal里面
        Assert.isTrue(1 == res.getOk(), "合成视频上传失败");

        doInsert(res.getFlowid(), videoCompoundBo);
    }

    private void doInsert(String flowId, VideoCompoundBo videoCompoundBo) {
        ipVideoDao.insertSelective(GdCreativeIpVideoPo.builder()
                .flowId(flowId)
                .name(videoCompoundBo.getName())
                .alphaVideoUrl(videoCompoundBo.getAlphaVideoUrl())
                .rgbVideoUrl(videoCompoundBo.getRgbVideoUrl())
                .videoType(videoCompoundBo.getVideoType())
                .bgVideoPngSequencesUrl(videoCompoundBo.getBgVideoPngSequencesUrl())
                .outBoxVideoPngSequencesUrl(videoCompoundBo.getOutBoxVideoPngSequencesUrl())
                .dealStatus(VideoDealStatus.INIT.getCode())
                .build());
    }

    private CompoundVideoReq buildCompoundReq(VideoCompoundBo videoCompoundBo) {
        Function<String, String> replaceUrlPrefix = url -> url.replace(httpPre, UPOS);

        CompoundVideoReq req = CompoundVideoReq.builder()
                .profile(profile)
                .build();
        Integer videoType = videoCompoundBo.getVideoType();
        String ctxCfileUrl;
        String ctxMaskFileUrl;
        if (videoType == null || videoType == IpVideoTypeEnum.NORMAL.getCode()) {
            ctxCfileUrl = videoCompoundBo.getRgbVideoUrl();
            ctxMaskFileUrl = videoCompoundBo.getAlphaVideoUrl();
        } else {
            ctxCfileUrl = videoCompoundBo.getBgVideoPngSequencesUrl();
            ctxMaskFileUrl = videoCompoundBo.getOutBoxVideoPngSequencesUrl();
        }

        req.setCtx_cfile_url(replaceUrlPrefix.apply(ctxCfileUrl));
        req.setCtx_mask_file_url(replaceUrlPrefix.apply(ctxMaskFileUrl));

        return req;
    }

    private CompoundVideoRes sendCompoundVideoReq(CompoundVideoReq req) {
        // https://info.bilibili.co/pages/viewpage.action?pageId=458876039
        log.info("请求视频云合成ip视频，req:[{}]", req);
        CompoundVideoRes res = OkHttpUtils.bodyPost(compoundUrl)
                .bean(req)
                .callForObject(CompoundVideoRes.class);
        log.info("请求视频云合成ip视频，res:[{}]", res);
        return res;
    }

    @Override
    public void removeIPVideo(Integer id) {
        Assert.notNull(id, "请输入视频id");
        GdCreativeIpVideoPo videoPo = ipVideoDao.selectByPrimaryKey(id);
        Assert.notNull(videoPo, "您要删除的视频信息不存在");

        ipVideoDao.updateByPrimaryKeySelective(GdCreativeIpVideoPo.builder().id(id)
                .isDeleted(IsDeleted.DELETED.getCode()).build());
    }

    @Override
    public IPVideoBo getIPVideoById(Integer id) {
        Assert.notNull(id, "ip视频id不能为空");
        GdCreativeIpVideoPo t = ipVideoDao.selectByPrimaryKey(id);
        Assert.notNull(t, "根据"+ id + "查找不到对应的ip视频");

        IPVideoBo videoBo = new IPVideoBo();
        BeanUtils.copyProperties(t, videoBo);
        videoBo.setVideoUrl(t.getCompoundVideoUrl().replace(UPOS, httpPre));
        videoBo.setBeforeUrl(t.getCompoundVideoUrl());
        videoBo.setVideoMd5(t.getCompoundVideoMd5());
        videoBo.setVideoName(t.getName());
        videoBo.setIpVideoType(t.getVideoType());
        return videoBo;
    }

}
