package com.bilibili.brand.biz.account.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountLabelMappingPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 标签id
     */
    private Integer labelId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}