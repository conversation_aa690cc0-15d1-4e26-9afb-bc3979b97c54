package com.bilibili.brand.biz.creative.service;

import com.alibaba.fastjson.JSONArray;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.creative.dto.CreativeDrawGestureDto;
import com.bilibili.brand.api.operation.ContractAccountDrawGestureConfigDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.resource.platform.IPlatformService;
import com.bilibili.brand.api.resource.platform.MgkVideoDto;
import com.bilibili.brand.biz.converter.BrandCreativeConverter;
import com.bilibili.brand.biz.schedule.service.ContractConfigHandler;
import com.bilibili.cpt.platform.api.creative.dto.MgkVideoInfoDto;
import com.bilibili.cpt.platform.biz.dao.CreativeDrawGestureDao;
import com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePo;
import com.bilibili.cpt.platform.biz.po.CreativeDrawGesturePoExample;
import com.bilibili.enums.GdInteractStyleEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.enums.TemplatePropertyEnum;
import com.bilibili.ssa.platform.common.enums.GdDrawDirectionEnum;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CreativeDrawGestureService {

    @Autowired
    private IPlatformService platformService;

    @Autowired
    private CreativeDrawGestureDao creativeDrawGestureDao;

    @Autowired
    private ContractConfigHandler contractConfigHandler;

    @Value("${creative.draw.gesture.dur.time:3000}")
    private Integer durTime;

    /**
     * 校验滑动信息是否合法
     *
     * @param context 上下文
     */
    public boolean validate(SaveCreativeDrawGestureContext context) {
        // 模板是否支持滑动
        Integer templateId = context.getTemplateId();
        TemplatePropertyEnum templateProperty = TemplatePropertyEnum.getByTemplateIdWithoutEx(templateId);
        if (!templateProperty.getOptions().isSupportDrawGesture()) {
            return false;
        }
        // 模板是否选择滑动
        Integer interactStyle = context.getInteractStyle();
        GdInteractStyleEnum interactStyleEnum = GdInteractStyleEnum.getByCode(interactStyle);
        if (!Objects.equals(interactStyleEnum, GdInteractStyleEnum.DRAW_GESTURE)) {
            return false;
        }
        // 选择滑动校验手势滑动信息
        CreativeDrawGestureDto drawGesture = context.getDrawGesture();
        Assert.notNull(drawGesture, "手势滑动信息不能为空");
        // 校验【手势展示开始时间点】，针对视频卡比图文卡多一个时间范围校验
        List<Long> activeTimes = drawGesture.getActiveTimes();
        Assert.notEmpty(activeTimes, "手势展示开始时间点不能为空。");
        Integer mgkVideoId = Optional.ofNullable(context.getMgkVideo()).map(MgkVideoInfoDto::getMgkVideoId).orElse(null);
        if (Utils.isPositive(mgkVideoId)) {
            MgkVideoDto mgkVideoDto = platformService.getMgkVideoDto(mgkVideoId);
            Assert.notNull(mgkVideoDto, "素材视频不存在，请检查");
            activeTimes.forEach(activeTime -> Assert.isTrue(activeTime >= 0
                    && activeTime <= mgkVideoDto.getDuration(), String.format("手势展示开始时间点允许填写的时间范围为【0ms-%dms】", mgkVideoDto.getDuration())));
        }
        for (int i = 1; i < activeTimes.size(); i++) {
            Long preActiveTime = activeTimes.get(i - 1);
            Long activeTime = activeTimes.get(i);
            Assert.isTrue(activeTime - preActiveTime >= 6000, "2个滑动交互手势引导提示的时间间隔必须大于等于【6000ms】，请修改后重新保存。");
        }
        return true;
    }

    /**
     * 保存滑动信息
     *
     * @param context 上下文
     */
    public void saveCreativeDrawGesture(SaveCreativeDrawGestureContext context) {
        // 未选择手势滑动信息，删除手势滑动数据，可能是创建时先选择手势滑动，更新时选择扭一扭
        if (!this.validate(context)) {
            CreativeDrawGesturePoExample creativeDrawGesturePoExample = new CreativeDrawGesturePoExample();
            creativeDrawGesturePoExample.createCriteria()
                    .andCreativeIdEqualTo(context.creativeId)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            CreativeDrawGesturePo updated = CreativeDrawGesturePo.builder()
                    .creativeId(context.creativeId)
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build();
            creativeDrawGestureDao.updateByExampleSelective(updated, creativeDrawGesturePoExample);
            return;
        }

        Integer templateId = context.getTemplateId();
        TemplatePropertyEnum templateProperty = TemplatePropertyEnum.getByTemplateIdWithoutEx(templateId);


        // 计算相关信息并保存
        GdOrderDto gdOrderDto = context.getGdOrderDto();
        CreativeDrawGestureDto drawGesture = context.getDrawGesture();
        CreativeDrawGesturePo creativeDrawGesturePo = BrandCreativeConverter.MAPPER.toCreativeDrawGesturePo(drawGesture);

        // 角度长度通过客户配置计算而来
        ContractAccountDrawGestureConfigDto defaultDrawGestureConfig = ContractAccountDrawGestureConfigDto.builder()
                .drawLength(100)
                .drawAngle(90)
                .build();
        Map<Integer, ContractAccountDrawGestureConfigDto> drawGestureConfig =
                contractConfigHandler.getGdDrawGestureConfig(gdOrderDto.getContractAccountId());
        ContractAccountDrawGestureConfigDto iphoneDrawGestureConfig = drawGestureConfig.getOrDefault(PlatformType.IPHONE.getCode(), defaultDrawGestureConfig);
        Assert.notNull(iphoneDrawGestureConfig, "未配置iphone手机滑动交互的客户配置信息，请联系相关人员配置，合同账号ID：" + gdOrderDto.getContractAccountId());
        ContractAccountDrawGestureConfigDto androidDrawGestureConfig = drawGestureConfig.getOrDefault(PlatformType.ANDROID.getCode(), defaultDrawGestureConfig);
        Assert.notNull(androidDrawGestureConfig, "未配置android手机滑动交互的客户配置信息，请联系相关人员配置，合同账号ID：" + gdOrderDto.getContractAccountId());
        creativeDrawGesturePo.setDrawDirection(drawGesture.getDrawDirection().getCode());

        Integer iosAngle = Objects.nonNull(iphoneDrawGestureConfig.getDrawAngle()) ? iphoneDrawGestureConfig.getDrawAngle() : defaultDrawGestureConfig.getDrawAngle();
        Integer iosDrawLength = Objects.nonNull(iphoneDrawGestureConfig.getDrawLength()) ? iphoneDrawGestureConfig.getDrawLength() : defaultDrawGestureConfig.getDrawLength();
        creativeDrawGesturePo.setIosStartAngle(calcStartAngle(drawGesture.getDrawDirection(), iosAngle));
        creativeDrawGesturePo.setIosEndAngle(calcEndAngle(drawGesture.getDrawDirection(), iosAngle));
        creativeDrawGesturePo.setIosDrawLength(iosDrawLength);

        Integer androidAngle = Objects.nonNull(androidDrawGestureConfig.getDrawAngle()) ? androidDrawGestureConfig.getDrawAngle() : defaultDrawGestureConfig.getDrawAngle();
        Integer androidDrawLength = Objects.nonNull(androidDrawGestureConfig.getDrawLength()) ? androidDrawGestureConfig.getDrawLength() : defaultDrawGestureConfig.getDrawLength();
        creativeDrawGesturePo.setAndroidStartAngle(calcStartAngle(drawGesture.getDrawDirection(), androidAngle));
        creativeDrawGesturePo.setAndroidEndAngle(calcEndAngle(drawGesture.getDrawDirection(), androidAngle));
        creativeDrawGesturePo.setAndroidDrawLength(androidDrawLength);

        // 手势引导动效时长投放端写死3000ms
        creativeDrawGesturePo.setDurTime(durTime);
        creativeDrawGesturePo.setActiveTimes(JSONArray.toJSONString(drawGesture.getActiveTimes()));
        creativeDrawGesturePo.setActionType(templateProperty.getOptions().getDrawGestureActionType().getCode());


        // 判断是更新还是新增
        List<CreativeDrawGesturePo> creativeDrawGesturePoList = this.queryCreativeDrawGesture(Lists.newArrayList(context.getCreativeId()));
        if (CollectionUtils.isEmpty(creativeDrawGesturePoList)) {
            creativeDrawGestureDao.insertSelective(creativeDrawGesturePo);
        } else {
            Long id = creativeDrawGesturePoList.get(0).getId();
            creativeDrawGesturePo.setId(id);
            creativeDrawGestureDao.updateByPrimaryKeySelective(creativeDrawGesturePo);
        }
    }

    /**
     * 通过创意id查询滑动信息
     *
     * @param creativeIds 创意id列表
     */
    public List<CreativeDrawGesturePo> queryCreativeDrawGesture(List<Long> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return new ArrayList<>();
        }
        CreativeDrawGesturePoExample creativeDrawGesturePoExample = new CreativeDrawGesturePoExample();
        creativeDrawGesturePoExample.createCriteria()
                .andCreativeIdIn(creativeIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return creativeDrawGestureDao.selectByExample(creativeDrawGesturePoExample);
    }

    /**
     * 通过创意id查询滑动信息
     *
     * @param creativeIds 创意id列表
     */
    public Map<Long, CreativeDrawGestureDto> getCreativeDrawGestures(List<Long> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return new HashMap<>();
        }

        List<CreativeDrawGesturePo> creativeDrawGesturePoList = queryCreativeDrawGesture(creativeIds);
        return creativeDrawGesturePoList.stream()
                .map(po -> {
                    GdDrawDirectionEnum drawDirectionEnum = GdDrawDirectionEnum.getByCode(po.getDrawDirection());
                    CreativeDrawGestureDto dto = BrandCreativeConverter.MAPPER.toCreativeDrawGestureDto(po);
                    dto.setActiveTimes(JSONArray.parseArray(po.getActiveTimes(), Long.class));
                    dto.setDrawDirection(drawDirectionEnum);
                    dto.setIosDrawAngle(revCalcAngle(drawDirectionEnum, po.getIosStartAngle(), po.getIosEndAngle()));
                    dto.setIosDrawLength(po.getIosDrawLength());
                    dto.setAndroidDrawAngle(revCalcAngle(drawDirectionEnum, po.getAndroidStartAngle(), po.getAndroidEndAngle()));
                    dto.setAndroidDrawLength(po.getAndroidDrawLength());
                    return dto;
                })
                .collect(Collectors.toMap(CreativeDrawGestureDto::getCreativeId, Function.identity(), (v1, v2) -> v1));
    }


    /**
     * 运营后台刷手势滑动配置信息
     */
    @Transactional(rollbackOn = Exception.class)
    public void refreshDrawGestureConfig(List<Long> createIdList, Integer platformType, Integer drawAngle, Integer drawLength) {
        log.info("CreativeDrawGestureService refreshDrawGestureConfig，createIdList：{}，platformType：{}，drawAngle：{}，drawLength：{}", createIdList, platformType, drawAngle, drawLength);
        if (CollectionUtils.isEmpty(createIdList)) {
            return;
        }
        CreativeDrawGesturePoExample creativeDrawGesturePoExample = new CreativeDrawGesturePoExample();
        creativeDrawGesturePoExample.createCriteria()
                .andCreativeIdIn(createIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CreativeDrawGesturePo> creativeDrawGesturePoList = creativeDrawGestureDao.selectByExample(creativeDrawGesturePoExample);
        if (CollectionUtils.isEmpty(creativeDrawGesturePoList)) {
            return;
        }

        if (PlatformType.isIos(platformType)) {
            creativeDrawGesturePoList.forEach(po -> {
                GdDrawDirectionEnum drawDirectionEnum = GdDrawDirectionEnum.getByCode(po.getDrawDirection());
                if (Objects.nonNull(drawLength)) {
                    po.setIosDrawLength(drawLength);
                }
                if (Objects.nonNull(drawAngle)) {
                    po.setIosStartAngle(calcStartAngle(drawDirectionEnum, drawAngle));
                    po.setIosEndAngle(calcEndAngle(drawDirectionEnum, drawAngle));
                }
            });
        }

        if (PlatformType.isAndroid(platformType)) {
            creativeDrawGesturePoList.forEach(po -> {
                GdDrawDirectionEnum drawDirectionEnum = GdDrawDirectionEnum.getByCode(po.getDrawDirection());
                if (Objects.nonNull(drawLength)) {
                    po.setAndroidDrawLength(drawLength);
                }
                if (Objects.nonNull(drawAngle)) {
                    po.setAndroidStartAngle(calcStartAngle(drawDirectionEnum, drawAngle));
                    po.setAndroidEndAngle(calcEndAngle(drawDirectionEnum, drawAngle));
                }
            });
        }

        creativeDrawGesturePoList.forEach(po -> creativeDrawGestureDao.updateByPrimaryKeySelective(po));
    }

    /**
     * 计算起始角度信息
     */
    private Integer calcStartAngle(GdDrawDirectionEnum drawDirection, Integer angle) {
        // 无方向默认全向，起始角度0度，结束角度360度
        if (Objects.equals(drawDirection, GdDrawDirectionEnum.NO_DIRECTION)) {
            return 0;
        }
        // 带方向需要跟据方向和角度值计算起始角度和结束角度
        Integer midAngle = drawDirection.getMidAngle();
        return normalizeAngle(midAngle - angle);
    }

    /**
     * 计算结束角度信息
     */
    private Integer calcEndAngle(GdDrawDirectionEnum drawDirection, Integer angle) {
        // 无方向默认全向，起始角度0度，结束角度360度
        if (Objects.equals(drawDirection, GdDrawDirectionEnum.NO_DIRECTION)) {
            return 360;
        }
        // 带方向需要跟据方向和角度值计算起始角度和结束角度
        Integer midAngle = drawDirection.getMidAngle();
        return normalizeAngle(midAngle + angle);
    }

    /**
     * 反向计算角度信息
     */
    private Integer revCalcAngle(GdDrawDirectionEnum drawDirectionEnum, Integer startAngle, Integer endAngle) {
        // 无方向默认全向，起始角度0度，结束角度360度
        if (Objects.equals(drawDirectionEnum, GdDrawDirectionEnum.NO_DIRECTION)) {
            return 180;
        }
        Integer midAngle = drawDirectionEnum.getMidAngle();
        return (endAngle - midAngle + 360) % 360;
    }

    /**
     * 规范化角度信息【0，360】左闭右闭区间，方便人理解，虽然0度和360度概念上是等价的
     */
    public int normalizeAngle(int angle) {
        int normalizedAngle = angle % 360;
        if (normalizedAngle < 0) {
            normalizedAngle += 360; // 调整负值到正确的区间
        }
        return (normalizedAngle == 0 && angle >= 360) ? 360 : normalizedAngle;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SaveCreativeDrawGestureContext {
        private Long creativeId;
        private Integer templateId;
        private Long videoId;
        private Integer interactStyle;
        private GdOrderDto gdOrderDto;
        private CreativeDrawGestureDto drawGesture;
        private MgkVideoInfoDto mgkVideo;
    }
}
