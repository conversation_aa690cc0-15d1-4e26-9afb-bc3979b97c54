package com.bilibili.brand.biz.creative.dao;

import com.bilibili.brand.biz.creative.po.CreativeTwistPo;
import com.bilibili.brand.biz.creative.po.CreativeTwistPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CreativeTwistDao {
    long countByExample(CreativeTwistPoExample example);

    int deleteByExample(CreativeTwistPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CreativeTwistPo record);

    int insertBatch(List<CreativeTwistPo> records);

    int insertUpdateBatch(List<CreativeTwistPo> records);

    int insert(CreativeTwistPo record);

    int insertUpdateSelective(CreativeTwistPo record);

    int insertSelective(CreativeTwistPo record);

    List<CreativeTwistPo> selectByExample(CreativeTwistPoExample example);

    CreativeTwistPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CreativeTwistPo record, @Param("example") CreativeTwistPoExample example);

    int updateByExample(@Param("record") CreativeTwistPo record, @Param("example") CreativeTwistPoExample example);

    int updateByPrimaryKeySelective(CreativeTwistPo record);

    int updateByPrimaryKey(CreativeTwistPo record);
}