package com.bilibili.brand.biz.schedule.handler;

import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/30
 **/
@Slf4j
public abstract class AccountPriceRaiseHandler implements IPriceRaiseHandler{

    @Resource
    protected ISystemConfigService systemConfigService;

    @Resource
    protected GdOrderService orderService;
    @Resource
    protected ISoaCrmContractService soaCrmContractService;

    public abstract String getConfigKey();

    protected List<String> getConfigList(String configKey) {
        String value = systemConfigService.getValue(configKey);
        if (!StringUtils.isEmpty(value)) {
            return Arrays.stream(value.split(",")).map(String::trim).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    protected String getConfig(String configKey) {
        return systemConfigService.getValue(configKey);
    }

    protected Integer getAccountId(Integer orderId) {
        GdOrderDto order = orderService.getOrderById(orderId);
        Assert.notNull(order, String.format("订单「%s」不存在", orderId));
        // 查询合同对应最新的账户id
        Integer crmContractId = order.getCrmContractId();
        ContractDto contract = soaCrmContractService.getContractById(crmContractId);
        Assert.notNull(contract, String.format("合同「%s」不存在", crmContractId));
        Integer accountId = contract.getAccountId();
        log.info("合同「{}」对应的账户为「{}」", crmContractId, accountId);
        return accountId;
    }

    @Override
    public Integer preCheck(Integer raiseRatio) {
        if (Objects.isNull(raiseRatio)) {
            raiseRatio = Integer.valueOf(getConfig(getRaiseRatioConfigKey()));
        }
        if (raiseRatio <= 0) {
            return null;
        }
        return raiseRatio;
    }

    @Override
    public Integer handleRaise(Integer orderId, List<String> launchDates, Integer raiseRatio) {
        raiseRatio = preCheck(raiseRatio);
        if (raiseRatio == null) return 0;
        if (needRaise(getAccountId(orderId), launchDates)) {
            return raiseRatio;
        }
        return 0;
    }

    @Override
    public Integer handleRaise(Integer orderId, String launchDate, Integer raiseRatio) {
        return handleRaise(orderId, Collections.singletonList(launchDate), raiseRatio);
    }

    @Override
    public boolean needRaise(Object key) {
        List<String> whiteCustomer = getConfigList(getConfigKey());
        if (whiteCustomer.stream().anyMatch(customerId -> Objects.equals(key.toString(), customerId))) {
            // 白名单客户不需要加收
            log.info("白名单客户「{}」跳过加收", key);
            return false;
        }
        return true;
    }

    @Override
    public Integer handleRaise(Integer orderId, Integer raiseRatio) {
        raiseRatio = preCheck(raiseRatio);
        if (raiseRatio == null) return 0;
        if (needRaise(getAccountId(orderId))) {
            return raiseRatio;
        }
        return 0;
    }

    @Override
    public boolean needSkipRaise(Integer orderId) {
        Integer accountId = getAccountId(orderId);
        return !needRaise(accountId);
    }
}
