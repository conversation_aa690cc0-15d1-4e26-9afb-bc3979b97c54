package com.bilibili.brand.biz.event_bus.event;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.annotation.JSONField;
import com.bilibili.brand.api.resource.bluekeyword.BlueKeywordConfigItemDto;
import com.bilibili.brand.biz.event_bus.BrandEvent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 品牌小蓝词信息变更事件
 *
 * <AUTHOR>
 * @date 2024/5/12 16:13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlueKeywordConfigChangeEvent extends BrandEvent implements Serializable {
    private static final long serialVersionUID = 1004515962801550847L;
    /**
     * 配置id
     */
    private Long configId;
    /**
     * 变更前的明细
     */
    private BlueKeywordConfigItemDto oldItem;
    /**
     * 变更类型
     */
    @JSONField(serializeFeatures = {JSONWriter.Feature.WriteEnumsUsingName})
    private BrandEvent.ActionType actionType;
}
