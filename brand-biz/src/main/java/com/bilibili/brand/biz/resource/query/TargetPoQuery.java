package com.bilibili.brand.biz.resource.query;

import java.util.Collection;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/10/9.
 *
 * <AUTHOR>
 */
public class TargetPoQuery {

    private Collection<Integer> targetTypeList;

    private Collection<Integer> statusList;

    private Boolean isDeleted;

    public Collection<Integer> getTargetTypeList() {
        return targetTypeList;
    }

    public TargetPoQuery setTargetTypeList(Collection<Integer> targetTypeList) {
        this.targetTypeList = targetTypeList;
        return this;
    }

    public Collection<Integer> getStatusList() {
        return statusList;
    }

    public TargetPoQuery setStatusList(Collection<Integer> statusList) {
        this.statusList = statusList;
        return this;
    }

    public Boolean getDeleted() {
        return isDeleted;
    }

    public TargetPoQuery setDeleted(Boolean deleted) {
        isDeleted = deleted;
        return this;
    }
}
