package com.bilibili.brand.biz.schedule.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.schedule.dto.LauAccountCorpMidMappingDto;
import com.bilibili.brand.api.schedule.service.IBrandInfoService;
import com.bilibili.brand.biz.schedule.dao.LauAccountCorpMidMappingDao;
import com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPo;
import com.bilibili.brand.biz.schedule.po.LauAccountCorpMidMappingPoExample;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021.03.03 19:04
 */
@Service
public class BrandInfoService implements IBrandInfoService {

    @Autowired
    private LauAccountCorpMidMappingDao midMappingDao;

    @Override
    public List<LauAccountCorpMidMappingDto> getAccountCorp(Integer accountId) {
        LauAccountCorpMidMappingPoExample example = new LauAccountCorpMidMappingPoExample();
        example.or().andAccountIdEqualTo(accountId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("mtime desc");

        List<LauAccountCorpMidMappingPo> mappingPoList = midMappingDao.selectByExample(example);
        if(mappingPoList == null){
            return null;
        }
        return mappingPoList.stream().map(t-> {
            LauAccountCorpMidMappingDto dto = new LauAccountCorpMidMappingDto();
            BeanUtils.copyProperties(t, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public LauAccountCorpMidMappingDto getBrandInfo(Long mid, Integer accountId) throws ServiceException {
        if(mid == 0){
            return new LauAccountCorpMidMappingDto();
        }
        LauAccountCorpMidMappingPoExample example = new LauAccountCorpMidMappingPoExample();
        example.or().andMidEqualTo(mid).andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId);
        List<LauAccountCorpMidMappingPo> mappingPoList = midMappingDao.selectByExample(example);
        if(CollectionUtils.isEmpty(mappingPoList)){
            throw new ServiceException("不存在" + mid + "对应的品牌信息");
        }

        LauAccountCorpMidMappingDto dto = new LauAccountCorpMidMappingDto();
        BeanUtils.copyProperties(mappingPoList.get(0), dto);
        return dto;
    }

    @Override
    public Map<Long, LauAccountCorpMidMappingDto> getBrandInfos(List<Long> mids, Integer accountId) {
        if(CollectionUtils.isEmpty(mids)){
            return new HashMap<>();
        }
        LauAccountCorpMidMappingPoExample example = new LauAccountCorpMidMappingPoExample();
        example.or().andMidIn(mids).andAccountIdEqualTo(accountId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauAccountCorpMidMappingPo> mappingPoList = midMappingDao.selectByExample(example);
        if(mappingPoList == null){
            return new HashMap<>();
        }

        Map<Long, LauAccountCorpMidMappingDto> res = new HashMap<>();
        for(LauAccountCorpMidMappingPo po : mappingPoList){
            LauAccountCorpMidMappingDto dto = new LauAccountCorpMidMappingDto();
            BeanUtils.copyProperties(po, dto);
            res.put(po.getMid(), dto);
        }
        return res;
    }

    @Override
    public Map<Long, LauAccountCorpMidMappingDto> getBrandInfos(List<Long> mids) {
        if(CollectionUtils.isEmpty(mids)){
            return new HashMap<>();
        }
        LauAccountCorpMidMappingPoExample example = new LauAccountCorpMidMappingPoExample();
        example.or().andMidIn(mids)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauAccountCorpMidMappingPo> mappingPoList = midMappingDao.selectByExample(example);
        if(mappingPoList == null){
            return new HashMap<>();
        }

        Map<Long, LauAccountCorpMidMappingDto> res = new HashMap<>();
        for(LauAccountCorpMidMappingPo po : mappingPoList){
            LauAccountCorpMidMappingDto dto = new LauAccountCorpMidMappingDto();
            BeanUtils.copyProperties(po, dto);
            res.put(po.getMid(), dto);
        }
        return res;
    }
}
