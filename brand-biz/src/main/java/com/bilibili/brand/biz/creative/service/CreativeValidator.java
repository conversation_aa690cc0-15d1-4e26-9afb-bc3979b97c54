package com.bilibili.brand.biz.creative.service;

import com.alibaba.fastjson.JSONArray;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.creative.dto.CreativeDanmakuDto;
import com.bilibili.brand.api.creative.dto.CreativeDrawGestureDto;
import com.bilibili.brand.api.creative.dto.CreativeLotteryDto;
import com.bilibili.brand.api.creative.dto.CreativeProductCarouselDto;
import com.bilibili.brand.api.ext.ILiveInfoService;
import com.bilibili.brand.api.launch.dto.CreativeIpVideoDto;
import com.bilibili.brand.api.launch.dto.EggVideoDto;
import com.bilibili.brand.api.launch.dto.ImageDto;
import com.bilibili.brand.api.material.IIPVideoService;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.api.resource.platform.IPlatformService;
import com.bilibili.brand.api.resource.platform.MgkVideoDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.cpt.platform.api.creative.dto.MgkVideoInfoDto;
import com.bilibili.cpt.platform.common.IpVideoSizeEnum;
import com.bilibili.enums.TemplatePropertyEnum;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.common.ButtonCopyTypeEnum;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.ssa.platform.common.enums.SystemConfig;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by walker on 16/9/1.
 */
@Component
public class CreativeValidator {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private IPlatformService platformService;

    @Autowired
    private IIPVideoService ipVideoService;

    @Autowired
    private CreativeLotteryService creativeLotteryService;

    @Autowired
    private CreativeDanmakuService creativeDanmakuService;

    @Autowired
    private CreativeDrawGestureService creativeDrawGestureService;

    @Autowired
    private ILiveInfoService liveInfoService;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private ConfigCenter configCenter;

    public List<Integer> getSupportCardRecallAccountIds() {
        String accountIds = systemConfigService.getValueByItem(SystemConfig.SUPPORT_CARD_RECALL_ACCOUNT_IDS);
        return JSONArray.parseArray(accountIds, Integer.class);
    }

    public void validateCreative(CreativeValidationContext context) {
        this.validateInline3DIpEggeMaterial(context);
        this.validateInlineLotteryMaterial(context);
        this.validateInlineDanmakuMaterial(context);
        this.validateDrawGestureMaterial(context);
        this.validateLiveBooking(context);
        this.validateLiveCover(context);
    }

    /**
     * 检验直播封面是否符合直播要求
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004459306">【品牌】起飞GD投放直播间支持自定义封面</a>
     *
     * @param context
     */
    private void validateLiveCover(CreativeValidationContext context) {
        ImageDto liveCover = context.getLiveCover();
        if (Objects.isNull(liveCover)) {
            //直播封面可选，顾可以不传
            return;
        }
        boolean isLiveLivingTemplate = configCenter.getLiveConfig().isSupportsLivingTemplate(context.getTemplateId());
        Assert.isTrue(isLiveLivingTemplate, "当前模板不支持直播，不允许配置直播自定义封面，模板id=" + context.getTemplateId());
    }

    /**
     * 校验直播预约id，直播预约id必须是有效的直播预约id
     * 当buttonType=6时，story资源位上确认采用直播优选卡方案，优选卡和story组件存在互斥逻辑，不可同时出现
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004443629">【品牌广告】（非闪屏资源-inline/story）品牌直播预约升级</a>
     */
    private void validateLiveBooking(CreativeValidationContext context) {
        Long liveBookingId = context.getLiveBookingId();
        if (Objects.isNull(liveBookingId) || Objects.equals(liveBookingId, 0L)) {
            return;
        }
        this.liveInfoService.validateLiveBookingIdList(Lists.newArrayList(liveBookingId));
        Integer buttonCopyId = context.getButtonCopyId();
        if (Utils.isPositive(buttonCopyId)) {
            Integer templateId = context.getTemplateId();
            TemplateDto templateDto = queryTemplateService.getTemplateById(templateId);
            Assert.notNull(templateDto, "模板不存在");

            Map<Integer, Integer> buttonIdToTypeMap = Optional.ofNullable(templateDto.getButtonCopyDtos()).orElse(new ArrayList<>())
                    .stream()
                    .collect(Collectors.toMap(ButtonCopyDto::getId, ButtonCopyDto::getType, (v1, v2) -> v1));
            Integer buttonType = buttonIdToTypeMap.get(buttonCopyId);
            Assert.notNull(buttonType, "按钮文案ID不存在");

            if (Objects.equals(buttonType, ButtonCopyTypeEnum.LIVE_BOOKING.getCode())) {
                Assert.isTrue(CollectionUtils.isEmpty(context.getComponentIds()), "直播优选卡和story组件存在互斥逻辑，不可同时出现");
            }
        }
    }

    /**
     * 校验【品牌】游戏抽卡（3D浮层彩蛋样式）
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004259274">【品牌】游戏抽卡（3D浮层彩蛋样式）-仅硬广</a>
     *
     * @param context 上下文
     */
    private void validateInline3DIpEggeMaterial(CreativeValidationContext context) {
        TemplatePropertyEnum templateProperty = TemplatePropertyEnum.getByTemplateIdWithoutEx(context.getTemplateId());
        if (!Objects.equals(templateProperty, TemplatePropertyEnum.TEMPLATE_INLINE_3D_IP_EGG_WITH_TWIST)) {
            return;
        }
        //校验【主素材和可展示时间关系】
        MgkVideoInfoDto mgkVideoInfo = context.getMgkVideo();
        MgkVideoDto mgkVideo = this.platformService.getMgkVideoDto(mgkVideoInfo.getMgkVideoId());
        Assert.notNull(mgkVideo, "彩蛋/IP浮层视频必须绑定素材视频");
        //校验 IP浮层视频
        CreativeIpVideoDto ipVideo = context.getIpVideo();
        Assert.isTrue(Utils.isPositive(ipVideo.getVideoId()), "IP浮层视频不能为空");

        IPVideoBo ipVideoBo = ipVideoService.getIPVideoById(ipVideo.getVideoId());
        Assert.notNull(ipVideoBo, "IP浮层视频不存在");
        Assert.isTrue(Objects.equals(ipVideoBo.getWidth(), IpVideoSizeEnum.INLINE_3D_IP_VIDEO.getWidth())
                        && Objects.equals(ipVideoBo.getHeight(), IpVideoSizeEnum.INLINE_3D_IP_VIDEO.getHeight()),
                "IP浮层视频尺寸必须是300*540，当前视频尺寸为" + ipVideoBo.getWidth() + "*" + ipVideoBo.getHeight());
        Assert.isTrue(ipVideoBo.getSize() <= 5 * 1000 * 1000,
                "IP浮层视频大小不能超过5MB，当前视频大小为" + ipVideoBo.getSize() + "B");

        Assert.isTrue(Values.zeroIfNull(ipVideo.getStartTime()) >= 2000, "可展示开始时间必须大于等于2s");
        Assert.isTrue(Values.zeroIfNull(ipVideo.getEndTime()) - ipVideo.getStartTime() >= ipVideoBo.getDuration(),
                "【可展示开始时间 - 可展示结束时间】区间必须大于等于IP浮层视频时长，当前视频时长为" + ipVideoBo.getDuration() + "ms");
        Assert.isTrue(ipVideo.getEndTime() <= mgkVideo.getDuration(),
                "可展示结束时间必须小于等于素材视频时长，当前素材视频时长为" + mgkVideo.getDuration() + "ms");

        //彩蛋可以为空
        //校验 彩蛋
        EggVideoDto eggVideo = context.getEggVideo();
        if (Utils.isPositive(eggVideo.getVideoId())) {
            IPVideoBo eggVideoBo = ipVideoService.getIPVideoById(eggVideo.getVideoId());
            Assert.notNull(eggVideoBo, "彩蛋视频不存在");
            Assert.isTrue(Objects.equals(eggVideoBo.getWidth(), IpVideoSizeEnum.EGG_INLINE.getWidth())
                            && Objects.equals(eggVideoBo.getHeight(), IpVideoSizeEnum.EGG_INLINE.getHeight()),
                    "彩蛋视频视频尺寸必须是1080*1920，当前视频尺寸为" + eggVideoBo.getWidth() + "*" + eggVideoBo.getHeight());
            Assert.isTrue(eggVideoBo.getSize() <= 150 * 1000 * 1000,
                    "彩蛋视频大小不能超过150MB，当前视频大小为" + eggVideoBo.getSize() + "B");
//            Assert.isTrue(eggVideoBo.getDuration() >= 3000 && eggVideoBo.getDuration() <= 8000,
//                    "彩蛋视频时长必须在3s-8s，当前视频时长为" + eggVideoBo.getDuration() + "ms");
        }
    }

    /**
     * 【品牌广告】游戏抽卡-仅硬广（技术调研）
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004269936">游戏抽卡-仅硬广</a>
     *
     * @param context 上下文
     */
    private void validateInlineLotteryMaterial(CreativeValidationContext context) {
        this.creativeLotteryService.validate(CreativeLotteryService.SaveCreativeLotteryContext.builder()
                .templateId(context.getTemplateId())
                .lottery(context.getLottery())
                .mgkVideo(context.getMgkVideo())
                .build());
    }

    /**
     * 【品牌广告】七夕连击互动样式-仅硬广
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004272197">七夕连击互动样式</a>
     *
     * @param context 上下文
     */
    private void validateInlineDanmakuMaterial(CreativeValidationContext context) {
        this.creativeDanmakuService.validate(CreativeDanmakuService.SaveCreativeDanmakuContext.builder()
                .templateId(context.getTemplateId())
                .danmaku(context.getDanmaku())
                .mgkVideo(context.getMgkVideo())
                .build());
    }

    /**
     * 【品牌广告】信息流inline-滑动互动
     * <a href="https://www.tapd.cn/********/prong/stories/view/11********004314210">...</a>
     *
     * @param context 上下文
     */
    private void validateDrawGestureMaterial(CreativeValidationContext context) {
        this.creativeDrawGestureService.validate(CreativeDrawGestureService.SaveCreativeDrawGestureContext.builder()
                .templateId(context.getTemplateId())
                .videoId(context.getVideoId())
                .interactStyle(context.getInteractStyle())
                .drawGesture(context.getDrawGesture())
                .mgkVideo(context.getMgkVideo())
                .build());
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreativeValidationContext {
        private Integer templateId;
        private Long videoId;
        private Integer interactStyle;
        private Long liveBookingId;
        private Integer buttonCopyId;
        private List<Long> componentIds;
        private CreativeIpVideoDto ipVideo;
        private EggVideoDto eggVideo;
        private MgkVideoInfoDto mgkVideo;
        private CreativeLotteryDto lottery;
        private CreativeDanmakuDto danmaku;
        private CreativeProductCarouselDto productCarousel;
        private CreativeDrawGestureDto drawGesture;
        private ImageDto liveCover;
    }
}
