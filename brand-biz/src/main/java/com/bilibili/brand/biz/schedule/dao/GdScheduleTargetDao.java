package com.bilibili.brand.biz.schedule.dao;

import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface GdScheduleTargetDao {
    long countByExample(GdScheduleTargetPoExample example);

    int deleteByExample(GdScheduleTargetPoExample example);

    int deleteByPrimaryKey(Integer scheduleTargetId);

    int insertUpdate(GdScheduleTargetPo record);

    int insertBatch(List<GdScheduleTargetPo> records);

    int insertUpdateBatch(List<GdScheduleTargetPo> records);

    int insert(GdScheduleTargetPo record);

    int insertUpdateSelective(GdScheduleTargetPo record);

    int insertSelective(GdScheduleTargetPo record);

    List<GdScheduleTargetPo> selectByExample(GdScheduleTargetPoExample example);

    GdScheduleTargetPo selectByPrimaryKey(Integer scheduleTargetId);

    int updateByExampleSelective(@Param("record") GdScheduleTargetPo record, @Param("example") GdScheduleTargetPoExample example);

    int updateByExample(@Param("record") GdScheduleTargetPo record, @Param("example") GdScheduleTargetPoExample example);

    int updateByPrimaryKeySelective(GdScheduleTargetPo record);

    int updateByPrimaryKey(GdScheduleTargetPo record);
}