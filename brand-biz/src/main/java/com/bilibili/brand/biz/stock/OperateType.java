package com.bilibili.brand.biz.stock;

/**
 * <AUTHOR>
 * @date 2016年12月12日
 */
public enum OperateType {
    QUERY(0, "QUERY"),
    LOCK(1, "LOCK"),
    UPDATE(2, "UPDATE"),
	RELEASE(3,"RELEASE");
	
    private Integer code;
    private String desc;

    OperateType(Integer code, String desc) {
        this.desc = desc;
        this.code = code;
    }
    
    public final static OperateType getByCode(int code) {
        for (OperateType operateType : values()) {
            if (operateType.code == code) {
                return operateType;
            }
        }

        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
