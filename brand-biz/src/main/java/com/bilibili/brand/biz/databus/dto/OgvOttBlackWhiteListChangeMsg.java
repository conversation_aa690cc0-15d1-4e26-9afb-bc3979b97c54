package com.bilibili.brand.biz.databus.dto;

import com.alibaba.fastjson2.PropertyNamingStrategy;
import com.alibaba.fastjson2.annotation.JSONType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OTT OGV黑白名单变更
 * * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=794299628">投放黑白名单变动消息</a>
 *
 * <AUTHOR>
 * @date 2023/11/23 17:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class OgvOttBlackWhiteListChangeMsg implements Serializable {
    private static final long serialVersionUID = -2061220328589404353L;
    /**
     * 投放对象id，目前可能取值：ssid/epid
     */
    private Long id;
    /**
     * 投放对象类型
     * 枚举列表：
     * 分季：season
     * 分集：episode
     */
    private String type;
    /**
     * 名单类型
     * 枚举列表：
     * 黑名单：black
     * 白名单：allow
     */
    private String listType;
    /**
     * 操作
     * 枚举列表：
     * 增加：add
     * 移除：remove
     */
    private String action;
    /**
     * 资源位，固定值：player_fragment
     */
    private String resourcePlacementCode;
}
