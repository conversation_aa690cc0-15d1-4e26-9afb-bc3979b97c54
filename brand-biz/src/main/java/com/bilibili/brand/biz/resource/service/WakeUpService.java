package com.bilibili.brand.biz.resource.service;

import com.bilibili.brand.api.common.bean.LaunchConstant;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.biz.account.acc_dao.AccAccountLabelMappingDao;
import com.bilibili.brand.biz.account.po.AccAccountLabelMappingPoExample;
import com.bilibili.brand.biz.resource.pojo.ResWakeUpBarWhiteListPoExample;
import com.bilibili.brand.biz.resource.res_dao.ResWakeUpBarWhiteListDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Service
public class WakeUpService implements IWakeUpService {

    @Autowired
    private AccAccountLabelMappingDao accAccountLabelMappingDao;

    @Autowired
    private ResWakeUpBarWhiteListDao resWakeUpBarWhiteListDao;

    @Value("${direct.wakeup.label:38}")
    private Integer directWakeUpLabelId;

    @Override
    public boolean isInDirectWakeUpWhiteList(Integer accountId) {
        Assert.notNull(accountId, "账号id不可为空");

        AccAccountLabelMappingPoExample example = new AccAccountLabelMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId)
                .andLabelIdEqualTo(directWakeUpLabelId);
        return accAccountLabelMappingDao.countByExample(example) > 0;
    }

    @Override
    public boolean isInWakeUpBarWhiteList(String schema) {
        Assert.hasText(schema, "schema不可为空");

        ResWakeUpBarWhiteListPoExample example = new ResWakeUpBarWhiteListPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAppSchemaEqualTo(schema);
        return resWakeUpBarWhiteListDao.countByExample(example) > 0;
    }

    @Override
    public boolean hasWakeUpBar(String schemaUrl) {
        if (StringUtils.isEmpty(schemaUrl)) {
            return false;
        }

        return schemaUrl.contains(LaunchConstant.WAKE_UP_BAR_SCHEMA_CONTENT)
                || schemaUrl.contains(LaunchConstant.BILI_WAKE_UP_BAR_SCHEMA_CONTENT);
    }
}
