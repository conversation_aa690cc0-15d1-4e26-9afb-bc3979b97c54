package com.bilibili.brand.biz.creative.service;

import com.alibaba.fastjson2.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.creative.dto.ManuscriptInfoBO;
import com.bilibili.brand.biz.creative.dao.GdCreativeManuscriptInfoDao;
import com.bilibili.brand.biz.creative.po.GdCreativeManuscriptInfoPo;
import com.bilibili.brand.biz.creative.po.GdCreativeManuscriptInfoPoExample;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/9/16 20:42
 */
@Slf4j
@Service
public class CreativeManuscriptService {
    @Autowired
    private GdCreativeManuscriptInfoDao manuscriptInfoDao;
    @Autowired
    private ArchiveGrpcClient archiveGrpcClient;

    public void saveCreativeManuscriptInfo(long creativeId, ManuscriptInfoBO manuscriptInfo) {
        if (manuscriptInfo == null || !Utils.isPositive(manuscriptInfo.getAid())) {
            return;
        }
        log.info("[GdCreativeManuscriptService] saveCreativeManuscriptInfo creativeId={}, manuscriptInfoBO={}",
                creativeId, JSON.toJSONString(manuscriptInfo));

        Assert.isTrue(Utils.isPositive(manuscriptInfo.getMid()), "稿件mid不能为空");
        GdCreativeManuscriptInfoPoExample example = new GdCreativeManuscriptInfoPoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        manuscriptInfoDao.updateByExampleSelective(GdCreativeManuscriptInfoPo.builder()
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);

        ArchiveInfoBo archiveInfo = archiveGrpcClient.queryArchiveInfo(manuscriptInfo.getAid());

        GdCreativeManuscriptInfoPo po = GdCreativeManuscriptInfoPo.builder()
                .creativeId(creativeId)
                .aid(manuscriptInfo.getAid())
                .cid(manuscriptInfo.getCid())
                .coverUrl(manuscriptInfo.getCoverUrl())
                .title(manuscriptInfo.getTitle())
                .seq(1)
                .mid(manuscriptInfo.getMid())
                .scene(manuscriptInfo.getScene())
                .bizType(manuscriptInfo.getBizType())
                .secondPartitionId(Optional.ofNullable(archiveInfo).map(ArchiveInfoBo::getSecondPartitionId).orElse(0))
                .build();
        manuscriptInfoDao.insertSelective(po);
    }

    public Map<Long, ManuscriptInfoBO> getCreativeManuscriptInfo(List<Long> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyMap();
        }

        GdCreativeManuscriptInfoPoExample example = new GdCreativeManuscriptInfoPoExample();
        example.createCriteria()
                .andIsDeletedEqualTo(0)
                .andCreativeIdIn(creativeIds);

        List<GdCreativeManuscriptInfoPo> manuscriptInfoPos = manuscriptInfoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(manuscriptInfoPos)) {
            return Collections.emptyMap();
        }
        return manuscriptInfoPos.stream()
                .collect(Collectors.toMap(GdCreativeManuscriptInfoPo::getCreativeId,
                        po -> ManuscriptInfoBO.builder()
                                .aid(po.getAid())
                                .cid(po.getCid())
                                .coverUrl(po.getCoverUrl())
                                .title(po.getTitle())
                                .mid(po.getMid())
                                .build(),
                        (o1, o2) -> o1
                ));
    }

    /**
     * 查询稿件 -> 创意的映射关系
     */
    public Map<Long, List<Long>> getManuscriptCreativeMapByAvid(List<Long> avidList) {
        return getManuscriptCreativeMapByAvid(avidList, null, null);
    }

    /**
     * 查询稿件 -> 创意的映射关系
     */
    public Map<Long, List<Long>> getManuscriptCreativeMapByAvid(List<Long> avidList, List<Integer> sceneList) {
        return this.getManuscriptCreativeMapByAvid(avidList, sceneList, null);
    }

    /**
     * 查询稿件 -> 创意的映射关系
     */
    public Map<Long, List<Long>> getManuscriptCreativeMapByAvid(List<Long> avidList, List<Integer> sceneList,
                                                                List<Integer> bizTypeList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Maps.newHashMap();
        }
        List<GdCreativeManuscriptInfoPo> manuscripts = this.getManuscriptList(avidList, sceneList, bizTypeList);
        return manuscripts.stream()
                .collect(Collectors.groupingBy(GdCreativeManuscriptInfoPo::getAid,
                        Collectors.mapping(GdCreativeManuscriptInfoPo::getCreativeId, Collectors.toList())));
    }

    /**
     * 根据稿件id，查询创意 -> 稿件的映射关系
     */
    public Map<Long, List<ManuscriptInfoBO>> getCreativeManuscriptMapByAvid(List<Long> avidList, List<Integer> sceneList,
                                                                            List<Integer> bizTypeList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Maps.newHashMap();
        }
        List<GdCreativeManuscriptInfoPo> manuscriptInfoPoList = this.getManuscriptList(avidList, sceneList, bizTypeList);
        return manuscriptInfoPoList.stream()
                .collect(Collectors.groupingBy(GdCreativeManuscriptInfoPo::getCreativeId,
                        Collectors.mapping(po -> ManuscriptInfoBO.builder()
                                .aid(po.getAid())
                                .cid(po.getCid())
                                .coverUrl(po.getCoverUrl())
                                .title(po.getTitle())
                                .mid(po.getMid())
                                .scene(po.getScene())
                                .bizType(po.getBizType())
                                .build(), Collectors.toList())));
    }

    private List<GdCreativeManuscriptInfoPo> getManuscriptList(List<Long> avidList, List<Integer> sceneList, List<Integer> bizTypeList) {
        if (CollectionUtils.isEmpty(avidList)) {
            return Collections.emptyList();
        }
        GdCreativeManuscriptInfoPoExample example = new GdCreativeManuscriptInfoPoExample();
        GdCreativeManuscriptInfoPoExample.Criteria criteria = example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAidIn(avidList);
        if (!CollectionUtils.isEmpty(sceneList)) {
            criteria.andSceneIn(sceneList);
        }
        if (!CollectionUtils.isEmpty(bizTypeList)) {
            criteria.andBizTypeIn(bizTypeList);
        }
        List<GdCreativeManuscriptInfoPo> manuscriptInfoPoList = manuscriptInfoDao.selectByExample(example);
        return Objects.isNull(manuscriptInfoPoList) ? Collections.emptyList() : manuscriptInfoPoList;
    }
}
