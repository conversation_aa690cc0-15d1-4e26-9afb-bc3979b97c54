package com.bilibili.ssa.platform.biz.service.schedule;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.adp.resource.api.soa.ISoaAwakenAppWhitelistService;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.utils.SsaConfigUtil;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.ssa.platform.api.order.service.ISsaOrderService;
import com.bilibili.ssa.platform.api.splash_screen.dto.OttCptCreativeDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewScheduleSplashScreenMappingDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDetailDto;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.component.OttCreativeValidator;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleDao;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenServiceDelegate;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.sql.Timestamp;

import static org.mockito.Matchers.any;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年11月05日
 */
public class OttValiadTest extends MockitoDefaultTest {

    @Mock
    private SsaScheduleService ssaScheduleService;
    @Mock
    private ISoaAwakenAppWhitelistService awakenAppWhitelistService;
    @Mock
    private SsaSplashScreenServiceDelegate ssaSplashScreenServiceDelegate;
    @Mock
    private ISoaCrmContractService contractService;
    @Mock
    private ISoaAppPackageService soaAppPackageService;
    @Mock
    private ISsaUposVideoService ssaUposVideoService;
    @Mock
    private IWakeUpService wakeUpService;
    @Mock
    private SsaScheduleDao ssaScheduleDao;
    @Mock
    private ISsaOrderService ssaOrderService;

    @Mock
    private SsaConfigUtil ssaConfigUtil;
    
    @InjectMocks
    OttCreativeValidator validator;

    @Before
    public void before() {
        Mockito.when(ssaSplashScreenServiceDelegate.getSsaSplashScreens(any())).thenReturn(null);
    }


    @Test
    public void validateExternalUpdateSplashScreenBasicInfo() {
        validator.validateExternalUpdateSplashScreenBasicInfo(BeanTestUtils
                .initSimpleFields(OttCptCreativeDto.builder().ssaNewScheduleSplashScreenMappingDtos(
                        Lists.newArrayList(SsaNewScheduleSplashScreenMappingDto.builder()
                                .beginTime(new Timestamp(System.currentTimeMillis() - 1000 * 60* 2))
                                .endTime(new Timestamp(System.currentTimeMillis())).scheduleId(1)
                                .splashScreenId(1).build())
                ).build()), new SsaSplashScreenDetailDto(), new ScheduleDto());
    }


    @Test
    public void testCreateCptSchedule() {

    }



}