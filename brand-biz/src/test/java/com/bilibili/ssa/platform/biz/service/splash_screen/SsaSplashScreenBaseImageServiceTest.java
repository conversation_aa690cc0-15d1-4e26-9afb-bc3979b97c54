package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.ssa.platform.api.splash_screen.dto.BfsFile;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewSplashScreenBaseImageDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenBaseImageDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaImageRuleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenImageService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenBaseImageDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSplashScreenBaseImageDao;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class SsaSplashScreenBaseImageServiceTest extends AbstractMockitoTest {
    @Mock
    private ISsaImageRuleService ssaImageRuleService;
    @Mock
    private ISsaSplashScreenImageService ssaSplashScreenImageService;
    @Mock
    private IBfsService bfsService;
    @Mock
    private ThreadPoolTaskExecutor taskExecutor;
    @Mock
    private SsaSplashScreenBaseImageDao ssaSplashScreenBaseImageDao;
    @Mock
    private ISsaSplashScreenService ssaSplashScreenService;
    @Mock
    private ExtSplashScreenBaseImageDao extSplashScreenBaseImageDao;

    @InjectMocks
    private SsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;

    @Test
    public void testBathSave() {
        Mockito.when(extSplashScreenBaseImageDao.batchInsert(Mockito.any())).thenReturn(1);
        ssaSplashScreenBaseImageService.bathSave(Lists.newArrayList(SsaNewSplashScreenBaseImageDto.builder()
                .splashScreenId(1)
                .type(1)
                .url("123")
                .md5("123")
                .hash("eyJpbWFnZV9NRDUiOiIxMjMiLCJpbWFnZV90eXBlIjoxLCJpbWFnZV91cmwiOiIxMjMifQ")
                .build()));
    }

    @Test
    public void testUpdate() throws IOException, SystemException {
        Map<Integer, Integer> creativeIdMap = new HashMap<>();
        creativeIdMap.put(1, 1);
        Map<Integer, Integer> packageIdMap = new HashMap<>();
        packageIdMap.put(1, 1);
        try {
            ssaSplashScreenBaseImageService.update(Lists.newArrayList(SsaSplashScreenBaseImageDto.builder()
                    .id(1)
                    .splashScreenId(1)
                    .type(1)
                    .url("123")
                    .MD5("123")
                    .hash("eyJpbWFnZV9NRDUiOiIxMjMiLCJpbWFnZV90eXBlIjoxLCJpbWFnZV91cmwiOiIxMjMifQ")
                    .build()), packageIdMap, creativeIdMap, null, null);
        } catch (IOException e) {
        } catch (SystemException e) {
        }
    }

    @Test
    public void testuploadBaseImage() throws ServiceException {

        try {
            ssaSplashScreenBaseImageService.uploadBaseImage(2, BfsFile.builder().build(), 0,0);
        } catch (Exception e) {

        }
    }

    @Test
    public void testDeleted() {
        Mockito.when(ssaSplashScreenBaseImageDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        ssaSplashScreenBaseImageService.deleted(1);
    }

    @Test
    public void testBathDeleted() {
        Mockito.when(ssaSplashScreenBaseImageDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        ssaSplashScreenBaseImageService.bathDeleted(Lists.newArrayList(1));
    }

    @Test
    public void testDeletedBaseImageBySplashScreenIdAndType() {
        Mockito.when(ssaSplashScreenBaseImageDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        ssaSplashScreenBaseImageService.deletedBaseImageBySplashScreenIdAndType(1, 1);
    }
}
