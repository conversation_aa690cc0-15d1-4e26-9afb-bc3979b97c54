package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.platform.report.api.dto.StatCreativeDto;
import com.bilibili.brand.platform.report.api.dto.StatSplashScreenDto;
import com.bilibili.brand.platform.report.api.service.IStatCreativeService;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenRuleImageDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewSplashScreenImageDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaImageRuleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenBaseImageService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenImageDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSplashScreenImageDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenImagePo;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenImageStatus;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenStatus;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2019/8/15
 **/
public class SsaSplashScreenImageServiceTest extends AbstractMockitoTest {

    @Mock
    private SsaSplashScreenImageDao ssaSplashScreenImageDao;
    @Mock
    private ExtSplashScreenImageDao extSplashScreenImageDao;
    @Mock
    private SsaSplashScreenServiceDelegate splashScreenServieDelegate;
    @Mock
    private ISsaSplashScreenService ssaSplashScreenService;
    @Mock
    private IBfsService bfsService;
    @Mock
    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;
    @Mock
    private ISsaImageRuleService ssaImageRuleService;
    @Mock
    private IStatCreativeService statCreativeService;

    @InjectMocks
    private SsaSplashScreenImageService ssaSplashScreenImageService;

    @Before
    public void before() {
        when(ssaSplashScreenImageDao.selectByExample(any())).thenReturn(Collections.singletonList(BeanTestUtils.generateInstance(SsaSplashScreenImagePo.class)));
        when(statCreativeService.getInCreativeIdsGroupByType(any())).thenReturn(Collections.singletonList(BeanTestUtils.generateInstance(StatCreativeDto.class)));
    }

    @Test
    public void getGdSplashScreenStatMapTest() {
        Map<Integer, StatSplashScreenDto> result = ssaSplashScreenImageService.getGdSplashScreenStatMap(Arrays.asList(1));
        Assert.assertNotNull(result);
    }

    @Test
    public void testBatchSave() {
        when(extSplashScreenImageDao.batchInsert(any())).thenReturn(1);
        ssaSplashScreenImageService.batchSave(Lists.newArrayList(SsaNewSplashScreenImageDto.builder()
                .platformId(1)
                .height(10)
                .width(10)
                .imageRuleId(1)
                .splashScreenId(1)
                .md5("123")
                .status(1)
                .url("123")
                .appPackageId(1)
                .build()));
    }

    @Test
    public void testBatchSave2() {
        when(extSplashScreenImageDao.batchInsert(any())).thenReturn(1);
        Map<Integer, Integer> creativeIdMap = new HashMap<>();
        creativeIdMap.put(1, 1);
        ssaSplashScreenImageService.batchSave(Lists.newArrayList(SsaNewSplashScreenImageDto.builder()
                .platformId(1)
                .height(10)
                .width(10)
                .imageRuleId(1)
                .splashScreenId(1)
                .md5("123")
                .status(1)
                .url("123")
                .appPackageId(1)
                .build()), creativeIdMap);
    }

    @Test
    public void testDeleted() {
        when(ssaSplashScreenImageDao.updateByExampleSelective(any(), any())).thenReturn(1);
        ssaSplashScreenImageService.deleted(1);
    }

    @Test
    public void testBatchDeleted() {
        when(ssaSplashScreenImageDao.updateByExampleSelective(any(), any())).thenReturn(1);
        ssaSplashScreenImageService.batchDeleted(Lists.newArrayList(1));
    }

    @Test
    public void testDeletedBySplashScreenIdAndRuleIds() {
        when(ssaSplashScreenImageDao.updateByExampleSelective(any(), any())).thenReturn(1);
        ssaSplashScreenImageService.deletedBySplashScreenIdAndRuleIds(1, Lists.newArrayList(1));
    }

    @Test
    public void testuUpdateSplashImageStatus() {
        when(ssaSplashScreenImageDao.updateByExampleSelective(any(), any())).thenReturn(1);
        ssaSplashScreenImageService.updateSplashImageStatus(1, Lists.newArrayList(1),
                SsaSplashScreenImageStatus.SUCCESS);
    }

    @Test
    public void testUpdateAppPackageIds() {
        when(extSplashScreenImageDao.batchUpdateAppPackIds(any())).thenReturn(1);
        SsaSplashScreenImagePo imagePo = new SsaSplashScreenImagePo();
        imagePo.setAppPackageId(1);
        when(ssaSplashScreenImageDao.selectByExample(any()))
                .thenReturn(Lists.newArrayList(imagePo));
        Map<Integer, Integer> map = new HashMap<>();
        map.put(1, 1);
        map.put(2, 2);
        ssaSplashScreenImageService.updateAppPackageIds(1, map);
    }

    @Test
    public void testSaveRuleImage() {
        when(splashScreenServieDelegate.getSsaSplashScreenById(any()))
                .thenReturn(SsaSplashScreenDto.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .build());
        SsaSplashScreenImagePo imagePo = new SsaSplashScreenImagePo();
        imagePo.setMd5("123");
        when(ssaSplashScreenImageDao.selectByExample(any()))
                .thenReturn(Lists.newArrayList(imagePo));
        doNothing().when(extSplashScreenImageDao).saveOrUpdateSelective(any());
        doNothing().when(splashScreenServieDelegate).batchUpdateStatus(any(), any(), any());
        ssaSplashScreenImageService.saveRuleImage(operator, SplashScreenRuleImageDto.builder()
                .splashScreenId(1)
                .imageRuleId(1)
                .imageUrl("123")
                .imageHash("eyJpbWFnZV9NRDUiOiIxMjMiLCJpbWFnZV90eXBlIjoxLCJpbWFnZV91cmwiOiIxMjMifQ==")
                .build());
    }
}
