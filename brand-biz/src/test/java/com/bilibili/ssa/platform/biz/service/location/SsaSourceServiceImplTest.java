package com.bilibili.ssa.platform.biz.service.location;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.ssa.platform.api.business_side.dto.SsaBusinessSideRotationConfigDto;
import com.bilibili.ssa.platform.api.business_side.service.ISsaBusinessSideService;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.TargetPriceDto;
import com.bilibili.ssa.platform.api.location.dto.UpdateSsaSourceConfigDto;
import com.bilibili.ssa.platform.api.location.service.ISsaCycleService;
import com.bilibili.ssa.platform.api.location.service.ISsaSourceGroupService;
import com.bilibili.ssa.platform.api.log.service.ISsaLogService;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.biz.dao.SsaPriceConfigDao;
import com.bilibili.ssa.platform.biz.dao.SsaSourceConfigDao;
import com.bilibili.ssa.platform.biz.dao.SsaTargetPriceDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSsaPriceConfigDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSsaTargetPriceDao;
import com.bilibili.ssa.platform.biz.po.SsaPriceConfigPo;
import com.bilibili.ssa.platform.biz.po.SsaSourceConfigPo;
import com.bilibili.ssa.platform.biz.po.SsaTargetPricePo;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * Created by dailuwei on 2019/11/29 12:00
 *
 * <AUTHOR>
 */
public class SsaSourceServiceImplTest extends AbstractMockitoTest {
    @Mock
    private SsaSourceConfigDao ssaSourceConfigDao;
    @Mock
    private ISsaLogService ssaLogService;
    @Mock
    private IQuerySourceService querySourceService;
    @Mock
    private ISsaScheduleService ssaScheduleService;
    @Mock
    private ISsaSourceGroupService ssaSourceGroupService;
    @Mock
    private ISsaBusinessSideService ssaBusinessSideService;
    @Mock
    private SsaTargetPriceDao ssaTargetPriceDao;
    @Mock
    private ISsaCycleService ssaCycleService;
    @Mock
    private SsaPriceConfigDao ssaPriceConfigDao;
    @Mock
    private ExtSsaPriceConfigDao extSsaPriceConfigDao;
    @Mock
    private IQueryScheduleService queryScheduleService;
    @Mock
    private ExtSsaTargetPriceDao  extSsaTargetPriceDao;

    @InjectMocks
    SsaSourceServiceImpl ssaSourceService;

    @Test
    public void updateSourceConfig() {
        UpdateSsaSourceConfigDto updateSourceConfigDto = UpdateSsaSourceConfigDto.builder()
                .busMFreqLimit(1)
                .externalPrice(new BigDecimal(1))
                .internalPrice(new BigDecimal(1))
                .internalCpmPrice(new BigDecimal(1))
                .mFreqLimit(1)
                .rotationNum(1)
                .sourceConfigId(1)
                .sourceConfigIds(Arrays.asList(1))
                .targetPrice(new TargetPriceDto())
                .priceConfigId(1)
                .build();
        SsaSourceConfigPo po = new SsaSourceConfigPo();
        po.setExternalPrice(1);
        po.setInternalCpmPrice(1);
        po.setInternalPrice(1);
        po.setBusMFreqLimit(1);
        po.setId(1);
        po.setCycleId(1);
        po.setMFreqLimit(1);
        po.setPageId(1);
        po.setPlatformId(1);
        po.setResourceId(1);
        po.setRotationNum(1);
        po.setAdType(1);
        po.setCycleType(1);
        po.setSalesType(41);
        po.setStatus(1);
        Mockito.when(ssaSourceConfigDao.selectByPrimaryKey(any())).thenReturn(po);
        Mockito.doNothing().when(ssaLogService).insertLog(anyInt(),any(),any(),any(),any());
        Mockito.when(ssaPriceConfigDao.selectByPrimaryKey(Mockito.any()))
                .thenReturn(SsaPriceConfigPo.builder()
                        .id(1)
                        .externalPrice(1)
                        .internalPrice(1)
                        .internalCpmPrice(1)
                        .showStyle(1)
                        .clickArea(1)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .build());
        Mockito.when(queryScheduleService.countSchedule(Mockito.any())).thenReturn(0L);
        SourceAllInfoDto sourceAllInfoDto = SourceAllInfoDto.builder()
                .resourceId(1)
                .platformId(1)
                .pageId(1)
                .build();
        Mockito.when(querySourceService.getSourcesInSourceIds(Arrays.asList(12))).thenReturn(Arrays.asList(sourceAllInfoDto));
        Mockito.when(ssaScheduleService.querySsaSchedule(any())).thenReturn(new ArrayList<>());
        ssaSourceService.updateSourceConfig(operator,updateSourceConfigDto);

        po.setSalesType(SalesType.SSA_GD.getCode());
    }

    @Test
    public void getSourceGroupConfigDto() {

       Mockito.when(ssaSourceGroupService.getSourceIdsByGroupId(any())).thenReturn(Arrays.asList(1));
        SsaSourceConfigPo ssaSourceConfigPo = new SsaSourceConfigPo();
        ssaSourceConfigPo.setRotationNum(1);
        ssaSourceConfigPo.setMFreqLimit(1);
        ssaSourceConfigPo.setSalesType(41);
        ssaSourceConfigPo.setCycleType(1);
        ssaSourceConfigPo.setBusMFreqLimit(1);
        ssaSourceConfigPo.setAdType(1);
        ssaSourceConfigPo.setInternalPrice(1);
        ssaSourceConfigPo.setInternalCpmPrice(1);
        ssaSourceConfigPo.setExternalPrice(1);
        ssaSourceConfigPo.setId(1);
       Mockito.when(ssaSourceConfigDao.selectByExample(any())).thenReturn(Arrays.asList(ssaSourceConfigPo));
        SsaBusinessSideRotationConfigDto ssaBusinessSideRotationConfigDto = new SsaBusinessSideRotationConfigDto();
        ssaBusinessSideRotationConfigDto.setBusRotationLimit(1);
        ssaBusinessSideRotationConfigDto.setConRotationLimit(1);
        Mockito.when(ssaBusinessSideService.getBusinessSideLimitBySourceConfigId(any(),any())).thenReturn(ssaBusinessSideRotationConfigDto);
        Mockito.when(ssaTargetPriceDao.selectByExample(any())).thenReturn(new ArrayList<>());
        ssaSourceService.getSourceGroupConfigDto(1,1,1,1,41,
                null, null, 0, OrderProduct.SSA_CPT.getCode(), 0,0,
                null, null, 0);
    }

    @Test
    public void getSourcesByCycleId() {
        SsaCycleDto ssaCycleDto = SsaCycleDto.builder()
                .beginTime(new Timestamp(1))
                .endTime(new Timestamp(Integer.MAX_VALUE))
                .adType(1)
                .id(1)
                .name("test")
                .status(1)
                .build();
        Mockito.when(ssaCycleService.getCycleDtoById(any())).thenReturn(ssaCycleDto);
        SsaSourceConfigPo ssaSourceConfigPo = new SsaSourceConfigPo();
        ssaSourceConfigPo.setRotationNum(1);
        ssaSourceConfigPo.setMFreqLimit(1);
        ssaSourceConfigPo.setSalesType(41);
        ssaSourceConfigPo.setCycleType(1);
        ssaSourceConfigPo.setBusMFreqLimit(1);
        ssaSourceConfigPo.setAdType(1);
        ssaSourceConfigPo.setInternalPrice(1);
        ssaSourceConfigPo.setInternalCpmPrice(1);
        ssaSourceConfigPo.setExternalPrice(1);
        ssaSourceConfigPo.setSourceId(1);
        ssaSourceConfigPo.setId(1);
        Mockito.when(ssaSourceConfigDao.selectByExample(any())).thenReturn(Collections.singletonList(ssaSourceConfigPo));
        Map<Integer, SourceAllInfoDto> sourceMap = new HashMap<>();
        SourceAllInfoDto sourceAllInfoDto = SourceAllInfoDto.builder()
                .resourceId(1)
                .platformId(1)
                .pageId(1)
                .sourceId(1)
                .build();
        sourceMap.put(1,sourceAllInfoDto);
        Mockito.when(querySourceService.getCptSourceMapInSourceIds(Arrays.asList(1))).thenReturn(sourceMap);
        ssaSourceService.getSourcesByCycleId(1,1);
    }

    @Test
    public void copyInsertSourceConfigByCycleId() {
        SsaSourceConfigPo ssaSourceConfigPo = new SsaSourceConfigPo();
        ssaSourceConfigPo.setRotationNum(1);
        ssaSourceConfigPo.setMFreqLimit(1);
        ssaSourceConfigPo.setSalesType(41);
        ssaSourceConfigPo.setCycleType(1);
        ssaSourceConfigPo.setBusMFreqLimit(1);
        ssaSourceConfigPo.setAdType(1);
        ssaSourceConfigPo.setInternalPrice(1);
        ssaSourceConfigPo.setInternalCpmPrice(1);
        ssaSourceConfigPo.setExternalPrice(1);
        ssaSourceConfigPo.setSourceId(1);
        ssaSourceConfigPo.setId(1);
        Mockito.when(ssaSourceConfigDao.selectByExample(any())).thenReturn(Collections.singletonList(ssaSourceConfigPo));

        Mockito.when(ssaPriceConfigDao.selectByExample(any())).thenReturn(Collections.singletonList(SsaPriceConfigPo
                .builder().id(12).externalPrice(200).internalCpmPrice(12).internalPrice(445)
                .showStyle(1).sourceConfigId(1).clickArea(12).isDeleted(0).build()));

        Mockito.when(ssaTargetPriceDao.selectByExample(any()))
                .thenReturn(Collections.singletonList(SsaTargetPricePo.builder().areaType(112).id(1).build()));

        ssaSourceService.copyInsertSourceConfigByCycleId(1,2, 1);
    }

}