package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.api.common.enums.SsaVideoTypeEnum;
import com.bilibili.brand.api.creative.service.IGdCreativeShareService;
import com.bilibili.brand.api.resource.wakeup.IWakeUpService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.utils.SwitchUtil;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.ssa.platform.api.order.dto.SsaOrderDto;
import com.bilibili.ssa.platform.api.order.service.ISsaOrderService;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.api.splash_screen.service.*;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.component.SplashScreenValidator;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenBrandCreativeMappingDao;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSplashScreenDao;
import com.bilibili.ssa.platform.biz.dao.ext.ExtSsaSplashScreenBrandCreativeMappingDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.temp.service.TempLauCreativeServiceDelegate;
import com.bilibili.ssa.platform.common.enums.*;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Base64;
import java.util.Collections;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年08月14日
 */
public class SsaSplashScreenServiceDelegateTest extends AbstractMockitoTest {

    @Mock
    private SsaSplashScreenDao ssaSplashScreenDao;
    @Mock
    private ExtSsaSplashScreenBrandCreativeMappingDao extSsaSplashScreenBrandCreativeMappingDao;
    @Mock
    private SsaSplashScreenBrandCreativeMappingDao ssaSplashScreenBrandCreativeMappingDao;
    @Mock
    private ISsaOrderService ssaOrderService;
    @Mock
    private SsaSplashScreenLogService ssaSplashScreenLogService;
    @Mock
    private SplashScreenValidator splashScreenValidator;
    @Mock
    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;
    @Mock
    private ISsaSplashScreenImageService ssaSplashScreenImageService;
    @Mock
    private ISsaImageRuleService ssaImageRuleService;
    @Mock
    private ISsaSplashScreenOtherService ssaSplashScreenOtherService;
    @Mock
    private ISsaSplashScreenVersionService ssaSplashScreenVersionService;
    @Mock
    private ISsaSplashScreenSchduleService ssaSplashScreenSchduleService;
    @Mock
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;
    @Mock
    private ISsaScheduleService ssaScheduleService;
    @Mock
    private ISoaQueryAccountService soaQueryAccountService;
    @Mock
    private ExtSplashScreenDao extSplashScreenDao;
    @Mock
    private IQueryScheduleService querySheduleService;
    @Mock
    private ISsaUposVideoService ssaUposVideoService;
    @Mock
    private TempLauCreativeServiceDelegate tempLauCreativeServiceDelegate;
    @Mock
    private ISsaSplashScreenCustomUrlService ssaSplashScreenCustomUrlService;
    @Mock
    private IGdCreativeShareService gdCreativeShareService;
    @Mock
    private IWakeUpService wakeUpService;
    @Mock
    private SsaSplashScreenPreLoadService ssaSplashScreenPreLoadService;

    @Mock
    private ISsaSplashScreenJumpInfoService jumpInfoService;

    @Mock
    private ISsaSplashScreenButtonService buttonService;

    @InjectMocks
    SsaSplashScreenServiceDelegate ssaSplashScreenServiceDelegate;

    @Mock
    private SwitchUtil switchUtil;

    @Before
    public void before() {
        ReflectionTestUtils.setField(ssaSplashScreenServiceDelegate, "brandCreativeType", 1);
        ReflectionTestUtils.setField(ssaSplashScreenServiceDelegate, "sourceToPlatformMap", Collections.singletonMap(1, 1));

        doNothing().when(ssaSplashScreenCustomUrlService).batchSaveCustomizedImpUrl(Mockito.anyInt(), Mockito.any());
        doNothing().when(ssaSplashScreenCustomUrlService).batchSaveCustomizedClickUrl(Mockito.anyInt(), Mockito.any());
        doNothing().when(ssaSplashScreenCustomUrlService).batchUpdateCustomizedImpUrl(Mockito.anyInt(), Mockito.any());
        doNothing().when(ssaSplashScreenCustomUrlService).batchUpdateCustomizedClickUrl(Mockito.anyInt(), Mockito.any());
        Mockito.when(ssaSplashScreenCustomUrlService.getCustomizedImpUrlList(Mockito.anyInt())).thenReturn(Lists.newArrayList("https://baidu.com"));
        Mockito.when(ssaSplashScreenCustomUrlService.getCustomizedClickUrlList(Mockito.anyInt())).thenReturn(Lists.newArrayList("https://baidu.com"));
        doNothing().when(gdCreativeShareService).validateCreativeShareInfo(Mockito.any());
        doNothing().when(gdCreativeShareService).saveCreativeShare(Mockito.any());
    }

    @Test
    public void saveExternalSsa() throws ServiceException {
        Mockito.when(ssaSplashScreenDao.insertSelective(Mockito.any()))
                .thenAnswer(answer -> {
                    SsaSplashScreenPo splashScreenPo = answer.getArgument(0, SsaSplashScreenPo.class);
                    splashScreenPo.setId(1);
                    return 1L;
                });
        doNothing().when(ssaSplashScreenPreLoadService).externalPreloadInfoAdd(any(),any());
        int res = ssaSplashScreenServiceDelegate.saveExternalSsa(
                BeanTestUtils.initSimpleFields(SsaNewExternalSplashScreenDto.builder().build()),
                operator);

        Assert.assertTrue(res > 0);
    }

    @Test
    public void saveExternalSsaGd() throws ServiceException {
        Mockito.when(ssaSplashScreenDao.insertSelective(Mockito.any()))
                .thenAnswer(answer -> {
                    SsaSplashScreenPo splashScreenPo = answer.getArgument(0, SsaSplashScreenPo.class);
                    splashScreenPo.setId(1);
                    return 1L;
                });
        Mockito.when(ssaOrderService.load(any()))
                .thenReturn(BeanTestUtils.initSimpleFields(SsaOrderDto.builder()
                        .salesType(SalesType.SSA_GD.getCode())
                        .build()));
        doNothing().when(ssaSplashScreenPreLoadService).externalPreloadInfoAdd(any(),any());
        int res = ssaSplashScreenServiceDelegate.saveExternalSsa(
                BeanTestUtils.initSimpleFields(SsaNewExternalSplashScreenDto.builder().build()),
                operator);

        Assert.assertTrue(res > 0);
    }

    @Test
    public void saveInternalSsa() throws Exception {
        doNothing().when(ssaSplashScreenPreLoadService).innerPreloadInfoAdd(any(),any());
        int res = ssaSplashScreenServiceDelegate.saveInternalSsa(
                BeanTestUtils.initSimpleFields(SsaNewInternalSplashScreenDto.builder().build()),
                operator);

        Assert.assertTrue(res > 0);
    }


    @Test
    public void updateExternalSsa() throws Exception {
        doNothing().when(ssaSplashScreenPreLoadService).externalPreloadInfoUpdate(any(),any());
        int updateExternalSsa = ssaSplashScreenServiceDelegate.updateExternalSsa(
                BeanTestUtils.initSimpleFields(SsaUpdateSplashScreenDto.builder()
                        .ssaBaseImageDtos(Collections.singletonList(
                                BeanTestUtils.initSimpleFields(SsaSplashScreenBaseImageDto.builder()
                                        .hash(Base64.getEncoder().encodeToString(GsonUtils.toJson(
                                                BeanTestUtils.initSimpleFields(ImageHashDto.builder().build()))
                                                .getBytes()))
                                        .build())))
                        .build()),
                operator);

        Assert.assertTrue(updateExternalSsa > 0);
    }

    @Test
    public void testDeletedInScheduleIds() {
        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchdulesInScheduleIds(Mockito.any()))
                .thenReturn(Collections.emptyList());
        ssaSplashScreenServiceDelegate.deletedInScheduleIds(Lists.newArrayList(1), operator);

        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchdulesInScheduleIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleSplashScreenMappingDto.builder()
                        .id(1)
                        .scheduleId(1)
                        .build()));
        doNothing().when(ssaSplashScreenSchduleService).deletedInIds(Mockito.any(), Mockito.any());
        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchduleInSplashScreenIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleSplashScreenMappingDto.builder()
                        .splashScreenId(1)
                        .build()));
        ssaSplashScreenServiceDelegate.deletedInScheduleIds(Lists.newArrayList(1), operator);

        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchduleInSplashScreenIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleSplashScreenMappingDto.builder()
                        .splashScreenId(2)
                        .build()));
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any())).thenReturn(Collections.emptyList());
        ssaSplashScreenServiceDelegate.deletedInScheduleIds(Lists.newArrayList(1), operator);
    }

    @Test
    public void testPause() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .id(1)
                        .jumpType(1)
                        .status(4)
                        .build()));
        Mockito.when(ssaSplashScreenDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        doNothing().when(ssaSplashScreenLogService).addUpdateStatusLog(Mockito.any(), Mockito.any());
        ssaSplashScreenServiceDelegate.pause(1, operator);
    }

    @Test
    public void testStartUp() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .id(1)
                        .jumpType(1)
                        .status(4)
                        .type(SsaSplashScreenType.OPERATE.getCode())
                        .build()));
        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchduleBySplashScreenId(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleSplashScreenMappingDto.builder()
                        .scheduleId(1)
                        .build()));
        Mockito.when(ssaScheduleService.querySsaSchedule(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleDto.builder()
                        .launchDate(Utils.getToday())
                        .build()));
        ssaSplashScreenServiceDelegate.startUp(1, operator);

        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .id(1)
                        .jumpType(1)
                        .status(4)
                        .type(SsaSplashScreenType.BIRTHDAY.getCode())
                        .showStyle(SsaShowStyleType.HORIZONTA_SCREEN_VIDEO.getCode())
                        .build()));
        ssaSplashScreenServiceDelegate.startUp(1, operator);
    }

    @Test
    public void testBatchReject() {
        ssaSplashScreenServiceDelegate.batchReject(operator, Collections.emptyList(), "test");

        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        ssaSplashScreenServiceDelegate.batchReject(operator, Lists.newArrayList(1), "test");

        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.COMPLETED.getCode())
                        .jumpType(1)
                        .build()));
        ssaSplashScreenServiceDelegate.batchReject(operator, Lists.newArrayList(1), "test");

        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .jumpType(1)
                        .build()));
        doNothing().when(extSplashScreenDao).batchUpdateStatus(Mockito.any());
        doNothing().when(ssaSplashScreenLogService).addUpdateStatusLog(Mockito.any(), Mockito.any());
        ssaSplashScreenServiceDelegate.batchReject(operator, Lists.newArrayList(1), "test");
    }

   // @Test
    public void testReAduitPass() throws ServiceException {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.WAIT_ON_LINE.getCode())
                        .showStyle(SsaShowStyleType.HORIZONTA_SCREEN_VIDEO.getCode())
                        .jumpType(1)
                        .issuedTime(SsaIssuedTimeType.HAVE_A_TRIAL.getCode())
                        .build()));
        Mockito.when(ssaSplashScreenImageService.getFailImageBySplashScreenId(Mockito.any()))
                .thenReturn(Collections.emptyList());
        Mockito.when(ssaSplashScreenVideoService.getVideoBySplashScreenIdAndVideoType(Mockito.any(), SsaVideoTypeEnum.SSA_NORMAL.getCode()))
                .thenReturn(SsaSplashScreenVideoDto.builder()
                        .status(SsaSplashScreenVideoStatus.TRANS_SUCCESS.getCode())
                        .build());
        doNothing().when(extSplashScreenDao).batchUpdateStatus(Mockito.any());
        doNothing().when(ssaSplashScreenLogService).addUpdateStatusLog(Mockito.any(), Mockito.any());
//        ssaSplashScreenServiceDelegate.reAduitPass(1, operator);

        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.WAIT_ON_LINE.getCode())
                        .showStyle(SsaShowStyleType.HORIZONTA_SCREEN_VIDEO.getCode())
                        .jumpType(1)
                        .issuedTime(SsaIssuedTimeType.TWO_HOURS_BEFORE_THE_SCHEDULE.getCode())
                        .build()));
        Mockito.when(ssaSplashScreenSchduleService.getSplashScreenSchduleBySplashScreenId(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleSplashScreenMappingDto.builder()
                        .scheduleId(1)
                        .build()));
        Mockito.when(ssaScheduleService.querySsaSchedule(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaScheduleDto.builder()
                        .launchDate(Utils.getToday())
                        .build()));
//        ssaSplashScreenServiceDelegate.reAduitPass(1, operator);
    }

    @Test
    public void testAduitReject() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .jumpType(1)
                        .build()));
        Mockito.when(ssaSplashScreenDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        doNothing().when(ssaSplashScreenLogService).addUpdateStatusLog(Mockito.any(), Mockito.any());
        ssaSplashScreenServiceDelegate.aduitReject(1, "test", operator);
    }

    @Test
    public void testAduitReject2() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .jumpType(1)
                        .build()));
        Mockito.when(ssaSplashScreenDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        ssaSplashScreenServiceDelegate.aduitReject(1, "test");
    }

    @Test
    public void testUpdateManagerShowTime() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .jumpType(1)
                        .build()));
        Mockito.when(ssaSplashScreenDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        ssaSplashScreenServiceDelegate.updateManagerShowTime(1, 3, operator);
    }

    @Test
    public void testUpdateLaunchShowTime() {
        Mockito.when(ssaSplashScreenDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenPo.builder()
                        .status(SsaSplashScreenStatus.TO_BE_FIRST_AUDIT.getCode())
                        .jumpType(1)
                        .type(SsaSplashScreenType.BIRTHDAY.getCode())
                        .build()));
        Mockito.when(ssaSplashScreenDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        ssaSplashScreenServiceDelegate.updateLaunchShowTime(1, 3, operator);
    }

    @Test
    public void testUpdateRuleImage() {
        doNothing().when(ssaSplashScreenImageService).saveRuleImage(Mockito.any(), Mockito.any());
        ssaSplashScreenServiceDelegate.updateRuleImage(Mockito.any(), Mockito.any());
    }
}