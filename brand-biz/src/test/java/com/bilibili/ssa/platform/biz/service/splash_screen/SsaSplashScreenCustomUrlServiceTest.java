package com.bilibili.ssa.platform.biz.service.splash_screen;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenCustomizedDTO;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenCustomUrlDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenCustomUrlPo;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;

import java.util.List;

public class SsaSplashScreenCustomUrlServiceTest extends AbstractMockitoTest {
    @Mock
    private SsaSplashScreenCustomUrlDao ssaSplashScreenCustomUrlDao;

    @InjectMocks
    private SsaSplashScreenCustomUrlService ssaSplashScreenCustomUrlService;

    @Before
    public void before() {
        Mockito.when(ssaSplashScreenCustomUrlDao.insertBatch(Mockito.any())).thenReturn(1);
        Mockito.when(ssaSplashScreenCustomUrlDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
    }

    @Test
    public void testBatchSaveCustomizedImpUrl() {
        ssaSplashScreenCustomUrlService.batchSaveCustomizedImpUrl(1, Lists.newArrayList("https://baidu.com"));
    }

    @Test
    public void testBatchSaveCustomizedClickUrl() {
        ssaSplashScreenCustomUrlService.batchSaveCustomizedClickUrl(1, Lists.newArrayList("https://baidu.com"));
    }

    @Test
    public void testBatchUpdateCustomizedImpUrl() {
        ssaSplashScreenCustomUrlService.batchUpdateCustomizedImpUrl(1, Lists.newArrayList("https://baidu.com"));
    }

    @Test
    public void testBatchUpdateCustomizedClickUrl() {
        ssaSplashScreenCustomUrlService.batchUpdateCustomizedClickUrl(1, Lists.newArrayList("https://baidu.com"));
    }

    @Test
    public void testGetCustomizedImpUrlList() {
        Mockito.when(ssaSplashScreenCustomUrlDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenCustomUrlPo.builder()
                        .id(1)
                        .splashScreenId(1)
                        .urlType(1)
                        .isDeleted(0)
                        .customUrl("https://baidu.com")
                        .build()));
        List<String> result = ssaSplashScreenCustomUrlService.getCustomizedImpUrlList(1);
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetCustomizedClickUrlList() {
        Mockito.when(ssaSplashScreenCustomUrlDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(SsaSplashScreenCustomUrlPo.builder()
                        .id(1)
                        .splashScreenId(1)
                        .urlType(2)
                        .isDeleted(0)
                        .customUrl("https://baidu.com")
                        .build()));
        List<String> result = ssaSplashScreenCustomUrlService.getCustomizedClickUrlList(1);
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetUrlPlatformMap() {
        ssaSplashScreenCustomUrlService.getUrlPlatformMap(1);
    }

    @Test
    public void testBatchSave() {
        ssaSplashScreenCustomUrlService.batchSave(1, Lists.newArrayList(SplashScreenCustomizedDTO.builder()
                .ssaCustomizedClickUrlList(Lists.newArrayList("sfsafa"))
                .ssaCustomizedImpUrlList(Lists.newArrayList("sdfasfa"))
                .platformId(1).build()));
    }

}
