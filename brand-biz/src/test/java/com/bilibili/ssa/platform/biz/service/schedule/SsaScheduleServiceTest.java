package com.bilibili.ssa.platform.biz.service.schedule;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.biz.dao.SsaScheduleDao;
import com.bilibili.ssa.platform.biz.po.SsaSchedulePo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * Created by dailuwei on 2020/3/29 23:45
 *
 * <AUTHOR>
 */
public class SsaScheduleServiceTest extends AbstractMockitoTest {
    @Mock
    private SsaScheduleDao ssaScheduleDao;
    @InjectMocks
    private SsaScheduleService ssaScheduleService;
    @Test
    public void getSsaScheduleByGdScheduleId() {
        List<SsaSchedulePo> list = new ArrayList<>();
        list.add(SsaSchedulePo.builder().id(1).externalPrice(1L).internalPrice(1L).build());
        Mockito.when(ssaScheduleDao.selectByExample(Mockito.any())).thenReturn(list);
        ssaScheduleService.getSsaScheduleByGdScheduleId(Lists.newArrayList(1));
    }
}