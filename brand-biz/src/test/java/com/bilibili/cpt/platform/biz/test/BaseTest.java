//package com.bilibili.cpt.platform.biz.test;
//
//import com.bilibili.adp.common.bean.Operator;
//import com.bilibili.adp.common.enums.OperatorType;
//import com.bilibili.adp.common.enums.SystemType;
//import com.bilibili.bjcom.mock.SpringMockitoInjectListener;
//import org.junit.Before;
//import org.junit.Rule;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.junit.MockitoJUnit;
//import org.mockito.junit.MockitoRule;
//import org.springframework.test.context.ContextConfiguration;
//import org.springframework.test.context.TestExecutionListeners;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration("classpath:config.xml")
//public class BaseTest {
//
//    protected Operator operator;
//
//    @Before
//    public void init() {
//        operator = Operator.builder()
//                .operatorId(1)
//                .operatorName("Bilibilier")
//                .operatorType(OperatorType.ADVERTISERS)
//                .bilibiliUserName("Bilibilier")
//                .systemType(SystemType.CPM)
//                .build();
//
//    }
//
//    @Test
//    public void defaultTest() {
//        System.out.println("defaultTest");
//    }
//
//}
//
//
//
