package com.bilibili.cpt.platform.biz.service.location;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.cpt.platform.api.location.dto.CptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.UpdateCptSourceConfigDto;
import com.bilibili.cpt.platform.biz.dao.CptSourceConfigDao;
import com.bilibili.cpt.platform.biz.dao.LocalCptSourceConfigDao;
import com.bilibili.cpt.platform.biz.po.CptSourceConfigPo;
import com.bilibili.location.api.service.query.IQueryPlatformService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * Created by dailuwei on 2019/9/4 19:10
 *
 * <AUTHOR>
 */
public class CptSourceServiceTest extends AbstractMockitoTest {
    @Mock
    private CptSourceConfigDao cptSourceConfigDao;
    @Mock
    private CptCycleService cptCycleService;
    @Mock
    private IQuerySourceService querySourceService;
    @Mock
    private IQueryPlatformService queryPlatformService;
    @Mock
    private IQueryScheduleService querySheduleService;
    @Mock
    private LocalCptSourceConfigDao localCptSourceConfigDao;
    @Mock
    private IGdLogService gdLogService;

    @InjectMocks
    private CptSourceService cptSourceService;


    @Test
    public void queryCptSourceList() {
        ButtonCopyDto buttonCopyDto = new ButtonCopyDto();
        buttonCopyDto.setType(1);
        TemplateDto templateDto = new TemplateDto().builder()
                .templateId(1)
                .templateName("teet")
                .isFillTitle(true)
                .titleMaxLength(1)
                .titleMinLength(1)
                .isFillDesc(true)
                .descMaxLength(1)
                .descMinLength(1)
                .isSupportImage(true)
                .isSupportExtImage(true)
                .extImageHeight(1)
                .extImageKbLimit(1)
                .extImageWidth(1)
                .imageWidth(1)
                .imageHeight(1)
                .imageKbLimit(1)
                .isSupportVideo(true)
                .isFillExtDesc(true)
                .extDescMaxLength(1)
                .extDescMinLength(1)
                .videoWidth(1)
                .videoHeight(1)
                .videoKbLimit(1)
                .videoDurationMax(1)
                .videoDurationMin(1)
                .isSupportVideoId(true)
                .html("asdf")
                .isSupportButton(true)
                .buttonCopyDtos(Arrays.asList(buttonCopyDto))
                .build();
        SourceAllInfoDto sourceAllInfoDto = new SourceAllInfoDto().builder()
                .name("test")
                .pageId(1)
                .pageName("asdf")
                .platformId(1)
                .platformName("asdf")
                .resourceId(1)
                .resourceName("sdf")
                .sourceId(1)
                .cpc(1)
                .cpm(1)
                .cpt(1)
                .gd(1)
                .level("asdf")
                .resType(1)
                .status(1)
                .adReserve(1)
                .adx(1)
                .cont(1)
                .isAdLoc(1)
                .isContLoc(1)
                .isOnlineSell(1)
                .srcIndex(1)
                .templateDtos(Arrays.asList(templateDto))
                .build();
        Map<Integer, SourceAllInfoDto> map = new HashMap<>();
        map.put(1, sourceAllInfoDto);
        Mockito.when(querySourceService.getCptSourceMapInResourceIds(any())).thenReturn(map);
        CptSourceConfigPo cptSourceConfigPo = new CptSourceConfigPo();
        cptSourceConfigPo.setCycleId(1);
        cptSourceConfigPo.setRotationNum(1);
        cptSourceConfigPo.setMFreqLimit(1);
        cptSourceConfigPo.setExternalPrice(1);
        cptSourceConfigPo.setInternalPrice(1);
        cptSourceConfigPo.setInternalCpmPrice(1);
        cptSourceConfigPo.setIsDistribution(1);
        cptSourceConfigPo.setCycleId(1);
        cptSourceConfigPo.setSourceId(1);
        Mockito.when(cptSourceConfigDao.selectByExample(any())).thenReturn(Arrays.asList(cptSourceConfigPo));
        CptCycleDto cptCycleDto = new CptCycleDto();
        cptCycleDto.setId(1);
        cptCycleDto.setBeginTime(new Timestamp(1));
        cptCycleDto.setEndTime(new Timestamp(2));
        Map<Integer, CptCycleDto> cptCycleDtoMap = new HashMap<>();
        cptCycleDtoMap.put(1, cptCycleDto);
        Mockito.when(cptCycleService.getCycleDtoMapInIds(any())).thenReturn(cptCycleDtoMap);
        Mockito.when(querySheduleService.countScheduleIdByCycle(any())).thenReturn(0);
        cptSourceService.queryCptSourceList(1, Arrays.asList(1), Arrays.asList(1), Arrays.asList(1), SalesType.CPT.getCode());

    }

    @Test
    public void getCptSourceBySourceId() {
        ButtonCopyDto buttonCopyDto = new ButtonCopyDto();
        buttonCopyDto.setType(1);
        TemplateDto templateDto = new TemplateDto().builder()
                .templateId(1)
                .templateName("teet")
                .isFillTitle(true)
                .titleMaxLength(1)
                .titleMinLength(1)
                .isFillDesc(true)
                .descMaxLength(1)
                .descMinLength(1)
                .isSupportImage(true)
                .isSupportExtImage(true)
                .extImageHeight(1)
                .extImageKbLimit(1)
                .extImageWidth(1)
                .imageWidth(1)
                .imageHeight(1)
                .imageKbLimit(1)
                .isSupportVideo(true)
                .isFillExtDesc(true)
                .extDescMaxLength(1)
                .extDescMinLength(1)
                .videoWidth(1)
                .videoHeight(1)
                .videoKbLimit(1)
                .videoDurationMax(1)
                .videoDurationMin(1)
                .isSupportVideoId(true)
                .html("asdf")
                .isSupportButton(true)
                .buttonCopyDtos(Arrays.asList(buttonCopyDto))
                .build();
        SourceAllInfoDto sourceAllInfoDto = new SourceAllInfoDto().builder()
                .name("test")
                .pageId(1)
                .pageName("asdf")
                .platformId(1)
                .platformName("asdf")
                .resourceId(1)
                .resourceName("sdf")
                .sourceId(1)
                .cpc(1)
                .cpm(1)
                .cpt(1)
                .gd(1)
                .level("asdf")
                .resType(1)
                .status(1)
                .adReserve(1)
                .adx(1)
                .cont(1)
                .isAdLoc(1)
                .isContLoc(1)
                .isOnlineSell(1)
                .srcIndex(1)
                .templateDtos(Arrays.asList(templateDto))
                .build();
        Map<Integer, SourceAllInfoDto> map = new HashMap<>();
        map.put(1, sourceAllInfoDto);
        Mockito.when(querySourceService.getSourceMapInSourceIds(any())).thenReturn(map);

        CptSourceConfigPo cptSourceConfigPo = new CptSourceConfigPo();
        cptSourceConfigPo.setCycleId(1);
        cptSourceConfigPo.setRotationNum(1);
        cptSourceConfigPo.setMFreqLimit(1);
        cptSourceConfigPo.setExternalPrice(1);
        cptSourceConfigPo.setInternalPrice(1);
        cptSourceConfigPo.setInternalCpmPrice(1);
        cptSourceConfigPo.setIsDistribution(1);
        cptSourceConfigPo.setCycleId(1);
        cptSourceConfigPo.setSourceId(1);
        Mockito.when(cptSourceConfigDao.selectByExample(any())).thenReturn(Arrays.asList(cptSourceConfigPo));
        CptCycleDto cptCycleDto = new CptCycleDto();
        cptCycleDto.setId(1);
        cptCycleDto.setBeginTime(new Timestamp(1));
        cptCycleDto.setEndTime(new Timestamp(2));

        Mockito.when(cptCycleService.getCycleDtoById(any())).thenReturn(cptCycleDto);
        Mockito.when(querySheduleService.countScheduleIdByCycle(any())).thenReturn(0);

        cptSourceService.getCptSourceBySourceId(1, 1);
    }

    @Test
    public void updateSourceConfig() {
        UpdateCptSourceConfigDto updateCptSourceConfigDto = UpdateCptSourceConfigDto.builder()
                .cycleId(1)
                .sourceId(1)
                .mFreqLimit(1)
                .rotationNum(1)
                .externalPrice(new BigDecimal(1))
                .internalPrice(new BigDecimal(1))
                .internalCpmPrice(new BigDecimal(1))
                .isDistribution(1)
                .build();
        SourceAllInfoDto sourceAllInfoDto = SourceAllInfoDto.builder()
                .templateDtos(Arrays.asList(new TemplateDto()))
                .pageId(1)
                .platformId(1)
                .resourceId(1)
                .name("test")
                .build();
        Mockito.when(querySourceService.getCptSourceBySourceId(any())).thenReturn(sourceAllInfoDto);
        CptCycleDto cptCycleDto = CptCycleDto.builder()
                .beginTime(new Timestamp(Integer.MAX_VALUE))
                .endTime(new Timestamp(Integer.MAX_VALUE))
                .id(1)
                .name("test")
                .build();
        Mockito.when(cptCycleService.getCycleDtoById(any())).thenReturn(cptCycleDto);
        CptSourceConfigPo cptSourceConfigPo = new CptSourceConfigPo();
        cptSourceConfigPo.setInternalCpmPrice(1);
        cptSourceConfigPo.setInternalPrice(1);
        cptSourceConfigPo.setExternalPrice(1);
        cptSourceConfigPo.setCycleId(1);
        Mockito.when(cptSourceConfigDao.selectByExample(any())).thenReturn(Arrays.asList(cptSourceConfigPo));
        Mockito.when(querySheduleService.countCptScheduleByCycleIdAndSourceId(any(), any())).thenReturn(0);
        Mockito.when(localCptSourceConfigDao.saveSelective(any())).thenReturn(0);
        Mockito.doNothing().when(gdLogService).insertLog(anyInt(),any(),any(),any(),any());

        cptSourceService.updateSourceConfig(operator, updateCptSourceConfigDto);
    }

    @Test
    public void testBatchOpenSourcePreBooking() {
        Mockito.when(cptSourceConfigDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        cptSourceService.batchOpenSourcePreBooking(Operator.builder().operatorName("test").operatorType(OperatorType.SYSTEM).build(), 1, Lists.newArrayList(1), SalesType.CPT.getCode());

        CptSourceConfigPo sourceConfigPo = new CptSourceConfigPo();
        sourceConfigPo.setId(1);
        sourceConfigPo.setSourceId(1);
        sourceConfigPo.setCycleId(1);
        Mockito.when(cptSourceConfigDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(sourceConfigPo));
        Mockito.when(cptSourceConfigDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(cptCycleService.getCycleDtoById(Mockito.any())).thenReturn(CptCycleDto.builder().name("测试刊例").build());
        Mockito.when(querySourceService.getCptSourcesInSourceIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(SourceAllInfoDto.builder().sourceId(1).name("测试资源位").build()));
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        cptSourceService.batchOpenSourcePreBooking(Operator.builder().operatorName("test").operatorType(OperatorType.SYSTEM).build(), 1, Lists.newArrayList(1), SalesType.CPT.getCode());
    }

    @Test
    public void testBatchCloseSourcePreBooking() {
        Mockito.when(cptSourceConfigDao.selectByExample(Mockito.any()))
                .thenReturn(Collections.emptyList());
        cptSourceService.batchCloseSourcePreBooking(Operator.builder().operatorName("test").operatorType(OperatorType.SYSTEM).build(), 1, Lists.newArrayList(1), SalesType.CPT.getCode());

        CptSourceConfigPo sourceConfigPo = new CptSourceConfigPo();
        sourceConfigPo.setId(1);
        sourceConfigPo.setSourceId(1);
        sourceConfigPo.setCycleId(1);
        Mockito.when(cptSourceConfigDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(sourceConfigPo));
        Mockito.when(cptSourceConfigDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(cptCycleService.getCycleDtoById(Mockito.any())).thenReturn(CptCycleDto.builder().name("测试刊例").build());
        Mockito.when(querySourceService.getCptSourcesInSourceIds(Mockito.any()))
                .thenReturn(Lists.newArrayList(SourceAllInfoDto.builder().sourceId(1).name("测试资源位").build()));
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
        cptSourceService.batchCloseSourcePreBooking(Operator.builder().operatorName("test").operatorType(OperatorType.SYSTEM).build(), 1, Lists.newArrayList(1), SalesType.CPT.getCode());
    }
}