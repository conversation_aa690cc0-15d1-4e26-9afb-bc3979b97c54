package com.bilibili.cpt.platform.biz.service.business_side;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.cpt.platform.api.business_side.dto.NewBusinessSideDto;
import com.bilibili.cpt.platform.api.business_side.dto.UpdateBusinessSideDto;
import com.bilibili.cpt.platform.api.business_side.service.ICptUserService;
import com.bilibili.cpt.platform.api.location.dto.CptCycleDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceBusinessSidePreBookingDto;
import com.bilibili.cpt.platform.api.location.dto.SourceConfigDto;
import com.bilibili.cpt.platform.api.location.service.ICptCycleService;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.biz.dao.*;
import com.bilibili.cpt.platform.biz.po.CptBusinessSidePo;
import com.bilibili.cpt.platform.biz.po.CptBusinessSidePreBookingConfigPo;
import com.bilibili.cpt.platform.biz.po.CptBusinessSideRotationConfigPo;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.ssa.platform.api.business_side.service.ISsaBusinessSideService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;

import java.util.*;

public class BusinessSideServiceTest extends MockitoDefaultTest {
    @Mock
    private ICptSourceService cptSourceService;
    @Mock
    private IResourceBookingService resourceBookingService;
    @Mock
    private CptBusinessSideDao cptBusinessSideDao;
    @Mock
    private IQuerySourceService querySourceService;
    @Mock
    private CptBusinessSidePreBookingConfigDao cptBusinessSidePreBookingConfigDao;
    @Mock
    private ICptCycleService cycleService;
    @Mock
    private IGdLogService gdLogService;
    @Mock
    private LocalCptBusinessSideRotationConfigDao localCptBusinessSideRotationConfigDao;
    @Mock
    private BusinessSideDelegate businessSideDelegate;
    @Mock
    private CptBusinessSideRotationConfigDao cptBusinessSideRotationConfigDao;
    @Mock
    private ISoaQueryAccountService soaQueryAccountService;
    @Mock
    private LocalCptSourcePrivilegeDao localCptSourcePrivilegeDao;
    @Mock
    private ISsaBusinessSideService ssaBusinessSideService;
    @Mock
    private CptSourcePrivilegeDao cptSourcePrivilegeDao;
    @Mock
    private CptUserBusinessSideMappingDao cptUserBusinessSideMappingDao;
    @Mock
    private CptBusinessSideRedPacketDao cptBusinessSideRedPacketDao;
    @Mock
    private CptBusinessSideCashRechargeDao cptBusinessSideCashRechargeDao;
    @Mock
    private ICptUserService cptUserService;
    @Mock
    private LocalCptUserBusinessSideMappingDao localCptUserBusinessSideMappingDao;

    @InjectMocks
    private BusinessSideService businessSideService;

    private Operator operator;

    @Before
    public void before() {
        operator = Operator.builder().operatorName("test").operatorType(OperatorType.SYSTEM).build();

        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any());
    }

    @Test
    public void testAddBusinessSidePreBooking() {
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setId(1);
        businessSidePo.setName("测试业务方");
        businessSidePo.setStatus(Status.VALID.getCode());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(querySourceService.getCptSourceBySourceId(Mockito.any())).thenReturn(SourceAllInfoDto.builder()
                .sourceId(1)
                .name("测试资源位")
                .build());
        Mockito.when(cptBusinessSidePreBookingConfigDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(CptBusinessSidePreBookingConfigPo.builder()
                        .id(1)
                        .cycleId(1)
                        .sourceId(1)
                        .businessSideId(1)
                        .mRotationLimit(1)
                        .build()));
        Mockito.when(cptSourceService.getSourceConfigBySourceId(Mockito.anyInt(), Mockito.anyInt(), SalesType.CPT.getCode()))
                .thenReturn(SourceConfigDto.builder()
                        .mFreqLimit(10)
                        .rotationNum(1)
                        .build());
        Mockito.when(resourceBookingService.countBookingItem(Mockito.any())).thenReturn(0L);
        Mockito.when(cptBusinessSidePreBookingConfigDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(cptBusinessSidePreBookingConfigDao.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.when(cycleService.getCycleDtoById(Mockito.any())).thenReturn(CptCycleDto.builder().name("测试刊例").build());

        businessSideService.addBusinessSidePreBooking(operator, 1, 1, 1, 1);

        businessSideService.addBusinessSidePreBooking(operator, 2, 1, 1, 1);
    }

    @Test
    public void testDeleteBusinessSidePreBooking() {
        Mockito.when(cptBusinessSidePreBookingConfigDao.selectByPrimaryKey(Mockito.any()))
                .thenReturn(CptBusinessSidePreBookingConfigPo.builder()
                        .id(1)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .build());
        Mockito.when(querySourceService.getCptSourceBySourceId(Mockito.any())).thenReturn(SourceAllInfoDto.builder()
                .sourceId(1)
                .name("测试资源位")
                .build());
        Mockito.when(cptBusinessSidePreBookingConfigDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setId(1);
        businessSidePo.setName("测试业务方");
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(cycleService.getCycleDtoById(Mockito.any())).thenReturn(CptCycleDto.builder().name("测试刊例").build());

        businessSideService.deleteBusinessSidePreBooking(operator, 1);
    }

    @Test
    public void testGetBusinessSidePreBookingBySourceId() {
        Mockito.when(cptBusinessSidePreBookingConfigDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(CptBusinessSidePreBookingConfigPo.builder()
                        .id(1)
                        .cycleId(1)
                        .sourceId(1)
                        .businessSideId(1)
                        .mRotationLimit(1)
                        .build()));
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setId(1);
        businessSidePo.setName("测试业务方");
        Mockito.when(cptBusinessSideDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(businessSidePo));
        List<CptSourceBusinessSidePreBookingDto> result = businessSideService.getBusinessSidePreBookingBySourceId(operator, 1, 1);
        Assert.assertFalse(CollectionUtils.isEmpty(result));

        Mockito.when(cptBusinessSidePreBookingConfigDao.selectByExample(Mockito.any())).thenReturn(Collections.emptyList());
        result = businessSideService.getBusinessSidePreBookingBySourceId(operator, 1, 1);
        Assert.assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetCptSourceBusinessSidePreBookingDto() {
        Mockito.when(cptBusinessSidePreBookingConfigDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(CptBusinessSidePreBookingConfigPo.builder()
                        .id(1)
                        .cycleId(1)
                        .sourceId(1)
                        .businessSideId(1)
                        .mRotationLimit(1)
                        .build()));
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setId(1);
        businessSidePo.setName("测试业务方");
        Mockito.when(cptBusinessSideDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(businessSidePo));
        CptSourceBusinessSidePreBookingDto result = businessSideService.getCptSourceBusinessSidePreBookingDto(1, 1, 1);
        Assert.assertNotNull(result);
    }

    @Test
    public void testAddBusinessSideLimit() {
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setId(1);
        businessSidePo.setName("测试业务方");
        businessSidePo.setStatus(Status.VALID.getCode());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(querySourceService.getCptSourceBySourceId(Mockito.any())).thenReturn(SourceAllInfoDto.builder()
                .sourceId(1)
                .name("测试资源位")
                .build());
        Mockito.when(cycleService.getCycleDtoById(Mockito.any())).thenReturn(CptCycleDto.builder().name("测试刊例").build());
        Mockito.when(resourceBookingService.countBookingItem(Mockito.any())).thenReturn(0L);
        Mockito.when(localCptBusinessSideRotationConfigDao.save(Mockito.any())).thenReturn(1);
        businessSideService.addBusinessSideLimit(operator, 1, 1, 1, 1);
    }

    @Test
    public void testGetInternalBusinessSideAccountIds() {
        Set<Integer> result = businessSideService.getInternalBusinessSideAccountIds(Collections.emptyList());
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        Mockito.when(cptBusinessSideDao.selectByExample(Mockito.any())).thenReturn(Collections.emptyList());
        result = businessSideService.getInternalBusinessSideAccountIds(Lists.newArrayList(1));
        Assert.assertTrue(CollectionUtils.isEmpty(result));

        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setAccountId(1);
        Mockito.when(cptBusinessSideDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(businessSidePo));
        result = businessSideService.getInternalBusinessSideAccountIds(Lists.newArrayList(1));
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testCharge() {
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setVersion(1);
        businessSidePo.setCash(100L);
        businessSidePo.setRedPacket(100L);
        Mockito.when(businessSideDelegate.getById(Mockito.anyInt())).thenReturn(businessSidePo);
        Mockito.when(cptBusinessSideDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        businessSideService.charge(1, 1L, 1L);
    }

    @Test
    public void testDeleteBusinessSideLimit() {
        CptBusinessSideRotationConfigPo rotationConfigPo = new CptBusinessSideRotationConfigPo();
        rotationConfigPo.setStatus(Status.VALID.getCode());
        rotationConfigPo.setSourceId(1);
        rotationConfigPo.setCycleId(1);
        Mockito.when(cptBusinessSideRotationConfigDao.selectByPrimaryKey(Mockito.any())).thenReturn(rotationConfigPo);
        Mockito.when(cptBusinessSideRotationConfigDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        SourceAllInfoDto sourceAllInfo = new SourceAllInfoDto();
        Mockito.when(querySourceService.getCptSourceBySourceId(Mockito.anyInt())).thenReturn(sourceAllInfo);
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any());
        businessSideService.deleteBusinessSideLimit(operator, 1);
    }

    @Test
    public void testUpdateBusinessSideLimit() {
        CptBusinessSideRotationConfigPo rotationConfigPo = new CptBusinessSideRotationConfigPo();
        rotationConfigPo.setStatus(Status.VALID.getCode());
        rotationConfigPo.setSourceId(1);
        rotationConfigPo.setCycleId(1);
        rotationConfigPo.setBusinessSideId(1);
        rotationConfigPo.setMRotationLimit(2);
        Mockito.when(cptBusinessSideRotationConfigDao.selectByPrimaryKey(Mockito.any())).thenReturn(rotationConfigPo);
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setStatus(Status.VALID.getCode());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(cptBusinessSideRotationConfigDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(querySourceService.getCptSourceBySourceId(Mockito.anyInt())).thenReturn(new SourceAllInfoDto());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(cycleService.getCycleDtoById(Mockito.any())).thenReturn(new CptCycleDto());
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any());
        businessSideService.updateBusinessSideLimit(operator, 1, 1);
    }

    @Test
    public void testCreate() {
        Map<Integer, SourceAllInfoDto> cptSourceMap = new HashMap<>();
        Mockito.when(querySourceService.getCptSourceMapInSourceIds(Mockito.any())).thenReturn(cptSourceMap);
        Map<Integer, SourceAllInfoDto> sourceAllInfoDtoMap = new HashMap<>();
        sourceAllInfoDtoMap.put(1, new SourceAllInfoDto());
        Mockito.when(querySourceService.getCptSourceMapInSourceIds(Mockito.any())).thenReturn(sourceAllInfoDtoMap);
        Mockito.when(soaQueryAccountService.getAccountBaseDtoById(Mockito.any()))
                .thenReturn(AccountBaseDto.builder()
                        .build());
        Mockito.when(cptBusinessSideDao.countByExample(Mockito.any())).thenReturn(0L);
        Mockito.when(cptBusinessSideDao.insertSelective(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(localCptSourcePrivilegeDao).batchSave(Mockito.any());
        Mockito.when(ssaBusinessSideService.create(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any());
        businessSideService.create(operator, NewBusinessSideDto.builder()
                .name("test")
                .accountId(1)
                .departmentId(1)
                .logoColor("#FF0000")
                .type(1)
                .sourceIds(Lists.newArrayList(1))
                .build());
    }

    @Test
    public void testUpdate() {
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setAccountId(1);
        businessSidePo.setStatus(Status.VALID.getCode());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(cptSourcePrivilegeDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Map<Integer, SourceAllInfoDto> sourceAllInfoDtoMap = new HashMap<>();
        sourceAllInfoDtoMap.put(1, new SourceAllInfoDto());
        Mockito.when(querySourceService.getCptSourceMapInSourceIds(Mockito.any())).thenReturn(sourceAllInfoDtoMap);
        Mockito.doNothing().when(localCptSourcePrivilegeDao).batchSave(Mockito.any());
        Mockito.when(cptBusinessSideDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any());
        businessSideService.update(operator, UpdateBusinessSideDto.builder()
                .id(1)
                .name("test")
                .accountId(1)
                .departmentId(1)
                .logoColor("#FF0000")
                .sourceIds(Lists.newArrayList(1))
                .build());
    }

    @Test
    public void testDelete() {
        CptBusinessSidePo businessSidePo = new CptBusinessSidePo();
        businessSidePo.setAccountId(1);
        businessSidePo.setStatus(Status.VALID.getCode());
        Mockito.when(cptBusinessSideDao.selectByPrimaryKey(Mockito.any())).thenReturn(businessSidePo);
        Mockito.when(cptBusinessSideDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        Mockito.when(cptBusinessSideRotationConfigDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.when(cptUserBusinessSideMappingDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
        Mockito.doNothing().when(ssaBusinessSideService).delete(Mockito.any(), Mockito.anyInt());
        Mockito.doNothing().when(gdLogService).insertLog(Mockito.anyInt(), Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any());
        businessSideService.delete(operator, 1);
    }

    @Test
    public void testUpdateBusinessInitRedPacket() {
        businessSideService.updateBusinessInitRedPacket(operator, 1, 1L);
    }

    @Test
    public void testCashRecharge() {
        businessSideService.cashRecharge(operator, 1, 1L);
    }

    @Test
    public void testBindUserToBusinessSide() {
        businessSideService.bindUserToBusinessSide(operator, 1, Lists.newArrayList("test"));
    }

    @Test
    public void testUnbundledUser() {
        businessSideService.unbundledUser(operator, 1, 1);
    }

    @Test
    public void testGetAllInternalBusinessSideAccountIds() {
        Set<Integer> result = businessSideService.getAllInternalBusinessSideAccountIds();
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testGetAllExternalBusinessSideAccountIds() {
        Set<Integer> result = businessSideService.getAllExternalBusinessSideAccountIds();
        Assert.assertFalse(CollectionUtils.isEmpty(result));
    }
}
