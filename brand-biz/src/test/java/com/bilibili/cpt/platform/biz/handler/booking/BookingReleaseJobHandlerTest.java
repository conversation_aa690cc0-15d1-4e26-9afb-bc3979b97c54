package com.bilibili.cpt.platform.biz.handler.booking;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.booking.service.ISSABookingService;
import com.bilibili.brand.api.booking.service.ITopViewResourceService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

public class BookingReleaseJobHandlerTest extends AbstractMockitoTest {
    @Mock
    private IResourceBookingService resourceBookingService;
    @Mock
    private ISSABookingService ssaBookingService;
    @Mock
    private ITopViewResourceService topViewResourceService;
}
