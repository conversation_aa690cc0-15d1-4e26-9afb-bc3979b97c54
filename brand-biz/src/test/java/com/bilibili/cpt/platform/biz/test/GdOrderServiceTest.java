package com.bilibili.cpt.platform.biz.test;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.api.account.service.IIndustryCategoryService;
import com.bilibili.brand.api.common.enums.GdOrderResourceType;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.dto.*;
import com.bilibili.brand.api.order.service.IAdxOrderService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.order.dao.ExtGdOrderDao;
import com.bilibili.brand.biz.order.dao.FcOrderDao;
import com.bilibili.brand.biz.order.handler.CrmOrderHandler;
import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.crm.platform.soa.ISoaCrmOrderService;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.List;

/**
 * 请输入描述说明。
 *
 * <AUTHOR>
 * @since 2019年08月15日
 */
public class GdOrderServiceTest extends AbstractMockitoTest {

    @Mock
    private FcOrderDao fcOrderDao;
    @Mock
    private ExtGdOrderDao extGdOrderDao;
    @Mock
    private IQueryScheduleService querySheduleService;

    @Mock
    private ISoaCrmContractService crmContractService;

    @Mock
    private IIndustryCategoryService categoryService;

    @Mock
    private ISoaQueryAccountService queryAccountService;

    @Mock
    private IGdLogService gdLogService;

    @Mock
    private CrmOrderHandler crmOrderHandler;
    @Mock
    private ISoaCrmOrderService crmOrderService;
    @Mock
    private IBusinessSideService businessSideService;

    @Mock
    private IAdxOrderService adxOrderService;

    @InjectMocks
    private GdOrderService gdOrderService;

    @Test
    public void createOrder() throws Exception {
        Mockito.when(fcOrderDao.insertSelective(Mockito.any()))
                .thenAnswer(answer -> BeanTestUtils.initSimpleFields(answer.getArgument(0, FcOrderPo.class))
                        .getOrderId());

        Integer order = gdOrderService.createOrder(BeanTestUtils.initSimpleFields(NewOrderDto.builder()
                .orderName("闪屏CPT订单勿动")
                .crmContractNumber(10001905210000L)
                .resourceType(GdOrderResourceType.SALES.getCode())
                .product(OrderProduct.SSA_CPT.getCode())
                .build()), operator);
        Assert.assertNotNull(order);
        Assert.assertTrue(order > 0);
    }

    @Test
    public void createPDBOrder() throws Exception {
        Mockito.when(fcOrderDao.insertSelective(Mockito.any()))
                .thenAnswer(answer -> BeanTestUtils.initSimpleFields(answer.getArgument(0, FcOrderPo.class))
                        .getOrderId());

        NewPDBOrderResult order = gdOrderService.createPDBOrder(BeanTestUtils.initSimpleFields(NewPDBOrderDto.builder()
                .orderName("闪屏CPT订单勿动")
                .crmContractNumber(10001905210000L)
                .resourceType(GdOrderResourceType.SALES.getCode())
                .build()), operator);
        Assert.assertNotNull(order);
        Assert.assertTrue(order.getGdOrderId() > 0);
    }

    @Test
    public void queryOrders() throws Exception {
        List<GdOrderDto> orderDtos = gdOrderService.queryOrders(BeanTestUtils.initSimpleFields(QueryOrderParamDto.builder()
                .orderName("闪屏CPT订单勿动")
                .build()));
        print(orderDtos);
        Assert.assertNotNull(orderDtos);
        Assert.assertFalse(orderDtos.isEmpty());
    }

    @Test
    public void updateOrder() throws Exception {
        gdOrderService.updateOrder(BeanTestUtils.initSimpleFields(UpdateOrderDto.builder()
                .orderName("闪屏CPT订单勿动")
                .build()), operator);
    }

    @Test
    public void updateOrder2() throws Exception {
        gdOrderService.updateOrder(1, Utils.getToday(), Utils.getNow(), 1L, 100L);
    }

    @Test
    public void updatePDBOrder() throws Exception {
        gdOrderService.updatePDBOrder(BeanTestUtils.initSimpleFields(UpdatePDBOrderDto.builder()
                .orderName("闪屏CPT订单勿动")
                .build()), operator);
    }

    @Test
    public void queryOrdersByOperator() throws Exception {
        List<GdOrderDto> orderDtos = gdOrderService.queryOrders(BeanTestUtils.initSimpleFields(QueryOrderParamDto.builder()
                .orderName("闪屏CPT订单勿动")
                .build()), operator);

        print(orderDtos);
        Assert.assertNotNull(orderDtos);
        Assert.assertFalse(orderDtos.isEmpty());
    }

}