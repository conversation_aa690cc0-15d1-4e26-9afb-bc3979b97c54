package com.bilibili.brand.biz.schedule.handler;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.brand.api.account.service.IAccountGdQuotaService;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.api.booking.service.ITopViewBookingService;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.schedule.dto.*;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.schedule.dao.*;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenSchduleService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 查询排期测试。
 *
 * <AUTHOR>
 * @since 2019年07月23日
 */
public class ScheduleDateHandlerTest extends MockitoDefaultTest {

    @Mock
    private GdScheduleDateDao gdScheduleDateDao;
    @Mock
    private GdFlowAllocationDao gdFlowAllocationDao;

    @InjectMocks
    ScheduleDateHandler scheduleDateHandler;

    @Test
    public void getScheduleDatesInScheduleIds() throws ServiceException {
        List<GdScheduleDateDto> scheduleDateDtos = scheduleDateHandler
                .getScheduleDatesInScheduleIds(Lists.newArrayList(1));
        Assert.assertNotNull(scheduleDateDtos);
    }
}