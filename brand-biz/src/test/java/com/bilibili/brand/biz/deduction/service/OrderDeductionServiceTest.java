package com.bilibili.brand.biz.deduction.service;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.brand.api.account.dto.WalletDto;
import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.brand.biz.deduction.dao.GdFeeDeductionInfoDao;
import com.bilibili.brand.biz.deduction.handler.OrderDeductionHandler;
import com.bilibili.brand.biz.deduction.po.GdFeeDeductionInfoPo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OrderDeductionServiceTest extends AbstractMockitoTest {

    @Mock
    private OrderDeductionHandler cptOrderDeductionHandler;
    @Mock
    private OrderDeductionHandler ssaCptOrderDeductionHandler;
    @Mock
    private IQueryAccountService queryAccountService;
    @Mock
    private IMailService mailService;
    @Mock
    private GdFeeDeductionInfoDao gdFeeDeductionInfoDao;

    @InjectMocks
    private OrderDeductionService orderDeductionService;

    @Test
    public void testDeductAllCptOrderFee() throws ServiceException {
        Mockito.doNothing().when(cptOrderDeductionHandler).deductFee(Mockito.any());
        Mockito.doNothing().when(ssaCptOrderDeductionHandler).deductFee(Mockito.any());
        Mockito.when(gdFeeDeductionInfoDao.selectByExample(Mockito.any()))
                .thenReturn(Lists.newArrayList(GdFeeDeductionInfoPo.builder()
                        .accountId(1)
                        .build()));
        Map<Integer, WalletDto> walletDtoMap = new HashMap<>();
        walletDtoMap.put(1, WalletDto.builder().accountId(1).cash(new BigDecimal(0)).build());
        Mockito.when(queryAccountService.getWalletDtoMap(Mockito.any())).thenReturn(walletDtoMap);
        Map<Integer, List<String>> userMap = new HashMap<>();
        userMap.put(1, Lists.newArrayList("test"));
        Mockito.when(queryAccountService.getBiliUserMapByAccoungIds(Mockito.any())).thenReturn(userMap);
        Mockito.doNothing().when(mailService).send(Mockito.any());

        orderDeductionService.deductAllCptOrderFee(Utils.getToday());
    }

    @Test
    public void testHandleFailDeduction() {
        Mockito.doNothing().when(cptOrderDeductionHandler).handleFailDeduction();
        orderDeductionService.handleFailDeduction();
    }
}
