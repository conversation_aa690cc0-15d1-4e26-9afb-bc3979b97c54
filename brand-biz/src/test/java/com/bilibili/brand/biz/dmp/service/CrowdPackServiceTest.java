package com.bilibili.brand.biz.dmp.service;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.account.service.IAccountGroupService;
import com.bilibili.brand.biz.resource.res_dao.ResAccountGroupCrowdPackGroupMappingDao;
import com.bilibili.dmp.service.ISoaGroupService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class CrowdPackServiceTest extends AbstractMockitoTest {
    @Mock
    private IAccountGroupService accountGroupService;

    @Mock
    private ISoaGroupService dmpCrowdPackService;

    @Mock
    private ResAccountGroupCrowdPackGroupMappingDao groupCrowdPackGroupMappingDao;

    @InjectMocks
    private ScheduleCrowdPackService crowdPackService;

    @Test
    public void getCrowdPackDtosByAccountIdDirectly() throws ServiceException {
        crowdPackService.getCrowdPackDtosByAccountIdDirectly(1);
    }

    @Test
    public void getCrowdPackNameMapInIds() {
    }
}
