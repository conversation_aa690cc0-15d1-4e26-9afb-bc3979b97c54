package com.bilibili.brand.biz.soa.service;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.api.booking.service.ITopViewBookingService;
import com.bilibili.brand.api.creative.dto.*;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenBaseImageService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenCustomUrlService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenSchduleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenVideoService;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.google.common.collect.Lists;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.util.CollectionUtils;

public class SoaTopViewCreativeServiceTest extends AbstractMockitoTest {
    @Mock
    private ITopViewCreativeService topViewCreativeService;
    @Mock
    private ISsaSplashScreenCustomUrlService ssaSplashScreenCustomUrlService;
    @Mock
    private IBusinessSideService businessSideService;
    @Mock
    private IGdOrderService gdOrderService;
    @Mock
    private IQueryScheduleService queryScheduleService;
    @Mock
    private ITopViewBookingService topViewBookingService;
    @Mock
    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;
    @Mock
    private ISsaUposVideoService ssaUposVideoService;
    @Mock
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;
    @Mock
    private ISsaSplashScreenSchduleService ssaSplashScreenSchduleService;

    @InjectMocks
    private SoaTopViewCreativeService soaTopViewCreativeService;

    @Test
    public void testAuditPass() throws ServiceException {
        soaTopViewCreativeService.auditPass(Lists.newArrayList(TopViewAuditDto.builder()
                .topViewId(1)
                .version(1)
                .reason("通过")
                .build()), operator);
    }

    @Test
    public void testAuditReject() {
        soaTopViewCreativeService.auditReject(Lists.newArrayList(TopViewAuditDto.builder()
                .topViewId(1)
                .version(1)
                .reason("拒绝")
                .build()), operator);
    }

    @Test
    public void testQueryForPage() {
        Mockito.when(topViewCreativeService.queryForPage(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(new PageResult<>(1, Lists.newArrayList(TopViewCreativeListDto.builder()
                        .topViewDto(BeanTestUtils.initSimpleFields(GdTopViewDto.builder().build()))
                        .ssaCreativeDto(BeanTestUtils.initSimpleFields(SsaSplashScreenDto.builder().build()))
                        .hfIosCreativeDto(BeanTestUtils.initSimpleFields(GdCreativeDto.builder().build()))
                        .hfAndroidCreativeDto(BeanTestUtils.initSimpleFields(GdCreativeDto.builder().build()))
                        .build())));
        PageResult<SoaTopViewCreativeDto> result = soaTopViewCreativeService.queryForPage(SoaQueryTopViewCreativeDto.builder()
                .topViewId(1)
                .statusList(Lists.newArrayList(1))
                .startTime(Utils.getYesteday())
                .endTime(Utils.getToday())
                .build(), 1, 10);
        Assert.assertFalse(CollectionUtils.isEmpty(result.getRecords()));
    }
}
