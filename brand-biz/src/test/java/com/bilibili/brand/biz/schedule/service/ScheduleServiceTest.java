package com.bilibili.brand.biz.schedule.service;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.api.booking.dto.BookingItemDto;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.NewScheduleDto;
import com.bilibili.brand.api.schedule.dto.NewTopViewCptScheduleDto;
import com.bilibili.brand.api.schedule.dto.UpdateScheduleDto;
import com.bilibili.brand.api.schedule.dto.UpdateTopViewCptScheduleDto;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.cpt.platform.api.location.dto.CptSourceAllInfoDto;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.ssa.platform.biz.component.SsaLock;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.SsaScheduleServiceDelegate;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.redisson.api.RedissonClient;
import org.redisson.command.CommandExecutor;
import org.springframework.test.util.ReflectionTestUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

@Slf4j
public class ScheduleServiceTest extends AbstractMockitoTest {
    @Mock
    private IGdOrderService gdOrderService;
    @Mock
    private RedissonClient redissonClient;
    @Mock
    private ScheduleServiceDelegate scheduleServiceDelegate;
    @Mock
    private IGdCreativeService gdCreativeService;
    @Mock
    private SsaLock ssaLock;
    @Mock
    private ICptSourceService cptSourceService;
    @Mock
    private SsaScheduleServiceDelegate ssaScheduleServiceDelegate;
    @Mock
    private ISoaAppPackageService soaAppPackageService;
    @Mock
    private CommandExecutor commandExecutor;

    @Mock
    private IResourceBookingService bookingService;

    @InjectMocks
    private ScheduleService scheduleService;

    @Before
    public void before() {
        ReflectionTestUtils.setField(scheduleService, "topViewHomeFocusIosSourceId", 1);
        ReflectionTestUtils.setField(scheduleService, "topViewHomeFocusAndroidSourceId", 1);

        ReflectionTestUtils.setField(scheduleService, "topViewHomeFocusIosSourceId", 3145);
        ReflectionTestUtils.setField(scheduleService, "topViewHomeFocusAndroidSourceId", 3152);
        ReflectionTestUtils.setField(scheduleService, "newTopViewHomeFocusIosSourceId", 4334);
        ReflectionTestUtils.setField(scheduleService, "newTopViewHomeFocusAndroidSourceId", 4338);

        Map<Integer, List<CptSourceAllInfoDto>> map = new HashMap<>();
        map.put(3145, Lists.newArrayList(BeanTestUtils.initSimpleFields(CptSourceAllInfoDto.builder().build())));
        map.put(3152, Lists.newArrayList(BeanTestUtils.initSimpleFields(CptSourceAllInfoDto.builder().build())));
        map.put(4334, Lists.newArrayList(BeanTestUtils.initSimpleFields(CptSourceAllInfoDto.builder().build())));
        map.put(4338, Lists.newArrayList(BeanTestUtils.initSimpleFields(CptSourceAllInfoDto.builder().build())));
        Mockito.when(cptSourceService.getCptSourceAllInfoMapInSourceIds(any(), SalesType.CPT.getCode())).thenReturn(map);
        Mockito.when(bookingService.queryResourceBooking(any())).thenReturn(Lists.newArrayList(BeanTestUtils
                .initSimpleFields(BookingItemDto.builder().topViewSourceId(4334).cptScheduleId(0).build())));

    }

    @Test
    public void testCreateTopViewCptSchedule() {

        scheduleService.createTopViewCptSchedule(
                BeanTestUtils.initSimpleFields(NewTopViewCptScheduleDto.builder()
                        .promotionPurposeType(2)
                        .ssaScreenStyle(2)
                        .ssaShowStyle(3)
                        .build()),
                operator);
    }

    @Test
    public void testUpdateTopViewCptSchedule() {
        scheduleService.updateTopViewCptSchedule(
                BeanTestUtils.initSimpleFields(UpdateTopViewCptScheduleDto.builder()
                        .build()),
                operator);
    }

    @Test
    public void testWriteBackTopViewIdToSchedule() {
        scheduleService.writeBackTopViewIdToSchedule(1, 1, 0);
    }

    @Test
    public void testRemoveTopViewIdFromSchedule() {
        scheduleService.removeTopViewIdFromSchedule(1);
    }

    @Test
    public void updateSchedule() throws ServiceException {
        Mockito.when(scheduleServiceDelegate.getById(any())).thenReturn(GdSchedulePo.builder()
                .isTodaySchedule(0).status(1).beginDate(Timestamp.valueOf("2020-05-04 00:00:00"))
                .endDate(Timestamp.valueOf("2020-05-20 00:00:00"))
                .totalImpression(100)
                .splitDaysFlag(0).build());

        try {
            scheduleService.updateSchedule(UpdateScheduleDto.builder().name("45").requestId("4551")
                    .scheduleId(10021522)
                    .speedMode(1)
                    .totalImpression(100L)
                            .scheduleDates(Lists.newArrayList(Timestamp.valueOf("2020-05-19 00:00:00"))).build(),
                    operator);
        } catch (Exception e) {
            log.info("无法跳过redis锁的空指针，只能内部消化异常");
        }
    }

    @Test
    public void deleteSchedule() throws ServiceException {
        Mockito.when(scheduleServiceDelegate.getById(any())).thenReturn(GdSchedulePo.builder()
                .isTodaySchedule(0).status(1).beginDate(Timestamp.valueOf("2090-05-04 00:00:00")).endDate(Timestamp.valueOf("2090-05-20 00:00:00")).hour(8).build());

        Mockito.when(gdCreativeService.getGdCreativesByScheduleId(any())).thenReturn(new ArrayList<>());

        try {
            scheduleService.deleteSchedule(12,
                    operator);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("无法跳过redis锁的空指针，只能内部消化异常");
        }
    }

//    @Test
//    public void batchCreateSchedule() throws ServiceException {
//        Mockito.when(scheduleServiceDelegate.getById(any())).thenReturn(GdSchedulePo.builder()
//                .isTodaySchedule(0).status(1).beginDate(Timestamp.valueOf("2090-05-04 00:00:00")).endDate(Timestamp.valueOf("2090-05-20 00:00:00")).build());
//
//        try {
//            scheduleService.createGdSchedule(NewScheduleDto.builder().creativeStyle(4).name("45").build(),
//                    operator);
//        } catch (Exception e) {
//            log.info("无法跳过redis锁的空指针，只能内部消化异常");
//        }
//    }
}
