package com.bilibili.utils;

import com.bilibili.adp.common.util.Utils;
import org.junit.Assert;
import org.junit.Test;

import java.sql.Timestamp;

public class DateUtilTest {

    @Test
    public void getEarlierDayExcludeSame() {
        Timestamp timestamp = DateUtil.getEarlierDayExcludeSame(Utils.getToday(),Utils.getSomeDayAfterToday(1));
        Assert.assertEquals(timestamp, Utils.getToday());
    }

    @Test
    public void getLaterDay() {
        Timestamp timestamp = DateUtil.getLaterDay(Utils.getToday(),Utils.getSomeDayAfterToday(1));
        Assert.assertEquals(timestamp, Utils.getSomeDayAfterToday(1));
    }

    @Test
    public void getEarlierDayDay() {
        Timestamp timestamp = DateUtil.getEarlierDayDay(Utils.getToday(),Utils.getSomeDayAfterToday(1));
        Assert.assertEquals(timestamp, Utils.getToday());
    }
}