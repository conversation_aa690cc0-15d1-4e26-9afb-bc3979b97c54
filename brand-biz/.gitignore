#ignore target under current directory and all subdirectories
target/
*/target/
test-output/
*/test-output/

#ignore settings & gitignore file
.settings/
*/.settings/
.springBeans
*/.springBeans

#ignore svn files
.svn
*/.svn

.project

.metadata

*/.project

.classpath
*/.classpath

.data/
*/.data/
*~

.idea/
*.iml

#logs
*/*/logs/

### OSX template
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon  

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

### Eclipse template
*.pydevproject
.gradle
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# PDT-specific
.buildpath

# sbteclipse plugin
.target

# TeXlipse plugin
.texlipse

### JetBrains template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm

## Directory-based project format
/*.iml
# if you remove the above rule, at least ignore user-specific stuff:
# .idea/workspace.xml
# .idea/tasks.xml
# .idea/dictionaries
# and these sensitive or high-churn files:
# .idea/dataSources.ids
# .idea/dataSources.xml
# .idea/sqlDataSources.xml
# .idea/dynamic.xml
# and, if using gradle::
# .idea/gradle.xml
# .idea/libraries

## File-based project format
*.ipr
*.iws

## Additional for IntelliJ
out/

# generated by mpeltonen/sbt-idea plugin
.idea_modules/

# generated by JIRA plugin
atlassian-ide-plugin.xml

# generated by Crashlytics plugin (for Android Studio and Intellij)
com_crashlytics_export_strings.xml
account-biz/src/test/resources/crm-database.properties
account-biz/src/test/resources/database.properties
account-biz/src/test/resources/statsd.properties

# merge conflict original file
*.orig

# mvn change version left version backup file
*.versionsBackup
bfs-api/src/main/java/com/bilibili/adp/bfs/dto/BfsHeader.java
launch-biz/src/main/java/com/bilibili/adp/launch/biz/common/ObjectClassifier.java
launch-biz/src/main/resources/advertiser-platform.properties
launch-biz/src/main/resources/crm-database.properties
launch-biz/src/main/resources/database.properties
launch-biz/src/main/resources/statsd.properties
launch-biz/src/test/resources/statsd.properties
resource-biz/src/main/java/com/bilibili/adp/resource/biz/interceptor/MybatisInterceptor.java
resource-biz/src/test/resources/ad-manager.properties
