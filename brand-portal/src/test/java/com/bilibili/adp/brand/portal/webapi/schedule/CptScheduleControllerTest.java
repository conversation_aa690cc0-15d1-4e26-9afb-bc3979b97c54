package com.bilibili.adp.brand.portal.webapi.schedule;

import com.bilibili.adp.brand.portal.service.launch.WebAppPackageService;
import com.bilibili.adp.brand.portal.validator.schedule.PermissionValidator;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.NewSourceGroupCptScheduleVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.UpdateCptScheduleVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.brand.api.dmp.IScheduleCrowdPackService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.service.IScheduleService;
import com.bilibili.cpt.platform.api.schedule.service.ICptScheduleService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @since 2020年8月25日
 */
public class CptScheduleControllerTest extends MockitoDefaultTest {

    @Mock
    private ICptScheduleService cptScheduleService;

    @Mock
    private IScheduleService scheduleService;

    @Mock
    private IQueryScheduleService queryScheduleService;

    @Mock
    private IGdOrderService gdOrderService;

    @Mock
    private WebAppPackageService webAppPackageService;

    @Mock
    private PermissionValidator permissionValidator;

    @Mock
    private IScheduleCrowdPackService crowdPackService;

    @Mock
    private ISystemConfigService systemConfigService;

    @InjectMocks
    CptScheduleController cptScheduleController;

    private Context context;

    @Before
    public void before(){
        context = BeanTestUtils.initSimpleFields(new Context());
    }

    @Test
    public void createScheduleBySourceGroup() {
        cptScheduleController.createScheduleBySourceGroup(context, BeanTestUtils.initSimpleFields(NewSourceGroupCptScheduleVo.builder()
                .crowd_pack_ids(Lists.newArrayList(2373))
                .exclude_crowd_pack_ids(Lists.newArrayList(2374)).build()));
    }

    @Test
    public void update() {
        cptScheduleController.update(context, BeanTestUtils.initSimpleFields(UpdateCptScheduleVo.builder()
                .crowd_pack_ids(Lists.newArrayList(2373))
                .exclude_crowd_pack_ids(Lists.newArrayList(2374)).build()));
    }

    @Test
    public void updateScheduleStatus() throws ServiceException {
        cptScheduleController.updateScheduleStatus(context, Lists.newArrayList(1),1);
    }

    @Test
    public void deleteSchedule() throws ServiceException {
        cptScheduleController.deleteSchedule(context, Lists.newArrayList(1));
        cptScheduleController.deleteSchedule(context, Lists.newArrayList(1, 2, 3));
    }

    @Test
    public void getSchedule() throws ServiceException {
        cptScheduleController.getSchedule(context, 1);
    }

    @Test
    public void getCrowdPackage() throws ServiceException {
        cptScheduleController.getCrowdPackage(context);
    }

    @Test
    public void checkSourceBelongSmallYellow() throws ServiceException {
        cptScheduleController.checkSourceBelongSmallYellow(Lists.newArrayList(1));
    }
}