package com.bilibili.adp.brand.portal.validator;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.brand.portal.validator.schedule.impl.PermissionValidatorImpl;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.utils.SwitchUtil;
import com.bilibili.cpt.platform.common.CptCrowdPackType;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;

import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * Created by x<PERSON><PERSON><PERSON> on 2020/9/4 16:04
 *
 */
public class PermissionValidatorImplTest extends AbstractMockitoTest {
    @Mock
    private IQueryScheduleService queryScheduleService;

    @InjectMocks
    private PermissionValidatorImpl permissionValidator;

    @Test
    public void validatePermissionByScheduleIds() throws ServiceException {
        CptCrowdPackType.getByCode(1);
        permissionValidator.validatePermissionByScheduleIds(BeanTestUtils.initSimpleFields(new Context()),
                Lists.newArrayList(1));
    }

    @Test
    public void validatePermissionByScheduleId() throws ServiceException {
        permissionValidator.validatePermissionByScheduleId(BeanTestUtils.initSimpleFields(new Context()),
               1);
    }

}