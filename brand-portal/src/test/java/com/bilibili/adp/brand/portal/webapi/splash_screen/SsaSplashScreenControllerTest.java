//package com.bilibili.adp.brand.portal.webapi.splash_screen;
//
//import com.bilibili.AbstractMockitoTest;
//import com.bilibili.adp.brand.portal.service.launch.WebAppPackageService;
//import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.*;
//import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenRuleImageVo;
//import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SsaScheduleSelectVo;
//import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaNewSplashScreenVideoVo;
//import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaUpdateSplashScreenVideoVo;
//import com.bilibili.adp.common.bean.PageResult;
//import com.bilibili.adp.common.enums.SalesType;
//import com.bilibili.adp.common.exception.ServiceException;
//import com.bilibili.adp.web.framework.core.Context;
//import com.bilibili.adp.web.framework.core.Pagination;
//import com.bilibili.adp.web.framework.core.Response;
//import com.bilibili.bjcom.mock.BeanTestUtils;
//import com.bilibili.brand.api.creative.handler.CreativePreviewHandler;
//import com.bilibili.brand.api.creative.service.IGdCreativeService;
//import com.bilibili.brand.api.order.service.IGdOrderService;
//import com.bilibili.brand.api.schedule.dto.ScheduleDto;
//import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
//import com.bilibili.brand.biz.utils.OssStorageUtil;
//import com.bilibili.brand.biz.utils.SwitchUtil;
//import com.bilibili.brand.platform.report.api.dto.StatSplashScreenDto;
//import com.bilibili.brand.platform.report.api.service.IStatSplashScreenService;
//import com.bilibili.ssa.platform.api.log.service.ISsaLogService;
//import com.bilibili.ssa.platform.api.order.service.ISsaOrderService;
//import com.bilibili.ssa.platform.api.schedule.group.service.ISsaScheduleGroupService;
//import com.bilibili.ssa.platform.api.splash_screen.dto.*;
//import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenBaseImageService;
//import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenImageService;
//import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenOtherService;
//import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
//import com.bilibili.ssa.platform.common.enums.BaseImageTypeEnum;
//import com.bilibili.ssa.platform.common.enums.SsaScheduleStatus;
//import com.bilibili.ssa.platform.common.enums.SsaShowStyleType;
//import com.google.common.collect.Lists;
//import com.google.common.collect.Maps;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.springframework.test.util.ReflectionTestUtils;
//import org.springframework.util.CollectionUtils;
//import org.springframework.web.multipart.MultipartFile;
//
//import java.io.File;
//import java.io.IOException;
//import java.io.InputStream;
//import java.sql.Timestamp;
//import java.util.Collections;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.mockito.Matchers.any;
//import static org.mockito.Matchers.anyList;
//import static org.mockito.Mockito.when;
//
//public class SsaSplashScreenControllerTest extends AbstractMockitoTest {
//
//    @Mock
//    private ISsaSplashScreenService ssaSplashScreenService;
//    @Mock
//    private ISsaSplashScreenImageService ssaSplashScreenImageService;
//    @Mock
//    private ISsaOrderService ssaOrderService;
//
//    @Mock
//    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;
//    @Mock
//    private ISsaScheduleGroupService ssaScheduleGroupService;
//    @Mock
//    private IStatSplashScreenService statSplashScreenService;
//    @Mock
//    private WebAppPackageService webAppPackageService;
//    @Mock
//    private IQueryScheduleService gdScheduleService;
//    @Mock
//    private ISsaSplashScreenOtherService ssaSplashScreenOtherService;
//    @Mock
//    private IGdCreativeService gdCreativeService;
//    @Mock
//    private CreativePreviewHandler creativePreviewHandler;
//    @Mock
//    private IGdOrderService gdOrderService;
//    @Mock
//    private IQueryScheduleService queryScheduleService;
//    @Mock
//    private SwitchUtil switchUtil;
//    @Mock
//    private ISsaLogService ssaLogService;
//    @Mock
//    private OssStorageUtil ossStorageUtil;
//
//    @InjectMocks
//    private SsaSplashScreenController ssaSplashScreenController;
//
//    private Context context;
//
//    @Before
//    public void before() throws ServiceException {
//        context = new Context();
//        context.setAccountId(1);
//        ReflectionTestUtils.setField(ssaSplashScreenController, "creativePreviewMaxMinute", 20);
//
//        Map<Integer, String> gdOrderNameMap = Maps.newHashMap();
//        gdOrderNameMap.put(1, "测试单");
//        when(gdOrderService.getOrderId2NameMap(anyList())).thenReturn(gdOrderNameMap);
//
//        Map<Integer, String> gdScheduleMap = Maps.newHashMap();
//        gdScheduleMap.put(1, "测试排期");
//        when(queryScheduleService.getScheduleId2NameMapInIds(anyList())).thenReturn(gdScheduleMap);
//
//        Mockito.when(gdScheduleService.getScheduleById(any()))
//                .thenReturn(BeanTestUtils.initSimpleFields(ScheduleDto.builder()
//                        .scheduleId(1)
//                        .salesType(SalesType.SSA_GD.getCode())
//                        .beginDate(new Timestamp(System.currentTimeMillis()))
//                        .build()));
//
//        Mockito.when(ssaSplashScreenService.getSsaSplashScreens(any(), any(), any(), any()))
//                .thenReturn(PageResult.<SsaSplashScreenDto>builder()
//                        .total(1)
//                        .records(Lists.newArrayList(BeanTestUtils.initSimpleFields(SsaSplashScreenDto.builder()
//                                .id(1)
//                                .gdScheduleId(1)
//                                .salesType(SalesType.SSA_GD.getCode())
//                                .showStyle(SsaShowStyleType.FULL_SCREEN.getCode())
//                                .status(SsaScheduleStatus.AUDITED.getCode())
//                                .build())))
//                        .build());
//
//        Map<Integer, StatSplashScreenDto> screenStatMap = new HashMap<>(1);
//        screenStatMap.put(1, BeanTestUtils.initSimpleFields(StatSplashScreenDto.builder().build()));
//        Mockito.when(ssaSplashScreenImageService.getGdSplashScreenStatMap(any())).thenReturn(screenStatMap);
//
//        Map<Integer, SsaSplashScreenBaseImageDto> screenBaseImageDtoMap = new HashMap<>(1);
//        screenBaseImageDtoMap.put(1, BeanTestUtils.initSimpleFields(SsaSplashScreenBaseImageDto.builder()
//                .hash("hash")
//                .url("url")
//                .type(BaseImageTypeEnum.FULL_SCREEN.getCode())
//                .build()));
//        Mockito.when(ssaSplashScreenBaseImageService.getHorizontalImageMapBySplashScreenIds(any()))
//                .thenReturn(screenBaseImageDtoMap);
//
//        Map<Long, Timestamp> previewTimeMap = new HashMap<>(1);
//        previewTimeMap.put(1L, new Timestamp(System.currentTimeMillis()));
//        Mockito.when(gdCreativeService.getCreative2PreviewTimeMap(any())).thenReturn(previewTimeMap);
//
//        Mockito.doNothing().when(ssaSplashScreenService).addCreativePreview(any(), any());
//
//        Mockito.doNothing().when(ssaSplashScreenService).cancelCreativePreview(any(), any());
//    }
//
//    @Test
//    public void testGetSplashScreenList() throws ServiceException {
//        Response<Pagination<List<ExternalSplashScreenListVo>>> response =  ssaSplashScreenController
//                .getSplashScreenList(new Context(1, "AAA", 1, "BBB"),
//                        1, null, null, null, null,
//                        null, null, null,null, null, null);
//        Assert.assertSame("success", response.getStatus());
//        Assert.assertNotNull(response.getResult());
//        Assert.assertFalse(CollectionUtils.isEmpty(response.getResult().getData()));
//    }
//
//    @Test
//    public void testAddCreativePreview() throws ServiceException {
//        Response response = ssaSplashScreenController.addCreativePreview(context, 1);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void updateShowTime() throws ServiceException {
//        Response response = ssaSplashScreenController.updateShowTime(context,1, 2);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void getSplashScreenBaseInfoList(){
//        Response<List<SplashScreenBaseVo>> response = ssaSplashScreenController
//                .getSplashScreenBaseInfoList(context, 1, 1, 0, "");
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void testCancelCreativePreview() throws ServiceException {
//        Response response = ssaSplashScreenController.cancelCreativePreview(context, 1);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void createExternalSplashScreen() throws ServiceException {
//
//        Response<Integer> response = ssaSplashScreenController.createExternalSplashScreen(context,
//                BeanTestUtils.initSimpleFields(NewExternalSplashScreenVo.builder()
//                        .platform_switch(0)
//                        .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                                .end_time("2020-08-04 14:12:11").build()))
//                        .build()));
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//
//    @Test
//    public void createExternalSplashScreen1() throws ServiceException {
//        Response<Integer> response = ssaSplashScreenController.createExternalSplashScreen(context,
//                BeanTestUtils.initSimpleFields(NewExternalSplashScreenVo.builder()
//                        .platform_switch(1)
//                        .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                                .end_time("2020-08-04 14:12:11").build()))
//                        .build()));
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void updateSplashScreen() throws Exception {
//        Response<Integer> response = ssaSplashScreenController.updateSplashScreen(context,
//                BeanTestUtils.initSimpleFields(UpdateExternalSplashScreenVo.builder()
//                        .platform_switch(0)
//                        .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                                .end_time("2020-08-04 14:12:11").build()))
//                        .build()));
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void updateSplashScreen1() throws Exception {
//        Response<Integer> response = ssaSplashScreenController.updateSplashScreen(context,
//                BeanTestUtils.initSimpleFields(UpdateExternalSplashScreenVo.builder()
//                        .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                                .end_time("2020-08-04 14:12:11").build()))
//                        .platform_switch(1)
//                        .build()));
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void getSplashScreenDetail() throws Exception {
//        Mockito.when(ssaSplashScreenService
//                .getSsaSplashScreenDetailById(any())).thenReturn(BeanTestUtils.initSimpleFields(SsaSplashScreenDetailDto.builder()
//                .platformSwitch(1).salesType(41).splashScreenJumpDTOS(Lists.newArrayList(
//                        BeanTestUtils.initSimpleFields(SplashScreenJumpDTO.builder().platformId(1).build()),
//                        BeanTestUtils.initSimpleFields(SplashScreenJumpDTO.builder().platformId(2).build())))
//                .build()));
//        Response<SplashScreenDetailVo> response = ssaSplashScreenController.getSplashScreenDetail(1);
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void getSplashScreenDetail1() throws Exception {
//        Mockito.when(ssaSplashScreenService
//                .getSsaSplashScreenDetailById(any())).thenReturn(BeanTestUtils.initSimpleFields(SsaSplashScreenDetailDto.builder()
//                .platformSwitch(0).salesType(41).splashScreenJumpDTOS(Lists.newArrayList(
//                        BeanTestUtils.initSimpleFields(SplashScreenJumpDTO.builder().platformId(1).build()),
//                        BeanTestUtils.initSimpleFields(SplashScreenJumpDTO.builder().platformId(2).build())))
//                .build()));
//        Response<SplashScreenDetailVo> response = ssaSplashScreenController.getSplashScreenDetail(1);
//        Assert.assertNotNull(response);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void testCreateExternalSplashScreen() throws ServiceException {
//        Mockito.when(ssaSplashScreenService.saveExternalSsa(any(), any())).thenReturn(1);
//        Response<Integer> response = ssaSplashScreenController.createExternalSplashScreen(context,
//                NewExternalSplashScreenVo.builder()
//                .ssa_order_id(1)
//                .copy_writing("")
////                .customized_click_url("")
////                .customized_click_url_list(Lists.newArrayList(""))
////                .customized_imp_url("")
////                .customized_imp_url_list(Lists.newArrayList(""))
//                .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                        .end_time("2020-08-04 14:12:11").build()))
//                .title("测试")
//                .base_image_vos(Collections.emptyList())
//                .version_control(Collections.emptyList())
//                .splash_screen_jump_vos(Lists.newArrayList(BeanTestUtils.initSimpleFields(SplashScreenJumpVo.builder().build())))
//                .video(SsaNewSplashScreenVideoVo.builder().build())
//                .build());
//        Assert.assertSame(1, response.getResult());
//    }
//
//    @Test
//    public void testUpdateSplashScreen() throws IOException, ServiceException {
//        Mockito.when(ssaSplashScreenService.updateExternalSsa(any(), any())).thenReturn(1);
//        Response<Integer> response = ssaSplashScreenController.updateSplashScreen(context, UpdateExternalSplashScreenVo.builder()
//                .id(1)
//                .copy_writing("")
//                .title("")
//                .base_image_vos(Collections.emptyList())
//                .version_control(Collections.emptyList())
//                .schedules(Lists.newArrayList(BeanTestUtils.initSimpleFields(SsaScheduleSelectVo.builder().build())))
//                .splash_screen_jump_vos(Lists.newArrayList(BeanTestUtils.initSimpleFields(SplashScreenJumpVo.builder().build())))
//                .schedules(Lists.newArrayList(SsaScheduleSelectVo.builder().begin_time("2020-08-01 14:12:11")
//                        .end_time("2020-08-04 14:12:11").build()))
//                .video(SsaUpdateSplashScreenVideoVo.builder().build())
//                .show_style(1)
//                .build());
//        Assert.assertSame(1, response.getResult());
//    }
//
//    @Test
//    public void testGetSplashScreenDetail() throws ServiceException {
//        Mockito.when(ssaSplashScreenService.getSsaSplashScreenDetailById(Mockito.anyInt())).thenReturn(SsaSplashScreenDetailDto.builder()
//                .id(1)
//                .type(1)
//                .baseImageDtos(Collections.emptyList())
//                .status(1)
//                .salesType(41)
//                .ssaSplashScreenVersionControlDtos(Collections.emptyList())
//                .ssaSplashScreenVideoDto(SsaSplashScreenVideoDto.builder()
//                        .status(1)
//                        .build())
//                .splashScreenJumpDTOS(Lists.newArrayList(BeanTestUtils.initSimpleFields(SplashScreenJumpDTO.builder().build())))
//                .ssaSplashScreenImageDtos(Collections.emptyList())
//                .ssaScheduleDtos(Collections.emptyList())
//                .build());
//        Mockito.when(ssaOrderService.getSsaOrderId2NameMapInSsaOrderIds(any())).thenReturn(Collections.emptyMap());
//
//        Response<SplashScreenDetailVo> response = ssaSplashScreenController.getSplashScreenDetail(1);
//        Assert.assertNotNull(response.getResult());
//    }
//
//    @Test
//    public void getSplashScreenInternalList() throws ServiceException {
//        Response response = ssaSplashScreenController.getSplashScreenInternalList(context, 0 ,
//                "", 1, Lists.newArrayList(1), 1 ,15);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void saveRuleImage() throws ServiceException {
//        Response response = ssaSplashScreenController.saveRuleImage(context,
//                BeanTestUtils.initSimpleFields(SplashScreenRuleImageVo.builder().build()));
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void getUsedLaunchDate() throws ServiceException {
//        Response response = ssaSplashScreenController.getUsedLaunchDate();
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void queryLog() throws ServiceException {
//        Response response = ssaSplashScreenController.queryLog(1, 1, 15);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void ruleImageUpload() throws ServiceException, IOException {
//        Response response = ssaSplashScreenController.ruleImageUpload(BeanTestUtils.initSimpleFields(new MultipartFile() {
//            @Override
//            public String getName() {
//                return null;
//            }
//
//            @Override
//            public String getOriginalFilename() {
//                return "sfw";
//            }
//
//            @Override
//            public String getContentType() {
//                return null;
//            }
//
//            @Override
//            public boolean isEmpty() {
//                return false;
//            }
//
//            @Override
//            public long getSize() {
//                return 0;
//            }
//
//            @Override
//            public byte[] getBytes() throws IOException {
//                return new byte[0];
//            }
//
//            @Override
//            public InputStream getInputStream() throws IOException {
//                return null;
//            }
//
//            @Override
//            public void transferTo(File dest) throws IOException, IllegalStateException {
//
//            }
//        }), 1);
//        Assert.assertSame("success", response.getStatus());
//    }
//
//    @Test
//    public void baseImageUpload() throws ServiceException, IOException {
//        try {
//            Response response = ssaSplashScreenController.baseImageUpload(new MultipartFile() {
//                @Override
//                public String getName() {
//                    return "sf";
//                }
//
//                @Override
//                public String getOriginalFilename() {
//                    return "fafa";
//                }
//
//                @Override
//                public String getContentType() {
//                    return null;
//                }
//
//                @Override
//                public boolean isEmpty() {
//                    return false;
//                }
//
//                @Override
//                public long getSize() {
//                    return 0;
//                }
//
//                @Override
//                public byte[] getBytes() throws IOException {
//                    return new byte[0];
//                }
//
//                @Override
//                public InputStream getInputStream() throws IOException {
//                    return null;
//                }
//
//                @Override
//                public void transferTo(File dest) throws IOException, IllegalStateException {
//
//                }
//            }, 1);
//            Assert.assertSame("success", response.getStatus());
//        } catch (ServiceException e) {
//
//        } catch (IllegalStateException e) {
//        } catch (IOException e) {
//        }
//    }
//
//
//}
