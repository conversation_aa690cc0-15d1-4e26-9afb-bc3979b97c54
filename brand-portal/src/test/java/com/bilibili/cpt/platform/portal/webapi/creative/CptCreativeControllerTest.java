package com.bilibili.cpt.platform.portal.webapi.creative;

import com.bilibili.AbstractMockitoTest;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.biz.utils.SwitchUtil;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeAllInfoDto;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeDto;
import com.bilibili.cpt.platform.biz.service.creative.CptCreativeService;
import com.bilibili.cpt.platform.portal.webapi.creative.vo.NewBusinessCreativeVo;
import com.bilibili.cpt.platform.portal.webapi.creative.vo.UpdateCreativeVo;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

/**
 * Created by dailuwei on 2019/9/4 11:04
 *
 * <AUTHOR>
 */
public class CptCreativeControllerTest extends AbstractMockitoTest {
    @Mock
    private CptCreativeService cptCreativeService;
    @Mock
    private IQueryTemplateService queryTemplateService;
    private Context context;
    @Mock
    private IBusinessSideService businessSideService;
    @Mock
    private SwitchUtil switchUtil;
    @InjectMocks
    private  CptCreativeController cptCreativeController;
    @Before
    public void before() throws ServiceException {
        context = new Context();
        context.setAccountId(1);
    }
    @Test
    public void getAcutalLaunchUrl() {
        cptCreativeController.getAcutalLaunchUrl(GdJumpType.VIDEO_MOBILE,"asdf");
        cptCreativeController.getAcutalLaunchUrl(GdJumpType.GAME,"asdf");
    }

    @Test
    public void transferVideoIdFromWeb() {
        cptCreativeController.transferVideoIdToWeb(123L);
    }

    @Test
    public void transferVideoIdToWeb() {
        cptCreativeController.transferVideoIdFromWeb("123");
    }

    @Test
    public void getCreativeList() throws ServiceException {
        CptCreativeAllInfoDto cptCreativeAllInfoDto = new CptCreativeAllInfoDto();
        cptCreativeAllInfoDto.setOrderName("test");
        cptCreativeAllInfoDto.setAvid(123l);
        cptCreativeAllInfoDto.setBusinessNumber(123l);
        cptCreativeAllInfoDto.setButtonCopyId(10);
        cptCreativeAllInfoDto.setBusinessSideName("test");
        cptCreativeAllInfoDto.setCptCreativeStatus(1);
//        cptCreativeAllInfoDto.setCreativeTips();
        cptCreativeAllInfoDto.setPromotionPurposeContent("123");
        cptCreativeAllInfoDto.setScheduleEndTime("1971/02/03");
        cptCreativeAllInfoDto.setBeginTime(new Timestamp(1));
        cptCreativeAllInfoDto.setRotationNum(100);
        cptCreativeAllInfoDto.setCmMark(1);
        cptCreativeAllInfoDto.setScheduleStartTime("1971/02/03");
        cptCreativeAllInfoDto.setBusinessSideId(1);
        cptCreativeAllInfoDto.setCreativeId(1l);
        cptCreativeAllInfoDto.setCreativeName("test");
        cptCreativeAllInfoDto.setCreativeType(1);
        cptCreativeAllInfoDto.setCustomizedClickUrl("http://www.baidu.com");
        cptCreativeAllInfoDto.setDescription("tewst");
        cptCreativeAllInfoDto.setCustomizedImpUrl("http://123.jpg");
        cptCreativeAllInfoDto.setDisableCreativeId(123);
        cptCreativeAllInfoDto.setEndTime(new Timestamp(3));
        cptCreativeAllInfoDto.setExtDescription("detws");
        cptCreativeAllInfoDto.setExtImageMd5("asdf");
        cptCreativeAllInfoDto.setExtImageUrl("http://we.jpg");
        cptCreativeAllInfoDto.setImageMd5("asdf");
        cptCreativeAllInfoDto.setImageUrl("1234");
        cptCreativeAllInfoDto.setJumpType(2);
        cptCreativeAllInfoDto.setLocPageId(1);
        cptCreativeAllInfoDto.setLocPageName("test");
        cptCreativeAllInfoDto.setMgkPageId(1l);
        cptCreativeAllInfoDto.setPlatformId(1);
        cptCreativeAllInfoDto.setPlatformName("tyresd");
        cptCreativeAllInfoDto.setReason("test");
        cptCreativeAllInfoDto.setResourceId(1);
        cptCreativeAllInfoDto.setResourceName("test");
        cptCreativeAllInfoDto.setTemplateId(1);
        cptCreativeAllInfoDto.setScheduleId(1);
        cptCreativeAllInfoDto.setSourceId(1);
        cptCreativeAllInfoDto.setSourceName("terts");
        cptCreativeAllInfoDto.setTitle("test");
        cptCreativeAllInfoDto.setType(1);
        cptCreativeAllInfoDto.setVersion(1);
        cptCreativeAllInfoDto.setVideoId(1l);
        cptCreativeAllInfoDto.setVideoUrl("https://www.bilibili.com/video/av123213");
        PageResult<CptCreativeAllInfoDto> re = new PageResult<CptCreativeAllInfoDto>();
        re.setRecords(Arrays.asList(cptCreativeAllInfoDto));
        re.setTotal(1);

        when(cptCreativeService.getCptCreatives(any(),any(),any(),any())).thenReturn(re);
        Map<Integer, TemplateDto> templateMap = new HashMap<>();
        ButtonCopyDto buttonCopyDto = new ButtonCopyDto();
        buttonCopyDto.setType(1);
        TemplateDto templateDto = new TemplateDto().builder()
                .templateId(1)
                .templateName("teet")
                .isFillTitle(true)
                .titleMaxLength(1)
                .titleMinLength(1)
                .isFillDesc(true)
                .descMaxLength(1)
                .descMinLength(1)
                .isSupportImage(true)
                .isSupportExtImage(true)
                .extImageHeight(1)
                .extImageKbLimit(1)
                .extImageWidth(1)
                .imageWidth(1)
                .imageHeight(1)
                .imageKbLimit(1)
                .isSupportVideo(true)
                .isFillExtDesc(true)
                .extDescMaxLength(1)
                .extDescMinLength(1)
                .videoWidth(1)
                .videoHeight(1)
                .videoKbLimit(1)
                .videoDurationMax(1)
                .videoDurationMin(1)
                .isSupportVideoId(true)
                .html("asdf")
                .isSupportButton(true)
                .buttonCopyDtos(Arrays.asList(buttonCopyDto))
                .build();
        templateMap.put(1,templateDto);
        when(queryTemplateService.getTemplateMapInIds(any())).thenReturn(templateMap);

        cptCreativeController.getCreativeList(context,"test","1234",12, Arrays.asList(2),Arrays.asList(2),Arrays.asList(2),1l,2l,1,15);

    }
    @Test
    public void queryCreative() throws ServiceException {
        CptCreativeDto cptCreativeDto = new CptCreativeDto().builder()
                .cptCreativeStatus(1)
                .creativeId(1l)
                .scheduleId(1)
                .adMark("test")
                .beginTime(new Timestamp(1))
                .endTime(new Timestamp(1))
                .businessSideId(1)
                .buttonCopy("test")
                .buttonCopyId(1)
                .cmMark(1)
                .creativeName("test")
                .creativeType(1)
                .customizedClickUrl("test")
                .customizedImpUrl("teset")
                .description("test")
                .disableCreativeId(1l)
                .extDescription("terst")
                .extImageHash("test")
                .extImageMd5("test")
                .imageHash("asdf")
                .imageMd5("1234")
                .imageUrl("1234")
                .invitationAvid(1l)
                .isIdfaEncrypted(1)
                .isOnline(true)
                .creativeName("test")
                .extImageUrl("tst")
                .jumpType(1)
                .LocPageId(1)
                .locPageName("terst")
                .mgkPageId(1l)
                .orderId(1)
                .platformId(1)
                .platformName("test")
                .promotionPurposeContent("asdf")
                .reason("trest")
                .resourceId(1)
                .resourceName("test")
                .schemeUrl("asdf")
                .sourceId(1)
                .sourceName("test")
                .templateId(1)
                .title("tet")
                .version(1)
                .videoHash("fsadf")
                .videoId(1l)
                .videoUrl("tesrt")
                .build();
        ButtonCopyDto buttonCopyDto = new ButtonCopyDto();
        buttonCopyDto.setType(1);
        TemplateDto templateDto = new TemplateDto().builder()
                .templateId(1)
                .templateName("teet")
                .isFillTitle(true)
                .titleMaxLength(1)
                .titleMinLength(1)
                .isFillDesc(true)
                .descMaxLength(1)
                .descMinLength(1)
                .isSupportImage(true)
                .isSupportExtImage(true)
                .extImageHeight(1)
                .extImageKbLimit(1)
                .extImageWidth(1)
                .imageWidth(1)
                .imageHeight(1)
                .imageKbLimit(1)
                .isSupportVideo(true)
                .isFillExtDesc(true)
                .extDescMaxLength(1)
                .extDescMinLength(1)
                .videoWidth(1)
                .videoHeight(1)
                .videoKbLimit(1)
                .videoDurationMax(1)
                .videoDurationMin(1)
                .isSupportVideoId(true)
                .html("asdf")
                .isSupportButton(true)
                .buttonCopyDtos(Arrays.asList(buttonCopyDto))
                .build();

        when(cptCreativeService.getCreativeByIdWithoutOperator(any())).thenReturn(cptCreativeDto);
        when(queryTemplateService.getTemplateById(any())).thenReturn(templateDto);
        BusinessSideBaseDto businessSide = new BusinessSideBaseDto().builder()
                .name("test")
                .type(1)
                .accountId(1)
                .id(1)
                .isAdmin(1)
                .status(1)
                .cash(1l)
                .logoColor("test")
                .redPacket(1l)
                .build();
        when(businessSideService.getBusinessSideById(any())).thenReturn(businessSide);
        cptCreativeController.queryCreative(context,"1");

    }
    @Test
    public void createBusinessCreative() throws ServiceException {
        NewBusinessCreativeVo newCreativeVo = new NewBusinessCreativeVo().builder()
                .button_copy("test")
                .button_copy_id(1)
                .cm_mark(1)
                .creative_name("tst")
                .customized_click_url("asddf")
                .customized_imp_url("q23er")
                .description("test")
                .end_time(1l)
                .ext_description("tst")
                .ext_image_hash("asdf")
                .ext_image_url("asdf")
                .image_hash("dasf")
                .image_url("dfasdf")
                .is_idfa_encrypted(1)
                .jump_type(1)
                .order_id(1)
                .platform_id(1)
                .promotion_purpose_content("asdf")
                .scheme_url("dfad")
                .source_id(1)
                .start_time(1l)
                .template_id(1)
                .title("test")
                .video_hash("asdf")
                .video_id("1234")
                .video_url("asdf")
                .build();
                when(cptCreativeService.createBusinessCreative(any(),any())).thenReturn(1l);
                cptCreativeController.createBusinessCreative(context,newCreativeVo);
    }
    @Test
    public void updateCreative() throws ServiceException {
        UpdateCreativeVo updateCreativeVo = new UpdateCreativeVo().builder()
                .button_copy("test")
                .creative_id("12")
                .button_copy_id(1)
                .cm_mark(1)
                .creative_name("tst")
                .customized_click_url("asddf")
                .customized_imp_url("q23er")
                .description("test")
                .end_time(1l)
                .ext_description("tst")
                .ext_image_hash("asdf")
                .ext_image_url("asdf")
                .image_hash("dasf")
                .image_url("dfasdf")
                .is_idfa_encrypted(1)
                .jump_type(1)
                .promotion_purpose_content("asdf")
                .scheme_url("dfad")
                .start_time(1l)
                .title("test")
                .video_hash("asdf")
                .video_id("1234")
                .video_url("asdf")
                .build();
        doNothing().when(cptCreativeService).update(any(), any());
        cptCreativeController.updateCreative(context,updateCreativeVo);
    }
}