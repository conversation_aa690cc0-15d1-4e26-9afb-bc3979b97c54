package com.bilibili;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.BeforeClass;
import org.junit.Test;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-09
 **/
@Slf4j
public class AbstractMockitoTest extends MockitoDefaultTest {

    protected static Operator operator;

    @BeforeClass
    public static void setUp() {
        operator = Operator.builder().operatorId(10005).operatorName("cuihaichuan").systemType(SystemType.AGENT).operatorType(OperatorType.OPERATING_PERSONNEL).ip("666.666.666.666").build();
    }

    @Test
    public void deafult() {
        this.print(operator);
    }

    protected void print(Object obj) {
        System.out.println(JSON.toJSONString(obj));
    }
}