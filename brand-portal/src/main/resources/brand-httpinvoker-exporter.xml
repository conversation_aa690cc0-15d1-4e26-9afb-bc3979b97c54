<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean name="/service/gdScheduleService" id="gdScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="gdScheduleService" class="com.bilibili.brand.biz.schedule.soa.SoaGdScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.schedule.soa.service.ISoaGdScheduleService"/>
    </bean>

    <bean name="/service/gdOrderService" id="gdOrderServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="gdOrderService" class="com.bilibili.brand.biz.soa.service.SoaGdOrderService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaGdOrderService"/>
    </bean>


    <bean name="/service/gdCreativeService" id="gdCreativeServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="gdCreativeService" class="com.bilibili.brand.biz.soa.service.SoaGdCreativeService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaGdCreativeService"/>
    </bean>

    <bean name="/service/brandScheduleService" id="brandScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="brandScheduleService" class="com.bilibili.brand.biz.soa.service.SoaBrandScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaBrandScheduleService"/>
    </bean>

    <bean name="/service/stockService" id="stockServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="stockService" class="com.bilibili.brand.biz.soa.service.SoaStockService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaStockService"/>
    </bean>

    <bean name="/service/resAreaGroupService" id="resAreaGroupServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="resAreaGroupService" class="com.bilibili.brand.biz.soa.service.SoaResAreaGroupService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaResAreaGroupService"/>
    </bean>

    <bean name="/service/targetService" id="targetServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="targetService" class="com.bilibili.brand.biz.soa.service.SoaTargetService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaTargetService"/>
    </bean>

    <bean name="/service/targetItemService" id="targetItemServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="targetItemService" class="com.bilibili.brand.biz.soa.service.SoaResTargetItemService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaResTargetItemService"/>
    </bean>

    <!--cpt-->
    <bean name="/service/cptLogService" id="cptLogServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptLogService" class="com.bilibili.cpt.platform.biz.service.log.CptLogOperationService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.api.log.service.ICptLogService"/>
    </bean>
    <bean name="/service/cptCreativeService" id="cptCreativeServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptCreativeService" class="com.bilibili.cpt.platform.biz.service.creative.CptCreativeService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.api.creative.service.ICptCreativeService"/>
    </bean>
    <bean name="/service/soaCptCreativeService" id="soaCptCreativeServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaCptCreativeService" class="com.bilibili.cpt.platform.biz.soa.SoaCptCreativeService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.soa.ISoaCptCreativeService"/>
    </bean>
    <bean name="/service/cptSourceService" id="cptSourceServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptSourceService" class="com.bilibili.cpt.platform.biz.service.location.CptSourceService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.api.location.service.ICptSourceService"/>
    </bean>
    <bean name="/service/cptSourceGroupService" id="cptSourceGroupServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptSourceGroupService" class="com.bilibili.cpt.platform.biz.service.location.CptSourceGroupService"/>
        </property>
        <property name="serviceInterface"
                  value="com.bilibili.cpt.platform.api.location.service.ICptSourceGroupService"/>
    </bean>
    <bean name="/service/cptBusinessSideService" id="cptBusinessSideServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptBusinessSideService" class="com.bilibili.cpt.platform.biz.service.business_side.BusinessSideService"/>
        </property>
        <property name="serviceInterface"
                  value="com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService"/>
    </bean>
    <bean name="/service/cptUserService" id="cptUserServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptUserService" class="com.bilibili.cpt.platform.biz.service.business_side.CptUserService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.api.business_side.service.ICptUserService"/>
    </bean>

    <bean name="/service/cptScheduleService" id="cptScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptScheduleService" class="com.bilibili.cpt.platform.biz.soa.SoaCptScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.soa.ISoaCptScheduleService"/>
    </bean>

    <bean name="/service/cptScheduleServiceV2" id="cptScheduleServiceExporterV2"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean class="com.bilibili.brand.biz.schedule.soa.SoaCptScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.schedule.soa.service.ISoaCptScheduleService"/>
    </bean>

    <bean name="/service/cptCycleService" id="cptCycleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptCycleService" class="com.bilibili.cpt.platform.biz.soa.SoaCptCycleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.soa.ISoaCptCycleService"/>
    </bean>

    <bean name="/service/cptOrderService" id="cptOrderServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="cptOrderService" class="com.bilibili.cpt.platform.biz.soa.SoaCptOrderService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.soa.ISoaCptOrderService"/>
    </bean>

    <!--===============================SSA===============================================================-->
    <!-- 通过Spring HttpInvoker机制暴露远程访问服务 -->
    <bean name="/service/ssaLogService" id="ssaLogServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaLogService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaLogOperationServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaLogService"/>
    </bean>
    <bean name="/service/ssaSourceService" id="ssaSourceServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaSourceService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaSourceServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaSourceService"/>
    </bean>
    <bean name="/service/ssaSourceGroupService" id="ssaSourceGroupServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaSourceGroupService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaSourceGroupServiceImpl"/>
        </property>
        <property name="serviceInterface"
                  value="com.bilibili.ssa.platform.soa.ISoaSsaSourceGroupService"/>
    </bean>
    <bean name="/service/ssaBusinessSideService" id="ssaBusinessSideServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaBusinessSideService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaBusinessSideServiceImpl"/>
        </property>
        <property name="serviceInterface"
                  value="com.bilibili.ssa.platform.soa.ISoaSsaBusinessSideService"/>
    </bean>

    <bean name="/service/ssaCycleService" id="ssaCycleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaCycleService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaCycleServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaCycleService"/>
    </bean>

    <bean name="/service/ssaOrderService" id="ssaOrderServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaOrderService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaOrderServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaOrderService"/>
    </bean>

    <bean name="/service/ssaScheduleService" id="ssaScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaScheduleService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaScheduleServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaScheduleService"/>
    </bean>

    <bean name="/service/ssaSplashScreenService" id="ssaSplashScreenServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaScheduleService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaSplashScreenServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaSplashScreenService"/>
    </bean>

    <bean name="/service/ssaScheduleBrandMappingService" id="ssaScheduleBrandMappingServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="ssaScheduleBrandMappingService" class="com.bilibili.ssa.platform.biz.soa.SoaSsaScheduleBrandMappingService"></bean>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaScheduleBrandMappingService"/>
    </bean>

    <bean name="/service/soaUposVideoService" id="soaUposVideoServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaUposVideoService" class="com.bilibili.ssa.platform.biz.soa.SoaUposVideoServiceImpl"></bean>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaUposVideoService"/>
    </bean>

    <bean name="/service/soaBackdoorGdFlowAllocationService" id="soaBackdoorGdFlowAllocationService"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaBackdoorGdFlowAllocationService" class="com.bilibili.brand.biz.soa.service.SoaBackdoorGdFlowAllocationService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaBackdoorGdFlowAllocationService"/>
    </bean>

    <bean name="/service/soaTopViewScheduleService" id="soaTopViewScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaTopViewScheduleService" class="com.bilibili.brand.biz.schedule.soa.SoaTopViewScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.schedule.soa.service.ISoaTopViewScheduleService"/>
    </bean>

    <bean name="/service/soaTopViewCreativeService" id="soaTopViewCreativeServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaTopViewCreativeService" class="com.bilibili.brand.biz.soa.service.SoaTopViewCreativeService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.creative.soa.ISoaTopViewCreativeService"/>
    </bean>
    <bean name="/service/soaTopViewLogService" id="soaTopViewLogServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaTopViewLogService" class="com.bilibili.brand.biz.soa.service.SoaTopViewLogService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.log.service.ISoaTopViewLogService"/>
    </bean>
    <bean name="/service/soaTopViewSourceService" id="soaTopViewServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaTopViewSourceService" class="com.bilibili.ssa.platform.biz.soa.SoaTopViewSourceServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaTopViewSourceService"/>
    </bean>
    <bean name="/service/soaTopViewBusinessSideService" id="soaTopViewBusinessSideServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="soaTopViewBusinessSideService" class="com.bilibili.ssa.platform.biz.soa.SoaTopViewBusinessSideServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaTopViewBusinessSideService"/>
    </bean>

    <bean name="/service/bookingMappingService" id="bookingMappingServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="bookingMappingService" class="com.bilibili.cpt.platform.biz.soa.SoaCptUniteBookingService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.cpt.platform.soa.ISoaCptUniteBookingMappingService"/>
    </bean>

    <bean name="/service/statCreativeService" id="statCreativeServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean id="statCreativeService" class="com.bilibili.brand.biz.soa.service.SoaStatCreativeService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaStatCreativeService"/>
    </bean>

    <bean name="/service/gdPriceService" id="gdPriceServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean class="com.bilibili.brand.biz.soa.service.SoaGdPriceService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.soa.service.ISoaGdPriceService"/>
    </bean>

    <bean name="/service/adxOrderService" id="adxOrderServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean class="com.bilibili.brand.biz.order.service.AdxOrderService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.order.service.IAdxOrderService"/>
    </bean>

    <bean name="/service/ssaPdScheduleService" id="ssaPdScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean class="com.bilibili.ssa.platform.biz.soa.SoaSsaPdScheduleServiceImpl"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.ssa.platform.soa.ISoaSsaPdScheduleService"/>
    </bean>

    <bean name="/service/pdScheduleService" id="pdScheduleServiceExporter"
          class="org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter">
        <property name="service">
            <bean class="com.bilibili.brand.biz.schedule.soa.SoaPdScheduleService"/>
        </property>
        <property name="serviceInterface" value="com.bilibili.brand.api.schedule.soa.service.ISoaPdScheduleService"/>
    </bean>

</beans>
