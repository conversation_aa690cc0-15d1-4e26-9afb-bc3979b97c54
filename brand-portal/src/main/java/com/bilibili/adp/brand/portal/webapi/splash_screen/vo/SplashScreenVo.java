package com.bilibili.adp.brand.portal.webapi.splash_screen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenVo {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增ID")
    private Integer id;

    /**
     * 刊例周期id
     */
    @ApiModelProperty(value = "刊例周期id")
    private Integer cycle_id;

    /**
     * 业务方id
     */
    @ApiModelProperty(value = "业务方id")
    private Integer business_side_id;

    /**
     * ssa订单ID
     */
    @ApiModelProperty(value = "ssa订单ID")
    private Integer ssa_order_id;

    /**
     * 闪屏类型: 0-默认 1-运营 2-生日 3-VIP
     */
    @ApiModelProperty(value = "闪屏类型: 0-默认 1-运营 2-生日 3-VIP")
    private Integer type;

    /**
     * 闪屏标题
     */
    @ApiModelProperty(value = "闪屏标题")
    private String title;

    /**
     * 闪屏文案
     */
    @ApiModelProperty(value = "闪屏文案")
    private String copy_writing;

    /**
     * 展示时长
     */
    @ApiModelProperty(value = "展示时长")
    private Integer show_time;


    /**
     * 展示样式: 1-全屏 2-半屏
     */
    @ApiModelProperty(value = "展示样式: 1-全屏 2-半屏")
    private Integer show_style;

    /**
     * 是否可跳过: 0-否 1-是
     */
    @ApiModelProperty(value = "是否可跳过: 0-否 1-是")
    private Integer is_skip;

    /**
     * 跳转类型: 1-链接 2-视频 3-番剧 4-直播 5-游戏中心
     */
    @ApiModelProperty(value = "跳转类型: 1-链接 2-视频 3-番剧 4-直播 5-游戏中心")
    private Integer jump_type;

    /**
     * 跳转链接
     */
    @ApiModelProperty(value = "跳转链接")
    private String jump_link;

    /**
     * 点击监控链接
     */
    @ApiModelProperty(value = "点击监控链接")
    private String customized_click_url;

    /**
     * 下发时间: 1-过审下发 2-排期开始前2小时下发
     */
    @ApiModelProperty(value = "下发时间: 1-过审下发 2-排期开始前2小时下发")
    private Integer issued_time;

    /**
     * 1-待初审 2-待复审 3-已驳回 4-待上线 5-上线中 6-已暂停 7-已完成 8-已删除
     */
    @ApiModelProperty(value = "1-待初审 2-待复审 3-已驳回 4-待上线 5-上线中 6-已暂停 7-已完成 8-已删除")
    private Integer status;




}