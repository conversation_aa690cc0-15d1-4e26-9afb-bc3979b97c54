package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.bilibili.adp.brand.portal.webapi.component.vo.ComponentVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.GdTargetAggregationVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2016年9月30日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ApiModel("分页查询创意返回模型")
public class CreativeReportVo {

    @ApiModelProperty(notes = "合同名称")
    private String contract_name;

    @ApiModelProperty(notes = "合同号")
    private String contract_number;

    @ApiModelProperty(notes = "订单ID")
    private Integer order_id;

    @ApiModelProperty(notes = "订单名称")
    private String order_name;

    @ApiModelProperty(notes = "排期id")
    private Integer schedule_id;

    @ApiModelProperty(notes = "排期名称")
    private String schedule_name;

    @ApiModelProperty(notes = "排期开始日期时间")
    private String schedule_begin_date;

    @ApiModelProperty(notes = "排期结束日期时间")
    private String schedule_end_date;

    @ApiModelProperty(notes = "排期开始具体时间")
    private String schedule_begin_time;

    @ApiModelProperty(notes = "排期结束具体时间")
    private String schedule_end_time;

    @ApiModelProperty(notes = "目标展现量")
    private Integer schedule_target_show;

    @ApiModelProperty(notes = "投放时段")
    private String time_segments;

    @ApiModelProperty(notes = "创意id")
    private String creative_id;

    @ApiModelProperty(notes = "创意名")
    private String creative_name;

    @ApiModelProperty(notes = "账户ID")
    private int account_id;

    @ApiModelProperty(notes = "状态")
    private int status;

    @ApiModelProperty(notes = "状态描述")
    private String status_desc;

    @ApiModelProperty(notes = "模板")
    private TemplateVo template;

    @ApiModelProperty(notes = "模板名称")
    private String template_Name;

    @ApiModelProperty(notes = "创意类型")
    private int creative_type;
    @ApiModelProperty(notes = "创意类型")
    private String creative_type_desc;

    @ApiModelProperty(notes = "图片")
    private List<String> image_urls;

    @ApiModelProperty(notes = "视频URL")
    private String video_url;

    @ApiModelProperty(notes = "视频AVID")
    private String video_id;

    @ApiModelProperty(notes = "视频库视频ID")
    private long mgk_video_id;

    @ApiModelProperty(notes = "视频库视频URL")
    private String mgk_video_url;

    @ApiModelProperty(notes = "标题")
    private String title;

    @ApiModelProperty(notes = "描述")
    private String description;

    @ApiModelProperty(notes = "扩展描述")
    private String ext_description;

    @ApiModelProperty(notes = "审核状态")
    private int audit_status;

    private String audit_status_desc;

    @ApiModelProperty(notes = "审核拒绝的原因")
    private String reason;

    @ApiModelProperty(notes = "广告位ID")
    private int slot_id;

    @ApiModelProperty(notes = "广告位ID")
    private int jump_type;

    @ApiModelProperty(notes = "推广位置")
    private int slot_name;

    @ApiModelProperty(notes = "广告标识")
    private String cm_mark_name;

    @ApiModelProperty(notes = "跳转链接")
    private String promotion_purpose_content;

    @ApiModelProperty(notes = "展示数量")
    private Long show_count = 0L;
    @ApiModelProperty(notes = "点击次数")
    private Integer click_count = 0;
    @ApiModelProperty(notes = "点击率")
    private BigDecimal click_rate = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal cost_per_click = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private BigDecimal average_cost_per_thousand = BigDecimal.ZERO;
    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost = BigDecimal.ZERO;

    @ApiModelProperty(notes = "按钮文案")
    private String button_copy;

    @ApiModelProperty(notes = "是否可编辑")
    private boolean enable_edit;

    private String acutal_jump_link;

    private String source_name;

    @ApiModelProperty(value = "预览时间（时间戳，单位毫秒）")
    private Long preview_end_time;

    @ApiModelProperty(value = "预览状态: 1-不可预览 2-可预览 3-预览中")
    private Integer preview_status;

    @ApiModelProperty("创建人名称")
    private String creator_name;

    private Integer sales_type;

    @ApiModelProperty("推广类型")
    private Integer promotion_purpose_type;

    @ApiModelProperty("推广类型描述")
    private String promotion_purpose_type_desc;

    @ApiModelProperty("播放量")
    private Long play_nums;

    @ApiModelProperty("播放成本")
    private BigDecimal play_cost;

    @ApiModelProperty("播放率")
    private BigDecimal play_rate;

    @ApiModelProperty("产品类型")
    private Integer order_product;

    @ApiModelProperty("稿件信息")
    private ManuscriptInfoVo manuscript_info_vo;

    @ApiModelProperty(value = "关联的组件")
    private List<ComponentVo> components;

    @ApiModelProperty(value = "平台名称")
    private String platform_name;

    @ApiModelProperty("定向聚合信息")
    private GdTargetAggregationVo target_aggregation;

    @ApiModelProperty("唤起scheme链接")
    private String scheme_url;

    @ApiModelProperty("up-mid")
    private Long up_mid;

    @ApiModelProperty("up动态id")
    private String up_dynamic_id;

    @ApiModelProperty("投放类型：0、动态投放 1、稿件投放")
    private Integer launch_type;

    @ApiModelProperty("曝光监测链接列表")
    private List<String> customized_imp_url_list;

    @ApiModelProperty("点击监测链接列表")
    private List<String> customized_click_url_list;

    @ApiModelProperty("自定义的直播封面地址")
    private String live_cover_url;
}
