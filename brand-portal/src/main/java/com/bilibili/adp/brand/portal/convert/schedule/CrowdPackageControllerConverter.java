package com.bilibili.adp.brand.portal.convert.schedule;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.CrowdPackVo;
import com.bilibili.brand.api.resource.crowd.BrandCrowdPackageDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/12
 */
@Mapper
public interface CrowdPackageControllerConverter {

    CrowdPackageControllerConverter MAPPER = Mappers.getMapper(CrowdPackageControllerConverter.class);

    @Deprecated
    default List<CrowdPackVo> toPackageVos(Map<Integer, String> crowdPackageNameMap, List<Integer> crowdPackageIds) {
        if (CollectionUtils.isEmpty(crowdPackageNameMap) || CollectionUtils.isEmpty(crowdPackageIds)) {
            return new LinkedList<>();
        }
        return crowdPackageIds.stream()
                .map(packageId -> CrowdPackVo.builder()
                        .id(packageId)
                        .name(crowdPackageNameMap.get(packageId))
                        .build())
                .collect(Collectors.toList());
    }

    default List<CrowdPackVo> brandDtoPackageVos(Map<Integer, BrandCrowdPackageDto> crowdPackageDtoMap, List<Integer> crowdPackageIds) {
        List<CrowdPackVo> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(crowdPackageDtoMap) || CollectionUtils.isEmpty(crowdPackageIds)) {
            return res;
        }
        for (Integer crowdPackageId : crowdPackageIds) {
            BrandCrowdPackageDto crowdPackageDto = crowdPackageDtoMap.get(crowdPackageId);
            if (Objects.nonNull(crowdPackageDto)) {
                res.add(CrowdPackVo.builder()
                        .id(crowdPackageId)
                        .name(crowdPackageDto.getName())
                        .userType(crowdPackageDto.getUserType())
                        .build());
            }
        }
        return res;
    }
}
