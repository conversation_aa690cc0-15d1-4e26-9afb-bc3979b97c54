package com.bilibili.adp.brand.portal.webapi.report.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DataReportPageQueryRequest implements Serializable {

    @NotNull
    @ApiModelProperty("开始时间")
    private Long fromTime;

    @NotNull
    @ApiModelProperty("结束时间")
    private Long toTime;

    @ApiModelProperty("创建人")
    private String creatorName;

    @ApiModelProperty("合同ID")
    private List<Integer> crmContractIdList = new ArrayList<>();

    @ApiModelProperty("合同Number")
    private List<Long> crmContractNumberList = new ArrayList<>();

    @ApiModelProperty("订单ID")
    private List<Integer> orderIdList = new ArrayList<>();

    @ApiModelProperty("排期ID")
    private List<Integer> scheduleIdList = new ArrayList<>();

    @ApiModelProperty("创意ID")
    private List<Long> creativeIdList = new ArrayList<>();

    @ApiModelProperty("订单类型")
    private List<Integer> orderProducts = new ArrayList<>();

    @NotNull
    @ApiModelProperty("广告维度, 0、订单维度 1、排期维度 2、创意维度")
    private Integer adDimension;

    @NotNull
    @ApiModelProperty("聚合维度，0、全部 1、按天")
    private Integer aggDimension;

    @ApiModelProperty("页码")
    private Integer page = 1;

    @ApiModelProperty("页大小")
    private Integer pageSize = 20;

    @ApiModelProperty("订单名称")
    private String orderName;
}
