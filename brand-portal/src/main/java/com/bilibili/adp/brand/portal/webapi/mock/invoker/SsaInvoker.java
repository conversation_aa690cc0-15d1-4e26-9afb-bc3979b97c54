package com.bilibili.adp.brand.portal.webapi.mock.invoker;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.InvokerDesc;
import com.bilibili.adp.brand.portal.webapi.mock.annotations.MethodDesc;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.MockSsaTwistButtonReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.QuerySsaByTopViewReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.QuerySsaByTopViewRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaBaseReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaButtonReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaButtonRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaCreativeInfoReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaCreativeInfoRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.SsaTwistButtonRes;
import com.bilibili.adp.common.enums.SwitchStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.SsaButtonTwistType;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.schedule.dao.GdTopViewDao;
import com.bilibili.brand.biz.schedule.po.GdTopViewPo;
import com.bilibili.brand.biz.schedule.po.GdTopViewPoExample;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.cpt.platform.common.ButtonInteractStyleEnum;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaScheduleSplashScreenMappingDto;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenButtonDao;
import com.bilibili.ssa.platform.biz.dao.SsaSplashScreenDao;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenButtonPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenButtonPoExample;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPo;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenPoExample;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenSchduleService;
import com.bilibili.ssa.platform.common.enums.SsaButtonStyle;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 闪屏操作
 *
 * <AUTHOR>
 * @date 2023/1/10
 */
@InvokerDesc
public class SsaInvoker extends AbstractInvoker {

    @Autowired
    private QueryScheduleService queryScheduleService;
    @Autowired
    private SsaSplashScreenSchduleService ssaSplashScreenSchduleService;
    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;
    @Autowired
    private SsaSplashScreenButtonDao ssaSplashScreenButtonDao;
    @Autowired
    private GdTopViewDao topViewDao;


    @MethodDesc(
            summary = "修改扭一扭闪屏按钮角度和加速度",
            desc = "订单，排期，创意选一个填就可以了，所有填多个信息的地方都用英文逗号分隔" + CHANGE_LINE
                    + "如果出现返回没有数据的情况，则为输入的信息所对应的创意都不为扭一扭闪屏" + CHANGE_LINE
                    + "如果仅查询，那么不需要输入任何角度和加速度，直接查就可以" + CHANGE_LINE
                    + COMMON_DESC
    )
    public List<SsaTwistButtonRes> updateSsaTwistButton(MockSsaTwistButtonReq ssaTwistButtonReq) {

        List<Integer> ssaCreativeIds = querySsaCreativeId(ssaTwistButtonReq, ssaTwistButtonReq.getSsaCreativeIds());

        SsaSplashScreenButtonPoExample example = new SsaSplashScreenButtonPoExample();
        SsaSplashScreenButtonPoExample.Criteria criteria = example.createCriteria();
        criteria.andSplashScreenIdIn(ssaCreativeIds)
                .andInteractStyleIn(Lists.newArrayList(ButtonInteractStyleEnum.TWIST.getCode(),
                        ButtonInteractStyleEnum.BRAND_CARD_TWIST.getCode(),
                        ButtonInteractStyleEnum.BRAND_CARD_CLICK_TWIST.getCode(),
                        ButtonInteractStyleEnum.TWIST_EASTER_EGG_CLICKS.getCode(),
                        ButtonInteractStyleEnum.TWIST_PRODUCT_CLICK.getCode()))
                .andIsDeletedEqualTo(0);
        if (!CollectionUtils.isEmpty(ssaTwistButtonReq.getPlatforms())) {
            criteria.andPlatformIn(ssaTwistButtonReq.getPlatforms());
        }

        SsaSplashScreenButtonPo update = new SsaSplashScreenButtonPo();

        boolean needUpdate = OptionalUtil.notNegative(ssaTwistButtonReq.getAngel(), update::setTwistAngle);
        needUpdate = OptionalUtil.notNegative(ssaTwistButtonReq.getSpeed(), update::setTwistSpeed) || needUpdate;
        needUpdate = OptionalUtil.notNegative(ssaTwistButtonReq.getReverseAngle(), update::setTwistReverseAngle) || needUpdate;

        if (needUpdate) {
            ssaSplashScreenButtonDao.updateByExampleSelective(update, example);
        }

        List<SsaSplashScreenButtonPo> ssaSplashScreenButtonPos = ssaSplashScreenButtonDao.selectByExample(example);

        return ssaSplashScreenButtonPos.stream()
                .map(po -> SsaTwistButtonRes.builder()
                        .angel(po.getTwistAngle())
                        .reverseAngle(po.getTwistReverseAngle())
                        .speed(po.getTwistSpeed())
                        .platform(PlatformType.getByCode(po.getPlatform()).getDesc())
                        .splashScreenId(po.getSplashScreenId())
                        .build()
                ).collect(Collectors.toList());
    }

    private List<ScheduleDto> querySchedule(SsaBaseReq ssaBaseReq) {

        List<Integer> orderIds = ssaBaseReq.getOrderIds();
        List<Integer> scheduleIds = ssaBaseReq.getScheduleIds();

        if (CollectionUtils.isEmpty(orderIds)
                && CollectionUtils.isEmpty(scheduleIds)) {
            throw new RuntimeException("没有订单或排期信息，结束");
        }

        QueryScheduleDto queryScheduleDto = new QueryScheduleDto();
        if (!CollectionUtils.isEmpty(orderIds)) {
            queryScheduleDto.setOrderIds(orderIds);
        }
        if (!CollectionUtils.isEmpty(scheduleIds)) {
            queryScheduleDto.setScheduleIds(scheduleIds);
        }

        queryScheduleDto.setStatusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()));

        List<ScheduleDto> scheduleDtos = queryScheduleService.querySchedule(queryScheduleDto);
        if (CollectionUtils.isEmpty(scheduleDtos)) {
            throw new RuntimeException("没有订单或排期信息，结束");
        }
        return scheduleDtos;
    }

    private List<Integer> querySsaCreativeId(SsaBaseReq ssaBaseReq, List<Integer> ssaCreativeId) {

        if (!CollectionUtils.isEmpty(ssaCreativeId)) {
            return ssaCreativeId;
        }

        List<ScheduleDto> scheduleDtos = querySchedule(ssaBaseReq);
        if (CollectionUtils.isEmpty(scheduleDtos)) {
            throw new RuntimeException("未找到有效排期，请检查订单列表或排期列表");
        }

        List<SsaScheduleSplashScreenMappingDto> mappingList = ssaSplashScreenSchduleService.getSplashScreenSchdulesInGdScheduleIds(
                scheduleDtos.stream()
                        .map(ScheduleDto::getScheduleId)
                        .distinct()
                        .collect(Collectors.toList())
        );

        if (CollectionUtils.isEmpty(mappingList)) {
            throw new RuntimeException("未找到有效的创意，请检查输入参数");
        }

        return mappingList.stream()
                .map(SsaScheduleSplashScreenMappingDto::getSplashScreenId)
                .distinct()
                .collect(Collectors.toList());
    }

    @MethodDesc(
            summary = "修改闪屏全屏滑动限定角度",
            desc = "订单，排期，创意选一个填就可以了，用英文逗号分隔" + CHANGE_LINE
                    + COMMON_DESC
    )
    public List<SsaCreativeInfoRes> updateSsaCreativeInfo(SsaCreativeInfoReq ssaCreativeInfoReq) {
        List<Integer> ssaCreativeIds = querySsaCreativeId(ssaCreativeInfoReq, ssaCreativeInfoReq.getSsaCreativeIds());

        SsaSplashScreenPoExample example = new SsaSplashScreenPoExample();
        example.or().andIdIn(ssaCreativeIds);

        if (Utils.isPositive(ssaCreativeInfoReq.getSlideAngel())) {
            SsaSplashScreenPo updatePo = new SsaSplashScreenPo();
            updatePo.setSlideAngle(ssaCreativeInfoReq.getSlideAngel());
            ssaSplashScreenDao.updateByExampleSelective(updatePo, example);
        }

        List<SsaSplashScreenPo> ssaSplashScreenPos = ssaSplashScreenDao.selectByExample(example);

        return ssaSplashScreenPos.stream()
                .map(po -> SsaCreativeInfoRes.builder()
                        .id(po.getId())
                        .slideAngel(po.getSlideAngle())
                        .build())
                .collect(Collectors.toList());
    }

    @MethodDesc(
            summary = "修改闪屏按钮信息",
            desc = "订单，排期，创意选一个填就可以了，用英文逗号分隔" + CHANGE_LINE
                    + COMMON_DESC
    )
    public List<SsaButtonRes> updateSsaButton(SsaButtonReq ssaButtonReq) {

        List<Integer> buttonTwistTypes = ssaButtonReq.getButtonTwistTypes();
        List<String> rotationAxis = ssaButtonReq.getRotationAxis();

        Assert.isTrue(!CollectionUtils.isEmpty(rotationAxis), "请输入扭动轴");
        Assert.isTrue(buttonTwistTypes.size() == rotationAxis.size(), "按钮扭动类型数量和轴数量不匹配");
        Assert.isTrue(buttonTwistTypes.stream().allMatch(type -> SsaButtonTwistType.getByCode(type) != null),
                "请输入正确的扭动类型");

        List<Integer> ssaCreativeIds = querySsaCreativeId(ssaButtonReq, ssaButtonReq.getSsaCreativeIds());

        SsaSplashScreenButtonPoExample example = new SsaSplashScreenButtonPoExample();
        example.or()
                .andButtonGroupEqualTo(0)
                .andSplashScreenIdIn(ssaCreativeIds)
                .andSeqEqualTo(0)
                .andButtonStyleEqualTo(SsaButtonStyle.TWIST.getCode());

        SsaSplashScreenButtonPo updatePo = new SsaSplashScreenButtonPo();
        String xAxis = "x";
        String yAxis = "y";
        String zAxis = "z";

        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < rotationAxis.size(); i++) {
            map.put(rotationAxis.get(i), buttonTwistTypes.get(i));
        }

        if (map.containsKey(xAxis)) {
            updatePo.setTwistXType(map.get(xAxis));
        }
        if (map.containsKey(yAxis)) {
            updatePo.setTwistYType(map.get(yAxis));
        }
        if (map.containsKey(zAxis)) {
            updatePo.setTwistZType(map.get(zAxis));
        }

        ssaSplashScreenButtonDao.updateByExampleSelective(updatePo, example);

        List<SsaSplashScreenButtonPo> ssaSplashScreenButtonPos = ssaSplashScreenButtonDao.selectByExample(example);

        return ssaSplashScreenButtonPos.stream()
                .map(button -> {

                    Function<Integer, String> getTwistDesc = twistType -> {
                        SsaButtonTwistType type = SsaButtonTwistType.getByCode(twistType);
                        return type == null ?
                                String.format("类型为%d，不合法，请修改", twistType) :
                                type.getDesc();
                    };

                    return SsaButtonRes.builder()
                            .ssaCreativeId(button.getSplashScreenId())
                            .platform(PlatformType.getByCode(button.getPlatform()).getDesc())
                            .buttonXTwistDesc(getTwistDesc.apply(button.getTwistXType()))
                            .buttonYTwistDesc(getTwistDesc.apply(button.getTwistYType()))
                            .buttonZTwistDesc(getTwistDesc.apply(button.getTwistZType()))
                            .build();
                }).collect(Collectors.toList());
    }


    @MethodDesc(
            summary = "查询TopView对应的闪屏信息",
            desc = "查询TopView对应的闪屏信息。" + COMMON_DESC
    )
    public List<QuerySsaByTopViewRes> querySsaByTopView(QuerySsaByTopViewReq req) {
        if (CollectionUtils.isEmpty(req.getTopViewIds())) {
            return Lists.newArrayList();
        }
        GdTopViewPoExample example = new GdTopViewPoExample();
        example.or()
                .andIdIn(req.getTopViewIds())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<GdTopViewPo> topViewPos = this.topViewDao.selectByExample(example);
        return topViewPos.stream()
                .map(tv -> QuerySsaByTopViewRes.builder()
                        .topViewId(tv.getId())
                        .splashScreenId(tv.getSsaCreativeId())
                        .build())
                .collect(Collectors.toList());
    }
}
