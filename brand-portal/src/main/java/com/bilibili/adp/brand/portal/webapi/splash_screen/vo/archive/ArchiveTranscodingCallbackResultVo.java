package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.archive;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 稿件转码回调
 * 参考文档：<a href="https://info.bilibili.co/pages/viewpage.action?pageId=148075345#id-">...</a>点播资源管理平台接入文档-13.根据cid合成音视频并获取下载地址
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ArchiveTranscodingCallbackResultVo {

    private Integer code;

    private TranscodingResultVo data;

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @Data
    public static class TranscodingResultVo {
        private Long cid;

        private Integer qn;

        private String uri;

        private String downloadUrl;

        private Integer width;

        private Integer height;

        private String md5;

        private Integer size;

        private Integer duration;

    }
}
