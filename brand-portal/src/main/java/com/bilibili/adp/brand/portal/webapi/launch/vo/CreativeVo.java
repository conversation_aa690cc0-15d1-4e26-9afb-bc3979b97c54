package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.bilibili.adp.brand.portal.common.ProductLabelVo;
import com.bilibili.adp.brand.portal.webapi.component.vo.ComponentVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.CreativeProductCarouselVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.GdTargetAggregationVo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2016年9月21日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
@ApiModel("创意明细模型")
public class CreativeVo {

    private String creative_id;

    private String creative_name;

    private int schedule_id;

    private String schedule_name;

    private Integer cm_mark;

    private String cm_mark_name;

    private String promotion_purpose_content;

    private String title;

    private String description;

    private String ext_description;

    @ApiModelProperty("是否是选择式卡片")
    private boolean isSelectiveCard;

    private List<ImageVo> images;

    @ApiModelProperty(notes = "极致战队icon")
    private ImageVo extreme_team_icon;

    private String video_url;

    private String video_id;

    private String ext_image_url;

    private String ext_image_hash;

    private int audit_status;

    private int status;

    private String reason;

    private TemplateVo template;

    private int first_category_id;

    private int second_category_id;

    private List<String> tags;

    @ApiModelProperty("创意是否在线")
    private boolean online;

    private Integer order_id;

    private String order_name;

    @ApiModelProperty(notes = "附加类型：0-无文字 1-按钮 3-链接文字")
    private Integer attach_type;

    private Integer button_copy_id;

    private String button_copy_url;

    private String scheme_url;

    @ApiModelProperty("跳转类型")
    private Integer jump_type;
    @ApiModelProperty("跳转类型描述:(1-链接 2-移动视频 3-游戏 4-Web视频 5-页面ID）")
    private String jump_type_desc;
    private List<String> danmakus;

    private List<ImageVo> extra169_images;

    @ApiModelProperty("投放开始时间")
    private String begin_time;

    @ApiModelProperty("投放结束时间")
    private String end_time;

    private List<Long> date_list;

    private String acutal_jump_link;

    @ApiModelProperty(notes = "建站视频ID")
    private int mgk_video_id;

    @ApiModelProperty(notes = "建站视频URL")
    private String mgk_video_url;

    @ApiModelProperty(notes = "ip视频ID")
    private Integer ip_video_id;

    @ApiModelProperty(notes = "ip视频URL")
    private String ip_video_url;


    @ApiModelProperty(notes = "创意形态")
    private Integer creative_style;

    @ApiModelProperty(notes = "创意监控链接")
    private List<GdCreativeMonitoringVo> creative_monitoring;

    @ApiModelProperty(value = "分享开关 0-关闭 1-打开")
    private Integer share_state;

    @ApiModelProperty(value = "分享标题")
    private String share_title;

    @ApiModelProperty(value = "分享副标题")
    private String share_sub_title;

    @ApiModelProperty(value = "分享图片URL")
    private String share_image_url;

    @ApiModelProperty(value = "分享图片hash")
    private String share_image_hash;

    @ApiModelProperty(value = "极致战队配置id")
    private Long extreme_team_config_id;

    @ApiModelProperty(value = "商业标id")
    private Integer bus_mark_id;

    private ManuscriptInfoVo manuscript_info_vo;

    @ApiModelProperty("头像url")
    private String face_url;

    @ApiModelProperty("头像md5")
    private String face_md5;

    @ApiModelProperty("品牌描述")
    private String brand_description;

    @ApiModelProperty("品牌名称")
    private String brand_name;

    //品牌空间
    private String space;

    @ApiModelProperty(value = "二维码弹出时间")
    private Integer qr_pop_time;

    @ApiModelProperty(value = "二维码跳转链接")
    private Integer qr_jump_type;

    @ApiModelProperty(value = "二维码跳转链接")
    private String qr_jump_link;

    @ApiModelProperty(value = "二维码跳转链接")
    private QrInfoVo qr_ext_info;

    /**
     * 跳转类型: 1-链接 2-视频 3-番剧 4-直播 5-游戏中心
     */
    @ApiModelProperty(value = "直播间类型: 0-运营 1-电竞")
    private Integer live_type;

    @ApiModelProperty(value = "直播间描述")
    private String live_type_desc;

    @ApiModelProperty(value = "番剧id")
    private Long season_id;

    @ApiModelProperty(value = "剧集id")
    private Long ep_id;

    @ApiModelProperty(notes = "建站视频ID,用于跳转类型为本地视频")
    private Long jump_mgk_video_id;

    @ApiModelProperty(notes = "是否打开转发：0-打开 1-关闭")
    private Integer forward_state;

    @ApiModelProperty(notes = "是否打开评论：0-打开 1-关闭")
    private Integer reply_state;

    @ApiModelProperty(notes = "评论审核设置：1-先审后发 2-先发后审 默认 1-先审后发")
    private Integer reply_monitor;

    //直播预约卡
    @ApiModelProperty(value = "直播预约id")
    private Long live_booking_id;

    @ApiModelProperty(value = "曝光监测链接列表")
    private List<String> customized_imp_url_list;

    @ApiModelProperty(value = "点击监测链接列表")
    private List<String> customized_click_url_list;

    @ApiModelProperty(notes = "触发彩蛋展示的开始时间 单位ms")
    private String egg_start_time;

    @ApiModelProperty(notes = "触发彩蛋展示的结束时间 单位ms")
    private String egg_end_time;

    @ApiModelProperty(notes = "lottie展示风格，0、中间后底部 1、始终底部")
    private Integer show_style;

    @ApiModelProperty(notes = "彩蛋视频id")
    private Integer egg_video_id;

    @ApiModelProperty(notes = "彩蛋视频url")
    private String egg_video_url;

    @ApiModelProperty(value = "兜底跳转类型 2-稿件 11-本地视频 10-tv_schema")
    private Integer backup_jump_type;

    @ApiModelProperty(value = "兜底跳转链接")
    private String backup_jump_url;

    @ApiModelProperty(value = "兜底真实跳转链接")
    private String actual_backup_jump_url;

    //是否支持自定义播放监测
    private Boolean support_customize_play_monitor;

    //原生跳转类型 1-外部跳转链接 5-建站落地页
    private Integer original_jump_type;

    //原生跳转链接
    private String original_jump_url;

    //0-普通投放 1-原生投放
    private Integer advertising_mode;

    @ApiModelProperty(notes = "lottie类型：0、默认效果 1、图片效果")
    private Integer lottie_type;

    @ApiModelProperty(value = "引导图（目前使用场景：扭一扭）")
    private String lottie_url;

    @ApiModelProperty(value = "交互文案（目前使用场景：扭一扭）")
    private String hint;

    @ApiModelProperty(value = "彩蛋hint的x轴坐标百分比0-100")
    private Integer egg_coor_x;

    @ApiModelProperty(value = "彩蛋hint的y轴坐标百分比0-100")
    private Integer egg_coor_y;

    @ApiModelProperty(value = "关联的组件")
    private List<ComponentVo> components;

    @ApiModelProperty(value = "产品型号")
    private ProductLabelVo product_label;

    @ApiModelProperty(value = "小程序")
    private MiniProgramVo mini_program;

    @ApiModelProperty(value = "应用包id")
    private Integer app_package_id;

    @ApiModelProperty(value = "应用包id")
    private String platform_name;

    @ApiModelProperty("定向聚合信息")
    private GdTargetAggregationVo target_aggregation;

    @ApiModelProperty("平台")
    private List<Integer> platform_id_list;

    @ApiModelProperty(notes = "扭一扭交互类型")
    private Integer twist_action_type;

    @ApiModelProperty(notes = "IP视频展示的开始时间 单位ms")
    private Long ip_start_time;

    @ApiModelProperty(notes = "IP视频展示的结束时间 单位ms")
    private Long ip_end_time;

    @ApiModelProperty("投放类型：0、动态投放 1、稿件投放")
    private Integer launch_type;

    @ApiModelProperty("up-mid")
    private Long up_mid;

    @ApiModelProperty("up动态id")
    private String up_dynamic_id;

    @ApiModelProperty(notes = "抽卡信息")
    private CreativeLotteryVo lottery;

    @ApiModelProperty(notes = "弹幕信息")
    private CreativeDanmakuVo danmaku;

    @ApiModelProperty(notes = "3d特效素材信息")
    private List<Creative3dModelVo> model_list;

    @ApiModelProperty(notes = "交互降级文案")
    private String degrade_hint;

    @ApiModelProperty(notes = "交互锚点位附加描述文案")
    private String anchor_hint;

    @ApiModelProperty(notes = "商品画卷")
    private CreativeProductCarouselVo product_carousel;

    @ApiModelProperty("交互方式，1、扭一扭 2、滑动")
    private Integer interact_style;

    @ApiModelProperty("滑动信息")
    private CreativeDrawGestureVo draw_gesture;

    /**
     * 这个和{@link #brand_name}不是同一个场景，不要混淆
     */
    @ApiModelProperty("品牌名称")
    private String brand_title;

    @ApiModelProperty("自定义的直播封面")
    private ImageVo live_cover;
}
