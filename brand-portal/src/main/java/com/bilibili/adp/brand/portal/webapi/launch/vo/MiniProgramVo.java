package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/16 21:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MiniProgramVo implements Serializable {
    private static final long serialVersionUID = -505383318872508745L;
    @ApiModelProperty("小程序id")
    private String id;
    @ApiModelProperty("小程序名称")
    private String name;
    @ApiModelProperty("小程序链接")
    private String path;
}
