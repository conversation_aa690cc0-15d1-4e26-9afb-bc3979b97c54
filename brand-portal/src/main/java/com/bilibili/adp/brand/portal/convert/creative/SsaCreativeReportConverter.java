package com.bilibili.adp.brand.portal.convert.creative;

import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.ssa_story.SsaArchiveStoryCreativeReportVo;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaArchiveStoryCreativeReportBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/3
 */
@Mapper
public interface SsaCreativeReportConverter {

    SsaCreativeReportConverter MAPPER = Mappers.getMapper(SsaCreativeReportConverter.class);

    List<SsaArchiveStoryCreativeReportVo> toVos(List<SsaArchiveStoryCreativeReportBo> bos);


}
