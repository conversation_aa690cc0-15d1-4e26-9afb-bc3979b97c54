package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.bilibili.adp.brand.portal.webapi.resource.vo.GameVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCptDetailScheduleVo {

    @ApiModelProperty("订单id")
    private Integer order_id;

    @ApiModelProperty("订单名称")
    private String order_name;

    @ApiModelProperty("售卖类型")
    private Integer sale_type;

    @ApiModelProperty("售卖类型描述")
    private String sale_type_desc;

    @ApiModelProperty("排期id")
    private Integer schedule_id;

    @ApiModelProperty("排期名")
    private String name;

    @ApiModelProperty("平台id")
    private Integer platform_id;

    @ApiModelProperty("平台名称")
    private String platform_name;

    @ApiModelProperty("广告位ID")
    private Integer source_id;

    @ApiModelProperty("广告位名称")
    private String source_name;

    @ApiModelProperty("开始日期")
    private Long begin_time;

    @ApiModelProperty("结束日期")
    private Long end_time;

    @ApiModelProperty("投放主体: 0-蓝V号 1-自建主体")
    private Integer launch_obj;

    @ApiModelProperty("模板号")
    private Integer template_id;

    @ApiModelProperty("模板名称")
    private String template_name;

    @ApiModelProperty("uid")
    private String uid;

    private String brand_name;

    //推广目的
    private Integer promotion_purpose_type;

    private String promotion_purpose_type_desc;

    @ApiModelProperty("关键词列表")
    private List<String> keywords;

    @ApiModelProperty("APP包ID")
    private Integer app_package_id;

    @ApiModelProperty("唤起外部APP：0-无须唤起 1-需要唤起")
    private Integer need_wake_app;

    @ApiModelProperty("创意形态")
    private Integer creative_style;

    @ApiModelProperty("关键词词包")
    private List<KeywordsPackageVo> keywords_packages;

    @ApiModelProperty("卡类型")
    private Integer template_card_type;

    @ApiModelProperty("刊例周期id")
    private Integer cycle_id;

    @ApiModelProperty("整体预期展示量")
    private Integer total_impression;

    @ApiModelProperty("售卖类型：0、CPT 1、CPM")
    private Integer biz_sales_type;

    @ApiModelProperty("游戏信息")
    private GameVo game;

    @ApiModelProperty("唤起应用类型")
    private Integer wake_app_type;

    @ApiModelProperty("投放场景")
    private Integer launch_scene;
}
