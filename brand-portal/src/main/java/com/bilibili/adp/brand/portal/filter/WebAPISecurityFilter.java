package com.bilibili.adp.brand.portal.filter;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.TokenUtil;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.bilibili.bjcom.sso.SSOUserInfo;
import com.bilibili.bjcom.sso.SSOUtils;
import com.bilibili.cpt.platform.common.CptConstants;
import com.dianping.cat.Cat;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.SignedJWT;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.HttpMethod;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

public class WebAPISecurityFilter implements Filter {
    private static final String CUSTOM_HEADERS = "Content-Type, HTTP-CONSUMER-KEY, HTTP-DEVICE-TYPE, HTTP-ACCESS-TOKEN";
    public static final String HTTP_ACCESS_TOKEN = "HTTP-ACCESS-TOKEN";
    public static final String HTTP_SECRET_KEY = "HTTP-SECRET-KEY";

    private static final List<String> IGNORE_URL = new ArrayList<>();

    private SalesType salesType;

    public SalesType getSalesType() {
        return salesType;
    }

    public void setSalesType(SalesType salesType) {
        this.salesType = salesType;
    }

    final static Logger LOGGER = LoggerFactory.getLogger(WebAPISecurityFilter.class);
    private static MACVerifier verifier = null;

    static {
        SecretKey secretKeySpec = new SecretKeySpec(TokenUtil.getSecretKey(), "AES");
        try {
            verifier = new MACVerifier(secretKeySpec);
        } catch (JOSEException e) {
            LOGGER.error("new MACVerifier.error", e);
            System.exit(0);
        }
        IGNORE_URL.add("/login");
        IGNORE_URL.add("/sale");
        IGNORE_URL.add("api-docs");
        IGNORE_URL.add("/register");
        IGNORE_URL.add("/password/reset");
        IGNORE_URL.add("/captcha");
        IGNORE_URL.add("/web_api/v1/ssa/resource/archive/callback");
        IGNORE_URL.add("/open_api");
        IGNORE_URL.add("/web_api/v1/resource/brand_archive/ad_mark_callback");
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
            ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        corsResponse(httpResponse);
        if (HttpMethod.OPTIONS.equals(httpRequest.getMethod())) return;
        for (String ignore : IGNORE_URL) {
            if (httpRequest.getRequestURI().contains(ignore)) {
                chain.doFilter(request, response);
                return;
            }
        }
        Response<String> error = securityCheck(httpRequest, httpResponse);
        if (null != error) {
            LOGGER.error("securityCheck 302 error != null");
            httpResponse.setStatus(HttpServletResponse.SC_FORBIDDEN);
            httpResponse.setContentType("application/json");
            httpResponse.setCharacterEncoding("UTF-8");
            httpResponse.getWriter().write(JSON.toJSONString(error));
            httpResponse.getWriter().flush();
            return;
        }

        chain.doFilter(request, response);
    }


    private Response<String> securityCheck(HttpServletRequest request, HttpServletResponse
            response) {
        SSOUserInfo userInfo = SSOUtils.getUserInfo(request);
        String token = getCmFromCookie(request);

        if (StringUtils.isEmpty(token)) {
            token = request.getHeader(HTTP_ACCESS_TOKEN);
        }
        if (StringUtils.isEmpty(token)) {
            return Response.FAIL(WebApiExceptionCode.BAD_REQUEST);
        }

        SignedJWT signedJWT = null;
        try {
            signedJWT = SignedJWT.parse(token);
        } catch (ParseException e) {
            LOGGER.error("SignedJWT.parse.error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }
        try {
            if (!signedJWT.verify(verifier)) {
                return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
            }
        } catch (JOSEException e) {
            LOGGER.error("signedJWT.verify.error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }

        try {
            if (Utils.getNow().getTime() > signedJWT.getJWTClaimsSet().getExpirationTime().getTime()) {
                LOGGER.info("token is expire token{}", token);
                return Response.FAIL(WebApiExceptionCode.TOKEN_EXPIRE);
            }
            if (null != userInfo) {
                Cat.logEvent("BRAND_PV", request.getRequestURI());
                Cat.logEvent("BRAND_" + userInfo.getUserName(), request.getRequestURI());
            }
            request.setAttribute("context", new Context(
                    Integer.valueOf(signedJWT.getJWTClaimsSet().getJWTID()),
                    signedJWT.getJWTClaimsSet().getIssuer(), salesType.getCode(),
                    userInfo == null ? "" : userInfo.getUserName()));
        } catch (ParseException e) {
            LOGGER.error("signedJWT.getJWTClaimsSet().error", e);
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }

        return null;
    }

    public void destroy() {

    }

    private String getCmFromCookie(HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();

        if (ArrayUtils.isEmpty(cookies)) {
            return "";
        }

        return Stream.of(cookies)
                .filter(c -> c.getName().equals(CptConstants.HTTP_COOKIE))
                .findFirst()
                .map(c -> c.getValue())
                .orElse("");
    }

    /**
     * Cross-origin resource sharing (CORS)
     *
     * @param response
     */
    private void corsResponse(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, PUT, GET, OPTIONS, DELETE");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", CUSTOM_HEADERS);
        response.setHeader("Access-Control-Expose-Headers", "X-My-Custom-Header, X-Another-Custom-Header, Date");
    }


}
