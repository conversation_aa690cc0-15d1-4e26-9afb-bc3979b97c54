package com.bilibili.adp.brand.portal.webapi.splash_screen.vo;

import com.bilibili.adp.brand.portal.config.validator.URLCollection;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description 闪屏跳转模型
 * <AUTHOR>
 * @Date 2020.05.25 11:40
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenCustomizedVo {

    @ApiModelProperty(value = "平台编号: 1-iPhone 2-Android 3-iPad 10-ott")
    private Integer platform_id;

    @ApiModelProperty(value = "点击监控链接列表")
    @URLCollection(message = "自定义点击监控链接列表不合法，请检查你的链接后再试")
    private List<String> customized_click_url_list;

    @ApiModelProperty(value = "展示监控链接列表")
    @URLCollection(message = "自定义展示监控链接列表不合法，请检查你的链接后再试")
    private List<String> customized_imp_url_list;

    @ApiModelProperty(value = "中间页点击监控链接列表")
    private List<String> customized_middle_page_click_url_list;

    @ApiModelProperty(value = "自动进入中间页监控链接列表（参考友商，自动进入中间页算点击的 from 柠糕）")
    private List<String> customized_open_middle_page_click_url_list;

}
