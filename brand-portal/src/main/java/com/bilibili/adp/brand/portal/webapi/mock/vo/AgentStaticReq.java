package com.bilibili.adp.brand.portal.webapi.mock.vo;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/9/2
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentStaticReq {

    @FieldDesc(summary = "文件", desc = "营销官网品牌信息", supportsUpload = true)
    private String fileUrl;
}
