package com.bilibili.adp.brand.portal.webapi.booking.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-29
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveHourCreateBookingItemVo {

    @ApiModelProperty("日预约id")
    @NotNull
    private Integer day_booking_id;

    @ApiModelProperty("位次id")
    @NotNull
    private Integer source_id;

    @ApiModelProperty("预约日期")
    @NotNull
    private Long date;

    @ApiModelProperty("预约时间序列集合")
    @NotEmpty
    private List<Integer> seq;

    @ApiModelProperty("是否开启联合预约 仅支持单个预约")
    @NotNull
    private Boolean is_unite_booking;

}
