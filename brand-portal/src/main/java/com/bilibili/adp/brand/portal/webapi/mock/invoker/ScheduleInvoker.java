package com.bilibili.adp.brand.portal.webapi.mock.invoker;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.InvokerDesc;
import com.bilibili.adp.brand.portal.webapi.mock.annotations.MethodDesc;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ScheduleImpressionQueryReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ScheduleImpressionQueryResultItem;
import com.bilibili.adp.brand.portal.webapi.mock.vo.schedule.UpdateScheduleLimitReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.schedule.UpdateScheduleLimitRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.schedule.UpdateTargetReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.schedule.UpdateTargetRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.CrowdRes;
import com.bilibili.adp.brand.portal.webapi.mock.vo.ssa.MockCrowdReq;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.bean.LaunchConstant;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.resource.target_lau.IResTargetItemService;
import com.bilibili.brand.api.resource.target_lau.dto.ResTargetItemDto;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleTargetDto;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.dmp.service.ScheduleCrowdPackService;
import com.bilibili.brand.biz.schedule.dao.GdFlowAllocationDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTargetDao;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPo;
import com.bilibili.brand.biz.schedule.po.GdFlowAllocationPoExample;
import com.bilibili.brand.biz.schedule.po.GdSchedulePo;
import com.bilibili.brand.biz.schedule.po.GdSchedulePoExample;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPo;
import com.bilibili.brand.biz.schedule.po.GdScheduleTargetPoExample;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.biz.dao.CptCrowdPackDao;
import com.bilibili.cpt.platform.biz.po.CptCrowdPackPo;
import com.bilibili.cpt.platform.biz.po.CptCrowdPackPoExample;
import com.bilibili.cpt.platform.biz.service.schedule.CptScheduleDelegate;
import com.bilibili.cpt.platform.common.CptCrowdPackType;
import com.bilibili.cpt.platform.common.ResourceType;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.enums.PlatformType;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.ssa.platform.biz.dao.SsaCpmScheduleDao;
import com.bilibili.ssa.platform.biz.po.SsaCpmSchedulePo;
import com.bilibili.ssa.platform.biz.po.SsaCpmSchedulePoExample;
import com.bilibili.ssa.platform.common.enums.SsaConstants;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mysema.commons.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.brand.portal.webapi.mock.invoker.AbstractInvoker.CHANGE_LINE;
import static com.bilibili.adp.brand.portal.webapi.mock.invoker.AbstractInvoker.COMMON_DESC;

/**
 * <AUTHOR>
 * @date 2022/12/30 10:22
 */
@InvokerDesc
@Slf4j
public class ScheduleInvoker {

    @Autowired
    private GdFlowAllocationDao flowAllocationDao;
    @Autowired
    private IQuerySourceService querySourceService;
    @Autowired
    private GdScheduleDao scheduleDao;
    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private QueryScheduleService queryScheduleService;
    @Autowired
    private GdScheduleTargetDao gdScheduleTargetDao;
    @Autowired
    private SsaCpmScheduleDao ssaCpmScheduleDao;
    @Autowired
    private CptCrowdPackDao cptCrowdPackDao;
    @Autowired
    private CptScheduleDelegate cptScheduleDelegate;
    @Autowired
    private ScheduleCrowdPackService scheduleCrowdPackService;

    private final static Map<ResourceType, Set<Integer>> RESOURCE_TYPE_MAP = new HashMap<ResourceType, Set<Integer>>() {{
        put(ResourceType.SMALL_CARD, LaunchConstant.SMALL_CARD_SOURCE);
        put(ResourceType.BIG_CARD, LaunchConstant.BIG_CARD_SOURCE);//含PD资源位（ResourceType.PD）
        put(ResourceType.UNDER_BOX_CARD, Sets.newHashSet(2336, 2338));
        put(ResourceType.STORY_CARD, Sets.newHashSet(4353, 4356));
    }};


    @MethodDesc(
            summary = "给指定订单或排期刷上人群包",
            desc = "操作类型对应有4种，0：查询操作，1：新增操作（增量），2：新增操作（替换），3:删除操作" + CHANGE_LINE +
                    "现有某排期包含人群包A和排除人群包B来解释以下操作" + CHANGE_LINE +
                    "操作类型为0：查询操作时，会返回排期下的人群包信息，示例：输入排期ID，则返回排期下包含人群包A，排除人群包B" + CHANGE_LINE +
                    "操作类型为1：新增操作（增量）时，仅会在排期原有人群包的基础上新增输入的人群包，未指定的人群包会保持原样，示例：输入包含人群包C，排期人群包无，则刷入结果为包含人群包AC，排除人群包B" + CHANGE_LINE +
                    "操作类型为2：新增操作（替换）时，会按照输入的人群包完全替换原有人群包信息，未指定的人群包会保持原样，示例：输入包含人群包C，排期人群包无，则刷入结果为包含人群包C，排除人群包B" + CHANGE_LINE +
                    "操作类型为3：删除操作时，会在指定的排期下，按照输入的人群包删除，如果不存在这个人群包，则不做处理，如果存在则删除，示例：输入人群包A，排除人群包C，则刷入结果为包含人群包无，排除人群包B"
                    + COMMON_DESC
    )
    public List<CrowdRes> updateCrowdPackage(MockCrowdReq mockCrowdReq) {
        // 参数校验
        Assert.isTrue(!CollectionUtils.isEmpty(mockCrowdReq.getOrderIds())
                        || !CollectionUtils.isEmpty(mockCrowdReq.getScheduleIds()),
                "订单id和排期id不能同时为空");
        int queryType = 0, addType = 1, replaceType = 2, deleteType = 3;
        List<Integer> allowedTypeList = Arrays.asList(queryType, addType, replaceType, deleteType);
        Assert.isTrue(allowedTypeList.contains(mockCrowdReq.getType()), "操作类型错误，请重新填写");

        // 查询人群包信息
        QueryScheduleDto queryScheduleDto = QueryScheduleDto.builder()
                .orderIds(mockCrowdReq.getOrderIds())
                .scheduleIds(mockCrowdReq.getScheduleIds())
                .build();
        List<ScheduleDto> scheduleDtoList = this.queryScheduleService.queryBaseSchedule(queryScheduleDto);
        List<Integer> scheduleIdList = scheduleDtoList.stream()
                .map(ScheduleDto::getScheduleId)
                .distinct()
                .collect(Collectors.toList());
        Assert.notEmpty(scheduleIdList, "未查询到相关排期信息");
        CptCrowdPackPoExample cptCrowdPackPoExample = new CptCrowdPackPoExample();
        cptCrowdPackPoExample.createCriteria()
                .andGdScheduleIdIn(scheduleIdList)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CptCrowdPackPo> cptCrowdPackPoList = cptCrowdPackDao.selectByExample(cptCrowdPackPoExample);
        Map<Integer, Pair<Set<Integer>, Set<Integer>>> crowdPackInfoPairMap = cptCrowdPackPoList.stream()
                .collect(Collectors.groupingBy(CptCrowdPackPo::getGdScheduleId))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> {
                            Set<Integer> crowdPackIds = entry.getValue().stream()
                                    .filter(t -> t.getType().equals(CptCrowdPackType.CONTAIN.getCode()))
                                    .map(CptCrowdPackPo::getCrowdPackId)
                                    .collect(Collectors.toSet());
                            Set<Integer> excludeCrowdPackIds = entry.getValue().stream()
                                    .filter(t -> t.getType().equals(CptCrowdPackType.EXCLUDE.getCode()))
                                    .map(CptCrowdPackPo::getCrowdPackId)
                                    .collect(Collectors.toSet());
                            return Pair.of(crowdPackIds, excludeCrowdPackIds);
                        }
                ));

        // 计算更新人群包信息
        scheduleIdList.forEach(scheduleId -> {
            Pair<Set<Integer>, Set<Integer>> crowdPackPair = crowdPackInfoPairMap.getOrDefault(scheduleId, Pair.of(new HashSet<>(), new HashSet<>()));

            switch (mockCrowdReq.getType()) {
                case 1:
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getCrowdPackIds())) {
                        crowdPackPair.getFirst().addAll(mockCrowdReq.getCrowdPackIds());
                    }
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getExcludeCrowdPackIds())) {
                        crowdPackPair.getSecond().addAll(mockCrowdReq.getExcludeCrowdPackIds());
                    }
                    break;
                case 2:
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getCrowdPackIds())) {
                        crowdPackPair.getFirst().clear();
                        crowdPackPair.getFirst().addAll(mockCrowdReq.getCrowdPackIds());
                    }
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getExcludeCrowdPackIds())) {
                        crowdPackPair.getSecond().clear();
                        crowdPackPair.getSecond().addAll(mockCrowdReq.getExcludeCrowdPackIds());
                    }
                    break;
                case 3:
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getCrowdPackIds())) {
                        crowdPackPair.getFirst().removeAll(mockCrowdReq.getCrowdPackIds());
                    }
                    if (!CollectionUtils.isEmpty(mockCrowdReq.getExcludeCrowdPackIds())) {
                        crowdPackPair.getSecond().removeAll(mockCrowdReq.getExcludeCrowdPackIds());
                    }
                    break;
            }

            crowdPackInfoPairMap.put(scheduleId, crowdPackPair);
        });

        // 真正执行更新操作
        scheduleIdList.forEach(scheduleId -> {
            Pair<Set<Integer>, Set<Integer>> crowdPackPair = crowdPackInfoPairMap.getOrDefault(scheduleId, Pair.of(new HashSet<>(), new HashSet<>()));

            // 更新人群包信息；对于包含的人群包，额外还会写入到gd_schedule_target表中，target_type=100，这里需要额外处理
            this.cptScheduleDelegate.insertUpdateCrowdPack(new ArrayList<>(crowdPackPair.getFirst()), new ArrayList<>(crowdPackPair.getSecond()), scheduleId);
            GdScheduleTargetPo gdScheduleTargetPo = GdScheduleTargetPo.builder()
                    .scheduleId(scheduleId)
                    .targetType(TargetType.CROW_PACK.getCode())
                    .targetItemIds(crowdPackPair.getFirst().toString())
                    .build();
            this.gdScheduleTargetDao.insertUpdateSelective(gdScheduleTargetPo);
        });

        // 返回结果
        Set<Integer> crowdPackIds = crowdPackInfoPairMap.values()
                .stream()
                .flatMap(entry -> Stream.concat(entry.getFirst().stream(), entry.getSecond().stream()))
                .collect(Collectors.toSet());
        Map<Integer, String> crowdPackId2NameMap = this.scheduleCrowdPackService.getPeopleId2NameMap(crowdPackIds);
        return scheduleIdList.stream()
                .map(scheduleId -> {
                    Pair<Set<Integer>, Set<Integer>> crowdPackPair = crowdPackInfoPairMap.getOrDefault(scheduleId, Pair.of(new HashSet<>(), new HashSet<>()));
                    String crowdPackNames = crowdPackPair.getFirst().stream()
                            .map(crowdPackId -> crowdPackId2NameMap.getOrDefault(crowdPackId, "未知人群包"))
                            .collect(Collectors.joining(","));
                    String excludeCrowdPackNames = crowdPackPair.getSecond().stream()
                            .map(crowdPackId -> crowdPackId2NameMap.getOrDefault(crowdPackId, "未知人群包"))
                            .collect(Collectors.joining(","));

                    return CrowdRes.builder()
                            .scheduleId(scheduleId)
                            .crowdPackIds(crowdPackPair.getFirst())
                            .crowdPackNames(crowdPackNames)
                            .excludeCrowdPackIds(crowdPackPair.getSecond())
                            .excludeCrowdPackNames(excludeCrowdPackNames)
                            .build();
                }).collect(Collectors.toList());
    }


    @MethodDesc(
            summary = "修改排期定向",
            desc = "排期支持多个同时处理，用英文逗号分隔" + CHANGE_LINE
                    + "定向信息请先输入定向类型后再输入名称（不需要完整名称，可以自动匹配）" + CHANGE_LINE
                    + "修改后请检查定向是否符合预期" + CHANGE_LINE
                    + COMMON_DESC
    )
    public List<UpdateTargetRes> updateTarget(UpdateTargetReq updateTargetReq) {
        Integer operateType = updateTargetReq.getOperateType();

        Assert.isTrue(operateType == null || Arrays.asList(0, 1, 2).contains(operateType), "操作类型错误，请重新填写");

        //查询
        if (operateType == null || operateType == 0) {
            List<ScheduleTargetDto> targets = queryScheduleService.getScheduleTargetDtoInScheduleIds(updateTargetReq.getScheduleIds());
            return targets.stream()
                    .filter(target -> updateTargetReq.getTargetType() == null || target.getTargetType().equals(updateTargetReq.getTargetType()))
                    .map(target -> UpdateTargetRes.builder()
                            .scheduleId(target.getScheduleId())
                            .targets(Optional.ofNullable(resTargetItemService.getTargetsString(target.getTargetItemIds())).orElse("无定向"))
                            .targetTypeName(TargetType.getByCodeWithoutEx(target.getTargetType()).getName())
                            .build())
                    .collect(Collectors.toList());
        }

        try {
            TargetType.getByCode(updateTargetReq.getTargetType());
        } catch (Exception e) {
            throw new RuntimeException("定向类型错误，请重新填写");
        }
        Assert.isTrue(StringUtils.isNotBlank(updateTargetReq.getTargetName()), "定向名称不能为空");
        List<ResTargetItemDto> targets = resTargetItemService.getTargetsByFuzzyName(updateTargetReq.getTargetType(), updateTargetReq.getTargetName());
        if (targets.isEmpty()) {
            throw new RuntimeException("未查询到相关定向");
        }
        if (targets.size() > 1) {
            List<ResTargetItemDto> filterTargets = targets.stream().filter(target -> target.getName().equals(updateTargetReq.getTargetName())).collect(Collectors.toList());
            if (filterTargets.size() != 1) {
                throw new RuntimeException("查询到多个定向，请明确指定，内容为：" + targets.stream().map(ResTargetItemDto::getName).collect(Collectors.joining(",")));
            } else {
                targets = filterTargets;
            }
        }

        ResTargetItemDto updateTarget = targets.get(0);
        updateTargetReq.getScheduleIds().forEach(scheduleId -> {
            GdScheduleTargetPoExample scheduleTargetPoExample = new GdScheduleTargetPoExample();
            scheduleTargetPoExample.or()
                    .andScheduleIdEqualTo(scheduleId)
                    .andTargetTypeEqualTo(updateTargetReq.getTargetType())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<GdScheduleTargetPo> gdScheduleTargetPos = gdScheduleTargetDao.selectByExample(scheduleTargetPoExample);

            if (Objects.equals(operateType, 1)) {//新增
                if (CollectionUtils.isEmpty(gdScheduleTargetPos)) {
                    gdScheduleTargetDao.insertSelective(GdScheduleTargetPo.builder()
                            .scheduleId(scheduleId)
                            .targetType(updateTarget.getType())
                            .targetItemIds(Collections.singletonList(updateTarget.getId()).toString())
                            .build());
                } else {
                    gdScheduleTargetPos.stream().
                            map(scheduleTarget -> {
                                Set<String> targetSet = new HashSet<>();
                                if (StringUtils.isNotBlank(scheduleTarget.getTargetItemIds())) {
                                    targetSet = new HashSet<>(GsonUtils.toList(scheduleTarget.getTargetItemIds(), String.class));
                                }
                                targetSet.add(String.valueOf(updateTarget.getId()));
                                return GdScheduleTargetPo.builder()
                                        .scheduleTargetId(scheduleTarget.getScheduleTargetId())
                                        .targetItemIds(targetSet.toString())
                                        .build();
                            }).forEach(gdScheduleTargetDao::updateByPrimaryKeySelective);
                }
            } else if (Objects.equals(operateType, 2)) {//删除
                if (!CollectionUtils.isEmpty(gdScheduleTargetPos)) {
                    gdScheduleTargetPos.stream().
                            map(scheduleTarget -> {
                                Set<String> targetSet = new HashSet<>();
                                if (StringUtils.isNotBlank(scheduleTarget.getTargetItemIds())) {
                                    targetSet = new HashSet<>(GsonUtils.toList(scheduleTarget.getTargetItemIds(), String.class));
                                }
                                targetSet.remove(String.valueOf(updateTarget.getId()));
                                return GdScheduleTargetPo.builder()
                                        .scheduleTargetId(scheduleTarget.getScheduleTargetId())
                                        .targetItemIds(targetSet.toString())
                                        .build();
                            }).forEach(gdScheduleTargetDao::updateByPrimaryKeySelective);
                }
            }
        });

        GdScheduleTargetPoExample scheduleTargetPoExample = new GdScheduleTargetPoExample();
        scheduleTargetPoExample.or()
                .andScheduleIdIn(updateTargetReq.getScheduleIds())
                .andTargetTypeEqualTo(updateTargetReq.getTargetType())
                .andIsDeletedEqualTo(0);
        List<GdScheduleTargetPo> gdScheduleTargetPos = gdScheduleTargetDao.selectByExample(scheduleTargetPoExample);

        return gdScheduleTargetPos.stream()
                .map(po -> UpdateTargetRes.builder()
                        .scheduleId(po.getScheduleId())
                        .targetTypeName(TargetType.getByCodeWithoutEx(po.getTargetType()).getName())
                        .targets(Optional.ofNullable(resTargetItemService.getTargetsString(GsonUtils.toList(po.getTargetItemIds(), Integer.class))).orElse("无定向"))
                        .build())
                .collect(Collectors.toList());
    }

    @MethodDesc(summary = "修改排期频控")
    @Transactional(rollbackFor = Exception.class)
    public List<UpdateScheduleLimitRes> updateScheduleFrequency(UpdateScheduleLimitReq req) {

        GdSchedulePoExample scheduleExample = new GdSchedulePoExample();
        scheduleExample.or()
                .andScheduleIdIn(req.getScheduleIds())
                .andIsDeletedEqualTo(0);
        GdSchedulePo schedulePo = new GdSchedulePo();
        schedulePo.setFrequencyLimit(req.getLimit());
        scheduleDao.updateByExampleSelective(schedulePo, scheduleExample);


        SsaCpmSchedulePoExample ssaCpmSchedulePoExample = new SsaCpmSchedulePoExample();
        ssaCpmSchedulePoExample.or()
                .andGdScheduleIdIn(req.getScheduleIds())
                .andIsDeletedEqualTo(0);
        SsaCpmSchedulePo ssaCpmSchedulePo = new SsaCpmSchedulePo();
        ssaCpmSchedulePo.setFrequencyLimit(req.getLimit());
        ssaCpmScheduleDao.updateByExampleSelective(ssaCpmSchedulePo, ssaCpmSchedulePoExample);

        List<GdSchedulePo> gdSchedulePos = scheduleDao.selectByExample(scheduleExample);
        if (CollectionUtils.isEmpty(gdSchedulePos)) {
            return new LinkedList<>();
        } else {
            return gdSchedulePos.stream()
                    .map(po -> {
                        UpdateScheduleLimitRes res = new UpdateScheduleLimitRes();
                        res.setScheduleId(po.getScheduleId());
                        res.setLimit(po.getFrequencyLimit());
                        return res;
                    }).collect(Collectors.toList());
        }
    }


    @MethodDesc(summary = "查询排期库存占用量", desc = "查询指定日期的各个排期的占用情况。")
    public List<ScheduleImpressionQueryResultItem> queryImpression(ScheduleImpressionQueryReq req) {
        Assert.notNull(req, "req must not be null");
        Assert.notNull(req.getDate(), "date must not be null");
        Assert.notNull(req.getQueryType(), "queryType must not be null");

        List<ScheduleImpressionQueryResultItem> result = null;
        Timestamp date = Timestamp.valueOf(req.getDate().atStartOfDay());
        try {
            if (req.getQueryType().equals(0)) {
                Assert.notNull(req.getResourceType(), "resourceType must not be null");
                result = queryFlowAllocation4GD(date, req.getResourceType());
            } else if (req.getQueryType().equals(1)) {
                result = queryFlowAllocation4CPT(date);
            } else {
                result = queryFlowAllocation4SSA(date);
            }
        } catch (Exception e) {
            log.error("queryImpression error", e);
        }
        if (!CollectionUtils.isEmpty(result)) {
            double totalRotationNum = result.stream().mapToDouble(
                            r -> Objects.isNull(r.getRotationNum()) ? 0D : r.getRotationNum().doubleValue())
                    .sum();
            int totalImpression = result.stream().mapToInt(
                            r -> Objects.isNull(r.getImpression()) ? 0 : r.getImpression())
                    .sum();
            result.stream().forEach(r -> {
                r.setTotalImpression(totalImpression);
                r.setTotalRotationNum((float) totalRotationNum);
            });
            return result;
        }
        return Collections.emptyList();
    }

    /**
     * 资源位信息：
     * //闪屏：Constant.SplashSource;
     * //大卡（inline）：Constant.GD_BIG_CARD_SOURCE;
     * //小卡：Constant.GD_SMALL_CARD_SOURCE;
     * //框下：2336,2338 -> Constant.PLATFORM_2_UNDER_BOX_SOURCE;
     * //story：4353,4356 -> Constant.PLATFORM_2_STORY_SOURCE
     * //闪屏的占用情况不体现在gd_flow_allocation表中，因此通过SsaConstants.SSA_PLUS_SALES_TYPES到排期中查询即可
     *
     * @return
     * @throws ServiceException
     */
    public List<ScheduleImpressionQueryResultItem> queryFlowAllocation4SSA(Timestamp date)
            throws ServiceException {
        //包含date的排期
        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusNotEqualTo(SwitchStatus.DELETE.getCode())
                .andSalesTypeIn(SsaConstants.SSA_PLUS_SALES_TYPES)
                .andBeginDateLessThanOrEqualTo(date)
                .andEndDateGreaterThanOrEqualTo(date);
        List<GdSchedulePo> schedulePos = this.scheduleDao.selectByExample(schedulePoExample);
        if (CollectionUtils.isEmpty(schedulePos)) {
            return Collections.emptyList();
        }
        return schedulePos.stream()
                .filter(po -> configCenter.getRobotConfig().keepThisOrderProduct(po.getOrderProduct()))
                .map(s -> {
                    ScheduleImpressionQueryResultItem resultItem = ScheduleImpressionQueryResultItem.builder()
                            .orderId(s.getOrderId())
                            .scheduleId(s.getScheduleId())
                            .scheduleName(s.getName())
                            .date(TimeUtil.timestampToIsoDateStr(date))
                            .impression(s.getTotalImpression())
                            .platformType("-")
                            .resourceType("闪屏")
                            .build();
                    Timestamp launchStartTime = Objects.isNull(s.getGdBeginTime()) ? s.getBeginDate() : s.getGdBeginTime();
                    Timestamp launchEndTime = Objects.isNull(s.getGdEndTime()) ? s.getEndDate() : s.getGdEndTime();
                    resultItem.setLaunchTime(TimeUtil.timestampToIsoTimeStr(launchStartTime)
                            + " - "
                            + TimeUtil.timestampToIsoTimeStr(launchEndTime));
                    return resultItem;
                }).collect(Collectors.toList());
    }

    /**
     * 资源位信息：
     * //闪屏：Constant.SplashSource;
     * //大卡（inline）：Constant.GD_BIG_CARD_SOURCE;
     * //小卡：Constant.GD_SMALL_CARD_SOURCE;
     * //框下：2336,2338 -> Constant.PLATFORM_2_UNDER_BOX_SOURCE;
     * //story：4353,4356 -> Constant.PLATFORM_2_STORY_SOURCE
     * //闪屏的占用情况不体现在gd_flow_allocation表中，因此通过SsaConstants.SSA_PLUS_SALES_TYPES到排期中查询即可
     *
     * @return
     * @throws ServiceException
     */
    public List<ScheduleImpressionQueryResultItem> queryFlowAllocation4GD(Timestamp date, Integer resourceType)
            throws ServiceException {
        ResourceType resource = ResourceType.getByCodeWithValidation(resourceType);
        Set<Integer> sourceIds = RESOURCE_TYPE_MAP.get(resource);
        List<GdSchedulePo> schedulePos = null;
        GdFlowAllocationPoExample gdFlowAllocationPoExample = new GdFlowAllocationPoExample();
        GdFlowAllocationPoExample.Criteria criteria = gdFlowAllocationPoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andLaunchDayEqualTo(date);
        if (Objects.nonNull(sourceIds)) {
            criteria.andSourceIn(Lists.newArrayList(sourceIds));
        } else {
            criteria.andResourceTypeEqualTo(resource.getCode());
        }
        List<GdFlowAllocationPo> gdFlowAllocationPos = flowAllocationDao.selectByExample(gdFlowAllocationPoExample);
        List<Integer> scheduleIds = Lists.newArrayList(gdFlowAllocationPos.stream()
                .map(GdFlowAllocationPo::getScheduleId)
                .collect(Collectors.toSet()));
        if (!CollectionUtils.isEmpty(scheduleIds)) {
            GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
            schedulePoExample.createCriteria()
                    .andScheduleIdIn(scheduleIds)
                    .andStatusNotEqualTo(SwitchStatus.DELETE.getCode());
            schedulePos = this.scheduleDao.selectByExample(schedulePoExample);
        }
        if (CollectionUtils.isEmpty(schedulePos)) {
            return Collections.emptyList();
        }

        return schedulePos.stream()
                .filter(po -> configCenter.getRobotConfig().keepThisOrderProduct(po.getOrderProduct()))
                .map(s -> {
                    ScheduleImpressionQueryResultItem resultItem = ScheduleImpressionQueryResultItem.builder()
                            .orderId(s.getOrderId())
                            .scheduleId(s.getScheduleId())
                            .scheduleName(s.getName())
                            .date(TimeUtil.timestampToIsoDateStr(date))
                            .impression(s.getTotalImpression())
                            .platformType(PlatformType.getByCode(s.getPlatformId()).getDesc())
                            .resourceType(resource.getName())
                            .build();
                    Timestamp launchStartTime = Objects.isNull(s.getGdBeginTime()) ? s.getBeginDate() : s.getGdBeginTime();
                    Timestamp launchEndTime = Objects.isNull(s.getGdEndTime()) ? s.getEndDate() : s.getGdEndTime();
                    resultItem.setLaunchTime(TimeUtil.timestampToIsoTimeStr(launchStartTime)
                            + " - "
                            + TimeUtil.timestampToIsoTimeStr(launchEndTime));
                    return resultItem;
                }).collect(Collectors.toList());
    }

    public List<ScheduleImpressionQueryResultItem> queryFlowAllocation4CPT(Timestamp date) {
        //包含date的排期
        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusNotEqualTo(SwitchStatus.DELETE.getCode())
                .andSalesTypeIn(Lists.newArrayList(SalesType.CPT.getCode(), SalesType.SEARCH_CPT.getCode()))
                .andBeginDateLessThanOrEqualTo(date)
                .andEndDateGreaterThanOrEqualTo(date);
        List<GdSchedulePo> schedulePos = this.scheduleDao.selectByExample(schedulePoExample);
        if (CollectionUtils.isEmpty(schedulePos)) {
            return Collections.emptyList();
        }
        List<Integer> sourceIdList = schedulePos.stream()
                .map(GdSchedulePo::getSlotId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, SourceAllInfoDto> sourceMap = this.querySourceService.getSourceMapInSourceIds(sourceIdList);
        return schedulePos.stream()
                .filter(po -> configCenter.getRobotConfig().keepThisOrderProduct(po.getOrderProduct()))
                .map(s -> {
                    ScheduleImpressionQueryResultItem resultItem = ScheduleImpressionQueryResultItem.builder()
                            .orderId(s.getOrderId())
                            .scheduleId(s.getScheduleId())
                            .scheduleName(s.getName())
                            .date(TimeUtil.timestampToIsoDateStr(date))
                            .impression(s.getTotalImpression())
                            .platformType(PlatformType.getByCode(s.getPlatformId()).getDesc())
                            .build();
                    if (Objects.nonNull(sourceMap) && sourceMap.containsKey(s.getSlotId())) {
                        resultItem.setResourceType(sourceMap.get(s.getSlotId()).getName());
                    }
                    int rr = Utils.isPositive(s.getRotationRatio()) ? s.getRotationRatio() : 0;
                    int t = rr / 500;
                    resultItem.setRotationNum(t * 0.5f);

                    Timestamp launchStartTime = Objects.isNull(s.getGdBeginTime()) ? s.getBeginDate() : s.getGdBeginTime();
                    Timestamp launchEndTime = Objects.isNull(s.getGdEndTime()) ? s.getEndDate() : s.getGdEndTime();
                    resultItem.setLaunchTime(TimeUtil.timestampToIsoTimeStr(launchStartTime)
                            + " - "
                            + TimeUtil.timestampToIsoTimeStr(launchEndTime));

                    return resultItem;
                }).collect(Collectors.toList());
    }
}
