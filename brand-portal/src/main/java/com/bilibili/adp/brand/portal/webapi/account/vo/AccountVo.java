package com.bilibili.adp.brand.portal.webapi.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by fanwen<PERSON> on 16/9/19.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class AccountVo {
    private Integer account_id;
    private String username;
    private String mobile;
    private Integer password_strength;
    private Integer status;
    private BigDecimal cash;
    private BigDecimal red_packed;
    private String name;
    private String company_url;
    private String company_name;
    private String agent_info;
    private List<String> sales;

    private BigDecimal total_cash_recharge;
    private BigDecimal total_cash_consume;
    private BigDecimal total_red_packet_recharge;
    private BigDecimal total_red_packet_consume;

    private boolean gd_order_need_contract_number;
    private Integer is_admin;
    private Integer is_inner;
    private Boolean is_adx_order;

    private Boolean is_support_half_booking;

    //是否支持展示ip合成视频
    private Boolean support_ip_video;

    @ApiModelProperty("是否支持闪屏首刷")
    private Boolean support_ssa_first_brush;

    //是否支持运营工具箱
    private boolean support_operation_tools;

    //是否支持搜索CPM
    private Boolean support_search_cpm;
    //权限
    private List<String> privileges;

}
