/*
 * Copyright (c) 2015-2021 BiliBili Inc.
 */

package com.bilibili.adp.brand.portal.webapi.resource.vo.scv;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TagDescVo {
    private String provider;
    private String source;
    private String style;
}
