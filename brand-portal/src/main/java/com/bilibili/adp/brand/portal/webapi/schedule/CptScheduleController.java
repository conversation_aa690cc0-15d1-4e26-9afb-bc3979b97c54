package com.bilibili.adp.brand.portal.webapi.schedule;

import com.bilibili.adp.brand.portal.convert.schedule.CptScheduleConvert;
import com.bilibili.adp.brand.portal.convert.schedule.CrowdPackageControllerConverter;
import com.bilibili.adp.brand.portal.service.launch.WebAppPackageService;
import com.bilibili.adp.brand.portal.validator.schedule.GdParamCheckValidator;
import com.bilibili.adp.brand.portal.validator.schedule.PermissionValidator;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.PromotionPurposeType;
import com.bilibili.brand.api.dmp.DmpUserType;
import com.bilibili.brand.api.dmp.IScheduleCrowdPackService;
import com.bilibili.brand.api.dmp.dto.CrowdPackDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.crowd.BrandCrowdPackageDto;
import com.bilibili.brand.api.resource.crowd.ICrowdPackageService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.service.IScheduleService;
import com.bilibili.brand.common.Constant;
import com.bilibili.cpt.platform.api.schedule.dto.CptScheduleDto;
import com.bilibili.cpt.platform.api.schedule.service.ICptScheduleService;
import com.bilibili.enums.ScheduleLaunchSceneEnum;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import net.sf.oval.constraint.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xiongyan on 2020/08/21.
 *
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/web_api/v1/cpt/schedules")
@Api(value = "/cpt/schedules", description = "cpt排期相关")
public class CptScheduleController extends BaseController {

    @Autowired
    private ICptScheduleService cptScheduleService;

    @Autowired
    private IScheduleService scheduleService;

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private WebAppPackageService webAppPackageService;

    @Autowired
    private PermissionValidator permissionValidator;

    @Autowired
    private IScheduleCrowdPackService crowdPackService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private GdParamCheckValidator paramCheckValidator;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Value("${brand.env:prod}")
    private String env;

    @Autowired
    private ICrowdPackageService crowdPackageService;

    @ApiOperation(value = "新建cpt排期")
    @RequestMapping(value = "/cpt/source_group", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<CreateScheduleResultVo> createScheduleBySourceGroup(
            @ApiIgnore Context context,
            @ApiParam("按广告位新建")
            @RequestBody NewSourceGroupCptScheduleVo vo) {
        webAppPackageService.validateCptScheduleAppPackage(context.getAccountId(), vo);

        cptScheduleService.createCptScheduleBySourceGroup(CptScheduleConvert.convert2SourceGroupScheduleDto(vo)
                , super.getOperator(context));

        return Response.SUCCESS(CreateScheduleResultVo.builder().source_ids(vo.getSource_ids()).build());
    }

    @ApiOperation(value = "更新cpt排期")
    @RequestMapping(value = "/cpt", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<String> update(
            @ApiIgnore Context context,
            @RequestBody UpdateCptScheduleVo updateScheduleVo){

        cptScheduleService.updateCptSchedule(CptScheduleConvert.convert2UpdateCptScheduleDto(updateScheduleVo),
                getOperator(context));

        return Response.SUCCESS(null);
    }


    @ApiOperation("变更排期状态")
    @RequestMapping(value = "/status", method = RequestMethod.PUT)
    @ResponseBody
    public Response<Object> updateScheduleStatus(@ApiIgnore Context context,
                                                  @RequestParam("ids") @ApiParam("排期ID列表")
                                                  @NotNull(message = "排期列表不能为空") List<Integer> scheduleIds,
                                                 @RequestParam("status") @ApiParam("目标状态")
                                                             int status) throws ServiceException {
        permissionValidator.validatePermissionByScheduleIds(context, scheduleIds);

        List<ScheduleDto> scheduleDtoS = queryScheduleService.querySchedule(QueryScheduleDto.builder().scheduleIds(scheduleIds).build());
        for(ScheduleDto scheduleDto : scheduleDtoS){
            GdOrderDto gdOrderDto = gdOrderService.getOrderById(scheduleDto.getOrderId());
            paramCheckValidator.validateGDOrderStatus(gdOrderDto);
        }

        scheduleService.updateSchedulesStatus(scheduleIds, status, getOperator(context));

        return Response.SUCCESS(null);
    }

    @ApiOperation("删除排期")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @ResponseBody
    public Response<Object> deleteSchedule(@ApiIgnore Context context,
                                           @RequestParam("id") @ApiParam("排期ID逗号分隔")
                                           @NotNull(message = "排期列表不能为空")List<Integer> scheduleIds) throws ServiceException {
        permissionValidator.validatePermissionByScheduleIds(context, scheduleIds);

        if(!CollectionUtils.isEmpty(scheduleIds)){
            scheduleIds.forEach(t->cptScheduleService.deleteCptScheduleById(scheduleIds.get(0),
                    getOperator(context)));
        }

        return Response.SUCCESS(null);
    }

    @ApiOperation("获取指定排期信息")
    @RequestMapping(value = "/{schedule_id}", method = RequestMethod.GET)
    @ResponseBody
    public Response<CptScheduleVo> getSchedule(@ApiIgnore Context context, @PathVariable("schedule_id") int scheduleId)
            throws ServiceException {
        CptScheduleDto schedule = cptScheduleService.getScheduleById(scheduleId);
        GdOrderDto gdOrderDto = gdOrderService.getOrderById(schedule.getGdOrderId(), super.getOperator(context));
        if (!context.getAccountId().equals(schedule.getAccountId())) {
            throw new ServiceException(WebApiExceptionCode.NO_PERMISSION);
        }

        CptScheduleVo scheduleVo = CptScheduleConvert.convertScheduleDto2Vo(schedule, gdOrderDto);

        Map<Integer, BrandCrowdPackageDto> crowdPackageDtoMap = crowdPackageService.queryPackage(schedule.getCrowdPackIds(), schedule.getExcludeCrowdPackIds(), schedule.getOrderProduct());
        scheduleVo.setCrowd_pack_vos(CrowdPackageControllerConverter.MAPPER.brandDtoPackageVos(crowdPackageDtoMap, schedule.getCrowdPackIds()));
        scheduleVo.setExclude_crowd_pack_vos(CrowdPackageControllerConverter.MAPPER.brandDtoPackageVos(crowdPackageDtoMap, schedule.getExcludeCrowdPackIds()));

        if (OrderProduct.LIVE_CPT.getCode().equals(schedule.getOrderProduct())) {
            String liveStrongSourceIdStr = systemConfigService.getValue(SystemConfigEnum.LIVE_CPT_STRONG_SOURCE_IDS.getCode());
            Set<String> liveStrongSourceIds = new HashSet<>(Arrays.asList(liveStrongSourceIdStr.split(",")));
            scheduleVo.setSupport_strong_version(liveStrongSourceIds.contains(String.valueOf(scheduleVo.getSlot_id())));
        }
        // 是否支持分享
        return Response.SUCCESS(scheduleVo);
    }

    @ApiOperation("获取人群定向包信息")
    @RequestMapping(value = "/get/crowd_package", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<CrowdPackVo>> getCrowdPackage(@ApiIgnore Context context)
            throws ServiceException {
        if("uat".equals(env)){
            return Response.SUCCESS(Lists.newArrayList(
                    CrowdPackVo.builder().id(16500).name("uat电子行业").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(16531).name("uat美妆行业").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(17531).name("极度敏感人群").combined(false).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(16520).name("敏感人群1").combined(false).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(24225).name("本地测试创建buvid人群").userType(DmpUserType.BUVID.getCode()).build(),
                    CrowdPackVo.builder().id(24259).name("大兔子测试buvid").userType(DmpUserType.BUVID.getCode()).build()));
        }
        List<CrowdPackDto> crowdPackDtos = crowdPackService.getCrowdPackDtosByAccountIdDirectly(context.getAccountId());
        if(CollectionUtils.isEmpty(crowdPackDtos)){
            return Response.SUCCESS(new ArrayList<>());
        }

        List<Integer> sivtAccountList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_ACCOUNT_LIST.getCode());
        List<Integer> sivtCrowdPackageList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_LIST.getCode());
        boolean isSivtBlackAccount = sivtAccountList.contains(context.getAccountId());

        List<CrowdPackVo> response = crowdPackDtos.stream()
                .map(dto -> CrowdPackVo.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .combined(dto.getIsGroupSet() != null && dto.getIsGroupSet())
                        .userType(dto.getUserType())
                        .excludeSelected(isSivtBlackAccount && sivtCrowdPackageList.contains(dto.getId()))
                        .build())
                .collect(Collectors.toList());
        return Response.SUCCESS(response);
    }

    @Deprecated
    @ApiOperation("校验资源位是不是小黄条")
    @RequestMapping(value = "/check/source", method = RequestMethod.GET)
    @ResponseBody
    public Response<Boolean> checkSourceBelongSmallYellow(@RequestParam("source_ids") @ApiParam("资源位列表")
                                                          List<Integer> sourceIds){
        Assert.isTrue(!CollectionUtils.isEmpty(sourceIds), "资源位列表不能为空");

        String sources = systemConfigService.getValueByItemEnum(SystemConfigEnum.SMALL_YELLOW_SOURCE_IDS);
        List<String> sourceList = Arrays.asList(sources.split(","));

        for(Integer sourceId : sourceIds){
            if(!sourceList.contains(sourceId.toString())){
                return Response.SUCCESS(false);
            }
        }

        return Response.SUCCESS(true);
    }

    @ApiOperation("查询排期选项信息")
    @RequestMapping(value = "/options", method = RequestMethod.GET)
    @ResponseBody
    public Response<ScheduleOptionVo> getScheduleOptions(@RequestParam("source_ids")
                                                             @ApiParam("资源位列表")
                                                             List<Integer> sourceIds,
                                                         @RequestParam("promotion_purpose_type")
                                                         @ApiParam("推广目的")
                                                         Integer promotionPurposeType){
        Assert.isTrue(!CollectionUtils.isEmpty(sourceIds), "资源位列表不能为空");
        List<Integer> yellowSourceList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SMALL_YELLOW_SOURCE_IDS.getCode());
        Set<Integer> playerDetailSourceList = Constant.PLAYER_DETAIL_SOURCE_PLATFORM_MAP.keySet();
        List<Integer> splitTimeSourceIdList = this.systemConfigService.getValueReturnListInt(SystemConfigEnum.SUPPORTS_SPLIT_TIME_CPT_SOURCE_IDS.getCode());
        List<Integer> skipBookingSourceIdList = this.systemConfigService.getValueReturnListInt(SystemConfigEnum.CPT_SKIP_RESOURCE_BOOKING_SOURCE_ID_LIST.getCode());
        List<Integer> launchScenes = Collections.emptyList();
        if (Objects.equals(promotionPurposeType, PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode())) {
            launchScenes = playerDetailSourceList.containsAll(sourceIds)
                    ? Arrays.stream(ScheduleLaunchSceneEnum.values()).map(ScheduleLaunchSceneEnum::getCode).sorted().collect(Collectors.toList()) :
                    Collections.emptyList();
        }
        ScheduleOptionVo.ScheduleOptionVoBuilder builder = ScheduleOptionVo.builder()
                .smallYellow(new HashSet<>(yellowSourceList).containsAll(sourceIds))
                .timeTypes(new HashSet<>(splitTimeSourceIdList).containsAll(sourceIds)
                        ? Lists.newArrayList(0, 1) : Lists.newArrayList(0))
                .skipBooking(new HashSet<>(skipBookingSourceIdList).containsAll(sourceIds))
                .launchScenes(launchScenes);
        return Response.SUCCESS(builder.build());
    }

}