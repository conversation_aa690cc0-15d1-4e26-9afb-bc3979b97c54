package com.bilibili.adp.brand.portal.webapi.statistic.vo;

import com.bilibili.adp.web.framework.annotations.CsvHeader;
import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/19 20:25
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TopViewCreativeDataVo implements Serializable {
    private static final long serialVersionUID = -4929301492564527847L;
    @CsvHeader("日期")
    @ApiModelProperty(notes = "日期")
    @ExcelResources(title = "日期")
    private String date;

    @CsvHeader("订单id")
    @ApiModelProperty(notes = "订单id")
    @ExcelResources(title = "订单id")
    private Integer orderId;

    @CsvHeader("订单名")
    @ApiModelProperty(notes = "订单名")
    @ExcelResources(title = "订单名")
    private String orderName;

    @CsvHeader("创意id")
    @ApiModelProperty(notes = "创意id")
    @ExcelResources(title = "创意id")
    private Integer creativeId;

    @CsvHeader("创意名")
    @ApiModelProperty(notes = "创意名")
    @ExcelResources(title = "创意名")
    private String creativeName;

    @ApiModelProperty(notes = "闪屏展示数量")
    @CsvHeader("闪屏展示数量")
    @ExcelResources(title = "闪屏展示数量")
    private Long ssaShowCount;

    @CsvHeader("闪屏点击次数")
    @ApiModelProperty(notes = "闪屏点击次数")
    @ExcelResources(title = "闪屏点击次数")
    private Long ssaClickCount;

    @CsvHeader("闪屏点击率")
    @ApiModelProperty(notes = "闪屏点击率")
    @ExcelResources(title = "闪屏点击率")
    private BigDecimal ssaClickRate;

    @ApiModelProperty(notes = "首焦展示数量")
    @CsvHeader("首焦展示数量")
    @ExcelResources(title = "首焦展示数量")
    private Long hfShowCount;

    @CsvHeader("首焦点击次数")
    @ApiModelProperty(notes = "首焦点击次数")
    @ExcelResources(title = "首焦点击次数")
    private Long hfClickCount;

    @CsvHeader("首焦点击率")
    @ApiModelProperty(notes = "首焦点击率")
    @ExcelResources(title = "首焦点击率")
    private BigDecimal hfClickRate;
}
