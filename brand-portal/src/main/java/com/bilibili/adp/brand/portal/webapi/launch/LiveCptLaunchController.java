package com.bilibili.adp.brand.portal.webapi.launch;

import com.bilibili.adp.brand.portal.convert.creative.LiveCptControllerConverter;
import com.bilibili.adp.brand.portal.webapi.launch.vo.CreativeVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.live.LiveCptCreativeVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.live.LiveCptDetailCreativeVo;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.cpt.platform.api.creative.service.ILiveCptCreativeService;
import com.bilibili.cpt.platform.api.creative.service.ILiveCptExtInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;

/**
 * 直播cpt创意投放
 *
 * <AUTHOR>
 * @date 2022/3/8
 */
@RestController
@RequestMapping("/web_api/v1/cpt/live")
@Api(value = "直播cpt投放")
public class LiveCptLaunchController extends BaseController {

    @Autowired
    private LaunchController launchController;
    @Autowired
    private ILiveCptCreativeService creativeService;
    @Autowired
    private ILiveCptExtInfoService extInfoService;

    @ApiOperation(value = "保存创意信息")
    @PostMapping(value = "/creative/save")
    public Response<Long> saveCreative(
            @ApiIgnore Context context,
            @ApiParam(required = true)
            @RequestBody LiveCptCreativeVo creativeInfo) throws Exception {

        Long creativeId = creativeService.saveCreative(LiveCptControllerConverter.MAPPER.toDto(creativeInfo), getOperator(context));

        return Response.SUCCESS(creativeId);
    }

    @ApiOperation(value = "查询创意详情")
    @GetMapping(value = "/creative/detail")
    public Response<LiveCptDetailCreativeVo> queryCreative(
            @ApiIgnore Context context,
            @ApiParam(required = true)
            @RequestParam("creative_id") Long creativeId) throws Exception {

        Response<CreativeVo> result = launchController.queryCreative(context, String.valueOf(creativeId));

        //cpt处会因为一些逻辑在结束结束默认增加1小时，对时间重写
        Pair<Timestamp, Timestamp> beginAndEndTime = extInfoService.getCreativeDate(creativeId);
        CreativeVo creativeVo = result.getResult();
        if (creativeVo != null) {
            creativeVo.setBegin_time(Utils.getTimestamp2String(beginAndEndTime.getLeft(), "yyyy-MM-dd HH:mm:ss"));
            creativeVo.setEnd_time(Utils.getTimestamp2String(beginAndEndTime.getRight(), "yyyy-MM-dd HH:mm:ss"));
        }

        return Response.SUCCESS(LiveCptControllerConverter.MAPPER.toVo(result.getResult()));
    }
}
