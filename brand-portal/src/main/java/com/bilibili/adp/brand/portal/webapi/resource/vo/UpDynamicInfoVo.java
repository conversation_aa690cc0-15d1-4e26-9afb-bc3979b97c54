package com.bilibili.adp.brand.portal.webapi.resource.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpDynamicInfoVo implements Serializable {

    private Long upMid;

    private String upNickName;

    private String upFaceUrl;

    private Long upFansNumber;

    private List<SingleDynamicInfoVo> dynamicList;
}
