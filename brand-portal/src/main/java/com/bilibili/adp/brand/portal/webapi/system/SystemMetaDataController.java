package com.bilibili.adp.brand.portal.webapi.system;

import com.bilibili.adp.brand.portal.webapi.system.vo.SystemMetaDataVo;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统相关参数接口
 *
 * <AUTHOR>
 * @date 2023/7/3
 */
@RestController
@RequestMapping("/web_api/v1/system")
public class SystemMetaDataController {

    @Autowired
    private ConfigCenter configCenter;

    @GetMapping("/meta_data")
    public SystemMetaDataVo getSystemInfo() {
        return SystemMetaDataVo.builder()
                .env(configCenter.getMetaDataConfig().getEnv())
                .goblinEnv(configCenter.getMetaDataConfig().getGoblinEnv())
                .build();

    }
}
