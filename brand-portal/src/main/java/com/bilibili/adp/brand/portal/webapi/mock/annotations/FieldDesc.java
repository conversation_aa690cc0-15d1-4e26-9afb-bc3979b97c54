package com.bilibili.adp.brand.portal.webapi.mock.annotations;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/12/12 21:27
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target(value = {ElementType.FIELD})
@Documented
public @interface FieldDesc {

    /**
     * 字段摘要
     */
    String summary() default "";

    /**
     * 字段描述
     */
    String desc() default "";

    /**
     * 是否必填
     */
    boolean required() default false;

    /**
     * 是否支持文件上传
     */
    boolean supportsUpload() default false;

    /**
     * 是否是查询日志使用的id信息
     */
    boolean logObjId() default false;

}
