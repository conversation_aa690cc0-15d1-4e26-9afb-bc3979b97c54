package com.bilibili.adp.brand.portal.webapi.resource.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.bilibili.enums.OgvResourceStickerEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/13 17:27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OgvResourceStickerVo implements Serializable {
    private static final long serialVersionUID = -1859437022350650373L;
    /**
     * @see OgvResourceStickerEnum
     */
    @ApiModelProperty("id")
    private Integer id;
    /**
     * @see OgvResourceStickerEnum
     */
    @ApiModelProperty("desc")
    private String desc;
}
