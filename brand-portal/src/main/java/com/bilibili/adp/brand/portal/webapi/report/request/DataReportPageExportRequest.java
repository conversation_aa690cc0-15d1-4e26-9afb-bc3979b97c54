package com.bilibili.adp.brand.portal.webapi.report.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DataReportPageExportRequest extends DataReportPageQueryRequest {

    @ApiModelProperty("处理序列")
    private String dealSeq;
}
