package com.bilibili.adp.brand.portal.webapi.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/6.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewInternalOrderVo {
    @NotBlank
    @ApiModelProperty("订单名称")
    private String order_name;

    @NotNull
    @ApiModelProperty("资源类型 0其他 1内部、2售卖、3配送、4补量")
    private Integer resource_type;
    
    
    @ApiModelProperty("绑定的合同号")
    private long contract_number;

    @ApiModelProperty("一级行业id")
    private Integer category_first_id;
    @ApiModelProperty("二级行业id")
    private Integer category_second_id;
}
