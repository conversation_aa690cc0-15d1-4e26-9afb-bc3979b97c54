package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.component;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 闪屏附加组件
 * <AUTHOR>
 * @date 2022/8/26
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaAdditionalComponentsVo {
    @ApiModelProperty("组件类型")
    private List<Integer> componentTypes;

    @ApiModelProperty("倒计时组件")
    private SsaCountDownComponentVo countDownComponent;
}
