package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewSourceCptScheduleVo {
    @NotNull
    @ApiModelProperty("订单id")
    private Integer order_id;
    @NotNull
    @ApiModelProperty("广告位id")
    private Integer source_id;
    @NotNull
    @NotEmpty
    @ApiModelProperty("日期列表")
    private List<NewCptScheduleDateVo> dates;
}
