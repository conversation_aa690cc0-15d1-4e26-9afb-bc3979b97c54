package com.bilibili.adp.brand.portal.webapi.material;

import com.bilibili.adp.brand.portal.convert.resource.ResourceControllerConverter;
import com.bilibili.adp.brand.portal.webapi.material.vo.InlineVideoVo;
import com.bilibili.adp.brand.portal.webapi.material.vo.VideoCompoundVo;
import com.bilibili.adp.brand.portal.webapi.material.vo.VideoSizeVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.material.IIPVideoService;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.cpt.platform.biz.enumerate.IpVideoTypeEnum;
import com.bilibili.cpt.platform.common.IpVideoSizeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/web_api/v1/ip/video")
@Api(value = "/ip/video", description = "ip合成视频相关")
public class IPVideoController extends BaseController {

    @Autowired
    private IIPVideoService ipVideoService;


    @ApiOperation(value = "查询视频尺寸")
    @RequestMapping(value = "/get/video/size", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<VideoSizeVo>> getVideoSizeList(){

        return Response.SUCCESS(Arrays.stream(IpVideoSizeEnum.values())
                .map(t -> VideoSizeVo.builder()
                        .height(t.getHeight())
                        .width(t.getWidth())
                        .build())
                .distinct()
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询ip视频列表")
    @RequestMapping(value = "/get/video/list", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Pagination<List<InlineVideoVo>>> get3DVideoList(
             @ApiParam(name = "文件名称")
             @RequestParam(value = "name", required = false) String name,
             @RequestParam(value = "page", required = false, defaultValue = "1")
             @ApiParam("页码") Integer currentPage,
             @RequestParam(value = "size", required = false, defaultValue = "15")
             @ApiParam("页长") Integer size,
             @RequestParam(value = "width", required = false)
             @ApiParam("宽度") Integer width,
             @RequestParam(value = "height", required = false)
             @ApiParam("高度") Integer height,
             @RequestParam(value = "type", required = false) @ApiParam("ip视频类型") List<Integer> videoTypeList){

        PageResult<IPVideoBo> pageResult = ipVideoService.getIPVideoBoList(name,
                currentPage, size, null, width, height, videoTypeList, null, null, false);

        List<InlineVideoVo> result = new ArrayList<>();
        if(pageResult.getTotal() != 0){
            result = pageResult.getRecords().stream().map(t-> {
                InlineVideoVo vo = new InlineVideoVo();
                BeanUtils.copyProperties(t,vo);
                vo.setId(t.getId());
                return vo;
            }).collect(Collectors.toList());
        }

        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), result));
    }

    @ApiOperation(value = "合成视频")
    @RequestMapping(value = "/video/upload", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Integer> compoundVideo(
            @ApiParam(required = true, value = "compound vo data")
            @RequestBody VideoCompoundVo compoundVo) throws ServiceException {

        Assert.isTrue(StringUtils.isNotBlank(compoundVo.getName()), "ip视频名称不能为空");
        Integer videoType = compoundVo.getVideoType();

        if (videoType == null || videoType == IpVideoTypeEnum.NORMAL.getCode()) {
            Assert.notNull(compoundVo.getRgbVideoUrl(), "rgb通道视频不能为空");
            Assert.notNull(compoundVo.getAlphaVideoUrl(), "alpha通道视频url不能为空");
        } else if (videoType == IpVideoTypeEnum.OUT_BOX_VIDEO.getCode()) {
            Assert.isTrue(StringUtils.isNotBlank(compoundVo.getOutBoxVideoPngSequencesUrl()), "出框视频png序列url不能为空");
            Assert.isTrue(StringUtils.isNotBlank(compoundVo.getBgVideoPngSequencesUrl()), "背景视频png序列url不能为空");
        }else {
            throw new ServiceException(String.format("合并视频时类型错误，错误类型为:%d", videoType));
        }

        ipVideoService.compoundIPVideo(ResourceControllerConverter.MAPPER.toVideoCompoundBo(compoundVo));

        return Response.SUCCESS(1);
    }

    @ApiOperation(value = "删除ip视频")
    @RequestMapping(value = "/video/delete", method = RequestMethod.DELETE)
    public
    @ResponseBody
    Response<Integer> delete3DVideo(
            @ApiParam(required = true, name = "视频id")
            @RequestParam("id") Integer id) throws ServiceException {

        Assert.notNull(id, "ip视频id不能为空");
        ipVideoService.removeIPVideo(id);

        return Response.SUCCESS(1);
    }

}

