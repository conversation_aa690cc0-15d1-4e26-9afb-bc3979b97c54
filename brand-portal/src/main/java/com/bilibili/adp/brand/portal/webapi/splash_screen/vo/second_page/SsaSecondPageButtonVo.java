package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.second_page;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/1
 */
@Data
@ApiModel("闪屏第二屏按钮信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaSecondPageButtonVo implements Serializable {

    private static final long serialVersionUID = -1961448180234771833L;
    @ApiModelProperty("按钮文案")
    private String buttonText;

    @ApiModelProperty("动效url")
    private String lottieUrl;

    @ApiModelProperty("动效MD5")
    private String lottieMd5;

    @ApiModelProperty("降级按钮文案")
    private String downgradeButtonText;
}
