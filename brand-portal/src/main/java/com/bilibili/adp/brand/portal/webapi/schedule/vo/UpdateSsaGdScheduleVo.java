package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2019/5/28.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSsaGdScheduleVo {
    @NotNull
    private int schedule_id;
    @NotBlank
    private String name;

    @ApiModelProperty("定向")
    private SsaScheduleTargetVo target;

    private List<Timestamp> dates;


    @ApiModelProperty("合约展现量")
    private int total_impression;
}
