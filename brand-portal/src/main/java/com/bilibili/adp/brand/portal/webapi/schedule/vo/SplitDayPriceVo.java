package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by xiongyan on 2020/8/05.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("分日价格信息")
public class SplitDayPriceVo {

    @ApiModelProperty("日期")
    private String schedule_date;

    @ApiModelProperty("平台")
    private Integer platform_id;

    @ApiModelProperty("平台名称")
    private String platform_name;

    @ApiModelProperty("单价 元/cpm")
    private BigDecimal price;
}
