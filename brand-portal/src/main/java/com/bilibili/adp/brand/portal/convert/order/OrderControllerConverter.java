package com.bilibili.adp.brand.portal.convert.order;

import com.bilibili.adp.brand.portal.webapi.order.vo.GdOrderExtVo;
import com.bilibili.adp.brand.portal.webapi.order.vo.NewOrderVo;
import com.bilibili.adp.brand.portal.webapi.order.vo.UpdateOrderVo;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.dto.NewOrderDto;
import com.bilibili.brand.api.order.dto.UpdateOrderDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
@Mapper
public interface OrderControllerConverter {

    OrderControllerConverter MAPPER = Mappers.getMapper(OrderControllerConverter.class);

    @Mappings({
            @Mapping(target = "regressionRatio", expression = "java(getRegressionRatio.apply(newOrderVo" +
                    ".getRegressionRatio()))"),
            @Mapping(target = "gdOrderExtDto", source = "gdOrderExtVo"),
            @Mapping(target = "gdOrderExtDto.orderAmount", expression = "java(yuanToFen.apply(gdOrderExtVo" +
                    ".getOrderAmount()))"),
            @Mapping(target = "gdOrderExtDto.orderCpm", defaultValue = "0"),

    })
    NewOrderDto toNewOrderDto(NewOrderVo newOrderVo);

    @Mappings({
            @Mapping(target = "orderId", source = "id"),
            @Mapping(target = "gdOrderExtDto", source = "gdOrderExtVo"),
            @Mapping(target = "gdOrderExtDto.orderAmount", expression = "java(yuanToFen.apply(gdOrderExtVo" +
                    ".getOrderAmount()))"),
            @Mapping(target = "gdOrderExtDto.orderCpm", defaultValue = "0"),

    })
    UpdateOrderDto toUpdateOrderDto(UpdateOrderVo updateOrderVo);


    @Mappings({
            @Mapping(target = "orderAmount",expression = "java(fenToYuan.apply(gdOrderExtDto" +
                    ".getOrderAmount()))")
    })
    GdOrderExtVo toGdOrderExtVo(GdOrderExtDto gdOrderExtDto);


    Function<String, String> getRegressionRatio = regressionRatio -> {
        if (regressionRatio == null) {
            return "100";
        }
        return regressionRatio;
    };

    Function<BigDecimal, Long> yuanToFen = Utils::fromYuanToFen;

    Function<Long, BigDecimal> fenToYuan = Utils::fromFenToYuan;

    default Timestamp map(Long time) {
        if (time == null) {
            return null;
        }
        return new Timestamp(time);
    }

    default Long map(Timestamp time) {
        if (time == null) {
            return null;
        }
        return time.getTime();
    }
}
