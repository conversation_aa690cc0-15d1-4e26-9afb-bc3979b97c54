package com.bilibili.adp.brand.portal.webapi.mock.invoker;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.InvokerDesc;
import com.bilibili.adp.brand.portal.webapi.mock.annotations.MethodDesc;
import com.bilibili.adp.brand.portal.webapi.mock.vo.DrawGestureQueryReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.DrawGestureQueryResultItem;
import com.bilibili.adp.brand.portal.webapi.mock.vo.DrawGestureRefreshReq;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.creative.dto.CreativeDrawGestureDto;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.creative.po.GdCreativePoExample;
import com.bilibili.brand.biz.creative.service.CreativeDrawGestureService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.enums.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.brand.portal.webapi.mock.invoker.AbstractInvoker.COMMON_DESC;

@Slf4j
@InvokerDesc
public class DrawGestureInvoker {

    @Autowired
    private GdCreativeDao gdCreativeDao;

    @Autowired
    private CreativeDrawGestureService creativeDrawGestureService;

    @MethodDesc(summary = "刷手势滑动的角度和长度",
            desc = "基于实际投放效果，动态调整手势滑动的角度、长度等相关配置" + COMMON_DESC)
    public void refreshDrawGesture(DrawGestureRefreshReq req) {
        log.info("refreshDrawGesture，req：{}", GsonUtils.toJson(req));
        Assert.isTrue(Objects.nonNull(req.getOrderId())
                || Objects.nonNull(req.getScheduleId())
                || Objects.nonNull(req.getCreativeId()), "订单ID、排期ID、创意ID必须指定其一");
        Assert.notNull(PlatformType.getByCodeWithoutEx(req.getPlatformType()), "平台类型指定错误，请检查");
        if (Objects.nonNull(req.getDrawAngle())) {
            int drawAngle = req.getDrawAngle();
            Assert.isTrue(drawAngle >= 0 && drawAngle <= 180, "角度值，必须在0-180度范围内");
        }
        if (Objects.nonNull(req.getDrawLength())) {
            int drawLength = req.getDrawLength();
            Assert.isTrue(drawLength >= 0, "长度值，必须>=0");
        }

        GdCreativePoExample gdCreativePoExample = new GdCreativePoExample();
        GdCreativePoExample.Criteria criteria = gdCreativePoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.notNullDo(req.getOrderId(), c -> criteria.andOrderIdEqualTo(req.getOrderId()));
        ObjectUtils.notNullDo(req.getScheduleId(), c -> criteria.andScheduleIdEqualTo(req.getScheduleId()));
        ObjectUtils.notNullDo(req.getCreativeId(), c -> criteria.andCreativeIdEqualTo(req.getCreativeId()));
        List<GdCreativePo> gdCreativePoList = gdCreativeDao.selectByExample(gdCreativePoExample);
        List<Long> createIdList = gdCreativePoList.stream()
                .map(GdCreativePo::getCreativeId)
                .distinct()
                .collect(Collectors.toList());
        log.info("refreshDrawGesture，req：{}，createIdList：{}", GsonUtils.toJson(req), GsonUtils.toJson(createIdList));
        creativeDrawGestureService.refreshDrawGestureConfig(createIdList, req.getPlatformType(), req.getDrawAngle(), req.getDrawLength());
    }

    @MethodDesc(summary = "查询手势滑动的角度和长度",
            desc = "查询手势滑动的角度、长度等相关配置，验证调整是否已生效" + COMMON_DESC)
    public List<DrawGestureQueryResultItem> queryDrawGesture(DrawGestureQueryReq req) {
        log.info("queryDrawGesture，req：{}", GsonUtils.toJson(req));
        Assert.isTrue(Objects.nonNull(req.getOrderId())
                || Objects.nonNull(req.getScheduleId())
                || Objects.nonNull(req.getCreativeId()), "订单ID、排期ID、创意ID必须指定其一");

        GdCreativePoExample gdCreativePoExample = new GdCreativePoExample();
        GdCreativePoExample.Criteria criteria = gdCreativePoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.notNullDo(req.getOrderId(), c -> criteria.andOrderIdEqualTo(req.getOrderId()));
        ObjectUtils.notNullDo(req.getScheduleId(), c -> criteria.andScheduleIdEqualTo(req.getScheduleId()));
        ObjectUtils.notNullDo(req.getCreativeId(), c -> criteria.andCreativeIdEqualTo(req.getCreativeId()));
        List<GdCreativePo> gdCreativePoList = gdCreativeDao.selectByExample(gdCreativePoExample);
        Map<Long, GdCreativePo> gdCreativePoMap = gdCreativePoList.stream()
                .collect(Collectors.toMap(GdCreativePo::getCreativeId, Function.identity(), (v1, v2) -> v1));
        Map<Long, CreativeDrawGestureDto> creativeDrawGestureDtoMap = creativeDrawGestureService.getCreativeDrawGestures(new ArrayList<>(gdCreativePoMap.keySet()));
        return creativeDrawGestureDtoMap.entrySet().stream()
                .map(entry -> {
                    Long creativeId = entry.getKey();
                    CreativeDrawGestureDto creativeDrawGestureDto = entry.getValue();
                    GdCreativePo gdCreativePo = gdCreativePoMap.get(creativeId);

                    return DrawGestureQueryResultItem.builder()
                            .orderId(gdCreativePo.getOrderId())
                            .scheduleId(gdCreativePo.getScheduleId())
                            .creativeId(gdCreativePo.getCreativeId())
                            .creativeName(gdCreativePo.getCreativeName())
                            .iosDrawAngle(creativeDrawGestureDto.getIosDrawAngle())
                            .iosDrawLength(creativeDrawGestureDto.getIosDrawLength())
                            .androidDrawAngle(creativeDrawGestureDto.getAndroidDrawAngle())
                            .androidDrawLength(creativeDrawGestureDto.getAndroidDrawLength())
                            .ctime(TimeUtil.timestampToIsoTimeStr(gdCreativePo.getCtime()))
                            .mTime(TimeUtil.timestampToIsoTimeStr(gdCreativePo.getMtime()))
                            .build();
                }).collect(Collectors.toList());
    }
}
