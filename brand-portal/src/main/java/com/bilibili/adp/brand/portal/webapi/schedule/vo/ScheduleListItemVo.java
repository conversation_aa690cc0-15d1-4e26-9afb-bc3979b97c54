package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by zhongyuan on 2016/12/9.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("排期列表项")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ScheduleListItemVo {

	@ApiModelProperty("排期ID")
	private int schedule_id;

	@ApiModelProperty("排期名")
	private String name;

	@ApiModelProperty("开关")
	private int status;

	@ApiModelProperty("状态描述：1-已删除 2-已结束 3-待补量 4-已暂停 5-有效")
	private String status_desc;

	@ApiModelProperty("订单ID")
	private int order_id;

	@ApiModelProperty("订单名")
	private String order_name;

	@ApiModelProperty("广告位ID")
	private int slot_id;

	@ApiModelProperty("广告位名")
	private String slot_name;

	@ApiModelProperty("排期时间")
	private List<String> time_segments;

	@ApiModelProperty("投放小时段")
	private List<String> hour_range;
	
	@ApiModelProperty("排期类型：0-新建 1-补量")
	private int type;

	@ApiModelProperty("补量目标排期ID")
	private int complemented_id;

	@ApiModelProperty("合约刊例总价, 单位:元")
	private BigDecimal total_price;

	@ApiModelProperty("合约展现量")
	private long total_impression;
	
	@ApiModelProperty("是否可删除")
    private boolean enable_delete;

	@ApiModelProperty(notes = "展示数量")
    private Long show_count = 0L;
    @ApiModelProperty(notes = "点击次数")
    private Integer click_count = 0;
    @ApiModelProperty(notes = "点击率")
    private BigDecimal click_rate = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal cost_per_click = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private BigDecimal average_cost_per_thousand = BigDecimal.ZERO;
    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost = BigDecimal.ZERO;
    
	@ApiModelProperty("广告位组id")
	private Integer slot_group_id;
	
	@ApiModelProperty("广告位组名称")
	private String slot_group_name;
	
	@ApiModelProperty("投放位置：0广告位组 1广告位")
	private Integer put_location;

	@ApiModelProperty("是否可编辑")
	private boolean enable_edit;
	
	@ApiModelProperty("是否是当天排期：0-是，1-否")
    private boolean is_today_schedule;
    
	@ApiModelProperty("当天排期起始小时数")
    private int hour;
    
	@ApiModelProperty("频次单元(1-日 2-周 3-月)")
    private Integer frequency_unit;
    
	@ApiModelProperty("频次限制")
    private Integer frequency_limit;
	
	@ApiModelProperty("是否可创建排期")
	private boolean enable_create;

	/**
	 * 开始时间
	 */
	private String begin_date;

	/**
	 * 结束时间
	 */
	private String end_date;

	private Integer sales_type;

	@ApiModelProperty("创意个数")
	private Long creative_count;

	@ApiModelProperty("广告类型")
	private Integer ad_type;
	@ApiModelProperty("广告类型描述")
	private String ad_type_desc;
	@ApiModelProperty("资源类型")
	private String res_type_desc;

	@ApiModelProperty("模板id")
	private Integer template_id;

	@ApiModelProperty("模板名称")
	private String template_name;

	@ApiModelProperty("创意类型")
	private Integer creative_style;

	@ApiModelProperty("创意类型名称")
	private String creative_style_desc;

	@ApiModelProperty("落地页是否支持分享")
	private boolean share_enable;

	@ApiModelProperty("排期下的创意名称列表")
	private List<String> creative_names;

	@ApiModelProperty("是否创建完成 0-创建完成 1-创建中 2-创建失败")
	private Integer finish_flag;

	@ApiModelProperty("创建失败原因")
	private String fail_msg;

	@ApiModelProperty("推广类型")
	private Integer promotion_purpose_type;

	@ApiModelProperty("推广类型描述")
	private String promotion_purpose_type_desc;

	@ApiModelProperty("展示优先级，0：常规展示，1：优先展示")
	private Integer show_priority;

	@ApiModelProperty("产品类型")
	private Integer order_product;

	@ApiModelProperty("播放量")
	private Long play_nums;

	@ApiModelProperty("播放成本")
	private BigDecimal play_cost;

	@ApiModelProperty("播放率")
	private BigDecimal play_rate;

	@ApiModelProperty("adx deal_id")
	private String deal_id;

	@ApiModelProperty("GD定向条件")
	private GdTargetVo target;

	@ApiModelProperty("展示方式")
	private Integer show_style;

	@ApiModelProperty("展示方式描述")
	private String show_style_desc;

	private Integer platform_id;

	@ApiModelProperty("dealGroupId")
	private String deal_group_id;


	@ApiModelProperty("模板cardType")
	private Integer templateCardType;


	@Deprecated
	@ApiModelProperty("闪屏第二屏展示方式，0-无，1-沉浸视频")
	private Integer ssaSecondShowStyle;

	@ApiModelProperty("mid")
	private String uid;

	@ApiModelProperty("是否开启企业空间（0-关闭 1-开启）")
	private Integer openEnterpriseSpace;

	@ApiModelProperty("闪屏相关：交互方式 0-点击交互 1-滑动交互")
	private Integer interact_style;

	@ApiModelProperty("闪屏相关：跳转模块动效 0-无动效 1-跳动动效")
	private Integer jump_area_effect;

	@ApiModelProperty("闪屏相关：按钮类型 0-点击交互 1-滑动交互 2-选择交互")
	private Integer button_style;

	@ApiModelProperty("TopView排期信息")
	@JsonProperty("topView_info")
	private TopViewScheduleInfoVo topView_info;

	@ApiModelProperty("闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频")
	private Integer ssaVideoPlayMode;

	@ApiModelProperty("DSP Source ID")
	private String dspSourceId;

	@ApiModelProperty("闪屏按钮类型，按钮类型（点击/滑动/扭一扭/摇一摇）")
	private String ssaButtonStyle;

	@ApiModelProperty("ott闪屏类型")
	private Integer ottScreenType;

	@ApiModelProperty("ott闪屏样式 0-视频闪屏 1-图片闪屏")
	private Integer ottScreenStyle;

	@ApiModelProperty("预占轮数")
	private Integer rotationNum;

	@ApiModelProperty("定向聚合信息")
	private GdTargetAggregationVo targetAggregation;

	@ApiModelProperty("唤起应用类型")
	private Integer wake_app_type;

	@ApiModelProperty("业务售卖类型 0-CPT 1-CPM")
	private Integer biz_sales_type;

	@ApiModelProperty("是否支持创意分端，主要用于在创建创意时是否支持分端填写素材，以便生成多端创意")
	private boolean supports_creative_split_platform;

	//单排期可能对应多平台，如版位合并
	@ApiModelProperty("平台")
	private List<Integer> platform_id_list;

	@ApiModelProperty("展示方式")
	private Integer display_mode;

	@ApiModelProperty("投放方式，0：天，1：小时")
	private Integer schedule_style;

	/**
	 * @see com.bilibili.enums.ScheduleLaunchSceneEnum
	 */
	@ApiModelProperty("投放场景")
	private Integer launch_scene;

	@ApiModelProperty("投放主体: 0-蓝V号 1-自建主体")
	private Integer launch_obj;

	@ApiModelProperty("指定up粉丝")
	private List<Long> up_mid_list;

	@ApiModelProperty("指定up粉丝等级")
	private Integer up_fans_level;

	@ApiModelProperty("排期是否可拆分")
	private Boolean is_split_allowed;

	@ApiModelProperty("交互后效果类型，0：默认（无），1：交互彩蛋")
	private Integer effective_type;
}
