package com.bilibili.adp.brand.portal.webapi.agent;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.DateUtils;
import com.bilibili.adp.common.util.Utils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.DateUtil;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/8/26
 **/
@Data
public class AgentStaticImportVo {

    /**
     * 代理商公司ID(作为es查询唯一键)
     */
    @ExcelProperty(index = 0)
    private Integer companyId;

    /**
     * 代理商公司名称
     */
    @ExcelProperty(index = 1)
    private String companyName;

    /**
     * 代理资质
     */
    @ExcelProperty(index = 2)
    private String agentQualification;

    /**
     * 授权有效期开始
     */
    @ExcelProperty(index = 3, converter = DateListConverter.class)
    private List<Timestamp> startDate;

    /**
     * 授权有效期结束
     */
    @ExcelProperty(index = 4, converter = DateListConverter.class)
    private List<Timestamp> endDate;

    /**
     * 服务地区
     */
    @ExcelProperty(index = 5)
    private String area;

    /**
     * 业务范围
     */
    @ExcelProperty(index = 6)
    private String businessScope;

    /**
     * 服务认证-代理商星级
     */
    @ExcelProperty(index = 7)
    private String serviceCertification;

    /**
     * 授权有效期开始
     */
    @ExcelProperty(index = 8, converter = DateListConverter.class)

    private List<Timestamp> certStartDate;

    /**
     * 授权有效期结束
     */
    @ExcelProperty(index = 9, converter = DateListConverter.class)
    private List<Timestamp> certEndDate;

    /**
     * 联系方式
     */
    @ExcelProperty(index = 10)
    private String contactInfo;

    /**
     * 联系邮箱
     */
    @ExcelProperty(index = 11)
    private String email;

    /**
     * 企业logo
     */
    @ExcelProperty(index = 12)
    private String logoUrl;

    /**
     * 关联词条
     */
    @ExcelProperty(index = 13)
    private String relateWord;

    /**
     * 主营行业
     */
    @ExcelProperty(index = 14)
    private String mainIndustry;

    /**
     * 认证类型（花火才有）
     */
    @ExcelProperty(index = 15)
    private String certificationType;

    public static class DateListConverter implements Converter<List<Timestamp>> {

        @Override
        public Class supportJavaTypeKey() {
            return List.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public List<Timestamp> convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String stringValue = cellData.getStringValue();
            List<Timestamp> dates = new ArrayList<>();
            if (StringUtils.isBlank(stringValue)) {
                BigDecimal numberValue = cellData.getNumberValue();
                if (Objects.nonNull(numberValue)) {
                    dates.add(convertNumber(numberValue, excelContentProperty, globalConfiguration));
                }
            } else {
                String[] split = stringValue.split(",");
                for (String s : split) {
                    dates.add(convertString(s, excelContentProperty, globalConfiguration));
                }
            }
            return dates;
        }

        @Override
        public CellData convertToExcelData(List<Timestamp> dates, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            String format = excelContentProperty != null && excelContentProperty.getDateTimeFormatProperty() != null ? excelContentProperty.getDateTimeFormatProperty().getFormat() : "yyyy/MM/dd";
            if (CollectionUtils.isNotEmpty(dates)) {
                return new CellData(dates.stream().map(t -> Utils.getTimestamp2String(t, format)).collect(Collectors.joining(",")));
            }
            return new CellData("");
        }

        private Timestamp convertNumber(BigDecimal data, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
            Date date = contentProperty != null && contentProperty.getDateTimeFormatProperty() != null ? DateUtil.getJavaDate(data.doubleValue(), contentProperty.getDateTimeFormatProperty().getUse1904windowing(), (TimeZone) null) : DateUtil.getJavaDate(data.doubleValue(), globalConfiguration.getUse1904windowing(), (TimeZone) null);
            return Utils.utilDateToTimestamp(date);
        }

        private Timestamp convertString(String data, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws ParseException {
            return contentProperty != null && contentProperty.getDateTimeFormatProperty() != null ? Utils.getTimestamp(data, contentProperty.getDateTimeFormatProperty().getFormat()) : Utils.getTimestamp(data, "yyyy/MM/dd");
        }
    }
}
