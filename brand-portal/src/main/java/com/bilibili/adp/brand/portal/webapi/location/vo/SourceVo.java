package com.bilibili.adp.brand.portal.webapi.location.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2017年6月13日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class SourceVo {
	
    @ApiModelProperty("广告位ID")
    private Integer source_id;

    @ApiModelProperty("广告位名称")
    private String source_name;

    @ApiModelProperty("平台ID")
    private Integer platform_id;

    @ApiModelProperty("广告位名称")
    private String platform_name;

    @ApiModelProperty("外部刊例价,单位(分)")
    private BigDecimal external_price;
    
    @ApiModelProperty("外部刊例价,单位(分)")
    private BigDecimal internal_price;
    
    @ApiModelProperty("是否可配送:0-否 1-是")
    private Integer is_distribution;

    @ApiModelProperty("是否支持增强版")
    private Boolean support_strong_version;

}
