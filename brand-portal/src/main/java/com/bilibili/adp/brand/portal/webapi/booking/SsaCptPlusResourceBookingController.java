package com.bilibili.adp.brand.portal.webapi.booking;

import com.bilibili.adp.brand.portal.webapi.booking.vo.SsaPlusBookingItemVo;
import com.bilibili.adp.brand.portal.webapi.booking.vo.SsaPlusDateBookingItemVo;
import com.bilibili.adp.brand.portal.webapi.booking.vo.SsaPlusStockVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.account.dto.AccountDto;
import com.bilibili.brand.api.common.enums.GdOrderStatus;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.account.service.QueryAccountService;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.CptBookingStatus;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.ssa.platform.api.location.dto.SsaCycleDto;
import com.bilibili.ssa.platform.api.location.dto.SsaQueryCycleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmQueryScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaCpmScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.dto.QuerySplashScreenParamDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.biz.service.location.SsaCycleDelegate;
import com.bilibili.ssa.platform.common.enums.OrderProduct;
import com.bilibili.ssa.platform.common.enums.SsaAdType;
import com.bilibili.ssa.platform.common.enums.SsaConstants;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenStatus;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: CPT资源预定、资源锁定
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Controller
@RequestMapping("/web_api/v1/ssa/plus/booking")
@Api(value = "/ssa/plus/booking", description = "新版闪屏+资源预定")
@Slf4j
public class SsaCptPlusResourceBookingController extends BaseController {

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private SsaCycleDelegate cycleDelegate;

    @Autowired
    private QueryAccountService accountService;

    @Autowired
    private IGdOrderService orderService;

    @Autowired
    private ISsaCpmScheduleService ssaCpmScheduleService;

    @Autowired
    private ISsaSplashScreenService ssaSplashScreenService;

    @ApiOperation(value = "SSA查询库存使用情况")
    @RequestMapping(value = "/stock", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<SsaPlusStockVo>> querySsaPlusStock(
            @ApiIgnore Context context,
            @ApiParam("开始时间")
            @RequestParam("begin_time") Long begin_time,
            @ApiParam("结束日期")
            @RequestParam("end_time") Long end_time) throws ServiceException {
        Timestamp beginTime = new Timestamp(begin_time);
        Timestamp endTime = new Timestamp(end_time);

        List<SsaCycleDto> cycleDtos = cycleDelegate.query(SsaQueryCycleDto.builder().beginTime(beginTime)
                .endTime(endTime).adType(SsaAdType.VIDEO.getCode()).status(1)
                .orderProduct(OrderProduct.SSA_CPT.getCode()).build());
        Assert.notEmpty(cycleDtos, "当前时间内查询不到有效的刊例周期!");
//        Assert.isTrue(cycleDtos.size() == 1, "不可跨刊例查询!");
        SsaCycleDto cycleDto = cycleDtos.get(0);

        //内外部已使用流量信息
        List<ScheduleDto> scheduleDtoS = queryScheduleService.querySchedule(QueryScheduleDto.builder()
                .startTime(beginTime)
                .endTime(endTime)
                .orderProducts(SsaConstants.SSA_PLUS_ORDER_PRODUCTS)
                .statusList(Lists.newArrayList(1, 2)).build());
        AccountDto accountDto = accountService.getAccount(context.getAccountId());
        Map<Timestamp, List<ScheduleDto>> listMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(scheduleDtoS)) {
            //账户类型
            Map<Integer, Integer> accountTypeMap = accountService.getAccountDtosInAccountIds(
                    scheduleDtoS.stream().map(ScheduleDto::getAccountId)
                            .collect(Collectors.toList())).stream().collect(Collectors.toMap(AccountDto::getAccountId,
                    AccountDto::getIsInner));

            listMap = scheduleDtoS.stream()
                    .filter(t -> {
                        Integer isInner = accountDto.getIsInner();
                        return isInner.equals(accountTypeMap.get(t.getAccountId()));
                    }).collect(Collectors.groupingBy(ScheduleDto::getBeginDate));
        }

        List<SsaPlusStockVo> stockVos = new ArrayList<>();
        LocalDate begin = TimeUtil.timestampToLocalDate(cycleDto.getBeginTime());
        LocalDate end = TimeUtil.timestampToLocalDate(cycleDto.getEndTime()).plusDays(1);
        for (; begin.isBefore(end); begin = begin.plusDays(1)) {
            Timestamp date = TimeUtil.localDateToTimestamp(begin);
            List<ScheduleDto> scheduleS = listMap.get(date);
            long used = 0;
            if (!CollectionUtils.isEmpty(scheduleS)) {
                used = scheduleS.stream().mapToInt(ScheduleDto::getTotalImpression).sum();
            }

            stockVos.add(SsaPlusStockVo.builder().used_cpm(used).available_cpm(10000L).date(date.getTime()).build());
        }
        log.info("querySsaPlusStock cycle [{}], stockVos [{}]", cycleDto, stockVos);

        stockVos = stockVos.stream().sorted(Comparator.comparing(SsaPlusStockVo::getDate))
                .collect(Collectors.toList());
        return Response.SUCCESS(stockVos);
    }

    @ApiOperation(value = "SSA查询预占使用情况")
    @RequestMapping(value = "/order", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<SsaPlusDateBookingItemVo>> querySsaPlusOrder(
            @ApiIgnore Context context,
            @ApiParam("开始时间")
            @RequestParam("begin_time") Long begin_time,
            @ApiParam("结束日期")
            @RequestParam("end_time") Long end_time) throws ServiceException {

        Timestamp beginTime = new Timestamp(begin_time);
        Timestamp endTime = new Timestamp(end_time);

        List<ScheduleDto> scheduleDtoS = queryScheduleService.querySchedule(QueryScheduleDto.builder()
                .startTime(beginTime)
                .endTime(endTime)
                .orderProducts(SsaConstants.SSA_PLUS_ORDER_PRODUCTS)
                .statusList(Lists.newArrayList(1, 2)).build());
        if (CollectionUtils.isEmpty(scheduleDtoS)) {
            return Response.SUCCESS(null);
        }

        Map<Timestamp, List<ScheduleDto>> scheduleMap = scheduleDtoS.stream()
                .collect(Collectors.groupingBy(ScheduleDto::getBeginDate));

        Map<Integer, GdOrderDto> orderDtoMap = orderService
                .getOrderMapInOrderIds(scheduleDtoS.stream().map(ScheduleDto::getOrderId)
                        .collect(Collectors.toList()));

        Map<Integer, AccountDto> accountMap = accountService.getAccountDtosInAccountIds(
                scheduleDtoS.stream().map(ScheduleDto::getAccountId)
                        .collect(Collectors.toList())).stream().collect(Collectors.toMap(AccountDto::getAccountId,
                t -> t));

        List<SsaCpmScheduleDto> ssaCpmScheduleDtos = ssaCpmScheduleService
                .querySSaCpmSchedule(SsaCpmQueryScheduleDto.builder().gdScheduleIds(scheduleDtoS.stream()
                        .map(ScheduleDto::getScheduleId).collect(Collectors.toList())).build());
        Map<Integer, SsaCpmScheduleDto> ssaCpmScheduleMap = ssaCpmScheduleDtos.stream()
                .collect(Collectors.toMap(SsaCpmScheduleDto::getGdScheduleId, Function.identity()));

        Timestamp now = TimeUtil.getBeginOfDay(Timestamp.valueOf(LocalDateTime.now()));
        Set<Integer> validSsaSchedules = Sets.newHashSet();
        List<Integer> pastSchedules = scheduleDtoS.stream()
                .filter(s -> {
                    boolean isPast = now.compareTo(s.getEndDate()) > 0;
                    if (!isPast) {
                        //非历史排期，则暂时认为存在有效创意
                        validSsaSchedules.add(s.getScheduleId());
                    }
                    return isPast;
                }).map(ScheduleDto::getScheduleId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(pastSchedules)) {
            List<SsaSplashScreenDto> ssaSplashScreenList = ssaSplashScreenService.getSsaSplashScreensSimple(
                    QuerySplashScreenParamDto.builder()
                            .gdScheduleIds(pastSchedules)
                            .statusList(SsaSplashScreenStatus.AUDIT_PASS_STATUS)
                            .build());
            validSsaSchedules.addAll(ssaSplashScreenList.stream()
                    .map(SsaSplashScreenDto::getGdScheduleId)
                    .collect(Collectors.toSet()));
        }

        List<SsaPlusDateBookingItemVo> dateBookingItemVos = new ArrayList<>();
        scheduleMap.forEach((k, v) -> {
            SsaPlusDateBookingItemVo dateBookingItemVo = new SsaPlusDateBookingItemVo();
            dateBookingItemVo.setDate(k.getTime());
            List<SsaPlusBookingItemVo> itemVoList = new ArrayList<>();
            List<SsaPlusBookingItemVo> finalItemVoList = itemVoList;
            //屏蔽已失效的订单，防止all fail
            v.stream().filter(t -> orderDtoMap.containsKey(t.getOrderId())).forEach(t -> {
                int count = TimeUtils.countDays(new Timestamp(System.currentTimeMillis()), t.getBeginDate());
                GdOrderDto orderDto = orderDtoMap.get(t.getOrderId());
                AccountDto accountDto = accountMap.get(orderDto.getAccountId());
                OrderProduct orderProduct = OrderProduct.getByCode(orderDto.getProduct());
                String desc = accountDto.getUsername() + "-" + orderProduct.getDesc();
                if (orderProduct == OrderProduct.SSA_GD_PLUS || orderProduct == OrderProduct.TOP_VIEW_GD_PLUS) {
                    desc += "-" + t.getTotalImpression() + "cpm";
                } else {
                    desc += String.format("-%d轮-%dcpm",
                            ssaCpmScheduleMap.get(t.getScheduleId()).getRotationNum(),
                            ssaCpmScheduleMap.get(t.getScheduleId()).getTotalImpression());
                }
                finalItemVoList.add(SsaPlusBookingItemVo.builder()
                        .booking_status(CptBookingStatus.BOOKED_SCHEDULED.getCode())
                        .booking_status_desc(CptBookingStatus.BOOKED_SCHEDULED.getDesc())
                        .releasing(SsaConstants.SSA_PLUS_RELEASE_GAP.contains(count) && !GdOrderStatus.AUDIT_PASS_STATUS_LIST
                                .contains(orderDto.getGdOrderStatus()) && CollectionUtils.isEmpty(t.getCreativeNames()))
                        .desc(desc)
                        .date(t.getBeginDate().getTime())
                        .dependency_order_id(t.getOrderId())
                        .dependency_order_name(orderDto.getOrderName())
                        .order_product(orderDto.getProduct())
                        .operator(orderDto.getCreatorName())
                        .dependency_schedule_id(t.getScheduleId())
                        .is_my_booking(context.getAccountId().equals(t.getAccountId()) ? 1 : 0)
                        .has_valid_ssa(validSsaSchedules.contains(t.getScheduleId()))
                        .build());
            });
            itemVoList = itemVoList.stream().sorted(Comparator.comparing(SsaPlusBookingItemVo::getOrder_product))
                    .collect(Collectors.toList());
            dateBookingItemVo.setItemVoList(itemVoList);
            dateBookingItemVos.add(dateBookingItemVo);
        });

        return Response.SUCCESS(dateBookingItemVos.stream()
                .sorted(Comparator.comparing(SsaPlusDateBookingItemVo::getDate)).collect(Collectors.toList()));
    }

}
