package com.bilibili.adp.brand.portal.filter;


import com.bilibili.utils.TraceUtils;
import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2022/10/31 21:40
 */
public class TraceFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
            ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String traceId = httpRequest.getHeader(TraceUtils.TRACE_ID);
        if (traceId == null || traceId.isEmpty()) {
            traceId = TraceUtils.genTraceId();
        }
        try {
            MDC.put(TraceUtils.TRACE_ID, traceId);
            chain.doFilter(request, response);
        } finally {
            MDC.remove(TraceUtils.TRACE_ID);
        }
    }


    public void destroy() {

    }
}

