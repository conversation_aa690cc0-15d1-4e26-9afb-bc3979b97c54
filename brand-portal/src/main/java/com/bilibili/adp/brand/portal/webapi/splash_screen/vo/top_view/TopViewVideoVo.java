package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewVideoVo {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "topView ID")
    private Integer top_view_id;

    @ApiModelProperty(value = "转码后视频URL")
    private String xcode_upos_url;

    @ApiModelProperty(value = "转码后视频MD5")
    private String xcode_md5;

    @ApiModelProperty(value = "转码后视频宽度")
    private Integer xcode_width;

    @ApiModelProperty(value = "转码后视频高度")
    private Integer xcode_height;

    @ApiModelProperty(value = "视频状态")
    private Integer status;

    @ApiModelProperty(value = "视频状态描述")
    private String status_desc;

    @ApiModelProperty(value = "bizId")
    private Integer biz_id;

    @ApiModelProperty(value = "视频URL")
    private String upos_url;

    @ApiModelProperty(value = "视频Auth")
    private String upos_auth;

    @ApiModelProperty(value = "视频名称")
    private String file_name;

    @ApiModelProperty(value = "外部可访问URL")
    private String url;

    @ApiModelProperty(value = "内部可访问URL")
    private String xcode_before_url;
}
