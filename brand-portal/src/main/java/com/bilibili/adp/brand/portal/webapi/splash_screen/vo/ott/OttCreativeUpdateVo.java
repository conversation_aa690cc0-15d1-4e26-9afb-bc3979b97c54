package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.ott;

import com.bilibili.adp.brand.portal.common.ProductLabelVo;
import com.bilibili.adp.brand.portal.config.validator.URLCollection;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SsaQrInfoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SsaScheduleSelectVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaNewSplashScreenVideoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OttCreativeUpdateVo {

    @ApiModelProperty(value = "创意ID")
    private Integer id;

    /**
     * 闪屏标题
     */
    @ApiModelProperty(value = "闪屏标题")
    private String title;

    /**
     * 展示样式: 1-全屏 2-半屏
     */
    @ApiModelProperty(value = "展示样式: 1-全屏 2-半屏")
    private Integer show_style;

    /**
     * 是否可跳过: 0-否 1-是
     */
    @ApiModelProperty(value = "是否可跳过: 0-否 1-是")
    private Integer is_skip;

    /**
     * 闪屏文案
     */
    @ApiModelProperty(value = "闪屏文案")
    private String copy_writing;

    /**
     * 广告角标(1:广告，2:推广)
     */
    @ApiModelProperty(value = "广告角标(1:广告，2:推广)")
    private Integer cm_mark;

    /**
     * 闪屏基础图片
     */
    @ApiModelProperty("闪屏基础图片")
    private List<SplashScreenBaseImageVo> base_image_vos;

    /**
     * 闪屏视频
     */
    @ApiModelProperty("闪屏视频")
    private SsaNewSplashScreenVideoVo video;

    /**
     * 时间定向
     */
    @ApiModelProperty("时间定向: 0-禁用，1-启用")
    private Integer time_target;

    /**
     * 排期
     */
    @ApiModelProperty("选择的排期")
    private List<SsaScheduleSelectVo> schedules;

    /**
     * 下发时间: 1-过审下发 2-排期开始前2小时下发
     */
    @ApiModelProperty(value = "下发时间: 1-过审下发 2-排期开始前2小时下发")
    private Integer issued_time;

    @URLCollection(message = "自定义点击监控链接列表不合法，请检查你的链接后再试")
    @ApiModelProperty(value = "点击监控链接列表")
    private List<String> customized_click_url_list;

    @URLCollection(message = "自定义展示监控链接列表不合法，请检查你的链接后再试")
    @ApiModelProperty(value = "展示监控链接列表")
    private List<String> customized_imp_url_list;

    /**
     * 跳转类型
     */
    @ApiModelProperty(value = "二维码跳转类型 1-跳转链接 6-落地页")
    private Integer qr_jump_type;

    @ApiModelProperty(value = "二维码跳转链接")
    private String qr_jump_link;

    @ApiModelProperty(value = "二维码弹出时间 单位毫秒")
    private Integer qr_pop_time;

    @ApiModelProperty(value = "二维码额外信息")
    private SsaQrInfoVo qr_ext_info;

    @ApiModelProperty(value = "闪屏分端跳转模型列表")
    private List<SplashScreenJumpVo> splash_screen_jump_vos;

    @ApiModelProperty("产品型号")
    private ProductLabelVo product_label;

}