package com.bilibili.adp.brand.portal.convert.schedule;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.SplitDaysImpressHolder;
import com.bilibili.brand.api.schedule.dto.SsaPdScheduleDto;
import com.bilibili.brand.biz.schedule.handler.SsaFestivalPriceRaiseHandler;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;
import com.bilibili.ssa.platform.common.enums.SsaButtonStyle;
import com.bilibili.ssa.platform.common.enums.SsaClickAreaType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.common.enums.SystemConfigEnum.GD_PRICE_FESTIVAL_DAYS;


/**
 * @Description ssa_pd排期转换工具类
 * <AUTHOR>
 * @Date 2020.05.26 18:17
 */

@Slf4j
@Component
public class SsaPDScheduleConvert {

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    protected ISystemConfigService systemConfigService;

    @Resource
    private SsaFestivalPriceRaiseHandler ssaFestivalPriceRaiseHandler;

    public List<SsaPdScheduleDto> convert2Dtos(SsaPDScheduleAddVo vo) {
        List<SplitDaysImpressVo> splitDaysImpressVos = vo.getSplitDaysImpressVos();
        List<SsaPdScheduleDto> res = new ArrayList<>();

        // 处理deal_group_id
        // https://www.tapd.cn/67874887/prong/stories/view/1167874887004445447
        if (StringUtils.isEmpty(vo.getDealGroupId())) {
            vo.setDealGroupId(String.valueOf(snowflakeIdWorker.nextId()));
        }

        // 是否需要跳过加收 如果跳过加收下面不会根据营销节点拆分排期
        boolean needSkipRaise = ssaFestivalPriceRaiseHandler.needSkipRaise(vo.getOrderId());
        // 先讲splitDaysImpressVos 按照开始时间排期
        splitDaysImpressVos.sort(Comparator.comparing(SplitDaysImpressVo::getBeginTime));
        // 将2023-01-01,2023-01-02,2023-01-05,2023-01-06 切分成 [2023-01-01,2023-01-02],[2023-01-05,2023-01-06]
        List<SplitDaysImpressVo> splitDaysImpressVosNew = new ArrayList<>();
        if (splitDaysImpressVos.size() == 1) {
            SsaPdScheduleDto ssaPdScheduleDto = convert2Dto(vo, splitDaysImpressVos, needSkipRaise);
            Assert.isTrue(Objects.equals(ssaPdScheduleDto.getTotalImpression(), vo.getTotalImpression()),
                    "自定义分配的目标展示，与整体目标展示量不相等，请修改后重试");
            return Collections.singletonList(ssaPdScheduleDto);
        }
        SplitDaysImpressVo preDayImpress = splitDaysImpressVos.get(0);
        if (Objects.equals(vo.getIsPeriodBooking(), true)) {
            // 周期预定总量分到第一天，后面的为0
            preDayImpress.setImpressionCpm((long) vo.getTotalImpression());
        }
        splitDaysImpressVosNew.add(preDayImpress);
        String value = systemConfigService.getValue(GD_PRICE_FESTIVAL_DAYS.getCode());
        List<String> festivalDays = null;
        if (!StringUtils.isEmpty(value)) {
            festivalDays = Arrays.stream(value.split(",")).map(String::trim).collect(Collectors.toList());
        }
        long totalImpression = 0;
        for (int i = 1; i < splitDaysImpressVos.size(); i++) {
            String preDayDateTime = preDayImpress.getEndTime();
            SplitDaysImpressVo dayImpress = splitDaysImpressVos.get(i);
            String beginTime = dayImpress.getBeginTime();
            if (TimeUtil.areDateTimesContinuous(Arrays.asList(preDayDateTime, beginTime)) && (needSkipRaise || PdScheduleConvert.isFestivalSame(festivalDays, preDayDateTime, beginTime))) {
                splitDaysImpressVosNew.add(dayImpress);
                preDayImpress = dayImpress;
            } else {
                // 不连续先处理之前日期
                Assert.isTrue(Objects.equals(vo.getIsPeriodBooking(), false), "周期预定的排期日期必须连续并且不能同时包含营销节点和非营销节点!");
                SsaPdScheduleDto ssaPdScheduleDto = convert2Dto(vo, splitDaysImpressVosNew, needSkipRaise);
                totalImpression += ssaPdScheduleDto.getTotalImpression();
                res.add(ssaPdScheduleDto);
                // 清空list
                splitDaysImpressVosNew.clear();
                preDayImpress = dayImpress;
                splitDaysImpressVosNew.add(preDayImpress);
            }
        }
        if (!CollectionUtils.isEmpty(splitDaysImpressVosNew)) {
            SsaPdScheduleDto ssaPdScheduleDto = convert2Dto(vo, splitDaysImpressVosNew, needSkipRaise);
            res.add(ssaPdScheduleDto);
            totalImpression += ssaPdScheduleDto.getTotalImpression();
        }
        Assert.isTrue(Objects.equals(totalImpression, (long) vo.getTotalImpression()),
                "自定义分配的目标展示，与整体目标展示量不相等，请修改后重试");
        return res;
    }

    public static SsaPdScheduleDto convert2Dto(SsaPDScheduleAddVo vo, List<SplitDaysImpressVo> splitDaysImpressVos, boolean needSkipRaise) {
        SsaPdScheduleDto dto = new SsaPdScheduleDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setClickArea(vo.getMaterialClickArea());
        dto.setTarget(convert2ssaScheduleTargetDto(vo.getTarget()));
//        dto.setBeginDate(TimeUtils.getBeginOfDay(vo.getBeginDate()));
//        dto.setEndDate(TimeUtils.getBeginOfDay(vo.getEndDate()));
        dto.setDealId(StringUtils.isEmpty(vo.getDealId()) ? null : Long.valueOf(vo.getDealId()));
        dto.setDealGroupId(StringUtils.isEmpty(vo.getDealGroupId()) ? null : Long.valueOf(vo.getDealGroupId()));
        if(SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())){
            dto.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }
        SplitDaysImpressHolder splitDaysImpressHolder = SplitDaysImpressConverter.MAPPER
                .toSplitDaysImpressHolder(splitDaysImpressVos);
        dto.setBeginDate(Utils.getBeginOfDay(splitDaysImpressHolder.getStartTime()));
        dto.setEndDate(Utils.getEndOfDay(splitDaysImpressHolder.getEndTime()));
        dto.setBeginTime(splitDaysImpressHolder.getStartTime());
        dto.setEndTime(splitDaysImpressHolder.getEndTime());
        dto.setSplitDaysImpress(splitDaysImpressHolder.getDaysImpress());
        dto.setTotalImpression(Math.toIntExact(splitDaysImpressHolder.getTotalImpression()));
        dto.setNeedSkipFestivalRaise(needSkipRaise);
        dto.setIsPeriodBooking(BooleanUtils.isTrue(vo.getIsPeriodBooking()) ? 1 : 0);
        return dto;
    }

    public static SsaPdScheduleDto convert2Dto(SsaPDScheduleUpdateVo vo){
        SsaPdScheduleDto dto = new SsaPdScheduleDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setClickArea(vo.getMaterialClickArea());
        dto.setTarget(convertGd2SsaScheduleTargetDto(vo.getTarget()));
        dto.setGdScheduleId(vo.getScheduleId());
//        dto.setBeginDate(TimeUtils.getBeginOfDay(vo.getBeginDate()));
//        dto.setEndDate(TimeUtils.getBeginOfDay(vo.getEndDate()));
        if(SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())){
            dto.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }
        SplitDaysImpressHolder splitDaysImpressHolder = SplitDaysImpressConverter.MAPPER
                .toSplitDaysImpressHolder(vo.getSplitDaysImpressVos());
        dto.setBeginDate(Utils.getBeginOfDay(splitDaysImpressHolder.getStartTime()));
        dto.setEndDate(Utils.getEndOfDay(splitDaysImpressHolder.getEndTime()));
        dto.setBeginTime(splitDaysImpressHolder.getStartTime());
        dto.setEndTime(splitDaysImpressHolder.getEndTime());
        dto.setSplitDaysImpress(splitDaysImpressHolder.getDaysImpress());
        dto.setWakeAppType(vo.getWakeAppType());
        return dto;
    }

    private static SsaScheduleTargetDto convert2ssaScheduleTargetDto(SsaScheduleTargetVo vo){
        if(vo == null){
            return new SsaScheduleTargetDto();
        }
        return SsaScheduleTargetDto.builder().age(vo.getAge()).area(vo.getArea()).gender(vo.getGender())
                .os(vo.getOs()).build();
    }

    private static SsaScheduleTargetDto convertGd2SsaScheduleTargetDto(GdTargetVo vo){
        if(vo == null){
            return new SsaScheduleTargetDto();
        }
        return SsaScheduleTargetDto.builder().age(vo.getAge() != null ? vo.getAge().stream()
                .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .area(vo.getArea() != null ? vo.getArea().stream()
                        .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .gender(vo.getGender() != null ? vo.getGender().stream()
                        .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .os(vo.getOs() != null ? vo.getOs().stream()
                        .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .build();
    }





}
