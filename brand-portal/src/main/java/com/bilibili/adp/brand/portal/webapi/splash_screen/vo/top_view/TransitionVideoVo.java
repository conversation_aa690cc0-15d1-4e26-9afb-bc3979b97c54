package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 过渡视频VO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TransitionVideoVo {

    @ApiModelProperty("视频URL")
    private String url;
    
    @ApiModelProperty("业务ID（IP视频库ID）")
    private Integer bizId;
    
    @ApiModelProperty("视频MD5")
    private String md5;
    
    @ApiModelProperty("视频宽度")
    private Integer width;
    
    @ApiModelProperty("视频高度")
    private Integer height;
    
    @ApiModelProperty("视频时长（毫秒）")
    private Long duration;
    
    @ApiModelProperty("视频大小（字节）")
    private Long size;
}
