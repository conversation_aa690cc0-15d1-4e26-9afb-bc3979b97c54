package com.bilibili.adp.brand.portal.helper;

import com.bilibili.adp.brand.portal.webapi.booking.vo.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.booking.dto.*;
import com.bilibili.brand.api.booking.service.IResourceBookingService;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.creative.dto.GdCreativeDto;
import com.bilibili.brand.api.creative.dto.QueryGdCreativeDto;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.cpt.platform.api.business_side.dto.BusinessSideBaseDto;
import com.bilibili.cpt.platform.api.business_side.service.IBusinessSideService;
import com.bilibili.cpt.platform.api.location.dto.CptSourceAllInfoDto;
import com.bilibili.cpt.platform.api.location.dto.CptSourceQueryDto;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.biz.dao.CptSourceHourBookingDao;
import com.bilibili.cpt.platform.biz.dao.CptSourceHourBookingLiveDao;
import com.bilibili.cpt.platform.biz.po.CptSourceHourBookingLivePo;
import com.bilibili.cpt.platform.biz.po.CptSourceHourBookingLivePoExample;
import com.bilibili.cpt.platform.biz.po.CptSourceHourBookingPo;
import com.bilibili.cpt.platform.biz.po.CptSourceHourBookingPoExample;
import com.bilibili.cpt.platform.common.CptBookingStatus;
import com.bilibili.cpt.platform.common.IsBookingDay;
import com.bilibili.ssa.platform.common.enums.SystemConfig;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.dianping.cat.Cat;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Slf4j
@Component
public class ResourceBookingConvertHelper {

    @Autowired
    private ICptSourceService cptSourceService;

    @Autowired
    private IBusinessSideService businessSideService;

    @Autowired
    private IGdCreativeService gdCreativeService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private CptSourceHourBookingLiveDao hourBookingLiveDao;

    @Value("${live.cpt.release.day:3}")
    private Integer liveReleaseDay;

    @Autowired
    private IResourceBookingService resourceBookingService;

    /**
     * 填充广告位数据
     *
     */
    public List<SourceItemVo> fillDecorateSourceItemVos(List<BookingItemDetailDto> detailDtos,
                                                        BookingQueryDto queryDto, Integer orderProduct) {
//        if (queryDto.isMyResource()) {
            //todo 暂时不做
//            return convertMyResourceDtos2Vos(detailDtos, queryDto, orderProduct);
//        } else {
            return convertAllResourceDtos2Vos(detailDtos, queryDto, orderProduct);
//        }
    }

    private List<SourceItemVo> convertAllResourceDtos2Vos(List<BookingItemDetailDto> detailDtos, BookingQueryDto queryDto, Integer orderProduct) {
        List<SourceItemVo> result = new ArrayList<>();
        Map<Integer, List<BookingItemDetailDto>> sourceIdResultMap = detailDtos.stream()
                .collect(Collectors.groupingBy(BookingItemDetailDto::getSourceId));

        // 我已经预定的和有权限预定的
        BusinessSideBaseDto sideBaseDto = businessSideService.getBusinessSideByAccountId(queryDto.getAccountId());
        List<Integer> sourceIds = sideBaseDto == null ? Collections.emptyList() :
                cptSourceService.getSourceIdsByBusinessSideId(sideBaseDto.getId());

        Set<Integer> mySourceIds = detailDtos.stream()
                .filter(dto -> dto.getAccountId().equals(queryDto.getAccountId()))
                .map(BookingItemDto::getSourceId)
                .collect(Collectors.toSet());
        mySourceIds.addAll(sourceIds);
        if (mySourceIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询限制为有权限的资源位
        List<Integer> qSourceIds;
        if (CollectionUtils.isEmpty(queryDto.getSourceIds())) {
            qSourceIds = new ArrayList<>(mySourceIds);
        } else {
            qSourceIds = queryDto.getSourceIds().stream()
                    .filter(mySourceIds::contains).collect(Collectors.toList());
        }

        Map<Integer, Set<Integer>> itemAccountMap = new HashMap<>();
        Set<Integer> liveSourceIds = Sets.newHashSet(systemConfigService.getValueReturnListInt(
                SystemConfigEnum.LIVE_CPT_SOURCE_IDS.getCode()));
        Set<Integer> splitTimesSourceIds = Sets.newHashSet(systemConfigService.getValueReturnListInt(
                SystemConfigEnum.SUPPORTS_SPLIT_TIME_CPT_SOURCE_IDS.getCode()));

        if(OrderProduct.LIVE_CPT.getCode().equals(orderProduct)){
            qSourceIds = qSourceIds.stream().filter(liveSourceIds::contains)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(qSourceIds)) {
                return Collections.emptyList();
            }

            List<Integer> bookingIds = detailDtos.stream().filter(t-> IsBookingDay.NO.getCode().equals(t.getIsBookingDay()))
                    .map(BookingItemDetailDto::getId).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(bookingIds)){
                CptSourceHourBookingLivePoExample livePoExample = new CptSourceHourBookingLivePoExample();
                livePoExample.or().andDayBookingIdIn(bookingIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                List<CptSourceHourBookingLivePo> livePos = hourBookingLiveDao.selectByExample(livePoExample);
                itemAccountMap = livePos.stream().collect(Collectors.groupingBy(CptSourceHourBookingLivePo::getDayBookingId,
                        Collectors.mapping(CptSourceHourBookingLivePo::getAccountId, Collectors.toSet())));
            }
        } else {
            qSourceIds = qSourceIds.stream().filter(t -> !liveSourceIds.contains(t)).collect(Collectors.toList());
            if (Objects.equals(queryDto.getTimeType(), 2)) {
                //如果当前查询是分时，则只返回支持分时的资源位
                qSourceIds = qSourceIds.stream().filter(splitTimesSourceIds::contains).collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(qSourceIds)) {
                return Collections.emptyList();
            }

            List<Integer> bookingIds = detailDtos.stream()
                    .filter(t-> Objects.equals(IsBookingDay.NO.getCode(), t.getIsBookingDay()))
                    .map(BookingItemDetailDto::getId)
                    .distinct()
                    .collect(Collectors.toList());

            //判断是否有分时预约的
            if(!CollectionUtils.isEmpty(bookingIds)){
                List<HourBookingItemDto> hourBookingItems = this.resourceBookingService.queryHourResourceBooking(bookingIds, null);
                itemAccountMap = hourBookingItems.stream()
                        .collect(Collectors.groupingBy(HourBookingItemDto::getDayBookingId,
                                Collectors.mapping(HourBookingItemDto::getAccountId, Collectors.toSet())));
            }
        }
        queryDto.setSourceIds(qSourceIds);

        List<CptSourceAllInfoDto> cptSourceAllInfoDtos = cptSourceService.queryCptSourceList(CptSourceQueryDto.builder()
                .platformId(queryDto.getPlatformId())
                .pageIds(queryDto.getPageIds())
                .resourceIds(queryDto.getResourceIds())
                .sourceIds(queryDto.getSourceIds())
                .beginTime(queryDto.getBeginTime())
                .endTime(queryDto.getEndTime())
                .build());

        Map<Integer, List<CptSourceAllInfoDto>> sourceIdInfoMap = cptSourceAllInfoDtos.stream()
                .collect(Collectors.groupingBy(CptSourceAllInfoDto::getSourceId));
        Map<Integer, Long> cptScheduleCreativeCountMap = getBookingItemCreativeCountMap(detailDtos);

        for (Map.Entry<Integer, List<CptSourceAllInfoDto>> entry : sourceIdInfoMap.entrySet()) {
            Integer sourceId = entry.getKey();
            CptSourceAllInfoDto allInfoDto = entry.getValue().get(0);
            Map<String, List<BookingItemDetailDto>> tempResultMap = this.buildOneSourceData(entry.getValue(),
                    sourceIdResultMap.get(sourceId), queryDto);
            if (!CollectionUtils.isEmpty(tempResultMap)) {
                List<SourceItemVo> vos = this.convertOneSourceDto2Vo(SourceItemVo.builder()
                                .platform_id(allInfoDto.getPlatformId())
                                .page_id(allInfoDto.getPageId())
                                .resource_id(allInfoDto.getResourceId())
                                .source_id(allInfoDto.getSourceId())
                                .source_name(allInfoDto.getSourceName()),
                        tempResultMap, queryDto.getAccountId(), cptScheduleCreativeCountMap,
                        orderProduct, itemAccountMap);
                if (!CollectionUtils.isEmpty(vos)) {
                    result.addAll(vos);
                }
            }
        }
        return result;
    }

    private List<SourceItemVo> convertMyResourceDtos2Vos(List<BookingItemDetailDto> detailDtos,
                                                         BookingQueryDto queryDto, Integer orderProduct) {
        List<SourceItemVo> result = new ArrayList<>();
        List<BookingItemDetailDto> filteredDetailDtos = detailDtos.stream()
                .filter(dto -> dto.getAccountId().equals(queryDto.getAccountId()))
                .collect(Collectors.toList());

        Map<Integer, List<BookingItemDetailDto>> sourceIdResultMap = filteredDetailDtos.stream()
                .collect(Collectors.groupingBy(BookingItemDetailDto::getSourceId));
        Map<Integer, Long> cptScheduleCreativeCountMap = getBookingItemCreativeCountMap(detailDtos);

        for (List<BookingItemDetailDto> value : sourceIdResultMap.values()) {
            Map<String, List<BookingItemDetailDto>> tempResultMap = this.buildOneSourceData(value, queryDto);
            if (!CollectionUtils.isEmpty(tempResultMap)) {
                BookingItemDetailDto itemDetailDto = value.get(0);
                List<SourceItemVo> vos = this.convertOneSourceDto2Vo(SourceItemVo.builder()
                                .platform_id(itemDetailDto.getPlatformId())
                                .page_id(itemDetailDto.getPageId())
                                .resource_id(itemDetailDto.getResourceId())
                                .source_id(itemDetailDto.getSourceId())
                                .source_name(itemDetailDto.getSourceName()),
                        tempResultMap, queryDto.getAccountId(), cptScheduleCreativeCountMap,
                        orderProduct, new HashMap<>());
                if (!CollectionUtils.isEmpty(vos)) {
                    result.addAll(vos);
                }
            }
        }
        return result;
    }

    private Map<Integer, Long> getBookingItemCreativeCountMap(List<BookingItemDetailDto> itemDetailDtos) {
        log.info("ResourceBookingConvertHelper.getBookingItemCreativeCountMap:itemDetailDtos.size={}", CollectionHelper.getSize(itemDetailDtos));
        List<Integer> cptScheduleIds = itemDetailDtos.stream()
                .map(BookingItemDetailDto::getCptScheduleId)
                .distinct()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());

        List<GdCreativeDto> creativeDtos = CollectionUtils.isEmpty(cptScheduleIds) ? Collections.emptyList() :
                gdCreativeService.queryGdCreativeSimple(QueryGdCreativeDto.builder().isExpired(false)
                        .scheduleIds(cptScheduleIds).build());
        log.info("ResourceBookingConvertHelper.getBookingItemCreativeCountMap:creativeDtos.size={}", CollectionHelper.getSize(creativeDtos));
        Map<Integer, List<GdCreativeDto>> scheduleCreativeMap = creativeDtos.stream()
                .collect(Collectors.groupingBy(GdCreativeDto::getScheduleId));

        Map<Integer, Long> itemCreativeCounts = itemDetailDtos.stream().collect(
                Collectors.toMap(BookingItemDetailDto::getId,
                        itemDetailDto -> {
                    Timestamp groupDateStart = Utils.getBeginOfDay(itemDetailDto.getGroupDate());
                    Timestamp groupDateEnd = Utils.getEndMillisecondOfDay(itemDetailDto.getGroupDate());
                    return scheduleCreativeMap.getOrDefault(itemDetailDto.getCptScheduleId(), Collections.emptyList())
                            .stream()
                            // 排期ID相等并且时间有重叠（所有时间不相交的情况取反）
                            .filter(creativeDto -> creativeDto.getEndTime().compareTo(groupDateStart) >= 0
                                    && creativeDto.getBeginTime().compareTo(groupDateEnd) <= 0)
                            .count();
                        },
                        Math::addExact));

        log.info("ResourceBookingConvertHelper.getBookingItemCreativeCountMap:itemCreativeCounts={}", itemCreativeCounts);
        return itemCreativeCounts;
    }

    /**
     * 对于一个位次轮， 转化为一个vo
     *
     * @param tempResultMap
     * @param itemCreativeCountMap
     * @return
     */
    private List<SourceItemVo> convertOneSourceDto2Vo(SourceItemVo.SourceItemVoBuilder itemVoBuilder,
                                                      Map<String, List<BookingItemDetailDto>> tempResultMap,
                                                      Integer accountId,
                                                      Map<Integer, Long> itemCreativeCountMap,
                                                      Integer orderProduct,
                                                      Map<Integer, Set<Integer>> itemAccountMap) {
        List<SourceItemVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(tempResultMap)) {
            return result;
        }
        int maxCount = tempResultMap.values().stream()
                .map(List::size)
                .max(Comparator.naturalOrder())
                .get();

        Set<Integer> splitTimesSourceIds = Sets.newHashSet(systemConfigService.getValueReturnListInt(
                SystemConfigEnum.SUPPORTS_SPLIT_TIME_CPT_SOURCE_IDS.getCode()));

        for (int i = 0; i < maxCount; i++) {
            List<BookingItemVo> items = new ArrayList<>();
            SourceItemVo vo = itemVoBuilder.items(items)
                    .build();
            result.add(vo);
            tempResultMap.keySet().stream()
                    .sorted(Comparator.naturalOrder()).forEach(key -> {
                //日期按从小到大排序
                List<BookingItemDetailDto> list = tempResultMap.get(key);
                if (list.size() > 0) {
                    BookingItemDetailDto one = list.remove(0);
                    Long creativeCount = itemCreativeCountMap.getOrDefault(one.getId(), 0L);
                    if (Utils.isPositive(one.getTopViewSourceId())
                            && creativeCount > 0) {
                        creativeCount = 1L;
                    }

                    Integer bookStatus = one.getStatus();
                    boolean releaseStatus = false;
                    if(OrderProduct.LIVE_CPT.getCode().equals(orderProduct)
                            || splitTimesSourceIds.contains(vo.getSource_id())){
                        if(one.getId() != null && one.getId() != 0){
                            if(IsBookingDay.NO.getCode().equals(one.getIsBookingDay())) {
                                Set<Integer> accounts = itemAccountMap.getOrDefault(one.getId(), new HashSet<>());
                                if (accounts.contains(accountId)) {
                                    bookStatus = CptBookingStatus.PART_BOOKED.getCode();
                                } else {
                                    if (one.isFullBooking()) {
                                        bookStatus = CptBookingStatus.BOOKED.getCode();
                                    } else {
                                        bookStatus = CptBookingStatus.PART_BOOKABLE.getCode();
                                    }
                                }
                            }

                            //todo:针对splitTimesSourceIds的资源位，待确认释放时间是否走新的配置项
                            releaseStatus = one.getCtime().before(TimeUtils.addDays(Utils.getBeginOfDay(
                                    new Timestamp(System.currentTimeMillis())), -1 * (liveReleaseDay)));
                        }

                    }
                    items.add(BookingItemVo.builder()
                            .booking_id(one.getId())
                            .date(one.getGroupDate().getTime())
                            .booking_status(bookStatus)
                            .booking_status_desc(CptBookingStatus.getByCode(bookStatus).getDesc())
                            .dependency_order_id(one.getCptOrderId())
                            .dependency_order_name(one.getCptOrderName())
                            .dependency_schedule_id(one.getCptScheduleId())
                            .business_side_name(one.getBusinessSideName())
                            .is_my_booking(accountId.equals(one.getAccountId()) ? 1 : 0)
                            .creative_count(creativeCount)
                            .releasing(releaseStatus)
                            .operator(accountId.equals(one.getAccountId()) ? one.getOperator() : null)
                            .top_view_source_Id(one.getTopViewSourceId())
                            .product(orderProduct != null ? orderProduct : (Utils.isPositive(one.getTopViewSourceId()) ?
                                    OrderProduct.TOP_VIEW_CPT.getCode() : OrderProduct.CPT.getCode()))
                            .build());
                } else {
                    items.add(BookingItemVo.builder()
                            .date(getTimeFromSourceKey(key).getTime())
                            .booking_status(CptBookingStatus.INVALID.getCode())
                            .build());
                }
            });
        }
        return result;
    }

    private String generateSourceKey(Integer sourceId, Timestamp day) {
        return sourceId + "_" + Utils.getTimestamp2String(day, "yyyy-MM-dd");
    }

    private Timestamp getTimeFromSourceKey(String key) {
        return Utils.getTimestamp(key.split("_")[1], "yyyy-MM-dd");
    }

    /**
     * 对于一个位次（含多轮/含多个刊例）做处理
     *
     * @param sourceAllInfoDtoList 位次的信息集合
     * @param itemVoList           查询到的该位次的预约信息集合
     * @param queryDto
     * @return
     */
    private Map<String, List<BookingItemDetailDto>> buildOneSourceData(List<CptSourceAllInfoDto> sourceAllInfoDtoList,
                                                                       List<BookingItemDetailDto> itemVoList, BookingQueryDto queryDto) {
        if (CollectionUtils.isEmpty(sourceAllInfoDtoList)) {
            return Collections.emptyMap();
        }
        Map<String, List<BookingItemDetailDto>> bookedItemMap = Collections.EMPTY_MAP;
        if (!CollectionUtils.isEmpty(itemVoList)) {
            bookedItemMap = itemVoList.stream()
                    .collect(Collectors.groupingBy(dto -> this.generateSourceKey(dto.getSourceId(), dto.getGroupDate())));
        }
        Map<String, List<BookingItemDetailDto>> sourceItemsMap = new HashMap<>();//key: sourceId+date
        int maxRotationNum = sourceAllInfoDtoList.stream()
                .mapToInt(CptSourceAllInfoDto::getRotationNum)
                .max().getAsInt();
        for (Timestamp temp = queryDto.getBeginTime(); !temp.after(queryDto.getEndTime()); temp = Utils.getSomeDayAfter(temp, 1)) {
            for (CptSourceAllInfoDto infoDto : sourceAllInfoDtoList) {
                String key = this.generateSourceKey(infoDto.getSourceId(), temp);
                if (temp.compareTo(infoDto.getCycleBeginTime()) >= 0
                        && temp.compareTo(infoDto.getCycleEndTime()) <= 0) {
                    //在刊例时间范围内
                    List<BookingItemDetailDto> bookedList = bookedItemMap.getOrDefault(key, new ArrayList<>());
                    int bookedCount = bookedList.size();
                    Integer rotationNum = infoDto.getRotationNum();
                    if (bookedCount < rotationNum) {
                        //该位次有轮数可预约
                        bookedList.addAll(this.buildBookingItemDtos(infoDto.getSourceId(), infoDto.getSourceName(),
                                temp, CptBookingStatus.BOOKABLE.getCode(), rotationNum - bookedCount));
                        bookedCount = rotationNum;
                    }
                    if (bookedCount < maxRotationNum) {
                        //该位次有无效轮数
                        bookedList.addAll(this.buildBookingItemDtos(infoDto.getSourceId(), infoDto.getSourceName(),
                                temp, CptBookingStatus.INVALID.getCode(), infoDto.getRotationNum()));
                    }
                    sourceItemsMap.put(key, bookedList);
                }
            }
        }
        return sourceItemsMap;
    }

    /**
     * 对于一个位次（含多轮/含多个刊例）做处理
     *
     * @param itemVoList 查询到的该位次的预约信息集合
     * @param queryDto
     * @return
     */
    private Map<String, List<BookingItemDetailDto>> buildOneSourceData(List<BookingItemDetailDto> itemVoList, BookingQueryDto queryDto) {
        if (CollectionUtils.isEmpty(itemVoList)) {
            return Collections.emptyMap();
        }
        Map<String, List<BookingItemDetailDto>> bookedItemMap = itemVoList.stream()
                .collect(Collectors.groupingBy(dto -> this.generateSourceKey(dto.getSourceId(), dto.getGroupDate())));
        Integer maxCount = bookedItemMap.values().stream()
                .map(list -> list.size())
                .max(Comparator.naturalOrder()).get();
        for (Timestamp temp = queryDto.getBeginTime(); !temp.after(queryDto.getEndTime()); temp = Utils.getSomeDayAfter(temp, 1)) {
            Integer sourceId = itemVoList.get(0).getSourceId();
            String sourceName = itemVoList.get(0).getSourceName();
            String key = this.generateSourceKey(sourceId, temp);
            List<BookingItemDetailDto> list = bookedItemMap.getOrDefault(key, new ArrayList<>(maxCount));
            if (list.size() < maxCount) {
                list.addAll(this.buildBookingItemDtos(sourceId, sourceName, temp, CptBookingStatus.BOOKABLE.getCode(), maxCount - list.size()));
                bookedItemMap.put(key, list);
            }
        }
        return bookedItemMap;
    }

    private List<BookingItemDetailDto> buildBookingItemDtos(Integer sourceId, String sourceName, Timestamp day, Integer status, Integer count) {
        Assert.isTrue(count >= 0, sourceId + " 在" + day + "超过刊例轮播数");
        List<BookingItemDetailDto> result = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            BookingItemDetailDto dto = new BookingItemDetailDto();
            dto.setSourceId(sourceId);
            dto.setSourceName(sourceName);
            dto.setStatus(status);
            dto.setGroupDate(day);
            dto.setResourceType(0);
            result.add(dto);
        }
        return result;
    }

    public NewResourceBookingDto convertBookingVo2Dto(CreateBookingItemVo vo, Operator operator) {
        return NewResourceBookingDto.builder()
                .accountId(operator.getOperatorId())
                .sourceId(vo.getSource_id())
                .groupDates(vo.getGroup_dates().stream()
                        .map(Timestamp::new)
                        .collect(Collectors.toList()))
                .build();
    }

    public List<BookingItemResultVo> convertBookingResultDto2Vo(List<BookingItemResultDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        List<BookingItemResultVo> result = new ArrayList<>();
        Map<Integer, List<BookingItemResultDto>> map = dtos.stream()
                .collect(Collectors.groupingBy(BookingItemResultDto::getSourceId));
        for (Map.Entry<Integer, List<BookingItemResultDto>> entry : map.entrySet()) {
            Map<Timestamp, List<BookingItemResultDto>> groupDateMap = entry.getValue().stream()
                    .collect(Collectors.groupingBy(BookingItemResultDto::getGroupDate));
            BookingItemResultDto oneDto = entry.getValue().get(0);
            List<Timestamp> sortedKeys = groupDateMap.keySet().stream()
                    .sorted(Comparator.naturalOrder())
                    .collect(Collectors.toList());

            while (groupDateMap.values().stream()
                    .flatMap(Collection::stream)
                    .count() > 0) {

                List<Long> successDates = new ArrayList<>();
                List<Long> failedDates = new ArrayList<>();

                BookingItemResultVo vo = BookingItemResultVo.builder()
                        .platform_id(oneDto.getPlatformId())
                        .platform_name(oneDto.getPlatformName())
                        .page_id(oneDto.getPageId())
                        .page_name(oneDto.getPageName())
                        .resource_id(oneDto.getResourceId())
                        .resource_name(oneDto.getResourceName())
                        .source_id(oneDto.getSourceId())
                        .source_name(oneDto.getSourceName())
                        .success_group_date(successDates)
                        .failed_group_date(failedDates)
                        .build();

                result.add(vo);

                for (Timestamp key : sortedKeys) {
                    List<BookingItemResultDto> list = groupDateMap.get(key);
                    if (!CollectionUtils.isEmpty(list)) {
                        BookingItemResultDto dto = list.remove(0);
                        if (dto.getSuccess() == 1) {
                            successDates.add(dto.getGroupDate().getTime());
                        } else {
                            failedDates.add(dto.getGroupDate().getTime());
                        }
                    }
                }
            }
        }
        return result;
    }

    public DeleteResourceBookingDto convertDeleteVo2Dto(DeleteBookingItemVo vo, Operator operator) {
        return DeleteResourceBookingDto.builder()
                .accountId(operator.getOperatorId())
                .platformId(vo.getPlatform_id())
                .pageId(vo.getPage_id())
                .resourceId(vo.getResource_id())
                .sourceId(vo.getSource_id())
                .ids(vo.getIds())
                .build();
    }

    public void resolveReleasingState(List<SourceItemVo> records, List<ResourceBookingNotificationDto> releasingNotificationDtos) {
        if (CollectionUtils.isEmpty(records) || CollectionUtils.isEmpty(releasingNotificationDtos)) {
            return;
        }
        Map<Integer, ResourceBookingNotificationDto> releasingMap = releasingNotificationDtos.stream().collect(
                Collectors.toMap(ResourceBookingNotificationDto::getSourceDayBookId, Function.identity(), (a, b) -> a));
        for (SourceItemVo record : records) {
            List<BookingItemVo> bookingItemVos = record.getItems();
            if (CollectionUtils.isEmpty(bookingItemVos)) {
                continue;
            }
            for (BookingItemVo bookingItemVo : bookingItemVos) {
                ResourceBookingNotificationDto notificationDto = releasingMap.get(bookingItemVo.getBooking_id());
                if (notificationDto != null) {
                    bookingItemVo.setReleasing(true);
                    bookingItemVo.setReleasing_date(StringDateParser.getDateString(notificationDto.getReleaseDate()));
                }
            }
        }
    }
}
