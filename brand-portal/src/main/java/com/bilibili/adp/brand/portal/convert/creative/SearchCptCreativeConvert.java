package com.bilibili.adp.brand.portal.convert.creative;

import com.bilibili.adp.brand.portal.common.IdNameVo;
import com.bilibili.adp.brand.portal.webapi.common.convert.CreativeConverter;
import com.bilibili.adp.brand.portal.webapi.launch.vo.*;
import com.bilibili.adp.brand.portal.webapi.resource.vo.AdditionalCardTypeVo;
import com.bilibili.cpt.platform.api.creative.dto.*;
import com.bilibili.enums.AdditionalCardType;
import com.bilibili.enums.GdJumpType;
import com.bilibili.brand.api.creative.dto.GdCreativeDto;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.enums.TemplatePropertyEnum;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mgk.platform.common.video_library.SizeTypeEnum;
import com.bilibili.utils.NumberUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021.03.08 11:50
 */
@Component
public class SearchCptCreativeConvert {

    @Autowired
    private ConfigCenter configCenter;
    @Autowired
    private CreativeConverter creativeConverter;

    public static MultiCreateSearchCptCreativeDto convert2MultiCreateSearchCptCreativeDto(MultiCreateSearchCptCreativeInfoVo vo) {
        if (vo == null) {
            return null;
        }
        MultiCreateSearchCptCreativeDto multiCreateSearchCptCreativeDto = new MultiCreateSearchCptCreativeDto();
        multiCreateSearchCptCreativeDto.setScheduleIdList(vo.getScheduleIds());
        multiCreateSearchCptCreativeDto.setSearchCptCreativeDto(SearchCptCreativeConvert.convert2CreativeDto(vo));
        return multiCreateSearchCptCreativeDto;
    }

    public static SearchCptCreativeDto convert2CreativeDto(SearchCptCreativeInfoVo vo) {
        if (vo == null) {
            return null;
        }
        SearchCptCreativeDto searchCptCreativeDto = new SearchCptCreativeDto();
        searchCptCreativeDto.setCreativeName(vo.getCreative_name());
        searchCptCreativeDto.setCmMark(vo.getCm_mark());
        searchCptCreativeDto.setBusMarkId(vo.getCm_mark());//使用新版广告标
        searchCptCreativeDto.setBeginTime(new Timestamp(vo.getBegin_time()));
        searchCptCreativeDto.setEndTime(new Timestamp(vo.getEnd_time()));
        searchCptCreativeDto.setGdScheduleId(vo.getSchedule_id());
        searchCptCreativeDto.setJumpType(vo.getJump_type());
        searchCptCreativeDto.setPromotionPurposeContent(vo.getPromotion_purpose_content());
        searchCptCreativeDto.setSchemeUrl(vo.getScheme_url());
        searchCptCreativeDto.setCustomizedImpUrl(vo.getCustomized_imp_url());
        searchCptCreativeDto.setCustomizedClickUrl(vo.getCustomized_click_url());
        searchCptCreativeDto.setBrandName(vo.getBrand_name());
        searchCptCreativeDto.setFaceUrl(vo.getFace_url());
        searchCptCreativeDto.setFaceMd5(vo.getFace_md5());
        searchCptCreativeDto.setBrandDescription(vo.getBrand_description());
        searchCptCreativeDto.setButtons(CreativeConverter.convert2ButtonDto(vo.getButton_vos()));
        searchCptCreativeDto.setImages(CreativeConverter.convert2ImageDto(vo.getImages()));
        searchCptCreativeDto.setMgkVideos(CreativeConverter.convert2MgkVideoDto(vo.getMgk_videos()));
        searchCptCreativeDto.setShareState(vo.getShare_state());
        searchCptCreativeDto.setShareImageHash(vo.getShare_image_hash());
        searchCptCreativeDto.setShareImageUrl(vo.getShare_image_url());
        searchCptCreativeDto.setShareSubTitle(vo.getShare_sub_title());
        searchCptCreativeDto.setShareTitle(vo.getShare_title());
        searchCptCreativeDto.setAids(vo.getAids());
        searchCptCreativeDto.setTemplateId(vo.getTemplate_id());
        searchCptCreativeDto.setBackgroundColorCode(vo.getBackground_color_code());
        searchCptCreativeDto.setTitle(vo.getTitle());
        searchCptCreativeDto.setOpenDanmuku(vo.getOpen_danmuku());
//        searchCptCreativeDto.setMgkVideoInfoDto(convert2MgkVideoDto(vo.getMgk_video_vo()));
        searchCptCreativeDto.setManuscriptJumpType(vo.getManuscript_jump_type());
        searchCptCreativeDto.setManuscriptJumpUrl(vo.getManuscript_jump_url());
        //兼容旧数据，如果不为空则覆盖MgkVideos
        if (vo.getMgk_video_vo() != null) {
            searchCptCreativeDto.setMgkVideos(CreativeConverter.convert2MgkVideoDto(Lists.newArrayList(vo.getMgk_video_vo())));
        }
        searchCptCreativeDto.setDescription(vo.getDescription());
        searchCptCreativeDto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProduct_label()));
        searchCptCreativeDto.setAdditionalCardType(vo.getAdditional_card_type());
        searchCptCreativeDto.setLiveBookingId(vo.getLive_booking_id());
        searchCptCreativeDto.setBackGroundColor(BackgroundColorConverter.MAPPER.toBo(vo.getBackground_color()));
        return searchCptCreativeDto;
    }

    public static SearchCptCreativeDto convertUpdate2CreativeDto(SearchCptUpdateCreativeVo vo) {
        if (vo == null) {
            return null;
        }
        SearchCptCreativeDto searchCptCreativeDto = new SearchCptCreativeDto();
        searchCptCreativeDto.setGdCreativeId(vo.getCreative_id());
        searchCptCreativeDto.setCreativeName(vo.getCreative_name());
        searchCptCreativeDto.setCmMark(vo.getCm_mark());
        searchCptCreativeDto.setBusMarkId(vo.getCm_mark());
        searchCptCreativeDto.setBeginTime(new Timestamp(vo.getBegin_time()));
        searchCptCreativeDto.setEndTime(new Timestamp(vo.getEnd_time()));
        searchCptCreativeDto.setJumpType(vo.getJump_type());
        searchCptCreativeDto.setPromotionPurposeContent(vo.getPromotion_purpose_content());
        searchCptCreativeDto.setSchemeUrl(vo.getScheme_url());
        searchCptCreativeDto.setCustomizedImpUrl(vo.getCustomized_imp_url());
        searchCptCreativeDto.setCustomizedClickUrl(vo.getCustomized_click_url());
        searchCptCreativeDto.setBrandName(vo.getBrand_name());
        searchCptCreativeDto.setFaceUrl(vo.getFace_url());
        searchCptCreativeDto.setFaceMd5(vo.getFace_md5());
        searchCptCreativeDto.setBrandDescription(vo.getBrand_description());
        searchCptCreativeDto.setButtons(CreativeConverter.convert2ButtonDto(vo.getButton_vos()));
        searchCptCreativeDto.setImages(CreativeConverter.convert2ImageDto(vo.getImages()));
        searchCptCreativeDto.setMgkVideos(CreativeConverter.convert2MgkVideoDto(vo.getMgk_videos()));
        searchCptCreativeDto.setShareState(vo.getShare_state());
        searchCptCreativeDto.setShareImageHash(vo.getShare_image_hash());
        searchCptCreativeDto.setShareImageUrl(vo.getShare_image_url());
        searchCptCreativeDto.setShareSubTitle(vo.getShare_sub_title());
        searchCptCreativeDto.setShareTitle(vo.getShare_title());
        searchCptCreativeDto.setAids(vo.getAids());
        searchCptCreativeDto.setTemplateId(vo.getTemplate_id());
//        searchCptCreativeDto.setMgkVideoInfoDto(convert2MgkVideoDto(vo.getMgk_video_vo()));
        searchCptCreativeDto.setTitle(vo.getTitle());
        searchCptCreativeDto.setManuscriptJumpType(vo.getManuscript_jump_type());
        searchCptCreativeDto.setManuscriptJumpUrl(vo.getManuscript_jump_url());
        searchCptCreativeDto.setBackgroundColorCode(vo.getBackground_color_code());
        searchCptCreativeDto.setOpenDanmuku(vo.getOpen_danmuku());
        searchCptCreativeDto.setDescription(vo.getDescription());
        //兼容旧数据，如果不为空则覆盖MgkVideos
        if (vo.getMgk_video_vo() != null) {
            searchCptCreativeDto.setMgkVideos(CreativeConverter.convert2MgkVideoDto(Lists.newArrayList(vo.getMgk_video_vo())));
        }
        searchCptCreativeDto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProduct_label()));
        searchCptCreativeDto.setAdditionalCardType(vo.getAdditional_card_type());
        searchCptCreativeDto.setLiveBookingId(vo.getLive_booking_id());
        searchCptCreativeDto.setBackGroundColor(BackgroundColorConverter.MAPPER.toBo(vo.getBackground_color()));
        return searchCptCreativeDto;
    }

    public SearchCptCreativeInfoVo convert2CreativeVo(SearchCptCreativeDto dto,
                                                             List<CreativeStyle> supportStyles) {
        if (dto == null) {
            return null;
        }
        GdJumpType jumpType = GdJumpType.getByCodeWithoutEx(dto.getJumpType());
        SearchCptCreativeInfoVo searchCptCreativeInfoVo = new SearchCptCreativeInfoVo();
        searchCptCreativeInfoVo.setCreative_id(dto.getGdCreativeId());
        searchCptCreativeInfoVo.setSchedule_id(dto.getGdScheduleId());
        searchCptCreativeInfoVo.setSchedule_name(dto.getScheduleName());
        searchCptCreativeInfoVo.setBegin_time(dto.getBeginTime().getTime());
        searchCptCreativeInfoVo.setEnd_time(dto.getEndTime().getTime());
        searchCptCreativeInfoVo.setCreative_name(dto.getCreativeName());
        searchCptCreativeInfoVo.setJump_type(dto.getJumpType());
        searchCptCreativeInfoVo.setPromotion_purpose_content(Objects.nonNull(jumpType) ?
                jumpType.parseLaunchUrl(dto.getPromotionPurposeContent()) : dto.getPromotionPurposeContent());
        searchCptCreativeInfoVo.setCustomized_imp_url(dto.getCustomizedImpUrl());
        searchCptCreativeInfoVo.setCustomized_click_url(dto.getCustomizedClickUrl());
        searchCptCreativeInfoVo.setButton_vos(CreativeConverter.convert2ButtonVo(dto.getButtons()));
        searchCptCreativeInfoVo.setImages(CreativeConverter.convert2ImageVo(dto.getImages()));
        searchCptCreativeInfoVo.setMgk_videos(CreativeConverter.convert2MgkVideoVo(dto.getMgkVideos()));
        searchCptCreativeInfoVo.setAids(dto.getAids());
        searchCptCreativeInfoVo.setCm_mark(dto.getCmMark());
        searchCptCreativeInfoVo.setTemplate_id(dto.getTemplateId());
        searchCptCreativeInfoVo.setOrder_id(dto.getGdOrderId());
        searchCptCreativeInfoVo.setScheme_url(dto.getSchemeUrl());
        searchCptCreativeInfoVo.setShare_state(NumberUtil.toValidInt(dto.getShareState()));
        searchCptCreativeInfoVo.setShare_title(dto.getShareTitle());
        searchCptCreativeInfoVo.setShare_sub_title(dto.getShareSubTitle());
        searchCptCreativeInfoVo.setShare_image_url(dto.getShareImageUrl());
        searchCptCreativeInfoVo.setShare_image_hash(dto.getShareImageHash());
        searchCptCreativeInfoVo.setBrand_name(dto.getBrandName());
        searchCptCreativeInfoVo.setFace_url(dto.getFaceUrl());
        searchCptCreativeInfoVo.setFace_md5(dto.getFaceMd5());
        searchCptCreativeInfoVo.setBrand_description(dto.getBrandDescription());
        searchCptCreativeInfoVo.setOrder_id(dto.getGdOrderId());
        searchCptCreativeInfoVo.setOrder_name(dto.getOrderName());
        searchCptCreativeInfoVo.setSource_id(dto.getSourceId());
        searchCptCreativeInfoVo.setSource_name(dto.getSourceName());
        searchCptCreativeInfoVo.setSales_type(dto.getSalesType());
        searchCptCreativeInfoVo.setSales_type_desc(dto.getSalesTypeDesc());
        searchCptCreativeInfoVo.setTemplate(templateDto2Vo(dto, supportStyles));
        searchCptCreativeInfoVo.setBackground_color_code(dto.getBackgroundColorCode());
        searchCptCreativeInfoVo.setTitle(dto.getTitle());
        searchCptCreativeInfoVo.setOpen_danmuku(dto.getOpenDanmuku());
        searchCptCreativeInfoVo.setMgk_video_vo(CreativeConverter.convert2MgkVideoVo(dto.getMgkVideoInfoDto()));
        searchCptCreativeInfoVo.setManuscript_jump_type(dto.getManuscriptJumpType());
        searchCptCreativeInfoVo.setManuscript_actual_jump_url(dto.getManuscriptJumpUrl() == null ? "" : dto.getManuscriptJumpUrl());
        searchCptCreativeInfoVo.setManuscript_jump_url(dto.getManuscriptJumpType() == null ? "" :
                GdJumpType.getByCode(dto.getManuscriptJumpType()).parseLaunchUrl(dto.getManuscriptJumpUrl()));
        searchCptCreativeInfoVo.setDescription(dto.getDescription());
        searchCptCreativeInfoVo.setProduct_label(GdCreativeConverter.MAPPER.toProductLabelVo(dto.getProductLabel()));
        searchCptCreativeInfoVo.setAdditional_card_type(dto.getAdditionalCardType());
        searchCptCreativeInfoVo.setLive_booking_id(dto.getLiveBookingId());
        searchCptCreativeInfoVo.setBackground_color(BackgroundColorConverter.MAPPER.toVo(dto.getBackGroundColor()));
        return searchCptCreativeInfoVo;
    }

    private TemplateVo templateDto2Vo(SearchCptCreativeDto searchCptCreativeDto, List<CreativeStyle> supportStyles) {
        if (null == searchCptCreativeDto.getTemplate()) {
            return null;
        }
        TemplateDto template = searchCptCreativeDto.getTemplate();
        Boolean isSupportSchema = searchCptCreativeDto.getIsSupportSchema();
        List<com.bilibili.adp.brand.portal.webapi.resource.vo.ButtonVo> buttonVos = this.creativeConverter.getButtonInfo(
                CptTemplateDto.builder()
                        .templateId(template.getTemplateId())
                        .templateButtons(template.getTemplateButtons())
                        .cardType(template.getCardType())
                        .launchObj(searchCptCreativeDto.getLauncher())
                        .build(), searchCptCreativeDto.getPromotionPurposeType());


        TemplateVo templateVo = TemplateVo.builder()
                .id(template.getTemplateId())
                .name(template.getTemplateName())
                ._fill_title(template.getIsFillTitle())
                .title_max_length(template.getTitleMaxLength())
                .title_min_length(template.getTitleMinLength())
                ._fill_desc(template.getIsFillDesc())
                .desc_max_length(template.getDescMaxLength())
                .desc_min_length(template.getDescMinLength())
                ._support_image(template.getIsSupportImage())
                .image_width(template.getImageWidth())
                .image_height(template.getImageHeight())
                .image_kb_limit(template.getImageKbLimit())
                ._support_ext_image(template.getIsSupportExtImage())
                .ext_image_width(template.getExtImageWidth())
                .ext_image_height(template.getExtImageHeight())
                .ext_image_kb_limit(template.getExtImageKbLimit())
                ._support_video(template.getIsSupportVideo())
                .video_width(template.getVideoWidth())
                .video_height(template.getVideoHeight())
                .video_kb_limit(template.getVideoKbLimit())
                .video_duration_max(template.getVideoDurationMax())
                .video_duration_min(template.getVideoDurationMin())
                ._fill_ext_desc(template.getIsFillExtDesc())
                .ext_desc_min_length(template.getExtDescMinLength())
                .ext_desc_max_length(template.getExtDescMaxLength())
                ._support_video_id(template.getIsSupportVideoId())
                .html(template.getHtml())
                .video_aspect_ratio(SizeTypeEnum.getSizeTypeEnum(template.getVideoWidth(), template.getVideoHeight()).getDesc())
                ._support_button(template.getIsSupportButton())
                .button_copy_min_length(template.getButtonCopyMinLength())
                .button_copy_max_length(template.getButtonCopyMaxLength())
                .support_image_num(template.getImageNum())
                .card_type(template.getCardType())
                .support_styles(CollectionUtils.isEmpty(supportStyles) ?
                        Collections.emptyList() : supportStyles.stream()
                        .filter(cs -> cs.matchTemplate(template))
                        .map(cs -> IdNameVo.builder()
                                .id(cs.getCode().longValue())
                                .name(cs.getName())
                                .build())
                        .collect(Collectors.toList()))
                ._support_animation(template.getIsSupportAnimation())
                .animation_kb_Limit(template.getAnimationKbLimit())
                .animation_loop(template.getAnimationLoop())
                .animation_max_duration(template.getAnimationMaxDuration())
                .image_vos(CreativeConverter.convert2TemplateImageVo(template.getTemplateImages()))
                .video_vos(CreativeConverter.convert2TemplateVideoVo(template.getTemplateVideos()))
                .button_vos(buttonVos)
                .support_video_id_nums(template.getSupportVideoIdNums())
                ._support_scheme_url(isSupportSchema)
                .is_support_ssa_search_keyword(configCenter.getSearchCptConfig().isSearchCptLinkedSsaTemplate(template.getTemplateId()))
                .build();
        TemplatePropertyEnum.TemplateOptions templateOptions = TemplatePropertyEnum.getByTemplateIdWithoutEx(templateVo.getId()).getOptions();
        List<AdditionalCardType> additionalCards = templateOptions.getSupportAdditionalCards();
        if (!CollectionUtils.isEmpty(additionalCards)) {
            templateVo.setAdditional_card_types(additionalCards.stream().map(SearchCptCreativeConvert::additionalCardType2Vo).collect(Collectors.toList()));
        }
        return templateVo;
    }

    public static AdditionalCardTypeVo additionalCardType2Vo(AdditionalCardType type) {
        return AdditionalCardTypeVo.builder()
                .code(type.getCode())
                .desc(type.getDesc())
                .build();
    }


    public static CreativeBaseVo creativeDto2BaseVo(GdCreativeDto creative) {
        return CreativeBaseVo.builder()
                .id(creative.getCreativeId().toString())
                .title(creative.getCreativeName())
                .build();
    }

}
