package com.bilibili.adp.brand.portal.webapi.launch.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("图片模型")
public class ImageVo {

    private Integer id;

    private String image_url;

    private String image_hash;

    @ApiModelProperty(notes = "图片类型 0-素材主图 1-背景图 2-头像 3-极致战队icon")
    private Integer image_style;

    //以下两个字段用于主素材为gif图时,因为引擎不兼容才另外建了两个字段,把封面图和gif存在一条数据里
    @ApiModelProperty(notes = "封面url")
    private String cover_url;

    @ApiModelProperty(notes = "封面url")
    private String cover_hash;

    @ApiModelProperty(notes = "跳转类型")
    private Integer jump_type;

    @ApiModelProperty(notes = "跳转url")
    private String jump_url;

    @ApiModelProperty(notes = "唤起url")
    private String scheme_url;

    @ApiModelProperty(notes = "按钮信息")
    private List<ButtonVo> button_vos;

    @ApiModelProperty(notes = "是否是gif")
    private Boolean is_gif;

    @ApiModelProperty(notes = "标题")
    private String title;

    @ApiModelProperty(notes = "描述")
    private String description;

    @ApiModelProperty(notes = "顺序")
    private Integer seq;

}
