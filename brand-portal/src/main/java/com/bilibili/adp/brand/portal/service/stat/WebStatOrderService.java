package com.bilibili.adp.brand.portal.service.stat;

import com.bilibili.adp.brand.portal.common.GroupType;
import com.bilibili.adp.brand.portal.webapi.statistic.vo.OrderDataVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.service.IGdOrderExtService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.platform.report.api.dto.StatOrderDto;
import com.bilibili.cpt.report.platform.api.creative.dto.VideoPlayStatCreativeDto;
import com.bilibili.brand.platform.report.api.service.IStatOrderService;
import com.bilibili.cpt.report.platform.api.creative.service.IVideoPlayStatCreativeService;
import com.bilibili.utils.NumberUtil;
import com.bilibili.utils.OptionalUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 16/9/29.
 */
@Service
public class WebStatOrderService extends WebStatBaseService<OrderDataVo> {

    @Autowired
    private IStatOrderService statOrderService;

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private IGdCreativeService creativeService;

    @Autowired
    private IVideoPlayStatCreativeService videoPlayStatCreativeService;

    @Autowired
    private IQueryScheduleService querySheduleService;

    @Autowired
    private IGdOrderExtService gdOrderExtService;


    @Override
    protected List<OrderDataVo> groupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return convertVosToDtos(statOrderService.getByAccountIdGroupByTime(accountId, fromTime, toTime, salesType), product, fromTime, toTime);
    }

    @Override
    protected List<OrderDataVo> groupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return convertVosToDtos(statOrderService.getByAccountIdGroupByDay(accountId, fromTime, toTime, salesType), product, fromTime, toTime);
    }

    @Override
    protected List<OrderDataVo> groupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return convertVosToDtos(statOrderService.getByAccountIdGroupByWeek(accountId, fromTime, toTime, salesType), product, fromTime, toTime);
    }

    @Override
    protected List<OrderDataVo> groupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return convertVosToDtos(statOrderService.getByAccountIdGroupByMonth(accountId, fromTime, toTime, salesType), product, fromTime, toTime);
    }

    @Override
    protected List<OrderDataVo> groupByGroupTypeAndOrderIdList(Integer accountId, Timestamp fromTime, Timestamp toTime,
                                                  Integer salesType, GroupType groupType, List<Integer> orderIdList, Integer product) throws ServiceException {
        return convertVosToDtos(statOrderService.getByAccountIdGroupByTimeAndGroupType(accountId, fromTime,
                toTime, salesType, getByGroupType(groupType), orderIdList), product, fromTime, toTime);
    }

    private List<OrderDataVo> convertVosToDtos(List<StatOrderDto> dtos, Integer product, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        Set<Integer> orderIdSet = dtos.stream().map(StatOrderDto::getOrderId).collect(Collectors.toSet());
        Map<Integer, GdOrderDto> orderMap =
                gdOrderService.getOrderMapInOrderIds(new ArrayList<>(orderIdSet));
        List<Integer> orderIds = orderMap.values().stream().filter(t-> product.equals(t.getProduct()))
                .map(GdOrderDto::getOrderId)
                .collect(Collectors.toList());

        //一定要过滤下orderIds是否为空，防止计费表bug而导致查询全量的问题
        List<ScheduleDto> schedules = CollectionUtils.isEmpty(orderIds) ? Collections.emptyList():
                this.querySheduleService.queryBaseSchedule(QueryScheduleDto.builder()
                        .orderIds(orderIds)
                        .statusList(Lists.newArrayList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                        .beginDate(fromTime)
                        .endDate(toTime)
                        .timeContains(false)//交集
                        .build());
        Map<Integer, Long> orderImpressionMap = schedules.stream()
                .collect(Collectors.groupingBy(ScheduleDto::getOrderId,
                        Collectors.reducing(0L, s -> NumberUtil.toValidLong(s.getTotalImpression()), Long::sum)));

        //过滤是否为空，因为上面的filter逻辑有可能导致orderIds为空，进而导致andOrderIdIn 的SQL异常
        List<GdOrderExtDto> orderExtInfoList = CollectionUtils.isEmpty(orderIds) ? Collections.emptyList() :
                gdOrderExtService.getGdOrderExtInfoList(orderIds);
        Map<Integer, GdOrderExtDto> orderExtInfoMap = orderExtInfoList.stream()
                .collect(Collectors.toMap(GdOrderExtDto::getOrderId, Function.identity(), OptionalUtil.override()));
        //过滤出起飞订单
        List<Integer> flyOrderIdList = orderIds.stream()
                .filter(orderId -> gdOrderExtService.isFlyGdOrder(orderExtInfoMap.get(orderId))) //如果orderExtInfo为null，则非起飞
                .collect(Collectors.toList());

        Map<Integer, Long> order2PlayCountMap = new HashMap<>();
        Map<Integer, Long> order2PlayTimeMap = new HashMap<>();

        if(!CollectionUtils.isEmpty(flyOrderIdList)){
            Map<Integer, List<Long>> orders2CreativeIds = creativeService
                    .getGdCreativeIdsMapInOrderIds(new ArrayList<>(flyOrderIdList));
            if(!CollectionUtils.isEmpty(orders2CreativeIds)){
                Map<Long, VideoPlayStatCreativeDto> videoPlayStatMap = videoPlayStatCreativeService
                        .getCreativeStat(orders2CreativeIds.values().stream()
                                .flatMap(Collection::stream).collect(Collectors.toList()));
                orders2CreativeIds.forEach((k,v)->{
                    long playCount = 0L, playTime = 0L;
                    for (Long creativeId : v) {
                        VideoPlayStatCreativeDto videoPlayStat = videoPlayStatMap.get(creativeId);
                        playCount += Objects.isNull(videoPlayStat) ? 0L : videoPlayStat.getPlayCount();
                        playTime += Objects.isNull(videoPlayStat) ? 0L : videoPlayStat.getPlayTime();
                    }
                    order2PlayTimeMap.put(k, playTime);
                    order2PlayCountMap.put(k, playCount);
                });
            }
        }

        List<OrderDataVo> result = new ArrayList<>(dtos.size());
        result.addAll(dtos.stream().filter(t->orderIds.contains(t.getOrderId())).map(dto -> {
            Long playNums = order2PlayCountMap.getOrDefault(dto.getOrderId(), 0L);
            Long playTime = order2PlayTimeMap.getOrDefault(dto.getOrderId(), 0L);
            return OrderDataVo.builder()
                    .target_show_count(orderImpressionMap.getOrDefault(dto.getOrderId(), 0L) * 1000)
                    .show_count(dto.getShowAccount())
                    .cost_per_click(dto.getCostPerClick())
                    .cost(dto.getCost())
                    .average_cost_per_thousand(dto.getAverageCostPerThousand())
                    .click_rate(dto.getClickRate())
                    .click_count(dto.getClickCount())
                    .date(dto.getDate())
                    .order_id(dto.getOrderId())
                    .order_name(orderMap.get(dto.getOrderId()).getOrderName())
                    .play_nums(playNums)
                    .play_time(playTime)
                    .play_cost(Utils.isPositive(playNums) ? dto.getCost().divide(new BigDecimal(playNums),
                            2, RoundingMode.HALF_UP) : BigDecimal.ZERO)
                    .build();
        }).collect(Collectors.toList()));
        return result;
    }
}
