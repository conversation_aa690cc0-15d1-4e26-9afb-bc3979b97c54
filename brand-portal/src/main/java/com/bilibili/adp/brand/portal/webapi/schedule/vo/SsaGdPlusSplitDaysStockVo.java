package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by xiongyan on 2020/8/05.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("分日库存信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaGdPlusSplitDaysStockVo {

    @ApiModelProperty("库存")
    private Long stockCpm;

    @ApiModelProperty("开始时间")
    private String beginTime;

    @ApiModelProperty("结束时间")
    private String endTime;
}
