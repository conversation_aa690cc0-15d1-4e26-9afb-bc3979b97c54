package com.bilibili.adp.brand.portal.convert.creative;

import cn.hutool.core.bean.BeanUtil;
import com.bilibili.adp.brand.portal.common.PlatformSwitchEnum;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ManuscriptInfoVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.MiniProgramVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.MgkVideoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenCustomizedVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SsaScheduleSelectVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view.NewExternalTopViewVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view.TopViewHfJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view.TransitionVideoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view.UpdateExternalTopViewVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaNewSplashScreenVideoVo;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.SsaLinkageType;
import com.bilibili.brand.api.creative.dto.GdTopViewDto;
import com.bilibili.brand.api.creative.dto.NewExternalTopViewDto;
import com.bilibili.brand.api.creative.dto.TopViewHfJumpDto;
import com.bilibili.brand.api.creative.dto.TransitionVideoDto;
import com.bilibili.brand.api.creative.dto.UpdateExternalTopViewDto;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.creative.convert.TopViewConvert;
import com.bilibili.brand.biz.creative.service.TopViewCreativeService;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.cpt.platform.api.creative.dto.CptJumpDTO;
import com.bilibili.cpt.platform.api.schedule.dto.ScheduleDateDto;
import com.bilibili.enums.GdJumpType;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.common.enums.BaseImageTypeEnum;
import com.bilibili.ssa.platform.common.enums.SsaJumpType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * @Description 闪屏模型转换工具类
 * <AUTHOR>
 * @Date 2020.05.26 18:17
 */
@Component
public class TopViewCreativeConvert {

    @Autowired
    private SsaSplashScreenConvert ssaSplashScreenConvert;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private TopViewCreativeService topViewCreativeService;

    @Autowired
    private TopViewConvert topViewConvert;

    public NewExternalTopViewDto convertToNewExternalTopViewDto(NewExternalTopViewVo vo) {
        if (vo == null) {
            return null;
        }
        SsaJumpType ssaJumpType = SsaJumpType.getByCode(vo.getJumpType());
//        List<SplashScreenJumpDTO> splashScreenJumpDTOS = new ArrayList<>();
        List<SplashScreenCustomizedDTO> customizedDTOS;
        List<SplashScreenDynamicButtonBO> buttonBOS;
        ScheduleDto schedule = queryScheduleService.getScheduleBaseInfoById(vo.getScheduleId());
        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(vo.getScheduleId());
        SsaSplashScreenConvert.SsaConvertContext convertContext = SsaSplashScreenConvert.SsaConvertContext.builder()
                .ssaArchive(false)
                .linkageType(SsaLinkageType.NONE)
                .orderProduct(OrderProduct.getByCode(schedule.getOrderProduct()))
                .platformTypes(queryScheduleService.getScheduleOsTarget(schedule.getScheduleId(), true))
                .guideMaterialType(vo.getDynamicButtonVos().get(0).getGuideMaterialType())
                .isCustomizedNewHfBrandInfo(vo.getIsCustomizedNewHfBrandInfo())
                .ssaAdType(topViewScheduleInfo.getSsaAdType())
                .isEnableLiveBooking(vo.getIsEnableLiveBooking())
                .build();
        if (PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            //模块跳转的闪屏跳转信息存放在按钮里面
            buttonBOS = ssaSplashScreenConvert.convert2ButtonBOs(ssaJumpType.getCode(), vo.getTextColorStyle(), vo.getDynamicButtonVos());
            customizedDTOS = ssaSplashScreenConvert.convert2CustomizedDTO(vo.getSplashScreenCustomizedVos());
        } else {
            //不分端模块跳转的闪屏跳转信息存放在按钮里面
            buttonBOS = ssaSplashScreenConvert.convert2ButtonBOsWithoutPlatform(ssaJumpType.getCode(), vo.getTextColorStyle(),
                    vo.getDynamicButtonVos(), convertContext);
            customizedDTOS = ssaSplashScreenConvert.convert2CustomizedDTOWithoutPlatform(SplashScreenCustomizedVo.builder()
                    .customized_imp_url_list(vo.getCustomizedImpUrlList())
                    .customized_click_url_list(vo.getCustomizedClickUrlList())
                    .customized_middle_page_click_url_list(vo.getCustomizedMiddlePageClickUrlList())
                    .customized_open_middle_page_click_url_list(vo.getCustomizedOpenMiddlePageClickUrlList())
                    .build(), convertContext);
        }
        List<SsaNewSplashScreenVersionControlDto> versionControlDtos = mockTopViewVersionControl(schedule, topViewScheduleInfo, convertContext);
        //topview不支持选择式闪屏
        Assert.isTrue(!CollectionUtils.isEmpty(buttonBOS), "按钮跳转信息不能为空");

        HfConvertContext hfConvertContext = HfConvertContext.builder()
                .platformSwitch(vo.getPlatformSwitch())
                .isCustomizedHfJump(vo.getIsCustomizedHfJump())
                .hfJump(vo.getHfJump())
                .hfManuscriptInfo(vo.getManuscriptInfo())
                .newHfJumpType(vo.getNewHfJumpType())
                .newHfPromotionPurposeContent(vo.getNewHfPromotionPurposeContent())
                .ssaJumpType(SsaJumpType.getByCode(vo.getJumpType()))
                .ssaAppPackageIds(vo.getAppPackageIds())
                .ssaButtons(buttonBOS)
                .ssaMiniProgram(vo.getMiniProgram())
                .ssaWakeAppType(vo.getWakeAppType())
                .ssaConvertContext(convertContext)
                .build();

        TopViewHfJumpDto topViewHfJumpDto = convertHfContext(hfConvertContext);

        NewExternalTopViewDto dto = BeanUtil.copyProperties(vo, NewExternalTopViewDto.class);
        dto.setSsaBaseImageList(newSplashScreenBaseImageVos2NewDtos(vo.getSsaBaseImageList()));
        dto.setSsaVideo(newSplashScreenVideoVo2Dto(vo.getSsaVideo()));
        dto.setEggVideo(mgkVideoVoToDto(vo.getEggVideo()));
        dto.setTransitionVideo(transitionVideoVoToDto(vo.getTransitionVideo()));
        dto.setNewHfVideo(newSplashScreenVideoVo2Dto(vo.getNewHfVideo()));
        dto.setSsaCustomizedDTOS(customizedDTOS);
        dto.setSplashScreenJumpDTOS(new ArrayList<>());//没用到，闪屏的跳转信息都在buttonBOS里
        dto.setButtonBOS(buttonBOS);
        //可废弃
        dto.setCptJumpDTOS(topViewHfJumpDto.getJumps());
        dto.setInteractInstructions(vo.getInteractInstructions());

        if (PlatformSwitchEnum.CLOSE.getCode().equals(vo.getPlatformSwitch())) {
            dto.setHfAndroidCustomizedClickUrl(vo.getHfCustomizedClickUrl());
            dto.setHfAndroidCustomizedImpUrl(vo.getHfCustomizedImpUrl());
            dto.setHfIosCustomizedClickUrl(vo.getHfCustomizedClickUrl());
            dto.setHfIosCustomizedImpUrl(vo.getHfCustomizedImpUrl());
            dto.setHfAndroidCustomizedClickUrlList(vo.getHfCustomizedClickUrlList());
            dto.setHfIosCustomizedClickUrlList(vo.getHfCustomizedClickUrlList());
        }

        List<ScheduleDateDto> dateDtos = new ArrayList<>();
        List<SsaNewScheduleSplashScreenMappingDto> mappingDtos = new ArrayList<>();
        for (SsaScheduleSelectVo selectVo : vo.getSchedules()) {
            dateDtos.add(ScheduleDateDto.builder().beginDate(Timestamp.valueOf(selectVo.getBegin_time()))
                    .endDate(Timestamp.valueOf(selectVo.getEnd_time())).build());
            mappingDtos.add(SsaNewScheduleSplashScreenMappingDto.builder()
                    .scheduleId(selectVo.getId())
                    .gdScheduleId(vo.getScheduleId())
                    .beginTime(Timestamp.valueOf(selectVo.getBegin_time()))
                    .endTime(Timestamp.valueOf(selectVo.getEnd_time())).build());
        }
        dto.setHfSchedules(dateDtos);
        dto.setSsaNewScheduleSplashScreenMappingDtos(mappingDtos);
        dto.setManuscriptInfo(GdCreativeConverter.MAPPER.toManuscriptInfoBo(vo.getManuscriptInfo()));
        dto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProductLabel()));
        dto.setMiniProgram(GdCreativeConverter.MAPPER.toMiniProgramDto(vo.getMiniProgram()));
        dto.setUseDefaultVersion(vo.getUseDefaultVersion());
        if (BooleanUtils.isNotFalse(vo.getUseDefaultVersion())) {
            dto.setSsaNewSplashScreenVersionControlDtos(versionControlDtos);
        } else {
            dto.setSsaNewSplashScreenVersionControlDtos(SsaSplashScreenConvert.versionControlVos2DtoS(vo.getVersionControl(), versionControlDtos));
        }

        dto.setMiddlePage(SsaSplashScreenConverter.MAPPER.toSsaSplashScreenMiddlePageDto(vo.getMiddlePage()));
        dto.setHfJump(topViewHfJumpDto);
        dto.setWakeAppType(vo.getWakeAppType());
        return dto;
    }

    //https://www.tapd.cn/67874887/prong/stories/view/1167874887004523310
    //【品牌】TopView支持闪屏、首焦分开跳转-香奈儿3月商机
    private TopViewHfJumpDto convertHfContext(HfConvertContext context) {
        List<CptJumpDTO> cptJumps;
        MiniProgramDto miniProgram;
        List<Integer> appPackageIds;
        GdJumpType hfJumpType;
        Integer hfWakeAppType;

        SsaJumpType ssaJumpType = context.getSsaJumpType();
        Predicate<Integer> isSupportPlatform = platformId -> Objects.equals(platformId, PlatformType.IPHONE.getCode())
                || Objects.equals(platformId, PlatformType.ANDROID.getCode());

        if (Objects.equals(context.getIsCustomizedHfJump(), 1)) {
            //自定义首焦跳转信息
            TopViewHfJumpVo hfJump = context.getHfJump();
            miniProgram = GdCreativeConverter.MAPPER.toMiniProgramDto(hfJump.getMiniProgram());
            appPackageIds = hfJump.getAppPackageIds();
            hfJumpType = GdJumpType.getByCode(hfJump.getJumpType());
            hfWakeAppType = hfJump.getWakeAppType();
            if (PlatformSwitchEnum.CLOSE.getCode().equals(context.getPlatformSwitch())) {
                //不分端
                SplashScreenJumpVo jumpVo = hfJump.getJumpList().get(0);
                List<PlatformType> platformTypes = context.getSsaConvertContext().getPlatformTypes();
                cptJumps = platformTypes.stream()
                        .filter(platformType -> isSupportPlatform.test(platformType.getCode()))
                        .map(platformType -> convertOriginalCptJump(jumpVo, platformType.getCode(), hfJumpType.getCode()))
                        .collect(Collectors.toList());
            } else {
                //分端
                cptJumps = hfJump.getJumpList().stream()
                        .filter(jumpVo -> isSupportPlatform.test(jumpVo.getPlatformId()))
                        .map(jumpVo -> convertOriginalCptJump(jumpVo, null, hfJumpType.getCode()))
                        .collect(Collectors.toList());

            }
        } else {
            //如果没有自定义首焦跳转，则和闪屏保持一致
            miniProgram = GdCreativeConverter.MAPPER.toMiniProgramDto(context.getSsaMiniProgram());
            //copy：和闪屏互不影响
            appPackageIds = Objects.isNull(context.getSsaAppPackageIds()) ? null : Lists.newArrayList(context.getSsaAppPackageIds());
            hfJumpType = GdJumpType.getByCode(ssaJumpType.changeToGdJumpType());
            hfWakeAppType = context.getSsaWakeAppType();
            cptJumps = context.getSsaButtons().get(0).getSplashScreenJumpDTOS().stream()
                    .filter(jumpVo -> isSupportPlatform.test(jumpVo.getPlatformId()))
                    .map(ssaJumpDto -> CptJumpDTO.builder()
                            .jumpType(hfJumpType.getCode())
                            .jumpLink(ssaJumpDto.getJumpLink())
                            .schemeUrl(ssaJumpDto.getSchemeUrl())
                            .platformId(ssaJumpDto.getPlatformId())
                            //此时首焦唤起开关以闪屏的开关为准
                            .isCallApp(ssaJumpDto.getIsCallApp())
                            //.packageName("")//首焦（banner）创意不需要包名
                            .build())
                    .collect(Collectors.toList());
        }

        boolean isTopViewArchive = context.getHfManuscriptInfo() != null && Utils.isPositive(context.getHfManuscriptInfo().getAid());

        List<CptJumpDTO> cptJumpDTOS = cptJumps.stream()
                .peek(o -> {
                    if (isTopViewArchive) {
                        ManuscriptInfoVo manuscriptInfo = context.getHfManuscriptInfo();
                        o.setPromotionPurposeContent(String.valueOf(manuscriptInfo.getAid()));
                        o.setJumpType(GdJumpType.VIDEO_MOBILE.getCode());
                        o.setJumpLink(null);
                    } else {
                        o.setPromotionPurposeContent(o.getJumpLink());
                    }
                }).collect(Collectors.toList());

        cptJumpDTOS = this.decorateCptJumpDto(context.getNewHfJumpType(), context.getNewHfPromotionPurposeContent(), cptJumpDTOS);

        return TopViewHfJumpDto.builder()
                .miniProgram(miniProgram)
                .jumps(cptJumpDTOS)
                .appPackageIds(appPackageIds)
                .jumpType(hfJumpType)
                .isCustomizedHfJump(context.getIsCustomizedHfJump())
                .wakeAppType(hfWakeAppType)
                .build();
    }

    public List<SsaNewSplashScreenVersionControlDto> mockTopViewVersionControl(ScheduleDto scheduleDto,
                                                                               GdTopViewScheduleDto topViewScheduleDto,
                                                                               SsaSplashScreenConvert.SsaConvertContext context) {
        List<Integer> platformIds = context.getPlatformTypes().stream().map(PlatformType::getCode).collect(Collectors.toList());
        return topViewConvert.parseSsaVersionControl(platformIds, topViewScheduleDto.getHfAdType(),
                scheduleDto.getSsaVideoPlayMode(), scheduleDto.getButtonStyle(), context.getGuideMaterialType(), context.getIsCustomizedNewHfBrandInfo(), context.getSsaAdType(), context.getIsEnableLiveBooking(), topViewScheduleDto.getTransitionMode());
    }

    public UpdateExternalTopViewDto convertToUpdateExternalTopViewDto(UpdateExternalTopViewVo vo) {
        if (vo == null) {
            return null;
        }
        //分端获取跳转信息
        Integer jumpType = vo.getJumpType();
        List<SplashScreenJumpDTO> splashScreenJumpDTOS = new ArrayList<>();
        List<SplashScreenCustomizedDTO> customizedDTOS;
        List<SplashScreenDynamicButtonBO> buttonBOS;
        ScheduleDto schedule = queryScheduleService.getScheduleBaseInfoById(vo.getScheduleId());
        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(vo.getScheduleId());
        GdTopViewDto gdTopViewDto = topViewCreativeService.getTopViewInfoById(vo.getTopViewId());
        Assert.notNull(gdTopViewDto, "需要修改的记录不存在");

        SsaSplashScreenConvert.SsaConvertContext convertContext = SsaSplashScreenConvert.SsaConvertContext.builder()
                .ssaArchive(false)
                .linkageType(SsaLinkageType.NONE)
                .orderProduct(OrderProduct.getByCode(schedule.getOrderProduct()))
                .platformTypes(queryScheduleService.getScheduleOsTarget(schedule.getScheduleId(), true))
                .guideMaterialType(vo.getDynamicButtonVos().get(0).getGuideMaterialType())
                .isCustomizedNewHfBrandInfo(vo.getIsCustomizedNewHfBrandInfo())
                .ssaAdType(topViewScheduleInfo.getSsaAdType())
                .isEnableLiveBooking(vo.getIsEnableLiveBooking())
                .build();
        if (PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            //模块跳转的闪屏跳转信息存放在按钮里面
            buttonBOS = ssaSplashScreenConvert.convert2ButtonBOs(jumpType, vo.getTextColorStyle(), vo.getDynamicButtonVos());
            customizedDTOS = ssaSplashScreenConvert.convert2CustomizedDTO(vo.getSplashScreenCustomizedVos());
        } else {
            //不分端模块跳转的闪屏跳转信息存放在按钮里面
            buttonBOS = ssaSplashScreenConvert.convert2ButtonBOsWithoutPlatform(jumpType, vo.getTextColorStyle(),
                    vo.getDynamicButtonVos(), convertContext);
            customizedDTOS = ssaSplashScreenConvert.convert2CustomizedDTOWithoutPlatform(SplashScreenCustomizedVo.builder()
                    .customized_imp_url_list(vo.getCustomizedImpUrlList())
                    .customized_click_url_list(vo.getCustomizedClickUrlList())
                    .customized_middle_page_click_url_list(vo.getCustomizedMiddlePageClickUrlList())
                    .customized_open_middle_page_click_url_list(vo.getCustomizedOpenMiddlePageClickUrlList())
                    .build(), convertContext);
        }

        List<SsaSplashScreenVersionControlDto> versionControlDtos = mockUpdateTopViewVersionControl(schedule, topViewScheduleInfo, convertContext);

        //topview不支持选择式闪屏
        Assert.isTrue(!CollectionUtils.isEmpty(buttonBOS), "按钮跳转信息不能为空");

        HfConvertContext hfConvertContext = HfConvertContext.builder()
                .platformSwitch(vo.getPlatformSwitch())
                .isCustomizedHfJump(vo.getIsCustomizedHfJump())
                .hfJump(vo.getHfJump())
                .hfManuscriptInfo(vo.getManuscriptInfo())
                .newHfJumpType(vo.getNewHfJumpType())
                .newHfPromotionPurposeContent(vo.getNewHfPromotionPurposeContent())
                .ssaJumpType(SsaJumpType.getByCode(vo.getJumpType()))
                .ssaAppPackageIds(vo.getAppPackageIds())
                .ssaButtons(buttonBOS)
                .ssaMiniProgram(vo.getMiniProgram())
                .ssaWakeAppType(vo.getWakeAppType())
                .ssaConvertContext(convertContext)
                .build();

        TopViewHfJumpDto topViewHfJumpDto = convertHfContext(hfConvertContext);

        UpdateExternalTopViewDto dto = UpdateExternalTopViewDto.builder()
                .ssaBaseImageDTOList(newSplashScreenBaseImageVos2NewDtos(vo.getSsaBaseImageList()))
                .ssaVideo(newSplashScreenVideoVo2Dto(vo.getSsaVideo()))
                .eggVideo(mgkVideoVoToDto(vo.getEggVideo()))
                .transitionVideo(transitionVideoVoToDto(vo.getTransitionVideo()))
                .ssaCustomizedDTOS(customizedDTOS)
                .splashScreenJumpDTOS(splashScreenJumpDTOS)
                .buttonBOS(buttonBOS)
                .cptJumpDTOS(topViewHfJumpDto.getJumps())//可以忽略，参考topViewHfJumpDto
                .newHfVideo(newSplashScreenVideoVo2Dto(vo.getNewHfVideo()))
                .interactInstructions(vo.getInteractInstructions())
                .hfJump(topViewHfJumpDto)
                .build();
        BeanUtils.copyProperties(vo, dto);

        if (PlatformSwitchEnum.CLOSE.getCode().equals(vo.getPlatformSwitch())) {
            dto.setHfAndroidCustomizedClickUrl(vo.getHfCustomizedClickUrl());
            dto.setHfAndroidCustomizedImpUrl(vo.getHfCustomizedImpUrl());
            dto.setHfIosCustomizedClickUrl(vo.getHfCustomizedClickUrl());
            dto.setHfIosCustomizedImpUrl(vo.getHfCustomizedImpUrl());
            dto.setHfAndroidCustomizedClickUrlList(vo.getHfCustomizedClickUrlList());
            dto.setHfIosCustomizedClickUrlList(vo.getHfCustomizedClickUrlList());
        }

        List<ScheduleDateDto> dateDtos = new ArrayList<>();
        List<SsaNewScheduleSplashScreenMappingDto> mappingDtos = new ArrayList<>();
        for (SsaScheduleSelectVo selectVo : vo.getSchedules()) {
            dateDtos.add(ScheduleDateDto.builder().beginDate(Timestamp.valueOf(selectVo.getBegin_time()))
                    .endDate(Timestamp.valueOf(selectVo.getEnd_time())).build());
            mappingDtos.add(SsaNewScheduleSplashScreenMappingDto.builder().scheduleId(selectVo.getId())
                    .beginTime(Timestamp.valueOf(selectVo.getBegin_time()))
                    .endTime(Timestamp.valueOf(selectVo.getEnd_time())).build());
        }
        dto.setHfSchedules(dateDtos);
        dto.setSsaNewScheduleSplashScreenMappingDtos(mappingDtos);
        dto.setManuscriptInfo(GdCreativeConverter.MAPPER.toManuscriptInfoBo(vo.getManuscriptInfo()));
        dto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProductLabel()));
        dto.setMiniProgram(GdCreativeConverter.MAPPER.toMiniProgramDto(vo.getMiniProgram()));
        dto.setUseDefaultVersion(vo.getUseDefaultVersion());
        if (BooleanUtils.isNotFalse(vo.getUseDefaultVersion())) {
            dto.setSsaVersionControlDtos(versionControlDtos);
        } else {
            dto.setSsaVersionControlDtos(SsaSplashScreenConvert.versionControlUpdateVos2DtoS(vo.getVersionControl(), Math.toIntExact(gdTopViewDto.getSsaCreativeId()), versionControlDtos));
        }
        dto.setMiddlePage(SsaSplashScreenConverter.MAPPER.toSsaSplashScreenMiddlePageDto(vo.getMiddlePage()));
        dto.setWakeAppType(vo.getWakeAppType());
        return dto;
    }

    public List<SsaSplashScreenVersionControlDto> mockUpdateTopViewVersionControl(ScheduleDto scheduleDto,
                                                                                  GdTopViewScheduleDto topViewScheduleDto,
                                                                                  SsaSplashScreenConvert.SsaConvertContext context) {
        List<SsaNewSplashScreenVersionControlDto> controlDtos = mockTopViewVersionControl(scheduleDto, topViewScheduleDto, context);
        return controlDtos.stream().map(o -> SsaSplashScreenVersionControlDto.builder()
                .platformId(o.getPlatformId())
                .startVersion(o.getStartVersion())
                .endVersion(o.getEndVersion())
                .build()).collect(Collectors.toList());
    }

    private static List<SsaNewSplashScreenBaseImageDto> newSplashScreenBaseImageVos2NewDtos(List<SplashScreenBaseImageVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return new ArrayList<>();
        }
        List<SsaNewSplashScreenBaseImageDto> ssaNewSplashScreenBaseImageDtos = new ArrayList<>();
        for (SplashScreenBaseImageVo vo : vos) {
            if (BaseImageTypeEnum.LOGO.getCode().equals(vo.getType()) && Strings.isNullOrEmpty(vo.getUrl())) {
                continue;
            }
            ssaNewSplashScreenBaseImageDtos.add(newSplashScreenBaseImageVo2NewDto(vo));
        }
        return ssaNewSplashScreenBaseImageDtos;
    }

    private static SsaNewSplashScreenBaseImageDto newSplashScreenBaseImageVo2NewDto(SplashScreenBaseImageVo vo) {
        return SsaNewSplashScreenBaseImageDto.builder().hash(vo.getHash().trim()).url(vo.getUrl().trim()).type(vo.getType()).build();
    }

    private static SsaNewSplashScreenVideoDto newSplashScreenVideoVo2Dto(SsaNewSplashScreenVideoVo vo) {
        if (vo == null) {
            return null;
        }
        return SsaNewSplashScreenVideoDto.builder()
                .uposUrl(vo.getUpos_url())
                .uposAuth(vo.getUpos_auth())
                .bizId(vo.getBiz_id())
                .fileName(vo.getFile_name())
                .build();
    }

    private static SsaNewSplashScreenVideoDto mgkVideoVoToDto(MgkVideoVo eggVideo) {
        if (eggVideo == null) {
            return new SsaNewSplashScreenVideoDto();
        }
        return SsaNewSplashScreenVideoDto.builder()
                .bizId(Optional.ofNullable(eggVideo.getBiz_id()).orElse(eggVideo.getId()))
                .fileName(eggVideo.getName())
                .xcodeUposUrl(eggVideo.getUrl())
                .xcodeWidth(eggVideo.getWidth())
                .xcodeHeight(eggVideo.getHeight())
                .xcodeDuration(eggVideo.getDuration())
                .xcodeMd5(eggVideo.getMd5())
                .uposUrl(eggVideo.getBefore_url())
                .build();
    }

    private static TransitionVideoDto transitionVideoVoToDto(TransitionVideoVo transitionVideo) {
        if (transitionVideo == null) {
            return null;
        }
        return TransitionVideoDto.builder()
                .url(transitionVideo.getUrl())
                .bizId(transitionVideo.getBizId())
                .md5(transitionVideo.getMd5())
                .width(transitionVideo.getWidth())
                .height(transitionVideo.getHeight())
                .duration(transitionVideo.getDuration())
                .size(transitionVideo.getSize())
                .build();
    }

    private CptJumpDTO convertOriginalCptJump(SplashScreenJumpVo ssaJump, Integer platformId, Integer jumpType) {
        return CptJumpDTO.builder()
                .jumpType(Objects.nonNull(jumpType) ? jumpType : ssaJump.getJumpType())
                .jumpLink(ssaJump.getJumpLink())
                .schemeUrl(ssaJump.getSchemeUrl())
                .isCallApp(StringUtils.hasText(ssaJump.getSchemeUrl()) ? 2 : 1) //首焦只要存在scheme即认为唤起开关打开
                .platformId(Objects.nonNull(platformId) ? platformId : ssaJump.getPlatformId())
                //.packageName("")//首焦（banner）创意不需要包名
                .build();
    }

    private List<CptJumpDTO> decorateCptJumpDto(Integer jumpType, String ppc, List<CptJumpDTO> cptJumpDTOS) {
        List<CptJumpDTO> converted = cptJumpDTOS;
        //如果存在单独的首焦推广目的，比如直播兜底场景，覆盖来自闪屏的跳转链接
        if (!StringUtils.isEmpty(ppc)) {
            GdJumpType hfJumpType = GdJumpType.getByCode(jumpType);
            converted = cptJumpDTOS.stream()
                    .peek(jumpDTO -> {
                        jumpDTO.setJumpType(hfJumpType.getCode());
                        jumpDTO.setPromotionPurposeContent(ppc);
                    }).collect(Collectors.toList());
        }
        return converted;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class HfConvertContext {
        private Integer platformSwitch;
        private Integer isCustomizedHfJump;
        private TopViewHfJumpVo hfJump;
        private ManuscriptInfoVo hfManuscriptInfo;
        /**
         * 新版首焦跳转类型，历史逻辑待梳理
         * {@link GdJumpType}
         */
        private Integer newHfJumpType;
        /**
         * 新版首焦推广目的页，历史逻辑待梳理
         */
        private String newHfPromotionPurposeContent;
        private SsaJumpType ssaJumpType;
        private List<Integer> ssaAppPackageIds;
        private List<SplashScreenDynamicButtonBO> ssaButtons;
        private MiniProgramVo ssaMiniProgram;
        private Integer ssaWakeAppType;
        private SsaSplashScreenConvert.SsaConvertContext ssaConvertContext;
    }
}
