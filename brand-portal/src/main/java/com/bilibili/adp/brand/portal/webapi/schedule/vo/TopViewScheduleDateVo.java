package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewScheduleDateVo {

    @ApiModelProperty("排期内可选择的日期")
    private List<TopViewScheduleSingleDayVo> schedule_dates;
}
