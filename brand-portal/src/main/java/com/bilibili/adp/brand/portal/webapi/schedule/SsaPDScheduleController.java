package com.bilibili.adp.brand.portal.webapi.schedule;

import com.bilibili.adp.brand.portal.convert.schedule.SsaPDScheduleConvert;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.SsaPdScheduleDto;
import com.bilibili.brand.api.schedule.service.ISsaPdScheduleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.common.enums.SystemConfigEnum.GD_PRICE_FESTIVAL_RAISE_RATIO;
import static com.bilibili.ssa.platform.common.enums.SystemConfigEnum.SSA_PRICE_FESTIVAL_DAYS;

@Slf4j
@Controller
@RequestMapping("/web_api/v1/schedules/ssa/pd")
@Api(value = "/schedules", tags = "闪屏pd排期相关")
public class SsaPDScheduleController extends BaseController {

    @Autowired
    private ISsaPdScheduleService pdScheduleService;

    @Resource
    private SsaPDScheduleConvert ssaPDScheduleConvert;

    @ApiOperation(value = "新建闪屏pd排期")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> createCptSchedule(@ApiIgnore Context context, @Valid @RequestBody SsaPDScheduleAddVo vo) throws ServiceException {

        List<SsaPdScheduleDto> dtos = ssaPDScheduleConvert.convert2Dtos(vo);

        for (SsaPdScheduleDto dto : dtos) {
            pdScheduleService.createSchedule(dto, super.getOperator(context));
        }

        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "编辑闪屏pd排期")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateCptSchedule(@ApiIgnore Context context, @Valid @RequestBody SsaPDScheduleUpdateVo vo)
            throws ServiceException {

        SsaPdScheduleDto dto = SsaPDScheduleConvert.convert2Dto(vo);

        pdScheduleService.updateSchedule(dto, super.getOperator(context));

        return Response.SUCCESS(null);
    }

    @ApiOperation("创意可用时间")
    @RequestMapping(value = "/schedule/date", method = RequestMethod.GET)
    @ResponseBody
    public Response<SsaCpmOrderScheduleVo> getAvailableDate(
            @RequestParam("schedule_id") Integer scheduleId) {

        Pair<Timestamp, Timestamp> timePair =  pdScheduleService.getAvailableDate(scheduleId);

        return Response.SUCCESS(SsaCpmOrderScheduleVo.builder().beginDate(timePair.getKey())
                .endDate(timePair.getValue()).build());
    }
}
