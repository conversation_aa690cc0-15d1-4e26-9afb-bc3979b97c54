package com.bilibili.adp.brand.portal.webapi.resource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.brand.portal.common.IdNameVo;
import com.bilibili.adp.brand.portal.convert.creative.GdCreativeConverter;
import com.bilibili.adp.brand.portal.convert.resource.ResourceControllerConverter;
import com.bilibili.adp.brand.portal.convert.resource.ResourceConvert;
import com.bilibili.adp.brand.portal.convert.schedule.OgvScheduleConverter;
import com.bilibili.adp.brand.portal.service.launch.WebAppPackageService;
import com.bilibili.adp.brand.portal.webapi.launch.vo.CreativeTwistVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.*;
import com.bilibili.adp.brand.portal.webapi.resource.vo.NameValuePair;
import com.bilibili.adp.brand.portal.webapi.resource.vo.OgvResourceStickerVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.OgvSeasonVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.PgcInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.PlatformBaseVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.QrUploadResultVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.ScvVideoReq;
import com.bilibili.adp.brand.portal.webapi.resource.vo.SimpleArchiveInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.SimpleMidInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.SingleDynamicInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.TemplateVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.UpDynamicInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.UpFansVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.VideoManuscriptVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.VideoRequireParam;
import com.bilibili.adp.brand.portal.webapi.resource.vo.scv.CmArchiveVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.scv.CsvJsonData;
import com.bilibili.adp.brand.portal.webapi.resource.vo.scv.CsvPager;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.CrowdPackVo;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AppPackagePlatformStatus;
import com.bilibili.adp.common.enums.AppPackageStatus;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.app_package.dto.QueryAppPackageDto;
import com.bilibili.adp.resource.api.soa.ISoaAppPackageService;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.brand.api.account.dto.BilibiliUserInfoDto;
import com.bilibili.brand.api.account.service.IAccountGroupMappingService;
import com.bilibili.brand.api.common.bean.IdName;
import com.bilibili.brand.api.common.bean.LaunchConstant;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.creative.dto.CreativeTwistDto;
import com.bilibili.brand.api.launch.dto.BfsFile;
import com.bilibili.brand.api.launch.dto.ImageVideoDto;
import com.bilibili.brand.api.live.dto.LiveRoomDto;
import com.bilibili.brand.api.material.IIPVideoService;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.api.meta.ITemplateMaterialService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.service.IGdOrderExtService;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.account.group.IAccountSlotGroupService;
import com.bilibili.brand.api.resource.archive.BrandArchiveDto;
import com.bilibili.brand.api.resource.archive.BrandArchiveQueryDto;
import com.bilibili.brand.api.resource.archive.IBrandArchiveService;
import com.bilibili.brand.api.resource.area.group.IResAreaGroupService;
import com.bilibili.brand.api.resource.area.group.dto.AreaTreeDto;
import com.bilibili.brand.api.resource.channel.Channel;
import com.bilibili.brand.api.resource.channel.IChannelService;
import com.bilibili.brand.api.resource.cm.mark.CmMarkDto;
import com.bilibili.brand.api.resource.cm.mark.ICmMarkService;
import com.bilibili.brand.api.resource.crowd.BrandCrowdPackageDto;
import com.bilibili.brand.api.resource.ogv.IResOgvService;
import com.bilibili.brand.api.resource.ogv.OgvResourceQueryDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceStickerDto;
import com.bilibili.brand.api.resource.ogv.OgvSeasonDto;
import com.bilibili.brand.api.resource.platform.IPlatformService;
import com.bilibili.brand.api.resource.platform.MgkVideoDto;
import com.bilibili.brand.api.resource.platform.QueryMgkVideoParam;
import com.bilibili.brand.api.resource.slot.ISlotService;
import com.bilibili.brand.api.resource.slot.Slot;
import com.bilibili.brand.api.resource.slot.SlotGroupDto;
import com.bilibili.brand.api.resource.slot.SlotGroupQuery;
import com.bilibili.brand.api.resource.slot_group.IResSlotGroupService;
import com.bilibili.brand.api.resource.slot_group.ResSlotGroupBaseDto;
import com.bilibili.brand.api.resource.slot_group.StylePlatformGroupDto;
import com.bilibili.brand.api.resource.slot_group.StyleTreeDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.target_lau.IResTargetItemService;
import com.bilibili.brand.api.resource.target_lau.dto.ResTargetItemDto;
import com.bilibili.brand.api.resource.target_lau.dto.TargetTreeDto;
import com.bilibili.brand.api.resource.targetmeta.ITargetService;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.schedule.service.inventory.IGoblinInventoryService;
import com.bilibili.brand.api.template.IBrandTemplateLabelService;
import com.bilibili.brand.api.template.dto.BrandTemplateLabelRelationDto;
import com.bilibili.brand.biz.archive.ArchiveInfoService;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.config.business.OgvConfig;
import com.bilibili.brand.biz.creative.service.CreativeTwistService;
import com.bilibili.brand.biz.creative.service.CreativeValidator;
import com.bilibili.brand.biz.order.service.BidderConfigService;
import com.bilibili.brand.biz.proto.ImageUtil;
import com.bilibili.brand.biz.resource.crowd.CrowdPackageService;
import com.bilibili.brand.biz.resource.pojo.ResTemplateAreaGroupMappingPo;
import com.bilibili.brand.biz.resource.pojo.ResTemplateAreaGroupMappingPoExample;
import com.bilibili.brand.biz.resource.res_dao.ResTemplateAreaGroupMappingDao;
import com.bilibili.brand.biz.rpc.dto.ArchiveInfoBo;
import com.bilibili.brand.biz.rpc.dto.PgcInfoBo;
import com.bilibili.brand.biz.rpc.grpc.client.AccountGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.CpmAdpGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.ArchiveGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.BDataServiceGrpcClient;
import com.bilibili.brand.biz.rpc.grpc.client.LiveGrpcClient;
import com.bilibili.brand.biz.template.BrandTemplateHelper;
import com.bilibili.brand.common.Constant;
import com.bilibili.brand.dto.resource.GameDto;
import com.bilibili.brand.dto.template.TemplateLabelOptions;
import com.bilibili.cpt.platform.api.external.IUpService;
import com.bilibili.cpt.platform.api.external.SingleDynamicInfoDto;
import com.bilibili.cpt.platform.api.external.UpDynamicInfoDto;
import com.bilibili.cpt.platform.api.external.UpFansDto;
import com.bilibili.cpt.platform.biz.enumerate.TemplateJumpType;
import com.bilibili.cpt.platform.biz.enumerate.UpFansLevelEnum;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.cpt.platform.common.OriginTag;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.bilibili.enums.*;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.service.ICardTypeService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.service.template.bos.LocTemplateButtonBo;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mas.api.extreme.dto.ExtremeTeamConfigDetailDto;
import com.bilibili.mas.api.soa.ISoaMasExtremeTeamService;
import com.bilibili.mas.common.enums.VideoDealStatus;
import com.bilibili.mgk.platform.common.video_library.SizeTypeEnum;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaUposVideoDto;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.common.enums.SsaButtonStyle;
import com.bilibili.ssa.platform.common.enums.SsaConstants;
import com.bilibili.ssa.platform.common.enums.SystemConfig;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.base.Throwables;
import com.google.common.collect.*;
import com.google.gson.reflect.TypeToken;
import com.mysema.commons.lang.Pair;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.brand.portal.convert.resource.ResourceConvert.buildChannelVoList;
import static com.bilibili.adp.brand.portal.convert.resource.ResourceConvert.buildImageVideoVo;
import static com.bilibili.adp.brand.portal.convert.resource.ResourceConvert.buildSlotGroupVoList;
import static com.bilibili.adp.brand.portal.convert.resource.ResourceConvert.validateImage;

/**
 * Created by zhongyuan on 2016/12/12.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/resource")
@Api("资源相关接口")
public class ResourceController extends BaseController {

    private final static List<Integer> MOBILE_CM_MARKS = Arrays.asList(5, 6);

    @Autowired
    private ISlotService slotService;

    @Autowired
    private IAccountGroupMappingService accountGroupMappingService;

    @Autowired
    private IAccountSlotGroupService accountSlotGroupService;

    @Autowired
    private IChannelService channelService;

    @Autowired
    private ITargetService targetService;

    @Autowired
    private IResTargetItemService resTargetItemService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private ICmMarkService cmMarkService;

    @Autowired
    private IResSlotGroupService resSlotGroupService;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private IQuerySourceService querySourceService;

    @Autowired
    private IResAreaGroupService resAreaGroupService;

    @Autowired
    private CreativeValidator creativeValidator;

    @Autowired
    private IPlatformService platformService;

    @Autowired
    private IBfsService bfsService;

    @Autowired
    private ICardTypeService cardTypeService;

    @Autowired
    private LiveGrpcClient liveGrpcClient;

    @Autowired
    private AccountGrpcClient accountGrpcClient;

    @Autowired
    private ISoaAppPackageService soaAppPackageService;

    @Autowired
    private WebAppPackageService webAppPackageService;

    @Autowired
    private ISoaMasExtremeTeamService soaMasExtremeTeamService;

    @Autowired
    private IGdOrderService orderService;

    @Autowired
    private ResTemplateAreaGroupMappingDao mappingDao;

    @Autowired
    private ISsaUposVideoService uposVideoService;

    @Value("${bfs.categoryname}")
    private String categoryName;

    @Value("${brand.face.max.size.kb:300}")
    private Integer brandFaceMaxSizeKB;

    @Value("${brand.face.width:200}")
    private Integer brandFaceWidth;

    @Value("${brand.face.height:200}")
    private Integer brandFaceHeight;

    @Value("${ott.gd.video.width:1920}")
    private Integer ottGdVideoWidth;

    @Value("${ott.gd.video.height:1080}")
    private Integer ottGdVideoHeight;

    @Autowired
    private IIPVideoService ipVideoService;

    @Value("${resource.support.image.template.card.type}")
    private List<Integer> supportImageTemplateCardType;

    @Value("#{'${resource.selective.card.template.ids}'.split(',')}")
    private List<Integer> selectiveTemplateIds;

    @Value("#{'${resource.search.card.types:58}'.split(',')}")
    private List<Integer> searchCards;

    //动态模板
    @Value("#{'${resource.dynamic.template.ids:171,142}'.split(',')}")
    private List<Integer> dynamicTemplateIds;

    //支持ogv投放模板
    @Value("#{'${resource.ogv.template.ids}'.split(',')}")
    private List<Integer> ogvTemplateIds;

    //品牌默认商业标
    @Value("#{'${resource.default.bus.mark.ids:0,1}'.split(',')}")
    private List<Integer> defaultBusMarks;

    //起飞商业标
    @Value("#{'${resource.fly.bus.mark.ids:123}'.split(',')}")
    private List<Integer> flyBusMarks;

    @Value("#{'${resource.support.portrait.template.ids}'.split(',')}")
    private List<Integer> supportPortraitTemplateIds;

    @Value("#{'${resource.small.card.template.ids:22,23,38,61,165,237}'.split(',')}")
    private List<Integer> smallCardTemplateIds;

    @Value("#{'${resource.ott.gd.template.ids:271}'.split(',')}")
    private List<Integer> ottGdTemplateIds;

    @Value("#{'${resource.ssa.cm.mark.ids:1}'.split(',')}")
    private List<Integer> ssaCmMarks;

    @Value("#{'${resource.ott.gd.local.cm.mark.ids:93,127,9}'.split(',')}")
    private List<Integer> ottGdLocalCmMarks;

    @Value("#{'${resource.ott.gd.manuscript.cm.mark.ids:105,127,9}'.split(',')}")
    private List<Integer> ottGdManuscriptCmMarks;

    @Value("#{'${resource.ott.gd.ogv.cm.mark.ids:93,105,127,9}'.split(',')}")
    private List<Integer> ottGdOGVCmMarks;

    @Value("#{'${resource.cpt.cm.mark.ids:1,120}'.split(',')}")
    private List<Integer> cptCmMarks;

    @Value("#{'${resource.search.cpt.cm.mark.ids:1}'.split(',')}")
    private List<Integer> searchCptCmMarks;

    @Value("#{'${resource.ott.gd.manuscript.template.ids:395}'.split(',')}")
    private List<Integer> ottGdManuscriptTemplateIds;

    @Value("#{'${resource.search.inline.video.slots:4221,4226}'.split(',')}")
    private List<Integer> inlineVideoSlotIds;

    @Value("#{'${resource.search.inline.video.templates:387,388,389}'.split(',')}")
    private List<Integer> inlineVideoTemplateIds;

    //热门资源位
    @Value("#{'${resource.hot.cpt.source.ids:5320,5321}'.split(',')}")
    private List<Integer> hotCptSourceIds;

    //热门资源位
    @Value("#{'${resource.selected.cpt.source.ids:5845,5848,5849,5870,5871}'.split(',')}")
    private List<Integer> selectedCptSourceIds;

    @Value("#{'${resource.topic.cpt.cm.mark.ids:9}'.split(',')}")
    private List<Integer> cptTopicCmMarks;
    //评论区小黄条广告标（无标）
    //https://www.tapd.bilibili.co/********/prong/stories/view/11********003071955
    @Value("#{'${resource.cpt.comment.yellow.cm.mark.ids:9}'.split(',')}")
    private List<Integer> cptCommentYellowCmMarks;

    //动态-粉丝必现（无标）
    //https://www.tapd.cn/********/prong/stories/view/11********004248971
    @Value("#{'${resource.cpt.dynamic.fans.cm.mark.ids:9}'.split(',')}")
    private List<Integer> dynamicFanCmMarks;

    @Autowired
    private ConfigCenter configCenter;

    @Value("${qr.frame.max.size:100}")
    private Integer qrFrameMaxKbSize;

    @Value("${qr.frame.width:300}")
    private Integer qrFrameWidth;

    @Value("${qr.frame.height:300}")
    private Integer qrFrameHeight;

    @Value("${egg.video.width:1080}")
    private Integer eggVideoWidth;

    @Value("${egg.video.height:1920}")
    private Integer eggVideoHeight;

    @Value("${qr.animation.max.size:2048}")
    private Integer qrAnimationMaxKbSize;

    //    @Value("${ssa.pd.area.group.id:811}")
    private Set<Integer> ssaPdAreaGroupIdSet = Sets.newHashSet(811, 978);

    //    @Value("${pd.area.group.id:974}")
    private Set<Integer> pdAreaGroupIdSet = Sets.newHashSet(974, 979);

    //支持原生落地页投放模板
    @Value("#{'${resource.original.page.template.ids:418,419,420,421,422}'.split(',')}")
    private List<Integer> originalPageTemplateIds;

    /**
     * 查询csv稿件接口url
     */
    @Value("${csv.url:http://uat-cm-mng.bilibili.co/scv/api/open/lib/archive/list}")
    private String csvUrl;

    @Value("${splash.ott.target.id}")
    private Integer splashOttTargetId;

    @Autowired
    private ITemplateMaterialService templateMaterialService;

    @Autowired
    private IGdOrderExtService orderExtService;

    @Autowired
    private ArchiveGrpcClient archiveGrpcClient;

    @Autowired
    private ArchiveInfoService archiveInfoService;

    @Autowired
    private IBrandArchiveService brandArchiveService;

    @Autowired
    private IResOgvService resOgvService;
    @Autowired
    private CrowdPackageService crowdPackageService;
    @Autowired
    private IGoblinInventoryService goblinInventoryService;

    @Autowired
    private BDataServiceGrpcClient dataServiceGrpcClient;

    @Autowired
    private CpmAdpGrpcClient cpmAdpGrpcClient;

    @Autowired
    private IUpService upService;

    @Autowired
    private CreativeTwistService creativeTwistService;

    @Autowired
    private BidderConfigService bidderConfigService;

    @Autowired
    private IBrandTemplateLabelService brandTemplateLabelService;
    @Autowired
    private BrandTemplateHelper brandTemplateHelper;

    //地域组 + 顺序

    private static final Map<Integer, SimpleAreaGroupVo> AREA_GROUP_ORDER = ImmutableMap.<Integer, SimpleAreaGroupVo>builder()
            .put(2, SimpleAreaGroupVo.builder().id(2).order(1).name("核心城市").build())
            .put(1, SimpleAreaGroupVo.builder().id(1).order(2).name("重点城市").build())
            .put(620, SimpleAreaGroupVo.builder().id(620).order(3).name("一线城市").build())
            .put(3, SimpleAreaGroupVo.builder().id(3).order(4).name("其他省份").build())
            .put(811, SimpleAreaGroupVo.builder().id(811).order(5).name("闪屏PD可选城市").build())
            .put(974, SimpleAreaGroupVo.builder().id(974).order(6).name("pd可选城市").build())
            .build();

    //特殊客户的城市分组
    private static final Map<Integer, SimpleAreaGroupVo> SPECIAL_AREA_GROUP_ORDER = ImmutableMap.<Integer, SimpleAreaGroupVo>builder()
            .put(975, SimpleAreaGroupVo.builder().id(975).order(1).name("核心城市").build())
            .put(976, SimpleAreaGroupVo.builder().id(976).order(2).name("重点城市").build())
            .put(620, SimpleAreaGroupVo.builder().id(620).order(3).name("一线城市").build())
            .put(977, SimpleAreaGroupVo.builder().id(977).order(4).name("其他省份").build())
            .put(978, SimpleAreaGroupVo.builder().id(978).order(5).name("闪屏PD可选城市").build())
            .put(979, SimpleAreaGroupVo.builder().id(979).order(6).name("pd可选城市").build())
            .build();

    @GetMapping("/crowd_packages")
    public Response<List<CrowdPackVo>> queryCrowdPackages(Context context,
                                                          @RequestParam("start_time") Long startTime,
                                                          @RequestParam("end_time") Long endTime,
                                                          @RequestParam(value = "template_id", required = false) Integer templateId,
                                                          @RequestParam(value = "order_product", required = false) Integer orderProduct) {

        List<Integer> scenes = crowdPackageService.getSceneCode(templateId, orderProduct);

        Page<BrandCrowdPackageDto> brandCrowdPackageDtoPage =
                crowdPackageService.queryCrowdPackages(null, null, startTime, endTime, scenes, 1, Integer.MAX_VALUE);

        List<BrandCrowdPackageDto> rows = brandCrowdPackageDtoPage.getRows();
        List<CrowdPackVo> res = new LinkedList<>();
        if (!CollectionUtils.isEmpty(rows)) {
            List<Integer> sivtAccountList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_ACCOUNT_LIST.getCode());
            List<Integer> sivtCrowdPackageList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_LIST.getCode());
            boolean isSivtBlackAccount = sivtAccountList.contains(context.getAccountId());
            res = rows.stream()
                    .map(crowdPackage -> CrowdPackVo.builder()
                            .id(Math.toIntExact(crowdPackage.getPackageId()))
                            .name(crowdPackage.getName())
                            .userType(crowdPackage.getUserType())
                            .excludeSelected(isSivtBlackAccount && sivtCrowdPackageList.contains(Math.toIntExact(crowdPackage.getPackageId())))
                            .build())
                    .collect(Collectors.toList());
        }

        return Response.SUCCESS(res);
    }


    @GetMapping("pgc/detail")
    public Response<PgcInfoVo> queryPgcInfo(@RequestParam("avid") Long avid) {
        PgcInfoBo pgcInfoBo = archiveInfoService.queryPgcInfo(avid);
        return Response.SUCCESS(ResourceControllerConverter.MAPPER.toVo(pgcInfoBo));
    }

    @ApiOperation(value = "")
    @RequestMapping(value = "ssa/story/template", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<TemplateVo> getSsaStoryTemplate(Context context) {

        Map<Integer, TemplateDto> templateMap =
                queryTemplateService.getTemplateMapInIds(Collections.singletonList(configCenter.getSsaConfig().getSsaStoryTemplate()));

        List<TemplateVo> templateVos = templateDtoMapToVo(templateMap, PromotionPurposeType.LANDING_PAGE,
                isSupportSchemeUrl(context.getAccountId(), null, null), OrderProduct.GD_CPM.getCode());
        return Response.SUCCESS(templateVos.get(0));
    }

    @ApiOperation(value = "获取mid对应信息")
    @RequestMapping(value = "/mid_list", method = RequestMethod.GET)
    public Response<List<SimpleMidInfoVo>> queryMidList(@RequestParam("mid_list") List<Long> midList) {
        List<SimpleMidInfoVo> result = new LinkedList<>();
        if (!CollectionUtils.isEmpty(midList)) {
            if (midList.size() == 1 && midList.get(0) == 0) {
                return Response.SUCCESS(result);
            }

            List<List<Long>> partition = Lists.partition(midList, 20);
            partition.forEach(partitionMidList -> {
                Map<Long, BilibiliUserInfoDto> infosMap = accountGrpcClient.queryValidUser(partitionMidList);

                List<Long> validMidList = Lists.newArrayList(infosMap.keySet());
                Collection<Long> invalidMidList = org.apache.commons.collections4.CollectionUtils.subtract(partitionMidList, validMidList);
                Assert.isTrue(invalidMidList.isEmpty(), "下述mid不存在，请检查后重试，无效的mid=" + JSON.toJSONString(invalidMidList));

                infosMap.forEach((mid, userInfo) -> {
                    SimpleMidInfoVo simpleMidInfoVo = new SimpleMidInfoVo();
                    simpleMidInfoVo.setMid(userInfo.getMid());
                    simpleMidInfoVo.setName(userInfo.getName());
                    result.add(simpleMidInfoVo);
                });
            });
        }
        return Response.SUCCESS(result);
    }

    @ApiOperation(value = "获取avid对应信息")
    @RequestMapping(value = "/archive_list", method = RequestMethod.GET)
    public Response<List<SimpleArchiveInfoVo>> queryAvidList(@RequestParam("avid_list") List<Long> avidList) {
        List<SimpleArchiveInfoVo> result = new LinkedList<>();
        if (!CollectionUtils.isEmpty(avidList)) {
            List<List<Long>> partition = Lists.partition(avidList, 20);
            partition.forEach(partitionAvidList -> {
                Map<Long, ArchiveInfoBo> archiveInfos = archiveGrpcClient.queryArchiveInfo(avidList);
                archiveInfos.forEach((avid, archive) -> {
                    SimpleArchiveInfoVo simpleArchiveInfoVo = new SimpleArchiveInfoVo();
                    simpleArchiveInfoVo.setAvid(avid);
                    simpleArchiveInfoVo.setTitle(archive.getTitle());
                    result.add(simpleArchiveInfoVo);
                });

            });
        }
        return Response.SUCCESS(result);
    }

    @ApiOperation(value = "获取up粉丝相关信息")
    @RequestMapping(value = "/up/fans", method = RequestMethod.GET)
    public Response<List<UpFansVo>> queryFansByMid(@RequestParam(value = "up_mid_list") List<Long> upMidList) {
        List<UpFansDto> upFansDtos = upService.queryUpFans(upMidList);
        List<UpFansVo> upFansVos = upFansDtos.stream()
                .map(item -> UpFansVo.builder()
                        .upMid(item.getUpMid())
                        .upNickName(item.getUpNickName())
                        .upFansNumber(item.getUpFansNumber())
                        .upFansLevel(item.getUpFansLevel())
                        .upFansLevelDesc(UpFansLevelEnum.getByCode(item.getUpFansLevel()).getDesc())
                        .build()
                ).collect(Collectors.toList());
        return Response.SUCCESS(upFansVos);
    }

    @ApiOperation(value = "查询用户发布动态")
    @RequestMapping(value = "/dynamic/query", method = RequestMethod.GET)
    public Response<UpDynamicInfoVo> queryUpDynamic(@RequestParam(value = "up_mid") Long upMid, @RequestParam(value = "template_id", required = false) Integer templateId) {
        List<Long> typeList = new ArrayList<>();
        Long limitTime = 0L;
        if (Objects.equals(templateId, configCenter.getCptConfig().getDynamicPicTemplate())) {
            typeList.add(2L);
            typeList.add(4L);
            limitTime = LocalDateTime.now().minusDays(15).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000L;
        } else if (Objects.equals(templateId, configCenter.getCptConfig().getDynamicVideoTemplate())) {
            typeList.add(8L);
            limitTime = LocalDateTime.now().minusDays(30).toInstant(ZoneOffset.ofHours(8)).toEpochMilli() / 1000L;
        }

        UpDynamicInfoDto upDynamicInfoDto = upService.queryUpDynamic(upMid, typeList, limitTime);

        UpDynamicInfoVo upDynamicInfoVo = UpDynamicInfoVo.builder()
                .upMid(upDynamicInfoDto.getUpMid())
                .upNickName(upDynamicInfoDto.getUpNickName())
                .upFaceUrl(upDynamicInfoDto.getUpFaceUrl())
                .upFansNumber(upDynamicInfoDto.getUpFansNumber())
                .build();
        if (!CollectionUtils.isEmpty(upDynamicInfoDto.getDynamicList())) {
            List<SingleDynamicInfoVo> singleDynamicInfoVoList = upDynamicInfoDto.getDynamicList().stream()
                    .map(dynDto ->
                            SingleDynamicInfoVo.builder()
                                    .dynamicId(String.valueOf(dynDto.getDynamicId()))
                                    .title(dynDto.getTitle())
                                    .content(dynDto.getContent())
                                    .build()
                    ).collect(Collectors.toList());
            upDynamicInfoVo.setDynamicList(singleDynamicInfoVoList);
        }
        return Response.SUCCESS(upDynamicInfoVo);
    }

    @ApiOperation(value = "查询动态基本信息")
    @RequestMapping(value = "/dynamic/detail", method = RequestMethod.GET)
    public Response<SingleDynamicInfoDto> queryUpDynamicByDynamicId(@RequestParam(value = "mid") Long mid, @RequestParam(value = "dynamic_id") Long dynamicId) {
        SingleDynamicInfoDto singleDynamicInfoDto = upService.queryDynamicByDynamicId(mid, dynamicId);
        return Response.SUCCESS(singleDynamicInfoDto);
    }

    @ApiOperation(value = "上传素材")
    @RequestMapping(value = "/brand_info/upload_face", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<ImageVo> creativeUpload(
            @RequestParam("file") MultipartFile multipartFile
    ) throws ServiceException, IllegalStateException, IOException {
        File convertFile = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        multipartFile.transferTo(convertFile);

        BfsFile bfsFile = BfsFile.builder().size(multipartFile.getSize())
                .convFile(convertFile).mimeType(multipartFile.getContentType())
                .fileName(multipartFile.getOriginalFilename()).bytes(multipartFile.getBytes())
                .inputStream(multipartFile.getInputStream()).build();

        validateImage(bfsFile, brandFaceWidth, brandFaceHeight, brandFaceMaxSizeKB, 0);

        BfsUploadResult result = bfsService.upload(categoryName, bfsFile.getConvFile());

        return Response.SUCCESS(buildImageVideoVo(result, brandFaceWidth, brandFaceHeight));
    }

    @ApiOperation(value = "上传二维码相关素材")
    @RequestMapping(value = "/upload_qr_info", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<QrUploadResultVo> uploadQrInfo(
            @RequestParam("file") MultipartFile multipartFile,
            @RequestParam("type") @ApiParam("上传素材类型，1：二维码边框 2：二维码动效") Integer qrInfoType) throws ServiceException, IllegalStateException, IOException {


        checkQrInfo(multipartFile, qrInfoType);

        BfsUploadResult result = bfsService.upload(categoryName, multipartFile.getOriginalFilename(),
                multipartFile.getBytes());

        return Response.SUCCESS(QrUploadResultVo.builder()
                .url(result.getUrl())
                .md5(result.getMd5())
                .build());
    }

    private void checkQrInfo(MultipartFile file, Integer qrInfoType) throws IOException {
        //校验二维码边框
        if (qrInfoType == 1) {
            Assert.isTrue(file.getSize() <= qrFrameMaxKbSize * 1024, String.format("二维码边框大小不能超过%dKB", qrFrameMaxKbSize));
            Assert.isTrue(file.getContentType() != null && file.getContentType().startsWith("image"), "不支持该图片类型");
            try (InputStream inputStream = file.getInputStream()) {
                BufferedImage bufferedImage = ImageIO.read(inputStream);
                Assert.isTrue(bufferedImage.getWidth() == qrFrameWidth, String.format("二维码边框宽度必须为%dpx", qrFrameWidth));
                Assert.isTrue(bufferedImage.getHeight() == qrFrameHeight, String.format("二维码边框高度必须为%dpx",
                        qrFrameHeight));
            }
        }

        //校验二维码动效
        if (qrInfoType == 2) {
            Assert.isTrue(file.getSize() <= qrAnimationMaxKbSize * 1024, String.format("二维码动效大小不能超过%dKB",
                    qrAnimationMaxKbSize));
            JSONObject json;
            try {
                json = JSON.parseObject(file.getBytes(), JSONObject.class);
            } catch (Exception e) {
                throw new RuntimeException("二维码动效不是json格式");
            }
            List<String> lottieKeyWord = Arrays.asList("v", "fr", "ip", "op");
            Assert.isTrue(lottieKeyWord.stream().allMatch(keyWord -> json.get(keyWord) != null), "该文件不为动效文件，请重新选择");
        }
    }

    @ApiOperation(value = "查询角标列表")
    @RequestMapping(value = "/cm_marks", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<CmMarkVo>> queryMarkList(
            @ApiIgnore Context context,
            @RequestParam(name = "schedule_ids") List<Integer> scheduleIds
    ) throws ServiceException {
        ScheduleDto schedule = null;
        ResSlotGroupBaseDto slotGroup = null;
        if (!CollectionUtils.isEmpty(scheduleIds)) {
            schedule = queryScheduleService.getScheduleById(scheduleIds.get(0));
            if (SalesType.CPT.getCode() != schedule.getSalesType() && Utils.isPositive(schedule.getSlotGroupId())) {
                slotGroup = resSlotGroupService.getGroupById(schedule.getSlotGroupId());
            }
        }
        boolean isMobile = slotGroup != null && LaunchConstant.MOB_CHANNEL_ID.equals(slotGroup.getChannelId());
        Map<Integer, CmMarkDto> cmMarkMap = cmMarkService.getCmMarkMapByAccountId(context.getAccountId(),
                schedule == null ? 0 : schedule.getSalesType());
        List<CmMarkVo> cmMarkVos;
        if (CollectionUtils.isEmpty(cmMarkMap)) {
            return Response.SUCCESS();
        }

        cmMarkVos = cmMarkMap.values().stream()
                .filter(m -> isMobile || !MOBILE_CM_MARKS.contains(m.getId()))
                .map(m -> CmMarkVo.builder().id(m.getId()).name(m.getName())
                        .mark_type(m.getMarkType()).build())
                .collect(Collectors.toList());
        log.info("getCmMarkMapByAccountId res[{}]", cmMarkMap);

        //OGV 无标
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********003038608
        if (Objects.nonNull(schedule) &&
                Objects.equals(PromotionPurposeType.OGV_STANDARD.getCode(), schedule.getPromotionPurposeType())) {
            //新版
            cmMarkVos = Lists.newArrayList(CmMarkVo.builder().id(9).mark_type(1).name("不展示标").build());
            return Response.SUCCESS(cmMarkVos);
        }

        Integer product = orderService.getOrderById(schedule.getOrderId()).getProduct();

        List<CmMarkVo> finalCmMarkVos = cmMarkVos;
        Function<Collection<Integer>, List<CmMarkVo>> filter = marks -> finalCmMarkVos.stream()
                .filter(t -> marks.contains(t.getId()))
                .collect(Collectors.toList());

        if (configCenter.getGdPlusConfig().getDefaultBusMarkIdTemplates().contains(schedule.getTemplateId())) {
            int defaultCmMarkId = 1;
            cmMarkVos = cmMarkVos.stream()
                    .filter(cmMark -> cmMark.getId().equals(defaultCmMarkId))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        if (SsaConstants.SSA_PLUS_ORDER_PRODUCT.contains(product)) {
            cmMarkVos = cmMarkVos.stream().filter(t -> ssaCmMarks.contains(t.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        ResourceType resourceType = brandTemplateHelper.getResourceType(schedule.getTemplateId());

        if (Objects.equals(resourceType, ResourceType.TV_CARD)) {
            if (PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode()
                    .equals(schedule.getPromotionPurposeType())) {
                cmMarkVos = cmMarkVos.stream().filter(t -> ottGdManuscriptCmMarks.contains(t.getId()))
                        .collect(Collectors.toList());
            } else if (PromotionPurposeType.OGV.getCode()
                    .equals(schedule.getPromotionPurposeType())) {
                cmMarkVos = cmMarkVos.stream().filter(t -> ottGdOGVCmMarks.contains(t.getId()))
                        .collect(Collectors.toList());
            } else {
                cmMarkVos = cmMarkVos.stream().filter(t -> ottGdLocalCmMarks.contains(t.getId()))
                        .collect(Collectors.toList());
            }
            return Response.SUCCESS(cmMarkVos);
        }


        if (SalesType.BRAND_AFTER_PAY_GD_PLUS.getCode() == schedule.getSalesType()) {
            //gd+只支持新版的广告标
            cmMarkVos = cmMarkVos.stream().filter(t ->
                            MarkType.BUS_MARK.getCode().equals(t.getMark_type()))
                    .collect(Collectors.toList());
        }

        boolean isFlyOrder = orderExtService.isFlyGdOrder(schedule.getOrderId());
        if (OrderProduct.FLY_GD.getCode().equals(product) ||
                GdType.FLY.getCode().equals(schedule.getGdType())
                || isFlyOrder) {
            cmMarkVos = cmMarkVos.stream().filter(t -> flyBusMarks.contains(t.getId()))
                    .collect(Collectors.toList());
            int rocketMarkIndex = -1;
            for (int i = 0; i < cmMarkVos.size(); i++) {
                if (cmMarkVos.get(i).getId().equals(123)) {
                    rocketMarkIndex = i;
                    break;
                }
            }
            if (rocketMarkIndex > 0) {
                //如果包含火箭标，则将火箭标置为首位
                Collections.swap(cmMarkVos, 0, rocketMarkIndex);
            }
            return Response.SUCCESS(cmMarkVos);
        }


        if (Objects.equals(resourceType, ResourceType.STORY_CARD)) {
            cmMarkVos = cmMarkVos.stream().filter(t ->
                            configCenter.getGdPlusConfig().getStoryGdMarkId().contains(t.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        List<Integer> hotBusMarkIds = systemConfigService.getValueReturnListInt(SystemConfigEnum
                .HOT_CPT_BUS_MARK_IDS.getCode());
        if (hotCptSourceIds.contains(schedule.getSlotId())) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(t -> hotBusMarkIds.contains(t.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        List<Integer> selectedBudMarkIds = systemConfigService.getValueReturnListInt(SystemConfigEnum
                .SELECTED_CPT_BUS_MARK_IDS.getCode());
        if (selectedCptSourceIds.contains(schedule.getSlotId())) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(t -> selectedBudMarkIds.contains(t.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        if (OrderProduct.SEARCH_CPT.getCode().equals(schedule.getOrderProduct())) {
            List<CmMarkVo> marks = filter.apply(searchCptCmMarks);
            if (configCenter.getSearchCptConfig().getSearchCptFlyTemplateIds().contains(schedule.getTemplateId())) {
                marks.addAll(filter.apply(flyBusMarks));
            }

            int rocketMarkIndex = -1;
            for (int i = 0; i < marks.size(); i++) {
                if (marks.get(i).getId().equals(123)) {
                    rocketMarkIndex = i;
                    break;
                }
            }
            if (rocketMarkIndex > 0) {
                //如果包含火箭标，则将火箭标置为首位
                Collections.swap(marks, 0, rocketMarkIndex);
            }
            return Response.SUCCESS(marks);
        }

        if (SalesType.CPT.getCode() == schedule.getSalesType()) {
            int hotActivitySourceId = 5625;
            Set<Integer> commentYellowSourceIdSet = Sets.newHashSet(4140, 4134, 4165, 4159, 4156, 4168, 4138, 4132, 4162, 4180,
                    4764, 4177, 4766, 4170);
            Set<Integer> dynamicFansSourceIdSet = Sets.newHashSet(3163, 3171);
            if (configCenter.getIntegratedMarketingConfig().isIntegratedMarketing(schedule.getSlotId())
                    || schedule.getSlotId() == hotActivitySourceId) {
                cmMarkVos = filter.apply(cptTopicCmMarks);
            } else if (commentYellowSourceIdSet.contains(schedule.getSlotId())) {
                //评论区小黄条无标
                cmMarkVos = filter.apply(cptCommentYellowCmMarks);
            } else if (dynamicFansSourceIdSet.contains(schedule.getSlotId())) {
                cmMarkVos = filter.apply(dynamicFanCmMarks);
            } else if (Constant.PLAYER_DETAIL_PLATFORM_SOURCE_MAP.containsValue(schedule.getSlotId())) {
                //【品牌】相关推荐新增投放方案 - cpt
                // https://www.tapd.cn/********/prong/stories/view/11********004251131
                cmMarkVos = filter.apply(this.configCenter.getCptConfig().getPlayerDetailCmMarks());
                //如果是稿件则返回火箭标和无标，原生稿件互导需要支持无标
                Integer launchScene = schedule.getLaunchScene();
                if (Objects.equals(schedule.getPromotionPurposeType(), PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode())) {
                    //由于测试环境和生产环境的火箭标id不一样（测试：105，生产：123），因此统一使用文字过滤
                    cmMarkVos = cmMarkVos.stream()
                            .filter(mark -> mark.getName().contains("火箭标")
                                    || (ScheduleLaunchSceneEnum.isArchiveMutualRecommend(launchScene) && Objects.equals(mark.getId(), 9)))
                            .collect(Collectors.toList());
                } else {
                    //屏蔽无标
                    cmMarkVos = cmMarkVos.stream()
                            .filter(mark -> !Objects.equals(mark.getId(), 9))
                            .collect(Collectors.toList());
                }
            } else if(Constant.LIVE_ROOM_RIGHT_DOWN_CARD_SOURCE_PLATFORM_MAP.containsKey(schedule.getSlotId())){
                cmMarkVos = filter.apply(this.configCenter.getCptConfig().getLiveRoomCmMarks());
            } else {
                cmMarkVos = filter.apply(cptCmMarks);
            }
        }

        if (Objects.equals(resourceType, ResourceType.UNDER_BOX_CARD)) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(markId -> configCenter.getGdPlusConfig().getUnderBoxBusMarkIds().contains(markId.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        //动态广告
        if (configCenter.getGdPlusConfig().getDynamicTemplates().contains(schedule.getTemplateId())) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(markId -> configCenter.getGdPlusConfig().getDynamicMarkIds().contains(markId.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        if (Objects.equals(resourceType, ResourceType.TV_PAUSED)) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(markId -> configCenter.getGdPlusConfig().getOttMarkIds().contains(markId.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        if (Objects.equals(resourceType, ResourceType.PLAYER_DETAIL)) {
            cmMarkVos = cmMarkVos.stream()
                    .filter(markId -> configCenter.getGdPlusConfig().getPlayDetailMarkIds().contains(markId.getId()))
                    .collect(Collectors.toList());
            return Response.SUCCESS(cmMarkVos);
        }

        return Response.SUCCESS(cmMarkVos);
    }

    @ApiOperation(value = "查询定向条件")
    @RequestMapping(value = "/target", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> queryTargetList(@RequestParam(name = "template_id", required = false, defaultValue = "0") Integer templateId,
                                     @RequestParam(name = "order_product", required = false, defaultValue = "0") Integer orderProduct,
                                     @RequestParam(name = "is_ssa_archive", required = false, defaultValue = "false") Boolean isSsaArchive,
                                     @RequestParam(name = "ssa_linkage_type", required = false, defaultValue = "0") Integer ssaLinkageTypeValue,
                                     @RequestParam(name = "season_id", required = false) List<Long> seasonIdList,
                                     @RequestParam(name = "interact_style", required = false, defaultValue = "0") Integer interactStyle,
                                     @RequestParam(name = "button_style", required = false, defaultValue = "0") Integer buttonStyle,
                                     @RequestParam(name = "order_id", required = false, defaultValue = "-1") Integer orderId) {

        Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService
                .getAllValidTarget2ItemTreeMap();

        processOs(orderProduct, target2ItemMap, isSsaArchive, ssaLinkageTypeValue, seasonIdList, interactStyle, buttonStyle);

        String gdTargetConfig;
        if (OrderProduct.OTT_GD.getCode().equals(orderProduct)) {
            gdTargetConfig = systemConfigService.getValueByItem(SystemConfig.OTT_GD_TARGET_CONFIG);
        } else {
            gdTargetConfig = systemConfigService.getValueByItem(SystemConfig.GD_TARGET_CONFIG);
        }

        List<Integer> gdTargetIdList = JSONArray.parseArray(gdTargetConfig, Integer.class);

        if (CollectionUtils.isEmpty(target2ItemMap) || CollectionUtils.isEmpty(gdTargetIdList)) {
            return Response.SUCCESS(Collections.emptyMap());
        } else {
            if (templateId == null) {
                templateId = 0;
            }
            List<Integer> templateGroupIds;
            if (smallCardTemplateIds.contains(templateId)) {
                templateGroupIds = Lists.newArrayList(90);
            } else {
                //90是随机给的数字
                templateGroupIds = Lists.newArrayList(0);
            }

            ResTemplateAreaGroupMappingPoExample mappingPoExample = new ResTemplateAreaGroupMappingPoExample();
            mappingPoExample.or().andTemplateGroupIdIn(templateGroupIds)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<ResTemplateAreaGroupMappingPo> mappingPos = mappingDao.selectByExample(mappingPoExample);
            if (CollectionUtils.isEmpty(mappingPos)) {
                mappingPoExample.clear();
                mappingPoExample.or().andTemplateGroupIdIn(Lists.newArrayList(0))
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                mappingPos = mappingDao.selectByExample(mappingPoExample);
            }
            List<Integer> areaGroupIds = mappingPos.stream()
                    .map(ResTemplateAreaGroupMappingPo::getAreaGroupId)
                    .collect(Collectors.toList());

            //从地域分组获取地域树
            List<AreaTreeDto> areaGroupTree = resAreaGroupService.getAreaGroupTree(orderProduct)
                    .stream()
                    .filter(t -> areaGroupIds.contains(t.getId()))
                    .collect(Collectors.toList());


            // --https://www.tapd.cn/********/prong/stories/view/11********004525565
            // --【品牌】新增订单维度自定义城市组配置

            List<Integer> specialAreaGroupOrderWhiteList = systemConfigService.getValueReturnListInt(
                    SystemConfigEnum.SPECIAL_AREA_GROUP_ORDER_WHITE_LIST.getCode());

            boolean isSpecialAreaGroupOrder = specialAreaGroupOrderWhiteList.contains(orderId);


            Map<Integer, SimpleAreaGroupVo> finalAreaGroupOrder = isSpecialAreaGroupOrder ? SPECIAL_AREA_GROUP_ORDER : AREA_GROUP_ORDER;

            areaGroupTree = areaGroupTree.stream()
                    //过滤出普通 or 特殊 分组
                    .filter(areaTree -> finalAreaGroupOrder.containsKey(areaTree.getId()))
                    //如果不是SSA_PD，则需要排除SSA_PD可选城市
                    .filter(areaTree -> OrderProduct.SSA_PD.getCode().equals(orderProduct) || !ssaPdAreaGroupIdSet.contains(areaTree.getId()))
                    //如果不是PD，则需要排除PD可选城市
                    .filter(areaTree -> OrderProduct.PD.getCode().equals(orderProduct) || !pdAreaGroupIdSet.contains(areaTree.getId()))
                    .collect(Collectors.toList());


            Map<TargetType, Boolean> targetType2StatusMap = targetService.getTargetType2Status();
            Map<String, Object> targetMap = Maps.newHashMap();
            List<AreaTreeDto> finalAreaGroupTree = areaGroupTree;
            target2ItemMap.forEach((k, v) -> {
                if (TargetType.AREA.equals(k)) {

                    /*
                     * --https://www.tapd.cn/********/prong/stories/view/11********004525565
                     * --【品牌】新增订单维度自定义城市组配置
                     * 不同的地域组，可以具有相同的名字：底层是不同的地域组，那么其组名代表了对该组的独特解释，但在View层其实是同一个组，
                     * 比如「重点城市」，无论底层是何组「重点城市」，对客展示上都是一个「重点城市」。
                     * 如果在底表直接采用相同的名字，虽然View不需过多装饰，但其实对于底层处理或者问题排查容易引起混淆，顾只做View层转换即可
                     */

                    List<NameValuePair> nameValuePairs = ResourceConvert.areaTrees2NameValuePairs(finalAreaGroupTree)
                            .stream()
                            .peek(pair -> pair.setName(finalAreaGroupOrder.get(pair.getId()).getName()))
                            .collect(Collectors.toList());

                    targetMap.put(k.getByName(), nameValuePairs);
                } else {
                    targetMap.put(k.getByName(), v);
                }
            });

            Map<String, Boolean> targetTypeName2StatusMap = Maps.newHashMap();
            targetType2StatusMap.keySet().forEach(k -> targetTypeName2StatusMap
                    .put(k.getByName(), gdTargetIdList.contains(k.getCode())
                            && Boolean.TRUE.equals(targetType2StatusMap.get(k))));
            targetMap.put("target_status", targetTypeName2StatusMap);

            List<NameValuePair> areaPairs = (List<NameValuePair>) targetMap.get("area");
            areaPairs = areaPairs.stream().sorted(Comparator
                    .comparing(t -> finalAreaGroupOrder.get(t.getId()).getOrder())).collect(Collectors.toList());
            targetMap.put("area", areaPairs);

            processVideoPartition(targetMap, target2ItemMap, templateId);

            return Response.SUCCESS(targetMap);
        }
    }

    private void processOs(int orderProduct, Map<TargetType, List<TargetTreeDto>> target2ItemMap, Boolean isSsaArchive,
                           Integer ssaLinkageType, List<Long> seasonIdList, Integer interactStyle, Integer buttonStyle) {

        //ogv
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********003035247
        //https://www.tapd.cn/********/prong/stories/view/11********003091168
        if (OrderProduct.isOgv(orderProduct)) {
            if (CollectionUtils.isEmpty(seasonIdList)) {
                target2ItemMap.put(TargetType.OS, Collections.emptyList());
                return;
            }
            List<Integer> platformIdList = this.resOgvService.getCommonPlatform(seasonIdList);
            Assert.notEmpty(platformIdList, "所选season无公共可投放平台，请核实和修改后重试");
            List<TargetTreeDto> osTargetTrees = platformIdList.stream().map(pid -> {
                Optional<Map.Entry<Integer, Integer>> target = Constant.PLATFORM_MAP.entrySet()
                        .stream()
                        .filter(entry -> entry.getValue().equals(pid))
                        .findFirst();
                Assert.isTrue(target.isPresent(), pid + "平台不存在，请联系开发");
                Map.Entry<Integer, Integer> entry = target.get();
                return new TargetTreeDto(entry.getKey(), PlatformType.getByCode(pid).getDesc(), false, null);
            }).collect(Collectors.toList());
            target2ItemMap.put(TargetType.OS, osTargetTrees);
            return;
        } else {
            //非OGV的web，不走web定向
            Optional.ofNullable(target2ItemMap.get(TargetType.OS))
                    .ifPresent(osTargets -> osTargets.removeIf(t -> t.getId().equals(Constant.WEB_TARGET_ID)));
        }

        if (!OrderProduct.ALLOW_OS_LAUNCH.contains(orderProduct)) {
            target2ItemMap.remove(TargetType.OS);
        }

        if (orderProduct == OrderProduct.SSA_OTT_CPT.getCode()
                || orderProduct == OrderProduct.SSA_OTT_GD.getCode()
                || orderProduct == OrderProduct.SSA_OTT_PD.getCode()) {
            //为了不影响到三连，手动生成
            TargetTreeDto ottTarget = new TargetTreeDto(splashOttTargetId, "ott", false, null);
            target2ItemMap.put(TargetType.OS, Collections.singletonList(ottTarget));
        }

        //稿件闪屏、topview、闪屏默认搜索词场景没有ipad
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********002837726
        boolean isRemoveIpad = isSsaArchive
                || OrderProduct.TOPVIEW_CODE_SET.contains(orderProduct)
                || SsaLinkageType.isSearch(ssaLinkageType);
        List<TargetTreeDto> targetTrees = target2ItemMap.getOrDefault(TargetType.OS, Lists.newLinkedList());
        if (isRemoveIpad) {
            targetTrees.removeIf(target -> Constant.PAD_SET.contains(target.getId()));
        }

        // 没有安卓pad
        //
        //1、【品牌】闪屏样式拓展--品牌卡片闪屏
        //https://www.tapd.cn/********/prong/stories/view/11********004200276
        //2、ssa_pd

        boolean isRemoveAndroidPad = Objects.equals(OrderProduct.SSA_PD.getCode(), orderProduct)
                || SsaButtonStyle.isBrandCardButton(buttonStyle);
        if (isRemoveAndroidPad) {
            targetTrees.removeIf(target -> Constant.ANDROID_PAD_SET.contains(target.getId()));
        }
    }

    private void processVideoPartition(Map<String, Object> targetMap,
                                       Map<TargetType, List<TargetTreeDto>> target2ItemMap, Integer templateId) {

        //目前仅框下需要稿件内容分区定向
        targetMap.remove(TargetType.VIDEO_PARTITION.getByName());
        ResourceType resourceType = brandTemplateHelper.getResourceType(templateId);
        if (Objects.equals(resourceType, ResourceType.UNDER_BOX_CARD)) {
            List<TargetTreeDto> videoPartitionTargets = target2ItemMap.get(TargetType.VIDEO_PARTITION);
            if (CollectionUtils.isEmpty(videoPartitionTargets)) {
                return;
            }
            List<Integer> specifyTargets = systemConfigService.getValueReturnListInt(SystemConfig.GD_TARGET_VIDEO_PARTITION_CONFIG.name());
            if (CollectionUtils.isEmpty(specifyTargets)) {
                return;
            }

            HashSet<Integer> specifyTargetsSet = new HashSet<>(specifyTargets);

            List<TargetTreeDto> filteredTargets = videoPartitionTargets.stream()
                    .filter(target -> {
                        List<TargetTreeDto> validChild = target.getChildren().stream()
                                .filter(childTarget -> specifyTargetsSet.contains(childTarget.getId()))
                                .collect(Collectors.toList());
                        target.setChildren(validChild);

                        if (CollectionUtils.isEmpty(validChild)) {
                            return specifyTargetsSet.contains(target.getId());
                        } else {
                            return true;
                        }
                    })
                    .collect(Collectors.toList());

            targetMap.put(TargetType.VIDEO_PARTITION.getByName(), filteredTargets);
        }
    }

    @ApiOperation(value = "根据广告位ID查询模板")
    @RequestMapping(value = "/slot/{slot_id}/template", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<TemplateVo>> queryTemplateList(@ApiIgnore Context context, @PathVariable("slot_id") Integer slotId) {
        List<TemplateVo> templateVoList = getTemplateVoList(
                isSupportSchemeUrl(context.getAccountId(), null, null),
                Lists.newArrayList(slotId),
                null, PromotionPurposeType.LANDING_PAGE, 0);

        return Response.SUCCESS(templateVoList);
    }

    @ApiOperation(value = "根据排期ID列表查询模板")
    @RequestMapping(value = "/schedule/template", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<TemplateVo>> getTemplatesByScheduleId(
            @ApiIgnore Context context,
            @ApiParam("排期ID列表") @RequestParam("schedule_ids") String scheduleIds
    ) {

        List<Integer> scheduleIdList = Utils.stringToList(scheduleIds, Integer::parseInt);
        Assert.notEmpty(scheduleIdList, "排期ID不可为空");
        Map<Integer, ScheduleDto> scheduleMap = queryScheduleService.getScheduleMapInIds(scheduleIdList);
        Assert.notEmpty(scheduleMap, "排期不存在");

        //判断此账户是否支持唤起
        ScheduleDto scheduleDto = scheduleMap.values()
                .stream()
                .findFirst()
                .orElse(new ScheduleDto());
        boolean isSupportSchemeUrl = isSupportSchemeUrl(context.getAccountId(), scheduleDto.getOrderProduct(), Lists.newArrayList(scheduleDto.getTemplateId()));

        List<List<TemplateVo>> scheduleTemplateVoList = new ArrayList<>(scheduleMap.entrySet().size());
        for (Map.Entry<Integer, ScheduleDto> scheduleEntry : scheduleMap.entrySet()) {
            ScheduleDto schedule = scheduleEntry.getValue();
            // 0、广告位组 1、广告位
            List<Integer> sourceIdList = schedule.getSourceIdList();
            if (Objects.equals(schedule.getPutLocation(), PutLocation.SLOT_GROUP.getCode())) {
                sourceIdList = Lists.newArrayList(schedule.getSlotGroupId());
            } else {
                if (CollectionUtils.isEmpty(sourceIdList)) {
                    sourceIdList = Lists.newArrayList(schedule.getSlotId());
                }
            }
            List<TemplateVo> templateVos = getTemplateVoList(isSupportSchemeUrl, sourceIdList,
                    schedule.getPutLocation(),
                    PromotionPurposeType.getByCode(schedule.getPromotionPurposeType()),
                    schedule.getOrderProduct());
            if (!CollectionUtils.isEmpty(templateVos)) {
                templateVos = templateVos.stream()
                        .filter(t -> schedule.getTemplateId().equals(t.getId()))
                        .collect(Collectors.toList());
            }
            scheduleTemplateVoList.add(templateVos);
        }

        Map<Integer, TemplateVo> templateMap = new HashMap<>();
        List<Set<Integer>> scheduleTemplateIdList = new ArrayList<>();
        for (List<TemplateVo> templateList : scheduleTemplateVoList) {
            templateMap.putAll(templateList.stream().collect(Collectors.toMap(TemplateVo::getId, Function.identity())));
            scheduleTemplateIdList.add(templateList.stream().map(TemplateVo::getId).collect(Collectors.toSet()));
        }
        // 每个排期对应的模板列表取交集
        Set<Integer> intersectionTemplateIdList = scheduleTemplateIdList.stream()
                .reduce((p, q) -> {
                    p.retainAll(q);
                    return p;
                }).orElse(Collections.emptySet());
        List<TemplateVo> templateList = intersectionTemplateIdList.stream()
                .map(templateMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        GdOrderDto gdOrderDto = orderService.getOrderById(scheduleMap.get(scheduleIdList.get(0)).getOrderId(),
                super.getOperator(context));
        if (OrderProduct.OTT_GD.getCode().equals(gdOrderDto.getProduct())) {
            List<Integer> ottInlineTemplateList = brandTemplateHelper.getTemplateList(ResourceType.TV_CARD);
            templateList = templateList.stream()
                    .filter(t -> ottInlineTemplateList.contains(t.getId()))
                    .peek(t -> t.set_support_scheme_url(false))
                    .collect(Collectors.toList());
        }

        postProcessTemplates(templateList);

        return Response.SUCCESS(templateList);
    }

    @ApiOperation(value = "根据排期ID查询模板")
    @RequestMapping(value = "/schedule/{schedule_id}/template", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<TemplateVo>> getTemplatesByScheduleId(@ApiIgnore Context context,
                                                        @PathVariable("schedule_id") Integer scheduleId
    ) throws ServiceException {

        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(scheduleId);

        Integer putLocation = scheduleDto.getPutLocation();
        int sourceOrSlotGroupId;
        if (putLocation != null && putLocation == 0) {
            sourceOrSlotGroupId = scheduleDto.getSlotGroupId();
        } else {
            sourceOrSlotGroupId = scheduleDto.getSlotId();
        }

        List<TemplateVo> templateVoList = getTemplateVoList(
                isSupportSchemeUrl(context.getAccountId(), scheduleDto.getOrderProduct(), Lists.newArrayList(scheduleDto.getTemplateId())),
                Lists.newArrayList(sourceOrSlotGroupId), putLocation,
                PromotionPurposeType.getByCode(scheduleDto.getPromotionPurposeType()), scheduleDto.getOrderProduct()
        );

        GdOrderDto gdOrderDto = orderService.getOrderById(scheduleDto.getOrderId(), super.getOperator(context));
        if (OrderProduct.OTT_GD.getCode().equals(gdOrderDto.getProduct())) {
            List<Integer> ottInlineTemplateList = brandTemplateHelper.getTemplateList(ResourceType.TV_CARD);
            templateVoList = templateVoList.stream()
                    .filter(t -> ottInlineTemplateList.contains(t.getId()))
                    .peek(t -> t.set_support_scheme_url(false))
                    .collect(Collectors.toList());
        }

        return Response.SUCCESS(templateVoList);
    }

    private Boolean isSupportSchemeUrl(Integer accountId, Integer orderProduct, List<Integer> templateIdList) {
//        List<Integer> ottTemplates = configCenter.getGdPlusConfig().getOttTemplates();
        List<Integer> ottTemplates = brandTemplateHelper.getTemplateList(ResourceType.TV_PAUSED);
        if (!CollectionUtils.isEmpty(templateIdList)
                && CollectionUtils.containsAny(ottTemplates, templateIdList)) {
            return false;
        }
        List<Integer> supportCardRecallAccountIds = creativeValidator.getSupportCardRecallAccountIds();
        return !OrderProduct.NO_SCHEMA_PROUDCT.contains(orderProduct)
                && supportCardRecallAccountIds.contains(accountId);
    }

    private List<TemplateVo> getTemplateVoList(boolean isSupportSchemeUrl, List<Integer> sourceOrSlotGroupId,
                                               Integer type, PromotionPurposeType promotionPurposeType,
                                               Integer orderProduct) {
        Map<Integer, TemplateDto> templateDtoMap;

        if (type != null && type == 0) {
            List<StylePlatformGroupDto> stylePlatformGroupDtos = resSlotGroupService.getGdStylePlatformBySlotGroupId(
                    sourceOrSlotGroupId.get(0));

            if (CollectionUtils.isEmpty(stylePlatformGroupDtos)) {
                return Collections.emptyList();
            }

            List<Integer> templateIds = stylePlatformGroupDtos
                    .stream()
                    .map(StylePlatformGroupDto::getTemplateId)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapInIds(templateIds);
            templateDtoMap = CollectionUtils.isEmpty(templateMap) ? Collections.emptyMap() : templateMap;
        } else {
            List<SourceAllInfoDto> sourceAllInfoDtos = querySourceService.getSourcesInSourceIds(sourceOrSlotGroupId);
            templateDtoMap = Optional.ofNullable(sourceAllInfoDtos).orElse(Collections.emptyList())
                    .stream()
                    .map(SourceAllInfoDto::getTemplateDtos)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(TemplateDto::getTemplateId, Function.identity(), (s, t) -> t));
        }
        return templateDtoMapToVo(templateDtoMap, promotionPurposeType, isSupportSchemeUrl, orderProduct);
    }

    private List<TemplateVo> templateDtoMapToVo(Map<Integer, TemplateDto> templateDtoMap,
                                                PromotionPurposeType promotionPurposeType,
                                                Boolean isSupportSchemeUrl,
                                                Integer orderProduct) {

        List<Integer> cardTypeIds = CollectionUtils.isEmpty(templateDtoMap) ?
                Collections.emptyList() : templateDtoMap.values().stream()
                .map(TemplateDto::getCardType)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, List<CreativeStyle>> creativeStyleMap = CollectionUtils.isEmpty(cardTypeIds) ?
                Collections.emptyMap() : cardTypeIds.stream()
                .collect(Collectors.toMap(Function.identity(),
                        cardTypeId -> cardTypeService.getSupportStyle(cardTypeId)));

        List<TemplateVo> templateVoList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(templateDtoMap)) {
            templateVoList = new ArrayList<>(templateDtoMap.size());
            List<Integer> templateWhitelistIds = getGdTemplateWhitelist(OrderProduct.getByCode(orderProduct)
                    .getGdTemplateConfig());
            // 过滤白名单外的模板ID
            for (Integer templateWhitelistId : templateWhitelistIds) {

                TemplateDto template = templateDtoMap.get(templateWhitelistId);

                if (template == null) {
                    //GD先不支持组图
                    continue;
                }

                boolean isSelectiveCard = selectiveTemplateIds.contains(template.getTemplateId());
                boolean isDynamicTemplate = dynamicTemplateIds.contains(template.getTemplateId());
                boolean isLiveBookingTemplate = configCenter.getLiveConfig().isSupportsBookingTemplate(template.getTemplateId());
                boolean isLiveLivingTemplate = configCenter.getLiveConfig().isSupportsLivingTemplate(template.getTemplateId());
                boolean isSupportOgv = ogvTemplateIds.contains(template.getTemplateId());

                boolean isSupportOriginalPage = originalPageTemplateIds.contains(template.getTemplateId());
                ShowPriorityType showPriority = configCenter.getLiveConfig().getShowPriority(template.getTemplateId());
                boolean isSupportPortrait = supportPortraitTemplateIds.contains(template.getTemplateId());
                Pair<Integer, Integer> extraImage = templateMaterialService.getTemplateExtraImage(template.getTemplateId());
                templateVoList.add(TemplateVo.builder()
                        .id(template.getTemplateId())
                        .name(template.getTemplateName())
                        ._fill_title(template.getIsFillTitle())
                        .title_max_length(template.getTitleMaxLength())
                        .title_min_length(template.getTitleMinLength())
                        ._fill_desc(template.getIsFillDesc())
                        .desc_max_length(template.getDescMaxLength())
                        .desc_min_length(template.getDescMinLength())
                        ._support_image(template.getIsSupportImage())
                        .image_width(template.getImageWidth())
                        .image_height(template.getImageHeight())
                        .image_kb_limit(template.getImageKbLimit())
                        ._support_ext_image(template.getIsSupportExtImage())
                        .ext_image_width(template.getExtImageWidth())
                        .ext_image_height(template.getExtImageHeight())
                        .ext_image_kb_limit(template.getExtImageKbLimit())
                        ._support_video(!OrderProduct.FLY_GD.getCode().equals(orderProduct)
                                && template.getIsSupportVideo())
                        .video_width(template.getVideoWidth())
                        .video_height(template.getVideoHeight())
                        .video_kb_limit(template.getVideoKbLimit())
                        .video_duration_max(template.getVideoDurationMax())
                        .video_duration_min(template.getVideoDurationMin())
                        ._fill_ext_desc(template.getIsFillExtDesc())
                        .ext_desc_min_length(template.getExtDescMinLength())
                        .ext_desc_max_length(template.getExtDescMaxLength())
                        ._support_video_id(template.getIsSupportVideoId())
                        ._support_button(template.getIsSupportButton())
                        .button_copy_min_length(template.getButtonCopyMinLength())
                        .button_copy_max_length(template.getButtonCopyMaxLength())
                        .support_image_num(template.getImageNum())
                        .button_copys(buttonCopyDtos2Vos(template.getButtonCopyDtos(), promotionPurposeType))
                        .card_type(template.getCardType())
                        ._support_scheme_url(isSupportSchemeUrl)
                        ._support_extra169_image(!CollectionUtils.isEmpty(supportImageTemplateCardType) && supportImageTemplateCardType.contains(template.getCardType()))
                        .video_aspect_ratio(SizeTypeEnum.getSizeTypeEnum(template.getVideoWidth(), template.getVideoHeight()).getDesc())
                        .support_styles(getStyles(creativeStyleMap, template))
                        ._support_animation(template.getIsSupportAnimation())
                        .animation_kb_Limit(template.getAnimationKbLimit())
                        .animation_loop(template.getAnimationLoop())
                        .animation_max_duration(template.getAnimationMaxDuration())
                        .is_selectivity_card(isSelectiveCard)
                        .is_top_flow(Objects.equals(ShowPriorityType.BUY_OUT_AND_SHOW_BY_LIVE_STATUS, showPriority))
                        .image_vos(ResourceConvert.convert2ImageVoS(template.getTemplateImages(),
                                template, isSelectiveCard))
                        .support_video_id_nums(template.getSupportVideoIdNums())
                        .version(template.getVersion())
                        .show_priority(showPriority.getCode())
                        .support_head_portrait(isSupportPortrait)
                        .is_dynamic_template(isDynamicTemplate)
                        .is_support_live_order(isLiveBookingTemplate)
                        .is_support_live_living(isLiveLivingTemplate)
                        .support_ogv(isSupportOgv)
                        .support_purposes(templateMaterialService.getTemplatePurposes(template.getTemplateId()))
                        .support_original_jump_url(isSupportOriginalPage)
                        .egg_video_height(eggVideoHeight)
                        .egg_video_width(eggVideoWidth)
                        .extra_video_width(extraImage.getFirst())
                        .extra_video_height(extraImage.getSecond())
                        .build());
            }
        }
        return templateVoList;
    }

    private List<TemplateVo> searchTemplateDtoMapToVo(Map<Integer, TemplateDto> templateDtoMap,
                                                      Boolean isSupportSchemeUrl) {

        List<Integer> cardTypeIds = CollectionUtils.isEmpty(templateDtoMap) ?
                Collections.emptyList() : templateDtoMap.values().stream()
                .map(TemplateDto::getCardType)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, List<CreativeStyle>> creativeStyleMap = CollectionUtils.isEmpty(cardTypeIds) ?
                Collections.emptyMap() : cardTypeIds.stream()
                .collect(Collectors.toMap(Function.identity(),
                        cardTypeId -> cardTypeService.getSupportStyle(cardTypeId)));

        List<TemplateVo> templateVoList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(templateDtoMap)) {
            templateVoList = new ArrayList<>(templateDtoMap.size());
            // 过滤白名单外的模板ID
            for (Integer templateWhitelistId : getSearchTemplateWhitelist()) {

                TemplateDto template = templateDtoMap.get(templateWhitelistId);

                if (template == null) {
                    //GD先不支持组图
                    continue;
                }

                List<LocTemplateButtonBo> buttonBos = template.getTemplateButtons();
                List<ButtonVo> buttonVos = new ArrayList<>();
                if (!CollectionUtils.isEmpty(buttonBos)) {
                    //此逻辑只用于搜索品专卡,搜索品专卡有四个扩展运营位按钮都是文案自定义类型的按钮
                    buttonBos.stream().sorted(Comparator.comparing(LocTemplateButtonBo::getSeq))
                            .forEach(t -> buttonVos.add(ButtonVo.builder().seq(t.getSeq())
                                    .button_style(ButtonStyle.CUSTOMIZE.getCode())
                                    .button_copy_vos(t.getButtonTexts().stream()
                                            .map(x -> ButtonCopyVo.builder().type(x.getType()).name(x.getContent()).id(x.getId()).build())
                                            .collect(Collectors.toList())).build()));
                    ButtonVo buttonVo = buttonVos.get(0);
                    buttonVo.setButton_style(ButtonStyle.SELECT.getCode());
                }
                boolean isSelectiveCard = selectiveTemplateIds.contains(template.getTemplateId());
                templateVoList.add(TemplateVo.builder()
                        .id(template.getTemplateId())
                        .name(template.getTemplateName())
                        ._fill_title(template.getIsFillTitle())
                        .title_max_length(template.getTitleMaxLength())
                        .title_min_length(template.getTitleMinLength())
                        ._fill_desc(template.getIsFillDesc())
                        .desc_max_length(template.getDescMaxLength())
                        .desc_min_length(template.getDescMinLength())
                        ._support_image(template.getIsSupportImage())
                        .image_width(template.getImageWidth())
                        .image_height(template.getImageHeight())
                        .image_kb_limit(template.getImageKbLimit())
                        ._support_ext_image(template.getIsSupportExtImage())
                        .ext_image_width(template.getExtImageWidth())
                        .ext_image_height(template.getExtImageHeight())
                        .ext_image_kb_limit(template.getExtImageKbLimit())
                        ._support_video(template.getIsSupportVideo())
                        .video_width(template.getVideoWidth())
                        .video_height(template.getVideoHeight())
                        .video_kb_limit(template.getVideoKbLimit())
                        .video_duration_max(template.getVideoDurationMax())
                        .video_duration_min(template.getVideoDurationMin())
                        ._fill_ext_desc(template.getIsFillExtDesc())
                        .ext_desc_min_length(template.getExtDescMinLength())
                        .ext_desc_max_length(template.getExtDescMaxLength())
                        ._support_video_id(template.getIsSupportVideoId())
                        ._support_button(template.getIsSupportButton())
                        .button_copy_min_length(template.getButtonCopyMinLength())
                        .button_copy_max_length(template.getButtonCopyMaxLength())
                        .support_image_num(template.getImageNum())
                        .button_copys(buttonCopyDtos2Vos(template.getButtonCopyDtos(), null))
                        .card_type(template.getCardType())
                        ._support_scheme_url(isSupportSchemeUrl)
                        ._support_extra169_image(!CollectionUtils.isEmpty(supportImageTemplateCardType) && supportImageTemplateCardType.contains(template.getCardType()))
                        .video_aspect_ratio(SizeTypeEnum.getSizeTypeEnum(template.getVideoWidth(), template.getVideoHeight()).getDesc())
                        .support_styles(getStyles(creativeStyleMap, template))
                        ._support_animation(template.getIsSupportAnimation())
                        .animation_kb_Limit(template.getAnimationKbLimit())
                        .animation_loop(template.getAnimationLoop())
                        .animation_max_duration(template.getAnimationMaxDuration())
                        .is_selectivity_card(isSelectiveCard)
                        .image_vos(ResourceConvert.convert2ImageVoS(template.getTemplateImages(),
                                template, isSelectiveCard))
                        .support_video_id_nums(template.getSupportVideoIdNums())
                        .version(template.getVersion())
                        .button_vos(buttonVos)
                        .launch_subject(getLaunchSubject(template.getTemplateId()))
                        .is_support_enterprise_space(configCenter.getGdPlusConfig().getSupportEnterpriseSpaceTemplateIds().contains(template.getTemplateId()))
                        .support_enterprise_space(configCenter.getGdPlusConfig().getSupportEnterpriseSpaceMode(template.getTemplateId()))
                        .support_purposes(templateMaterialService.getTemplatePurposes(template.getTemplateId()))
                        .is_support_ssa_search_keyword(configCenter.getSearchCptConfig().isSearchCptLinkedSsaTemplate(template.getTemplateId()))
                        .build());
            }
        }
        return templateVoList;
    }

    private List<IdNameVo> getStyles(Map<Integer, List<CreativeStyle>> creativeStyleMap, TemplateDto template) {
        return creativeStyleMap.getOrDefault(template.getCardType(), Collections.emptyList())
                .stream()
                .filter(s -> {
                    if (s == CreativeStyle.VIDEO && (template.getIsSupportVideo() || template.getIsSupportVideoId())) {
                        return true;
                    }
                    return s.matchTemplate(template);
                })
                .map(s -> IdNameVo.builder()
                        .id(s.getCode().longValue())
                        .name(s.getName())
                        .build()).collect(Collectors.toList());
    }

    /**
     * 获取对应模板投放主体类型
     */
    private int getLaunchSubject(int templateId) {
        int blueVType = 1;
        int customCreativeType = 2;
        if (configCenter.getSearchCptConfig().getInlineVideoBlueVTemplateIds().contains(templateId)) {
            return blueVType;
        }
        if (configCenter.getSearchCptConfig().getInlineVideoCustomTemplateIds().contains(templateId)) {
            return customCreativeType;
        }
        return 0;
    }

    /**
     * 根据搜索广告位过滤模板信息
     */
    private List<Integer> filterTemplate(List<Integer> templateIds, List<Integer> slotIds) {
        if (slotIds == null || slotIds.stream().anyMatch(slot -> !inlineVideoSlotIds.contains(slot))) {
            templateIds = templateIds.stream()
                    .filter(templateId -> !inlineVideoTemplateIds.contains(templateId))
                    .collect(Collectors.toList());
        }
        return templateIds;
    }

    private List<Integer> getGdTemplateWhitelist(String item) {
        String templateWhitelist = systemConfigService.getValue(item);
        return JSONArray.parseArray(templateWhitelist, Integer.class);
    }

    private List<Integer> getSearchTemplateWhitelist() {
        String templateWhitelist = systemConfigService.getValueByItem(SystemConfig.SEARCH_CPT_TEMPLATE_WHITE_LIST);
        return JSONArray.parseArray(templateWhitelist, Integer.class);
    }

    @RequestMapping(value = "/channels", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询所有可选频道")
    public Response<List<ChannelVo>> queryChannelList(@ApiIgnore Context context) {
        List<Channel> channelList = channelService.getInnerCpmChannels();
        List<ChannelVo> channelVoList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(channelList)) {
            channelVoList = new ArrayList<>(channelList.size());
            List<SlotGroupDto> slotGroupDtoList = resSlotGroupService.getAllValidGroup();

            List<Integer> accountGroupIds = accountGroupMappingService
                    .getAccountGroupIdsByAccountId(context.getAccountId());
            List<Integer> settedSlotGroupIds = accountSlotGroupService.getAllSettedSlotGroupIds();
            if (CollectionUtils.isEmpty(accountGroupIds)) {
                slotGroupDtoList = slotGroupDtoList.stream().filter(dto ->
                        !settedSlotGroupIds.contains(dto.getId())).collect(Collectors.toList());
            } else {
                List<Integer> slotGroupIdsByAccountGroups = accountSlotGroupService
                        .getSlotGroupIdsInAccountGroupIds(Lists.newArrayList(accountGroupIds));
                slotGroupDtoList = slotGroupDtoList.stream().filter(dto -> slotGroupIdsByAccountGroups
                        .contains(dto.getId()) || !settedSlotGroupIds.contains(dto.getId())).collect(Collectors.toList());
            }

            List<NameValuePair> slotGroupVoList;
            Map<Integer, NameValuePair> haveTemplateSlotMap = getValidSlotMap(slotGroupDtoList);
            Map<Integer, List<SlotGroupDto>> slotGroupDtoMap = slotGroupDtoList.stream()
                    .collect(Collectors.groupingBy(SlotGroupDto::getChannelId));
            for (Channel channel : channelList) {
                slotGroupVoList = buildSlotGroupVoList(haveTemplateSlotMap, slotGroupDtoMap, channel);
                buildChannelVoList(channelVoList, slotGroupVoList, channel);
            }
        }
        return Response.SUCCESS(channelVoList);
    }


    private Map<Integer, NameValuePair> getValidSlotMap(List<SlotGroupDto> slotGroupDtoList) {
        List<TemplateDto> templateList = queryTemplateService.getAllValid();

        List<Integer> templateIds = templateList.stream().map(TemplateDto::getTemplateId).collect(Collectors.toList());
        Set<Integer> slotIdSet = Sets.newHashSet();
        slotGroupDtoList.forEach(dto -> slotIdSet.addAll(dto.getSlotIds()));
        List<Slot> slotDtos = slotService.getSlotList(slotIdSet);
        Map<Integer, NameValuePair> haveTemplateSlotMap = Maps.newHashMap();
        for (Slot dto : slotDtos) {
            List<Integer> tempIds = dto.getTemplateIds();
            if (!CollectionUtils.isEmpty(tempIds) && SwitchStatus.getByCode(dto.getStatus()) == SwitchStatus.STARTED) {
                tempIds.retainAll(templateIds);
                if (!CollectionUtils.isEmpty(tempIds)) {
                    haveTemplateSlotMap.put(dto.getSlotId(), NameValuePair.builder().id(dto.getSlotId()).name(dto.getSlotName()).build());
                }
            }
        }
        return haveTemplateSlotMap;
    }

    @RequestMapping(value = "/schedule/feeds", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询频道下可选广告位组")
    public Response<List<FeedsSlotGroupVo>> getFeedsSlotGroupList(@ApiIgnore Context context) {
        List<Integer> slotIds;
        List<SlotGroupDto> slotGroups = Lists.newArrayList();

        List<Integer> slotGroupIds = accountSlotGroupService.getAllSettedSlotGroupIds();
        List<SlotGroupDto> noMappedSLotGroups = resSlotGroupService.querySlotGroupDto(SlotGroupQuery
                .builder()
                .notInIdList(slotGroupIds)
                .saleTypes(Collections.singletonList(SalesType.GD.getCode()))
                .build());

        if (!CollectionUtils.isEmpty(noMappedSLotGroups)) {
            slotGroups.addAll(noMappedSLotGroups);
        }

        List<Integer> accountGroupIds = accountGroupMappingService.getAccountGroupIdsByAccountId(context.getAccountId());

        if (!CollectionUtils.isEmpty(accountGroupIds)) {
            List<Integer> slotGroupIdsByAccountGroups = accountSlotGroupService.getSlotGroupIdsInAccountGroupIds(accountGroupIds);

            if (!CollectionUtils.isEmpty(slotGroupIdsByAccountGroups)) {
                List<SlotGroupDto> mappedSlotGroups = resSlotGroupService.querySlotGroupDto(SlotGroupQuery
                        .builder()
                        .idList(slotGroupIdsByAccountGroups)
                        .saleTypes(Collections.singletonList(SalesType.GD.getCode()))
                        .build());

                if (!CollectionUtils.isEmpty(mappedSlotGroups)) {
                    slotGroups.addAll(mappedSlotGroups);
                }
            }
        }
        Map<Integer, NameValuePair> slotMap = getValidSlotMap(slotGroups);
        List<FeedsSlotGroupVo> resultSlotGroup = new ArrayList<>(slotGroups.size());

        for (SlotGroupDto slotGroup : slotGroups) {
            slotIds = slotGroup.getSlotIds();

            slotIds.retainAll(slotMap.keySet());

            if (!CollectionUtils.isEmpty(slotIds)) {
                resultSlotGroup.add(FeedsSlotGroupVo.builder()
                        .id(slotGroup.getId())
                        .name(slotGroup.getSlotGroupName())
                        .build());
            }
        }

        return Response.SUCCESS(resultSlotGroup);
    }

    @RequestMapping(value = "/template/platforms", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询模版下的平台")
    public Response<List<TemplateVo>> getTemplatePlatforms(@ApiIgnore Context context,
                                                           @RequestParam(value = "order_product", required = false, defaultValue = "3") Integer orderProduct,
                                                           @RequestParam(value = "order_id") Integer orderId,
                                                           @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType) {

        List<StyleTreeDto> treeDtos = resSlotGroupService.getGdStyleTree(context.getAccountId());
        log.info("getTemplatePlatforms getGdStyleTree by" + context.getAccountId() + " res:" + treeDtos);

        GdOrderDto orderDto = orderService.getOrderById(orderId);
        OrderProduct product = OrderProduct.getByCode(orderDto.getProduct());
        List<Integer> templateWhitelistIds = getGdTemplateWhitelist(product.getGdTemplateConfig());
        if (Objects.equals(product, OrderProduct.PD)) {
            List<Integer> accounts = this.systemConfigService.getValueReturnListInt(
                    SystemConfigEnum.PD_TWIST_TEMPLATE_CONTRACT_ACCOUNT_ID.getCode());
            if (accounts.stream().noneMatch(a -> a.equals(orderDto.getContractAccountId()))) {
                // https://www.tapd.bilibili.co/********/prong/stories/view/11********003056046
                // 非白名单客户，不展示462模板
                templateWhitelistIds.removeIf(t -> t.equals(462));
            }
        }

        log.info("getTemplatePlatforms getGdTemplateWhitelist res:" + templateWhitelistIds);
        Assert.isTrue(!CollectionUtils.isEmpty(templateWhitelistIds), "请配置模板白名单");

        if (orderProduct.equals(OrderProduct.OTT_GD.getCode())) {
            templateWhitelistIds = templateWhitelistIds.stream()
                    .filter(templateId -> {
                        boolean isManuscript = promotionPurposeType != null
                                && promotionPurposeType.equals(PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode());
                        return isManuscript == ottGdManuscriptTemplateIds.contains(templateId);
                    }).collect(Collectors.toList());
        }

        GdOrderExtDto extDto = orderExtService.getGdOrderExtInfo(orderId);
        if (extDto != null) {
            if (GdOrderSource.FLY.getCode().equals(extDto.getOrderSource())) {
                templateWhitelistIds = getGdTemplateWhitelist(OrderProduct.FLY_GD.getGdTemplateConfig());
            } else if (GdOrderSource.TOP_FLOW.getCode().equals(extDto.getOrderSource())) {
                templateWhitelistIds = getGdTemplateWhitelist(OrderProduct.TOP_FLOW_GD.getGdTemplateConfig());
            }
        }

        List<Integer> finalTemplateWhitelistIds = templateWhitelistIds;
        List<TemplateVo> vos = treeDtos.stream()
                .filter(t -> finalTemplateWhitelistIds.contains(t.getId()))
                .map(treeDto -> TemplateVo.builder().id(treeDto.getId()).name(treeDto.getName())
                        .platforms(treeDto.getChilds().stream().map(styleTreeDto -> PlatformBaseVo.builder()
                                        .id(styleTreeDto.getId()).name(styleTreeDto.getName()).build())
                                .collect(Collectors.toList()))
                        .build()).collect(Collectors.toList());

        Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapInIds(templateWhitelistIds);
        log.info("getTemplatePlatforms getTemplateMapInIds res:" + templateMap);
        List<TemplateVo> whiteListTemplateVo = templateDtoMapToVo(
                templateMap, null,
                isSupportSchemeUrl(context.getAccountId(), orderDto.getProduct(), templateWhitelistIds),
                orderDto.getProduct()
        );
        log.info("当前订单模板白名单[{}]", whiteListTemplateVo);
        List<TemplateVo> resultList = new ArrayList<>();
        log.info("当前所有模板[{}]", vos);
        for (TemplateVo vo : vos) {
            for (TemplateVo s : whiteListTemplateVo) {
                if (vo.getId() == s.getId()) {
                    s.setPlatforms(vo.getPlatforms());
                    resultList.add(s);
                }
            }
        }

        //程序化gd订单不支持投放动态
        if (!OriginTag.DEFAULT.getCode().equals(orderDto.getOriginTag())) {
            resultList = resultList.stream().filter(t -> !dynamicTemplateIds.contains(t.getId()))
                    .collect(Collectors.toList());
        }

        if (Utils.isPositive(orderDto.getBidderId())) {
            //https://www.tapd.cn/********/prong/stories/view/11********004403741
            //如果是程序化投放，则过滤dsp绑定的模板
            List<Integer> bidderTemplateList = bidderConfigService.getBidderTemplate(orderDto.getBidderId());
            log.info("adx template filter: bidder_id={}, templates={}", orderDto.getBidderId(), bidderTemplateList);
            Set<Integer> bidderTemplateSet = Sets.newHashSet(bidderTemplateList);
            resultList = resultList.stream().filter(t -> bidderTemplateSet.contains(t.getId())).collect(Collectors.toList());
        }

        postProcessTemplates(resultList);

        TemplateLabelOptions.TemplateOptionsBuilder templateOptionsBuilder = TemplateLabelOptions.newBuilder();
//        if (Objects.equals(orderDto.getProduct(), OrderProduct.GD_CPM.getCode()) && Objects.nonNull(extDto)) {
//            templateOptionsBuilder.withOption(TemplateLabel.ORDER_SOURCE, extDto.getOrderSource().longValue());
//        }
        resultList = postProcessWithLabel(resultList, templateOptionsBuilder.build());

        return Response.SUCCESS(resultList);
    }

    //绑定标签+过滤模板
    private List<TemplateVo> postProcessWithLabel(List<TemplateVo> templates, TemplateLabelOptions options) {
        List<Integer> templateIdList = templates.stream().map(TemplateVo::getId).collect(Collectors.toList());
        Map<Integer, List<BrandTemplateLabelRelationDto>> templateRelationMap = brandTemplateLabelService.getTemplateRelationsWithTemplateAndOptions(templateIdList, options);
        return templates.stream()
                .peek(template -> {
                    List<BrandTemplateLabelRelationDto> relations = templateRelationMap.getOrDefault(template.getId(), Collections.emptyList());
                    Map<String, Object> labelValueMap = relations.stream()
                            .collect(Collectors.toMap(BrandTemplateLabelRelationDto::getLabelKey, BrandTemplateLabelRelationDto::getBizValue));
                    template.setLabels(labelValueMap);
                })
                .collect(Collectors.toList());
    }

    private void postProcessTemplates(List<TemplateVo> templates) {
        templates.forEach(template -> {
            template.setIs_support_enterprise_space(
                    configCenter.getGdPlusConfig().getSupportEnterpriseSpaceTemplateIds().contains(template.getId())
            );
            template.setSupport_enterprise_space(
                    configCenter.getGdPlusConfig().getSupportEnterpriseSpaceMode(template.getId())
            );
            //story下一些不支持填写卡片跳转链接的模板
            template.set_support_scheme_url(
                    !configCenter.getGdPlusConfig().getUnsupportSchemeUrlTamplates().contains(template.getId())
            );
            //search cpt的一些模板不支持填写卡片跳转链接的模板，如果是这些模板则置为false，否则保持原样
            if (configCenter.getSearchCptConfig().isDisableSupportSchemaUrl(template.getId())) {
                template.set_support_scheme_url(false);
            }
            //填充扭一扭模板渲染交互方式属性
            template.setTwist_action_type(configCenter.getCreativeTwistConfig().getTwistActionShowType(template.getId()));
            template.setRequires_twist_material(TemplatePropertyEnum.isRequiresTwistMaterial(template.getId()));
            template.setSupport_custom_lottie(BooleanUtils.toInteger(configCenter.getCreativeTwistConfig().isSupportCustomLottieTemplate(template.getId())));
            // 跳转链接非必填
            Integer templateId = template.getId();
            TemplateJumpType jumpType = TemplateJumpType.getByTemplateId(templateId);
            if (Objects.nonNull(jumpType)) {
                template.setSupport_jump_type_list(jumpType.getJumpTypes().stream().map(GdJumpType::getCode).collect(Collectors.toList()));
                template.setSupport_monitor_list(jumpType.getSupportMonitorList());
                template.setRequired_link_url(jumpType.getIsRequired());
            }

            TemplatePropertyEnum.TemplateOptions templateOptions = TemplatePropertyEnum.getByTemplateIdWithoutEx(template.getId()).getOptions();
            //是否支持多稿件
            template.setIs_supports_multi_archive(templateOptions.isSupportsMultiArchive());
            //是否支持展示方式
            template.setIs_supports_display_mode(templateOptions.isSupportsDisplayMode());
            //是否自动播放升级
            template.setAuto_play_upgrade(templateOptions.isAutoPlayUpgrade());

            template.setIs_support_degrade_hint(configCenter.getCreativeTwistConfig().isSupportDegradeHint(template.getId()));
            template.setIs_support_anchor_hint(configCenter.getCreativeTwistConfig().isSupportAnchorHint(template.getId()));
            template.setModel_type_list(configCenter.getCreativeTwistConfig().getSupportModelTypeList(template.getId()));
            //是否支持滑动交互方式
            template.setIs_support_draw_gesture(templateOptions.isSupportDrawGesture());
        });
    }

    @RequestMapping(value = "/search/template/platforms", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation("查询模版下的平台")
    public Response<List<TemplateVo>> getSearchTemplatePlatforms(@ApiIgnore Context context,
                                                                 @ApiParam("广告位id，需要和模板组有关联关系时传递")
                                                                 @RequestParam(value = "slot_ids", required = false) List<Integer> slotIds) {

        List<Integer> templateWhitelistIds = getSearchTemplateWhitelist();

        templateWhitelistIds = filterTemplate(templateWhitelistIds, slotIds);

        Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapInIds(templateWhitelistIds);

        List<TemplateVo> whiteListTemplateVo = searchTemplateDtoMapToVo(templateMap, isSupportSchemeUrl(context.getAccountId(), null, templateWhitelistIds));

        return Response.SUCCESS(whiteListTemplateVo);
    }

    private List<ButtonCopyVo> buttonCopyDtos2Vos(List<ButtonCopyDto> dtos, PromotionPurposeType launchType) {
        if (CollectionUtils.isEmpty(dtos) || launchType == null || launchType == PromotionPurposeType.OGV) {
            return Collections.emptyList();
        }
        List<Integer> buttonTypes = PromotionPurposeTypeToButtonCopyTypeMapping.getByPType(launchType);
        return dtos.stream()
                .filter(dto -> dto != null && buttonTypes.contains(dto.getType()))
                .sorted(Comparator.comparing(ButtonCopyDto::getId))
                .map(dto -> ButtonCopyVo.builder()
                        .id(dto.getId())
                        .name(dto.getContent())
                        .type(dto.getType())
                        .type_desc(ButtonCopyTypeEnum.getByCode(dto.getType()).getDesc())
                        .build())
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "查询有效的App包下拉列表")
    @RequestMapping(value = "/app_package/drop_box", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<AppPackageVo>> queryValidAppPackageList(@ApiIgnore Context context) {

        QueryAppPackageDto query = QueryAppPackageDto
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .orderBy(Constants.MTIME_DESC)
                .status(AppPackageStatus.VALID.getCode())
                .platformStatus(AppPackagePlatformStatus.VALID.getCode())
                .build();

        List<AppPackageDto> result = soaAppPackageService.query(query);

        return Response.SUCCESS(webAppPackageService.dtosToVos(result));
    }


    @ApiOperation("获取账户下的稿件视频")
    @ResponseBody
    @RequestMapping(value = "/manuscripts", method = RequestMethod.GET)
    public Response<Pagination<List<VideoManuscriptVo>>> manuscripts(
            @ApiIgnore Context context,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "width") Integer width,
            @RequestParam(value = "height") Integer height,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size) {

        CsvJsonData<CsvPager<CmArchiveVo>> res = OkHttpUtils.get(csvUrl)
                .bean(ScvVideoReq.builder()
                        .account_id(context.getAccountId())
                        .audit_pass(true)
                        .page_no(page)
                        .page_size(size)
                        .ratio_width(width)
                        .ratio_height(height)
                        .name(name)
                        .build())
                .callForObject(new TypeToken<CsvJsonData<CsvPager<CmArchiveVo>>>() {
                });

        if (res.getCode() != 0) {
            return Response.FAIL(res.getCode(), res.getMessage());
        }

        return Response.SUCCESS(new Pagination<>(res.getData().getPage(), res.getData().getTotal(),
                ResourceControllerConverter.MAPPER.toVideoManuscriptVos(res.getData().getData())));
    }

    /**
     * @param scene 1：TopView 3D破框视频
     * @return
     */
    @ApiOperation("获取账户下的视频素材")
    @ResponseBody
    @RequestMapping(value = "/videos", method = RequestMethod.GET)
    public Response<Pagination<List<MgkVideoVo>>> videos(
            @ApiIgnore Context context,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "width") Integer width,
            @RequestParam(value = "height") Integer height,
            @RequestParam(value = "ip_video_type", required = false) List<Integer> ipVideoTypeList,
            @RequestParam(value = "max_size", required = false) Long maxSize,
            @RequestParam(value = "max_duration", required = false, defaultValue = "1000") Long maxDuration,
            @RequestParam(value = "page", defaultValue = "1") int page,
            @RequestParam(value = "size", defaultValue = "10") int size,
            @RequestParam(value = "order_product", required = false) Integer product,
            @RequestParam(value = "scene", required = false, defaultValue = "0") Integer scene) {

        PageResult<MgkVideoDto> pageResult;

        if (!CollectionUtils.isEmpty(ipVideoTypeList)) {
            PageResult<IPVideoBo> videoPageResult = ipVideoService
                    .getIPVideoBoList(name, page, size, VideoDealStatus.SUCCESS.getCode(),
                            width, height, ipVideoTypeList, maxSize, maxDuration, scene > 0);

            pageResult = new PageResult<>();
            pageResult.setTotal(videoPageResult.getTotal());
            pageResult.setRecords(videoPageResult.getRecords().stream()
                    .map(t -> MgkVideoDto.builder()
                            .id(t.getId())
                            .videoUrl(t.getVideoUrl())
                            .accountId(context.getAccountId())
                            .name(t.getVideoName())
                            .md5(t.getVideoMd5())
                            .width(t.getWidth())
                            .height(t.getHeight())
                            .duration(t.getDuration())
                            .size(Optional.ofNullable(t.getSize()).map(Long::intValue).orElse(0))
//                            .definitionType(DefinitionTypeEnum.P480.getCode())
                            .build())
                    .collect(Collectors.toList()));
        } else {
            // 传入的视频尺寸和合成的视频尺寸不一致
            boolean sizeTypeIgnore = false;
            if (IpVideoSizeEnum.STORY_BREAK_FRAME_VIDEO.matchWidthAndHeight(width, height)) {
                width = 728;
                height = 540;
                sizeTypeIgnore = true;
            }
            QueryMgkVideoParam queryMgkVideoParam = QueryMgkVideoParam.builder()
                    .accountId(context.getAccountId())
                    .page(page)
                    .size(size)
                    .name(name)
                    .sizeTypeIgnore(sizeTypeIgnore)
                    .width(width)
                    .height(height)
                    .build();
            pageResult = platformService.queryMgkVideoByPage(queryMgkVideoParam);
        }

        List<MgkVideoDto> dtos = pageResult.getRecords();
        int count = pageResult.getTotal();
        if (pageResult.getTotal() > 0 && OrderProduct.OTT_GD.getCode().equals(product)) {
            dtos = dtos.stream().filter(t -> ottGdVideoWidth.equals(t.getWidth())
                            && ottGdVideoHeight.equals(t.getHeight()))
                    .collect(Collectors.toList());
            count = dtos.size();
        }

        Map<Integer, SsaUposVideoDto> videoDtoMap = uposVideoService
                .getBizId2UposVideoMapInBizIds(dtos.stream()
                        .map(MgkVideoDto::getBizId).collect(Collectors.toList()));

        return Response.SUCCESS(new Pagination<>(page, count,
                CollectionUtils.isEmpty(pageResult.getRecords()) ? Collections.emptyList() :
                        dtos.stream().map(t -> convertToMgkVideoVo(t,
                                        videoDtoMap.getOrDefault(t.getBizId(), new SsaUposVideoDto())))
                                .collect(Collectors.toList())));
    }

    private MgkVideoVo convertToMgkVideoVo(MgkVideoDto mgkVideoDto, SsaUposVideoDto uposVideoDto) {
        return mgkVideoDto == null ? null : MgkVideoVo.builder()
                .cover(mgkVideoDto.getCover())
                .height(Utils.zeroIfNull(mgkVideoDto.getHeight()))
                .id(mgkVideoDto.getId())
                .name(mgkVideoDto.getName())
                .url(mgkVideoDto.getVideoUrl())
                .width(Utils.zeroIfNull(mgkVideoDto.getWidth()))
                .before_url(uposVideoDto.getUposUrl())
                .duration(mgkVideoDto.getDuration())
                .size(mgkVideoDto.getSize())
                .md5(mgkVideoDto.getMd5())
                .build();
    }

    @ApiOperation(value = "校验素材URL")
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    @ResponseBody
    public Response<ImageVideoVo> checkCreative(
            @RequestParam("template_id") Integer templateId,
            @RequestParam("type") Integer type,
            @RequestParam(value = "url", required = false) String url,
            @RequestParam(value = "seq", required = false) Integer seq) throws ServiceException {

        ImageVideoDto imageVideodto = platformService.checkAndGetMaterialInfo(templateId, type, url, seq);
        return Response.SUCCESS(getImageVideoVo(imageVideodto));
    }

    private ImageVideoVo getImageVideoVo(ImageVideoDto imageVideodto) {
        return ImageVideoVo.builder()
                .image_url(imageVideodto.getImage_url())
                .image_hash(imageVideodto.getImage_hash())
                .ext_image_url(imageVideodto.getExt_image_url())
                .ext_image_hash(imageVideodto.getExt_image_hash())
                .video_url(imageVideodto.getVideo_url())
                .video_hash(imageVideodto.getVideo_hash())
                .build();
    }

    @ApiOperation(value = "根据房间号查询直播间信息")
    @RequestMapping(value = "/live_room", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<LiveInfoVo> queryLiveInfo(@ApiParam("房间号") @RequestParam("live_id") String liveId) {

        Assert.isTrue(StringUtils.isNotBlank(liveId) && StringUtils.isNumeric(liveId),
                "直播间id不能为空");

        Long liveId2 = Long.valueOf(liveId);
        LiveRoomDto liveRoom = liveGrpcClient.queryLiveRoom(liveId2);
        Assert.notNull(liveRoom, "请确认房间号");

        String nickname = "";
        if (liveRoom.getUid() != null) {
            try {
                BilibiliUserInfoDto userInfoDto = accountGrpcClient.queryUser(liveRoom.getUid());
                nickname = userInfoDto == null ? "" : userInfoDto.getName();
            } catch (Exception e) {
                log.error("getLiveBroadcastRoomInfoById request user's nickname occur error! {}",
                        Throwables.getStackTraceAsString(e));
            }
        }
        return Response.SUCCESS(LiveInfoVo.builder()
                .description(nickname)
                .live_id(Math.toIntExact(liveRoom.getRoomId()))
                .title(liveRoom.getTitle())
                .cover(liveRoom.getCover())
                .status(liveRoom.getStatus())
                .status_desc(liveRoom.getStatus() == 0 ? "未开播" : "已开播")
                .link_type(GdJumpType.LINK.getCode())
                .link_type_desc(GdJumpType.LINK.getDesc())
                .link_url(liveRoom.getLink())
                .area_v2_id(liveRoom.getAreaId())
                .area_v2_name(liveRoom.getAreaName())
                .area_v2_parent_id(liveRoom.getAreaParentId())
                .area_v2_parent_name(liveRoom.getAreaParentName())
                .build());
    }

    @ApiOperation(value = "根据房间号查询直播间信息")
    @RequestMapping(value = "/batch_live_room", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<LiveInfoVo>> batchQueryLiveInfo(@ApiParam("房间号")
                                                         @RequestParam("live_id") List<Long> liveIdList) {

        Map<Long, LiveRoomDto> liveRoomMap = liveGrpcClient.queryLiveRoom(liveIdList);
        List<Long> uidList = liveRoomMap.values().stream()
                .map(LiveRoomDto::getUid)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, BilibiliUserInfoDto> userMap = accountGrpcClient.queryUser(uidList);

        List<LiveInfoVo> liveInfoVos = liveRoomMap.values().stream()
                .map(liveRoom -> {
                    BilibiliUserInfoDto user = userMap.get(liveRoom.getUid());
                    return LiveInfoVo.builder()
                            .description(Objects.nonNull(user) ? user.getName() : "")
                            .live_id(Math.toIntExact(liveRoom.getRoomId()))
                            .title(liveRoom.getTitle())
                            .cover(liveRoom.getCover())
                            .status(liveRoom.getStatus())
                            .status_desc(liveRoom.getStatus() == 0 ? "未开播" : "已开播")
                            .link_type(GdJumpType.LINK.getCode())
                            .link_type_desc(GdJumpType.LINK.getDesc())
                            .link_url(liveRoom.getLink())
                            .area_v2_id(liveRoom.getAreaId())
                            .area_v2_name(liveRoom.getAreaName())
                            .area_v2_parent_id(liveRoom.getAreaParentId())
                            .area_v2_parent_name(liveRoom.getAreaParentName())
                            .build();
                }).collect(Collectors.toList());
        return Response.SUCCESS(liveInfoVos);
    }

    @ApiOperation(value = "查询极致战队列表")
    @RequestMapping(value = "/extreme_team", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<ExtremeTeamVo>> queryExtremeTeam(
            @ApiParam("极致战队配置id") @RequestParam(value = "config_id") Long config_id) throws ServiceException {

        List<ExtremeTeamConfigDetailDto> extremeTeamDetailDtoList = soaMasExtremeTeamService
                .getExtremeTeamDetailListByConfigId(config_id);

        return Response.SUCCESS(CollectionUtils.isEmpty(extremeTeamDetailDtoList) ? new ArrayList<>() :
                extremeTeamDetailDtoList.stream().map(dto -> ExtremeTeamVo.builder()
                        .id(dto.getId())
                        .sequence(dto.getSequence())
                        .data_source_id(dto.getDataSourceId())
                        .data_source_name(dto.getDataSourceName())
                        .build()).collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询高播放稿件列表")
    @RequestMapping(value = "/high_play_archive", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<HighPlayArchiveVo>> getHighPlayArchiveList(
            @ApiParam("订单类型")
            @RequestParam(value = "order_product", required = false)
            Integer orderProduct) throws Exception {
        List<ResTargetItemDto> validItems = this.resTargetItemService.getValidItemByTargetType(
                TargetType.HIGH_PLAY_ARCHIVE.getCode());
        List<HighPlayArchiveVo> highPlayArchiveList = validItems.stream()
                .map(item -> HighPlayArchiveVo.builder()
                        .id(item.getId())
                        .name(item.getName())
                        .build())
                .collect(Collectors.toList());
        return Response.SUCCESS(highPlayArchiveList);
    }

    @ApiOperation(value = "上传图片")
    @RequestMapping(value = "/upload_image", method = RequestMethod.POST)
    @ResponseBody
    public Response<ImageVo> uploadImage(
            @RequestParam(value = "file") MultipartFile multipartFile,
            ImageRequireParam param
    ) throws ServiceException, IllegalStateException, IOException {
        File convertFile = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        multipartFile.transferTo(convertFile);
        BfsFile bfsFile = BfsFile.builder()
                .size(multipartFile.getSize())
                .convFile(convertFile)
                .mimeType(multipartFile.getContentType())
                .fileName(multipartFile.getOriginalFilename())
                .bytes(multipartFile.getBytes())
                .inputStream(multipartFile.getInputStream())
                .build();
        validateImage(bfsFile, param.getWidth(), param.getHeight(), param.getMax_size(), param.getMin_size());
        if (!CollectionUtils.isEmpty(param.getFormats())) {
            boolean isValidFormat = param.getFormats()
                    .stream()
                    .anyMatch(f -> ("image/" + f).equalsIgnoreCase(bfsFile.getMimeType()));
            Assert.isTrue(isValidFormat, "不支持的图片格式");
        }
        BfsUploadResult result = bfsService.upload(categoryName, bfsFile.getConvFile());
        return Response.SUCCESS(ImageVo.builder()
                .image_url(result.getUrl())
                .image_md5(result.getMd5())
                .image_hash(ImageUtil.buildHashCode(0, MaterialType.IMAGE, result.getUrl(), result.getMd5()))
                .build());
    }

    @ApiOperation(value = "上传视频")
    @RequestMapping(value = "/upload_video", method = RequestMethod.POST)
    @ResponseBody
    public Response<ImageVo> uploadVideo(
            @RequestParam("file") MultipartFile multipartFile,
            VideoRequireParam param)
            throws ServiceException, IllegalStateException, IOException {
        //todo
        return Response.SUCCESS(null);
    }


    /**
     * 支持闪屏CPT、闪屏GD、闪屏CPM、闪屏PD、TopView-CPT、TopView-GD
     */
    @ApiOperation(value = "查询可使用的平台信息")
    @RequestMapping(value = "/available_platform", method = RequestMethod.GET)
    @ResponseBody
    public Response<AvailablePlatformVo> getAvailablePlatform(@RequestParam(value = "schedule_id") Integer scheduleId,
                                                              @RequestParam(value = "component_types", required = false, defaultValue = "0") List<Integer> componentTypes,
                                                              @RequestParam(value = "guide_material_type", required = false, defaultValue = "0") Integer guideMaterialType,
                                                              @RequestParam(value = "is_enable_live_booking", required = false, defaultValue = "false") Boolean isEnableLiveBooking) {
        ScheduleDto scheduleDto = queryScheduleService.getScheduleBaseInfoById(scheduleId);
        //根据设备定向转换平台类型
        List<PlatformType> allAvailablePlatforms = queryScheduleService.getScheduleOsTarget(scheduleId, true);
        allAvailablePlatforms.sort(Comparator.comparing(PlatformType::getCode));
        Map<Integer, Integer> versions = SsaVersion.parseVersion(SsaVersion.VersionContext.builder()
                        .orderProduct(scheduleDto.getOrderProduct())
                        .buttonStyle(scheduleDto.getButtonStyle())
                        .videoPlayMode(scheduleDto.getSsaVideoPlayMode())
                        .platforms(allAvailablePlatforms.stream().map(PlatformType::getCode).collect(Collectors.toList()))
                        .adx(scheduleDto.isAdx())
                        .componentTypes(componentTypes)
                        .ssaJumpAreaEffect(scheduleDto.getJumpAreaEffect())
                        .guideMaterialType(guideMaterialType)
                        .isEnableLiveBooking(isEnableLiveBooking)
                        .effectiveType(scheduleDto.getEffectiveType())
                        .build())
                .stream()
                .collect(Collectors.toMap(javafx.util.Pair::getKey, javafx.util.Pair::getValue));

        boolean excludePad = OrderProduct.TOPVIEW_CODE_SET.contains(scheduleDto.getOrderProduct())
                || SsaLinkageType.isSearch(scheduleDto.getLinkageType())
                || SsaVideoPlayModeEnum.isArchive(scheduleDto.getSsaVideoPlayMode());

        List<AvailablePlatformVo.AvailableJumpVo> availableJump = Lists.newArrayList();
        List<AvailablePlatformVo.AvailableVersionVo> availableVersion = Lists.newArrayList();
        List<AvailablePlatformVo.AvailableMonitorVo> availableMonitor = Lists.newArrayList();

        for (PlatformType platform : allAvailablePlatforms) {
            //排除ipad_hd
            if (Objects.equals(platform, PlatformType.IPAD_HD)) {
                continue;
            }
            //这个过滤逻辑用处不大了，因为等都升级后allAvailablePlatforms中自然就不会出现这种排期：【需要排除但又包含pad】的BadCase
            //是否需要排除pad
            if (excludePad && PlatformType.isPad(platform.getCode())) {
                continue;
            }
            //build available jump
            availableJump.add(AvailablePlatformVo.AvailableJumpVo.builder()
                    .platformId(platform.getCode())
                    .platformName(platform.getDesc())
                    .build());

            //build available version
            availableVersion.add(AvailablePlatformVo.AvailableVersionVo.builder()
                    .platformId(platform.getCode())
                    .platformName(Objects.equals(platform, PlatformType.IPAD) ? "iPad粉" : platform.getDesc())
                    .version(versions.get(platform.getCode()))
                    .build());
            //ipad_hd 跟随 ipad
            if (Objects.equals(platform, PlatformType.IPAD)) {
                availableVersion.add(AvailablePlatformVo.AvailableVersionVo.builder()
                        .platformId(PlatformType.IPAD_HD.getCode())
                        .platformName(PlatformType.IPAD_HD.getDesc())
                        .version(versions.get(PlatformType.IPAD_HD.getCode()))
                        .build());
            }
            //build available monitor
            availableMonitor.add(AvailablePlatformVo.AvailableMonitorVo.builder()
                    .platformId(platform.getCode())
                    .platformName(platform.getDesc())
                    .build());
        }
        return Response.SUCCESS(AvailablePlatformVo.builder()
                .availableJump(availableJump)
                .availableVersion(availableVersion)
                .availableMonitor(availableMonitor)
                .build());
    }

    @ApiOperation(value = "创建品牌稿件")
    @RequestMapping(value = "brand_archive", method = RequestMethod.POST)
    public Response<Long> create(@ApiIgnore Context context,
                                 @RequestBody BrandArchiveVo createArchiveVo) {
        BrandArchiveDto brandArchiveDto = ResourceControllerConverter.MAPPER.toBrandArchiveDto(createArchiveVo);
        Long id = this.brandArchiveService.createBrandArchive(brandArchiveDto, getOperator(context));
        return Response.SUCCESS(id);
    }

    @ApiOperation(value = "查询品牌稿件")
    @RequestMapping(value = "brand_archive", method = RequestMethod.GET)
    public Response<Pagination<List<BrandArchiveVo>>> query(
            @ApiIgnore Context context,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "aid", required = false) List<Long> aidList,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size) {
        BrandArchiveQueryDto query = BrandArchiveQueryDto.builder()
                .accountId(getOperator(context).getOperatorId())
                .title(title)
                .aidList(aidList)
                .pageIndex(page)
                .pageSize(size)
                .build();
        PageResult<BrandArchiveDto> result = this.brandArchiveService.queryBrandArchive(query);
        List<BrandArchiveVo> vos = result.getRecords().stream()
                .map(ResourceControllerConverter.MAPPER::toBrandArchiveVo)
                .collect(Collectors.toList());
        return Response.SUCCESS(new Pagination<>(page, result.getTotal(), vos));
    }

    @ApiOperation(value = "查询品牌稿件（创建创意时）")
    @RequestMapping(value = "brand_archive/creative", method = RequestMethod.GET)
    public Response<Pagination<List<BrandArchiveVo>>> query(
            @ApiIgnore Context context,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "aid", required = false) List<Long> aidList,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "schedule_id", required = false) Integer scheduleId,
            @RequestParam(value = "ad_mark", defaultValue = "-1") Integer adMark,
            @RequestParam(value = "applicable_type", defaultValue = "-1") Integer applicableType) {
        OgvConfig ogvConfig = this.configCenter.getOgvConfig();
        org.apache.commons.lang3.tuple.Pair<Integer, Integer> duration = ogvConfig.getScheduleDuration(scheduleId);
        List<Integer> adMarkStatusList = null;
        if (adMark == 0) {
            //无标
            adMarkStatusList = Lists.newArrayList(BrandArchiveAdMarkStatus.NO.getCode());
        } else if (adMark == 1) {
            //有标
            adMarkStatusList = Lists.newArrayList(BrandArchiveAdMarkStatus.SUCCESS.getCode());
        }
        BrandArchiveQueryDto query = BrandArchiveQueryDto.builder()
                .accountId(getOperator(context).getOperatorId())
                .title(title)
                .aidList(aidList)
                .pageIndex(page)
                .pageSize(size)
                .minDuration(duration.getLeft() * 1000L)
                .maxDuration(duration.getRight() * 1000L)
                .maxSize(200 * 1024 * 1024L)//max=200M
                .minHeight(1080)
                .scaleTypeList(ogvConfig.getScaleTypes())
                .statusList(Collections.singletonList(BrandArchiveStatus.SUCCESS.getCode()))
                .markStatusList(adMarkStatusList)
                .applicableTypeList(applicableType == -1 ? null : Lists.newArrayList(applicableType))
                .build();
        PageResult<BrandArchiveDto> result = this.brandArchiveService.queryBrandArchive(query);
        List<BrandArchiveVo> vos = result.getRecords().stream()
                .map(ResourceControllerConverter.MAPPER::toBrandArchiveVo)
                .collect(Collectors.toList());
        return Response.SUCCESS(new Pagination<>(page, result.getTotal(), vos));
    }

    @ApiOperation(value = "查询season")
    @RequestMapping(value = "/season", method = RequestMethod.GET)
    public Response<List<OgvSeasonVo>> querySeason(@ApiIgnore Context context,
                                                   @RequestParam("season_id") List<Long> seasonIdList,
                                                   @RequestParam(value = "append_episode", required = false) boolean appendEpisode) {
        Assert.isTrue(seasonIdList.size() <= 200, "season数量不能超过200");
        List<OgvSeasonDto> ogvSeasonList = this.resOgvService.getOgvSeasonInResource(OgvResourceQueryDto.builder()
                .seasonIdList(seasonIdList)
                .statusList(Lists.newArrayList(OgvResourceStatusEnum.ONLINE.getCode()))
                .allowDowngrade(false)
                .appendEpisode(appendEpisode)
                .excludeBlack(true)
                .pageIndex(1)
                .pageSize(200)
                .build());
        List<OgvSeasonVo> seasons = OgvScheduleConverter.MAPPER.toOgvSeasonVo(ogvSeasonList);
        return Response.SUCCESS(seasons);
    }

    @ApiOperation(value = "查询season可投放时间")
    @RequestMapping(value = "/season/time", method = RequestMethod.GET)
    public Response<List<Long>> querySeasonTime(@ApiIgnore Context context,
                                                @RequestParam("season_id") List<Long> seasonIdList) {
        List<Long> times = this.resOgvService.getCommonLaunchTime(seasonIdList);
        long curDayBeginTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN)
                .atZone(ZoneId.systemDefault())
                .toInstant()
                .toEpochMilli();
        return Response.SUCCESS(times.stream()
                .filter(time -> time >= curDayBeginTime)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询season可投放贴次")
    @RequestMapping(value = "/season/sticker", method = RequestMethod.GET)
    public Response<List<OgvResourceStickerVo>> querySeasonSticker(@ApiIgnore Context context,
                                                                   @RequestParam("season_id") List<Long> seasonIdList) {
        List<OgvResourceStickerDto> stickers = this.resOgvService.getCommonLaunchSticker(seasonIdList);
        List<OgvResourceStickerVo> stickerVos = OgvScheduleConverter.MAPPER.toOgvResourceStickerVo(stickers);
        return Response.SUCCESS(stickerVos);
    }

    /**
     * 视频云回调
     *
     * @param status 取值0/1表示转码失败/成功
     */
    @ApiOperation(value = "品牌稿件广告标回调")
    @RequestMapping(value = "brand_archive/ad_mark_callback", method = RequestMethod.GET)
    public Response<String> brandArchiveAdMarkCallback(
            @RequestParam(value = "cid") Long cid,
            @RequestParam(value = "bvc_status") Integer status) {
        log.info(String.format("brandArchiveAdMarkCallback request, cid=%d, bvc_status=%d", cid, status));
        if (status == 0 || status == 1) {
            BrandArchiveDto brandArchive = this.brandArchiveService.getBrandArchiveByCid(cid);
            Assert.notNull(brandArchive, "archive not found，cid=" + cid);
            this.brandArchiveService.updateBrandArchive(BrandArchiveDto.builder()
                    .id(brandArchive.getId())
                    .markStatus(status == 0 ? BrandArchiveAdMarkStatus.FAILED.getCode() : BrandArchiveAdMarkStatus.SUCCESS.getCode())
                    .build());
        }
        return Response.SUCCESS("success");
    }


    @ApiOperation(value = "根据CRM客户二级行行业id/客户统一三级行业id查询一级产品型号")
    @RequestMapping(value = "/product_label/first", method = RequestMethod.GET)
    public Response<List<IdNameVo>> queryFirstProductLabel(@ApiIgnore Context context,
                                                           @RequestParam(value = "customer_united_third_industry_id", required = false) Integer customerUnitedThirdIndustryId,
                                                           @RequestParam(value = "customer_second_commercial_category_id", required = false) Integer customerSecondCommercialCategoryId) {
        Integer crmIndustryId = Utils.isPositive(customerUnitedThirdIndustryId) ? customerUnitedThirdIndustryId : customerSecondCommercialCategoryId;
        boolean useCrmNewMapping = Utils.isPositive(customerUnitedThirdIndustryId);
        Assert.isTrue(Objects.nonNull(crmIndustryId), "crm行业id不能为空");
        List<IdName> idNames = dataServiceGrpcClient.queryFirstProductLabel(crmIndustryId, useCrmNewMapping);
        return Response.SUCCESS(idNames.stream()
                .map(in -> IdNameVo.builder()
                        .id(in.getId())
                        .name(in.getName())
                        .build())
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "根据一级产品型号id查询二级产品型号")
    @RequestMapping(value = "/product_label/second", method = RequestMethod.GET)
    public Response<List<IdNameVo>> querySecondProductLabel(@ApiIgnore Context context,
                                                            @RequestParam("first_product_label_id") Long firstProductLabelId) {
        List<IdName> idNames = dataServiceGrpcClient.querySecondProductLabel(firstProductLabelId);
        return Response.SUCCESS(idNames.stream()
                .map(in -> IdNameVo.builder()
                        .id(in.getId())
                        .name(in.getName())
                        .build())
                .collect(Collectors.toList()));
    }


    @ApiOperation(value = "查询有效的游戏下拉列表")
    @RequestMapping(value = "/game/drop_box", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<GameVo>> queryValidGameList(@ApiIgnore Context context) {
        Integer accountId = context.getAccountId();
        //查询游戏信息
        List<GameDto> gameList = this.cpmAdpGrpcClient.getGameList(accountId);
        gameList = gameList.stream()
                //v1：一期暂支持联运包
                //v2：支持CPS包，https://www.tapd.cn/********/prong/stories/view/11********004363937
                .filter(gameDto -> Objects.equals(GameChannelEnum.NORMAL.getCode(), gameDto.getChannelId())
                        || Objects.equals(GameChannelEnum.CPS.getCode(), gameDto.getChannelId())
                        || Objects.equals(GameChannelEnum.AD.getCode(), gameDto.getChannelId()))
                .collect(Collectors.toList());
        List<GameVo> gameVos = ResourceControllerConverter.MAPPER.toGameVo(gameList);
        if (CollectionUtils.isEmpty(gameVos)) {
            return Response.SUCCESS(Collections.emptyList());
        }
//        //查询包信息
//        QueryAppPackageDto query = QueryAppPackageDto
//                .builder()
//                .accountIds(Collections.singletonList(context.getAccountId()))
//                .orderBy(Constants.MTIME_DESC)
//                .status(AppPackageStatus.VALID.getCode())
//                .platformStatus(AppPackagePlatformStatus.VALID.getCode())
//                .build();
//        List<AppPackageDto> result = soaAppPackageService.query(query);
//        List<AppPackageVo> appPackageVos = webAppPackageService.dtosToVos(result);
//        //首先过滤出安卓，减少下面的名称匹配的次数
//        appPackageVos = appPackageVos.stream()
//                .filter(p -> p.getPlatforms().contains(WebAppPackageService.RES_TARGET_PLATFORM_ANDROID))
//                .collect(Collectors.toList());
//        for (GameVo game : gameVos) {
//            appPackageVos.stream()
//                    .filter(p -> Objects.equals(game.getGameName(), p.getApp_name()))
//                    .findFirst()
//                    .ifPresent(game::setAppPackage);
//        }
        return Response.SUCCESS(gameVos);
    }

    @ApiOperation(value = "查询弹幕氛围信息")
    @RequestMapping(value = "/danmaku/atmosphere", method = RequestMethod.GET)
    public Response<List<DanmakuAtmosphereVo>> queryDanmakuAtmosphere(@ApiIgnore Context context) {
        List<DanmakuAtmosphereVo> atmosphereVos = Arrays.stream(DanmakuAtmosphereTypeEnum.values())
                .map(dat -> DanmakuAtmosphereVo.builder()
                        .type(dat.getType())
                        .name(dat.getName())
                        .btnAnimType(Objects.isNull(dat.getBtnAnimType()) ? null : dat.getBtnAnimType().getType())
                        .btnAnimUrl(dat.getBtnAnimUrl())
                        .btnText(dat.getBtnText())
                        .btnIcon(Objects.isNull(dat.getBtnIcon()) ? null : ImageVo.builder()
                                .image_url(dat.getBtnIcon().getUrl())
                                .image_hash(ImageUtil.buildHashCode(0, MaterialType.IMAGE, dat.getBtnIcon().getUrl(), dat.getBtnIcon().getMd5()))
                                .build())
                        .dmLeaderIcon(Objects.isNull(dat.getDmLeaderIcon()) ? null : ImageVo.builder()
                                .image_url(dat.getDmLeaderIcon().getUrl())
                                .image_hash(ImageUtil.buildHashCode(0, MaterialType.IMAGE, dat.getDmLeaderIcon().getUrl(), dat.getDmLeaderIcon().getMd5()))
                                .build())
                        .dmTextColor(dat.getDmTextColor())
                        .dmBgColor(dat.getDmBgColor())
                        .build())
                .sorted(Comparator.comparing(DanmakuAtmosphereVo::getType))
                .collect(Collectors.toList());
        return Response.SUCCESS(atmosphereVos);
    }

    @ApiOperation(value = "查询扭一扭配置信息")
    @RequestMapping(value = "/twist/config", method = RequestMethod.GET)
    public Response<CreativeTwistVo> queryCreativeTwistConfig(@ApiIgnore Context context, @RequestParam("template_id") Integer templateId) {
        CreativeTwistDto creativeTwistDto = creativeTwistService.queryCreativeTwistConfig(templateId);
        CreativeTwistVo creativeTwistVo = GdCreativeConverter.MAPPER.toCreativeTwistVo(creativeTwistDto);
        return Response.SUCCESS(creativeTwistVo);
    }
}
