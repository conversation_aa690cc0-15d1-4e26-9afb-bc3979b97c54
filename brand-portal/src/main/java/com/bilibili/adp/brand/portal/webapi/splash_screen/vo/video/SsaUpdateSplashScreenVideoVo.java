package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaUpdateSplashScreenVideoVo implements Serializable {

    private static final long serialVersionUID = 284681527258803302L;
    @ApiModelProperty(value = "闪屏ID")
    private Integer splash_screen_id;


    @ApiModelProperty(value = "upos_url")
    private String upos_url;

    @ApiModelProperty(value = "biz_id")
    private Integer biz_id;


    @ApiModelProperty(value = "权限认证")
    private String upos_auth;

    @ApiModelProperty(value = "视频名称")
    private String file_name;

}