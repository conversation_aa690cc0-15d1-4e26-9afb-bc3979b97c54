package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewCptScheduleDateVo {

    @ApiModelProperty("序列id")
    private Integer seq;

    @ApiModelProperty("资源位id")
    private Integer source_id;

    @ApiModelProperty("日期")
    private Long date;

    @NotNull
    @ApiModelProperty("开始日期")
    private Long begin_date;

    @NotNull
    @ApiModelProperty("结束日期")
    private Long end_date;

    @ApiModelProperty("小时预约轮数")
    private Integer booking_num;

    @ApiModelProperty("日期")
    private Long launch_date;

    @ApiModelProperty("天预约轮数")
    private Integer rotation_num;
}
