package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.ssa_story;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023/3/29
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaStoryJumpInfoVo {

    @ApiModelProperty("跳转类型")
    private Integer jumpType;

    @ApiModelProperty("是否分端")
    private Boolean differentiatePlatform;

    @ApiModelProperty("跳转链接信息")
    private List<SsaStoryJumpUrlVo> urls;
}
