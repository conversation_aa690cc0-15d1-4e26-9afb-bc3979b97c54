package com.bilibili.adp.brand.portal.service.stat;


import com.bilibili.adp.brand.portal.webapi.statistic.vo.TopViewCreativeDataVo;
import com.bilibili.adp.brand.portal.webapi.statistic.vo.TopViewOrderDataVo;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.creative.dto.GdTopViewDto;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.dto.QueryOrderParamDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.platform.report.api.dto.StatTopViewCreativeDto;
import com.bilibili.brand.platform.charging.dao.AdStatCreativeDayDao;
import com.bilibili.brand.platform.charging.po.AdStatCreativeDayPo;
import com.bilibili.brand.platform.charging.po.AdStatCreativeDayPoExample;
import com.bilibili.brand.platform.report.biz.service.StatBaseService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.GROUP_TYPE;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmQueryScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaCpmScheduleService;
import com.bilibili.utils.BatchUtil;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/19 20:17
 */
@Service
public class TopViewStatService extends StatBaseService {
    @Autowired
    private IGdOrderService gdOrderService;
    @Autowired
    private ITopViewCreativeService topViewCreativeService;
    @Autowired
    private AdStatCreativeDayDao creativeDao;
    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private ISsaCpmScheduleService ssaCpmScheduleService;

    //查询创意维度展点消
    public List<TopViewCreativeDataVo> getCreativeStat(Integer accountId,
                                                       GROUP_TYPE groupType,
                                                       Long fromTime,
                                                       Long toTime,
                                                       String orderName,
                                                       OrderProduct orderProduct) {
        Timestamp startTime = new Timestamp(fromTime);
        Timestamp endTime = Utils.getEndSecondOfDay(new Timestamp(toTime));
        List<StatTopViewCreativeDto> stats = groupBy(
                groupType,
                GROUP_KEY.CREATIVE_ID,
                doGetCreativeStat(accountId, startTime, endTime, orderName, orderProduct),
                startTime,
                endTime);
        return stats.stream().map(stat -> {
            TopViewCreativeDataVo vo = TopViewCreativeDataVo.builder()
                    .date(stat.getDate())
                    .orderId(stat.getOrderId())
                    .orderName(stat.getOrderName())
                    .creativeId(stat.getCreativeId())
                    .creativeName(stat.getCreativeName())
                    .ssaShowCount(stat.getSsaShowCount())
                    .ssaClickCount(stat.getSsaClickCount())
                    .ssaClickRate(stat.getSsaClickRate())
                    .hfShowCount(stat.getHfShowCount())
                    .hfClickCount(stat.getHfClickCount())
                    .hfClickRate(stat.getHfClickRate())
                    .build();
            return vo;
        }).collect(Collectors.toList());
    }

    //查询订单维度展点消
    //PS:
    //top_view的订单、排期维度的展点消数据是闪屏和首焦的累加值，而实际应该以闪屏为基准，
    //顾实际上订单维度的展点消数据仍然需要通过创意（闪屏和首焦）聚合才是正确的
    public List<TopViewOrderDataVo> getOrderStat(Integer accountId,
                                                 GROUP_TYPE groupType,
                                                 Long fromTime,
                                                 Long toTime,
                                                 String orderName,
                                                 OrderProduct orderProduct) {
        Timestamp startTime = new Timestamp(fromTime);
        Timestamp endTime = Utils.getEndSecondOfDay(new Timestamp(toTime));
        List<StatTopViewCreativeDto> stats = groupBy(
                groupType,
                GROUP_KEY.ORDER_ID,
                doGetCreativeStat(accountId, startTime, endTime, orderName, orderProduct),
                startTime,
                endTime);
        return stats.stream().map(stat -> {
            TopViewOrderDataVo vo = TopViewOrderDataVo.builder()
                    .date(stat.getDate())
                    .orderId(stat.getOrderId())
                    .orderName(stat.getOrderName())
                    .ssaShowCount(stat.getSsaShowCount())
                    .ssaClickCount(stat.getSsaClickCount())
                    .ssaClickRate(stat.getSsaClickRate())
                    .hfShowCount(stat.getHfShowCount())
                    .hfClickCount(stat.getHfClickCount())
                    .hfClickRate(stat.getHfClickRate())
                    .build();
            return vo;
        }).collect(Collectors.toList());
    }

    public List<StatTopViewCreativeDto> doGetCreativeStat(Integer accountId,
                                                          Timestamp startTime,
                                                          Timestamp endTime,
                                                          String orderName,
                                                          OrderProduct orderProduct) {
        Assert.notNull(accountId, "accountId must be specified");
        List<StatTopViewCreativeDto> result = Lists.newArrayList();
        List<GdOrderDto> orders = null;
        List<Integer> orderIdList = null;
        if (!StringUtils.isEmpty(orderName)) {
            //1、订单
            orders = gdOrderService.queryOrders(QueryOrderParamDto.builder()
                    .orderName(orderName)
                    .accountId(accountId)
                    .productList(Lists.newArrayList(orderProduct.getCode()))
                    .build());
            if (CollectionUtils.isEmpty(orders)) return result;
            orderIdList = orders.stream().map(GdOrderDto::getOrderId).collect(Collectors.toList());
        }

        //查询闪屏和首焦部分创意指标数据
        //闪屏以及TopView的创意虽然也会落创意表，但是实际使用时用的是闪屏id
        //闪屏部分的SalesType分别是和订单类型对应的76和95
        //首焦部分的SalesType统一是43
        SalesType ssaSalesType = orderProduct.getSalesType();
        SalesType hfSalesType = SalesType.TOP_VIEW_CPT;

        //闪屏展点消知识点：
        //在ad_stat_creative_day中的account_id是crm_account_id即ssa_cpm_schedule的contract_account_id，因此不能直接使用投放端
        //的账号id来查询ad_stat_creative_day表。
        //此外正常情况下ssa_cpm_schedule的contract_account_id其实就是fc_order中的contract_account_id，但是存在小概率业务中会变更
        //客户主体，导致fc_order的contract_account_id发生变化，但是ssa_cpm_schedule的contract_account_id中的没变（比如top_view_id=281,
        //闪屏id=13421），因此为了数据齐全，统一使用ssa_cpm_schedule的contract_account_id来查询闪屏部分的展点消数据

        //查询闪屏排期，只要和时间区间有交集即可
        List<ScheduleDto> scheduleDtos = this.queryScheduleService.queryBaseSchedule(
                QueryScheduleDto.builder()
                        .accountId(accountId)
                        .orderIds(orderIdList)
                        .gdBeginTime(startTime)
                        .gdEndTime(endTime)
                        .timeContains(false)
                        .salesTypes(Lists.newArrayList(ssaSalesType.getCode()))
                        .build());
        if (CollectionUtils.isEmpty(scheduleDtos)) return result;

        //根据排期查询对应的contract_account_id
        List<Integer> scheduleIdList = scheduleDtos.stream().map(ScheduleDto::getScheduleId).collect(Collectors.toList());
        List<Integer> contractAccountIdList = BatchUtil.batch(scheduleIdList, subScheduleIdList -> {
            List<SsaCpmScheduleDto> ssaCpmScheduleDtos = this.ssaCpmScheduleService.querySSaCpmSchedule(
                    SsaCpmQueryScheduleDto.builder()
                            .gdScheduleIds(subScheduleIdList)
                            .build());
            return CollectionUtils.isEmpty(ssaCpmScheduleDtos) ? Lists.newLinkedList() : ssaCpmScheduleDtos.stream()
                    .map(SsaCpmScheduleDto::getContractAccountId).collect(Collectors.toList());
        });

        contractAccountIdList = contractAccountIdList.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contractAccountIdList)) return result;

        //闪屏展点消
        List<AdStatCreativeDayPo> ssaStatCreativeList = getStatCreative(contractAccountIdList, orderIdList,
                startTime, endTime, ssaSalesType);

        //如果闪屏为空，即没曝光，则直接返回即可
        if (CollectionUtils.isEmpty(ssaStatCreativeList)) return result;

        orderIdList = ssaStatCreativeList.stream().map(AdStatCreativeDayPo::getOrderId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orders)) {
            orders = gdOrderService.queryOrders(QueryOrderParamDto.builder().orderIds(orderIdList).build());
        }
        Map<Integer, GdOrderDto> orderMap = orders.stream().collect(Collectors.toMap(GdOrderDto::getOrderId, Function.identity()));

        //首焦展点消
        //一定先根据闪屏补充orderIdList信息，否则orderIdList为空的话，查询首焦会把TopView-GD和TopView-CPT都查询出来了
        List<AdStatCreativeDayPo> hfStatCreativeList = getStatCreative(Lists.newArrayList(accountId), orderIdList,
                startTime, endTime, hfSalesType);

        //构建曝光日+首焦创意到首焦展点消的Mapping，方便根据TopView信息快速查到对应的某日首焦展点消
        Map<String, AdStatCreativeDayPo> time2HfCreativeMap = hfStatCreativeList.stream().collect(Collectors.toMap(
                po -> String.format("%s_%d", TimeUtil.timestampToIsoDateStr(po.getGroupTime()), po.getCreativeId()),
                Function.identity()));

        //查询当前所有订单对应的TopView信息，这是将闪屏展点消和首焦展点消关联起来的关键信息
        Map<Integer, List<GdTopViewDto>> orderTopViewMap = topViewCreativeService.getTopViewInfoMapByOrderIdList(orderIdList);

        //构建闪屏创意到TopView的Mapping，方便根据闪屏展点消数据找到对应的TopView，进而找到对应的首焦展点消
        Map<Long, GdTopViewDto> ssa2TopViewMap = orderTopViewMap.values().stream().flatMap(Collection::stream)
                .collect(Collectors.toMap(GdTopViewDto::getSsaCreativeId, Function.identity()));

        for (AdStatCreativeDayPo ssa : ssaStatCreativeList) {
            GdTopViewDto topView = ssa2TopViewMap.get(ssa.getCreativeId());
            //topView是有可能为null，不排除有曝光比如测试预览等case，然后业务库对应创意被删除，导致的二次查询查不到对应的创意
            if (Objects.nonNull(topView)) {
                //topView是根据订单查出来的，因此根据topView的订单Id查询订单，一定存在
                GdOrderDto order = orderMap.get(topView.getGdOrderId());
                //穿插个无关背景：
                //客户端之前存在一个bug，可能会存在闪屏和首焦创意错乱的问题（FIXED），所以有可能首焦的曝光比闪屏的还高
                AdStatCreativeDayPo hfIos = time2HfCreativeMap.get(
                        String.format("%s_%d", TimeUtil.timestampToIsoDateStr(ssa.getGroupTime()),
                                topView.getNewHfIosCreativeId()));
                AdStatCreativeDayPo hfAndroid = time2HfCreativeMap.get(
                        String.format("%s_%d", TimeUtil.timestampToIsoDateStr(ssa.getGroupTime()),
                                topView.getNewHfAndroidCreativeId()));
                StatTopViewCreativeDto dto = convertDto(ssa, hfIos, hfAndroid, order, topView);
                result.add(dto);
            }
        }
        return result;
    }

    private List<StatTopViewCreativeDto> groupBy(GROUP_TYPE groupType,
                                                 GROUP_KEY groupKey,
                                                 List<StatTopViewCreativeDto> stats,
                                                 Timestamp fromTime,
                                                 Timestamp toTime) {
        Map<Long, List<StatTopViewCreativeDto>> keyStats = stats.stream().collect(Collectors.groupingBy(groupKey::getGroupByKey));
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        List<StatTopViewCreativeDto> result = Lists.newArrayList();
        keyStats.forEach((key, subStat) -> {
            Map<String, StatTopViewCreativeDto> temp = new HashMap<>();
            subStat.forEach(stat -> {
                String groupTypeValue = groupType.generateTimeKey(calendar, stat.getGroupTime().getTime(), fromTime, toTime);
                //Do Merge
                temp.put(groupTypeValue, mergeStat(groupTypeValue, temp.get(groupTypeValue), stat));
            });
            result.addAll(temp.values());
        });
        return result;
    }

    private StatTopViewCreativeDto mergeStat(String date, StatTopViewCreativeDto s1, StatTopViewCreativeDto s2) {
        if (Objects.isNull(s1) && Objects.isNull(s2)) return null;
        if (Objects.isNull(s1) || Objects.isNull(s2)) {
            StatTopViewCreativeDto nonNullValue = Objects.nonNull(s1) ? s1 : s2;
            nonNullValue.setDate(date);
            return nonNullValue;
        }
        return StatTopViewCreativeDto.builder()
                .date(date)
                .accountId(s1.getAccountId())
                .orderId(s1.getOrderId())
                .orderName(s1.getOrderName())
                .creativeId(s1.getCreativeId())
                .creativeName(s1.getCreativeName())
                .ssaShowCount(Long.sum(s1.getSsaShowCount(), s2.getSsaShowCount()))
                .ssaClickCount(Long.sum(s1.getSsaClickCount(), s2.getSsaClickCount()))
                .ssaClickRate(getClickRate(Long.sum(s1.getSsaClickCount(), s2.getSsaClickCount()),
                        Long.sum(s1.getSsaShowCount(), s2.getSsaShowCount())))
                .hfShowCount(Long.sum(s1.getHfShowCount(), s2.getHfShowCount()))
                .hfClickCount(Long.sum(s1.getHfClickCount(), s2.getHfClickCount()))
                .hfClickRate(getClickRate(Long.sum(s1.getHfClickCount(), s2.getHfClickCount()),
                        Long.sum(s1.getHfShowCount(), s2.getHfShowCount())))
                .build();
    }

    private StatTopViewCreativeDto convertDto(AdStatCreativeDayPo po,
                                              AdStatCreativeDayPo hfIosPo,
                                              AdStatCreativeDayPo hfAndroidPo,
                                              GdOrderDto orderDto,
                                              GdTopViewDto topViewDto) {
        StatTopViewCreativeDto statDto = StatTopViewCreativeDto.builder()
                .accountId(po.getAccountId())
                .groupTime(po.getGroupTime())
                .orderId(po.getOrderId())
                .orderName(orderDto.getOrderName())
                .creativeId(topViewDto.getId())
                .creativeName(topViewDto.getCreativeName())
                .ssaShowCount(po.getShowCount())
                .ssaClickCount(po.getClickCount().longValue())
                .build();
        Long hfShowCount = 0L, hfClickCount = 0L;
        if (Objects.nonNull(hfIosPo)) {
            hfShowCount += hfIosPo.getShowCount();
            hfClickCount += hfIosPo.getClickCount();
        }
        if (Objects.nonNull(hfAndroidPo)) {
            hfShowCount += hfAndroidPo.getShowCount();
            hfClickCount += hfAndroidPo.getClickCount();
        }
        statDto.setHfShowCount(hfShowCount);
        statDto.setHfClickCount(hfClickCount);
        statDto.setSsaClickRate(getClickRate(statDto.getSsaClickCount(), statDto.getSsaShowCount()));
        statDto.setHfClickRate(getClickRate(statDto.getHfClickCount(), statDto.getHfShowCount()));
        return statDto;
    }

    public List<AdStatCreativeDayPo> getStatCreative(List<Integer> accountIdList,
                                                     List<Integer> orderIdList,
                                                     Timestamp fromTime,
                                                     Timestamp toTime,
                                                     SalesType salesType) {
        AdStatCreativeDayPoExample example = new AdStatCreativeDayPoExample();
        AdStatCreativeDayPoExample.Criteria criteria = example.or()
                .andAccountIdIn(accountIdList)
                .andGroupTimeGreaterThanOrEqualTo(fromTime)
                .andGroupTimeLessThanOrEqualTo(toTime)
                .andSalesTypeEqualTo(salesType.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(orderIdList)) {
            criteria.andOrderIdIn(orderIdList);
        }
        return creativeDao.selectByExample(example);
    }

    enum GROUP_KEY {
        ORDER_ID {
            @Override
            public Long getGroupByKey(StatTopViewCreativeDto stat) {
                return Long.valueOf(stat.getOrderId());
            }
        },
        CREATIVE_ID {
            @Override
            public Long getGroupByKey(StatTopViewCreativeDto stat) {
                return Long.valueOf(stat.getCreativeId());
            }
        };

        public abstract Long getGroupByKey(StatTopViewCreativeDto statPojo);
    }
}
