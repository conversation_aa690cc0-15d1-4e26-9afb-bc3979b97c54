package com.bilibili.adp.brand.portal.webapi.resource.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpFansVo implements Serializable {

    private Long upMid;

    private String upNickName;

    private Long upFansNumber;

    private Integer upFansLevel;

    private String upFansLevelDesc;
}
