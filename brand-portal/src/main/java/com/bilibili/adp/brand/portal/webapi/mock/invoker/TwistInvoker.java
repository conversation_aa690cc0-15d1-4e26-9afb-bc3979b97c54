package com.bilibili.adp.brand.portal.webapi.mock.invoker;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.InvokerDesc;
import com.bilibili.adp.brand.portal.webapi.mock.annotations.MethodDesc;
import com.bilibili.adp.brand.portal.webapi.mock.vo.TwistQueryReq;
import com.bilibili.adp.brand.portal.webapi.mock.vo.TwistQueryResultItem;
import com.bilibili.adp.brand.portal.webapi.mock.vo.TwistRefreshReq;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.biz.creative.dao.CreativeTwistDao;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.creative.po.CreativeTwistPo;
import com.bilibili.brand.biz.creative.po.CreativeTwistPoExample;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.creative.po.GdCreativePoExample;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.enums.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/14 21:15
 */
@Slf4j
@InvokerDesc
public class TwistInvoker {

    @Autowired
    private CreativeTwistDao creativeTwistDao;

    @Autowired
    private GdCreativeDao creativeDao;

    @MethodDesc(
            summary = "刷扭一扭角度和速度",
            desc = "主要功能是基于实际投放效果，动态调整扭一扭的角度、加速度等相关配置。" + AbstractInvoker.COMMON_DESC
    )
    public List<TwistQueryResultItem> refreshTwist(TwistRefreshReq req) {
        // 参数校验
        Assert.isTrue(Objects.equals(PlatformType.IPHONE.getCode(), req.getPlatformType())
                        || Objects.equals(PlatformType.ANDROID.getCode(), req.getPlatformType()),
                "平台类型不合法");
        Assert.isTrue(Objects.nonNull(req.getAngle())
                || Objects.nonNull(req.getSpeed()), "角度、加速度至少填写一个");

        // 查询扭一扭配置
        TwistQueryReq twistQueryReq = TwistQueryReq.builder()
                .orderId(req.getOrderId())
                .scheduleId(req.getScheduleId())
                .creativeId(req.getCreativeId())
                .resourceType(req.getResourceType())
                .build();
        List<GdCreativePo> gdCreativePoList = this.doQueryGdCreativePoList(twistQueryReq);
        List<Long> gdCreativeIdList = gdCreativePoList.stream()
                .map(GdCreativePo::getCreativeId)
                .distinct()
                .collect(Collectors.toList());
        List<CreativeTwistPo> creativeTwistPoList = this.doQueryCreativeTwistPoList(gdCreativeIdList, twistQueryReq.getResourceType());
        Assert.notEmpty(creativeTwistPoList, "未查询到扭一扭配置，请检查你的更新条件是否正确");

        // 更新操作
        for (CreativeTwistPo creativeTwistPo : creativeTwistPoList) {
            if (Objects.equals(PlatformType.IPHONE.getCode(), req.getPlatformType())) {
                ObjectUtils.notNullDo(req.getAngle(), creativeTwistPo::setIosAngle);
                ObjectUtils.notNullDo(req.getSpeed(), creativeTwistPo::setIosSpeed);
            }
            if (Objects.equals(PlatformType.ANDROID.getCode(), req.getPlatformType())) {
                ObjectUtils.notNullDo(req.getAngle(), creativeTwistPo::setAndroidAngle);
                ObjectUtils.notNullDo(req.getSpeed(), creativeTwistPo::setAndroidSpeed);
            }
            this.creativeTwistDao.updateByPrimaryKeySelective(creativeTwistPo);
        }

        // 返回更新结果
        Map<Long, GdCreativePo> gdCreativePoMap = gdCreativePoList.stream()
                .collect(Collectors.toMap(GdCreativePo::getCreativeId, Function.identity()));
        return this.convertToTwistQueryResultItem(creativeTwistPoList, gdCreativePoMap);
    }

    @MethodDesc(
            summary = "查询扭一扭角度和速度",
            desc = "主要功能是查询扭一扭角度和速度，通常用于验证【动态调整扭一扭的角度、加速度等相关配置】是否已生效。"
                    + AbstractInvoker.COMMON_DESC
    )
    public List<TwistQueryResultItem> queryTwist(TwistQueryReq req) {
        // 查询扭一扭配置
        List<GdCreativePo> gdCreativePoList = this.doQueryGdCreativePoList(req);
        List<Long> gdCreativeIdList = gdCreativePoList.stream()
                .map(GdCreativePo::getCreativeId)
                .distinct()
                .collect(Collectors.toList());
        List<CreativeTwistPo> creativeTwistPoList = this.doQueryCreativeTwistPoList(gdCreativeIdList, req.getResourceType());
        Assert.notEmpty(creativeTwistPoList, "未查询到扭一扭配置，请检查你的查询条件是否正确");

        // 转换结果
        Map<Long, GdCreativePo> gdCreativePoMap = gdCreativePoList.stream()
                .collect(Collectors.toMap(GdCreativePo::getCreativeId, Function.identity()));
        return this.convertToTwistQueryResultItem(creativeTwistPoList, gdCreativePoMap);
    }

    private List<GdCreativePo> doQueryGdCreativePoList(TwistQueryReq req) {
        Assert.isTrue(Objects.nonNull(req.getOrderId())
                        || Objects.nonNull(req.getScheduleId())
                        || Objects.nonNull(req.getCreativeId()),
                "订单Id、创意Id、排期Id至少填写一个");

        GdCreativePoExample gdCreativePoExample = new GdCreativePoExample();
        GdCreativePoExample.Criteria gdCreativePoCriteria = gdCreativePoExample.createCriteria();
        ObjectUtils.notNullDo(req.getOrderId(), gdCreativePoCriteria::andOrderIdEqualTo);
        ObjectUtils.notNullDo(req.getScheduleId(), gdCreativePoCriteria::andScheduleIdEqualTo);
        ObjectUtils.notNullDo(req.getCreativeId(), gdCreativePoCriteria::andCreativeIdEqualTo);
        ObjectUtils.notNullDo(IsDeleted.VALID.getCode(), gdCreativePoCriteria::andIsDeletedEqualTo);
        return this.creativeDao.selectByExample(gdCreativePoExample);
    }

    private List<CreativeTwistPo> doQueryCreativeTwistPoList(List<Long> creativeIdList, Integer resourceType) {
        if (CollectionUtils.isEmpty(creativeIdList)) {
            return new ArrayList<>();
        }
        CreativeTwistPoExample creativeTwistPoExample = new CreativeTwistPoExample();
        CreativeTwistPoExample.Criteria creativeTwistPoCriteria = creativeTwistPoExample.createCriteria();
        ObjectUtils.notNullDo(creativeIdList, creativeTwistPoCriteria::andCreativeIdIn);
        ObjectUtils.notNullDo(resourceType, creativeTwistPoCriteria::andResourceTypeEqualTo);
        ObjectUtils.notNullDo(IsDeleted.VALID.getCode(), creativeTwistPoCriteria::andIsDeletedEqualTo);
        return this.creativeTwistDao.selectByExample(creativeTwistPoExample);
    }

    private List<TwistQueryResultItem> convertToTwistQueryResultItem(List<CreativeTwistPo> creativeTwistPoList, Map<Long, GdCreativePo> gdCreativePoMap) {
        return creativeTwistPoList.stream()
                .map(creativeTwistPo -> {
                    GdCreativePo gdCreativePo = gdCreativePoMap.getOrDefault(creativeTwistPo.getCreativeId(), new GdCreativePo());

                    TwistQueryResultItem twistQueryResultItem = new TwistQueryResultItem();
                    BeanUtils.copyProperties(gdCreativePo, twistQueryResultItem);
                    BeanUtils.copyProperties(creativeTwistPo, twistQueryResultItem);
                    twistQueryResultItem.setResourceType(creativeTwistPo.getResourceType() == 0 ? "inline" : "story");
                    twistQueryResultItem.setCtime(TimeUtil.timestampToIsoTimeStr(creativeTwistPo.getCtime()));
                    twistQueryResultItem.setMTime(TimeUtil.timestampToIsoTimeStr(creativeTwistPo.getMtime()));
                    return twistQueryResultItem;
                }).collect(Collectors.toList());
    }
}
