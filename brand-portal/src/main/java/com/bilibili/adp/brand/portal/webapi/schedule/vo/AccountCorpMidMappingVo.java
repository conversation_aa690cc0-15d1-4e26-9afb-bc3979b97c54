package com.bilibili.adp.brand.portal.webapi.schedule.vo;


import com.bilibili.brand.biz.utils.LaunchUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountCorpMidMappingVo {

    private Integer account_id;

    private Integer id;

    private String mid;

    private String corp_face;

    private String corp_name;

    private String space;

}

