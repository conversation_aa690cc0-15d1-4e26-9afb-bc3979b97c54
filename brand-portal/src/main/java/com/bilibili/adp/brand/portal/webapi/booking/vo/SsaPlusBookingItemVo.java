package com.bilibili.adp.brand.portal.webapi.booking.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 日预约详细信息
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaPlusBookingItemVo {

    @ApiModelProperty("日期")
    private Long date;

    @ApiModelProperty("预约状态 -1-无效的 0-可预约  1-已预约 2-已锁定 3-预约已排期 4-锁定已排期")
    private Integer booking_status;

    @ApiModelProperty("预约状态描述")
    private String booking_status_desc;

    @ApiModelProperty("所属订单")
    private Integer dependency_order_id;

    @ApiModelProperty("所属订单名称")
    private String dependency_order_name;

    @ApiModelProperty("所属排期")
    private Integer dependency_schedule_id;

    @ApiModelProperty("业务方名称")
    private String business_side_name;

    @ApiModelProperty("是否我的预约（表示该业务方的预约） 0-否 1-是")
    private Integer is_my_booking;

    @ApiModelProperty("是否即将释放")
    private boolean releasing;

    @ApiModelProperty("释放时间")
    private String releasing_date;

    @ApiModelProperty("预约操作人")
    private String operator;

    @ApiModelProperty("产品类型 5-闪屏cpt 6-闪屏gd 7-topview")
    private Integer order_product;

    @ApiModelProperty("产品名称")
    private String order_product_name;

    @ApiModelProperty("预定信息")
    private String desc;

    @ApiModelProperty("是否是有效预占排期（存在有效创意）")
    private Boolean has_valid_ssa;

}
