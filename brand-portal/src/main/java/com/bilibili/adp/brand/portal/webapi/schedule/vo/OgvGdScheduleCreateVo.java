package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 16:09
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OgvGdScheduleCreateVo extends OgvScheduleCreateVo implements Serializable {
    private static final long serialVersionUID = 3485937121028185301L;
    @ApiModelProperty("展示量")
    private Integer totalImpression;
    @ApiModelProperty("人群包（包含）")
    private List<Integer> crowdPackIds;
    @ApiModelProperty("人群包（排除）")
    private List<Integer> excludeCrowdPackIds;
    @ApiModelProperty("地域组id")
    private Integer areaGroupId;
}
