package com.bilibili.adp.brand.portal.webapi.component.vo;

import com.bilibili.brand.api.component.ComponentMarkDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/14 21:07
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ComponentMarkVo implements Serializable {
    private static final long serialVersionUID = -4662676020081882731L;
    @ApiModelProperty("组件标名称")
    private String markName;
    @ApiModelProperty("组件标logo地址")
    private String markUrl;

    public static ComponentMarkVo newFromBo(ComponentMarkDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        ComponentMarkVo vo = new ComponentMarkVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
}
