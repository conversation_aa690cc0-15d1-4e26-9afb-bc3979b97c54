/** 
* <AUTHOR> 
* @date  2018年3月7日
*/ 

package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("闪屏CPT排期信息")
public class UpdateSsaCptScheduleVo {
	@NotNull
	private int schedule_id;

	@NotBlank
	private String name;

	@NotEmpty
	private List<SsaScheduledRotationVo> schedules;

	@ApiModelProperty("人群包（包含）")
	private List<Integer> crowd_pack_ids;

	@ApiModelProperty("人群包（排除）")
	private List<Integer> exclude_crowd_pack_ids;

	@ApiModelProperty("此预约占整个轮数的比例,满轮是1000,半轮是500")
	private Integer booking_ratio;

	@ApiModelProperty("定向类型:-1-无定向 0-男性 1-女性")
	private Integer target_item;

	@ApiModelProperty("刊例周期id")
	private Integer cycle_id;
}
