package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TopViewCptPlusScheduleVo extends TopViewPlusScheduleVo implements Serializable {
    private static final long serialVersionUID = 5679846785182433268L;
    private int _NOT_USED_;
}
