package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.oval.constraint.NotNull;

import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LiveSourceGroupCptScheduleVo {
    @NotNull(message = "订单id不能为空")
    @ApiModelProperty("订单id")
    private Integer order_id;

    @NotNull(message = "排期名不能为空")
    @ApiModelProperty("排期名")
    private String name;

    @ApiModelProperty("推广目的")
    private Integer promotion_purpose_type;

    @ApiModelProperty("版本类型 0-标准版 1-增强版")
    private Integer version_type;

    @ApiModelProperty("排期方式 0-按天 1-按小时")
    private Integer schedule_style;

    @NotNull(message = "广告位id列表不能为空")
    @ApiModelProperty("广告位id列表")
    private List<Integer> source_ids;

    @NotNull(message = "日期列表不能为空")
    @ApiModelProperty("日期列表")
    private List<NewCptScheduleDateVo> dates;

    @ApiModelProperty("刊例周期id")
    private Integer cycle_id;

}
