package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("闪屏gd+库存服务定向信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaGdPlusStockTargetInfo {

    @ApiModelProperty("定向")
    private SsaScheduleTargetVo target;

    private Integer hour;

    private Boolean isTodaySchedule;

    @ApiModelProperty("频次单元(1-日 2-周 3-月)")
    private Integer frequencyUnit;

    @ApiModelProperty("频次限制 前端默认值改成4")
    private Integer frequencyLimit;

    @ApiModelProperty("处理序列号")
    private String dealSeq;

    @ApiModelProperty("订单id")
    private Integer orderId;

    @ApiModelProperty("人群包（包含）")
    private List<Integer> crowdPackIds;

    @ApiModelProperty("人群包（排除）")
    private List<Integer> excludeCrowdPackIds;

    @ApiModelProperty("查询时间")
    private List<GDSplitDaysImpressVo> splitDaysImpressVos;

    @ApiModelProperty("展示方式，0：常规冷启，1：首刷冷启")
    private Integer displayMode;

    @ApiModelProperty("闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频")
    private Integer ssaVideoPlayMode;

    @ApiModelProperty("闪屏联动，0：无（常规闪屏），1：默认搜索词")
    private Integer ssaLinkageType;

    @ApiModelProperty("闪屏按钮类型")
    private Integer buttonStyle;

}
