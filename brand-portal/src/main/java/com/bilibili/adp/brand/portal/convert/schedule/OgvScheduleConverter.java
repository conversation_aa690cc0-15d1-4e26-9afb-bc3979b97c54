package com.bilibili.adp.brand.portal.convert.schedule;

import com.bilibili.adp.brand.portal.webapi.resource.vo.OgvEpisodeVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.OgvResourceStickerVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.OgvSeasonVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.brand.api.resource.ogv.OgvEpisodeDto;
import com.bilibili.brand.api.resource.ogv.OgvResourceStickerDto;
import com.bilibili.brand.api.resource.ogv.OgvSeasonDto;
import com.bilibili.brand.api.schedule.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/13 10:46
 */
@Mapper(uses = {SplitDaysImpressConverter.class},
        imports = {com.bilibili.adp.brand.portal.convert.schedule.TargetConvert.class,
                com.bilibili.brand.util.TimeUtil.class})
public interface OgvScheduleConverter {
    OgvScheduleConverter MAPPER = Mappers.getMapper(OgvScheduleConverter.class);

    @Mapping(target = "targets", expression = "java(TargetConvert.buildTargetRuleList(vo.getTarget()))")
    @Mapping(target = "beginTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getEndTime()))")
    OgvCptScheduleCreateDto toOgvCptScheduleCreateDto(OgvCptScheduleCreateVo vo);

    @Mapping(target = "targets", expression = "java(TargetConvert.buildTargetRuleList(vo.getTarget()))")
    @Mapping(target = "beginTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getEndTime()))")
    OgvCptScheduleUpdateDto toOgvCptScheduleUpdateDto(OgvCptScheduleUpdateVo vo);

    @Mapping(target = "targets", expression = "java(TargetConvert.buildTargetRuleList(vo.getTarget()))")
    @Mapping(target = "beginTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getEndTime()))")
    OgvGdScheduleCreateDto toOgvGdScheduleCreateDto(OgvGdScheduleCreateVo vo);

    @Mapping(target = "targets", expression = "java(TargetConvert.buildTargetRuleList(vo.getTarget()))")
    @Mapping(target = "beginTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(TimeUtil.isoTimeStr2Timestamp(vo.getEndTime()))")
    OgvGdScheduleUpdateDto toOgvGdScheduleUpdateDto(OgvGdScheduleUpdateVo vo);

    List<OgvSeasonVo> toOgvSeasonVo(List<OgvSeasonDto> dto);

    @Mapping(target = "beginTime", expression = "java(TimeUtil.timestampToIsoDateStr(dto.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(TimeUtil.timestampToIsoDateStr(dto.getEndTime()))")
    OgvSeasonVo toOgvSeasonVo(OgvSeasonDto dto);

    List<OgvEpisodeVo> toOgvEpisodeVo(List<OgvEpisodeDto> dto);

    OgvEpisodeVo toOgvEpisodeVo(OgvEpisodeDto dto);

    List<OgvResourceStickerVo> toOgvResourceStickerVo(List<OgvResourceStickerDto> dto);

    OgvResourceStickerVo toOgvResourceStickerVo(OgvResourceStickerDto dto);


    ScheduleOgvVo toScheduleOgvVo(ScheduleOgvDto dto);

}
