package com.bilibili.adp.brand.portal.webapi.cycle.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/14 11:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CycleVo implements Serializable {
    private static final long serialVersionUID = 3903100080179997488L;
    @ApiModelProperty("订单类型")
    private Integer orderProduct;
    @ApiModelProperty("刊例周期id")
    private Long cycleId;
    @ApiModelProperty("刊例周期名称")
    private String name;
}
