package com.bilibili.adp.brand.portal.webapi.mock.vo.ssa;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ToString(callSuper = true)
public class SsaCreativeInfoReq extends  SsaBaseReq{

    @FieldDesc(summary = "闪屏创意id列表", desc = "需要更新的排期id列表")
    private List<Integer> ssaCreativeIds;

    @FieldDesc(summary = "滑动限制角度", desc = "[0,90]的角度值，整数")
    private Integer slideAngel;

}
