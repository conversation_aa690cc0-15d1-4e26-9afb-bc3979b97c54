package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2019/5/28.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewSsaGdScheduleVo {
    @NotNull
    private int order_id;
    @NotBlank
    private String name;
    @NotNull
    private int res_type;
    @NotNull
    private int ad_type;
    @NotNull
    private int promotion_purpose_type;

    @ApiModelProperty("定向")
    private SsaScheduleTargetVo target;

    private List<Timestamp> dates;


    @ApiModelProperty("合约展现量")
    private int total_impression;

    @ApiModelProperty("闪屏全半屏样式 1-全屏 2-半屏")
    @NotNull
    private Integer screen_style;

    @ApiModelProperty(value = "闪屏展示样式 1-全屏图片 2-半屏图片 3-半屏横屏视频 4-半屏竖屏视频 5-全屏竖屏视频")
    @NotNull
    private Integer show_style;

    @ApiModelProperty("素材点击区域 0-常规区域 1-半屏全素材区域")
    private Integer material_click_area;

    @ApiModelProperty("交互方式 0-点击交互 1-滑动交互")
    @NotNull
    private Integer interact_style;

    @ApiModelProperty("跳转模块样式 0-引导说明 1-引导说明+标题")
    @NotNull
    private Integer jump_area_style;

    @ApiModelProperty("跳转模块动效 0-无动效 1-跳动动效")
    @NotNull
    private Integer jump_area_effect;

    @ApiModelProperty("按钮类型 0-点击交互 1-滑动交互 2-选择交互")
    @NotNull
    private Integer button_style;
}
