package com.bilibili.adp.brand.portal.webapi.splash_screen.vo;

import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SplashScreenScheduleVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaSplashScreenVideoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalSplashScreenListVo {

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(notes = "合同名称")
    private String contract_name;

    @ApiModelProperty(notes = "合同号")
    private String contract_number;

    @ApiModelProperty("缩略图")
    private SplashScreenBaseImageVo base_image_vo;

    @ApiModelProperty(value = "闪屏标题")
    private String title;

    @ApiModelProperty(value = "ssa订单ID")
    private Integer ssa_order_id;

    @ApiModelProperty(value = "订单名称")
    private String order_name;

    @ApiModelProperty(value = "展示样式: 1-全屏 2-半屏")
    private Integer show_style;

    private Integer is_skip;

    @ApiModelProperty(value = "展示时长")
    private Integer show_time;

    @ApiModelProperty(value = "展示样式描述")
    private String show_style_desc;

    @ApiModelProperty(value = "1-待初审 2-待复审 3-已驳回 4-待上线 5-上线中 6-已暂停 7-已完成 8-已删除")
    private Integer status;

    @ApiModelProperty(value = "闪屏状态描述")
    private String status_desc;

    @ApiModelProperty(value = "驳回原因")
    private String reason;

    @ApiModelProperty(value = "生效日期")
    private List<SplashScreenScheduleVo> schedules;

    @ApiModelProperty(notes = "投放时段")
    private String time_segments;

    @ApiModelProperty(value = "广告角标ID")
    private Integer cm_mark;

    @ApiModelProperty(value = "广告角标名称")
    private String cm_mark_name;

    @ApiModelProperty(value = "刊例周期id")
    private Integer cycle_id;

    /**
     * 闪屏视频
     */
    @ApiModelProperty(value = "闪屏视频")
    private SsaSplashScreenVideoVo video;

    /**
     * 售卖类型
     */
    @ApiModelProperty(value = "售卖类型")
    private Integer sales_type;

    @ApiModelProperty(value = "实际展现量")
    private Long show_count;

    @ApiModelProperty(value = "实际点击量")
    private Integer click_count;

    @ApiModelProperty(value = "点击率")
    private BigDecimal click_rate;

    private Integer schedule_id;

    private String schedule_name;

    @ApiModelProperty(notes = "排期开始时间")
    private String schedule_begin_date;

    @ApiModelProperty(notes = "排期结束时间")
    private String schedule_end_date;

    private Integer order_id;

    @ApiModelProperty(value = "预览时间（时间戳，单位毫秒）")
    private Long preview_end_time;

    @ApiModelProperty(value = "预览状态: 1-不可预览 2-可预览 3-预览中")
    private Integer preview_status;

    @ApiModelProperty("创建人名称")
    private String creator_name;

    @ApiModelProperty("推广类型")
    private Integer promotion_purpose_type;

    @ApiModelProperty("推广类型描述")
    private String promotion_purpose_type_desc;

    @ApiModelProperty(value = "产品类型")
    private Integer order_product;

    @ApiModelProperty(value = "闪屏感兴趣量")
    private Long interested_count;

    @ApiModelProperty(value = "闪屏感兴趣率")
    private BigDecimal interested_ratio;

    @ApiModelProperty(value = "信息流首位曝光量")
    private Integer flow_show_count;

    @ApiModelProperty(value = "信息流平均播放时长")
    private Long flow_avg_play_time;

    @ApiModelProperty(value = "信息流ok点击量")
    private Long flow_click_count;

    @ApiModelProperty("闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频")
    private Integer ssa_video_play_mode;
}