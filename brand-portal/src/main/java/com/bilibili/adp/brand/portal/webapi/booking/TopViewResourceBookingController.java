package com.bilibili.adp.brand.portal.webapi.booking;

import com.bilibili.adp.brand.portal.webapi.booking.vo.BookingItemVo;
import com.bilibili.adp.brand.portal.webapi.booking.vo.CreateBookingItemVo;
import com.bilibili.adp.brand.portal.webapi.booking.vo.SourceItemVo;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.booking.dto.CptSsaDayBookingCellDto;
import com.bilibili.brand.api.booking.service.ISSABookingService;
import com.bilibili.brand.api.booking.service.ITopViewResourceService;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.cpt.platform.common.CptBookingStatus;
import com.bilibili.cpt.platform.common.CptConstants;
import com.bilibili.ssa.platform.biz.component.SsaLock;
import com.bilibili.ssa.platform.common.enums.BookingRatio;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by dailuwei on 2020/3/17 16:56
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/web_api/v1/booking/top_view")
@Api(value = "/booking", description = "TopView资源预定")
@Slf4j
public class TopViewResourceBookingController extends BaseController {

    @Autowired
    private ITopViewResourceService topViewResourceService;
    @Autowired
    private ISSABookingService ssaBookingService;

    @Autowired
    private SsaLock ssaLock;

    @Value("${new.topView.homeFocus.ios.sourceId:4334}")
    private Integer newTopViewHomeFocusIosSourceId;

    @Value("${new.topView.homeFocus.android.sourceId:4338}")
    private Integer newTopViewHomeFocusAndroidSourceId;


    @ApiOperation(value = "TopView新建预约")
    @RequestMapping(value = "/resource", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<String> createTopViewBookingItems(
            @ApiIgnore Context context,
            @ApiParam("预约信息")
            @RequestBody CreateBookingItemVo bookingItemVo
    ) {
        Assert.notNull(bookingItemVo, "error request");
        Assert.notNull(bookingItemVo.getGroup_dates(), "less time info");
        Assert.notNull(bookingItemVo.getTop_view_type(), "TopView类型不可为空");

        List<RLock> allLockList = getAllBookingLock();
        try {
            //开始预约
            Long relatedId = bookingItemVo.getRelated_id() == null ? 0 : Long.parseLong(bookingItemVo.getRelated_id());
            topViewResourceService.createTopViewBooking(bookingItemVo.getGroup_dates().stream()
                    .map(Timestamp::new)
                    .collect(Collectors.toList()), bookingItemVo.getTop_view_type(),
                    getOperator(context), bookingItemVo.getBooking_ratio(), relatedId);
        } finally {
            ssaLock.releaseBatchLock(allLockList);
        }

        return Response.SUCCESS("");
    }
    @ApiOperation(value = "TopView查询全部资源")
    @RequestMapping(value = "/resource", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<SourceItemVo>> queryTopViewResource(
            @ApiIgnore Context context,
            @ApiParam("开始时间")
            @RequestParam("begin_time") Long beginTime,
            @ApiParam("结束日期")
            @RequestParam("end_time") Long endTime,
            @ApiParam("type 0-普通topview 1-首刷topView")
            @RequestParam(value = "type", defaultValue = "0") Integer type) {

        Assert.notNull(beginTime, "beginTime can not be null");
        Assert.notNull(endTime, "endTime can not be null");
        List<List<CptSsaDayBookingCellDto>> cellsTables =  topViewResourceService.getTopResourceReserveList(
                new Timestamp(beginTime), new Timestamp(endTime), type, getOperator(context));

        List<SourceItemVo> vos = new ArrayList<>();
        Map<Integer, Long> bookingCellCreativeCountMap = ssaBookingService.getBookingCellCreativeCountMap(
                cellsTables.stream().flatMap(Collection::stream).collect(Collectors.toList()));

        List<Integer> ssaBookingIds = cellsTables.stream().flatMap(Collection::stream).filter(Objects::nonNull)
                .map(CptSsaDayBookingCellDto::getId).filter(Utils::isPositive)
                .distinct().collect(Collectors.toList());
        log.info("queryTopViewResource.ssaBookingIds.size={}", CollectionHelper.getSize(ssaBookingIds));

        // todo 待完善完释放逻辑
        List<CptSsaDayBookingCellDto> releasingBookingDtos = new ArrayList<>();
//        List<CptSsaDayBookingCellDto> releasingBookingDtos = ssaBookingService
//        .getAlertCptSsaDayBookingDtos(Utils.getToday(), ssaBookingIds, Collections.emptyList());
        log.info("queryTopViewResource.releasingBookingDtos.size={}", CollectionHelper.getSize(releasingBookingDtos));

        Map<Integer, CptSsaDayBookingCellDto> releasingCellDtoMap = releasingBookingDtos.stream()
                .collect(Collectors.toMap(CptSsaDayBookingCellDto::getId, Function.identity(), (a, b) -> a));

        for (List<CptSsaDayBookingCellDto> cellsTable : cellsTables) {
            SourceItemVo sourceItemVo = new SourceItemVo();
            sourceItemVo.setSource_id(0);
            sourceItemVo.setSource_name("TopView");
            List<CptSsaDayBookingCellDto> cellDtoList = new ArrayList<>();
            cellsTable.forEach(t->{
                if(BookingRatio.FULL.getCode().equals(t.getBookingRatio())
                        || BookingRatio.FIRST_BRUSH.getCode().equals(t.getBookingRatio())
                        || CptBookingStatus.BOOKABLE.getCode().equals(t.getStatus())
                        ||CptBookingStatus.LESS_RESOURCE_TOP_VIEW.getCode().equals(t.getStatus())){
                    cellDtoList.add(t);
                }else {
                    cellDtoList.addAll(t.getRelatedList());
                }
            });
            sourceItemVo.setItems(cellDtoList.stream().map(dto -> {
                boolean isMyBooking = context.getAccountId().equals(dto.getAccountId());
                if (!CptBookingStatus.BOOKABLE.getCode().equals(dto.getStatus())
                        && !isMyBooking) {
                    dto.setStatus(CptBookingStatus.LESS_RESOURCE_TOP_VIEW.getCode());
                }
                BookingItemVo vo = new BookingItemVo();
                CptSsaDayBookingCellDto releasingCellDto = releasingCellDtoMap.get(dto.getId());
                vo.setBooking_id(dto.getId());
                vo.setDate(dto.getGroupDate().getTime());
                vo.setBooking_status(dto.getStatus());
                vo.setBooking_status_desc(CptBookingStatus.getByCode(dto.getStatus()).getDesc());
                vo.setDependency_order_id(dto.getGdOrderId());
                vo.setDependency_order_name(dto.getOrderName());
                vo.setDependency_schedule_id(dto.getGdScheduleId());
                vo.setBusiness_side_name(dto.getAccountName());
                vo.setIs_my_booking(isMyBooking ? 1 : 0);
                vo.setCreative_count(bookingCellCreativeCountMap.getOrDefault(dto.getId(), 0L));
                vo.setTop_view_source_Id(dto.getTopViewSourceId());
                vo.setResource_type(dto.getResourceType());
                vo.setProduct(OrderProduct.TOP_VIEW_CPT.getCode());
                if (releasingCellDto != null) {
                    vo.setReleasing(true);
                    vo.setReleasing_date(StringDateParser.getDateString(releasingCellDto.getReleasingDate()));
                }
                vo.setOperator(dto.getAccountId().equals(context.getAccountId()) ? dto.getOperator() : null);
                vo.setBooking_ratio(dto.getBookingRatio());
                vo.setRelated_id(dto.getRelatedId().toString());
                return vo;
            }).collect(Collectors.toList()));
            vos.add(sourceItemVo);
        }
        return Response.SUCCESS(vos);

    }
    @ApiOperation(value = "取消预约")
    @RequestMapping(value = "/resource", method = RequestMethod.DELETE)
    public
    @ResponseBody
    Response<Integer> deleteBookingItems(
            @ApiIgnore Context context,
            @ApiParam("预约ID") @RequestParam(value = "booking_id") Integer bookingId
    ) {
        List<RLock> allLockList = getAllBookingLock();
        try {
            topViewResourceService.deleteResourceBookingById(bookingId, super.getOperator(context));
        } finally {
            ssaLock.releaseBatchLock(allLockList);
        }
        return Response.SUCCESS(1);
    }

    private List<RLock> getAllBookingLock() {
        List<RLock> allLockList = new ArrayList<>(4);
        allLockList.add(ssaLock.getLock(newTopViewHomeFocusAndroidSourceId, CptConstants.BOOKING_LOCK_SUFFIX));
        allLockList.add(ssaLock.getLock(newTopViewHomeFocusIosSourceId, CptConstants.BOOKING_LOCK_SUFFIX));
        allLockList.add(ssaLock.getLock("", "ssa:booking"));
        allLockList.add(ssaLock.getLock("", "topView:booking"));
        return allLockList;
    }
}
