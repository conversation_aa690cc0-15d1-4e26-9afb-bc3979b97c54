/**
 * <AUTHOR>
 * @date 2018年5月15日
 */

package com.bilibili.adp.brand.portal.webapi.mock;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.brand.portal.webapi.mock.vo.StoreVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.ImageVo;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.booking.service.ISSABookingService;
import com.bilibili.brand.api.launch.dto.BfsFile;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.*;
import com.bilibili.brand.api.schedule.soa.dto.GdScheduleDailyDto;
import com.bilibili.brand.api.schedule.soa.service.ISoaGdScheduleService;
import com.bilibili.brand.biz.creative.dao.GdCreativeCustomizedUrlDao;
import com.bilibili.brand.biz.creative.dao.GdCreativeDao;
import com.bilibili.brand.biz.creative.dao.GdCreativeMonitoringDao;
import com.bilibili.brand.biz.creative.po.GdCreativeCustomizedUrlPo;
import com.bilibili.brand.biz.creative.po.GdCreativeMonitoringPo;
import com.bilibili.brand.biz.creative.po.GdCreativePo;
import com.bilibili.brand.biz.order.dao.FcOrderDao;
import com.bilibili.brand.biz.order.po.FcOrderPo;
import com.bilibili.brand.biz.order.po.FcOrderPoExample;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.brand.biz.proto.ImageUtil;
import com.bilibili.brand.biz.resource.dao.AccountGroupCmMarkDao;
import com.bilibili.brand.biz.resource.dao.SlotDao;
import com.bilibili.brand.biz.resource.po.AccountGroupCmMarkPo;
import com.bilibili.brand.biz.resource.po.SlotPo;
import com.bilibili.brand.biz.resource.pojo.ResTargetItemPo;
import com.bilibili.brand.biz.resource.pojo.ResTargetItemPoExample;
import com.bilibili.brand.biz.resource.res_dao.ResTargetItemDao;
import com.bilibili.brand.biz.schedule.dao.GdFlowAllocationDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTargetDao;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTargetRatioDao;
import com.bilibili.brand.biz.schedule.handler.ScheduleTargetHandler;
import com.bilibili.brand.biz.schedule.po.*;
import com.bilibili.brand.biz.schedule.service.GdPlusScheduleService;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.brand.biz.schedule.service.ScheduleService;
import com.bilibili.brand.biz.schedule.service.ScheduleServiceDelegate;
import com.bilibili.brand.biz.utils.BrandLittleAssistantUtil;
import com.bilibili.brand.schedule.raw.DirectoryStore;
import com.bilibili.brand.schedule.raw.InventoryStore;
import com.bilibili.brand.schedule.raw.Target;
import com.bilibili.brand.schedule.raw.TargetEntry;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.api.creative.dto.CptCreativeAllInfoDto;
import com.bilibili.cpt.platform.api.creative.dto.QueryCreativeParamDto;
import com.bilibili.cpt.platform.api.creative.service.ICptCreativeService;
import com.bilibili.cpt.platform.api.location.dto.CptSourceAllInfoDto;
import com.bilibili.cpt.platform.api.location.service.ICptSourceService;
import com.bilibili.cpt.platform.biz.dao.CptArchivePackageDao;
import com.bilibili.cpt.platform.biz.dao.CptArchivePackageMappingDao;
import com.bilibili.cpt.platform.biz.dao.CptSourceConfigDao;
import com.bilibili.cpt.platform.biz.enumerate.PlatformType;
import com.bilibili.cpt.platform.biz.po.CptArchivePackageMappingPo;
import com.bilibili.cpt.platform.biz.po.CptArchivePackagePo;
import com.bilibili.cpt.platform.biz.po.CptArchivePackagePoExample;
import com.bilibili.cpt.platform.biz.po.CptSourceConfigPo;
import com.bilibili.cpt.platform.biz.service.CptBaseService;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.cpt.platform.util.OkHttpUtils;
import com.bilibili.crm.platform.api.order.dto.UpdateGdOrderDto;
import com.bilibili.location.api.service.ISourceService;
import com.bilibili.location.api.service.query.IQuerySourceService;
import com.bilibili.location.api.source.dto.SourceAllInfoDto;
import com.bilibili.ssa.platform.api.location.dto.SsaSourceAllInfoDto;
import com.bilibili.ssa.platform.api.location.dto.TopViewSourceConfigDto;
import com.bilibili.ssa.platform.api.location.service.ISsaSourceService;
import com.bilibili.ssa.platform.api.location.service.ITopViewSourceService;
import com.bilibili.ssa.platform.api.splash_screen.dto.QuerySplashScreenParamDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenImageService;
import com.bilibili.ssa.platform.biz.dao.*;
import com.bilibili.ssa.platform.biz.po.*;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenService;
import com.bilibili.ssa.platform.common.enums.SsaConstants;
import com.bilibili.ssa.platform.common.enums.SsaSplashScreenStatus;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.JedisCluster;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/mock")
public class MockController extends CptBaseService {

    @Autowired
    private IQuerySourceService cptSourceService;

    @Autowired
    private ICptSourceService cptService;

    @Autowired
    private SlotDao slotDao;

    @Autowired
    private CptSourceConfigDao sourceConfigDao;

    @Autowired
    private ISsaSplashScreenImageService ssaSplashScreenImageService;

    @Autowired
    private ISSABookingService ssaBookingService;

    @Autowired
    private InventoryStore inventoryStore;

    @Autowired
    private JedisCluster jedisCluster;

    @Autowired
    private DirectoryStore directoryStore;


    @Qualifier("soaGdScheduleService")
    @Autowired
    private ISoaGdScheduleService soaSsaSchedule;

    @Autowired
    private ICptCreativeService cptCreativeService;

    @Autowired
    private ScheduleServiceDelegate scheduleServiceDelegate;

    @Autowired
    private ITopViewSourceService topViewSourceService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private FcOrderDao fcOrderDao;

    @Autowired
    private GdScheduleDao scheduleDao;

    @Autowired
    private GdScheduleTargetDao gdScheduleTargetDao;

    @Autowired
    private ResTargetItemDao targetItemDao;

    @Autowired
    private SsaScheduleDao ssaScheduleDao;

    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private SsaSplashScreenService ssaSplashScreenService;

    @Autowired
    private CptArchivePackageMappingDao packageMappingDao;

    @Autowired
    private CptArchivePackageDao packageDao;

    @Autowired
    private ISourceService sourceService;

    @Autowired
    private CptSourceConfigDao cptSourceConfigDao;

    @Autowired
    private GdFlowAllocationDao gdFlowAllocationDao;

    @Autowired
    private QueryScheduleService queryScheduleService;

    @Autowired
    private GdScheduleTargetRatioDao gdScheduleTargetRatioDao;

    @Autowired
    private SsaCycleDao ssaCycleDao;


    @Autowired
    private SsaSplashScreenDao ssaSplashScreenDao;

    @Autowired
    private SsaFlowAllocationDao flowAllocationDao;

    @Autowired
    private GdOrderService orderService;

    @Autowired
    private SsaCpmScheduleDao cpmScheduleDao;

    @Autowired
    private GdCreativeMonitoringDao monitoringDao;

    @Autowired
    private GdCreativeCustomizedUrlDao customizedUrlDao;

    @Autowired
    private AccountGroupCmMarkDao accountGroupCmMark;

    @Autowired
    private GdCreativeDao gdCreativeDao;

    @Autowired
    private ISsaSourceService ssaSourceService;

    @Autowired
    private SsaPriceConfigDao ssaPriceConfigDao;

    @Autowired
    private IGdOrderService gdOrderService;
    @Autowired
    private SsaSplashScreenVersionControlDao ssaSplashScreenVersionControlDao;
    @Autowired
    private GdScheduleDao gdScheduleDao;

    public void updateScheduleFrequencyLimit(int scheduleId, int limit) {
        GdSchedulePoExample scheduleExample = new GdSchedulePoExample();
        scheduleExample.or()
                .andScheduleIdEqualTo(scheduleId)
                .andIsDeletedEqualTo(0);

        GdSchedulePo schedulePo = new GdSchedulePo();
        schedulePo.setFrequencyLimit(limit);
        gdScheduleDao.updateByExampleSelective(schedulePo, scheduleExample);
    }

    @Autowired
    private SsaGdPriceDao ssaGdPriceDao;

    @Autowired
    private TopViewSourceConfigDao topViewSourceConfigDao;
    @Autowired
    private ScheduleTargetHandler targetHandler;
    @Autowired
    private GdScheduleTargetDao scheduleTargetDao;
    @Autowired
    private GdPlusScheduleService gdPlusScheduleService;

    @Autowired
    private IBfsService bfsService;

    @Value("${bfs.categoryname}")
    private String categoryName;

    @Autowired
    private OkHttpClient okHttpClient;

    public void addRatio(List<Integer> scheduleIds, int inlineSalesType) {
        GdScheduleTargetRatioPoExample example = new GdScheduleTargetRatioPoExample();
        example.or()
                .andScheduleIdIn(scheduleIds)
                .andIsDeletedEqualTo(0);
        List<GdScheduleTargetRatioPo> pos = gdScheduleTargetRatioDao.selectByExample(example);
        List<Integer> needProcessScheduleIds = scheduleIds.stream()
                .filter(scheduleId -> pos.stream().noneMatch(po -> po.getScheduleId().equals(scheduleId)))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(needProcessScheduleIds)) {
            return;
        }

        List<ScheduleDto> scheduleDtos = queryScheduleService.queryBaseSchedule(QueryScheduleDto.builder().scheduleIds(needProcessScheduleIds).build());

        TargetRule rule = new TargetRule();
        rule.setRuleType(TargetType.INLINE_SALES_TYPE.getCode());
        List<TargetRule> targets = Collections.singletonList(TargetRule.builder()
                .ruleType(TargetType.INLINE_SALES_TYPE.getCode())
                .valueIds(Collections.singletonList(inlineSalesType))
                .build());

        scheduleDtos.forEach(schedule -> {
            NewScheduleDto newScheduleDto = NewScheduleDto.builder()
                    .targetRules(targets)
                    .platformIds(Objects.equals(schedule.getPlatformId(), com.bilibili.enums.PlatformType.IPHONE_ANDROID.getCode()) ?
                            Arrays.asList(1, 2) : Collections.singletonList(schedule.getPlatformId()))
                    .templateId(schedule.getTemplateId())
                    .scheduleId(schedule.getScheduleId())
                    .gdType(schedule.getGdType())
                    .beginTime(schedule.getGdBeginTime())
                    .totalImpression(Long.valueOf(schedule.getTotalImpression()))
                    .build();

            gdPlusScheduleService.saveGdTargetRatio(newScheduleDto);
        });
    }

    public void addSsaScheduleOsTarget() {

        Timestamp start = TimeUtil.toTimestamp(LocalDate.of(2023, 1, 1).atStartOfDay());
        Timestamp end = TimeUtil.toTimestamp(LocalDate.of(2023, 1, 31).atStartOfDay());

        List<ScheduleDto> ssaScehdules = queryScheduleService.querySchedule(QueryScheduleDto.builder()
                .salesTypes(Arrays.asList(SalesType.SSA_CPT_PLUS.getCode(), SalesType.SSA_GD_PLUS.getCode()))
                .beginDate(start)
                .endDate(end)
                .timeContains(true)
                .statusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                .build());

        List<TargetRule> osTargets = new LinkedList<>();
        osTargets.add(TargetRule.builder()
                .ruleType(TargetType.OS.getCode())
                .valueIds(Arrays.asList(398, 399, 421))
                .build());

        ssaScehdules.forEach(schedule-> {
            if (schedule.getAccountId() == 3) {
                return;
            }

            GdScheduleTargetPoExample example = new GdScheduleTargetPoExample();
            example.or()
                    .andScheduleIdEqualTo(schedule.getScheduleId())
                    .andTargetTypeEqualTo(TargetType.OS.getCode());
            long osTargetCount = scheduleTargetDao.countByExample(example);
            if (osTargetCount != 0) {
                return;
            }
            targetHandler.addTarget(osTargets, schedule.getScheduleId());
        });

    }

    /**
     * {"topViewCycleId":194,"salesType":74,"ssaShowType":6,"externalPrice":10000,"ssaScreenStyle":1,"bannerShowType":1,"status":1,"ottScreenType":0}
     */
    public void insertTopviewPrice(TopViewSourceConfigPo po) {
        topViewSourceConfigDao.insertUpdateSelective(po);
    }

    public void insertSsaGdPrice(MockSsaGdPrice info) {

        SsaGdPricePo po = new SsaGdPricePo();
        po.setCycleId(info.getCycleId());
        po.setDisplayMode(info.getDisplayMode());
        po.setOrderProduct(info.getOrderProduct());
        po.setBasePrice(info.getPrice() * 100);
        po.setShowStyle(info.getShowStyle());
        po.setId(info.getId());
        ssaGdPriceDao.insertUpdateSelective(po);
    }


    @ApiOperation(value = "更新闪屏创意版本")
    @RequestMapping(value = "/update_ssa_creative_version", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<String> updateSsaCreativeVersion(@RequestParam(value = "order_ids", required = false) List<Integer> orderIds,
                                              @RequestParam(value = "schedule_ids", required = false) List<Integer> scheduleIds,
                                              @RequestParam(value = "splash_screen_ids", required = false) List<Integer> splashScreenIds,
                                              //1-iphone,2-android,3-ipad
                                              @RequestParam("platform") Integer platform,
                                              @RequestParam("start_version") Integer startVersion,
                                              @RequestParam(value = "end_version", required = false) Integer endVersion)
            throws ServiceException {

        if (CollectionUtils.isEmpty(orderIds) && CollectionUtils.isEmpty(scheduleIds) && CollectionUtils.isEmpty(splashScreenIds)) {
            return Response.SUCCESS("订单id，排期id，创意id不可以都为空");
        }

        QuerySplashScreenParamDto param = new QuerySplashScreenParamDto();
        if (!CollectionUtils.isEmpty(orderIds)) {
            param.setGdOrderIds(orderIds);
        }
        if (!CollectionUtils.isEmpty(scheduleIds)) {
            param.setGdScheduleIds(scheduleIds);
        }
        if (!CollectionUtils.isEmpty(splashScreenIds)) {
            param.setSplashScreenIdList(splashScreenIds);
        }

        List<SsaSplashScreenDto> ssaSplashScreens = ssaSplashScreenService.getSsaSplashScreens(param);
        if (CollectionUtils.isEmpty(ssaSplashScreens)) {
            return Response.SUCCESS("未查询到创意");
        }

        SsaSplashScreenVersionControlPo updateInfo = new SsaSplashScreenVersionControlPo();
        updateInfo.setStartVersion(startVersion);
        if (endVersion != null) {
            updateInfo.setEndVersion(endVersion);
        }


        SsaSplashScreenVersionControlPoExample example = new SsaSplashScreenVersionControlPoExample();
        example.or()
                .andSplashScreenIdIn(ssaSplashScreens.stream().map(SsaSplashScreenDto::getId).distinct().collect(Collectors.toList()))
                .andPlatformIdEqualTo(platform)
                .andIsDeletedEqualTo(0);
        ssaSplashScreenVersionControlDao.updateByExampleSelective(updateInfo, example);

        List<SsaSplashScreenVersionControlPo> ssaSplashScreenVersionControlPos = ssaSplashScreenVersionControlDao.selectByExample(example);

        return Response.SUCCESS(String.format("版本修改成功，修改后数据为：{%s}", JSONObject.toJSONString(ssaSplashScreenVersionControlPos, true)));
    }

    @ApiOperation(value = "更新订单信息到crm")
    @RequestMapping(value = "/syn_order", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<String> synOrderInfoToCrm(@RequestParam("order_ids") List<Integer> orderIds,
                                       @RequestParam("account_id") Integer accountId)
            throws ServiceException {

        Operator operator = new Operator();
        operator.setBilibiliUserName("brand_system_syn_order");
        operator.setOperatorId(accountId);
        operator.setOperatorName("brand_system_syn_order");
        operator.setOperatorType(OperatorType.SYSTEM);

        for (Integer orderId : orderIds) {
            GdOrderDto order = gdOrderService.getOrderById(orderId, operator);
            UpdateGdOrderDto updateGdOrderDto = scheduleServiceDelegate.updateGdOrder(order, operator);
            scheduleServiceDelegate.updateCrmGdOrder(updateGdOrderDto, operator);
        }

        return Response.SUCCESS("订单同步成功");
    }

    @ApiOperation(value = "复制闪屏cpt首刷的价格")
    @RequestMapping(value = "/copy/ssa/cpt/price", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateSchedule(@RequestParam("cycle_id")Integer cycle_id)
            throws ServiceException {
        List<SsaSourceAllInfoDto> allInfoDtos = ssaSourceService
                .getSourcesByCycleId(cycle_id, SalesType.SSA_CPT.getCode());
        SsaPriceConfigPoExample poExample = new SsaPriceConfigPoExample();
        poExample.or().andIsDeletedEqualTo(0).andSourceConfigIdIn(allInfoDtos.stream()
                .map(SsaSourceAllInfoDto::getSourceConfigId).collect(Collectors.toList()));

        List<SsaPriceConfigPo> ssaPriceConfigPos = ssaPriceConfigDao.selectByExample(poExample);
        ssaPriceConfigPos.forEach(t->{
            t.setDisplayMode(1);
            t.setId(null);
            ssaPriceConfigDao.insertUpdateSelective(t);
        });

        return Response.SUCCESS();
    }

    @ApiOperation(value = "更新排期")
    @RequestMapping(value = "/update/schedule/v1", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateSchedule(@RequestParam("schedule_id")Integer schedule_id,
                                  @RequestParam("cpms")Integer cpms,
                                  @RequestParam(value = "external_price", required = false)Long external_price)
            throws ServiceException {
        GdSchedulePo schedulePo = GdSchedulePo.builder()
                .scheduleId(schedule_id).totalImpression(cpms).build();
        if(Utils.isPositive(external_price)){
            schedulePo.setExternalPrice(external_price);
            schedulePo.setInternalPrice(external_price);
        }
        scheduleDao.updateByPrimaryKeySelective(schedulePo);
        return Response.SUCCESS();
    }

    @ApiOperation(value = "更新直播")
    @RequestMapping(value = "/update/live", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateLive(@RequestParam("id")String id,
                                  @RequestParam("jump_type")Integer jump_type,
                                  @RequestParam("content")String content) throws ServiceException {
        gdCreativeDao.updateByPrimaryKeySelective(GdCreativePo.builder().id(Integer.parseInt(id))
                .jumpType(jump_type)
                .promotionPurposeContent(content).build());
        return Response.SUCCESS();
    }

    @ApiOperation(value = "账户新增广告标")
    @RequestMapping(value = "/insert/cm/mark", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> insertCmMarkAccount(@RequestParam("account_group_id")Integer account_group_id,
                                       @RequestParam("cm_mark")Integer cm_mark,
                                       @RequestParam("mark_type")Integer mark_type) {
        accountGroupCmMark.batchInsert(Lists.newArrayList(AccountGroupCmMarkPo.builder()
                .accountGroupId(account_group_id).cmMarkId(cm_mark).markType(mark_type).build()));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "删除多余的监测链接")
    @RequestMapping(value = "/delete/monitor", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> deleteMonitor(@RequestParam("id")Integer id,
                                 @RequestParam("type")Integer type) throws ServiceException {
        if(type == 0) {
            monitoringDao.updateByPrimaryKeySelective(GdCreativeMonitoringPo.builder().id(id)
                    .isDeleted(1).build());
        }else {
            customizedUrlDao.updateByPrimaryKeySelective(GdCreativeCustomizedUrlPo.builder()
                    .id(id).isDeleted(1).build());
        }
        return Response.SUCCESS();
    }

    @ApiOperation(value = "删除gd分配表")
    @RequestMapping(value = "/update/ssa/status", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateSsaStatus(@RequestParam("id")Integer id) throws ServiceException {
        ssaSplashScreenService.updateCompleteOrOnLineStatus(Lists.newArrayList(id), SsaSplashScreenStatus.ON_LINE);
        return Response.SUCCESS();
    }

    @ApiOperation(value = "刷新合同对应的账户id")
    @RequestMapping(value = "/refresh/account/id", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> refreshAccountId(@RequestParam("date")String date) throws IOException {

        List<ScheduleDto> scheduleDtos = queryScheduleService.querySchedule(QueryScheduleDto.builder()
                .salesTypes(SsaConstants.SSA_PLUS_SALES_TYPES)
                .startTime(TimeUtil.isoStrToTimestamp(date))
                .statusList(Lists.newArrayList(1,2))
                .build());

        List<Integer> orderIds = scheduleDtos.stream().map(ScheduleDto::getOrderId)
                .collect(Collectors.toList());
        List<Integer> scheduleIds = scheduleDtos.stream().map(ScheduleDto::getScheduleId)
                .collect(Collectors.toList());
        Map<Integer, GdOrderDto> orderDtoMap = orderService.getOrderMapInOrderIds(orderIds);

        SsaCpmSchedulePoExample poExample = new SsaCpmSchedulePoExample();
        poExample.or().andGdScheduleIdIn(scheduleIds);
        List<SsaCpmSchedulePo> cpmSchedulePos = cpmScheduleDao.selectByExample(poExample);
        Map<Integer, SsaCpmSchedulePo> schedulePosMap = cpmSchedulePos.stream()
                .collect(Collectors.toMap(SsaCpmSchedulePo::getGdScheduleId, Function.identity()));
        scheduleDtos.forEach(t->{
            GdOrderDto orderDto = orderDtoMap.get(t.getOrderId());
            SsaCpmSchedulePo schedulePo = schedulePosMap.get(t.getScheduleId());
            if(!orderDto.getContractAccountId().equals(schedulePo.getContractAccountId())){
                schedulePo.setContractAccountId(orderDto.getContractAccountId());
                cpmScheduleDao.updateByPrimaryKeySelective(schedulePo);
            }
        });
        return Response.SUCCESS();
    }

    @ApiOperation(value = "ssa+添加ratio")
    @RequestMapping(value = "/ssa/add/ratio", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> addSsaRatio(@RequestParam("id")Integer id) throws ServiceException {
        SsaCyclePo cyclePo =  ssaCycleDao.selectByPrimaryKey(id);
        int innerRatio = systemConfigService.getValueReturnInt(SystemConfigEnum
                .SSA_PLUS_INNER_FLOW_DEFAULT_RATIO.getCode());
        LocalDate beginDate = TimeUtil.timestampToLocalDate(cyclePo.getBeginTime());
        LocalDate endDate = TimeUtil.timestampToLocalDate(cyclePo.getEndTime());
        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<SsaFlowAllocationPo> allocationPos = new ArrayList<>();
        for (; beginDate.isBefore(endDate); beginDate = beginDate.plusDays(1)){
            allocationPos.add(SsaFlowAllocationPo.builder().cycleId(id)
                    .innerAllocRatio(innerRatio).externalAllocRatio(1000 - innerRatio)
                    .launchDate(TimeUtil.localDateToTimestamp(beginDate)).isDeleted(IsDeleted.VALID.getCode())
                    .ctime(now).mtime(now).build());
        }
        flowAllocationDao.insertUpdateBatch(allocationPos);
        return Response.SUCCESS();
    }

    @ApiOperation(value = "删除gd分配表")
    @RequestMapping(value = "/update/ssa/rule", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateSsa(@RequestParam("id")Integer id,
                               @RequestParam("source")Integer sourceType) throws ServiceException {
        ssaSplashScreenDao.updateByPrimaryKeySelective(SsaSplashScreenPo.builder()
                .id(id).sourceType(sourceType)
                .build());
        return Response.SUCCESS();
    }


    @ApiOperation(value = "插入gd分配表")
    @RequestMapping(value = "/insert/gd", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> insertAd(@RequestParam("id")Integer id,
                           @RequestParam("source")Integer source) throws ServiceException {
        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(id);
        gdFlowAllocationDao.insertUpdateSelective(GdFlowAllocationPo.builder()
                .cpms(scheduleDto.getTotalImpression()).launchDay(scheduleDto.getBeginDate())
                .scheduleId(id).source(source).build());
        return Response.SUCCESS();
    }

    @ApiOperation(value = "插入gd分配ratio表")
    @RequestMapping(value = "/insert/gd/target", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> insertAdtarget(@RequestParam("id")Integer id,
                            @RequestParam("source")Integer source) throws ServiceException {
        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(id);
        gdScheduleTargetRatioDao.insertUpdateSelective(GdScheduleTargetRatioPo.builder()
                .showCount(scheduleDto.getTotalImpression()).launchDay(scheduleDto.getBeginDate())
                .innerTargetKeyId(0).ratio(120)
                .scheduleId(id).source(source).build());
        return Response.SUCCESS();
    }


    @ApiOperation(value = "闪屏更改预加载时间")
    @RequestMapping(value = "/update/preload", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> preload(@RequestParam("id")Integer id,
                           @RequestParam("date")String date) throws ServiceException {
        splashScreenDao.updateByPrimaryKeySelective(SsaSplashScreenPo.builder().id(id)
                .preloadTime(new Timestamp(TimeUtil.isoStrToTimestamp(date).getTime()
                - 7*24*60*60*1000)).creativeSizeEnablePreload(1)
                .creativeTypeEnablePreload(1).build());
        return Response.SUCCESS();
    }


    @ApiOperation(value = "ogv视频稿件刷库")
    @RequestMapping(value = "/refresh/ogv/aid", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Integer> updateSchedulePrice(@RequestParam("pack_id")Integer id,
                                          @RequestParam("pack_name")String packName,
                                          @RequestParam("path")String path) throws IOException {

        CptArchivePackagePoExample poExample = new CptArchivePackagePoExample();
        poExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdEqualTo(id);
        long count = packageDao.countByExample(poExample);
        if(count == 0){
            CptArchivePackagePo record = CptArchivePackagePo.builder()
                    .id(id).archivePackageName(packName).build();
            packageDao.insertSelective(record);
            id = record.getId();
        }

        List<CptArchivePackageMappingPo> mappingPos = new ArrayList<>();

        InputStream in  = new FileInputStream(path);
        Workbook wb = new XSSFWorkbook(in);

        //得到sheet
        Sheet sheet = wb.getSheetAt(0); //默认取第一个sheet
        //int colsNum = sheet.getPhysicalNumberOfRows();  //获取实际的行数
        int rowsNum = sheet.getLastRowNum();//
        Timestamp now = new Timestamp(System.currentTimeMillis());
        for(int j=0; j<rowsNum+1;j++) //第一行为表头，所以从第二行开始
        {// getLastRowNum，获取最后一行的行标
            Row row =sheet.getRow(j);
            if (row != null) {
                Cell cell =  row.getCell(0);
                if(cell.getCellType() == Cell.CELL_TYPE_NUMERIC || cell.getCellType() == Cell.CELL_TYPE_FORMULA){
                    cell.setCellType(Cell.CELL_TYPE_STRING);
                }
                mappingPos.add(CptArchivePackageMappingPo.builder().archivePackId(id).isDeleted(0)
                        .avid(Long.parseLong(cell.toString())).ctime(now).mtime(now).build());
                if(mappingPos.size() == 20) {
                    packageMappingDao.insertUpdateBatch(mappingPos);
                    mappingPos = new ArrayList<>();
                }
            }

        }
        wb.close();
        packageMappingDao.insertUpdateBatch(mappingPos);

        return Response.SUCCESS(id);
    }

    @ApiOperation(value = "cpt添加资源位")
    @RequestMapping(value = "/add/cpt/source/config", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> addCptPrice(@RequestParam("sourceIds")String sourceIds,
                               @RequestParam("cycleId")Integer cycleId,
                               @RequestParam("mFreqLimit")Integer mFreqLimit,
                               @RequestParam("rotationNum")Integer rotationNum,
                               @RequestParam("externalPrice")Integer externalPrice,
                               @RequestParam("internalPrice")Integer internalPrice) throws ServiceException {
        String[] slotIds = sourceIds.split(",");
        for(String slotId : slotIds){
            SourceAllInfoDto infoDto = sourceService.getSourceAllInfoBySourceId(Integer.parseInt(slotId));
            if(infoDto == null){
                continue;
            }
            cptSourceConfigDao.insertUpdateSelective(CptSourceConfigPo.builder()
                    .sourceId(infoDto.getSourceId()).cycleId(cycleId).salesType(31).platformId(infoDto.getPlatformId())
                    .pageId(infoDto.getPageId()).resourceId(infoDto.getResourceId()).templateId(0).industryId(0)
                    .externalPrice(externalPrice).internalPrice(internalPrice).cardType(0).priceType(0)
                    .mFreqLimit(mFreqLimit).rotationNum(rotationNum).status(1)
                    .build());
        }
        return Response.SUCCESS();
    }


    @ApiOperation(value = "ogv小黄条刷库")
    @RequestMapping(value = "/refresh/ogv/schedule", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> updateSchedulePrice(@RequestParam("order_id")Integer order_id,
                                       @RequestParam("pack_id")Integer pack_id) throws IOException {

        scheduleService.refreshOgvOrder(order_id, pack_id);

        return Response.SUCCESS();
    }

    @ApiOperation(value = "资管数据拉取")
    @RequestMapping(value = "/get/gd", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> getGdInfo(@RequestParam("date")String date, @RequestParam("path")String path,
                             @RequestParam("account")Integer account,
                             @RequestParam("sales_type")Integer sales_type) throws IOException {
        Timestamp begin = TimeUtil.localDateToTimestamp(TimeUtil.baseDateStrToLocalDate(date));
        GdSchedulePoExample poExample = new GdSchedulePoExample();
        poExample.or().andAccountIdEqualTo(account).andBeginDateGreaterThanOrEqualTo(begin)
                .andStatusIn(Lists.newArrayList(1,2)).andIsDeletedEqualTo(0)
                .andSalesTypeEqualTo(sales_type);
        List<GdSchedulePo> schedulePos = scheduleDao.selectByExample(poExample);

        Set<Integer> orderIds = schedulePos.stream().map(GdSchedulePo::getOrderId).collect(Collectors.toSet());
        List<Integer> ScheduleIds = schedulePos.stream().map(GdSchedulePo::getScheduleId).distinct()
                .collect(Collectors.toList());
        FcOrderPoExample fcOrderPoExample = new FcOrderPoExample();
        fcOrderPoExample.or().andOrderIdIn(new ArrayList<>(orderIds));

        List<FcOrderPo> fcOrderPos = fcOrderDao.selectByExample(fcOrderPoExample);
        Map<Integer, FcOrderPo> fcOrderMap = fcOrderPos.stream().collect(Collectors.toMap(FcOrderPo::getOrderId,
                t->t));
        Map<Integer, List<GdSchedulePo>> listMap = schedulePos.stream()
                .collect(Collectors.groupingBy(GdSchedulePo::getOrderId));

        GdScheduleTargetPoExample targetPoExample = new GdScheduleTargetPoExample();
        targetPoExample.or().andIsDeletedEqualTo(0).andTargetTypeNotEqualTo(11)
                .andScheduleIdIn(ScheduleIds);
        List<GdScheduleTargetPo> targetItemPos = gdScheduleTargetDao.selectByExample(targetPoExample);
        Map<Integer, List<GdScheduleTargetPo>> targetPoMap = targetItemPos.stream()
                .collect(Collectors.groupingBy(GdScheduleTargetPo::getScheduleId));

        ResTargetItemPoExample targetItemPoExample = new ResTargetItemPoExample();
        targetItemPoExample.or().andStatusEqualTo(1);
        List<ResTargetItemPo> targetPos = targetItemDao.selectByExampleWithBLOBs(targetItemPoExample);
        Map<Integer, List<ResTargetItemPo>> targetItemMap = targetPos.stream()
                .collect(Collectors.groupingBy(ResTargetItemPo::getType));
        Map<Integer, Map<Integer, String>> targetMap = new HashMap<>();
        targetItemMap.forEach((k,v)->targetMap.put(k, v.stream()
                .collect(Collectors.toMap(ResTargetItemPo::getId,
                        ResTargetItemPo::getName))));

        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(path),
                StandardCharsets.UTF_8));
        writer.write("订单ID 订单名称 订单开始时间 订单结束时间 订单金额 地域定向 年龄定向 性别定向");
        writer.newLine();

        Map<Integer, String> area = targetMap.get(1);
        Map<Integer, String> gender = targetMap.get(2);
        Map<Integer, String> age = targetMap.get(3);
        fcOrderPos = fcOrderPos.stream().sorted(Comparator.comparing(FcOrderPo::getBeginTime))
                .collect(Collectors.toList());
        fcOrderPos.forEach(x->{
            List<GdSchedulePo> v = listMap.get(x.getOrderId());
            FcOrderPo fcOrderPo = fcOrderMap.getOrDefault(x.getOrderId(), new FcOrderPo());
            try {
                writer.write(fcOrderPo.getOrderId() + " " + fcOrderPo.getOrderName().replace(" ", "")
                        + " " +
                        TimeUtil.timestampToIsoDateStr(fcOrderPo.getBeginTime())
                        + " " + TimeUtil.timestampToIsoDateStr(fcOrderPo.getEndTime())
                        + " " + fcOrderPo.getAmount()/100);
                GdSchedulePo schedulePo = v.get(0);
                List<GdScheduleTargetPo> targetPoList = targetPoMap.get(schedulePo.getScheduleId());
                if(!CollectionUtils.isEmpty(targetPoList)){

                    //地域
                    List<GdScheduleTargetPo> areaList = targetPoList.stream().filter(t->1==t.getTargetType())
                            .collect(Collectors.toList());
                    writer.write(" ");
                    if(!CollectionUtils.isEmpty(areaList)){
                        GdScheduleTargetPo targetPo = areaList.get(0);
                        String targets = targetPo.getTargetItemIds().replace("[", "")
                                .replace("]", "");
                        List<String> targetList = Arrays.asList(targets.split(","));
                        StringBuffer sb = new StringBuffer();
                        targetList.forEach(t->{
                            String name = area.get(new Integer(t));
                            if(!StringUtils.isEmpty(name)) {
                                sb.append(",").append(name);
                            }
                        });
                        writer.write(sb.substring(1));
                    }

                    //年龄
                    List<GdScheduleTargetPo> ageList = targetPoList.stream().filter(t->3 == t.getTargetType())
                            .collect(Collectors.toList());
                    writer.write(" ");
                    if(!CollectionUtils.isEmpty(ageList)){
                        GdScheduleTargetPo targetPo = ageList.get(0);
                        String targets = targetPo.getTargetItemIds().replace("[", "")
                                .replace("]", "");
                        List<String> targetList = Arrays.asList(targets.split(","));
                        StringBuffer sb = new StringBuffer();
                        targetList.forEach(t->{
                            String name = age.get(new Integer(t));
                            if(!StringUtils.isEmpty(name)) {
                                sb.append(",").append(name);
                            }
                        });
                        writer.write(sb.substring(1));
                    }

                    //性别
                    List<GdScheduleTargetPo> agenderList = targetPoList.stream().filter(t->2 == t.getTargetType())
                            .collect(Collectors.toList());
                    writer.write(" ");
                    if(!CollectionUtils.isEmpty(agenderList)){
                        GdScheduleTargetPo targetPo = agenderList.get(0);
                        String targets = targetPo.getTargetItemIds().replace("[", "")
                                .replace("]", "");
                        List<String> targetList = Arrays.asList(targets.split(","));
                        StringBuffer sb = new StringBuffer();
                        targetList.forEach(t->{
                            String name = gender.get(new Integer(t));
                            if(!StringUtils.isEmpty(name)) {
                                sb.append(",").append(name);
                            }
                        });
                        writer.write(sb.substring(1));
                    }
                }
                writer.newLine();
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

        writer.close();
        return Response.SUCCESS();
    }

    @ApiOperation(value = "输出Redis里的GD库存量")
    @RequestMapping(value = "/get/inventory", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Void> getRedisGDData(@RequestParam("sources")List<Integer> sourceIds,
                                  @ApiParam("yyyy-MM-dd") @RequestParam("date") String date,
                                  @ApiParam("path") @RequestParam("path") String path,
                                  @ApiParam("0、1、2或null")@RequestParam("inlineTarget") String inlineTarget) throws IOException {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        if (!CollectionUtils.isEmpty(sourceIds)){
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(path),
                    StandardCharsets.UTF_8));
            writer.write("资源位 城市 年龄 性别 库存");
            writer.newLine();
            for (Integer source: sourceIds){
                String hashKey = hashKey(source, localDate);
                Map<String, String> targetInvent = null;
                try {
                    targetInvent = jedisCluster.hgetAll(hashKey);
                    log.info("targetInvent-hashKey:{}, size:{}", hashKey, targetInvent.size());
                } catch (Exception e) {
                    log.error("jedisCluster hGetAll {} hit error, msg:{}, trace:{}", hashKey,
                            e.getMessage(), Throwables.getStackTraceAsString(e));
                }
                ResTargetItemPoExample targetItemPoExample = new ResTargetItemPoExample();
                targetItemPoExample.or().andStatusEqualTo(1)
                        .andTypeIn(Lists.newArrayList(1,2,3));
                List<ResTargetItemPo> targetPos = targetItemDao.selectByExampleWithBLOBs(targetItemPoExample);
                Map<Integer, List<ResTargetItemPo>> targetItemMap = targetPos.stream()
                        .collect(Collectors.groupingBy(ResTargetItemPo::getType));
                Map<Integer, Map<String, String>> targetMap = new HashMap<>();
                targetItemMap.forEach((k,v)->targetMap.put(k, v.stream().filter(t->!StringUtils
                        .isEmpty(t.getMappingContent()))
                        .collect(Collectors.toMap(ResTargetItemPo::getMappingContent,
                                ResTargetItemPo::getName))));

                Map<String, String> area = targetMap.get(1);
                Map<String, String> gender = targetMap.get(2);
                Map<String, String> age = targetMap.get(3);
                if(!CollectionUtils.isEmpty(targetInvent)) {
                    targetInvent.forEach((K, V) -> {
                        Target target = Target.fromHash(Integer.parseInt(K));
                        try {
                            if (target.hasTarget(TargetEntry.INLINE_SALES_TYPE, Integer.parseInt(inlineTarget))) {
                                Map<TargetEntry, Set<Integer>> targets = target.getTargets();

                                writer.write(source + " ");
                                //地域
                                List<Integer> areaTargets = new ArrayList<>(targets.get(TargetEntry.AREA));
                                Integer areaTarget = areaTargets.get(0);
                                writer.write(area.get(areaTarget.toString()) + " ");

                                //年龄
                                List<Integer> ageTargets = new ArrayList<>(targets.get(TargetEntry.AGE));
                                Integer ageTarget = ageTargets.get(0);
                                writer.write(age.get(ageTarget.toString()) + " ");

                                //性别
                                List<Integer> genderTargets = new ArrayList<>(targets.get(TargetEntry.GENDER));
                                Integer genderTarget = genderTargets.get(0);
                                writer.write(gender.get(genderTarget.toString()) + " ");

                                writer.write(V);
                                writer.newLine();
                            }
                        } catch (IOException e) {
                            e.printStackTrace();
                        }

                    });
                }
            }
            writer.close();
        }

        return Response.SUCCESS();
    }



    @ApiOperation(value = "刷新小排期价格")
    @RequestMapping(value = "/refresh/schedule/price", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<String> refreshSchedulePrice(@RequestParam("do_change")Boolean doChange) {
        SsaSchedulePoExample poExample = new SsaSchedulePoExample();
        poExample.or().andLaunchDateGreaterThan(Utils.getBeginOfDay(TimeUtils.lastMonthEnd()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andGdScheduleIdNotIn(Lists.newArrayList(201085,207300,207308,208839))
                .andSalesTypeEqualTo(SalesType.SSA_CPT.getCode());
        List<SsaSchedulePo> ssaSchedulePos = ssaScheduleDao.selectByExample(poExample);
        Assert.notNull(ssaSchedulePos, "查找不到闪屏排期");
        List<Integer> ids = ssaSchedulePos.stream().map(SsaSchedulePo::getGdScheduleId).collect(Collectors.toList());
        if(!doChange){
            return Response.SUCCESS(ids.size() + "--" + ids);
        }

        GdSchedulePoExample schedulePoExample = new GdSchedulePoExample();
        schedulePoExample.or().andScheduleIdIn(ids).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<GdSchedulePo> schedulePos = scheduleDao.selectByExample(schedulePoExample);
        Map<Integer, List<GdSchedulePo>> poMap = schedulePos.stream()
                .collect(Collectors.groupingBy(GdSchedulePo::getScheduleId));

        Timestamp time = new Timestamp(System.currentTimeMillis());
        ssaSchedulePos = ssaSchedulePos.stream().peek(t->{
            GdSchedulePo schedulePo = poMap.get(t.getGdScheduleId()).get(0);
            t.setExternalPrice(schedulePo.getExternalPrice());
            t.setInternalPrice(schedulePo.getInternalPrice());
            t.setMtime(time);
        }).collect(Collectors.toList());

        int count = ssaScheduleDao.insertUpdateBatch(ssaSchedulePos);
        return Response.SUCCESS(String.valueOf(count));
    }

    @ApiOperation(value = "更新新小排期价格")
    @RequestMapping(value = "/update/schedule/price", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Integer> updateSchedulePrice(@RequestParam("id")Integer id,
                                          @RequestParam("ex_price")Long ex_price,
                                          @RequestParam("in_price")Long in_price) {
        SsaSchedulePoExample poExample = new SsaSchedulePoExample();
        poExample.or().andLaunchDateGreaterThan(Utils.getBeginOfDay(TimeUtils.lastMonthEnd()))
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(id);

        int count = ssaScheduleDao.updateByExampleSelective(SsaSchedulePo.builder().externalPrice(ex_price)
                        .internalPrice(in_price).build(),
                poExample);

        return Response.SUCCESS(count);
    }

    @ApiOperation(value = "产品通知")
    @RequestMapping(value = "/inform/product", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Integer> inform() {
        scheduleService.informProduct();
        return Response.SUCCESS(1);
    }


    @ApiOperation(value = "添加cpt资源位和价格")
    @RequestMapping(value = "/add/cpt/source", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> addCptConfig(@RequestParam("cycle_id")Integer cycle_id,
                                  @RequestParam("channel")Integer channel) {

        String liveSourceIdStr = systemConfigService.getValue(SystemConfigEnum.LIVE_CPT_SOURCE_IDS.getCode());
        Set<String> liveSourceIds = new HashSet<>(Arrays.asList(liveSourceIdStr.split(",")));

        List<SourceAllInfoDto> allInfoDtos = cptSourceService
                .getCptSourcesInSourceIds(liveSourceIds.stream().map(Integer::new)
                        .collect(Collectors.toList()));

        log.info("mock query source is" + allInfoDtos);
        if(CollectionUtils.isEmpty(allInfoDtos)){
            return Response.SUCCESS();
        }
        Timestamp now = new Timestamp(System.currentTimeMillis());
        List<SlotPo> slotPos = allInfoDtos.stream().map(t->SlotPo.builder().cpt(1).channelId(channel).cpa(0)
                .cpc(0).cpm(1).inventory(1).status(1).cps(0).addTime(now).isDeleted(IsDeleted.VALID.getCode())
                .slotId(t.getSourceId()).slotName(t.getName()).templateIds("").updateTime(now).build()
        ).collect(Collectors.toList());
        slotDao.batchSave(slotPos);

        List<CptSourceConfigPo> configPos = allInfoDtos.stream().map(t->CptSourceConfigPo.builder().cardName("")
                .cardType(0).ctime(now).cycleId(cycle_id).externalPrice(0).hourExternalPrice(0)
                .hourExternalPriceStrong(0).internalCpmPrice(0).internalPrice(0).isDeleted(IsDeleted.VALID.getCode())
                .isDistribution(1).mFreqLimit(31).mtime(now).pageId(t.getPageId()).platformId(t.getPlatformId())
                .preBookingStatus(0).priceType(0).resourceId(t.getResourceId()).rotationNum(1).salesType(31)
                .sourceId(t.getSourceId()).status(1).templateId(0).templateName("")
                .build()).collect(Collectors.toList());
        sourceConfigDao.insertBatch(configPos);
        return Response.SUCCESS();
    }


    @ApiOperation(value = "测试cpt刊例价分页查询")
    @RequestMapping(value = "/query/cpt", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> queryCpt() {

        PageResult<CptSourceAllInfoDto> pageResult = cptService.getCptSourceList(101, 0,
                null,null,null,
                31,1,20);

        return Response.SUCCESS();
    }

    @ApiOperation(value = "测试闪屏分页查询")
    @RequestMapping(value = "/query/ssa", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> querySSa() throws ServiceException {

        ssaSplashScreenService
                .reAuditPass(1550, Operator.SYSTEM);

        return Response.SUCCESS();
    }


    @ApiOperation(value = "测试topview更新刊例价")
    @RequestMapping(value = "/update/topview", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> updateTopview() throws ServiceException {

        topViewSourceService.updateSourceConfig(Operator.builder().operatorType(OperatorType.OPERATING_PERSONNEL)
                        .operatorName("fsf").bilibiliUserName("xiongyan").operatorId(1005).ip("***********").build(),
                TopViewSourceConfigDto.builder().weekendRaiseRatio(0).festivalRaiseRatio(0)
                        .bannerRotationNum(1).bannerShowType(1).busMFreqLimit(60).clickArea(1)
                        .externalPrice(385000).id(1187).internalCpmPrice(85).internalPrice(385000)
                        .mFreqLimit(60).ssaRotationNum(1).ssaScreenStyle(2).salesType(43).bannerShowType(1).build());
        return Response.SUCCESS(null);
    }


    @ApiOperation(value = "测试crm接口")
    @RequestMapping(value = "/queryAudit", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<CptCreativeAllInfoDto>> queryAudit() throws ServiceException {
        QueryCreativeParamDto paramDto = QueryCreativeParamDto.builder()
//                .cptCreativeStatus(status)
//                .creativeNameLike(creativeNameLike)
//                .orderNameLike(orderNameLike)
//                .creativeId(Long.valueOf(creativeId))
//                .orderId(orderId)
//                .businessSideIds(businessSideIds)
//                .platformIds(platformIds)
//                .locPageIds(pageIds)
                .beginTime(new Timestamp(1617292800000L))
                .endTime(new Timestamp(1619884799000L))
                .salesType(SalesType.SEARCH_CPT.getCode())
                .build();
        PageResult<CptCreativeAllInfoDto> pageResult = cptCreativeService.getCptCreatives(paramDto,1, 50);
//        cptSourceService.addSourceConfig(Operator.builder().build(),
//                CptSourceConfigAddDto.builder().templateName("sfs")..build());

        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "测试crm接口")
    @RequestMapping(value = "/queryCrm", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<GdScheduleDailyDto>> queryCrm(@RequestParam("id")Integer crmOrderId) throws ServiceException {
        List<GdScheduleDailyDto> dailyDtos = soaSsaSchedule.getGdScheduleDailyDtosByCrmOrderId(crmOrderId);
        return Response.SUCCESS(dailyDtos);
    }

//    @ApiOperation(value = "刷redis到内存")
//    @RequestMapping(value = "/redis2memory", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    Response<Object> loadRedis2Memory(@RequestParam("sources")List<Integer> sourceIds,
//                                      @ApiParam("yyyy-MM-dd")
//    @RequestParam("dates") List<String> dateList) throws ServiceException {
//        if (!CollectionUtils.isEmpty(sourceIds)){
//            for (Integer source: sourceIds){
//                for (String date: dateList){
//                    inventoryStore.queryCachePerSrcDay(source, Objects.requireNonNull(TimeUtil.fromStringDb(date)),
//                            false);
//                }
//            }
//        }else {
//            inventoryStore.loadInvent(0, true);
//        }
//        return Response.SUCCESS(null);
//    }

    @ApiOperation(value = "刷ES数据到redis")
    @RequestMapping(value = "/es2redis", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> loadEs2Redis(@RequestParam("sources")List<Integer> sourceIds, @ApiParam("yyyy-MM-dd") @RequestParam("dates") List<String> dateList) throws ServiceException {
        if (!CollectionUtils.isEmpty(sourceIds)){
            for (Integer source: sourceIds){
                for (String date: dateList){
                    inventoryStore.loadDataToRedisFromEs(source, date);
                }
            }
        }else {
            inventoryStore.loadInvent(0, true);
        }
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "刷redis到内存")
    @RequestMapping(value = "/redis2memory", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> loadRedis2Memory(@RequestParam("sources")List<Integer> sourceIds, @ApiParam("yyyy-MM-dd")
    @RequestParam("dates") List<String> dateList) throws ServiceException {
        if (!CollectionUtils.isEmpty(sourceIds)){
            for (Integer source: sourceIds){
                for (String date: dateList){
                    inventoryStore.queryCachePerSrcDay(source, Objects.requireNonNull(TimeUtil.isoDateStrToLocalDate(date)),
                            false);
                }
            }
        }else {
            inventoryStore.loadInvent(0, true);
        }
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "刷单日所有资源位ES数据到Redis")
    @RequestMapping(value = "/es2redis/single", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> loadEs2RedisWithDate(@ApiParam("yyyy-MM-dd") @RequestParam("date") String date) {
        LocalDate locateDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        inventoryStore.loadDataToRedisFromEsWithDate(locateDate);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "手动执行刷es定时任务")
    @RequestMapping(value = "/loadEsJob", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> loadEs() {
        inventoryStore.loadInvent(0, true);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "查询Redis里的GD库存量")
    @RequestMapping(value = "/queryGDRedis", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Long> queryRedisGDData(@RequestParam("sources")List<Integer> sourceIds,
                                    @ApiParam("yyyy-MM-dd") @RequestParam("date") String date,
                                    @ApiParam("0、1、2或null")@RequestParam("inlineTarget") String inlineTarget,
                                    @RequestParam(value = "areas", required = false) List<Integer> areas) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        AtomicLong result = new AtomicLong(0L);
        if (!CollectionUtils.isEmpty(sourceIds)){
            for (Integer source: sourceIds){
                String hashKey = hashKey(source, localDate);
                Map<String, String> targetInvent = null;
                try {
                    targetInvent = jedisCluster.hgetAll(hashKey);
                    log.info("targetInvent-hashKey:{}, size:{}", hashKey, targetInvent.size());
                } catch (Exception e) {
                    log.error("jedisCluster hGetAll {} hit error, msg:{}, trace:{}", hashKey,
                            e.getMessage(), Throwables.getStackTraceAsString(e));
                }
                if(!CollectionUtils.isEmpty(targetInvent)) {
                    targetInvent.forEach((K, V) -> {
                        Target target = Target.fromHash(Integer.parseInt(K));
                        if(!StringUtils.isEmpty(inlineTarget)) {
                            if (target.hasTarget(TargetEntry.INLINE_SALES_TYPE, Integer.parseInt(inlineTarget))) {
                                if(areas != null){
                                    if (target.hasTarget(TargetEntry.AREA, areas)) {
                                        result.addAndGet(Long.parseLong(V));
                                    }
                                }else {
                                    result.addAndGet(Long.parseLong(V));
                                }
                            }
                        }else {
                            result.addAndGet(Long.parseLong(V));
                        }
                    });
                }
            }
        }
        return Response.SUCCESS(result.get());
    }

    private String hashKey(Integer source, LocalDate date) {
        return source + "_" + date.toString();
    }

    @ApiOperation(value = "查询本地内存里的GD库存量")
    @RequestMapping(value = "/queryGDMemory", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<StoreVo> queryMemoryGDData(@RequestParam("sources")List<Integer> sourceIds,
                                        @ApiParam("yyyy-MM-dd") @RequestParam("date") String date,
                                        @ApiParam("0、1、2或null")@RequestParam("inlineTarget") String inlineTarget) {
        LocalDate localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        StoreVo storeVo = new StoreVo();
        if (!CollectionUtils.isEmpty(sourceIds)){
            AtomicLong inventoryTotal = new AtomicLong(0L);
            Long srcTotal = 0L;
            Long firstTotal = 0L;
            Long normalTotal  = 0L;
            AtomicLong inventAvailTotal = new AtomicLong(0L);
            for (Integer source: sourceIds){
                Map<Target, Long> inventory = directoryStore.getInvent(source, localDate);
                if(!CollectionUtils.isEmpty(inventory)) {
                    inventory.forEach((k, v) -> {
                        if(!StringUtils.isEmpty(inlineTarget)) {
                            if(k.hasTarget(TargetEntry.INLINE_SALES_TYPE, Integer.parseInt(inlineTarget))) {
                                inventoryTotal.addAndGet(v);
                            }
                        }else {
                            inventoryTotal.addAndGet(v);
                        }
                    });
                }

                Map<Target, Long> invent = directoryStore.getInventLeft(source, localDate);
                if(!CollectionUtils.isEmpty(invent)) {
                    invent.forEach((k, v) -> {
                        if (!StringUtils.isEmpty(inlineTarget)) {
                            if (k.hasTarget(TargetEntry.INLINE_SALES_TYPE, Integer.parseInt(inlineTarget))) {
                                inventAvailTotal.addAndGet(v);
                            }
                        } else {
                            inventAvailTotal.addAndGet(v);
                        }
                    });
                }

                firstTotal = directoryStore.getSrcFirstTotal(localDate, source);
                normalTotal = directoryStore.getSrcNormalTotal(localDate, source);
                srcTotal = directoryStore.getSrcTotal(localDate, source);
            }
            storeVo.setInventory_total(inventoryTotal.get());
            storeVo.setInvent_avail_total(inventAvailTotal.get());
            storeVo.setSrc_first_total(firstTotal);
            storeVo.setSrc_normal_total(normalTotal);
            storeVo.setSrc_total(srcTotal);
        }
        return Response.SUCCESS(storeVo);
    }

    //    @Autowired
//    private ILauCreativeService creativeService;
//    @Autowired
//    private IQueryAccountService queryAccountService;
//    @Autowired
//    private IGdCreativeService gdCreativeService;
//    @Autowired
//    private GdCreativeDao gdCreativeDao;
//    @Autowired
//    private GdCreativeDateDao gdCreativeDateDao;
//    @Autowired
//    private GdCreativeLayoutDao gdCreativeLayoutDao;
//    @Autowired
//    private GdCreativeImageDao gdCreativeImageDao;
//    @Autowired
//    private IQuerySheduleService queryScheduleService;
//    @Autowired
//    private ILauUnitDateService unitDateService;
//    @Autowired
//    private IQueryTemplateService queryTemplateService;
//    @Autowired
//    private IMailService mailService;
//    @Autowired
//    private GdOrderDelegate gdOrderDelegate;
//    @Autowired
//    private CptScheduleDelegate cptScheduleDelegate;
//    @Autowired
//    private CptOrderDao cptOrderDao;
//
    @Autowired
    private SsaSplashScreenCustomUrlDao customUrlDao;

    @Autowired
    private SsaSplashScreenJumpInfoDao jumpInfoDao;

    @Autowired
    private SsaSplashScreenDao splashScreenDao;
    //
//    @Autowired
//    private CptScheduleDao cptScheduleDao;
//
//    @Autowired
//    private GdScheduleDao gdScheduleDao;
//    @Autowired
//    private GdStatusConvertHelper gdStatusConvertHelper;
//
//    @Autowired
//    private ICptSourceService cptSourceService;
//
//    @Autowired
//    private IBrandScheduleService brandScheduleService;
//    @Autowired
//    private ICptScheduleWalletService cptScheduleWalletService;
//
//    @Autowired
//    private BrandScheduleDao brandScheduleDao;
//
//
//    @ApiOperation(value = "email")
//    @RequestMapping(value = "/email", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    Response<Object> email() throws ServiceException {
//        MailMessage mailMessage = new MailMessage();
//        mailMessage.setTos(Lists.newArrayList("<EMAIL>"));
//        mailMessage.setUseHtml(false);
//        mailMessage.setHasFile(false);
//        mailMessage.setSubject("test");
//        mailMessage.setText("test");
//        mailService.send(mailMessage);
//        return Response.SUCCESS(null);
//    }
//
    @RequestMapping(value = "/rushSsaJumpData", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> rush() {
        SsaSplashScreenPoExample example = new SsaSplashScreenPoExample();
        example.or().andIsDeletedEqualTo(0).andSalesTypeBetween(41, 43);
        List<SsaSplashScreenPo> screenPos = splashScreenDao.selectByExample(example);
        SsaSplashScreenJumpInfoPo jumpInfoPo;
        List<SsaSplashScreenJumpInfoPo> records = new ArrayList<>();
        for(SsaSplashScreenPo po : screenPos){
            jumpInfoPo = SsaSplashScreenJumpInfoPo.builder()
                    .splashScreenId(po.getId()).schemeUrl(StringUtils.isEmpty(po.getSchemeUrl()) ? ""
                            : po.getSchemeUrl()).platformId(PlatformType.IPHONE.getCode())
                    .schemeCopywriting(StringUtils.isEmpty(po.getSchemeCopywriting())? "" : po.getSchemeCopywriting())
                    .isCallApp(po.getIsCallApp() == null ? 1 : po.getIsCallApp())
                    .packageName(StringUtils.isEmpty(po.getIosAppPackageName())? "" : po.getIosAppPackageName())
                    .isWithWakeUpBar(po.getIsWithWakeUpBar()==null ? 0 :po.getIsWithWakeUpBar())
                    .jumpLink(StringUtils.isEmpty(po.getJumpLink()) ? "" :po.getJumpLink())
                    .jumpType(po.getJumpType()==null ? 0 : po.getJumpType())
                    .universalApp("").isDeleted(0).ctime(po.getCtime()).mtime(po.getMtime()).build();
            records.add(jumpInfoPo);
            jumpInfoPo = SsaSplashScreenJumpInfoPo.builder()
                    .splashScreenId(po.getId()).schemeUrl(StringUtils.isEmpty(po.getSchemeUrl()) ? ""
                            : po.getSchemeUrl()).platformId(PlatformType.IPAD.getCode())
                    .schemeCopywriting(StringUtils.isEmpty(po.getSchemeCopywriting())? "" : po.getSchemeCopywriting())
                    .isCallApp(po.getIsCallApp() == null ? 1 : po.getIsCallApp())
                    .packageName(StringUtils.isEmpty(po.getIosAppPackageName())? "" : po.getIosAppPackageName())
                    .isWithWakeUpBar(po.getIsWithWakeUpBar()==null ? 0 :po.getIsWithWakeUpBar())
                    .jumpLink(StringUtils.isEmpty(po.getJumpLink()) ? "" :po.getJumpLink())
                    .jumpType(po.getJumpType()==null ? 0 : po.getJumpType())
                    .universalApp("").isDeleted(0).ctime(po.getCtime()).mtime(po.getMtime()).build();
            records.add(jumpInfoPo);
            jumpInfoPo = SsaSplashScreenJumpInfoPo.builder()
                    .splashScreenId(po.getId()).schemeUrl(StringUtils.isEmpty(po.getSchemeUrl()) ? ""
                            : po.getSchemeUrl()).platformId(PlatformType.ANDROID.getCode())
                    .schemeCopywriting(StringUtils.isEmpty(po.getSchemeCopywriting())? "" : po.getSchemeCopywriting())
                    .isCallApp(po.getIsCallApp() == null ? 1 : po.getIsCallApp())
                    .packageName(StringUtils.isEmpty(po.getAndroidAppPackageName())? "" : po.getAndroidAppPackageName())
                    .isWithWakeUpBar(po.getIsWithWakeUpBar()==null ? 0 :po.getIsWithWakeUpBar())
                    .jumpLink(StringUtils.isEmpty(po.getJumpLink()) ? "" :po.getJumpLink())
                    .jumpType(po.getJumpType()==null ? 0 : po.getJumpType())
                    .universalApp("").isDeleted(0).ctime(po.getCtime()).mtime(po.getMtime()).build();
            records.add(jumpInfoPo);
        }

        List<SsaSplashScreenJumpInfoPo> temp = new ArrayList<>();
        int i = 0;
//        try {
        for(SsaSplashScreenJumpInfoPo jumpInfoPo1 : records){
            i++;
            temp.add(jumpInfoPo1);
            if(i == 100){
                jumpInfoDao.insertBatch(temp);
                i = 0;
                temp = new ArrayList<>();
//                    Thread.sleep(1000);
            }
        }
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
        jumpInfoDao.insertBatch(temp);
        return Response.SUCCESS("ok");
    }

    @RequestMapping(value = "/rushSsaCustomerUrlData", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> rushSsaCustomerUrlData() {
        SsaSplashScreenCustomUrlPoExample example = new SsaSplashScreenCustomUrlPoExample();
        example.or().andIsDeletedEqualTo(0);
        List<SsaSplashScreenCustomUrlPo> customUrlPos= customUrlDao.selectByExample(example);


        List<SsaSplashScreenCustomUrlPo> records = new ArrayList<>();
        SsaSplashScreenCustomUrlPo tempPo;
        SsaSplashScreenCustomUrlPo tempPo1;
        for(SsaSplashScreenCustomUrlPo po : customUrlPos){
            tempPo = new SsaSplashScreenCustomUrlPo();
            BeanUtils.copyProperties(po, tempPo);
            tempPo.setPlatformId(PlatformType.IPAD.getCode());
            tempPo.setId(null);
            records.add(tempPo);

            tempPo1 = new SsaSplashScreenCustomUrlPo();
            BeanUtils.copyProperties(po,tempPo1);
            tempPo1.setPlatformId(PlatformType.ANDROID.getCode());
            tempPo1.setId(null);
            records.add(tempPo1);
        }

        List<SsaSplashScreenCustomUrlPo> temp = new ArrayList<>();
        int i = 0;
        for(SsaSplashScreenCustomUrlPo customUrlPo : records){
            i++;
            temp.add(customUrlPo);
            if(i == 100){
                customUrlDao.insertBatch(temp);
                i = 0;
                temp = new ArrayList<>();
            }
        }
        customUrlDao.insertBatch(temp);
        return Response.SUCCESS("ok");
    }

    @ApiOperation(value = "test")
    @RequestMapping(value = "/test", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> cptRest() throws ServiceException {
//        cptSourceService.getCptSourceAllInfoMapInSourceIds(Lists.newArrayList(244), SalesType.CPT.getCode());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "stock")
    @RequestMapping(value = "/stock", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> stock() throws ServiceException {
        inventoryStore.loadInvent(0, true);
        return Response.SUCCESS("");
    }

    @RequestMapping(value = "/job/cut_image", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> reCutSplashScreenImageJob() throws ServiceException {
        ssaSplashScreenImageService.reCutFailedImage();
        return Response.SUCCESS("ok");
    }

    @RequestMapping(value = "/releasejob", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> refreshSplashScreenStatusToCompletedJob(@RequestParam("date")String date) throws ServiceException {
        ssaBookingService.releaseJob(Utils.getTimestamp(date), Collections.emptyList(), Collections.emptyList(), SalesType.OTT_CPT.getCode());
        return Response.SUCCESS("ok");
    }

    @RequestMapping(value = "/alertjob", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> refreshSplashScreenStatusToOnLine(@RequestParam("date")String date) throws ServiceException {
        ssaBookingService.alertJob(Utils.getTimestamp(date), Collections.emptyList(), Collections.emptyList(), SalesType.OTT_CPT.getCode());
        return Response.SUCCESS("ok");
    }


//    @ApiOperation(value = "刷不在cpt schedule的brand schedule")
//    @RequestMapping(value = "/cpt/brand", method = RequestMethod.GET)
//    @Transactional(value = "brandScheduleTransactionManager", rollbackFor = Exception.class)
//    public
//    @ResponseBody
//    Response<Object> brandScheduleReset() throws ServiceException {
//        CptSchedulePoExample example = new CptSchedulePoExample();
//        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<CptSchedulePo> schedulePos = cptScheduleDao.selectByExample(example);
//
//        BrandSchedulePoExample brandExample = new BrandSchedulePoExample();
//        brandExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
//                .andScheduleIdNotIn(schedulePos.stream().map(CptSchedulePo::getId).distinct().collect(Collectors.toList()));
//
//        List<BrandSchedulePo> brandSchedulePos = brandScheduleDao.selectByExample(brandExample);
//        for (BrandSchedulePo brandSchedulePo : brandSchedulePos) {
//            BrandSchedulePo delete = new BrandSchedulePo();
//            delete.setId(brandSchedulePo.getId());
//            delete.setIsDeleted(1);
//            brandScheduleDao.updateByPrimaryKeySelective(delete);
//        }
//        return Response.SUCCESS(null);
//    }
//
//    @ApiOperation(value = "刷gd schedule 到cpt schedule")
//    @RequestMapping(value = "/cpt/schedule", method = RequestMethod.GET)
//    @Transactional(value = "brandScheduleTransactionManager", rollbackFor = Exception.class)
//    public
//    @ResponseBody
//    Response<Object> cptScheduleReset() throws ServiceException {
//        GdOrderPoExample gdOrderPoExample = new GdOrderPoExample();
//        gdOrderPoExample.or().andProductEqualTo(2)
//                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
//                .andCtimeGreaterThanOrEqualTo(Utils.getTimestamp("2018-01-01", "yyyy-MM-dd"));
//        List<GdOrderPo> gdOrderPos = gdOrderDao.selectByExample(gdOrderPoExample);
//
//        GdSchedulePoExample gdSchedulePoExample = new GdSchedulePoExample();
//        gdSchedulePoExample.or().andOrderIdIn(gdOrderPos.stream().map(GdOrderPo::getOrderId).collect(Collectors.toList()))
//            .andStatusEqualTo(SwitchStatus.STARTED.getCode())
//            .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<GdSchedulePo> gdSchedulePos = gdScheduleDao.selectByExample(gdSchedulePoExample);
//
//        CptSchedulePoExample cptSchedulePoExample = new CptSchedulePoExample();
//        cptSchedulePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<CptSchedulePo> cptSchedulePos = cptScheduleDao.selectByExample(cptSchedulePoExample);
//        Set<Integer> cptGdScheduleIdSet = cptSchedulePos.stream().map(CptSchedulePo::getGdScheduleId)
//                .collect(Collectors.toSet());
//
//        for (GdSchedulePo gdSchedulePo : gdSchedulePos) {
//            if (cptGdScheduleIdSet.contains(gdSchedulePo.getScheduleId())) {
//                continue;
//            }
//            CptSchedulePo cptSchedulePo = new CptSchedulePo();
//            cptSchedulePo.setPlatformId(gdSchedulePo.getPlatformId());
//            cptSchedulePo.setPlatformName(gdSchedulePo.getPlatformName());
//            cptSchedulePo.setPageId(gdSchedulePo.getPageId());
//            cptSchedulePo.setPageName(gdSchedulePo.getPageName());
//            cptSchedulePo.setResourceId(gdSchedulePo.getResourceId());
//            cptSchedulePo.setResourceName(gdSchedulePo.getResourceName());
//            cptSchedulePo.setSourceId(gdSchedulePo.getSlotId());
//            cptSchedulePo.setSourceName(gdSchedulePo.getSourceName());
//            cptSchedulePo.setLevel(gdSchedulePo.getLevel());
//            cptSchedulePo.setBeginDate(Utils.getBeginOfDay(gdSchedulePo.getBeginDate()));
//            cptSchedulePo.setEndDate(Utils.getBeginOfDay(gdSchedulePo.getEndDate()));
//            int dayCount = TimeUtils.countDays(gdSchedulePo.getBeginDate(), gdSchedulePo.getEndDate());
//            CptSourceAllInfoDto cptSourceAllInfoDto = cptSourceService.getCptSourceBySourceId(gdSchedulePo.getCycleId(), gdSchedulePo.getSlotId());
//            cptSchedulePo.setExternalPrice((int) (Utils.fromYuanToFen(cptSourceAllInfoDto.getExternalPrice()) * dayCount));
//            cptSchedulePo.setInternalPrice((int) (Utils.fromYuanToFen(cptSourceAllInfoDto.getInternalPrice()) * dayCount));
//            CptOrderPo cptOrderPo = super.getCptOrderPoByGdOrderId(gdSchedulePo.getOrderId());
//            cptSchedulePo.setCptOrderId(cptOrderPo.getId());
//            cptSchedulePo.setBusinessSideId(cptOrderPo.getBusinessSideId());
//            cptSchedulePo.setCycleId(gdSchedulePo.getCycleId());
//            cptSchedulePo.setGdScheduleId(gdSchedulePo.getScheduleId());
//            cptScheduleDao.insertSelective(cptSchedulePo);
//
//            List<NewBrandScheduleDto> newBrandScheduleDtos = new ArrayList<>();
//            List<Timestamp> days = TimeUtils.getEachDay(cptSchedulePo.getBeginDate(), cptSchedulePo.getEndDate());
//            newBrandScheduleDtos.addAll(days.stream().map(day -> NewBrandScheduleDto.builder()
//                    .scheduleId(cptSchedulePo.getId())
//                    .scheduleDate(day)
//                    .sourceId(cptSchedulePo.getSourceId())
//                    .build()).collect(Collectors.toList()));
//            brandScheduleService.deleteByCptScheduleId(cptSchedulePo.getId());
//            brandScheduleService.batchCreateCptScheduleDto(newBrandScheduleDtos);
//
//            List<ScheduleWalletDto> scheduleWalletDtos = new ArrayList<>();
//            Long cash = Long.valueOf(cptSchedulePo.getExternalPrice() / days.size());
//            for (Timestamp day : days) {
//                scheduleWalletDtos.add(ScheduleWalletDto.builder()
//                        .cash(cash)
//                        .redPacket(0l)
//                        .cptScheduleId(cptSchedulePo.getId())
//                        .businessSideId(cptSchedulePo.getBusinessSideId())
//                        .scheduleDate(day)
//                        .build());
//            }
//            cptScheduleWalletService.deleteByScheduleId(cptSchedulePo.getId());
//            cptScheduleWalletService.batchInsert(scheduleWalletDtos);
//        }
//
//        return Response.SUCCESS(null);
//    }
//
//    @Autowired
//    private ICptCreativeService cptCreativeService;
//
//    @ApiOperation(value = "刷老cpt创意数据")
//    @RequestMapping(value = "/cpt/creative", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    Response<Object> cptcreativeRest() throws ServiceException {
//        cptCreativeService.refreshCptCreativeToGdCreative(null, null, null);
//        return Response.SUCCESS(null);
//    }
//
//    @ApiOperation(value = "刷老cpt创意button数据")
//    @RequestMapping(value = "/cpt/creative_button", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    Response<Object> cptcreativeButtonRest() throws ServiceException {
//        cptCreativeService.refreshButtonCopy();
//        return Response.SUCCESS(null);
//    }
//
//    @ApiOperation(value = "刷老创意")
//    @RequestMapping(value = "/creative/reset", method = RequestMethod.GET)
//    public
//    @ResponseBody
//    Response<Object> reset() throws ServiceException {
//        List<AccountDto> accountDtos = queryAccountService.getAllAccountDtos();
//        for (AccountDto accountDto : accountDtos) {
//            List<Creative> creatives = creativeService.getCreativeInAccountId(accountDto.getAccountId(), Lists.newArrayList(SalesType.GD.getCode()));
//            for (Creative creative : creatives) {
//                ScheduleDto scheduleDto = null;
//                try {
//                    scheduleDto = queryScheduleService.getScheduleByCampaignId(creative.getCampaignId());
//                } catch (ServiceException e) {
//                    continue;
//                }
//                Map<Integer, List<Timestamp>> dateMap = unitDateService.getDateMapInUnitIds(Lists.newArrayList(creative.getUnitId()));
//                if (null != gdCreativeService.getGdCreativeDtoById(Long.valueOf(creative.getCreativeId()))) {
//                    updateGdCreative(scheduleDto.getScheduleId(), creative, new Timestamp(creative.getBeginTime().getTime()), new Timestamp(creative.getEndTime().getTime()));
//                } else {
//                    this.createGdCreative(scheduleDto.getScheduleId(), creative, new Timestamp(creative.getBeginTime().getTime()), new Timestamp(creative.getEndTime().getTime()));
//                }
//
//                if (dateMap.containsKey(creative.getUnitId())) {
//                    this.updateGdCreativeDate(dateMap.get(creative.getUnitId()), creative);
//                }
//                this.updateGdCreativeLayout(creative, queryTemplateService.getTemplateById(creative.getTemplateId()));
//                this.updateGdCreativeImage(creative);
//            }
//        }
//        return Response.SUCCESS(null);
//    }
//
//    private void createGdCreative(Integer gdScheduleId, Creative creative, Timestamp beginTime, Timestamp endTime) {
//        GdCreativePo gdCreativePo = BeanCopyUtil.transform(creative, GdCreativePo.class);
//        gdCreativePo.setScheduleId(gdScheduleId);
//        gdCreativePo.setBeginTime(beginTime);
//        gdCreativePo.setEndTime(endTime);
//        gdCreativePo.setTags(null == creative.getTags() ? "" : creative.getTags().stream().collect(Collectors.joining(",")));
//        gdCreativeDao.insertSelective(gdCreativePo);
//    }
//
//    private void updateGdCreative(Integer scheduleId, Creative creative, Timestamp beginTime, Timestamp endTime) {
//        GdCreativePoExample example = new GdCreativePoExample();
//        example.or().andCreativeIdEqualTo(Long.valueOf(creative.getCreativeId()));
//        GdCreativePo gdCreativePo = BeanCopyUtil.transform(creative, GdCreativePo.class);
//        gdCreativePo.setScheduleId(scheduleId);
//        gdCreativePo.setBeginTime(beginTime);
//        gdCreativePo.setEndTime(endTime);
//        gdCreativePo.setTags(null == creative.getTags() ? "" : creative.getTags().stream().collect(Collectors.joining(",")));
//        gdCreativeDao.updateByExampleSelective(gdCreativePo, example);
//    }
//
//    private void updateGdCreativeLayout(Creative creative, TemplateDto template) throws ServiceException {
//
//        if (template == null || StringUtils.isEmpty(template.getAndroidLayout()) && StringUtils.isEmpty(template.getFieldMapping())) {
//            return;
//        }
//
//        LocCreativeBean lcb = LocCreativeBean
//                .builder()
//                .buttonCopy(Utils.getString(creative.getButtonCopy()))
//                .buttonCopyUrl(Utils.getString(creative.getButtonCopyUrl()))
//                .buttonReportUrls(Collections.emptyList())
//                .cmMark(Utils.getInteger(creative.getCmMark()))
//                .cmMarkDesc(Utils.getString(creative.getCmMarkDesc()))
//                .creativeType(creative.getCreativeType())
//                .customizedClickUrl(Utils.getString(creative.getCustomizedClickUrl()))
//                .customizedImpUrl(Utils.getString(creative.getCustomizedImpUrl()))
//                .description(Utils.getString(creative.getDescription()))
//                .extDescription(Utils.getString(creative.getExtDescription()))
//                .extImageUrl(Utils.getString(creative.getExtImageUrl()))
//                .images(CollectionUtils.isEmpty(creative.getImageDtos()) ? Collections.emptyList() : creative.getImageDtos().stream().map(ImageDto::getUrl).collect(Collectors.toList()))
//                .promotionPurposeContent(creative.getPromotionPurposeContent())
//                .schemeUrl(Utils.getString(creative.getSchemeUrl()))
//                .title(Utils.getString(creative.getTitle()))
//                .build();
//
//        if (!StringUtils.isEmpty(creative.getAppDownloadUrl())) {
//            lcb.setButtonCopyUrl(creative.getAppDownloadUrl());
//        }
//
//        String layoutJson = "";
//        List<GdCreativeLayoutPo> layoutPos = Lists.newArrayList();
//
//        if (!StringUtils.isEmpty(template.getAndroidLayout())) {
//            GdCreativeLayoutPo androidLayoutPo = new GdCreativeLayoutPo();
//            androidLayoutPo.setCreativeId(Long.valueOf(creative.getCreativeId()));
//
//            layoutJson = LocUtils.getLayoutJson(lcb, template.getAndroidLayout());
//
//            androidLayoutPo.setType(LayoutTypeEnum.ANDROID.getCode());
//            androidLayoutPo.setLayout(layoutJson);
//
//            layoutPos.add(androidLayoutPo);
//        }
//
//        if (!StringUtils.isEmpty(template.getFieldMapping())) {
//            GdCreativeLayoutPo iosLayoutPo = new GdCreativeLayoutPo();
//            iosLayoutPo.setCreativeId(Long.valueOf(creative.getCreativeId()));
//
//            layoutJson = LocUtils.getLayoutJson(lcb, template.getFieldMapping());
//
//            iosLayoutPo.setType(LayoutTypeEnum.IOS.getCode());
//            iosLayoutPo.setLayout(layoutJson);
//
//            layoutPos.add(iosLayoutPo);
//        }
//
//        if (!CollectionUtils.isEmpty(layoutPos)) {
//            for (GdCreativeLayoutPo layoutPo : layoutPos) {
//                gdCreativeLayoutDao.insertUpdateSelective(layoutPo);
//            }
//        }
//    }
//
//    private String getMd5FromHash(String hash) {
//        if (Strings.isNullOrEmpty(hash)) {
//            return "";
//        }
//        String decodeHash = new String(org.apache.commons.codec.binary.Base64.decodeBase64(hash));
//        ImageHash imageHash = JSON.parseObject(decodeHash, ImageHash.class);
//        return imageHash.getMd5();
//    }
//
//    private void updateGdCreativeImage(Creative creative) {
//        GdCreativeImagePo delete = new GdCreativeImagePo();
//        delete.setIsDeleted(IsDeleted.DELETED.getCode());
//        GdCreativeImagePoExample deleteExample = new GdCreativeImagePoExample();
//        deleteExample.or().andCreativeIdEqualTo(Long.valueOf(creative.getCreativeId()));
//        gdCreativeImageDao.updateByExampleSelective(delete, deleteExample);
//        if (!CollectionUtils.isEmpty(creative.getImageDtos())) {
//            for (ImageDto imageDto : creative.getImageDtos()) {
//                if (StringUtils.isEmpty(imageDto.getUrl())) {
//                    continue;
//                }
//                GdCreativeImagePo po = new GdCreativeImagePo();
//                po.setCreativeId(Long.valueOf(creative.getCreativeId()));
//                po.setImageUrl(imageDto.getUrl());
//                po.setImageMd5(getMd5FromHash(imageDto.getHash()));
//                po.setType(CreativeImageType.DUAL_ROW.getCode());
//                gdCreativeImageDao.insertSelective(po);
//            }
//        }
//
//        if (!CollectionUtils.isEmpty(creative.getExtra169ImageDtos())) {
//            for (ImageDto imageDto : creative.getExtra169ImageDtos()) {
//                if (StringUtils.isEmpty(imageDto.getUrl())) {
//                    continue;
//                }
//                GdCreativeImagePo po = new GdCreativeImagePo();
//                po.setCreativeId(Long.valueOf(creative.getCreativeId()));
//                po.setImageUrl(imageDto.getUrl());
//                po.setImageMd5(getMd5FromHash(imageDto.getHash()));
//                po.setType(CreativeImageType.SINGAL_ROW.getCode());
//                gdCreativeImageDao.insertSelective(po);
//            }
//        }
//    }
//
//    private void updateGdCreativeDate(List<Timestamp> dateList, Creative creative) {
//        GdCreativeDatePo delete = new GdCreativeDatePo();
//        delete.setIsDeleted(IsDeleted.DELETED.getCode());
//        GdCreativeDatePoExample deleteExample = new GdCreativeDatePoExample();
//        deleteExample.or().andCreativeIdEqualTo(Long.valueOf(creative.getCreativeId()));
//        gdCreativeDateDao.updateByExampleSelective(delete, deleteExample);
//        for (Timestamp timestamp : dateList) {
//            GdCreativeDatePo po = new GdCreativeDatePo();
//            po.setBeginTime(Utils.getBeginOfDay(timestamp));
//            po.setEndTime(Utils.getEndOfDay(timestamp));
//            po.setAccountId(creative.getAccountId());
//            po.setOrderId(creative.getOrderId());
//            po.setCreativeId(Long.valueOf(creative.getCreativeId()));
//            gdCreativeDateDao.insertSelective(po);
//        }
//
//    }
//
//    private Timestamp getBeginTime(List<Timestamp> dateList) {
//        return dateList.stream().max((o1, o2) -> o1.compareTo(o2)).get();
//    }
//
//    private Timestamp getEndTime(List<Timestamp> dateList) {
//        return dateList.stream().min((o1, o2) -> o1.compareTo(o2)).get();
//    }
//
//    public void resetOrder() {
//        CptOrderPoExample cptOrderPoExample = new CptOrderPoExample();
//        cptOrderPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<CptOrderPo> cptOrderPos = cptOrderDao.selectByExample(cptOrderPoExample);
//
//
//        for (CptOrderPo cptOrderPo : cptOrderPos) {
//            CptSchedulePoExample cptSchedulePoExample = new CptSchedulePoExample();
//            cptSchedulePoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
//                    .andCptOrderIdEqualTo(cptOrderPo.getId());
//            List<CptSchedulePo> cptSchedulePos = cptScheduleDao.selectByExample(cptSchedulePoExample);
//            Timestamp begin = null;
//            Timestamp end = null;
//            if (!CollectionUtils.isEmpty(cptSchedulePos)) {
//                begin = cptSchedulePos.stream().max((o1, o2) -> o1.getBeginDate().compareTo(o2.getBeginDate())).get().getBeginDate();
//                end = cptSchedulePos.stream().max((o1, o2) -> o1.getEndDate().compareTo(o2.getEndDate())).get().getEndDate();
//            }
//
//            GdOrderPo gdOrderPo = new GdOrderPo();
//            gdOrderPo.setAccountId(0);
//            gdOrderPo.setOrderNumber(cptOrderPo.getOrderName());
//            gdOrderPo.setProduct(cptOrderPo.getOrderType() == 0 ? OrderProduct.CPT.getCode() : OrderProduct.INVIT.getCode());
//            gdOrderPo.setOrderType(cptOrderPo.getType());
//            gdOrderPo.setCrmOrderId(cptOrderPo.getCrmOrderId());
//            gdOrderPo.setCrmContractId(cptOrderPo.getCrmContractId());
//            gdOrderPo.setResourceType(cptOrderPo.getResourceType());
//            gdOrderPo.setGdOrderStatus(gdStatusConvertHelper.convertCptOrderStatusToGd(CptOrderStatus.getByCode(cptOrderPo.getStatus()), begin, end).getCode());
//            gdOrderPo.setOrderName(cptOrderPo.getOrderName());
//            gdOrderPo.setBeginTime(begin);
//            gdOrderPo.setEndTime(end);
//            gdOrderPo.setAmount(0L);
//            gdOrderPo.setTotalImpression(0);
//            gdOrderPo.setCategoryFirstId(0);
//            gdOrderPo.setCategorySecondId(0);
//            gdOrderPo.setOriginTag(cptOrderPo.getOriginTag());
//            gdOrderPo.setBusinessSideId(cptOrderPo.getBusinessSideId());
//            if (cptOrderPo.getGdOrderId() > 0) {
//                gdOrderPo.setOrderId(cptOrderPo.getGdOrderId());
//                gdOrderDelegate.updateOrder(gdOrderPo);
//            } else {
//                gdOrderDelegate.createOrder(gdOrderPo);
//                CptOrderPo updateOrder = new CptOrderPo();
//                updateOrder.setId(cptOrderPo.getId());
//                updateOrder.setGdOrderId(gdOrderPo.getOrderId());
//                cptOrderDao.updateByPrimaryKeySelective(updateOrder);
//            }
//
//            for (CptSchedulePo cptSchedulePo : cptSchedulePos) {
//                GdSchedulePo gdSchedulePo = new GdSchedulePo();
//                gdSchedulePo.setStatus(cptSchedulePo.getStatus() == 1 ? 1 : 3);
//                gdSchedulePo.setOrderId(gdOrderPo.getOrderId());
//                gdSchedulePo.setSlotId(cptSchedulePo.getSourceId());
//                gdSchedulePo.setPlatformId(cptSchedulePo.getPlatformId());
//                gdSchedulePo.setPlatformName(cptSchedulePo.getPlatformName());
//                gdSchedulePo.setPageId(cptSchedulePo.getPageId());
//                gdSchedulePo.setPageName(cptSchedulePo.getPageName());
//                gdSchedulePo.setResourceId(cptSchedulePo.getResourceId());
//                gdSchedulePo.setResourceName(cptSchedulePo.getResourceName());
//                gdSchedulePo.setSourceName(cptSchedulePo.getSourceName());
//                gdSchedulePo.setLevel(cptSchedulePo.getLevel());
//                gdSchedulePo.setBusinessSideId(cptSchedulePo.getBusinessSideId());
//                gdSchedulePo.setExternalPrice(cptSchedulePo.getExternalPrice());
//                gdSchedulePo.setInternalPrice(cptSchedulePo.getInternalPrice());
//                gdSchedulePo.setCycleId(cptSchedulePo.getCycleId());
//                gdSchedulePo.setBeginDate(cptSchedulePo.getBeginDate());
//                gdSchedulePo.setEndDate(cptSchedulePo.getEndDate());
//                if (cptSchedulePo.getGdScheduleId() > 1) {
//                    gdSchedulePo.setScheduleId(cptSchedulePo.getScheduleId());
//                    cptScheduleDelegate.updateGdSchedule(gdSchedulePo);
//                } else {
//                    cptScheduleDelegate.insertGdSchedule(gdSchedulePo);
//                    CptSchedulePo updateSchedule = new CptSchedulePo();
//                    updateSchedule.setId(cptSchedulePo.getId());
//                    updateSchedule.setGdScheduleId(gdSchedulePo.getScheduleId());
//                    cptScheduleDao.updateByPrimaryKeySelective(updateSchedule);
//                }
//
//                cptScheduleDelegate.updateGdScheduleDate(gdSchedulePo.getAccountId(), gdSchedulePo.getOrderId(), gdSchedulePo.getScheduleId(), gdSchedulePo.getBeginDate(), gdSchedulePo.getEndDate());
//            }
//
//        }
//    }

    @RequestMapping(value = "/hourstock", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> hourstock(@RequestParam("source_id")Integer sourceId, @RequestParam("date")String date) throws ServiceException {

        Integer hourstock = scheduleServiceDelegate.getMaxHourStock(sourceId, Utils.getTimestamp(date, "yyyy-MM-dd"));
        return Response.SUCCESS(hourstock);
    }

    @RequestMapping(value = "/hourstock/clear", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> clearhourstock(@RequestParam("source_id")Integer sourceId, @RequestParam("date")String date) throws ServiceException {

        scheduleServiceDelegate.clearHourStock(sourceId, Utils.getTimestamp(date, "yyyy-MM-dd"));
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "初始化TopView-GD+刊例价")
    @RequestMapping(value = "/topview/init_topview_gd_price", method = RequestMethod.GET)
    @ResponseBody
    public Response<Object> initTopViewGdPrice(@RequestParam("cycle_id") Integer cycleId) throws ServiceException {
        topViewSourceService.initSourceConfig(cycleId, SalesType.TOP_VIEW_CPT.getCode(), SalesType.TOP_VIEW_GD_PLUS.getCode());
        return Response.SUCCESS("success");
    }

    @ApiOperation(value = "初始化TopLive（TopView-CPT/GD：banner是直播形态）刊例价")
    @RequestMapping(value = "/topview/init_toplive_price", method = RequestMethod.GET)
    @ResponseBody
    public Response<Object> initTopLivePrice(@RequestParam("cycle_id") Integer cycleId) throws ServiceException {
        topViewSourceService.initTopLiveSourceConfig(cycleId);
        return Response.SUCCESS("success");
    }

    @ApiOperation(value = "初始化TopView 3D视频刊例价")
    @RequestMapping(value = "/topview/init_topview_3d_price", method = RequestMethod.GET)
    @ResponseBody
    public Response<Object> initTopView3DPrice(@RequestParam("cycle_id") Integer cycleId) throws ServiceException {
        topViewSourceService.initTopView3DSourceConfig(cycleId);
        return Response.SUCCESS("success");
    }

    @ApiOperation(value = "初始化TopView 稿件视频刊例价")
    @RequestMapping(value = "/topview/init_topview_archive_price", method = RequestMethod.GET)
    @ResponseBody
    public Response<Object> initTopViewArchivePrice(@RequestParam("cycle_id") Integer cycleId) throws ServiceException {
        topViewSourceService.initTopViewArchiveSourceConfig(cycleId);
        return Response.SUCCESS("success");
    }

    @ApiOperation(value = "上传图片")
    @RequestMapping(value = "/upload_any_image", method = RequestMethod.POST)
    @ResponseBody
    public Response<ImageVo> uploadImage(@RequestParam(value = "file") MultipartFile multipartFile) throws
            ServiceException, IllegalStateException, IOException {
        File convertFile = new File(Objects.requireNonNull(multipartFile.getOriginalFilename()));
        multipartFile.transferTo(convertFile);
        BfsFile bfsFile = BfsFile.builder()
                .size(multipartFile.getSize())
                .convFile(convertFile)
                .mimeType(multipartFile.getContentType())
                .fileName(multipartFile.getOriginalFilename())
                .bytes(multipartFile.getBytes())
                .inputStream(multipartFile.getInputStream())
                .build();
        BfsUploadResult result = bfsService.upload(categoryName, bfsFile.getConvFile());
        return Response.SUCCESS(ImageVo.builder()
                .image_url(result.getUrl())
                .image_hash(ImageUtil.buildHashCode(0, com.bilibili.brand.api.common.enums.MaterialType.IMAGE, result.getUrl(), result.getMd5()))
                .image_md5(result.getMd5())
                .build());
    }

    public void mockTestBot(String botUrl, String content) {
        BrandLittleAssistantUtil.sendWithMarkdown(botUrl, content);
    }


    public void mockTestBotV2(String botUrl, String content) {
       okhttp3.RequestBody requestBody = okhttp3.RequestBody.create(GsonUtils.toJson(
               WeiXinGdInfoDTO.builder()
                       .msgtype("markdown")
                       .markdown(WeiXinTextDTO.builder()
                               .content(content)
                               .build())
                       .build()), OkHttpUtils.JSON);
        Request request = new Request.Builder()
                .addHeader("Content-Type", "application/json")
                .url(botUrl)
                .post(requestBody)
                .build();
        try {
            okhttp3.Response response = okHttpClient.newCall(request).execute();
            response.close(); // 内部静默关闭
            if (!response.isSuccessful()) {
                throw new RuntimeException("Request Failure, Code: "
                        + response.code() + " Message: " + response.message());
            }
        } catch (Exception e) {
            //ignore exception
            log.error("[BrandLittleAssistantUtil]:sendWithMarkdown,content={}", content, e);
        }
    }
}
