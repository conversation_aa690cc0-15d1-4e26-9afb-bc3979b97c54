package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import java.util.List;

import com.bilibili.adp.brand.portal.webapi.resource.vo.TargetItemVo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("地域定向信息")
public class AreaTargetVo {
	
	private int category_id;
	private int area_flag;//10-地域分类 11-国家 12-省份 13-城市等
	private List<TargetItemVo> childs;
}
