package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.sf.oval.constraint.NotNull;

/**
 * <AUTHOR>
 * @date 2023/1/16 14:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpdateBidderSourceIdReq {
    @NotNull(message = "排期id不能为空")
    @ApiModelProperty("排期id")
    private Integer scheduleId;

    @NotNull(message = "bidderSourceId不能为空")
    @ApiModelProperty("DSP source id")
    private String bidderSourceId;
}
