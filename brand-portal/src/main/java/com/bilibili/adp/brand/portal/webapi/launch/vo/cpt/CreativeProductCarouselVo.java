package com.bilibili.adp.brand.portal.webapi.launch.vo.cpt;

import com.bilibili.adp.brand.portal.webapi.resource.vo.ImageVo;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/4 14:27
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CreativeProductCarouselVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 轮播图片列表 1到3个
     */
    private List<ImageVo> images;

    /**
     * 主题色
     */
    private String themeColor;

    /**
     * 引导图片
     */
    private ImageVo guideImage;

    /**
     * 可触发开始时间
     */
    private Integer triggerStartTime;

}
