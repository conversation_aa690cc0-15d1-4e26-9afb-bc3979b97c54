package com.bilibili.adp.brand.portal.filter;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.brand.portal.annotation.RateLimit;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.brand.biz.config.business.ConfigCenter;
import com.bilibili.brand.biz.config.business.RateLimitConfig;
import com.bilibili.brand.biz.config.degrade.DynamicRateLimitConfig;
import com.bilibili.business.cmpt.idatabus.client.spring.utils.YamlUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/3/5 18:14
 */
@Slf4j
@Component
public class RateLimitHandlerInterceptor extends HandlerInterceptorAdapter {
    @Autowired
    private ConfigCenter configCenter;
    @Resource(name = "objectRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;
    @Resource(name = "counterRateLimiter")
    private DefaultRedisScript<Long> counterRateLimiterScript;
    private static final String PREFIX = "/web_api/v1/";
    private static final String LOCATION = "classpath:rate_limiter.yaml";
//    private volatile DynamicRateLimitConfig dynamicRateLimitConfig;
//    private ScheduledThreadPoolExecutor scheduledThreadPoolExecutor;

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, Object handler) throws Exception {
        RateLimitConfig rateLimitConfig = configCenter.getRateLimitConfig();
        if (!rateLimitConfig.isEnable()) {
            return true;
        }
        if (handler instanceof HandlerMethod) {
            HandlerMethod mappingHandler = (HandlerMethod) handler;
            RateLimit rateLimit = mappingHandler.getMethodAnnotation(RateLimit.class);
            if (Objects.isNull(rateLimit)) {
                return true;
            }

            Context context = (Context) request.getAttribute("context");
            if (!CollectionUtils.isEmpty(rateLimitConfig.getAccounts())) {
                //临时
                //如果配置了账号，则只限流指定的账号；如果没有账号，则全量限流
                if (!rateLimitConfig.getAccounts().contains(context.getAccountId())) {
                    return true;
                }
            }

            String uri = request.getRequestURI();//  /brand/api/web_api/v1/schedules/ssa/plus/cpt/stock
            String infName = uri.contains(PREFIX) ? uri.substring(PREFIX.length() + uri.indexOf(PREFIX)) : uri; // schedules/ssa/plus/cpt/stock

            Map<String, Integer> resolvedMethods = rateLimitConfig.getResolvedMethods();
            Integer limit = resolvedMethods.getOrDefault(infName, rateLimit.limit());
            String key = this.buildKey(rateLimit, context, infName);
            Object[] values = new Object[]{limit.longValue(), rateLimit.mode().getTimeUnit().toSeconds(1)};
            Long result = redisTemplate.execute(counterRateLimiterScript, Lists.newArrayList(key), values);
            Assert.isTrue(Objects.equals(result, 1L), "当前操作过于频繁，请稍后重试");
        }
        return true;
    }
//
//    @PostConstruct
//    public void init() {
//        scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(1);
//        scheduledThreadPoolExecutor.scheduleWithFixedDelay(this::buildDynamicRateLimitConfig, 1, 5, TimeUnit.SECONDS);
//    }
//
//    private void buildDynamicRateLimitConfig() {
//        DynamicRateLimitConfig tDynamicRateLimitConfig = null;
//        try {
//            tDynamicRateLimitConfig = YamlUtils.getResourceBean(LOCATION, DynamicRateLimitConfig.class);
//        } catch (IOException e) {
//            log.error("[RateLimitHandlerInterceptor]:fail to load dynamicRateLimitConfig from location:" + LOCATION, e);
//        }
//        if (Objects.nonNull(tDynamicRateLimitConfig)) {
//            dynamicRateLimitConfig = tDynamicRateLimitConfig;
//        }
//    }

    private String buildKey(RateLimit rateLimit, Context context, String infName) {
        String roleValue = "";
        switch (rateLimit.role()) {
            case USER:
                roleValue = context.getBilibiliUserName();
                break;
            case ACCOUNT:
                roleValue = String.valueOf(context.getAccountId());
                break;
            case INTERFACE:
                roleValue = "i";
                break;
        }
        return String.format("BRAND_RATE_LIMITER_%s_%s_%s", rateLimit.role().name(), roleValue, infName);
    }
}
