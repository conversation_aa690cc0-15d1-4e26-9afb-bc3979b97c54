package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenScheduleVo {


    @ApiModelProperty(value = "排期ID")
    private Integer id;

    @ApiModelProperty(value = "轮播数")
    private Integer used_rotation_num;

    @ApiModelProperty(value = "投放开始时间")
    private String begin_time;

    @ApiModelProperty(value = "投放结束小时")
    private String end_time;

}