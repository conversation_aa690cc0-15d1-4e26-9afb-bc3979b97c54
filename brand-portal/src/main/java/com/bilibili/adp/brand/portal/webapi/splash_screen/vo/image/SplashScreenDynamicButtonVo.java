package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image;

import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021年6月13日
 * @Description 闪屏动效按钮
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SplashScreenDynamicButtonVo {

    @ApiModelProperty("序列号")
    private Integer seq;

    @ApiModelProperty("跳转引导文案")
    private String guideInstructions;

    @ApiModelProperty("跳转动态图片URL")
	private String jumpImageUrl;

    @ApiModelProperty("跳转动态图片Md5")
    private String jumpImageMd5;

    @ApiModelProperty("唤起动态图片URL")
    private String schemaImageUrl;

    @ApiModelProperty("唤起动态图片Md5")
    private String schemaImageMd5;

    @ApiModelProperty("引导动态图片URL")
    private String guideImageUrl;

    @ApiModelProperty("引导动态图片Md5")
    private String guideImageMd5;

    @ApiModelProperty("滑动引导logo图片URL")
    private String logoImageUrl;

    @ApiModelProperty("滑动引导logo图片hash")
    private String logoImageHash;

    @ApiModelProperty("0-点击交互 1-滑动交互 2-点击滑动 3-纯文字无交互")
    private Integer interactStyle;

    @ApiModelProperty(value = "ios包名")
    private String iosPackageName;

    @ApiModelProperty(value = "android包名")
    private String androidPackageName;

    //此字段用作不分端
    private List<SplashScreenJumpVo> jumpVos;

    //此处为了兼容前端的错误逻辑，此字段用作分端
    private List<SplashScreenJumpVo> splashScreenJumpVos;

    @ApiModelProperty(value = "品牌卡片标题")
    private String brandCardTitle;
    @ApiModelProperty(value = "品牌卡片描述")
    private String brandCardDesc;

    @ApiModelProperty(value = "引导素材类型")
    private Integer guideMaterialType;

}
