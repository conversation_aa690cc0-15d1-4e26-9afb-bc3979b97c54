package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.bilibili.enums.TopViewVideoEnum;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/5 11:15
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TopViewPlusScheduleVo implements Serializable {
    private static final long serialVersionUID = -570518954762807700L;
    @NotNull
    private Integer orderId;

    @NotBlank
    private String name;

    @ApiModelProperty("推广目的 2-落地页 4-应用下载")
    private Integer promotionPurposeType;

    @ApiModelProperty(value = "APP包ID")
    private List<Integer> appPackageIdList;

    @ApiModelProperty("唤起外部APP：0-无须唤起 1-需要唤起")
    private Integer needWakeApp;

    @NotNull
    private Integer resType;

    @NotNull
    private Integer adType;

    @ApiModelProperty("闪屏全半屏样式 1-全屏 2-半屏")
    @NotNull
    private Integer screenStyle;

    @ApiModelProperty(value = "闪屏展示样式 1-全屏图片 2-半屏图片 3-半屏横屏视频 4-半屏竖屏视频 5-全屏竖屏视频")
    @NotNull
    private Integer showStyle;

    @ApiModelProperty("素材点击区域 0-常规区域 1-半屏全素材区域")
    private Integer materialClickArea;

    @ApiModelProperty("交互方式 0-点击交互 1-滑动交互")
    @NotNull
    private Integer interactStyle;

    @ApiModelProperty("跳转模块样式 0-引导说明 1-引导说明+标题")
    @NotNull
    private Integer jumpAreaStyle;

    @ApiModelProperty("跳转模块动效 0-无动效 1-跳动动效")
    @NotNull
    private Integer jumpAreaEffect;

    @ApiModelProperty("按钮类型 0-点击 1-滑动 2-点击滑动 3-选择式 4-纯文字无按钮")
    @NotNull
    private Integer buttonStyle;

    @ApiModelProperty("产品类型")
    private Integer orderProduct;

    @ApiModelProperty("人群包（包含）")
    private List<Integer> crowdPackIds;

    @ApiModelProperty("人群包（排除）")
    private List<Integer> excludeCrowdPackIds;

    @ApiModelProperty("分日预约信息")
    private List<SplitDaysImpressVo> splitDaysImpressVos;

    // 首焦部分
    /**
     * {@link com.bilibili.ssa.platform.common.enums.BannerShowType}
     */
    @ApiModelProperty("首焦媒体类型 1-视频 2-图文 3-直播-视频兜底 4-直播-图片兜底 5-3D视频")
    private Integer hfAdType;

    private Long dealSeq;

    //是否投放内链
    private Boolean launchInnerJump;

    /**
     * topview视频类型
     *
     * @see TopViewVideoEnum
     */
    private Integer topViewVideoType;

    @ApiModelProperty("定向")
    private SsaScheduleTargetVo target;
    //(0, "常规TopView"),
    //(1, "首刷TopView");
    /**
     * {@link com.bilibili.ssa.platform.common.enums.TopViewSellingType}
     */
    private Integer sellingType;
    //地域（城市）
    private Integer areaGroupId;

    private Integer cycleId;

    @ApiModelProperty("唤起应用类型")
    private Integer wakeAppType;

    @ApiModelProperty("频控")
    private Integer frequencyLimit;

    /**
     * 闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频 3.浮窗彩蛋视频
     *
     * @see com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum
     */
    @ApiModelProperty("闪屏视频播放形式，0：普通视频，1：沉浸视频，2：稿件视频 3.浮窗彩蛋视频")
    private Integer ssaVideoPlayMode;

    /**
     * 过渡形式：0-普通过渡 1-自定义过渡
     */
    @ApiModelProperty("过渡形式：0-普通过渡 1-自定义过渡")
    private Integer transitionMode;

}
