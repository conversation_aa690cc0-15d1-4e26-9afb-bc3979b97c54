/**
* <AUTHOR>
* @date  2018年3月7日
*/

package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("订单排期信息")
public class SsaScheduledRotationVo {

	@ApiModelProperty("投放时间")
	@NotNull
	private Timestamp launch_date;

	@ApiModelProperty("投放轮数")
	@NotNull
	private int rotation_num;
}
