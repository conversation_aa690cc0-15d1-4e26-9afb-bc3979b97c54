package com.bilibili.adp.brand.portal.webapi.schedule.vo.ott;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.GdTargetVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.inventory.GdInventoryDetailVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 19:38
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class SsaOttPdScheduleDetailVo extends SsaOttBaseScheduleVo {
    private Integer scheduleId;

    private String orderName;

    private Integer inventories;

    private List<GdInventoryDetailVo> inventoryDetails;

    @JsonProperty("target")
    private GdTargetVo targets;

    private BigDecimal totalPrice;

    @ApiModelProperty("dealGroupId")
    private String dealGroupId;

    @ApiModelProperty("dsp的资源位id，比如京东天宫资源位id")
    private String bidderSourceId;
}
