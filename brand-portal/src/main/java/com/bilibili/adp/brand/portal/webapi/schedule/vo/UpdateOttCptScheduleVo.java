package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * Created by <PERSON>iongyan on 2021/3/28.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateOttCptScheduleVo {

    @NotNull
    private int schedule_id;

    @NotBlank
    private String name;

    @NotEmpty
    private List<SsaScheduledRotationVo> schedules;

}
