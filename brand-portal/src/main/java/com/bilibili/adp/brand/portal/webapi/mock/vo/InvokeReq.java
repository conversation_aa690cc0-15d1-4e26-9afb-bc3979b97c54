package com.bilibili.adp.brand.portal.webapi.mock.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/13 14:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvokeReq {
    /**
     * 接口
     */
    private String method;
    /**
     * 请求参数
     */
    private List<ToolField> reqFields;
}
