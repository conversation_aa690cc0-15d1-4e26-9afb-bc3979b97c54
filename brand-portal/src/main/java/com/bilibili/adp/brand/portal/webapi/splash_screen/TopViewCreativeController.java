package com.bilibili.adp.brand.portal.webapi.splash_screen;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.CommonBvidUtils;
import com.bilibili.adp.brand.portal.common.PlatformSwitchEnum;
import com.bilibili.adp.brand.portal.convert.creative.*;
import com.bilibili.adp.brand.portal.convert.schedule.GdScheduleConverter;
import com.bilibili.adp.brand.portal.webapi.launch.vo.CreativeReportExportVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.CreativeBaseVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ImageVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ShareInfoVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.AvailablePlatformVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.GdTargetAggregationVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenCustomizedVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenDynamicButtonVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.log.SsaLogOperationVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view.*;
import com.bilibili.adp.brand.portal.webapi.statistic.ExportController;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.CreativeImageType;
import com.bilibili.adp.common.enums.PreviewStatusEnum;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.booking.service.ITopViewResourceService;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.order.dto.QueryOrderParamDto;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleTargetDto;
import com.bilibili.brand.biz.common.ScheduleCompareProperties;
import com.bilibili.brand.dto.common.AppPackageDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.crm.platform.soa.ISoaCrmContractService;
import com.bilibili.enums.GdJumpType;
import com.bilibili.enums.PlatformType;
import com.bilibili.brand.api.creative.dto.*;
import com.bilibili.brand.api.creative.service.ICreativePreviewService;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.launch.dto.ImageHash;
import com.bilibili.brand.api.log.service.IGdLogService;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IGdOrderService;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.TopViewRotation;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.biz.creative.handler.TopViewCreativePreviewHandler;
import com.bilibili.brand.platform.report.api.dto.StatCreativeDto;
import com.bilibili.brand.platform.report.api.dto.StatSplashScreenDto;
import com.bilibili.brand.platform.report.api.service.IStatCreativeService;
import com.bilibili.brand.platform.report.api.service.IStatSplashScreenService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.api.log.dto.CptLogOperationDto;
import com.bilibili.cpt.platform.common.GdLogFlag;
import com.bilibili.cpt.platform.common.ButtonInteractStyleEnum;
import com.bilibili.cpt.platform.common.IpVideoSizeEnum;
import com.bilibili.cpt.platform.portal.webapi.creative.vo.ButtonCopyVo;
import com.bilibili.cpt.platform.portal.webapi.creative.vo.TemplateVo;
import com.bilibili.cpt.platform.util.SsaUtils;
import com.bilibili.enums.WakeAppType;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.common.ButtonCopyTypeEnum;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenBaseImageService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenJumpInfoService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenVideoService;
import com.bilibili.ssa.platform.api.upos.service.ISsaUposVideoService;
import com.bilibili.ssa.platform.biz.po.SsaSplashScreenCustomUrlPo;
import com.bilibili.ssa.platform.biz.service.splash_screen.SsaSplashScreenCustomUrlService;
import com.bilibili.ssa.platform.common.enums.*;
import com.bilibili.utils.ArrayUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/web_api/v1/creative/top_view")
@Api(value = "/creative/top_view", description = "TopView创意相关")
public class TopViewCreativeController extends ExportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(TopViewCreativeController.class);

    @Autowired
    private ITopViewCreativeService topViewCreativeService;

    @Autowired
    private ISsaSplashScreenBaseImageService ssaSplashScreenBaseImageService;

    @Autowired
    private IGdOrderService gdOrderService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Autowired
    private ISsaUposVideoService ssaUposVideoService;

    @Autowired
    private ISsaSplashScreenVideoService ssaSplashScreenVideoService;

    @Autowired
    private IGdLogService gdLogService;

    @Autowired
    private IStatSplashScreenService statSplashScreenService;

    @Autowired
    private IStatCreativeService statCreativeService;

    @Autowired
    private ITopViewResourceService topViewResourceService;

    @Autowired
    private TopViewCreativePreviewHandler topViewCreativePreviewHandler;

    @Autowired
    private SsaSplashScreenConvert ssaSplashScreenConvert;

    @Value("${topView.preview.maxShowTime.minute}")
    private Integer topViewPreviewMaxMinute;

    @Autowired
    private TopViewCreativeConvert topViewCreativeConvert;

    @Autowired
    private ICreativePreviewService previewService;

    @Autowired
    private ISoaCrmContractService soaCrmContractService;

    @Autowired
    private SsaSplashScreenCustomUrlService ssaSplashScreenCustomUrlService;

    @Autowired
    private ISsaSplashScreenJumpInfoService ssaSplashScreenJumpInfoService;

    @Autowired
    private GdCreativeConvert gdCreativeConvert;

    @ApiOperation(value = "新建运营TopView")
    @RequestMapping(value = "/external", method = RequestMethod.POST)
    @ResponseBody
    public Response<Integer> createExternalTopView(
            @ApiIgnore Context context,
            @Valid @ApiParam(required = true, value = "TopView信息") @RequestBody NewExternalTopViewVo vo) {
        topViewCreativeService.createExternal(topViewCreativeConvert.convertToNewExternalTopViewDto(vo), getOperator(context));
        return Response.SUCCESS(0);
    }

    @ApiOperation(value = "编辑运营TopView")
    @RequestMapping(value = "/external", method = RequestMethod.PUT)
    @ResponseBody
    public Response<Integer> updateExternalTopView(
            @ApiIgnore Context context,
            @Valid @ApiParam(required = true, value = "TopView信息") @RequestBody UpdateExternalTopViewVo vo) {
        topViewCreativeService.updateExternal(topViewCreativeConvert.convertToUpdateExternalTopViewDto(vo),
                getOperator(context));
        return Response.SUCCESS(0);
    }

    @ApiOperation(value = "删除TopView")
    @RequestMapping(value = "/{top_view_id}", method = RequestMethod.DELETE)
    @ResponseBody
    public Response<Boolean> deleteTopView(
            @ApiIgnore Context context,
            @ApiParam("TopView ID") @PathVariable("top_view_id") Integer topViewId) {
        topViewCreativeService.deleteByTopViewId(topViewId, getOperator(context));
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "批量删除TopView")
    @RequestMapping(value = "", method = RequestMethod.DELETE)
    @ResponseBody
    public Response<Boolean> batchDeleteTopView(
            @ApiIgnore Context context,
            @ApiParam("闪屏ID，多个以逗号分隔") @RequestParam("top_view_ids") List<Integer> topViewIdList) {
        topViewCreativeService.deleteByTopViewIdList(topViewIdList, getOperator(context));
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "查询TopView创意列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<ExternalTopViewListVo>>> getTopViewList(
            @ApiIgnore Context context,
            @ApiParam("合同号") @RequestParam(value = "contract_number", required = false) Long contractNumber,
            @ApiParam("TopView订单ID") @RequestParam(value = "order_id", required = false) Integer gdOrderId,
            @ApiParam("产品类型") @RequestParam(value = "order_product", required = false) Integer orderProduct,
            @ApiParam("TopView排期id") @RequestParam(value = "schedule_id", required = false) Integer gdScheduleId,
            @ApiParam("TopView ID") @RequestParam(value = "top_view_id", required = false) Integer topViewId,
            @ApiParam("TopView创意名称") @RequestParam(value = "creative_name", required = false) String creativeName,
            @ApiParam("TopView创意状态 1-待审核 2-已驳回 3-待上线 4-上线中 5-已暂停 6-已完成 7-已删除") @RequestParam(value = "top_view_status", required = false, defaultValue = "0") Integer topViewStatus,
            @ApiParam("创建人") @RequestParam(value = "creator_name", required = false) String likeCreatorName,
            @ApiParam("开始时间") @RequestParam(value = "from_time", required = false) Long fromTime,
            @ApiParam("截止时间") @RequestParam(value = "to_time", required = false) Long toTime,
            @ApiParam("页码") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("页长") @RequestParam(value = "size", required = false, defaultValue = "15") Integer size
    ) throws ServiceException {
        // 先通过订单信息过滤
        Timestamp beginDate = Optional.ofNullable(fromTime)
                .map(Utils::getTimestamp)
                .orElse(null);
        Timestamp endDate = Optional.ofNullable(toTime)
                .map(Utils::getTimestamp)
                .orElse(null);
        QueryOrderParamDto queryOrderParamDto = QueryOrderParamDto.builder()
                .accountId(context.getAccountId())
                .contractNumber(contractNumber)
                .orderIds(ArrayUtils.asList(gdOrderId))
                .product(orderProduct)
                .beginDate(beginDate)
                .endDate(endDate)
                .build();
        List<GdOrderDto> gdOrderDtoList = gdOrderService.queryOrders(queryOrderParamDto);
        List<Integer> orderIdList = gdOrderDtoList.stream().map(GdOrderDto::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return Response.SUCCESS(Pagination.emptyPagination());
        }
        QueryTopViewCreativeDto queryTopViewCreativeDto = QueryTopViewCreativeDto.builder()
                .accountId(context.getAccountId())
                .gdOrderIdList(orderIdList)
                .gdScheduleId(gdScheduleId)
                .topViewId(topViewId)
                .creativeName(creativeName)
                .statusList(Utils.isPositive(topViewStatus) ? Lists.newArrayList(topViewStatus) : TopViewCreativeStatus.NOT_DELETED_STATUS)
                .fromTime(beginDate)
                .toTime(endDate)
                .creatorName(likeCreatorName)
                .salesType(OrderProduct.getByCode(orderProduct).getSalesType().getCode())
                .build();
        PageResult<TopViewCreativeListDto> pageResult = topViewCreativeService.queryForPage(queryTopViewCreativeDto, page, size);
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                convertToExternalTopViewListVoList(pageResult.getRecords(), fromTime, toTime, orderProduct)));
    }

    @ApiOperation(value = "分页导出TopView创意列表")
    @RequestMapping(value = "/list/export", method = RequestMethod.GET)
    @ResponseBody
    public void exportCreativeList(
            HttpServletResponse response,
            @ApiIgnore Context context,
            @ApiParam("合同号") @RequestParam(value = "contract_number", required = false) Long contractNumber,
            @ApiParam("TopView订单ID") @RequestParam(value = "order_id", required = false) Integer gdOrderId,
            @ApiParam("产品类型") @RequestParam(value = "order_product", required = false) Integer orderProduct,
            @ApiParam("TopView排期id") @RequestParam(value = "schedule_id", required = false) Integer gdScheduleId,
            @ApiParam("TopView ID") @RequestParam(value = "top_view_id", required = false) Integer topViewId,
            @ApiParam("TopView创意名称") @RequestParam(value = "creative_name", required = false) String creativeName,
            @ApiParam("TopView创意状态 1-待审核 2-已驳回 3-待上线 4-上线中 5-已暂停 6-已完成 7-已删除") @RequestParam(value = "top_view_status", required = false, defaultValue = "0") Integer topViewStatus,
            @ApiParam("创建人") @RequestParam(value = "creator_name", required = false) String likeCreatorName,
            @ApiParam("开始时间") @RequestParam(value = "from_time", required = false) Long fromTime,
            @ApiParam("截止时间") @RequestParam(value = "to_time", required = false) Long toTime,
            @ApiParam("页码") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("页长") @RequestParam(value = "size", required = false, defaultValue = "15") Integer size
    ) throws Exception {
        // 分页查询创意列表
        Response<Pagination<List<ExternalTopViewListVo>>> paginationResponse = this.getTopViewList(context, contractNumber, gdOrderId, orderProduct, gdScheduleId, topViewId, creativeName, topViewStatus, likeCreatorName, fromTime, toTime, page, size);

        // 查询关联信息
        List<ExternalTopViewListVo> externalTopViewListVoList = paginationResponse.getResult().getData();
        List<Integer> ssaSplashScreenIdList = externalTopViewListVoList.stream()
                .map(ExternalTopViewListVo::getSplash_screen_id)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, List<SsaSplashScreenCustomUrlPo>> customizedImpUrlListMap = ssaSplashScreenCustomUrlService.getCustomizedUrlPoListByIdList(ssaSplashScreenIdList, CustomizedUrlType.SHOW_URL)
                .stream()
                .collect(Collectors.groupingBy(SsaSplashScreenCustomUrlPo::getSplashScreenId));
        Map<Integer, List<SsaSplashScreenCustomUrlPo>> customizedClickUrlListMap = ssaSplashScreenCustomUrlService.getCustomizedUrlPoListByIdList(ssaSplashScreenIdList, CustomizedUrlType.CLICK_URL)
                .stream()
                .collect(Collectors.groupingBy(SsaSplashScreenCustomUrlPo::getSplashScreenId));
        Map<Integer, List<SplashScreenJumpDTO>> jumpInfoMap = ssaSplashScreenJumpInfoService.getSimpleJumpInfoMap(ssaSplashScreenIdList);

        List<Integer> scheduleIdList = externalTopViewListVoList.stream()
                .map(ExternalTopViewListVo::getSchedule_id)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, ScheduleDto> scheduleDtoMap = queryScheduleService.getSchedulesInIds(scheduleIdList)
                .stream()
                .collect(Collectors.toMap(ScheduleDto::getScheduleId, Function.identity()));

        // 转换为导出对象
        List<CreativeReportExportVo> creativeReportExportVoList = externalTopViewListVoList.stream()
                .map(externalTopViewListVo -> {
                    // mapper转换为exportVo
                    CreativeReportExportVo vo = GdCreativeConverter.MAPPER.toCreativeReportExportVo(externalTopViewListVo);

                    // 其他基本属性
                    vo.setCreative_id(String.valueOf(externalTopViewListVo.getTop_view_id()));

                    ScheduleDto scheduleDto = scheduleDtoMap.get(externalTopViewListVo.getSchedule_id());
                    if (Objects.nonNull(scheduleDto)) {
                        // 定向信息
                        GdTargetAggregationVo targetAggregationVo = GdScheduleConverter.MAPPER.toTargetAggregationVo(scheduleDto.getTargetDto());
                        if (Objects.nonNull(targetAggregationVo)) {
                            vo.setTarget(targetAggregationVo.toExportString(false));
                        }
                        // 模板信息
                        if (Utils.isPositive(scheduleDto.getTemplateId())) {
                            vo.setTemplate_info(String.format("%d-%s", scheduleDto.getTemplateId(), scheduleDto.getTemplateName()));
                        }
                        // 平台名称
                        if (StringUtils.hasText(scheduleDto.getPlatformName())) {
                            vo.setPlatform_name(scheduleDto.getPlatformName());
                        }
                    }

                    // 唤起链接和跳转链接
                    List<SplashScreenJumpDTO> splashScreenJumpDTOList = jumpInfoMap.get(externalTopViewListVo.getSplash_screen_id());
                    if (!CollectionUtils.isEmpty(splashScreenJumpDTOList)) {
                        String schemeUrl = splashScreenJumpDTOList.stream()
                                .filter(item -> StringUtils.hasText(item.getSchemeUrl()))
                                .map(item -> {
                                    PlatformType platformType = PlatformType.getByCode(item.getPlatformId());
                                    return platformType.getDesc() + "->" + item.getSchemeUrl();
                                })
                                .distinct()
                                .collect(Collectors.joining(";"));
                        vo.setScheme_url(schemeUrl);

                        String jumpLink = splashScreenJumpDTOList.stream()
                                .filter(item -> StringUtils.hasText(item.getJumpLink()))
                                .map(item -> {
                                    PlatformType platformType = PlatformType.getByCode(item.getPlatformId());
                                    return platformType.getDesc() + "->" + item.getJumpLink();
                                })
                                .distinct()
                                .collect(Collectors.joining(";"));
                        vo.setPromotion_purpose_content(jumpLink);
                    }

                    // 展示监控链接
                    List<SsaSplashScreenCustomUrlPo> customizedImpUrlPoList = customizedImpUrlListMap.get(externalTopViewListVo.getSplash_screen_id());
                    if (!CollectionUtils.isEmpty(customizedImpUrlPoList)) {
                        String impMonitorUrl = customizedImpUrlPoList.stream()
                                .map(item -> {
                                    PlatformType platformType = PlatformType.getByCode(item.getPlatformId());
                                    return platformType.getDesc() + "->" + item.getCustomUrl();
                                })
                                .collect(Collectors.joining(";"));
                        vo.setImp_monitor_urls(impMonitorUrl);
                    }
                    // 点击监控链接
                    List<SsaSplashScreenCustomUrlPo> customizedClickUrlPoList = customizedClickUrlListMap.get(externalTopViewListVo.getSplash_screen_id());
                    if (Objects.nonNull(customizedClickUrlPoList)) {
                        String clickMonitorUrl = customizedClickUrlPoList.stream()
                                .map(item -> {
                                    PlatformType platformType = PlatformType.getByCode(item.getPlatformId());
                                    return platformType.getDesc() + "->" + item.getCustomUrl();
                                })
                                .collect(Collectors.joining(";"));
                        vo.setClick_monitor_urls(clickMonitorUrl);
                    }

                    return vo;
                }).collect(Collectors.toList());

        // 执行导出操作
        this.exportExcel(response, creativeReportExportVoList, String.format("创意明细_%d", System.currentTimeMillis()));
    }

    private List<ExternalTopViewListVo> convertToExternalTopViewListVoList(List<TopViewCreativeListDto> dtoList,
                                                                           Long beginTime, Long endTime,
                                                                           Integer orderProduct) throws ServiceException {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        List<Integer> orderIdList = dtoList.stream().map(TopViewCreativeListDto::getTopViewDto)
                .map(GdTopViewDto::getGdOrderId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, GdOrderDto> gdOrderDtoMap = gdOrderService.getOrderMapInOrderIds(orderIdList);

        List<Integer> crmContractIdList = gdOrderDtoMap.values()
                .stream()
                .map(GdOrderDto::getCrmContractId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, ContractDto> contractDtoMap = soaCrmContractService.queryContractMapInIds(crmContractIdList);

        List<Integer> scheduleIdList = dtoList.stream().map(TopViewCreativeListDto::getTopViewDto)
                .map(GdTopViewDto::getGdScheduleId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, ScheduleDto> gdScheduleMap = queryScheduleService.getScheduleMapInIds(scheduleIdList);

        List<Integer> ssaCreativeIdList = dtoList.stream().map(TopViewCreativeListDto::getSsaCreativeDto)
                .map(SsaSplashScreenDto::getId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, SsaSplashScreenBaseImageDto> ssaSplashScreenBaseImageDtoMap =
                ssaSplashScreenBaseImageService.getHorizontalImageMapBySplashScreenIds(ssaCreativeIdList);

        //老版首焦视频素材
        List<Integer> hfVideoBizIdList = dtoList.stream().map(topViewCreativeListDto -> {
            GdCreativeDto hfIosCreativeDto = topViewCreativeListDto.getHfIosCreativeDto();
            if (hfIosCreativeDto != null && hfIosCreativeDto.getCreativeVideoDto() != null
                    && hfIosCreativeDto.getCreativeVideoDto().getBizId() != null) {
                return hfIosCreativeDto.getCreativeVideoDto().getBizId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, SsaUposVideoDto> hfVideoMap = ssaUposVideoService.getBizId2UposVideoMapInBizIds(hfVideoBizIdList);

        //新版首焦视频素材
        List<Integer> newHfVideoBizIdList = dtoList.stream().map(topViewCreativeListDto -> {
            GdCreativeDto hfIosCreativeDto = topViewCreativeListDto.getNewHfIosCreativeDto();
            if (hfIosCreativeDto != null && hfIosCreativeDto.getCreativeVideoDto() != null
                    && hfIosCreativeDto.getCreativeVideoDto().getBizId() != null) {
                return hfIosCreativeDto.getCreativeVideoDto().getBizId();
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Integer, SsaUposVideoDto> newHfVideoMap = ssaUposVideoService.getBizId2UposVideoMapInBizIds(newHfVideoBizIdList);
        int salesType = OrderProduct.getByCode(orderProduct).getSalesType().getCode();
        Map<Integer, StatSplashScreenDto> ssaCreativeStatMap =
                statSplashScreenService.getSplashScreenStatMapInScreenIds(ssaCreativeIdList, salesType);
        //获取创建预览的信息
        List<Integer> topViewIds = dtoList.stream().map(t -> t.getTopViewDto().getId()).collect(Collectors.toList());
        Map<Long, LocalDateTime> creativeToPreviewEndTimeMap = previewService.getCreativeToPreviewEndTimeMap(
                topViewIds.stream().map(t -> CreativePreviewDto.builder()
                                .creativeId((long) t)
                                .salesType(salesType)
                                .build())
                        .collect(Collectors.toList()));

        Map<Long, StatCreativeDto> hfCreativeStatMap = getStatInfo(dtoList, beginTime, endTime);

        return dtoList.stream().map(topViewListDto -> {
            GdTopViewDto topViewDto = topViewListDto.getTopViewDto();
            List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
            if (Utils.isPositive(topViewDto.getNewHfIosCreativeId())) {
                hfCreativeIdList.add(topViewDto.getNewHfIosCreativeId());
            }
            if (Utils.isPositive(topViewDto.getNewHfAndroidCreativeId())) {
                hfCreativeIdList.add(topViewDto.getNewHfAndroidCreativeId());
            }
            //todo 临时逻辑，后续查询需要校验排期
            ScheduleDto scheduleDto = gdScheduleMap.getOrDefault(topViewDto.getGdScheduleId(), ScheduleDto.builder()
                    .promotionPurposeType(1).build());
            SsaSplashScreenDto ssaCreativeDto = topViewListDto.getSsaCreativeDto();
            GdCreativeDto hfIosCreativeDto = topViewListDto.getHfIosCreativeDto();
            if (hfIosCreativeDto == null) {
                hfIosCreativeDto = new GdCreativeDto();
            }
            GdCreativeDto anyOneCreative = Optional.ofNullable(topViewListDto.getNewHfIosCreativeDto()).orElse(topViewListDto.getNewHfAndroidCreativeDto());
            if (anyOneCreative == null) {
                SsaSplashScreenDto ssaSplashScreenDto = Optional.ofNullable(topViewListDto.getSsaCreativeDto()).orElse(new SsaSplashScreenDto());
                anyOneCreative = GdCreativeDto.builder()
                        .beginTime(TimeUtil.isoTimeStr2Timestamp(ssaSplashScreenDto.getStartTime()))
                        .endTime(TimeUtil.isoTimeStr2Timestamp(ssaSplashScreenDto.getEndTime()))
                        .build();
            }
            SsaUposVideoDto uposVideoDto = (hfIosCreativeDto.getCreativeVideoDto() != null
                    && hfIosCreativeDto.getCreativeVideoDto().getBizId() != null
                    ? hfVideoMap.get(hfIosCreativeDto.getCreativeVideoDto().getBizId())
                    : null);

            SsaUposVideoDto newUposVideoDto = (anyOneCreative.getCreativeVideoDto() != null
                    && anyOneCreative.getCreativeVideoDto().getBizId() != null
                    ? newHfVideoMap.get(anyOneCreative.getCreativeVideoDto().getBizId())
                    : null);

            StatSplashScreenDto ssaCreativeStatDto = ssaCreativeStatMap.getOrDefault(ssaCreativeDto.getId(),
                    StatSplashScreenDto.builder()
                            .showAccount(0L)
                            .clickCount(0)
                            .clickRate(BigDecimal.ZERO)
                            .build());
            List<StatCreativeDto> originStatList = new ArrayList<>(2);
            if (topViewDto.getHfIosCreativeId() != null) {
                originStatList.add(hfCreativeStatMap.getOrDefault(topViewDto.getHfIosCreativeId(),
                        StatCreativeDto.builder()
                                .showAccount(0L)
                                .clickCount(0)
                                .build()));
            }
            if (topViewDto.getHfAndroidCreativeId() != null) {
                originStatList.add(hfCreativeStatMap.getOrDefault(topViewDto.getHfAndroidCreativeId(),
                        StatCreativeDto.builder()
                                .showAccount(0L)
                                .clickCount(0)
                                .build()));
            }
            StatCreativeDto hfStat = calculateHfCreativeStat(originStatList);

            List<StatCreativeDto> newOriginStatList = new ArrayList<>(2);
            if (topViewDto.getNewHfIosCreativeId() != null) {
                newOriginStatList.add(hfCreativeStatMap.getOrDefault(topViewDto.getNewHfIosCreativeId(),
                        StatCreativeDto.builder()
                                .showAccount(0L)
                                .clickCount(0)
                                .build()));
            }
            if (topViewDto.getNewHfAndroidCreativeId() != null) {
                newOriginStatList.add(hfCreativeStatMap.getOrDefault(topViewDto.getNewHfAndroidCreativeId(),
                        StatCreativeDto.builder()
                                .showAccount(0L)
                                .clickCount(0)
                                .build()));
            }
            StatCreativeDto newHfStat = calculateHfCreativeStat(newOriginStatList);

            LocalDateTime previewEndTime = creativeToPreviewEndTimeMap.get((long) topViewDto.getId());

            //获取其中一个有效的创意即可，主要是获取创意的时间
            GdCreativeDto firstValidGdCreative = topViewListDto.getHfIosCreativeDto();
            if (Objects.isNull(firstValidGdCreative) || Objects.isNull(firstValidGdCreative.getCreativeId())) {
                firstValidGdCreative = topViewListDto.getNewHfIosCreativeDto();
            }
            if (Objects.isNull(firstValidGdCreative) || Objects.isNull(firstValidGdCreative.getCreativeId())) {
                firstValidGdCreative = topViewListDto.getHfAndroidCreativeDto();
            }
            if (Objects.isNull(firstValidGdCreative) || Objects.isNull(firstValidGdCreative.getCreativeId())) {
                firstValidGdCreative = topViewListDto.getNewHfAndroidCreativeDto();
            }
            if (Objects.isNull(firstValidGdCreative)) {
                SsaSplashScreenDto ssaSplashScreenDto = Optional.ofNullable(topViewListDto.getSsaCreativeDto()).orElse(new SsaSplashScreenDto());
                firstValidGdCreative = GdCreativeDto.builder()
                        .beginTime(TimeUtil.isoTimeStr2Timestamp(ssaSplashScreenDto.getStartTime()))
                        .endTime(TimeUtil.isoTimeStr2Timestamp(ssaSplashScreenDto.getEndTime()))
                        .build();
            }

            List<ImageVo> newHfImages = convertImageDtos2Vos(anyOneCreative.getGdCreativeImageDtos(),
                    anyOneCreative.getTemplateId(), CreativeImageType.DUAL_ROW.getCode());

            ManuscriptInfoBO manuscriptInfo = anyOneCreative.getManuscriptInfo();
            boolean isTopViewArchive = manuscriptInfo != null && Utils.isPositive(manuscriptInfo.getAid());
            if (isTopViewArchive) {
                newHfImages = Collections.singletonList(ImageVo.builder()
                        .image_url(manuscriptInfo.getCoverUrl())
                        .build());
            }

            String orderName = null;
            Long contractNumber = null;
            String contractName = null;
            GdOrderDto gdOrderDto = gdOrderDtoMap.get(topViewDto.getGdOrderId());
            if (Objects.nonNull(gdOrderDto)) {
                orderName = gdOrderDto.getOrderName();
                ContractDto contractDto = contractDtoMap.get(gdOrderDto.getCrmContractId());
                if (Objects.nonNull(contractDto)) {
                    contractNumber = contractDto.getContractNumber();
                    contractName = contractDto.getName();
                }
            }
            return ExternalTopViewListVo.builder()
                    .contract_number(String.valueOf(contractNumber))
                    .contract_name(contractName)
                    .top_view_id(topViewDto.getId())
                    .creative_name(topViewDto.getCreativeName())
                    .order_id(topViewDto.getGdOrderId())
                    .order_name(orderName)
                    .schedule_id(topViewDto.getGdScheduleId())
                    .schedule_name(scheduleDto.getName())
                    .status(topViewDto.getStatus())
                    .status_desc(TopViewCreativeStatus.getByCodeWithValidation(topViewDto.getStatus()).getDesc())
                    .reason(ssaCreativeDto.getReason())
                    .cm_mark(ssaCreativeDto.getCmMark())
                    .cm_mark_name(SsaCmMark.getByCode(ssaCreativeDto.getCmMark()).getDesc())
                    .time_segments(TimeUtil.timestampToIsoTimeStr(firstValidGdCreative.getBeginTime())
                            + " - "
                            + TimeUtil.timestampToIsoTimeStr(firstValidGdCreative.getEndTime()))
                    .creator_name(ssaCreativeDto.getCreatorName())
                    .splash_screen_id(ssaCreativeDto.getId())
                    .ssa_title(ssaCreativeDto.getTitle())
                    .ssa_video(ssaSplashScreenVideoDto2Vo(ssaCreativeDto.getVideo()))
                    .egg_video(ssaSplashScreenVideoDto2EggVo(ssaCreativeDto.getEggVideo()))
                    .ssa_base_image_list(Lists.newArrayList(getCompressBaseImageVoByDto(
                            ssaSplashScreenBaseImageDtoMap.get(topViewDto.getSsaCreativeId().intValue()))))
                    .ssa_show_count(Integer.parseInt(String.valueOf(ssaCreativeStatDto.getShowAccount())))
                    .ssa_click_count(ssaCreativeStatDto.getClickCount())
                    .ssa_click_rate(ssaCreativeStatDto.getClickRate())
                    .hf_creative_ids((topViewDto.getHfIosCreativeId() == null
                            || topViewDto.getHfIosCreativeId() == 0) ?
                            null : Lists.newArrayList(topViewDto.getHfIosCreativeId(), topViewDto.getHfAndroidCreativeId()))
                    .hf_title(hfIosCreativeDto.getTitle())
                    .hf_image_urls(hfIosCreativeDto.getImageUrl() == null ?
                            null : Lists.newArrayList(hfIosCreativeDto.getImageUrl()))
                    .hf_video(ssaUposVideoDto2Vo(uposVideoDto))
                    .hf_show_count(Integer.parseInt(String.valueOf(hfStat.getShowAccount())))
                    .hf_click_count(hfStat.getClickCount())
                    .hf_click_rate(hfStat.getClickRate())
                    .new_hf_creative_ids(hfCreativeIdList)
                    .new_hf_title(anyOneCreative.getTitle())
                    .new_hf_image_urls(newHfImages)
                    .new_hf_video(ssaUposVideoDto2Vo(newUposVideoDto))
                    .new_hf_show_count(Integer.parseInt(String.valueOf(newHfStat.getShowAccount())))
                    .new_hf_click_count(newHfStat.getClickCount())
                    .new_hf_click_rate(newHfStat.getClickRate())
                    .sales_type(topViewDto.getSalesType())
                    .preview_end_time(previewEndTime == null ? null : TimeUtil.getMilli(previewEndTime))
                    .preview_status(this.getPreviewStatus(topViewDto.getStatus(),
                            anyOneCreative.getBeginTime(),
                            anyOneCreative.getEndTime(),
                            previewEndTime))
                    .promotion_purpose_type(scheduleDto.getPromotionPurposeType())
                    .promotion_purpose_type_desc(PromotionPurposeType.getByCode(scheduleDto.getPromotionPurposeType())
                            .getDesc())
                    .order_product(orderProduct)
                    .new_hf_jump_type(anyOneCreative.getJumpType())
                    .new_hf_promotion_purpose_content(this.getVoPromotionPurposeContent(anyOneCreative.getJumpType(),
                            anyOneCreative.getPromotionPurposeContent()))
                    .actual_new_hf_promotion_purpose_content(anyOneCreative.getPromotionPurposeContent())
                    .build();
        }).collect(Collectors.toList());

    }


    private Map<Long, StatCreativeDto> getStatInfo(List<TopViewCreativeListDto> dtoList, Long beginTime, Long endTime) {
        List<Long> allHfCreativeIdList = new ArrayList<>(dtoList.size() * 4);
        for (TopViewCreativeListDto listDto : dtoList) {
            GdTopViewDto topViewDto = listDto.getTopViewDto();
            if (topViewDto == null) {
                continue;
            }
            if (topViewDto.getHfIosCreativeId() != null) {
                allHfCreativeIdList.add(topViewDto.getHfIosCreativeId());
            }
            if (topViewDto.getHfAndroidCreativeId() != null) {
                allHfCreativeIdList.add(topViewDto.getHfAndroidCreativeId());
            }
            if (topViewDto.getNewHfIosCreativeId() != null) {
                allHfCreativeIdList.add(topViewDto.getNewHfIosCreativeId());
            }
            if (topViewDto.getNewHfAndroidCreativeId() != null) {
                allHfCreativeIdList.add(topViewDto.getNewHfAndroidCreativeId());
            }
        }
        List<StatCreativeDto> statCreativeDtoList = statCreativeService.getInCreativeIdsGroupByTime(allHfCreativeIdList,
                beginTime != null ? new Timestamp(beginTime) : null,
                endTime != null ? new Timestamp(endTime) : null);
        return statCreativeDtoList.stream()
                .collect(Collectors.toMap(StatCreativeDto::getCreativeId, Function.identity()));
    }

    private int getPreviewStatus(int status, Timestamp gdBeginDate, Timestamp gdEndDate,
                                 LocalDateTime previewEndTime) {

        if (topViewCreativePreviewHandler.validateCreativeStatus(status)
                || !topViewCreativePreviewHandler.validateScheduleDate(gdBeginDate, gdEndDate)) {
            return PreviewStatusEnum.FORBID.getCode();
        }

        if (previewEndTime != null && previewEndTime.isAfter(LocalDateTime.now())) {
            return PreviewStatusEnum.PREWIEWING.getCode();
        }

        return PreviewStatusEnum.ABLE.getCode();
    }

    private SplashScreenBaseImageVo getCompressBaseImageVoByDto(SsaSplashScreenBaseImageDto dto) {
        if (dto == null) {
            return SplashScreenBaseImageVo.builder()
                    .hash("")
                    .url("")
                    .type(0)
                    .build();
        } else {
            BaseImageTypeEnum imageType = BaseImageTypeEnum.getByCode(dto.getType());
            return SplashScreenBaseImageVo.builder()
                    .hash(dto.getHash())
                    .url(dto.getUrl().concat(SsaUtils.getCompressImageSuffix(imageType.getWidth(), imageType.getHeight())))
                    .type(dto.getType())
                    .build();
        }
    }

    private TopViewVideoVo ssaSplashScreenVideoDto2Vo(SsaSplashScreenVideoDto dto) {
        if (dto == null) {
            return null;
        }
        return TopViewVideoVo.builder()
                .upos_url(dto.getUposUrl())
                .xcode_md5(dto.getXcodeMd5())
                .xcode_height(dto.getXcodeHeight())
                .xcode_width(dto.getXcodeWidth())
                .id(dto.getId())
                .biz_id(dto.getBizId())
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenVideoStatus.getByCode(dto.getStatus()).getDesc())
                .xcode_upos_url(dto.getXcodeUposUrl())
                .file_name(dto.getFileName())
                .upos_auth(dto.getUposAuth())
                .url(dto.getUrl())
                .xcode_before_url(dto.getXcodeBeforeUrl())
                .build();
    }

    private TopViewVideoVo ssaSplashScreenVideoDto2EggVo(SsaSplashScreenVideoDto dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        // 这里的id和bizId互换，因为前端认id在编辑详情页去ip库里面拿素材
        return TopViewVideoVo.builder()
                .upos_url(dto.getUposUrl())
                .xcode_md5(dto.getXcodeMd5())
                .xcode_height(dto.getXcodeHeight())
                .xcode_width(dto.getXcodeWidth())
                .id(dto.getBizId())
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenVideoStatus.getByCode(dto.getStatus()).getDesc())
                .xcode_upos_url(dto.getXcodeUposUrl())
                .file_name(dto.getFileName())
                .upos_auth(dto.getUposAuth())
                .url(dto.getUrl())
                .xcode_before_url(dto.getXcodeBeforeUrl())
                .build();
    }


    private TopViewVideoVo ssaUposVideoDto2Vo(SsaUposVideoDto dto) {
        if (dto == null) {
            return null;
        }
        return TopViewVideoVo.builder()
                .upos_url(dto.getUposUrl())
                .xcode_md5(dto.getXcodeMd5())
                .xcode_height(dto.getXcodeHeight())
                .xcode_width(dto.getXcodeWidth())
                .id(dto.getId())
                .biz_id(dto.getBizId())
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenVideoStatus.getByCode(dto.getStatus()).getDesc())
                .xcode_upos_url(dto.getXcodeUposUrl())
                .file_name(dto.getFileName())
                .upos_auth(dto.getUposAuth())
                .url(ssaSplashScreenVideoService.getActualVideoUrl(dto.getXcodeUposUrl()))
                .xcode_before_url(ssaSplashScreenVideoService.getActualBeforeVideoUrl(dto.getUposUrl()))
                .build();
    }

    private StatCreativeDto calculateHfCreativeStat(List<StatCreativeDto> statList) {
        Long showCount = 0L;
        Integer clickCount = 0;
        for (StatCreativeDto stat : statList) {
            if (stat.getShowAccount() != null) {
                showCount += stat.getShowAccount();
            }
            if (stat.getClickCount() != null) {
                clickCount += stat.getClickCount();
            }
        }
        BigDecimal clickRate = (clickCount <= 0 || showCount <= 0 ? BigDecimal.ZERO
                : new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100)));

        return StatCreativeDto.builder()
                .showAccount(showCount)
                .clickCount(clickCount)
                .clickRate(clickRate)
                .build();
    }

    @ApiOperation(value = "查询TopView创意详情")
    @RequestMapping(value = "/{top_view_id}", method = RequestMethod.GET)
    @ResponseBody
    public Response<TopViewCreativeDetailVo> getTopViewCreativeDetail(
            @ApiParam("TopView ID") @PathVariable("top_view_id") Integer topViewId) throws ServiceException {
        TopViewCreativeDetailDto dto = topViewCreativeService.getCreativeDetailById(topViewId);
        return Response.SUCCESS(convertToTopViewCreativeDetailVo(dto));
    }

    private TopViewCreativeDetailVo convertToTopViewCreativeDetailVo(TopViewCreativeDetailDto dto) {
        if (dto == null || dto.getNewHfIosCreative() == null && dto.getNewHfAndroidCreative() == null) {
            return null;
        }

        GdTopViewDto topViewDto = dto.getTopViewDto();
        SsaSplashScreenDetailDto ssaCreativeDetail = dto.getSsaCreativeDetail();
        GdCreativeDto newHfIosCreativeDto = dto.getNewHfIosCreative();
        GdCreativeDto newHfAndroidCreativeDto = dto.getNewHfAndroidCreative();

        List<Long> hfCreativeIdList = Lists.newArrayListWithCapacity(2);
        if (Utils.isPositive(topViewDto.getNewHfIosCreativeId())) {
            hfCreativeIdList.add(topViewDto.getNewHfIosCreativeId());
        }
        if (Utils.isPositive(topViewDto.getNewHfAndroidCreativeId())) {
            hfCreativeIdList.add(topViewDto.getNewHfAndroidCreativeId());
        }

        Predicate<SplashScreenDynamicButtonBO> buttonPredicate = button -> {
            if (hfCreativeIdList.size() > 1) {
                //双端的话，则取其中一端即可，至于为啥判断了COMIC_IPHONE？历史逻辑。。。。
                return Objects.equals(PlatformType.IPHONE.getCode(), button.getPlatform())
                        || Objects.equals(PlatformType.COMIC_IPHONE.getCode(), button.getPlatform());
            }
            //单端的话，则取当前端即可
            return true;
        };

        GdCreativeDto anyOneNotNullCreative = newHfIosCreativeDto != null ? newHfIosCreativeDto : newHfAndroidCreativeDto;

        GdOrderDto orderDto = gdOrderService.getOrderById(topViewDto.getGdOrderId());
        ScheduleDto scheduleDto = queryScheduleService.getScheduleBaseInfoById(topViewDto.getGdScheduleId());
        GdTopViewScheduleDto topViewScheduleDto = queryScheduleService.getTopViewScheduleInfo(topViewDto.getGdScheduleId());
        TemplateDto newTemplate = queryTemplateService.getTemplateById(anyOneNotNullCreative.getTemplateId());

        SsaUposVideoDto newHfVideo = null;
        if (anyOneNotNullCreative.getCreativeVideoDto() != null
                && anyOneNotNullCreative.getCreativeVideoDto().getBizId() != null) {
            newHfVideo = ssaUposVideoService.getSsaUposVideoByBizId(anyOneNotNullCreative.getCreativeVideoDto().getBizId(),
                    Lists.newArrayList(UposProfileEnum.MGK_AV.getCode()));
        }

        TopViewRotation topViewRotation = topViewResourceService.getTopViewRotationByTime(
                Utils.getBeginOfDay(scheduleDto.getBeginDate()), topViewScheduleDto.getSellingType(),
                SalesType.TOP_VIEW_CPT.getCode());

        List<SplashScreenJumpVo> jumpVos = new ArrayList<>();
        List<SplashScreenCustomizedVo> customizedVos = new ArrayList<>();
        List<SplashScreenDynamicButtonVo> buttonVos = new ArrayList<>();
        if (PlatformSwitchEnum.OPEN.getCode().equals(ssaCreativeDetail.getPlatformSwitch())) {
            List<SplashScreenJumpDTO> jumpDTOS = ssaCreativeDetail.getSplashScreenJumpDTOS();
            Map<Integer, List<SplashScreenJumpDTO>> seq2jumpDTOS = new HashMap<>();
            if (!CollectionUtils.isEmpty(jumpDTOS)) {
                seq2jumpDTOS = ssaCreativeDetail.getSplashScreenJumpDTOS().stream()
                        .collect(Collectors.groupingBy(SplashScreenJumpDTO::getSeq));
                jumpVos = jumpDTOS.stream()
                        .map(t -> {
                            SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
                            BeanUtils.copyProperties(t, jumpVo);
                            jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                            jumpVo.setInteractLinkTypeDesc(StringUtils.isEmpty(t.getInteractLink()) ? ""
                                    : GdJumpType.getByCode(t.getInteractLinkType()).getDesc());
                            return jumpVo;
                        }).collect(Collectors.toList());
            }


            if (!CollectionUtils.isEmpty(ssaCreativeDetail.getSsaCustomizedDTOS())) {
                customizedVos = ssaCreativeDetail.getSsaCustomizedDTOS().stream()
                        .map(t -> SplashScreenCustomizedVo.builder()
                                .platform_id(t.getPlatformId())
                                .customized_click_url_list(t.getSsaCustomizedClickUrlList())
                                .customized_imp_url_list(t.getSsaCustomizedImpUrlList())
                                .customized_open_middle_page_click_url_list(t.getCustomizedOpenMiddlePageClickUrlList())
                                .customized_middle_page_click_url_list(t.getCustomizedMiddlePageClickUrlList())
                                .build())
                        .collect(Collectors.toList());
            }

            List<SplashScreenDynamicButtonBO> buttonBOS = ssaCreativeDetail.getButtonBOs();
            if (!CollectionUtils.isEmpty(buttonBOS)) {
                Map<Integer, List<SplashScreenJumpDTO>> finalSeq2jumpDTOS = seq2jumpDTOS;
                buttonVos = buttonBOS.stream()
                        .filter(buttonPredicate)
                        .map(o -> {
                            SplashScreenDynamicButtonVo buttonVo = new SplashScreenDynamicButtonVo();
                            BeanUtils.copyProperties(o, buttonVo);
                            List<SplashScreenJumpDTO> jumpDTOS1 = finalSeq2jumpDTOS.get(o.getSeq());
                            if (!CollectionUtils.isEmpty(jumpDTOS1)) {
                                buttonVo.setSplashScreenJumpVos(jumpDTOS1.stream()
                                        .map(t -> {
                                            SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
                                            BeanUtils.copyProperties(t, jumpVo);
                                            jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                                            jumpVo.setInteractLinkTypeDesc(StringUtils.isEmpty(t.getInteractLink()) ? ""
                                                    : GdJumpType.getByCode(t.getInteractLinkType()).getDesc());
                                            return jumpVo;
                                        }).collect(Collectors.toList()));
                            }
                            return buttonVo;
                        }).collect(Collectors.toList());
            }
        }

        String topViewHfOutBoxVideoUrl = anyOneNotNullCreative.getTopViewHfOutBoxVideoUrl();
        Integer topViewHfOutBoxVideoId = anyOneNotNullCreative.getTopViewHfOutBoxVideoId();

        boolean isTopViewArchive = Objects.equals(topViewScheduleDto.getHfAdType(), BannerShowType.ARCHIVE.getCode());

        List<SsaSplashScreenVersionControlDto> controlDtos = ssaCreativeDetail.getSsaSplashScreenVersionControlDtos();
        SalesType salesType = SalesType.getByCode(scheduleDto.getSalesType());
        TopViewCreativeDetailVo vo = TopViewCreativeDetailVo.builder()
                .top_view_id(topViewDto.getId())
                .creative_name(topViewDto.getCreativeName())
                .order_id(topViewDto.getGdOrderId())
                .order_name(orderDto.getOrderName())
                .schedule_id(topViewDto.getGdScheduleId())
                .schedule_name(scheduleDto.getName())
                .status(topViewDto.getStatus())
                .status_desc(TopViewCreativeStatus.getByCodeWithValidation(topViewDto.getStatus()).getDesc())
                .reason(ssaCreativeDetail.getReason())
                .cm_mark(ssaCreativeDetail.getCmMark())
                .cm_mark_name(SsaCmMark.getByCode(ssaCreativeDetail.getCmMark()).getDesc())
                .schedule_dates(ssaCreativeDetail.getSsaScheduleDtos().stream().map(ssaScheduleDto -> {
                            String scheduleDateDesc = "";
                            if (Objects.equals(salesType, SalesType.TOP_VIEW_GD_PLUS)) {
                                scheduleDateDesc = String.format("%s ~ %s %d%s",
                                        TimeUtil.timestampToIsoTimeStr(ssaScheduleDto.getSsaStartTime()),
                                        TimeUtil.timestampToIsoTimeStr(ssaScheduleDto.getSsaEndTime()),
                                        ssaScheduleDto.getImpression(), "cpm");
                            } else {
                                scheduleDateDesc = String.format("%s %d%s",
                                        TimeUtil.timestampToIsoDateStr(ssaScheduleDto.getSsaStartTime()),
                                        ssaScheduleDto.getRotationNum(), "轮");
                            }
                            return TopViewCreativeDetailVo.ScheduledRotationVo.builder()
                                    .id(ssaScheduleDto.getSsaScheduleId())
                                    .begin_time(TimeUtil.timestampToIsoTimeStr(ssaScheduleDto.getSsaStartTime()))
                                    .end_time(TimeUtil.timestampToIsoTimeStr(ssaScheduleDto.getSsaEndTime()))
                                    .rotation_num(ssaScheduleDto.getRotationNum() / topViewRotation.getSsaRotation())
                                    .impression(ssaScheduleDto.getImpression())
                                    .schedule_date_desc(scheduleDateDesc)
                                    .build();
                        })
                        .collect(Collectors.toList()))
                .creator_name(anyOneNotNullCreative.getCreatorName())
                .jump_type(CollectionUtils.isEmpty(dto.getSsaCreativeDetail().getSplashScreenJumpDTOS()) ? 0 : dto.getSsaCreativeDetail().getSplashScreenJumpDTOS().get(0).getJumpType())
                .splash_screen_jump_vos(jumpVos)
                .splash_screen_customized_vos(customizedVos)
                .share_state(ssaCreativeDetail.getShareState())
                .share_title(ssaCreativeDetail.getShareTitle())
                .share_sub_title(ssaCreativeDetail.getShareSubTitle())
                .share_image_url(ssaCreativeDetail.getShareImageUrl())
                .share_image_hash(ssaCreativeDetail.getShareImageHash())
                .sales_type(ssaCreativeDetail.getSalesType())
                .splash_screen_id(ssaCreativeDetail.getId())
                .ssa_title(ssaCreativeDetail.getTitle())
                .ssa_video(ssaSplashScreenVideoDto2Vo(ssaCreativeDetail.getSsaSplashScreenVideoDto()))
                .egg_video(ssaSplashScreenVideoDto2EggVo(ssaCreativeDetail.getEggVideoDto()))
                .ssa_base_image_list(ssaSplashScreenBaseImageDtos2Vos(ssaCreativeDetail.getBaseImageDtos()))
                .ssa_is_skip(ssaCreativeDetail.getIsSkip())
                .ssa_issued_time(ssaCreativeDetail.getIssuedTime())
                .ssa_encryption(ssaCreativeDetail.getEncryption())
                .ssa_copy_writing(ssaCreativeDetail.getCopywriting())
                .new_hf_creative_ids(hfCreativeIdList)
                .new_hf_template_id(anyOneNotNullCreative.getTemplateId())
                .new_hf_template(templateDto2Vo(newTemplate))
                .new_hf_title(anyOneNotNullCreative.getTitle())
                .new_hf_images(isTopViewArchive ? new LinkedList<>() : convertImageDtos2Vos(anyOneNotNullCreative.getGdCreativeImageDtos(),
                        anyOneNotNullCreative.getTemplateId(), CreativeImageType.DUAL_ROW.getCode()))
                .new_hf_video(ssaUposVideoDto2Vo(newHfVideo))
                .is_customized_new_hf_brand_info(StringUtils.hasText(anyOneNotNullCreative.getBrandName()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                .new_hf_brand_name(anyOneNotNullCreative.getBrandName())
                .new_hf_face_url(anyOneNotNullCreative.getFaceUrl())
                .new_hf_face_md5(anyOneNotNullCreative.getFaceMd5())
                .hf_android_customized_click_url(Optional.ofNullable(newHfAndroidCreativeDto).map(GdCreativeDto::getCustomizedClickUrl).orElse(""))
                .hf_android_customized_imp_url(Optional.ofNullable(newHfAndroidCreativeDto).map(GdCreativeDto::getCustomizedImpUrl).orElse(""))
                .hf_ios_customized_click_url(Optional.ofNullable(newHfIosCreativeDto).map(GdCreativeDto::getCustomizedClickUrl).orElse(""))
                .hf_ios_customized_imp_url(Optional.ofNullable(newHfIosCreativeDto).map(GdCreativeDto::getCustomizedImpUrl).orElse(""))
                .hf_customized_click_url(PlatformSwitchEnum.CLOSE.getCode()
                        .equals(ssaCreativeDetail.getPlatformSwitch()) ?
                        anyOneNotNullCreative.getCustomizedClickUrl() : "")
                .hf_customized_imp_url(PlatformSwitchEnum.CLOSE.getCode()
                        .equals(ssaCreativeDetail.getPlatformSwitch()) ?
                        anyOneNotNullCreative.getCustomizedImpUrl() : "")
                .platform_switch(ssaCreativeDetail.getPlatformSwitch())
                .time_target(ssaCreativeDetail.getTimeTarget())
                .extra_guide_instructions(ssaCreativeDetail.getExtraGuideInstructions())
                .extra_scheme_copywriting(ssaCreativeDetail.getExtraSchemeCopywriting())
                .is_support_button_to_interact(ssaCreativeDetail.getIsSupportButtonToInteract())
                .support_custom_guide(ssaCreativeDetail.getSupportCustomGuide())
                .dynamic_button_vos(buttonVos)
                .text_color_style(ssaCreativeDetail.getTextColorStyle())
                .hf_ip_video_id(topViewHfOutBoxVideoId)
                .hf_ip_video_url(topViewHfOutBoxVideoUrl)
                .new_hf_jump_type(anyOneNotNullCreative.getJumpType())
                .manuscript_info(GdCreativeConverter.MAPPER.toManuscriptInfoVo(anyOneNotNullCreative.getManuscriptInfo()))
                .product_label(GdCreativeConverter.MAPPER.toProductLabelVo(ssaCreativeDetail.getProductLabel()))
                .mini_program(GdCreativeConverter.MAPPER.toMiniProgramVo(ssaCreativeDetail.getMiniProgram()))
                .app_package_ids(Optional.ofNullable(ssaCreativeDetail.getAppPackages())
                        .map(apps -> apps.stream().map(AppPackageDto::getId).collect(Collectors.toList()))
                        .orElse(Collections.emptyList()))
                .interact_instructions(ssaCreativeDetail.getInteractInstructions())
                .middle_page(SsaSplashScreenConverter.MAPPER.toSplashScreenMiddlePageVo(ssaCreativeDetail.getMiddlePage()))
                .hf_android_customized_click_url_list(Optional.ofNullable(newHfAndroidCreativeDto).map(GdCreativeDto::getCustomizedClickUrlList).orElse(Collections.emptyList()))
                .hf_ios_customized_click_url_list(Optional.ofNullable(newHfIosCreativeDto).map(GdCreativeDto::getCustomizedClickUrlList).orElse(Collections.emptyList()))
                .hf_customized_click_url_list(PlatformSwitchEnum.CLOSE.getCode()
                        .equals(ssaCreativeDetail.getPlatformSwitch()) ?
                        anyOneNotNullCreative.getCustomizedClickUrlList() : Collections.emptyList())
                .is_enable_live_booking(Utils.isPositive(ssaCreativeDetail.getLiveBookingId()))
                .live_booking_id(ssaCreativeDetail.getLiveBookingId())
                .build();
        if (!CollectionUtils.isEmpty(controlDtos)) {
            vo.setVersion_control(SsaSplashScreenConvert.ssaSplashScreenVersionControlDtos2Vos(controlDtos));
        }
        vo.setUse_default_version(ssaCreativeDetail.getUseDefaultVersion());


        PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(scheduleDto.getPromotionPurposeType());
        //直播场景
        //bug fix: 如果安卓和ios的首焦推广目的一致，则给出统一的推广目的内容，因为此时的场景可能是来自于直播兜底，详情可参考
        //com.bilibili.adp.brand.portal.convert.creative.TopViewCreativeConvert.convertCptJumpDto方法
        //如果是不一致则忽略，可能是正常的分端场景
        if (Objects.equals(promotionPurposeType, PromotionPurposeType.LIVE)) {
            if (Objects.nonNull(newHfIosCreativeDto) && Objects.nonNull(newHfAndroidCreativeDto)) {
                if (Objects.equals(newHfIosCreativeDto.getPromotionPurposeContent(),
                        newHfAndroidCreativeDto.getPromotionPurposeContent())) {
                    vo.setNew_hf_promotion_purpose_content(this.getVoPromotionPurposeContent(newHfIosCreativeDto.getJumpType(),
                            newHfIosCreativeDto.getPromotionPurposeContent()));
                }
            } else {
                vo.setNew_hf_promotion_purpose_content(this.getVoPromotionPurposeContent(anyOneNotNullCreative.getJumpType(),
                        anyOneNotNullCreative.getPromotionPurposeContent()));
            }
        }

        if (topViewHfOutBoxVideoId != null) {
            TemplateVo template = vo.getNew_hf_template();
            if (template != null) {
                template.setExtra_video_height(IpVideoSizeEnum.TOP_VIEW_3D_OUT_BOX_VIDEO.getHeight());
                template.setExtra_video_width(IpVideoSizeEnum.TOP_VIEW_3D_OUT_BOX_VIDEO.getWidth());
            }
        }

        if (PlatformSwitchEnum.CLOSE.getCode().equals(dto.getSsaCreativeDetail().getPlatformSwitch())) {
            List<SplashScreenJumpDTO> iPhoneJumpDTOS = ssaCreativeDetail.getSplashScreenJumpDTOS().stream()
                    //99是选择式按钮中的纯文字无交互按钮的跳转信息,是后端mock的,前段不需要展示
                    .filter(o -> o.getSeq() != 99)
                    .filter(t -> Lists.newArrayList(PlatformType.IPHONE.getCode(), PlatformType.COMIC_IPHONE.getCode())
                            .contains(t.getPlatformId())).collect(Collectors.toList());
            Map<Integer, String> iPhonePackage = iPhoneJumpDTOS.stream()
                    .collect(Collectors.toMap(SplashScreenJumpDTO::getButtonId, SplashScreenJumpDTO::getPackageName));

            List<SplashScreenJumpDTO> androidJumpDTOS = ssaCreativeDetail.getSplashScreenJumpDTOS().stream()
                    //99是选择式按钮中的纯文字无交互按钮的跳转信息,是后端mock的,前段不需要展示
                    .filter(o -> o.getSeq() != 99)
                    .filter(t -> Lists.newArrayList(PlatformType.COMIC_ANDROID.getCode(), PlatformType.ANDROID.getCode())
                            .contains(t.getPlatformId()))
                    .collect(Collectors.toList());
            Map<Integer, String> androidPackage = androidJumpDTOS.stream()
                    .collect(Collectors.toMap(SplashScreenJumpDTO::getSeq, SplashScreenJumpDTO::getPackageName));

            List<SplashScreenCustomizedDTO> customizedDTOS = ssaCreativeDetail.getSsaCustomizedDTOS();
            SplashScreenCustomizedDTO customizedDTO = CollectionUtils.isEmpty(customizedDTOS) ?
                    new SplashScreenCustomizedDTO() : customizedDTOS.get(0);

            List<SplashScreenDynamicButtonBO> buttonBOS = ssaCreativeDetail.getButtonBOs();
            if (!CollectionUtils.isEmpty(buttonBOS)) {
                buttonVos = buttonBOS.stream()
                        .filter(buttonPredicate)
                        .map(o -> {
                            SplashScreenDynamicButtonVo buttonVo = new SplashScreenDynamicButtonVo();
                            BeanUtils.copyProperties(o, buttonVo);
                            List<SplashScreenJumpDTO> buttonJumpDTOS = o.getSplashScreenJumpDTOS();
                            if (!CollectionUtils.isEmpty(buttonJumpDTOS)) {
                                buttonVo.setJumpVos(buttonJumpDTOS.stream()
                                        .map(t -> {
                                            SplashScreenJumpVo jump = new SplashScreenJumpVo();
                                            BeanUtils.copyProperties(t, jump);
                                            jump.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                                            jump.setInteractLinkTypeDesc(StringUtils.isEmpty(t.getInteractLink()) ? ""
                                                    : GdJumpType.getByCode(t.getInteractLinkType()).getDesc());
                                            return jump;
                                        }).collect(Collectors.toList()));
                            }
                            buttonVo.setIosPackageName(iPhonePackage.get(o.getId()));
                            buttonVo.setAndroidPackageName(androidPackage.get(o.getSeq()));
                            return buttonVo;
                        }).collect(Collectors.toList());

                vo.setDynamic_button_vos(buttonVos);
            }

            SplashScreenJumpDTO iosJumpDTO = CollectionUtils.isEmpty(iPhoneJumpDTOS) ? null : iPhoneJumpDTOS.get(0);
            SplashScreenJumpDTO androidJumpDTO = CollectionUtils.isEmpty(androidJumpDTOS) ? null : androidJumpDTOS.get(0);

            Assert.isTrue(iosJumpDTO != null || androidJumpDTO != null, "Android和ios的跳转信息不能同时为空");

            SplashScreenJumpDTO notNullJumpInfo = iosJumpDTO == null ? androidJumpDTO : iosJumpDTO;

            SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
            BeanUtils.copyProperties(notNullJumpInfo, jumpVo);
            jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(notNullJumpInfo.getActualJumpLink()));
            jumpVo.setInteractLinkTypeDesc(StringUtils.isEmpty(notNullJumpInfo.getInteractLink()) ? ""
                    : GdJumpType.getByCode(notNullJumpInfo.getInteractLinkType()).getDesc());
            vo.setJump_vo(jumpVo);

            if (iosJumpDTO != null) {
                vo.setIos_package_name(iosJumpDTO.getPackageName());
            }
            if (androidJumpDTO != null) {
                vo.setAndroid_package_name(androidJumpDTO.getPackageName());
            }
            vo.setCustomized_click_url_list(customizedDTO.getSsaCustomizedClickUrlList());
            vo.setCustomized_imp_url_list(customizedDTO.getSsaCustomizedImpUrlList());
            vo.setCustomized_open_middle_page_click_url_list(customizedDTO.getCustomizedOpenMiddlePageClickUrlList());
            vo.setCustomized_middle_page_click_url_list(customizedDTO.getCustomizedMiddlePageClickUrlList());
        }

        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004523310
        //【品牌】TopView支持闪屏、首焦分开跳转-香奈儿3月商机
        //补充首焦独立跳转信息，以及部分闪屏控制
        boolean isSplitPlatform = Objects.equals(ssaCreativeDetail.getPlatformSwitch(), PlatformSwitchEnum.OPEN.getCode());
        vo.setWake_app_type(parseWakeAppType(scheduleDto.getWakeAppType(), ssaCreativeDetail.getMiniProgram()).getCode());
        vo.setIs_customized_hf_jump(topViewDto.getIsCustomizedHfJump());
        if (Objects.equals(topViewDto.getIsCustomizedHfJump(), 1)) {
            vo.setHf_jump(TopViewHfJumpVo.builder()
                    .jumpType(anyOneNotNullCreative.getJumpType())
                    .jumpList((isSplitPlatform ? Lists.newArrayList(newHfIosCreativeDto, newHfAndroidCreativeDto) : Lists.newArrayList(anyOneNotNullCreative))
                            .stream()
                            .filter(Objects::nonNull)
                            .map(hfCreative -> SplashScreenJumpVo.builder()
                                    .jumpType(hfCreative.getJumpType())
                                    .jumpLink(gdCreativeConvert.getVoPromotionPoseContent(scheduleDto, hfCreative))
                                    .schemeUrl(hfCreative.getSchemeUrl())
                                    .isCallApp(StringUtils.hasText(hfCreative.getSchemeUrl()) ? 2 : 1)
                                    //如果是不分端，则platform_id设置为null
                                    .platformId(isSplitPlatform ? hfCreative.getPlatformId() : null)
                                    //.packageName("")//首焦唤起不需要package_name
                                    .build())
                            .collect(Collectors.toList()))
                    .appPackageIds(Lists.newArrayList(newHfIosCreativeDto, newHfAndroidCreativeDto).stream()
                            .filter(Objects::nonNull)
                            .map(GdCreativeDto::getAppPackageId)
                            .filter(Utils::isPositive)
                            .distinct()
                            .collect(Collectors.toList()))
                    .miniProgram(GdCreativeConverter.MAPPER.toMiniProgramVo(anyOneNotNullCreative.getMiniProgram()))
                    .wakeAppType(parseWakeAppType(scheduleDto.getWakeAppType(), anyOneNotNullCreative.getMiniProgram()).getCode())
                    .build());
        }
        return vo;
    }

    private WakeAppType parseWakeAppType(Integer scheduleWakeAppType, MiniProgramDto miniProgram) {
        WakeAppType wakeAppType = WakeAppType.getByCode(scheduleWakeAppType);
        if (!Objects.equals(wakeAppType, WakeAppType.APP_OR_MINI_PROGRAM)) {
            //如果不是APP_OR_MINI_PROGRAM，则就是排期上限定的唤起类型
            return wakeAppType;
        }

        if (Objects.nonNull(miniProgram) && miniProgram.isValid()) {
            //如果小程序有效，则说明选择是小程序，
            //1、因为小程序和应用互斥
            //2、因为如果选项卡确实选择是小程序，则小程序一定是必填的，而如果他选项卡选择的是应用，则应用也可能不填
            return WakeAppType.MINI_PROGRAM;
        }

        return WakeAppType.APP;
    }


    @Deprecated
    @ResponseBody
    @ApiOperation(value = "添加创意预览")
    @RequestMapping(value = "/topview/{topview_id}/add_preview", method = RequestMethod.POST)
    public Response<Object> addCreativePreview(
            @ApiIgnore Context context,
            @PathVariable("topview_id") String topviewId) {
        topViewCreativePreviewHandler.openPreview(getOperator(context), Long.valueOf(topviewId),CreativePreviewContext.builder().build());
        return Response.SUCCESS(null);
    }

    @Deprecated
    @ResponseBody
    @ApiOperation(value = "取消创意预览")
    @RequestMapping(value = "/topview/{topview_id}/cancel_preview", method = RequestMethod.DELETE)
    public Response<Object> cancelCreativePreview(
            @ApiIgnore Context context,
            @PathVariable("topview_id") String topviewId) {
        topViewCreativePreviewHandler.cancelPreview(getOperator(context), Long.valueOf(topviewId));
        return Response.SUCCESS(null);
    }

    private List<ImageVo> convertImageDtos2Vos(List<GdCreativeImageDto> imageDtos, Integer templateId, Integer type) {
        if (CollectionUtils.isEmpty(imageDtos)) {
            return Collections.emptyList();
        }

        return imageDtos.stream()
                .filter(dto -> dto.getType().equals(type))
                .map(dto -> ImageVo.builder()
                        .image_url(dto.getImageUrl())
                        .image_hash(this.buildHash(templateId, dto.getImageUrl(), dto.getImageMd5()))
                        .jump_type(dto.getJumpType())
                        .jump_url(dto.getJumpUrl())
                        .build()).collect(Collectors.toList());
    }

    private String buildHash(Integer templateId, String url, String md5) {
        if (Strings.isNullOrEmpty(url)) {
            return "";
        }
        ImageHash imageHash = ImageHash.builder()
                .url(url)
                .md5(md5)
                .templateId(templateId)
                .type(MaterialType.IMAGE)
                .build();
        return Base64.encodeBase64String(JSON.toJSONString(imageHash).getBytes());
    }

    private List<SplashScreenBaseImageVo> ssaSplashScreenBaseImageDtos2Vos(List<SsaSplashScreenBaseImageDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        return dtos.stream().map(this::ssaSplashScreenBaseImageDto2Vo).collect(Collectors.toList());
    }

    private SplashScreenBaseImageVo ssaSplashScreenBaseImageDto2Vo(SsaSplashScreenBaseImageDto dto) {
        return SplashScreenBaseImageVo.builder().hash(dto.getHash()).url(dto.getUrl()).type(dto.getType()).build();
    }

    private TemplateVo templateDto2Vo(TemplateDto template) {
        if (template == null) {
            return null;
        }
        return TemplateVo.builder()
                .template_id(template.getTemplateId())
                .template_name(template.getTemplateName())
                ._fill_title(template.getIsFillTitle())
                .title_max_length(template.getTitleMaxLength())
                .title_min_length(template.getTitleMinLength())
                ._fill_desc(template.getIsFillDesc())
                .desc_max_length(template.getDescMaxLength())
                .desc_min_length(template.getDescMinLength())
                ._support_image(template.getIsSupportImage())
                .image_width(template.getImageWidth())
                .image_height(template.getImageHeight())
                .image_kb_limit(template.getImageKbLimit())
                ._support_ext_image(template.getIsSupportExtImage())
                .ext_image_width(template.getExtImageWidth())
                .ext_image_height(template.getExtImageHeight())
                .ext_image_kb_limit(template.getExtImageKbLimit())
                ._support_video(template.getIsSupportVideo())
                .video_width(template.getVideoWidth())
                .video_height(template.getVideoHeight())
                .video_kb_limit(template.getVideoKbLimit())
                .video_duration_max(template.getVideoDurationMax())
                .video_duration_min(template.getVideoDurationMin())
                ._fill_ext_desc(template.getIsFillExtDesc())
                .ext_desc_min_length(template.getExtDescMinLength())
                .ext_desc_max_length(template.getExtDescMaxLength())
                ._support_video_id(template.getIsSupportVideoId())
                .html(template.getHtml())
                ._support_button(template.getIsSupportButton())
                .button_copy(this.getButtonCopyVoByDto(template.getButtonCopyDtos()))
                .card_type(template.getCardType())
                .build();
    }

    private List<ButtonCopyVo> getButtonCopyVoByDto(List<ButtonCopyDto> dtoList) {

        List<ButtonCopyVo> voList = Lists.newArrayList();
        dtoList.forEach(dto -> {
            if (ButtonCopyTypeEnum.JUMP_LINK.getCode() == dto.getType()) {
                ButtonCopyVo buttonCopy = ButtonCopyVo.builder().build();
                BeanUtils.copyProperties(dto, buttonCopy);

                voList.add(buttonCopy);
            }
        });

        return voList;
    }

    @ApiOperation(value = "暂停/开启TopView创意")
    @RequestMapping(value = "/{top_view_id}/switch", method = RequestMethod.PUT)
    @ResponseBody
    public Response<Boolean> pauseOrStartUpTopView(
            @ApiIgnore Context context,
            @ApiParam("TopView ID") @PathVariable("top_view_id") Integer topViewId,
            @ApiParam("暂停/开启 0-暂停 1-开启") @RequestParam(value = "switch") Integer switchStatus) {
        if (switchStatus == 0) {
            topViewCreativeService.pause(topViewId, super.getOperator(context));
        } else {
            topViewCreativeService.startUp(topViewId, super.getOperator(context));
        }
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "TopView日志查询")
    @RequestMapping(value = "/log", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<SsaLogOperationVo>>> queryLog(
            @ApiParam("id") @RequestParam(value = "obj_id") Integer id,
            @ApiParam("页数") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("页长") @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer size)
            throws ServiceException {
        PageResult<CptLogOperationDto> pageResult = gdLogService.getLogsByObjId(String.valueOf(id),
                GdLogFlag.TOP_VIEW_CREATIVE, null, page, size);
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(),
                convertToSsaLogOperationVoList(pageResult.getRecords())));
    }

    private List<SsaLogOperationVo> convertToSsaLogOperationVoList(List<CptLogOperationDto> operationDtoList) {
        if (CollectionUtils.isEmpty(operationDtoList)) {
            return Collections.emptyList();
        }
        return operationDtoList.stream().map(operationDto -> SsaLogOperationVo.builder()
                        .id(operationDto.getId())
                        .obj_id(Integer.valueOf(operationDto.getObjId()))
                        .obj_flag(operationDto.getObjFlag())
                        .operate_type(operationDto.getOperateType())
                        .operator_username(operationDto.getOperatorUsername())
                        .ctime(operationDto.getCtime())
                        .value(operationDto.getValue())
                        .build())
                .collect(Collectors.toList());
    }


    @ApiOperation(value = "新建运营闪屏查询分享信息")
    @RequestMapping(value = "/add/get/share", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<ShareInfoVo> addGetShareInfo(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "splash screen data")
            @RequestBody NewExternalTopViewVo vo) throws ServiceException,
            SystemException, IOException {

        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(vo.getScheduleId());

        if (PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            if (vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                //全屏跳转(现在只有互动闪屏)跳转信息放在外层对象
                if (!CollectionUtils.isEmpty(vo.getSplashScreenJumpVos())) {
                    SplashScreenJumpVo jumpVo = vo.getSplashScreenJumpVos().get(0);
                    if (ssaSplashScreenConvert.isSupportShare(vo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                    }
                }

            } else {
                Assert.notEmpty(vo.getDynamicButtonVos(), "请先上传按钮跳转信息");
                List<SplashScreenJumpVo> jumpVos = vo.getDynamicButtonVos().get(0).getJumpVos();
                Assert.notEmpty(jumpVos, "请先上传按钮跳转信息");
                if (!CollectionUtils.isEmpty(jumpVos)) {
                    SplashScreenJumpVo jumpVo = jumpVos.get(0);
                    if (ssaSplashScreenConvert.isSupportShare(vo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(),
                                vo.getSsaVideo()));
                    }
                }
            }
        } else {
            if (vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                Assert.notEmpty(vo.getJumpVos(), "请先上传跳转信息");
                SplashScreenJumpVo jumpVo = vo.getJumpVos().get(0);
                if (ssaSplashScreenConvert.isSupportShare(vo.getJumpType(), jumpVo.getJumpLink())) {
                    return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                            scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                }
            } else {
                Assert.notEmpty(vo.getDynamicButtonVos(), "请先上传按钮跳转信息");
                List<SplashScreenJumpVo> jumpVos = vo.getDynamicButtonVos().get(0).getJumpVos();
                Assert.notEmpty(jumpVos, "请先上传按钮跳转信息");
                if (!CollectionUtils.isEmpty(jumpVos)) {
                    SplashScreenJumpVo jumpVo = jumpVos.get(0);
                    if (ssaSplashScreenConvert.isSupportShare(vo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(),
                                vo.getSsaVideo()));
                    }
                }
            }
        }
        return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
    }

    @ApiOperation(value = "编辑运营闪屏")
    @RequestMapping(value = "/update/get/share", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<ShareInfoVo> updateGetShare(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "splash screen data")
            @RequestBody UpdateExternalTopViewVo vo) throws ServiceException, IOException, SystemException {
        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(vo.getScheduleId());
        if (PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            if (vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                //全屏跳转(现在只有互动闪屏)跳转信息放在外层对象
                if (!CollectionUtils.isEmpty(vo.getSplashScreenJumpVos())) {
                    SplashScreenJumpVo jumpVo = vo.getSplashScreenJumpVos().get(0);
                    if (ssaSplashScreenConvert.isSupportShare(jumpVo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                    }
                }

            } else {
                Assert.notEmpty(vo.getDynamicButtonVos(), "请先上传按钮跳转信息");
                List<SplashScreenJumpVo> jumpVos = vo.getDynamicButtonVos().get(0).getJumpVos();
                Assert.notEmpty(jumpVos, "请先上传按钮跳转信息");
                if (!CollectionUtils.isEmpty(jumpVos)) {
                    SplashScreenJumpVo jumpVo = jumpVos.get(0);
                    if (ssaSplashScreenConvert.isSupportShare(jumpVo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                    }
                }
            }
        } else {
            if (vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                Assert.notEmpty(vo.getJumpVos(), "请先上传跳转信息");
                SplashScreenJumpVo jumpVo = vo.getJumpVos().get(0);
                if (ssaSplashScreenConvert.isSupportShare(jumpVo.getJumpType(), jumpVo.getJumpLink())) {
                    return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                            scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                }
            } else {
                Assert.notEmpty(vo.getDynamicButtonVos(), "请先上传按钮跳转信息");
                List<SplashScreenJumpVo> jumpVos = vo.getDynamicButtonVos().get(0).getJumpVos();
                Assert.notEmpty(jumpVos, "请先上传按钮跳转信息");
                if (!CollectionUtils.isEmpty(jumpVos)) {
                    SplashScreenJumpVo jumpVo = jumpVos.get(0);
                    if (ssaSplashScreenConvert.isSupportShare(jumpVo.getJumpType(), jumpVo.getJumpLink())) {
                        return Response.SUCCESS(ssaSplashScreenConvert.getShareInfo(vo.getNewHfTitle(),
                                scheduleDto.getShowStyle(), vo.getSsaBaseImageList(), vo.getSsaVideo()));
                    }
                }
            }
        }
        return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
    }

    private String getVoPromotionPurposeContent(Integer gdJumpType, String ppc) {
        if (!Utils.isPositive(gdJumpType)) {
            return ppc;
        }

        GdJumpType jumpType = GdJumpType.getByCode(gdJumpType);

        if (Objects.equals(jumpType, GdJumpType.VIDEO_MOBILE)) {
            return CommonBvidUtils.urlBvToAv(ppc);
        }

        if (Objects.equals(jumpType, GdJumpType.LIVE)) {
            return GdJumpType.LIVE.parseLaunchUrl(ppc).replace(GdJumpType.LIVE.getPreffix(), "");
        }

        return ppc;
    }

    @ApiOperation(value = "批量新建运营TopView")
    @RequestMapping(value = "/multi_external", method = RequestMethod.POST)
    @ResponseBody
    public Response<Integer> createMultiExternalTopView(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "TopView信息") @RequestBody NewExternalTopViewVo vo) {
        List<Integer> scheduleIdList = vo.getScheduleIdList();
        vo.setScheduleIdList(null);
        JSON json = (JSON) JSONObject.toJSON(vo);//BeanUtils是浅copy，因此反序列化实现深copy，防止后续的链路数据被变更
        List<NewExternalTopViewDto> externalTopViewDtos = Lists.newArrayList();
        for (Integer scheduleId : scheduleIdList) {
            NewExternalTopViewVo externalTopViewVo = JSONObject.toJavaObject(json,
                    NewExternalTopViewVo.class);
            externalTopViewVo.setScheduleId(scheduleId);
            NewExternalTopViewDto externalTopViewDto = topViewCreativeConvert
                    .convertToNewExternalTopViewDto(externalTopViewVo);
            if (Objects.nonNull(externalTopViewDto)) {
                externalTopViewDtos.add(externalTopViewDto);
            }
        }
        topViewCreativeService.batchCreateExternal(externalTopViewDtos, getOperator(context));
        return Response.SUCCESS(0);
    }

    @ApiOperation(value = "查询可使用的平台信息")
    @RequestMapping(value = "/available_platform", method = RequestMethod.GET)
    @ResponseBody
    public Response<AvailablePlatformVo> getAvailablePlatform(@RequestParam(value = "schedule_id") Integer scheduleId,
                                                              @RequestParam(value = "guide_material_type", required = false, defaultValue = "0") Integer guideMaterialType,
                                                              @RequestParam(value = "is_customized_new_hf_brand_info", required = false, defaultValue = "0") Integer isCustomizedNewHfBrandInfo,
                                                              @RequestParam(value = "is_enable_live_booking", required = false, defaultValue = "false") Boolean isEnableLiveBooking) {
        //根据设备定向转换平台类型
        List<PlatformType> allAvailablePlatforms = queryScheduleService.getScheduleOsTarget(scheduleId, false);
        allAvailablePlatforms.sort(Comparator.comparing(PlatformType::getCode));
        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(scheduleId);

        Map<Integer, Integer> versions = topViewCreativeService.getDefaultPlatformVersion(scheduleId, guideMaterialType, isCustomizedNewHfBrandInfo, Objects.nonNull(topViewScheduleInfo.getSsaAdType()) ? topViewScheduleInfo.getSsaAdType() : null, isEnableLiveBooking);

        List<AvailablePlatformVo.AvailableJumpVo> availableJump = Lists.newArrayList();
        List<AvailablePlatformVo.AvailableVersionVo> availableVersion = Lists.newArrayList();
        List<AvailablePlatformVo.AvailableMonitorVo> availableMonitor = Lists.newArrayList();

        for (PlatformType platform : allAvailablePlatforms) {
            //排除ipad_hd
            if (Objects.equals(platform, PlatformType.IPAD_HD)) {
                continue;
            }
            //build available jump
            availableJump.add(AvailablePlatformVo.AvailableJumpVo.builder()
                    .platformId(platform.getCode())
                    .platformName(platform.getDesc())
                    .build());

            //build available version
            availableVersion.add(AvailablePlatformVo.AvailableVersionVo.builder()
                    .platformId(platform.getCode())
                    .platformName(platform.getDesc())
                    .version(versions.get(platform.getCode()))
                    .build());

            //build available monitor
            availableMonitor.add(AvailablePlatformVo.AvailableMonitorVo.builder()
                    .platformId(platform.getCode())
                    .platformName(platform.getDesc())
                    .build());
        }
        return Response.SUCCESS(AvailablePlatformVo.builder()
                .availableJump(availableJump)
                .availableVersion(availableVersion)
                .availableMonitor(availableMonitor)
                .build());
    }

    @ApiOperation(value = "查询TopView复制创意下拉列表")
    @RequestMapping(value = "/copy/drop_box", method = RequestMethod.GET)
    @ResponseBody
    Response<List<CreativeBaseVo>> queryCreativeDropBox(@ApiIgnore Context context,
                                                        @ApiParam("排期ID") @RequestParam(value = "schedule_id", required = true) Integer scheduleId,
                                                        @ApiParam("创意名称") @RequestParam(value = "creative_name_like", required = false) String creativeNameLike,
                                                        @ApiParam("页码") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                        @ApiParam("页大小") @RequestParam(value = "page_size", required = false, defaultValue = "200") Integer pageSize) {
        // 查询当前排期信息
        ScheduleDto scheduleInfo = queryScheduleService.getScheduleBaseInfoById(scheduleId);
        GdTopViewScheduleDto topViewScheduleInfo = queryScheduleService.getTopViewScheduleInfo(scheduleId);
        Assert.isTrue(Objects.equals(scheduleInfo.getAccountId(), context.getAccountId()), "您不能操作不属于您的排期");

        // 查询可复制的排期信息
        QueryScheduleDto queryScheduleDto = QueryScheduleDto.builder()
                .accountId(context.getAccountId())
                .orderProducts(Lists.newArrayList(scheduleInfo.getOrderProduct()))
                .promotionPurposeTypeList(Lists.newArrayList(scheduleInfo.getPromotionPurposeType()))
                .statusList(Lists.newArrayList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                .mTime(TimeUtil.toTimestamp(LocalDateTime.now().minusMonths(3)))
                .build();
        List<ScheduleDto> scheduleDtoList = queryScheduleService.queryBaseSchedule(queryScheduleDto);

        // 填充排期定向信息
        Set<Integer> scheduleIdSet = scheduleDtoList.stream()
                .map(ScheduleDto::getScheduleId)
                .collect(Collectors.toSet());
        scheduleIdSet.add(scheduleInfo.getScheduleId());
        Map<Integer, List<ScheduleTargetDto>> scheduleTargetDtoMap = queryScheduleService.getScheduleTargetDtoInScheduleIds(new ArrayList<>(scheduleIdSet))
                .stream()
                .collect(Collectors.groupingBy(ScheduleTargetDto::getScheduleId));
        scheduleInfo.setTargets(scheduleTargetDtoMap.get(scheduleInfo.getScheduleId()));
        scheduleDtoList.forEach(scheduleDto -> scheduleDto.setTargets(scheduleTargetDtoMap.get(scheduleDto.getScheduleId())));

        // 闪屏排期筛选
        scheduleDtoList = scheduleDtoList
                .stream()
                .filter(item -> ScheduleCompareProperties.SSA_COMPARATOR.compare(item, scheduleInfo) == 0)
                .collect(Collectors.toList());
        List<Integer> scheduleIdList = scheduleDtoList.stream()
                .map(ScheduleDto::getScheduleId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, GdTopViewScheduleDto> topViewScheduleInfoMap = queryScheduleService.getTopViewScheduleInfoMap(scheduleIdList);
        // TopView排期筛选
        scheduleDtoList = scheduleDtoList
                .stream()
                .filter(item -> {
                    // 同为闪屏排期
                    GdTopViewScheduleDto gdTopViewScheduleDto = topViewScheduleInfoMap.get(item.getScheduleId());
                    if (Objects.isNull(topViewScheduleInfo)) {
                        return Objects.isNull(gdTopViewScheduleDto);
                    }
                    // 同为TopView排期
                    if (Objects.isNull(gdTopViewScheduleDto)) {
                        return false;
                    }
                    return ScheduleCompareProperties.TOPVIEW_COMPARATOR.compare(gdTopViewScheduleDto, topViewScheduleInfo) == 0;
                }).collect(Collectors.toList());
        scheduleIdList = scheduleDtoList.stream()
                .map(ScheduleDto::getScheduleId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(scheduleIdList)) {
            return Response.SUCCESS();
        }

        // 查询可复制的创意信息
        QueryTopViewCreativeDto queryTopViewCreativeDto = QueryTopViewCreativeDto.builder()
                .accountId(context.getAccountId())
                .scheduleIds(scheduleIdList)
                .creativeName(creativeNameLike)
                .statusList(TopViewCreativeStatus.NOT_DELETED_STATUS)
                .orderBy("ctime desc")
                .build();
        Map<Integer, List<GdTopViewDto>> topViewInfoMap = topViewCreativeService.getTopViewInfoMapPage(queryTopViewCreativeDto, page, pageSize);
        if (MapUtils.isEmpty(topViewInfoMap)) {
            return Response.SUCCESS();
        }

        // 转换返回结果
        List<CreativeBaseVo> result = topViewInfoMap.values()
                .stream()
                .flatMap(Collection::stream)
                .map(item ->
                        CreativeBaseVo.builder()
                                .id(String.valueOf(item.getId()))
                                .title(item.getCreativeName())
                                .build()
                ).collect(Collectors.toList());
        return Response.SUCCESS(result);
    }
}
