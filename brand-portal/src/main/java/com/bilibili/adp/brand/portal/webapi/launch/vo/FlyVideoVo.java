package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel("起飞稿件信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FlyVideoVo {

    private String id;

    private Long aid;

    private String name;

    private String tag;

    private String desc;

    private Integer status;

    private String status_desc;

    private String cover;

    private String warn;

    private String pubTime;

    private Long pTime;

    private Integer businessType;

    private Integer launchVideoType;

    private Long cid;

    private Long mid;

    private String playUrl;

    private Integer width;

    private Integer height;

    //时长，单位：毫秒
    private Long duration;

    //用户输入的id，可能是bvid也可能是avid
    private String inputId;

}
