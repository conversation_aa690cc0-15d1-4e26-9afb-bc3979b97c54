package com.bilibili.adp.brand.portal.convert.creative;

import com.bilibili.CommonBvidUtils;
import com.bilibili.adp.brand.portal.common.PlatformSwitchEnum;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ShareInfoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.*;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.component.SsaAdditionalComponentsVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenDynamicButtonVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.UpdateSplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SplashScreenScheduleVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version.NewSplashScreenVersionControlVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version.SplashScreenVersionControlVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version.UpdateSplashScreenVersionControlVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaNewSplashScreenVideoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaSplashScreenVideoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaUpdateSplashScreenVideoVo;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.material.IImageService;
import com.bilibili.brand.api.material.IVideoService;
import com.bilibili.brand.api.material.bo.ImageBo;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.platform.report.api.dto.OttStatSplashScreenDto;
import com.bilibili.brand.platform.report.api.dto.StatSplashScreenDto;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.ButtonInteractStyleEnum;
import com.bilibili.cpt.platform.common.CutType;
import com.bilibili.cpt.platform.util.SsaUtils;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.enums.GdJumpType;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import com.bilibili.ssa.platform.common.enums.*;
import com.bilibili.utils.StringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.common.enums.SsaConstants.MAX_VERSION;
import static com.bilibili.ssa.platform.common.enums.SsaConstants.MIN_VERSION;

/**
 * @Description 闪屏模型转换工具类
 * <AUTHOR>
 * @Date 2020.05.26 18:17
 */
@Component
@Slf4j
public class SsaSplashScreenConvert {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private IVideoService videoService;

    @Autowired
    private IImageService imageService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SsaConvertContext{
        private OrderProduct orderProduct;
        private boolean ssaArchive;
        private SsaLinkageType linkageType;
        private List<PlatformType> platformTypes;
        private Integer videoPlayMode;
        private boolean adx;
        private Integer ssaJumpAreaEffect;
        private Integer guideMaterialType;
        private Integer isCustomizedNewHfBrandInfo;
        private Integer ssaAdType;
        private Boolean isEnableLiveBooking;
        private Integer effectiveType;

    }

    /*
     * 新建运营闪屏VO对象转DTO
     */
    public SsaNewExternalSplashScreenDto convertNewExternalSplashScreenVo2Dto(NewExternalSplashScreenVo vo){
        if(vo == null) {
            return null;
        }

        ScheduleDto schedule = queryScheduleService.getScheduleBaseInfoById(vo.getScheduleId());
        SsaConvertContext convertContext = SsaConvertContext.builder()
                .ssaArchive(SsaVideoPlayModeEnum.isArchive(schedule.getSsaVideoPlayMode()))
                .videoPlayMode(schedule.getSsaVideoPlayMode())
                .linkageType(SsaLinkageType.getByCode(schedule.getLinkageType()))
                .orderProduct(OrderProduct.getByCode(schedule.getOrderProduct()))
                .platformTypes(queryScheduleService.getScheduleOsTarget(schedule.getScheduleId(), true))
                .adx(schedule.isAdx())
                .ssaJumpAreaEffect(schedule.getJumpAreaEffect())
                .guideMaterialType(vo.getDynamicButtonVos().get(0).getGuideMaterialType())
                .effectiveType(schedule.getEffectiveType())
                .build();

        Integer jumpType = vo.getJumpType();
        List<SplashScreenCustomizedDTO> customizedDTOS;
        List<SplashScreenDynamicButtonBO> buttonBOS = new ArrayList<>();
        List<SplashScreenJumpDTO> splashScreenJumpDTOS = new ArrayList<>();
        if(PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            if(vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                 ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                //全屏跳转(现在只有互动闪屏)跳转信息放在外层对象
                splashScreenJumpDTOS = convertJumpInfo(vo.getSplashScreenJumpVos(), jumpType);
            }else {
                buttonBOS = convert2ButtonBOs(jumpType, vo.getTextColorStyle(), vo.getDynamicButtonVos());
            }
            customizedDTOS = convert2CustomizedDTO(vo.getSplashScreenCustomizedVos());
        }else {
            if(vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                SplashScreenJumpVo jumpVo = vo.getJumpVos().get(0);
                splashScreenJumpDTOS = convert2JumpDTOWithoutPlatform(jumpType, jumpVo.getIosPackageName(),
                        jumpVo.getAndroidPackageName(), vo.getJumpVos().get(0), convertContext);
            }else {
                buttonBOS = convert2ButtonBOsWithoutPlatform(jumpType, vo.getTextColorStyle(),
                        vo.getDynamicButtonVos(), convertContext);
            }
            customizedDTOS = convert2CustomizedDTOWithoutPlatform(SplashScreenCustomizedVo.builder()
                    .customized_imp_url_list(vo.getCustomizedImpUrlList())
                    .customized_click_url_list(vo.getCustomizedClickUrlList())
                    .build(), convertContext);
        }

        List<SsaNewSplashScreenVersionControlDto> versionControlDtos = mockSsaVersionControl(vo, convertContext);

        SsaNewExternalSplashScreenDto dto = SsaNewExternalSplashScreenDto.builder()
                .isSkip(IsSkipEnum.YES.getCode())
                .baseImageDtos(newSplashScreenBaseImageVos2NewDtos(vo.getBaseImageVos()))
                .ssaNewSplashScreenVersionControlDtos((vo.getUseDefaultVersion() == null || vo.getUseDefaultVersion()) ?
                        versionControlDtos : versionControlVos2DtoS(vo.getVersionControl(), versionControlDtos))
                .ssaNewScheduleSplashScreenMappingDtos(vo.getSchedules() == null ?
                        Collections.emptyList() : vo.getSchedules().stream().map(scheduleSelectVo ->
                        SsaNewScheduleSplashScreenMappingDto.builder()
                                .scheduleId(scheduleSelectVo.getId())
                                .gdScheduleId(vo.getScheduleId())
                                .beginTime(Timestamp.valueOf(scheduleSelectVo.getBegin_time()))
                                .endTime(Timestamp.valueOf(scheduleSelectVo.getEnd_time()))
                                .build()).collect(Collectors.toList()))
                .videoDto(newSplashScreenVideoVo2Dto(vo.getVideo()))
                .splashScreenJumpDTOS(splashScreenJumpDTOS)
                .ssaCustomizedDTOS(customizedDTOS)
                .buttonBOS(buttonBOS)
                .build();
        BeanUtils.copyProperties(vo, dto);

        dto.setAdditionalComponents(convertAdditionComponents(vo.getAdditionalComponents()));

        dto.setSecondPageInfo(SsaSplashScreenConverter.MAPPER.toDto(vo.getSecondPageInfo()));

        dto.setSsaArchiveMaterial(SsaSplashScreenConverter.MAPPER.toDto(vo.getSsaArchiveMaterial()));

        dto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProductLabel()));

        dto.setMiniProgram(GdCreativeConverter.MAPPER.toMiniProgramDto(vo.getMiniProgram()));

        dto.setInteractEgg(SsaSplashScreenConverter.MAPPER.toSecondPageInteractEggDto(vo.getInteractEgg()));

        return dto;
    }

    private SsaAdditionalComponentsDto convertAdditionComponents(SsaAdditionalComponentsVo additionalComponents) {
        SsaAdditionalComponentsDto componentsDto = SsaSplashScreenConverter.MAPPER.toDto(additionalComponents);
        List<Integer> componentTypes = componentsDto.getComponentTypes();
        if (CollectionUtils.isEmpty(componentTypes) || componentTypes.contains(SsaComponentTypeEnum.UNKNOWN.getCode())) {
            return null;
        }
        SsaAdditionalComponentsDto components = new SsaAdditionalComponentsDto();
        if (componentTypes.contains(SsaComponentTypeEnum.COUNT_DOWN.getCode())
                || componentTypes.contains(SsaComponentTypeEnum.TOP_COUNT_DOWN.getCode())) {
            SsaCountDownComponentDto countDownComponent = componentsDto.getCountDownComponent();
            Assert.notNull(countDownComponent ,"倒计时组件不能为空");
            Assert.notNull(countDownComponent.getActivityStartTime() ,"倒计时组件时间不能为空");
            //COUNT_DOWN和TOP_COUNT_DOWN不会同时存在
            if (componentTypes.contains(SsaComponentTypeEnum.COUNT_DOWN.getCode())) {
                countDownComponent.setComponentType(SsaComponentTypeEnum.COUNT_DOWN.getCode());
            } else {
                countDownComponent.setComponentType(SsaComponentTypeEnum.TOP_COUNT_DOWN.getCode());
            }
            components.setCountDownComponent(countDownComponent);
        }
        return components;
    }

    public List<SplashScreenJumpDTO> convertJumpInfo(List<SplashScreenJumpVo> jumpVos, Integer jumpType) {
        List<SplashScreenJumpDTO> jumpDTOS = Lists.newArrayList();
        for (SplashScreenJumpVo splashScreenJumpVo : jumpVos) {
            SplashScreenJumpDTO splashScreenJumpDTO = convert2JumpDTO(jumpType, splashScreenJumpVo);
            jumpDTOS.add(splashScreenJumpDTO);
            if (PlatformType.IPAD.getCode().equals(splashScreenJumpDTO.getPlatformId())) {
                //是否是ipad的跳转链接，如果有则自动塞一份ipad_hd的
                //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887003036634
                SplashScreenJumpDTO ipadHd = new SplashScreenJumpDTO();
                BeanUtils.copyProperties(splashScreenJumpDTO, ipadHd);
                ipadHd.setPlatformId(PlatformType.IPAD_HD.getCode());
                jumpDTOS.add(ipadHd);
            }
        }
        return jumpDTOS;
    }

    public List<SplashScreenDynamicButtonBO> convert2ButtonBOs(Integer jumpType,
                                                                      Integer textColorStyle,
                                                                       List<SplashScreenDynamicButtonVo> buttonVos){
        if(CollectionUtils.isEmpty(buttonVos)){
            return null;
        }
        return buttonVos.stream().map(t->{
            SplashScreenDynamicButtonBO buttonBO = new SplashScreenDynamicButtonBO();
            BeanUtils.copyProperties(t, buttonBO);
            buttonBO.setSplashScreenJumpDTOS(convertJumpInfo(t.getSplashScreenJumpVos(), jumpType));
            buttonBO.setTextColorStyle(textColorStyle);
            return buttonBO;
        }).collect(Collectors.toList());
    }

    public List<SplashScreenDynamicButtonBO> convert2ButtonBOsWithoutPlatform(Integer jumpType,
                                                                              Integer textColorStyle,
                                                                              List<SplashScreenDynamicButtonVo> buttonVos,
                                                                              SsaConvertContext context){
        if(CollectionUtils.isEmpty(buttonVos)){
            return null;
        }
        return buttonVos.stream().map(t->{
            SplashScreenDynamicButtonBO buttonBO = new SplashScreenDynamicButtonBO();
            BeanUtils.copyProperties(t, buttonBO);
            List<SplashScreenJumpDTO> jumpDTOS = convert2JumpDTOWithoutPlatform(jumpType, t.getIosPackageName(),
                    t.getAndroidPackageName(), t.getJumpVos().get(0), context);
            buttonBO.setSplashScreenJumpDTOS(jumpDTOS);
            buttonBO.setTextColorStyle(textColorStyle);
            return buttonBO;
        }).collect(Collectors.toList());
    }

    public SplashScreenJumpDTO convert2JumpDTO(Integer jumpType, SplashScreenJumpVo screenJumpVo){
        SplashScreenJumpDTO jumpDTO = new SplashScreenJumpDTO();
        BeanUtils.copyProperties(screenJumpVo, jumpDTO);
        jumpDTO.setJumpType(jumpType);
        jumpDTO.setSchemeUrl(screenJumpVo.getSchemeUrl() == null ? null : screenJumpVo.getSchemeUrl().trim());
        jumpDTO.setJumpLink(screenJumpVo.getJumpLink() == null ? null : screenJumpVo.getJumpLink().trim());
        jumpDTO.setInteractLink(screenJumpVo.getInteractLink() == null ? null : screenJumpVo.getInteractLink().trim());
        jumpDTO.setUserCancelJumpLink(Objects.isNull(screenJumpVo.getUserCancelJumpLink()) ? null : screenJumpVo.getUserCancelJumpLink().trim());
        return jumpDTO;
    }

    public List<SplashScreenJumpDTO> convert2JumpDTOWithoutPlatform(Integer jumpType,
                                                                    String iosPackageName,
                                                                    String androidPackageName,
                                                                    SplashScreenJumpVo screenJumpVo,
                                                                    SsaConvertContext context){
        List<SplashScreenJumpDTO> jumpDTOS = new ArrayList<>();
        SplashScreenJumpDTO jumpDTO = convert2JumpDTO(jumpType, screenJumpVo);
        List<PlatformType> platformTypes = context.getPlatformTypes();
        for (PlatformType platformType : platformTypes) {
            if (PlatformType.isIos(platformType.getCode())) {
                SplashScreenJumpDTO jumpDTO1 = new SplashScreenJumpDTO();
                BeanUtils.copyProperties(jumpDTO, jumpDTO1);
                jumpDTO1.setPlatformId(platformType.getCode());
                jumpDTO1.setPackageName(iosPackageName);
                jumpDTOS.add(jumpDTO1);
            }
            if (PlatformType.isAndroid(platformType.getCode())) {
                SplashScreenJumpDTO jumpDTO3 = new SplashScreenJumpDTO();
                BeanUtils.copyProperties(jumpDTO, jumpDTO3);
                jumpDTO3.setPlatformId(platformType.getCode());
                jumpDTO3.setPackageName(androidPackageName);
                jumpDTOS.add(jumpDTO3);
            }
        }
        return jumpDTOS;
    }

    public List<SplashScreenCustomizedDTO> convert2CustomizedDTO(List<SplashScreenCustomizedVo> customized_vos){

        //分端获取监控信息
        List<SplashScreenCustomizedDTO> ssaCustomizedDTOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(customized_vos)) {
            //去掉链接中的空格
            for (SplashScreenCustomizedVo customizedVo : customized_vos) {
                SplashScreenCustomizedDTO customizedDTO = SplashScreenCustomizedDTO.builder()
                        .platformId(customizedVo.getPlatform_id())
                        .ssaCustomizedClickUrlList(StringUtil.trim(customizedVo.getCustomized_click_url_list()))
                        .ssaCustomizedImpUrlList(StringUtil.trim(customizedVo.getCustomized_imp_url_list()))
                        .customizedMiddlePageClickUrlList(StringUtil.trim(customizedVo.getCustomized_middle_page_click_url_list()))
                        .customizedOpenMiddlePageClickUrlList(StringUtil.trim(customizedVo.getCustomized_open_middle_page_click_url_list()))
                        .build();
                ssaCustomizedDTOS.add(customizedDTO);
                if (PlatformType.IPAD.getCode().equals(customizedVo.getPlatform_id())) {
                    //是否是ipad的跳转链接，如果有则自动塞一份ipad_hd的
                    //https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887003036634
                    ssaCustomizedDTOS.add(SplashScreenCustomizedDTO.builder()
                            .platformId(PlatformType.IPAD_HD.getCode())
                            .ssaCustomizedClickUrlList(customizedDTO.getSsaCustomizedClickUrlList())
                            .ssaCustomizedImpUrlList(customizedDTO.getSsaCustomizedImpUrlList())
                            .customizedMiddlePageClickUrlList(customizedDTO.getCustomizedMiddlePageClickUrlList())
                            .customizedOpenMiddlePageClickUrlList(customizedDTO.getCustomizedOpenMiddlePageClickUrlList())
                            .build());
                }
            }
        }
        return ssaCustomizedDTOS;
    }


    public List<SplashScreenCustomizedDTO> convert2CustomizedDTOWithoutPlatform(SplashScreenCustomizedVo customizedVo,
                                                                                SsaConvertContext context){
        return context.getPlatformTypes().stream()
                .map(p -> SplashScreenCustomizedDTO.builder()
                        .ssaCustomizedImpUrlList(StringUtil.trim(customizedVo.getCustomized_imp_url_list()))
                        .ssaCustomizedClickUrlList(StringUtil.trim(customizedVo.getCustomized_click_url_list()))
                        .customizedMiddlePageClickUrlList(StringUtil.trim(customizedVo.getCustomized_middle_page_click_url_list()))
                        .customizedOpenMiddlePageClickUrlList(StringUtil.trim(customizedVo.getCustomized_open_middle_page_click_url_list()))
                        .platformId(p.getCode())
                        .build())
                .collect(Collectors.toList());
    }

    public SsaUpdateSplashScreenDto updateInternalSplashScreenVo2Dto(UpdateInternalSplashScreenVo vo) {
        if(vo == null){
            return null;
        }
        SsaUpdateSplashScreenDto dto = SsaUpdateSplashScreenDto.builder()
                .id(vo.getId())
                .copyWriting(vo.getCopy_writing().trim())
                .isSkip(vo.getIs_skip())
                .showStyle(vo.getShow_style())
                .startTime(vo.getStart_time())
                .endTime(vo.getEnd_time())
                .title(vo.getTitle())
                .cmMark(vo.getCm_mark())
                .ssaBaseImageDtos(updateBaseImageVos2Dtos(vo.getBase_image_vos(), vo.getId()))
                .ssaVersionControlDtos(updateSsaSplashScreenVersionControlVos2Dtos(vo.getVersion_control(), vo.getId()))
                .splashScreenJumpDTOS(convertJumpInfo(vo.getSplash_screen_jump_vos(), vo.getJump_type()))
                .build();

        if (SsaShowStyleType.HORIZONTA_SCREEN_VIDEO.getCode().equals(vo.getShow_style())
                || SsaShowStyleType.VERTICAL_SCREEN_VIDEO.getCode().equals(vo.getShow_style())
                || SsaShowStyleType.FULL_SCREEN_VERTICAL_SCREEN_VIDEO.getCode().equals(vo.getShow_style())) {
            dto.setVideoDto(updateSplashScreenVideoVo2Dto(vo.getVideo()));
        }
        return dto;
    }

    private List<SsaNewSplashScreenBaseImageDto> newSplashScreenBaseImageVos2NewDtos(List<SplashScreenBaseImageVo> vos) {
        if(CollectionUtils.isEmpty(vos)){
            return null;
        }
        List<SsaNewSplashScreenBaseImageDto> ssaNewSplashScreenBaseImageDtos = new ArrayList<>();
        for (SplashScreenBaseImageVo vo : vos) {
            if (BaseImageTypeEnum.LOGO.getCode().equals(vo.getType()) && Strings.isNullOrEmpty(vo.getUrl())) {
                continue;
            }
            ssaNewSplashScreenBaseImageDtos.add(newSplashScreenBaseImageVo2NewDto(vo));
        }
        return ssaNewSplashScreenBaseImageDtos;
    }

    private SsaNewSplashScreenBaseImageDto newSplashScreenBaseImageVo2NewDto(SplashScreenBaseImageVo vo) {
        return SsaNewSplashScreenBaseImageDto.builder().hash(vo.getHash().trim()).url(vo.getUrl().trim()).type(vo.getType()).build();
    }

    private SsaNewSplashScreenVideoDto newSplashScreenVideoVo2Dto(SsaNewSplashScreenVideoVo vo) {
        return SsaNewSplashScreenVideoDto.builder()
                .uposUrl(vo.getUpos_url())
                .uposAuth(vo.getUpos_auth())
                .bizId(vo.getBiz_id())
                .fileName(vo.getFile_name())
                .build();
    }

    public SsaUpdateSplashScreenDto updateExternalSplashScreenVo2Dto(UpdateExternalSplashScreenVo vo) {
        ScheduleDto schedule = queryScheduleService.getScheduleBaseInfoById(vo.getScheduleId());
        SsaConvertContext convertContext = SsaConvertContext.builder()
                .ssaArchive(SsaVideoPlayModeEnum.isArchive(schedule.getSsaVideoPlayMode()))
                .videoPlayMode(schedule.getSsaVideoPlayMode())
                .linkageType(SsaLinkageType.getByCode(schedule.getLinkageType()))
                .orderProduct(OrderProduct.getByCode(schedule.getOrderProduct()))
                .platformTypes(queryScheduleService.getScheduleOsTarget(schedule.getScheduleId(), true))
                .adx(schedule.isAdx())
                .ssaJumpAreaEffect(schedule.getJumpAreaEffect())
                .guideMaterialType(vo.getDynamicButtonVos().get(0).getGuideMaterialType())
                .effectiveType(schedule.getEffectiveType())
                .build();

        Integer jumpType = vo.getJumpType();
        List<SplashScreenJumpDTO> splashScreenJumpDTOS = new ArrayList<>();
        List<SplashScreenCustomizedDTO> customizedDTOS;
        List<SplashScreenDynamicButtonBO> buttonBOS = null;
        if(PlatformSwitchEnum.OPEN.getCode().equals(vo.getPlatformSwitch())) {
            if(vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
                //全屏跳转(现在只有互动闪屏)跳转信息放在外层对象
                splashScreenJumpDTOS = convertJumpInfo(vo.getSplashScreenJumpVos(), jumpType);
            }else {
                //模块跳转的闪屏跳转信息存放在按钮里面
                buttonBOS = convert2ButtonBOs(jumpType, vo.getTextColorStyle(), vo.getDynamicButtonVos());
            }
            customizedDTOS = convert2CustomizedDTO(vo.getSplashScreenCustomizedVos());
        }else {
            if(vo.getIsSupportButtonToInteract() != null && !vo.getIsSupportButtonToInteract() &&
                    ButtonInteractStyleEnum.SLIDE.getCode().equals(vo.getInteractStyle())) {
//                //不分端全屏跳转(现在只有互动闪屏)跳转信息放在外层对象
                SplashScreenJumpVo jumpVo = vo.getJumpVos().get(0);
                splashScreenJumpDTOS = convert2JumpDTOWithoutPlatform(jumpType, jumpVo.getIosPackageName(),
                        jumpVo.getAndroidPackageName(), vo.getJumpVos().get(0), convertContext);
            }else {
                //不分端模块跳转的闪屏跳转信息存放在按钮里面
                buttonBOS = convert2ButtonBOsWithoutPlatform(jumpType, vo.getTextColorStyle(),
                        vo.getDynamicButtonVos(),convertContext);
            }

            customizedDTOS = convert2CustomizedDTOWithoutPlatform(SplashScreenCustomizedVo.builder()
                    .customized_imp_url_list(vo.getCustomizedImpUrlList())
                    .customized_click_url_list(vo.getCustomizedClickUrlList())
                    .build(), convertContext);
        }
        List<SsaSplashScreenVersionControlDto> versionControlDtos = mockUpdateSsaVersionControl(vo, convertContext);
        SsaUpdateSplashScreenDto dto = SsaUpdateSplashScreenDto.builder()
                .isSkip(IsSkipEnum.YES.getCode())
                .ssaBaseImageDtos(updateBaseImageVos2Dtos(vo.getBaseImageVos(), vo.getId()))
                .ssaVersionControlDtos((vo.getUseDefaultVersion() == null || vo.getUseDefaultVersion())?
                        versionControlDtos : versionControlUpdateVos2DtoS(vo.getVersionControl(), vo.getId(), versionControlDtos))
                .ssaScheduleMappingDtos(vo.getSchedules().stream().map(scheduleSelectVo ->
                        SsaNewScheduleSplashScreenMappingDto.builder()
                                .scheduleId(scheduleSelectVo.getId())
                                .gdScheduleId(vo.getScheduleId())
                                .splashScreenId(vo.getId())
                                .beginTime(Timestamp.valueOf(scheduleSelectVo.getBegin_time()))
                                .endTime(Timestamp.valueOf(scheduleSelectVo.getEnd_time()))
                                .build()).collect(Collectors.toList()))
                .splashScreenJumpDTOS(splashScreenJumpDTOS)
                .ssaCustomizedDTOS(customizedDTOS)
                .buttonBOS(buttonBOS)
                .build();

        BeanUtils.copyProperties(vo, dto);
        if (SsaShowStyleType.getByCode(vo.getShowStyle()).isVideo()) {
            dto.setVideoDto(updateSplashScreenVideoVo2Dto(vo.getVideo()));
        }

        dto.setAdditionalComponents(convertAdditionComponents(vo.getAdditionalComponents()));


        dto.setSecondPageInfo(SsaSplashScreenConverter.MAPPER.toDto(vo.getSecondPageInfo()));
        dto.setSsaArchiveMaterial(SsaSplashScreenConverter.MAPPER.toDto(vo.getSsaArchiveMaterial()));
        dto.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelDto(vo.getProductLabel()));
        dto.setMiniProgram(GdCreativeConverter.MAPPER.toMiniProgramDto(vo.getMiniProgram()));
        dto.setInteractEgg(SsaSplashScreenConverter.MAPPER.toSecondPageInteractEggDto(vo.getInteractEgg()));
        return dto;
    }

    private List<SsaSplashScreenBaseImageDto> updateBaseImageVos2Dtos(List<UpdateSplashScreenBaseImageVo> vos, Integer splashScreenId) {

        List<SsaSplashScreenBaseImageDto> dtos = new ArrayList<>();

        for (UpdateSplashScreenBaseImageVo vo : vos) {
            if (Strings.isNullOrEmpty(vo.getUrl()) || Strings.isNullOrEmpty(vo.getHash())) {
                continue;
            }
            dtos.add(updateBaseImageVo2Dto(vo, splashScreenId));
        }
        return dtos;
    }

    private SsaSplashScreenBaseImageDto updateBaseImageVo2Dto(UpdateSplashScreenBaseImageVo vo, Integer splashScreenId) {
        return SsaSplashScreenBaseImageDto.builder()
                .hash(vo.getHash().trim())
                .url(vo.getUrl().trim())
                .type(vo.getType())
                .splashScreenId(splashScreenId)
                .build();
    }

    private List<SsaSplashScreenVersionControlDto> updateSsaSplashScreenVersionControlVos2Dtos(
            List<UpdateSplashScreenVersionControlVo> vos,
            Integer splashScreenId) {
        List<SsaNewSplashScreenVersionControlDto> ssaNewSplashScreenVersionControlDtos =
                splashNewScreenVersionControlVos2Dtos(0,
                vos.stream().map(vo -> NewSplashScreenVersionControlVo.builder()
                .version(vo.getVersion()).compare_type(vo.getCompare_type())
                        .platform_type(vo.getPlatform_type()).build()).collect(Collectors.toList()));

        return ssaNewSplashScreenVersionControlDtos.stream().map(newDto -> SsaSplashScreenVersionControlDto
                .builder().platformId(newDto.getPlatformId())
                .startVersion(newDto.getStartVersion()).endVersion(newDto.getEndVersion())
                .splashScreenId(splashScreenId).build()).collect(Collectors.toList());
    }

    private SsaUpdateSplashScreenVideoDto updateSplashScreenVideoVo2Dto(SsaUpdateSplashScreenVideoVo vo) {
        return SsaUpdateSplashScreenVideoDto.builder()
                .uposUrl(vo.getUpos_url())
                .bizId(vo.getBiz_id())
                .uposAuth(vo.getUpos_auth())
                .splashScreenId(vo.getSplash_screen_id())
                .fileName(vo.getFile_name())
                .build();
    }

    public SsaNewInternalSplashScreenDto newInternalSplashScreenVo2Dto(NewInternalSplashScreenVo vo) {
        return SsaNewInternalSplashScreenDto.builder()
                .copywriting(vo.getCopy_writing().trim())
                .isSkip(vo.getIs_skip())
                .cmMark(vo.getCm_mark())
                .showStyle(vo.getShow_style())
                .title(vo.getTitle())
                .type(vo.getType())
                .startTime(vo.getStart_time())
                .endTime(vo.getEnd_time())
                .baseImageDtos(newSplashScreenBaseImageVos2NewDtos(vo.getBase_image_vos()))
                .videoDto(newSplashScreenVideoVo2Dto(vo.getVideo()))
                .ssaNewSplashScreenVersionControlDtos(CollectionUtils.isEmpty(vo.getVersion_control()) ?
                        Collections.emptyList() : splashNewScreenVersionControlVos2Dtos(0,
                        vo.getVersion_control()))
                .salesType(SalesType.SSA_CPT.getCode())
                //分端获取跳转信息
                .splashScreenJumpDTOS(convertJumpInfo(vo.getSplash_screen_jump_vos(), vo.getJump_type()))
                .build();

    }

    public SplashScreenBaseImageVo getCompressBaseImageVoByDto(SsaSplashScreenBaseImageDto dto) {
        if (dto == null) {
            return SplashScreenBaseImageVo.builder()
                    .hash("")
                    .url("")
                    .type(0)
                    .build();
        } else {
            BaseImageTypeEnum imageType = BaseImageTypeEnum.getByCode(dto.getType());
            return SplashScreenBaseImageVo.builder()
                    .hash(dto.getHash())
                    .url(dto.getUrl().concat(SsaUtils.getCompressImageSuffix(imageType.getWidth(), imageType.getHeight())))
                    .type(dto.getType())
                    .build();
        }
    }

    public List<SsaNewSplashScreenVersionControlDto> splashNewScreenVersionControlVos2Dtos(Integer interactStyle,
            List<NewSplashScreenVersionControlVo> vos) {
        if(ButtonInteractStyleEnum.SLIDE.getCode().equals(interactStyle)){
            //todo 配置话
            return convert2SsaVersionControl(10010,
                    6010600);
        }
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }

        List<SsaNewSplashScreenVersionControlDto> dtos = new ArrayList<>();
        for (NewSplashScreenVersionControlVo vo : vos) {
            if (vo.getVersion() == null) {
                continue;
            }
            SsaNewSplashScreenVersionControlDto dto = new SsaNewSplashScreenVersionControlDto();
            dto.setPlatformId(vo.getPlatform_type());
            switch (CompareType.getByCode(vo.getCompare_type())) {
                case GREATER_THAN:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(MAX_VERSION);
                    break;
                case EQUAL:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(vo.getVersion());
                    break;
                case LESS_THAN:
                    dto.setStartVersion(MIN_VERSION);
                    dto.setEndVersion(vo.getVersion());
                    break;
            }
            dtos.add(dto);
        }
        return dtos;
    }

    public List<SsaNewSplashScreenVersionControlDto> convert2SsaVersionControl(
            Integer topViewStartVersionIphone,
            Integer topViewStartVersionAndroid) {

        return Lists.newArrayList(
                SsaNewSplashScreenVersionControlDto.builder()
                        .platformId(com.bilibili.cpt.platform.biz.enumerate.PlatformType.IPHONE.getCode())
                        .startVersion(topViewStartVersionIphone)
                        .endVersion(MAX_VERSION)
                        .build(),
                SsaNewSplashScreenVersionControlDto.builder()
                        .platformId(com.bilibili.cpt.platform.biz.enumerate.PlatformType.IPAD.getCode())
                        .startVersion(MAX_VERSION)
                        .endVersion(MAX_VERSION)
                        .build(),
                SsaNewSplashScreenVersionControlDto.builder()
                        .platformId(com.bilibili.cpt.platform.biz.enumerate.PlatformType.ANDROID.getCode())
                        .startVersion(topViewStartVersionAndroid)
                        .endVersion(MAX_VERSION)
                        .build());
    }

    public SplashScreenDetailVo ssaSplashScreenDetailDto2DetailVo(SsaSplashScreenDetailDto dto) {
        List<SplashScreenJumpVo> jumpVos = new ArrayList<>();
        List<SplashScreenCustomizedVo> customizedVos = new ArrayList<>();
        List<SplashScreenDynamicButtonVo> buttonVos = new ArrayList<>();
        if(PlatformSwitchEnum.OPEN.getCode().equals(dto.getPlatformSwitch())) {
            List<SplashScreenJumpDTO> jumpDTOS = dto.getSplashScreenJumpDTOS();
            Map<Integer, List<SplashScreenJumpDTO>> seq2jumpDTOS = new HashMap<>();
            if(!CollectionUtils.isEmpty(jumpDTOS)) {
                seq2jumpDTOS = dto.getSplashScreenJumpDTOS().stream()
                        .collect(Collectors.groupingBy(SplashScreenJumpDTO::getSeq));
                jumpVos = jumpDTOS.stream()
                        //前端不感知IPAD_HD
                        .filter(jump -> !PlatformType.IPAD_HD.getCode().equals(jump.getPlatformId()))
                        .map(t -> {
                            SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
                            BeanUtils.copyProperties(t, jumpVo);
                            jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                            return jumpVo;
                        }).collect(Collectors.toList());
            }


            if (!CollectionUtils.isEmpty(dto.getSsaCustomizedDTOS())) {
                customizedVos = dto.getSsaCustomizedDTOS().stream()
                        //前端不感知IPAD_HD
                        .filter(custom -> !PlatformType.IPAD_HD.getCode().equals(custom.getPlatformId()))
                        .map(t -> SplashScreenCustomizedVo.builder().platform_id(t.getPlatformId())
                                .customized_click_url_list(t.getSsaCustomizedClickUrlList())
                                .customized_imp_url_list(t.getSsaCustomizedImpUrlList()).build()).collect(Collectors.toList());
            }

            List<SplashScreenDynamicButtonBO> buttonBOS = dto.getButtonBOs();
            if(!CollectionUtils.isEmpty(buttonBOS)) {
                //现在没有选择式闪屏，取第一个即可
                SplashScreenDynamicButtonBO screenDynamicButton = buttonBOS.get(0);
                SplashScreenDynamicButtonVo buttonVo = new SplashScreenDynamicButtonVo();
                BeanUtils.copyProperties(screenDynamicButton, buttonVo);
                List<SplashScreenJumpDTO> jumpDTOS1 = seq2jumpDTOS.get(screenDynamicButton.getSeq());
                if(!CollectionUtils.isEmpty(jumpDTOS1)) {
                    buttonVo.setSplashScreenJumpVos(jumpDTOS1.stream()
                            //前端不感知IPAD_HD
                            .filter(jump -> !PlatformType.IPAD_HD.getCode().equals(jump.getPlatformId()))
                            .map(t -> {
                                SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
                                BeanUtils.copyProperties(t, jumpVo);
                                jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                                return jumpVo;
                            }).collect(Collectors.toList()));
                }
                buttonVos.add(buttonVo);
            }
        }

        SplashScreenDetailVo vo = SplashScreenDetailVo.builder()
                .id(dto.getId())
                .business_side_id(dto.getBusinessSideId())
                .business_side_name(dto.getBusinessSideName())
                .copy_writing(dto.getCopywriting())
                .is_skip(dto.getIsSkip())
                .issued_time(dto.getIssuedTime())
                .jump_type(CollectionUtils.isEmpty(dto.getSplashScreenJumpDTOS()) ? 0
                        : dto.getSplashScreenJumpDTOS().get(0).getJumpType())
                .jump_type_desc(CollectionUtils.isEmpty(dto.getSplashScreenJumpDTOS()) ? "" :
                        SsaJumpType.getByCode(dto.getSplashScreenJumpDTOS().get(0).getJumpType()).getDesc())
                .show_style(dto.getShowStyle())
                .ssa_order_id(dto.getSsaOrderId())
                .schedule_id(dto.getGdScheduleId())
                .order_id(dto.getGdOrderId())
                .order_name("test")
                .schedule_name("test")
                .cm_mark(dto.getCmMark())
                .ssa_order_name(dto.getSsaOrderName())
                .title(dto.getTitle())
                .type(dto.getType())
                .reason(dto.getReason())
                .type_desc(SsaSplashScreenType.getByCode(dto.getType()).getDesc())
                .base_image_vos(ssaSplashScreenBaseImageDtos2Vos(dto.getBaseImageDtos()))
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenStatus.getByCode(dto.getStatus()).getDesc())
                .start_time(dto.getStartTime())
                .end_time(dto.getEndTime())
                .version_control(ssaSplashScreenVersionControlDtos2Vos(dto.getSsaSplashScreenVersionControlDtos()))
                .splash_screen_schedules(ssaScheduleDtosToVos(dto))
                .video(ssaSplashScreenVideoDto2Vo(dto.getSsaSplashScreenVideoDto()))
                .is_version_limit(!CollectionUtils.isEmpty(dto.getSsaSplashScreenVersionControlDtos()))
                .time_target(dto.getTimeTarget())
                .encryption(dto.getEncryption())
                .share_state(dto.getShareState())
                .share_title(dto.getShareTitle())
                .share_sub_title(dto.getShareSubTitle())
                .share_image_url(dto.getShareImageUrl())
                .share_image_hash(dto.getShareImageHash())
                .splash_screen_jump_vos(jumpVos)
                .splash_screen_customized_vos(customizedVos)
                .platform_switch(dto.getPlatformSwitch())
                .product(dto.getOrderProduct())
                .interact_style(dto.getInteractStyle())
                .dynamic_button_vos(buttonVos)
                .use_default_version(dto.getUseDefaultVersion())
                .support_custom_guide(dto.getSupportCustomGuide())
                .extra_guide_instructions(dto.getExtraGuideInstructions())
                .extra_scheme_copywriting(dto.getExtraSchemeCopywriting())
                .button_bg_color_style(dto.getButtonBgColorStyle())
                .is_support_button_to_interact(dto.getIsSupportButtonToInteract())
                .text_color_style(dto.getTextColorStyle())
                .interact_instructions(dto.getInteractInstructions())
                .line_color_type(dto.getLineColorType())
                .is_enable_live_booking(Utils.isPositive(dto.getLiveBookingId()))
                .live_booking_id(dto.getLiveBookingId())
                .build();

        if(PlatformSwitchEnum.CLOSE.getCode().equals(dto.getPlatformSwitch())){
            //下面这种过滤意义也不大，既然都不分端了，我理解都是一样的？？

            List<SplashScreenJumpDTO> iPhoneJumpDTOS = dto.getSplashScreenJumpDTOS().stream()
                    //99是选择式按钮中的纯文字无交互按钮的跳转信息,是后端mock的,前段不需要展示
                    .filter(o->o.getSeq() != 99 && PlatformType.isIos(o.getPlatformId()))
                    .collect(Collectors.toList());

            List<SplashScreenJumpDTO> androidJumpDTOS = dto.getSplashScreenJumpDTOS().stream()
                    //99是选择式按钮中的纯文字无交互按钮的跳转信息,是后端mock的,前段不需要展示
                    .filter(o->o.getSeq() != 99  && PlatformType.isAndroid(o.getPlatformId()))
                    .collect(Collectors.toList());

            String iosPackageName = CollectionUtils.isEmpty(iPhoneJumpDTOS) ? "": iPhoneJumpDTOS.get(0).getPackageName();
            String androidPackageName = CollectionUtils.isEmpty(androidJumpDTOS) ? "": androidJumpDTOS.get(0).getPackageName();

            SplashScreenCustomizedDTO customizedDTO = CollectionUtils.isEmpty(dto.getSsaCustomizedDTOS()) ?
                    new SplashScreenCustomizedDTO() : dto.getSsaCustomizedDTOS().get(0);

            List<SplashScreenDynamicButtonBO> buttonBOS = dto.getButtonBOs();
            if(!CollectionUtils.isEmpty(buttonBOS)) {
                //既然不分端，那就取第一个
                SplashScreenDynamicButtonBO screenDynamicButton = buttonBOS.get(0);
                SplashScreenDynamicButtonVo buttonVo = new SplashScreenDynamicButtonVo();
                BeanUtils.copyProperties(screenDynamicButton, buttonVo);
                List<SplashScreenJumpVo> splashScreenJumpVos = Optional.ofNullable(screenDynamicButton.getSplashScreenJumpDTOS())
                        .map(js -> js.stream().map(t -> {
                            SplashScreenJumpVo jump = new SplashScreenJumpVo();
                            BeanUtils.copyProperties(t, jump);
                            jump.setActualJumpLink(CommonBvidUtils.urlBvToAv(t.getActualJumpLink()));
                            return jump;
                        }).collect(Collectors.toList()))
                        .orElse(Collections.emptyList());
                buttonVo.setJumpVos(splashScreenJumpVos);
                buttonVo.setIosPackageName(iosPackageName);
                buttonVo.setAndroidPackageName(androidPackageName);
                buttonVos.add(buttonVo);
                vo.setDynamic_button_vos(buttonVos);
            }

            //由于没分端，所以我们选择其中一个跳转链接即可
            SplashScreenJumpDTO screenJumpDTO = dto.getSplashScreenJumpDTOS().get(0);
            SplashScreenJumpVo jumpVo = new SplashScreenJumpVo();
            BeanUtils.copyProperties(screenJumpDTO, jumpVo);

            jumpVo.setActualJumpLink(CommonBvidUtils.urlBvToAv(screenJumpDTO.getActualJumpLink()));
            jumpVo.setInteractLinkTypeDesc(StringUtils.isEmpty(screenJumpDTO.getInteractLink()) ? ""
                    : GdJumpType.getByCode(screenJumpDTO.getInteractLinkType()).getDesc());
            jumpVo.setJumpType(screenJumpDTO.getJumpType());
            jumpVo.setJumpTypeDesc(SsaJumpType.getByCode(screenJumpDTO.getJumpType()).getDesc());
            jumpVo.setAndroidPackageName(androidPackageName);
            jumpVo.setIosPackageName(iosPackageName);

            vo.setJump_vo(jumpVo);
            vo.setJump_vos(Lists.newArrayList(jumpVo));
            vo.setAndroid_package_name(androidPackageName);
            vo.setIos_package_name(iosPackageName);
            vo.setCustomized_click_url_list(customizedDTO.getSsaCustomizedClickUrlList());
            vo.setCustomized_imp_url_list(customizedDTO.getSsaCustomizedImpUrlList());
        }

        vo.setAdditionalComponents(SsaSplashScreenConverter.MAPPER.toVo(dto.getAdditionalComponents()));
        vo.setSecondPageInfo(SsaSplashScreenConverter.MAPPER.toVo(dto.getSecondPageInfo()));
        vo.setSsaArchiveMaterial(SsaSplashScreenConverter.MAPPER.toVo(dto.getSsaArchiveMaterial()));
        vo.setProductLabel(GdCreativeConverter.MAPPER.toProductLabelVo(dto.getProductLabel()));
        vo.setMiniProgram(GdCreativeConverter.MAPPER.toMiniProgramVo(dto.getMiniProgram()));
        vo.setInteractEgg(SsaSplashScreenConverter.MAPPER.toSecondPageInteractEggVo(dto.getInteractEgg()));
        return vo;
    }

    private List<SplashScreenScheduleVo> ssaScheduleDtosToVos(SsaSplashScreenDetailDto dto) {
        return dto.getSsaScheduleDtos().stream()
                .map(ssd -> ssaScheduleDtoToVo(dto.getOrderProduct(), ssd))
                .collect(Collectors.toList());
    }

    private SplashScreenBaseImageVo ssaSplashScreenBaseImageDto2Vo(SsaSplashScreenBaseImageDto dto) {
        return SplashScreenBaseImageVo.builder().hash(dto.getHash()).url(dto.getUrl()).type(dto.getType()).build();
    }

    private List<SplashScreenBaseImageVo> ssaSplashScreenBaseImageDtos2Vos(List<SsaSplashScreenBaseImageDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        return dtos.stream().map(this::ssaSplashScreenBaseImageDto2Vo).collect(Collectors.toList());
    }

    private SplashScreenImageVo ssaSplashScreenImageDto2Vo(SsaSplashScreenImageDto dto) {
        return SplashScreenImageVo.builder()
                .id(dto.getId())
                .platform_id(dto.getPlatformId())
                .image_rule_id(dto.getImageRuleId())
                .width(dto.getWidth())
                .height(dto.getHeight())
                .url(dto.getUrl())
                .build();
    }

    public List<SplashScreenImageVo> ssaSplashScreenImageDtos2Vos(List<SsaSplashScreenImageDto> dtos,
                                                                         List<Integer> filterImageRuleIds) {
        return dtos.stream().filter(dto -> !filterImageRuleIds.contains(dto.getImageRuleId()))
                .map(dto -> {
                    if (!Strings.isNullOrEmpty(dto.getUrl())) {
                        dto.setUrl(dto.getUrl().concat(SsaUtils.getCompressImageSuffix(dto.getWidth(), dto.getHeight())));
                    }
                    return ssaSplashScreenImageDto2Vo(dto);
                }).collect(Collectors.toList());
    }

    public SsaSplashScreenVideoVo ssaSplashScreenVideoDto2Vo(SsaSplashScreenVideoDto dto) {
        if (dto == null) {
            return null;
        }
        return SsaSplashScreenVideoVo.builder()
                .upos_url(dto.getUposUrl())
                .xcode_md5(dto.getXcodeMd5())
                .xcode_height(dto.getXcodeHeight())
                .xcode_width(dto.getXcodeWidth())
                .id(dto.getId())
                .biz_id(dto.getBizId())
                .splash_screen_id(dto.getSplashScreenId())
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenVideoStatus.getByCode(dto.getStatus()).getDesc())
                .xcode_upos_url(dto.getXcodeUposUrl())
                .file_name(dto.getFileName())
                .upos_auth(dto.getUposAuth())
                .url(dto.getUrl())
                .xcode_before_url(dto.getXcodeBeforeUrl())
                .build();

    }

    private SplashScreenScheduleVo ssaScheduleDtoToVo(Integer orderProduct, SsaScheduleDto dto) {
        //闪屏GD不返回轮次
        return SplashScreenScheduleVo.builder()
                .id(dto.getSsaScheduleId())
                .used_rotation_num(Objects.equals(OrderProduct.SSA_GD_PLUS.getCode(), orderProduct) ? 0 : dto.getRotationNum())
                .begin_time(TimeUtil.timestampToIsoTimeStr(dto.getSsaStartTime()))
                .end_time(TimeUtil.timestampToIsoTimeStr(dto.getSsaEndTime()))
                .build();
    }

    public static List<SplashScreenVersionControlVo> ssaSplashScreenVersionControlDtos2Vos(List<SsaSplashScreenVersionControlDto> dtos) {
        return dtos.stream().map(SsaSplashScreenConvert::ssaSplashScreenVersionControlDto2Vo).collect(Collectors.toList());
    }

    public static SplashScreenVersionControlVo ssaSplashScreenVersionControlDto2Vo(SsaSplashScreenVersionControlDto dto) {
        SplashScreenVersionControlVo vo = SplashScreenVersionControlVo.builder()
                .platform_type(dto.getPlatformId())
                .platform_type_desc(PlatformType.getByCode(dto.getPlatformId()).getDesc()).build();
        if (MIN_VERSION.equals(dto.getStartVersion()) && !MAX_VERSION.equals(dto.getEndVersion())) {
            vo.setCompare_type(CompareType.LESS_THAN.getCode());
            vo.setVersion(dto.getEndVersion());

        }
        if (!MIN_VERSION.equals(dto.getStartVersion()) && !MAX_VERSION.equals(dto.getEndVersion())) {
            vo.setCompare_type(CompareType.EQUAL.getCode());
            vo.setVersion(dto.getEndVersion());

        }
        if (!MIN_VERSION.equals(dto.getStartVersion()) && MAX_VERSION.equals(dto.getEndVersion())) {
            vo.setCompare_type(CompareType.GREATER_THAN.getCode());
            vo.setVersion(dto.getStartVersion());
        }
        return vo;
    }

    public InternalSplashScreenListVo ssaSplashScreenDto2InternalListVo(SsaSplashScreenDto dto, StatSplashScreenDto statDto) {

        InternalSplashScreenListVo vo = InternalSplashScreenListVo.builder()
                .id(dto.getId())
                .show_style(dto.getShowStyle())
                .start_time(dto.getStartTime())
                .end_time(dto.getEndTime())
                .show_time(dto.getShowTime())
                .cm_mark(dto.getCmMark())
                .show_style_desc(SsaShowStyleType.getByCode(dto.getShowStyle()).getDesc())
                .status(dto.getStatus())
                .is_skip(dto.getIsSkip())
                .status_desc(SsaSplashScreenStatus.getByCode(dto.getStatus()).getDesc())
                .title(dto.getTitle())
                .reason(dto.getReason())
                .show_count(0L)
                .click_count(0)
                .click_rate(BigDecimal.ZERO)
                .build();

        if (statDto != null) {
            vo.setShow_count(statDto.getShowAccount());
            vo.setClick_count(statDto.getClickCount());
            vo.setClick_rate(statDto.getClickRate());
        }

        if (SsaShowStyleType.getByCode(dto.getShowStyle()).isVideo()) {
            vo.setVideo(ssaSplashScreenVideoDto2Vo(dto.getVideo()));
        }
        return vo;
    }

    public ExternalSplashScreenListVo ssaSplashScreenDto2ExternalListVo(SsaSplashScreenDto dto,
                                                                        StatSplashScreenDto statDto,
                                                                        GdOrderDto gdOrderDto,
                                                                        ContractDto crmContractDto,
                                                                        ScheduleDto gdScheduleDto,
                                                                        SsaSplashScreenBaseImageDto baseImageDto,
                                                                        Timestamp previewEndTime,
                                                                        int previewStatus,
                                                                        OttStatSplashScreenDto ottStatDto) {
        ExternalSplashScreenListVo vo = ExternalSplashScreenListVo.builder()
                .cycle_id(dto.getCycleId())
                .id(dto.getId())
                .contract_number(String.valueOf(crmContractDto.getContractNumber()))
                .contract_name(crmContractDto.getName())
                .order_id(dto.getGdOrderId())
                .schedule_id(dto.getGdScheduleId())
                .show_style(dto.getShowStyle())
                .show_time(dto.getShowTime())
                .cm_mark(dto.getCmMark())
                .cm_mark_name(SsaCmMark.getByCode(dto.getCmMark()).getDesc())
                .is_skip(dto.getIsSkip())
                .schedules(Collections.emptyList())
                .show_style_desc(SsaShowStyleType.getByCode(dto.getShowStyle()).getDesc())
                .ssa_order_id(dto.getSsaOrderId())
                .status(dto.getStatus())
                .status_desc(SsaSplashScreenStatus.getByCode(dto.getStatus()).getDesc())
                .reason(dto.getReason())
                .title(dto.getTitle())
                .sales_type(dto.getSalesType())
                .show_count(0L)
                .click_count(0)
                .click_rate(BigDecimal.ZERO)
                .order_name(gdOrderDto != null & gdOrderDto.getOrderName() != null ? gdOrderDto.getOrderName() : "--")
                .schedule_name(gdScheduleDto != null ? gdScheduleDto.getName() : "--")
                .schedule_begin_date(gdScheduleDto != null ? StringDateParser.getDateString(gdScheduleDto.getBeginDate()) : "--")
                .schedule_end_date(gdScheduleDto != null ? StringDateParser.getDateString(gdScheduleDto.getEndDate()) : "--")
                .base_image_vo(getCompressBaseImageVoByDto(baseImageDto))
                .preview_end_time((previewEndTime == null ? 0L : previewEndTime.getTime()))
                .preview_status(previewStatus)
                .creator_name(dto.getCreatorName())
                .promotion_purpose_type(dto.getSalesType() != null && SalesType.OTT_CPT.getCode() == dto.getSalesType() ?
                        PromotionPurposeType.LANDING_PAGE.getCode() : PromotionPurposeType.DEFAULT.getCode())
                .promotion_purpose_type_desc(dto.getSalesType() != null && SalesType.OTT_CPT.getCode() == dto.getSalesType() ?
                        PromotionPurposeType.LANDING_PAGE.getDesc() : PromotionPurposeType.DEFAULT.getDesc())
                .order_product(dto.getOrderProduct())
                .ssa_video_play_mode(gdScheduleDto != null ? gdScheduleDto.getSsaVideoPlayMode() :
                        SsaVideoPlayModeEnum.NORMAL.getCode())
                .build();
        if (statDto != null) {
            vo.setShow_count(statDto.getShowAccount());
            vo.setClick_count(statDto.getClickCount());
            vo.setClick_rate(statDto.getClickRate());
        }
        boolean isSsaPd = OrderProduct.SSA_PD.getCode().equals(dto.getOrderProduct());
        if (!CollectionUtils.isEmpty(dto.getSsaScheduleDtos())
                && !isSsaPd) {
            List<SplashScreenScheduleVo> scheduleVos = dto.getSsaScheduleDtos().stream().
                    map(scheduleDto -> SplashScreenScheduleVo.builder()
                            .id(scheduleDto.getSsaScheduleId())
                            .begin_time(scheduleDto.getSsaStartTime() == null ? "--"
                                    : TimeUtil.timestampToIsoDateStr(scheduleDto.getSsaStartTime()))
                            .end_time(scheduleDto.getSsaEndTime() == null ? "--"
                                    : TimeUtil.timestampToIsoDateStr(scheduleDto.getSsaEndTime()))
                            .used_rotation_num(scheduleDto.getRotationNum())
                            .build()).collect(Collectors.toList());
            vo.setSchedules(scheduleVos);
            String timeSegments = "";
            if (OrderProduct.SSA_OTT_CPT.getCode() == dto.getOrderProduct()
                    || OrderProduct.SSA_OTT_GD.getCode() == dto.getOrderProduct()) {
                timeSegments = dto.getSsaScheduleDtos().stream().map(x ->
                        x.getSsaStartTime() + "~" + x.getSsaEndTime()).collect(Collectors.joining(","));
            } else if (SalesType.SSA_CPM.getCode() == dto.getSalesType() ||
                    SalesType.SSA_CPT_PLUS.getCode() == dto.getSalesType()
                    || SalesType.ADX_SPLASH_CPM.getCode() == dto.getSalesType()
                    || SalesType.SSA_GD_PLUS.getCode() == dto.getSalesType()) {
                timeSegments = scheduleVos.stream().map(x ->
                        x.getBegin_time() + "~" + x.getEnd_time()).collect(Collectors.joining(","));
            } else {
                timeSegments = scheduleVos.stream().map(x ->
                        x.getBegin_time() + (Lists.newArrayList(SalesType.SSA_GD.getCode())
                                .contains(dto.getSalesType()) ? ""
                                : " " + x.getUsed_rotation_num() + "轮")).collect(Collectors.joining(","));
            }
            vo.setTime_segments(timeSegments);
        } else if (gdScheduleDto != null
                && (gdScheduleDto.getOrderProduct().equals(OrderProduct.SSA_OTT_GD.getCode())
                || gdScheduleDto.getOrderProduct().equals(OrderProduct.SSA_OTT_CPT.getCode())
                || isSsaPd)
        ) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeInfo;
            if (Objects.nonNull(gdScheduleDto.getGdBeginTime()) && Objects.nonNull(gdScheduleDto.getGdEndTime())) {
                timeInfo = String.format("%s ~ %s", format.format(gdScheduleDto.getGdBeginTime()),
                        format.format(gdScheduleDto.getGdEndTime()));
            } else {
                timeInfo = String.format("%s ~ %s", format.format(gdScheduleDto.getBeginDate()),
                        format.format(gdScheduleDto.getEndDate()));
            }
            vo.setTime_segments(timeInfo);
        }


        if (SsaShowStyleType.getByCode(dto.getShowStyle()).isVideo()) {
            vo.setVideo(ssaSplashScreenVideoDto2Vo(dto.getVideo()));
        }

        log.info("ssaSplashScreenDto2ExternalListVo gdScheduleDto [{}], ottStatDto [{}]",
                gdScheduleDto, ottStatDto);
        if (gdScheduleDto != null
                && OrderProduct.SSA_OTT_CPT.getCode().equals(gdScheduleDto.getOrderProduct())
                //闪屏ott-cpt改版后不走这个逻辑，改版后salesType为74
                && SalesType.SSA_CPT_PLUS.getCode() != dto.getSalesType()) {

            vo.setShow_count(ottStatDto.getSplashShowCount() == null ? 0 : Long.valueOf(ottStatDto.getSplashShowCount()));
            vo.setFlow_show_count(ottStatDto.getInlineShowCount());
            vo.setClick_rate(ottStatDto.getClickRate());
            vo.setClick_count(ottStatDto.getClickCount());
        }
        return vo;
    }

    public static List<SsaNewSplashScreenVersionControlDto> versionControlVos2DtoS(List<NewSplashScreenVersionControlVo> vos,
                                                                                    List<SsaNewSplashScreenVersionControlDto> mockControls) {

        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }

        List<SsaNewSplashScreenVersionControlDto> dtos = new ArrayList<>();
        for (NewSplashScreenVersionControlVo vo : vos) {
            if (vo.getVersion() == null) {
                continue;
            }
            SsaNewSplashScreenVersionControlDto dto = new SsaNewSplashScreenVersionControlDto();
            dto.setPlatformId(vo.getPlatform_type());
            switch (CompareType.getByCode(vo.getCompare_type())) {
                case GREATER_THAN:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(MAX_VERSION);
                    break;
                case EQUAL:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(vo.getVersion());
                    break;
                case LESS_THAN:
                    dto.setStartVersion(MIN_VERSION);
                    dto.setEndVersion(vo.getVersion());
                    break;
            }
            dtos.add(dto);
        }
        Assert.isTrue(dtos.size() == mockControls.size(), "自定义版本号时每个端都需要填版本号");

        Map<Integer, SsaNewSplashScreenVersionControlDto> controlDtoMap = dtos.stream()
                .collect(Collectors.toMap(SsaNewSplashScreenVersionControlDto::getPlatformId, t->t));
        mockControls.forEach(t->{
            SsaNewSplashScreenVersionControlDto controlDto = controlDtoMap.get(t.getPlatformId());
            Assert.isTrue(controlDto.getStartVersion() >= t.getStartVersion(),
                    "自定义版本号时,此样式的闪屏"+ PlatformType.getByCode(t.getPlatformId()).getDesc()
                            + "端的版本不能低于:" + t.getStartVersion());
        });
        return dtos;
    }

    public static List<SsaSplashScreenVersionControlDto> versionControlUpdateVos2DtoS(List<NewSplashScreenVersionControlVo> vos,
                                                                                       Integer splashScreenId,
                                                                                       List<SsaSplashScreenVersionControlDto> mockControlDtos) {

        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }

        List<SsaSplashScreenVersionControlDto> dtos = new ArrayList<>();

        for (NewSplashScreenVersionControlVo vo : vos) {
            if (vo.getVersion() == null) {
                continue;
            }
            SsaSplashScreenVersionControlDto dto = new SsaSplashScreenVersionControlDto();
            dto.setPlatformId(vo.getPlatform_type());
            switch (CompareType.getByCode(vo.getCompare_type())) {
                case GREATER_THAN:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(MAX_VERSION);
                    break;
                case EQUAL:
                    dto.setStartVersion(vo.getVersion());
                    dto.setEndVersion(vo.getVersion());
                    break;
                case LESS_THAN:
                    dto.setStartVersion(MIN_VERSION);
                    dto.setEndVersion(vo.getVersion());
                    break;
            }
            dto.setSplashScreenId(splashScreenId);
            dtos.add(dto);
        }

        Map<Integer, SsaSplashScreenVersionControlDto> controlDtoMap = dtos.stream()
                .collect(Collectors.toMap(SsaSplashScreenVersionControlDto::getPlatformId, t->t));
        mockControlDtos.forEach(t->{
            SsaSplashScreenVersionControlDto controlDto = controlDtoMap.get(t.getPlatformId());
            Assert.isTrue(controlDto.getStartVersion() >= t.getStartVersion(),
                    "自定义版本号时,此样式的闪屏"+ PlatformType.getByCode(t.getPlatformId()).getDesc()
                            + "端的版本不能低于:" + t.getStartVersion());
        });
        return dtos;
    }

    public List<SsaNewSplashScreenVersionControlDto> mockSsaVersionControl(NewExternalSplashScreenVo vo,
                                                                           SsaConvertContext context) {
        List<Pair<Integer, Integer>> versions = SsaVersion.parseVersion(SsaVersion.VersionContext.builder()
                .orderProduct(vo.getOrderProduct())
                .buttonStyle(vo.getButtonStyle())
                .videoPlayMode(context.getVideoPlayMode())
                .platforms(context.getPlatformTypes().stream().map(PlatformType::getCode).collect(Collectors.toList()))
                .adx(context.isAdx())
                .componentTypes(Optional.ofNullable(vo.getAdditionalComponents()).map(SsaAdditionalComponentsVo::getComponentTypes).orElse(null))
                .ssaJumpAreaEffect(context.getSsaJumpAreaEffect())
                .guideMaterialType(context.getGuideMaterialType())
                .isEnableLiveBooking(vo.getIsEnableLiveBooking())
                .effectiveType(context.getEffectiveType())
                .build());
        return versions.stream().map(o -> SsaNewSplashScreenVersionControlDto.builder()
                .platformId(o.getKey())
                .startVersion(o.getValue())
                .endVersion(MAX_VERSION)
                .build()).collect(Collectors.toList());
    }

    public List<SsaSplashScreenVersionControlDto> mockUpdateSsaVersionControl(UpdateExternalSplashScreenVo vo,
                                                                              SsaConvertContext context) {
        List<Pair<Integer, Integer>> versions = SsaVersion.parseVersion(SsaVersion.VersionContext.builder()
                .orderProduct(vo.getOrderProduct())
                .buttonStyle(vo.getButtonStyle())
                .videoPlayMode(context.getVideoPlayMode())
                .platforms(context.getPlatformTypes().stream().map(PlatformType::getCode).collect(Collectors.toList()))
                .adx(context.isAdx())
                .componentTypes(Optional.ofNullable(vo.getAdditionalComponents()).map(SsaAdditionalComponentsVo::getComponentTypes).orElse(null))
                .ssaJumpAreaEffect(context.getSsaJumpAreaEffect())
                .guideMaterialType(context.getGuideMaterialType())
                .isEnableLiveBooking(vo.getIsEnableLiveBooking())
                .effectiveType(context.getEffectiveType())
                .build());
        return versions.stream().map(o -> SsaSplashScreenVersionControlDto.builder()
                .platformId(o.getKey())
                .startVersion(o.getValue())
                .endVersion(MAX_VERSION)
                .build()).collect(Collectors.toList());
    }

    public Boolean isSupportShare(Integer jumpType, String jumpLink){
        List<String> nonSupportSchemaList = systemConfigService
                .getValueReturnList(SystemConfigEnum.NON_SUPPORT_SHARE_SCHEMA.getCode());
        if(StringUtils.isEmpty(jumpLink)){
            return false;
        }
        if(!SsaJumpType.LINK.getCode().equals(jumpType)
                && !SsaJumpType.VIDEO_PAGE.getCode().equals(jumpType)
                && !SsaJumpType.H5_DOWNLOAD.getCode().equals(jumpType)){
            return false;
        }

        for (String nonSupportSchema : nonSupportSchemaList){
            if (jumpLink.contains(nonSupportSchema)){
                return false;
            }
        }
        return true;
    }

    public ShareInfoVo getShareInfo(String title, Integer showStyle,
                                    List<SplashScreenBaseImageVo> baseImageVos,
                                    SsaNewSplashScreenVideoVo video)
            throws ServiceException, IOException, SystemException {
        SsaShowStyleType styleType = SsaShowStyleType.getByCode(showStyle);
        ImageBo imageBo;
        if(styleType.isVideo()){
            Assert.notNull(video, "请先上传视频信息");
            ImageBo videoImageBo = videoService.getSsaCoverFromVideo(
                    video.getBiz_id());
            imageBo = imageService.cutAndUpdateImage(videoImageBo.getUrl(),
                    0, 0, CutType.SHARE.getCode());
        }else {
            Assert.notEmpty(baseImageVos, "请先上传基础图片");
            SplashScreenBaseImageVo imageVo = baseImageVos.stream()
                    .filter(t->BaseImageTypeEnum.FULL_SCREEN.getCode().equals(t.getType()))
                    .findFirst().orElse(null);
            Assert.notNull(imageVo, "请先上传全面屏基础图图片");
            imageBo = imageService.cutAndUpdateImage(imageVo.getUrl(),
                    0, 0, CutType.SHARE.getCode());
        }
        String imageHash = imageService.buildHashCode(0, MaterialType.IMAGE,
                imageBo.getUrl(), imageBo.getMd5());
        return ShareInfoVo.builder().share_state(1)
                .share_title(title)
                .share_image_url(imageBo.getUrl())
                .share_image_hash(imageHash).build();
    }

    public ShareInfoVo getShareInfoForUpdate(String title, Integer showStyle,
                                    List<UpdateSplashScreenBaseImageVo> baseImageVos,
                                             SsaUpdateSplashScreenVideoVo video)
            throws ServiceException, IOException, SystemException {
        SsaShowStyleType styleType = SsaShowStyleType.getByCode(showStyle);
        ImageBo imageBo;
        if(styleType.isVideo()){
            Assert.notNull(video, "请先上传视频信息");
            ImageBo videoImageBo = videoService.getSsaCoverFromVideo(
                    video.getBiz_id());
            imageBo = imageService.cutAndUpdateImage(videoImageBo.getUrl(),
                    0, 0, CutType.SHARE.getCode());
        }else {
            Assert.notEmpty(baseImageVos, "请先上传基础图片");
            UpdateSplashScreenBaseImageVo imageVo = baseImageVos.stream()
                    .filter(t->BaseImageTypeEnum.FULL_SCREEN.getCode().equals(t.getType()))
                    .findFirst().orElse(null);
            Assert.notNull(imageVo, "请先上传全面屏基础图图片");
            imageBo = imageService.cutAndUpdateImage(imageVo.getUrl(),
                    0, 0, CutType.SHARE.getCode());
        }
        String imageHash = imageService.buildHashCode(0, MaterialType.IMAGE,
                imageBo.getUrl(), imageBo.getMd5());
        return ShareInfoVo.builder().share_state(1)
                .share_title(title)
                .share_image_url(imageBo.getUrl())
                .share_image_hash(imageHash).build();
    }




}
