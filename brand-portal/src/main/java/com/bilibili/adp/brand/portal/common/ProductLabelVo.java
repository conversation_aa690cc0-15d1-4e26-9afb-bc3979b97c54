package com.bilibili.adp.brand.portal.common;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/12/26 16:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ProductLabelVo implements Serializable {
    private static final long serialVersionUID = -8619466430524446116L;
    private Integer crmFirstCategoryId;
    private Integer crmSecondCategoryId;
    private Long firstProductLabelId;
    private String firstProductLabelName;
    private Long secondProductLabelId;
    private String secondProductLabelName;
}