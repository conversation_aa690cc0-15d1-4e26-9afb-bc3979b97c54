package com.bilibili.adp.brand.portal.webapi.schedule;

import com.bilibili.adp.brand.portal.annotation.RateLimit;
import com.bilibili.adp.brand.portal.convert.schedule.SsaPlusScheduleConvert;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.common.enums.SsaCptOrderModeEnum;
import com.bilibili.brand.api.common.enums.SsaLinkageType;
import com.bilibili.brand.api.common.enums.SsaVideoPlayModeEnum;
import com.bilibili.brand.api.dmp.DmpUserType;
import com.bilibili.brand.api.dmp.IScheduleCrowdPackService;
import com.bilibili.brand.api.dmp.dto.CrowdPackDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.order.service.IAdxOrderService;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.dto.SplitDaysStockDto;
import com.bilibili.brand.api.schedule.dto.StockPriceDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.api.stock.dto.QueryStockDto;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.data.ProphetTargetDao;
import com.bilibili.brand.biz.message.SsaPlusMsgProducer;
import com.bilibili.brand.biz.order.service.GdOrderService;
import com.bilibili.brand.biz.resource.po.ProphetTargetPo;
import com.bilibili.brand.biz.resource.po.ProphetTargetPoExample;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTempDao;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPo;
import com.bilibili.brand.biz.schedule.service.SsaPlusScheduleInvokeService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.ssa.platform.api.order.dto.SsaOrderDto;
import com.bilibili.ssa.platform.api.order.service.ISsaOrderService;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleDto;
import com.bilibili.ssa.platform.api.schedule.service.ISsaCpmScheduleService;
import com.bilibili.ssa.platform.api.schedule.service.ISsaPlusScheduleService;
import com.bilibili.ssa.platform.api.schedule.service.ISsaScheduleService;
import com.bilibili.ssa.platform.api.splash_screen.dto.QuerySplashScreenParamDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaScheduleSplashScreenMappingDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDto;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenSchduleService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.bilibili.ssa.platform.biz.service.schedule.SsaScheduleServiceValidator;
import com.bilibili.ssa.platform.common.enums.SsaScheduleStatus;
import com.bilibili.ssa.platform.common.enums.SystemConfig;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping("/web_api/v1/schedules/ssa/plus")
@Api(value = "/schedules", tags = "新版闪屏排期相关")
public class SsaPlusScheduleController extends BaseController {

    @Autowired
    private SsaPlusScheduleConvert ssaPlusScheduleConvert;

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ISsaPlusScheduleService plusScheduleService;

    @Autowired
    private ISsaCpmScheduleService cpmScheduleService;

    @Autowired
    private IQueryScheduleService queryScheduleService;

    @Autowired
    private GdScheduleTempDao gdScheduleTempDao;

    @Autowired
    private SsaPlusMsgProducer ssaPlusMsgProducer;

    @Autowired
    private SsaPlusScheduleInvokeService invokeService;

    @Autowired
    private IScheduleCrowdPackService crowdPackService;

    @Autowired
    private ISystemConfigService systemConfigService;

    @Autowired
    private ProphetTargetDao targetDao;

    @Value("${brand.env:prod}")
    private String env;
    @Autowired
    private GdOrderService gdOrderService;

    @Autowired
    private IAdxOrderService adxOrderService;

    @Autowired
    private SsaScheduleServiceValidator ssaScheduleServiceValidator;

    @Autowired
    private ISsaOrderService ssaOrderService;

    @Autowired
    private ISsaSplashScreenSchduleService ssaSplashScreenScheduleService;

    @Autowired
    private ISsaSplashScreenService ssaSplashScreenService;

    @Autowired
    private ISsaScheduleService ssaScheduleService;

    @ApiOperation("查询闪屏gd+排期的dealGroupId")
    @RequestMapping(value = "/list/deal_group_id", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<String>> querySsaDealGroupId(@RequestParam("order_id") Integer orderId) {

        List<Long> adxDealGroupIds = queryScheduleService.queryAdxDealGroupIds(orderId);

        return Response.SUCCESS(adxDealGroupIds.stream().map(String::valueOf).distinct().collect(Collectors.toList()));
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 30)
    @ApiOperation("cpt新建时查询库存")
    @RequestMapping(value = "/cpt/stock", method = RequestMethod.POST)
    @ResponseBody
    public Response<SsaCptPlusStockVo> queryStock(
            @ApiIgnore Context context,
            @RequestBody SsaPlusStockTargetInfo stockTargetInfo) {
        SsaScheduleTargetVo target = Objects.isNull(stockTargetInfo.getTarget()) ? new SsaScheduleTargetVo() : stockTargetInfo.getTarget();
        target.setOs(this.ssaScheduleServiceValidator.getDefaultOsTarget(
                target.getOs(),
                stockTargetInfo.getSsaVideoPlayMode(),
                stockTargetInfo.getSsaLinkageType(),
                stockTargetInfo.getButtonStyle()));
        stockTargetInfo.setTarget(target);

        List<Long> dates = getDates(stockTargetInfo);

        ssaPlusScheduleConvert.checkTime(dates);

        GdOrderDto order = gdOrderService.getOrderById(stockTargetInfo.getOrderId());

        String dealSeq = stockTargetInfo.getDealSeq();

        //查询缓存如果命中则直接返回
        if (dealSeq != null) {
            StockPriceDto stockPriceDto = redisService.getValue(dealSeq);
            if (stockPriceDto != null) {
                if (stockPriceDto.getIsSuccess()) {
                    return Response.SUCCESS(SsaPlusScheduleConvert.convert2SsaCptPlusStockVo(stockPriceDto));
                } else {
                    return Response.FAIL(500, stockPriceDto.getErrorMsg());
                }
            }
            return Response.SUCCESS(SsaCptPlusStockVo.builder().isDealFinish(false).dealSeq(dealSeq).build());
        }
        stockTargetInfo.setDealSeq(String.valueOf(snowflakeIdWorker.nextId()));
        //异步处理库存查询
        QueryStockDto stockDto = ssaPlusScheduleConvert.convert2QueryStockDto(stockTargetInfo, context);
        invokeService.queryCptStockAsync(stockDto);

        return Response.SUCCESS(SsaCptPlusStockVo.builder().isDealFinish(false).dealSeq(stockTargetInfo.getDealSeq())
                .build());
    }

    private List<Long> getDates(SsaPlusStockTargetInfo stockTargetInfo) {
        List<Long> dates;
        if (stockTargetInfo.getOrderMode() == SsaCptOrderModeEnum.BY_HOUR.getCode()) {
            dates = stockTargetInfo.getSplitDaysImpressVos().stream()
                    .map(time -> {
                        LocalDate localDate = TimeUtil.isoDateStrToLocalDate(time.getBeginTime());
                        if (localDate != null) {
                            return TimeUtil.toTimestamp(localDate.atStartOfDay()).getTime();
                        } else {
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } else {
            dates = stockTargetInfo.getScheduleDate();
        }
        return dates;


    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 5)
    @ApiOperation(value = "新建闪屏cpt排期")
    @RequestMapping(value = "/add/cpt", method = RequestMethod.POST)
    @ResponseBody
    public Response<Object> createSsaCptPlusSchedule(@ApiIgnore Context context,
                                                     @Valid @RequestBody SsaCptPlusScheduleAddVo vo) {

        SsaScheduleTargetVo target = Objects.isNull(vo.getTarget()) ? new SsaScheduleTargetVo() : vo.getTarget();
        target.setOs(this.ssaScheduleServiceValidator.getDefaultOsTarget(
                target.getOs(),
                vo.getSsaVideoPlayMode(),
                vo.getSsaLinkageType(),
                vo.getButtonStyle()));

        vo.setTarget(target);

        SsaPlusScheduleBo scheduleBo = ssaPlusScheduleConvert.convert2SsaCptPlusScheduleBo(vo, super.getOperator(context));
        ssaScheduleServiceValidator.preValidate(scheduleBo, getOperator(context));
        ssaPlusMsgProducer.productMessage(scheduleBo);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "编辑闪屏+排期")
    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateSsaCptPlusSchedule(@ApiIgnore Context context,
                                              @Valid @RequestBody SsaPlusScheduleUpdateVo vo) {
        SsaPlusScheduleBo scheduleBo = ssaPlusScheduleConvert.convertUpdate2SsaCptPlusScheduleBo(vo);
        ssaScheduleServiceValidator.preValidate(scheduleBo, getOperator(context));
        plusScheduleService.updateSchedule(scheduleBo, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation("闪屏cpt+创意可用时间")
    @RequestMapping(value = "/cpt/available/time", method = RequestMethod.GET)
    @ResponseBody
    public Response<SchedulePlusAvailableTimeVo> scheduleDate(@RequestParam("schedule_id") Integer scheduleId) {

        SsaCpmScheduleDto scheduleDto = cpmScheduleService.getCpmScheduleByScheduleId(scheduleId);

        long begin;
        long end;
        if (scheduleDto.getBeginTime() != null && scheduleDto.getEndTime() != null) {
            begin = scheduleDto.getBeginTime().getTime();
            end = scheduleDto.getEndTime().getTime();
        } else {
            begin = scheduleDto.getBeginDate().getTime();
            end = TimeUtils.getDayEnd(scheduleDto.getEndDate()).getTime();
        }
        return Response.SUCCESS(SchedulePlusAvailableTimeVo.builder()
                .start_time(begin)
                .end_time(end)
                .schedule_date_desc(TimeUtil.timestampToIsoDateStr(scheduleDto.getBeginDate()) + " " +
                        scheduleDto.getRotationNum() + "轮").build());
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 30)
    @ApiOperation(value = "闪屏gd+查询库存")
    @RequestMapping(value = "/gd/stock", method = RequestMethod.POST)
    @ResponseBody
    public Response<SsaGdPlusStockVo> queryStock(@ApiIgnore Context context,
                                                 @RequestBody SsaGdPlusStockTargetInfo vo) {

        SsaScheduleTargetVo target = Objects.isNull(vo.getTarget()) ? new SsaScheduleTargetVo() : vo.getTarget();
        target.setOs(this.ssaScheduleServiceValidator.getDefaultOsTarget(
                target.getOs(),
                vo.getSsaVideoPlayMode(),
                vo.getSsaLinkageType(),
                vo.getButtonStyle()));

        vo.setTarget(target);

        String dealSeq = vo.getDealSeq();
        log.info("闪屏gd+查询库存 by queryStock");
        //查询缓存如果命中则直接返回
        if (dealSeq != null) {
            StockPriceDto stockPriceDto = redisService.getValue(dealSeq);
            if (stockPriceDto != null) {
                if (stockPriceDto.getIsSuccess()) {
                    return Response.SUCCESS(SsaPlusScheduleConvert.convert2SsaGDPlusStockVo(stockPriceDto));
                } else {
                    return Response.FAIL(500, stockPriceDto.getErrorMsg());
                }
            }
            return Response.SUCCESS(SsaGdPlusStockVo.builder().isDealFinish(false).dealSeq(dealSeq).build());
        }
        vo.setDealSeq(String.valueOf(snowflakeIdWorker.nextId()));

        //异步处理库存查询
        QueryStockDto stockDto = ssaPlusScheduleConvert.convertGd2QueryStockDto(vo, context);

        invokeService.queryGdStockAsync(stockDto);

        return Response.SUCCESS(SsaGdPlusStockVo.builder().isDealFinish(false).dealSeq(vo.getDealSeq()).build());
    }


    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 5)
    @ApiOperation(value = "新建闪屏gd排期")
    @RequestMapping(value = "/add/gd", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Void> createSsaGdPlusSchedule(@ApiIgnore Context context,
                                           @Valid @RequestBody SsaGdPlusScheduleAddVo vo) {

        SsaScheduleTargetVo target = Objects.isNull(vo.getTarget()) ? new SsaScheduleTargetVo() : vo.getTarget();
        target.setOs(this.ssaScheduleServiceValidator.getDefaultOsTarget(
                target.getOs(),
                vo.getSsaVideoPlayMode(),
                vo.getSsaLinkageType(),
                vo.getButtonStyle()));

        vo.setTarget(target);

        SsaPlusScheduleBo scheduleBo = ssaPlusScheduleConvert.convert2SsaGdPlusScheduleBo(vo,
                super.getOperator(context));
        ssaScheduleServiceValidator.preValidate(scheduleBo, getOperator(context));

        List<GdScheduleTempPo> tempPos = ssaPlusScheduleConvert.convert2GdScheduleTempPo(scheduleBo,
                super.getOperator(context));
        tempPos.forEach(t -> gdScheduleTempDao.insertUpdateSelective(t));

        ssaPlusMsgProducer.productMessage(scheduleBo);

        return Response.SUCCESS(null);
    }


    @ApiOperation("闪屏gd+创意可用时间")
    @RequestMapping(value = "/gd/available/time", method = RequestMethod.GET)
    @ResponseBody
    public Response<SchedulePlusAvailableTimeVo> scheduleGdDate(@RequestParam("schedule_id") Integer scheduleId)
            throws ServiceException {

        ScheduleDto scheduleDto = queryScheduleService.getScheduleById(scheduleId);

        return Response.SUCCESS(SchedulePlusAvailableTimeVo.builder().start_time(
                        scheduleDto.getBeginDate().getTime())
                .end_time(scheduleDto.getGdEndTime().getTime())
                .schedule_date_desc(TimeUtil.timestampToIsoTimeStr(scheduleDto.getBeginDate())
                        + "~" + TimeUtil.timestampToIsoTimeStr(scheduleDto.getEndDate())).build());
    }

    @ApiOperation(value = "切换闪屏排期")
    @RequestMapping(value = "/change/order", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Void> updateSsaPlusSchedule(@ApiIgnore Context context,
                                         @Valid @RequestBody ScheduleMigrateInfoVo infoVo) throws ServiceException {

        Assert.notNull(infoVo.getSchedule_id(), "排期id不能为空");
        Assert.notNull(infoVo.getOrder_id(), "订单id不能为空");
        plusScheduleService.changeSchedule(infoVo.getSchedule_id(),
                infoVo.getOrder_id(), super.getOperator(context));

        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "闪屏排期批量转移")
    @RequestMapping(value = "/batch/migrate", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Void> batchMigrateSsaPlusSchedule(@ApiIgnore Context context,
                                               @Valid @RequestBody ScheduleMigrateInfoVo vo)
            throws ServiceException {
        plusScheduleService.batchMigrateSsaPlusSchedule(vo.getSchedule_id_list(), vo.getOrder_id(), getOperator(context));
        return Response.SUCCESS();
    }

    @Deprecated
    @ApiOperation("获取人群定向包信息")
    @RequestMapping(value = "/get/crowd_package", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<CrowdPackVo>> getCrowdPackage(Context context)
            throws ServiceException {
        if ("uat".equals(env)) {
            return Response.SUCCESS(Lists.newArrayList(
                    CrowdPackVo.builder().id(16500).name("uat电子行业").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(16531).name("uat美妆行业").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(17531).name("极度敏感人群").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(16520).name("敏感人群1").combined(true).userType(DmpUserType.MID.getCode()).build(),
                    CrowdPackVo.builder().id(24225).name("本地测试创建buvid人群").userType(DmpUserType.BUVID.getCode()).build(),
                    CrowdPackVo.builder().id(24259).name("大兔子测试buvid").userType(DmpUserType.BUVID.getCode()).build()));
        }
        Integer accountId = systemConfigService
                .getValueReturnInt(SystemConfigEnum.SSA_PLUS_CROWD_PACK_ACCOUNT_ID.getCode());
        Integer peopleGroup = systemConfigService
                .getValueReturnInt(SystemConfigEnum.SSA_PLUS_CROWD_PACK_PEOPLE_GROUP.getCode());
        List<CrowdPackDto> crowdPackDtos = crowdPackService
                .getCrowdPackDtosByAccountIdDirectly(accountId);
        if (CollectionUtils.isEmpty(crowdPackDtos)) {
            return Response.SUCCESS(new ArrayList<>());
        }

        ProphetTargetPoExample poExample = new ProphetTargetPoExample();
        poExample.or().andTargetTypeIdEqualTo(TargetType.CROW_PACK.getProphetCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<ProphetTargetPo> targetCrowPack = targetDao.selectByExample(poExample);
        List<Integer> availableCrowPack = targetCrowPack.stream()
                .map(ProphetTargetPo::getTargetValue).collect(Collectors.toList());

        List<Integer> sivtAccountList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_ACCOUNT_LIST.getCode());
        List<Integer> sivtCrowdPackageList = systemConfigService.getValueReturnListInt(SystemConfigEnum.SIVT_BLACK_CROWD_PACKAGE_LIST.getCode());
        boolean isSivtBlackAccount = sivtAccountList.contains(context.getAccountId());

        List<CrowdPackVo> response = crowdPackDtos.stream()
                .filter(t -> peopleGroup.equals(t.getSoaGroupType()))
                .filter(t -> availableCrowPack.contains(t.getId()))
                .map(dto -> CrowdPackVo.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .combined(dto.getIsGroupSet() != null && dto.getIsGroupSet())
                        .excludeSelected(isSivtBlackAccount && sivtCrowdPackageList.contains(dto.getId()))
                        .build())
                .collect(Collectors.toList());
        return Response.SUCCESS(response);
    }

    /**
     * 单独更新BidderSourceId
     * Q:为什么不走update schedule的逻辑？
     * A:update schedule会走审核的逻辑，而BidderSourceId的变更是不需要审核的，因此单独封装接口
     *
     * @return
     * @throws ServiceException
     */
    @ApiOperation("更新BidderSourceId")
    @RequestMapping(value = "/update/bidder_source_id", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> updateBidderSourceId(
            @ApiIgnore Context context,
            @Valid @RequestBody UpdateBidderSourceIdReq req) throws ServiceException {
        this.adxOrderService.updateBidderSourceIdByMappingId(
                Long.valueOf(req.getScheduleId()),
                req.getBidderSourceId().trim(),
                getOperator(context));
        return Response.SUCCESS("success");
    }

    @ApiOperation("创意可用时间")
    @RequestMapping(value = "/schedule/date", method = RequestMethod.GET)
    @ResponseBody
    public Response<SsaOrderScheduleVo> scheduleDate(@ApiIgnore Context context,
                                                     @RequestParam("schedule_id") Integer scheduleId,
                                                     @RequestParam(value = "creative_id", required = false) Integer splashScreenId) {
        SsaOrderDto ssaOrderDto = ssaOrderService.getSsaOrderByGdScheduleId(scheduleId);

        String valueTimeTarget = systemConfigService.getValueByItem(SystemConfig.SSA_CPT_CAN_TIME_TARGET_ACCOUNTS);
        boolean canTimeTarget = GsonUtils.toList(valueTimeTarget, Integer.class).contains(context.getAccountId());

        // 白名单用户可以重复使用排期，所以不查已有的闪屏
        Set<Integer> scheduleSet = canTimeTarget ? Collections.emptySet() :
                ssaSplashScreenScheduleService.getSplashScreenSchduleInSplashScreenIds(
                        ssaSplashScreenService.getSsaSplashScreens(QuerySplashScreenParamDto.builder()
                                .ssaOrderId(ssaOrderDto.getId()).build()
                        ).stream().map(SsaSplashScreenDto::getId).collect(Collectors.toList())
                ).stream().map(SsaScheduleSplashScreenMappingDto::getScheduleId).collect(Collectors.toSet());

        List<SsaScheduleDto> ssaScheduleDetailDtos = ssaScheduleService.getSsaSchedulesBySsaOrderId(ssaOrderDto.getId());
        ssaScheduleDetailDtos = ssaScheduleDetailDtos.stream()
                .filter(s -> SsaScheduleStatus.AUDITED.getCode().equals(s.getStatus())
                        && !scheduleSet.contains(s.getSsaScheduleId())
                        && s.getLaunchDate().getTime() >= Utils.getToday().getTime())
                .collect(Collectors.toList());

        // 当前闪屏已有的日期要拿出来
        Map<Integer, SsaScheduleDto> ssaScheduleDtoMap = Collections.emptyMap();
        if (Utils.isPositive(splashScreenId)) {
            SsaSplashScreenDto splashScreenDto = ssaSplashScreenService.getSsaSplashScreenById(splashScreenId);
            List<SsaScheduleDto> scheduleDtos = Values.defaultIfNull(splashScreenDto.getSsaScheduleDtos(), Collections.emptyList());
            ssaScheduleDtoMap = splashScreenDto.getSsaScheduleDtos().stream()
                    .collect(Collectors.toMap(SsaScheduleDto::getSsaScheduleId,
                            Function.identity(), (a, b) -> a));
            ssaScheduleDetailDtos.addAll(scheduleDtos);
        }

        Map<Integer, SsaScheduleDto> finalSsaScheduleDtoMap = ssaScheduleDtoMap;
        List<SsaScheduleVo> ssaScheduleVos = new ArrayList<>(ssaScheduleDetailDtos.stream()
                .filter(s -> s != null && s.getLaunchDate() != null)
                .sorted(Comparator.comparing(SsaScheduleDto::getLaunchDate))
                .map(dto -> {
                    SsaScheduleDto ssaScheduleDto = finalSsaScheduleDtoMap.get(dto.getSsaScheduleId());
                    SsaScheduleVo scheduleVo = SsaScheduleVo.builder()
                            .ssa_schedule_id(dto.getSsaScheduleId())
                            .schedule_date_desc(Utils.getTimestamp2String(dto.getLaunchDate())
                                    + " " + dto.getRotationNum()
                                    + "/" + dto.getSourceRotationNum() + "轮")
                            .creative_using(ssaScheduleDto != null)
                            .date_available(dto.getLaunchDate().compareTo(Utils.getToday()) >= 0)
                            .build();
                    if (ssaScheduleDto != null && ssaScheduleDto.getSsaStartTime() != null) {
                        scheduleVo.setFrom_hour(ssaScheduleDto.getSsaStartTime().getHours());
                        scheduleVo.setFrom_minute(ssaScheduleDto.getSsaStartTime().getMinutes());
                    } else {
                        scheduleVo.setFrom_hour(0);
                        scheduleVo.setFrom_minute(0);
                    }
                    if (ssaScheduleDto != null && ssaScheduleDto.getSsaEndTime() != null) {
                        scheduleVo.setTo_hour(ssaScheduleDto.getSsaEndTime().getHours());
                        scheduleVo.setTo_minute(ssaScheduleDto.getSsaEndTime().getMinutes());
                    } else {
                        scheduleVo.setTo_hour(23);
                        scheduleVo.setTo_minute(59);
                    }
                    return scheduleVo;
                })
                .collect(Collectors.toMap(SsaScheduleVo::getSsa_schedule_id,
                        Function.identity(), (a, b) -> a,
                        LinkedHashMap::new)).values());

        SsaOrderScheduleVo vo = new SsaOrderScheduleVo();
        vo.setSsa_order_id(ssaOrderDto.getId());
        vo.setSsa_schedules(ssaScheduleVos);
        return Response.SUCCESS(vo);
    }

}
