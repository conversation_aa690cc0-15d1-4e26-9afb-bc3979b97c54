package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.bilibili.adp.brand.portal.config.validator.URLCollection;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/08/06
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "GdCreativeCustomizeMonitoringVo")
public class GdCreativeCustomizeMonitoringVo {

    @ApiModelProperty(notes = "自定义时间参数,单位秒")
    private Integer customize_time;

    @URLCollection(message = "自定义监控链接不合法，请检查你的链接后再试")
    private List<String> urls;


}
