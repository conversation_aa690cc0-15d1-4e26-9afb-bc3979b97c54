package com.bilibili.adp.brand.portal.webapi.booking.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 日预约详细信息
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BookingItemVo {
    @ApiModelProperty("预约ID")
    private Integer booking_id;

    @ApiModelProperty("日期")
    private Long date;

    @ApiModelProperty("预约状态 -1-无效的 0-可预约  1-已预约 2-已锁定 3-预约已排期 4-锁定已排期")
    private Integer booking_status;

    @ApiModelProperty("预约状态描述")
    private String booking_status_desc;

    @ApiModelProperty("所属订单")
    private Integer dependency_order_id;

    @ApiModelProperty("所属订单名称")
    private String dependency_order_name;

    @ApiModelProperty("所属排期")
    private Integer dependency_schedule_id;

    @ApiModelProperty("业务方名称")
    private String business_side_name;

    @ApiModelProperty("是否我的预约（表示该业务方的预约） 0-否 1-是")
    private Integer is_my_booking;

    @ApiModelProperty("排期创意个数")
    private Long creative_count;

    @ApiModelProperty("是否即将释放")
    private boolean releasing;

    @ApiModelProperty("释放时间")
    private String releasing_date;

    @ApiModelProperty("预约操作人")
    private String operator;

    @ApiModelProperty("topViewSourceId")
    private Integer top_view_source_Id;

    @ApiModelProperty("资源类型")
    private Integer resource_type;

    @ApiModelProperty("产品类型")
    private Integer product;

    @ApiModelProperty("是否预约整日,用于直播资源预约 0-是 1-否")
    private Integer isBookingDay;

    @ApiModelProperty("此预约占整个轮数的比例,满轮是1000,半轮是500")
    private Integer booking_ratio;

    @ApiModelProperty("关联id,同一轮的id都是一致的")
    private String related_id;

}
