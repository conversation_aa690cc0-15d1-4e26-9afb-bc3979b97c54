/** 
* <AUTHOR> 
* @date  2018年3月7日
*/ 

package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("每日排期轮播信息")
public class SsaDayRotationInfoVo {

	@ApiModelProperty("日期")
	private Long date;

	@ApiModelProperty("轮数")
	private long rotation_num;
}
