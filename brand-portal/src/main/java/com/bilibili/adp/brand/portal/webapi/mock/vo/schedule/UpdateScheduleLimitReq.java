package com.bilibili.adp.brand.portal.webapi.mock.vo.schedule;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/6
 */
@Data
public class UpdateScheduleLimitReq {

    @FieldDesc(summary = "排期id列表", desc = "需要修改的排期id列表，用英文逗号分隔", required = true)
    private List<Integer> scheduleIds;

    @FieldDesc(summary = "频控", desc = "期望修改成的频控", required = true)
    private Integer limit;

}
