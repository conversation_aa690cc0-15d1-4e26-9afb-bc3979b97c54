package com.bilibili.adp.brand.portal.webapi.material.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022年05月02日
 * @Description 3d——inline视频信息
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VideoCompoundVo {

    //ip视频名称
    private String name;

    //rgb通道视频
    private String rgbVideoUrl;

    //alpha通道视频url
    private String alphaVideoUrl;

    /**
     * 背景视频png序列帧url
     */
    private String bgVideoPngSequencesUrl;

    /**
     * 出框视频png序列帧url
     */
    private String outBoxVideoPngSequencesUrl;

    @ApiModelProperty("ip视屏类型，0：普通ip视频，1：出框ip视频")
    private Integer videoType;
}
