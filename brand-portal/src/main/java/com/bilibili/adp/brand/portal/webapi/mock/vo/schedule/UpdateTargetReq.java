package com.bilibili.adp.brand.portal.webapi.mock.vo.schedule;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/21
 */
@Data
public class UpdateTargetReq {

    @FieldDesc(summary = "排期id", required = true)
    private List<Integer> scheduleIds;

    @FieldDesc(summary = "操作类型", desc = "0或者不填：查询，1：新增定向，2：删除定向")
    private Integer operateType;

    @FieldDesc(summary = "定向类型", desc = "1：地域，2：性别，3：年龄，4：平台")
    private Integer targetType;

    @FieldDesc(summary = "定向名称",desc = "支持模糊填写，操作完成后请务必检查结果")
    private String targetName;
}
