package com.bilibili.adp.brand.portal.webapi.launch.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OpenCreativePreviewVo {

    private Long creativeId;

    private Integer orderProduct;
}
