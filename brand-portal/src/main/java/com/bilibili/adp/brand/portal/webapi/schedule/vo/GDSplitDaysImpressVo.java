package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by xiongyan on 2020/8/05.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("gd分日库存预约信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class GDSplitDaysImpressVo {

    @ApiModelProperty("预约库存")
    private Long impressionCpm;

    @ApiModelProperty("开始时间 yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    @ApiModelProperty("结束时间 yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @ApiModelProperty("日期")
    private String scheduleDate;

    @ApiModelProperty("播放约量")
    private Integer playCount;
}
