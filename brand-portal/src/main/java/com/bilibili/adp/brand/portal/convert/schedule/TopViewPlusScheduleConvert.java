package com.bilibili.adp.brand.portal.convert.schedule;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.PutLocation;
import com.bilibili.brand.api.common.enums.SsaDisplayModeEnum;
import com.bilibili.brand.api.resource.system.ISystemConfigService;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.api.stock.dto.QueryStockDto;
import com.bilibili.brand.api.stock.dto.ssa.StockTagGroupDto;
import com.bilibili.brand.api.schedule.dto.StockPriceDto;
import com.bilibili.brand.biz.schedule.handler.ScheduleTargetHandler;
import com.bilibili.brand.common.Constant;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.common.CptConstants;
import com.bilibili.cpt.platform.common.SplitDaysFlagEnum;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.ssa.platform.api.schedule.dto.SplitDaysImpressBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;
import com.bilibili.ssa.platform.api.schedule.dto.TopViewPlusScheduleBo;
import com.bilibili.ssa.platform.common.enums.*;
import com.bilibili.utils.NumberUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mysema.commons.lang.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.ssa.platform.biz.service.schedule.TopViewPlusScheduleValidator.DEFAULT_FREQUENCY_LIMIT;


/**
 * @Description ssa排期转换工具类
 * <AUTHOR>
 * @Date 2020.05.26 18:17
 */
@Component
public class TopViewPlusScheduleConvert {

    @Autowired
    private ISystemConfigService configService;

    @Autowired
    private ScheduleTargetHandler targetHandler;

    /**
     * TopView本质上还是依赖闪屏，因此引用了SsaPlusScheduleConvert作为delegate，复用部分闪屏逻辑
     */
    @Autowired
    private SsaPlusScheduleConvert ssaPlusScheduleConvert;

    private TopViewPlusScheduleConvert() {
    }

    public SsaPlusScheduleBo convert2SsaCptPlusScheduleBo(TopViewCptPlusScheduleVo vo) {
        SsaPlusScheduleBo scheduleBo = new SsaPlusScheduleBo();
        BeanUtils.copyProperties(vo, scheduleBo);
        scheduleBo.setClickArea(vo.getMaterialClickArea());
        scheduleBo.setSalesType(SalesType.TOP_VIEW_CPT.getCode());
        scheduleBo.setFrequencyLimit(configService.getValueReturnInt(SystemConfigEnum
                .SSA_PLUS_DEFAULT_FREQUENCY_LIMIT.getCode()));
        scheduleBo.setImpressBos(vo.getSplitDaysImpressVos().stream()
                .map(t -> {
                    SplitDaysImpressBo impressBo = new SplitDaysImpressBo();
                    BeanUtils.copyProperties(t, impressBo);
                    return impressBo;
                }).collect(Collectors.toList()));
        if (SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())) {
            scheduleBo.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }
        return scheduleBo;
    }

    public SsaPlusScheduleBo convertUpdate2SsaCptPlusScheduleBo(TopViewScheduleUpdateVo vo) {
        SsaPlusScheduleBo scheduleBo = new SsaPlusScheduleBo();
        scheduleBo.setClickArea(vo.getMaterialClickArea());
        BeanUtils.copyProperties(vo, scheduleBo);
        if (SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())) {
            scheduleBo.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }
        return scheduleBo;
    }

    public static TopViewPlusStockVo convert2TopViewPlusStockVo(StockPriceDto priceDto) {
        return TopViewPlusStockVo.builder()
                .isDealFinish(priceDto.getIsDealFinish())
                .dealSeq(priceDto.getDealSeq())
                .splitDaysStocks(priceDto.getSplitDaysStocks()
                        .stream()
                        .map(t -> SplitDaysStockVo.builder()
                                .stock_cpm(t.getStockCpm())
                                .schedule_date(t.getScheduleDate())
                                .rotation_num(t.getRotationNum())
                                .begin_time(t.getBeginTime())
                                .end_time(t.getEndTime())
                                .build())
                        .collect(Collectors.toList()))
                .build();
    }

    public QueryStockDto convert2QueryStockDto(TopViewCptPlusStockTargetInfo stockTargetInfo, Context context) {
        //校验闪屏刊例周期
        this.ssaPlusScheduleConvert.checkTime(stockTargetInfo.getScheduleDate());
        List<Timestamp> dates = stockTargetInfo.getScheduleDate()
                .stream()
                .map(t -> TimeUtils.getBeginOfDay(new Timestamp(t)))
                .collect(Collectors.toList());
        Integer dealNumsLimit = configService.getValueReturnInt(SystemConfigEnum.SSA_PLUS_DEAL_DAY_NUM_LIMIT.getCode());
        Assert.isTrue(dates.size() <= dealNumsLimit, "查询或新增的最大天数不能超过" + dealNumsLimit);
        List<TargetRule> targetRules = this.ssaPlusScheduleConvert.buildTargetRules(
                Objects.isNull(stockTargetInfo.getTarget())
                        ? SsaScheduleTargetDto.builder().build() : stockTargetInfo.getTarget().toDto());
        this.appendTargetRuleIfNecessary(targetRules, stockTargetInfo);
        List<StockTagGroupDto> groupDtos = targetHandler.getSsaGDTagGroupDtoS(targetRules);
        return QueryStockDto.builder()
                .requestId(stockTargetInfo.getDealSeq())
                .accountId(context.getAccountId())
                .putLocation(PutLocation.SLOT_GROUP.getCode())
                .commonScheduleDate(Sets.newTreeSet(dates))
                .splitScheduleDate(Sets.newTreeSet())
                .scheduleDate(Sets.newTreeSet(dates))
                .splitDaysFlag(SplitDaysFlagEnum.OPEN.getCode())
                .frequencyLimit(configService.getValueReturnInt(SystemConfigEnum
                        .SSA_PLUS_DEFAULT_FREQUENCY_LIMIT.getCode()))
                .sourceIds(CptConstants.SSA_SOURCE_IDS)
                .excludeCrowdPackIds(stockTargetInfo.getExcludeCrowdPackIds())
                .crowdPackIds(stockTargetInfo.getCrowdPackIds())
                .targetRules(targetRules)
                .groupDtos(groupDtos)
                .orderId(stockTargetInfo.getOrderId())
                .timeInfo(dates.stream()
                        .map(date -> Pair.of(date, TimeUtil.toTimestamp(TimeUtil.endOfDay(date.toLocalDateTime()))))
                        .collect(Collectors.toList()))
                .supportsMock(true)
                .build();
    }

    public QueryStockDto convert2QueryStockDto(TopViewGdPlusStockTargetInfo stockTargetInfo, Context context) {
        //即使存在当天排期，SplitDaysImpressVos中的数据也是正常的
        Set<Timestamp> dates = stockTargetInfo.getSplitDaysImpressVos().stream()
                .map(dayVo -> TimeUtil.isoStrToTimestamp(dayVo.getBeginTime()))
                .collect(Collectors.toSet());

        this.ssaPlusScheduleConvert.checkTime(dates.stream()
                .map(date -> Utils.getBeginOfDay(date).getTime())
                .collect(Collectors.toList()));

        List<Pair<Timestamp, Timestamp>> timeInfo = new ArrayList<>();
        stockTargetInfo.getSplitDaysImpressVos()
                .forEach(t -> timeInfo.add(Pair.of(
                        TimeUtil.isoTimeStr2Timestamp(t.getBeginTime()),
                        TimeUtil.isoTimeStr2Timestamp(t.getEndTime()))));

        Map<Pair<Timestamp, Timestamp>, List<StockTagGroupDto>> tagGroupMap = new HashMap<>();
        List<TargetRule> targetRules = this.ssaPlusScheduleConvert.buildTargetRules(stockTargetInfo.getTarget().toDto());

        this.appendTargetRuleIfNecessary(targetRules, stockTargetInfo);

        timeInfo.forEach(t -> {
            List<Integer> hours = new ArrayList<>();
            for (int i = t.getFirst().toLocalDateTime().getHour();
                 i < t.getSecond().toLocalDateTime().getHour() + 1; i++) {
                hours.add(i);
            }
            if (hours.size() != 24) {
                targetRules.add(TargetRule.builder()
                        .ruleType(TargetType.HOUR.getCode())
                        .valueIds(hours).build());
            }
            List<StockTagGroupDto> groupDtos = targetHandler.getSsaGDTagGroupDtoS(targetRules);
            tagGroupMap.put(t, groupDtos);
        });
        Integer frequencyLimit = stockTargetInfo.getFrequencyLimit();
        return QueryStockDto.builder()
                .requestId(stockTargetInfo.getDealSeq())
                .accountId(context.getAccountId())
                .putLocation(PutLocation.SLOT_GROUP.getCode())
                .commonScheduleDate(Sets.newTreeSet(dates))
                .splitScheduleDate(Sets.newTreeSet())
                .scheduleDate(Sets.newTreeSet(dates))
                .splitDaysFlag(SplitDaysFlagEnum.OPEN.getCode())
                .frequencyLimit(Objects.nonNull(frequencyLimit) ? frequencyLimit : 4)
                .targetRules(targetRules)
                .sourceIds(stockTargetInfo.getTarget().getSource_ids())
                .hour(stockTargetInfo.getHour())
                .isTodaySchedule(stockTargetInfo.getIsTodaySchedule())
                .timeInfo(timeInfo)
                .excludeCrowdPackIds(stockTargetInfo.getExcludeCrowdPackIds())
                .crowdPackIds(stockTargetInfo.getCrowdPackIds())
                .tagGroupMap(tagGroupMap)
                .orderId(stockTargetInfo.getOrderId())
                .supportsMock(true)
                .build();
    }

    public void appendTargetRuleIfNecessary(List<TargetRule> targetRules, TopViewPlusStockTargetInfo stockTargetInfo) {
        TopViewSellingType sellingType = null;
        if (Objects.nonNull(stockTargetInfo.getSellingType())) {
            sellingType = TopViewSellingType.getByCode(stockTargetInfo.getSellingType());
        }
        if (Objects.isNull(sellingType)) {
            sellingType = TopViewSellingType.COMMON;
        }

        if (TopViewSellingType.FIRST_BRUSH == sellingType) {
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.SSA_SALES_TYPE.getCode())
                    .valueIds(Lists.newArrayList(SsaDisplayModeEnum.FIRST_BRUSH.getProphetCode()))
                    .build());
        }

        if (!CollectionUtils.isEmpty(stockTargetInfo.getCrowdPackIds())) {
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.CROW_PACK.getCode())
                    .valueIds(stockTargetInfo.getCrowdPackIds())
                    .build());
        }
        appendOsTargetRuleIfNecessary(targetRules);
    }

    public void appendTargetRuleIfNecessary(List<TargetRule> targetRules, TopViewPlusScheduleVo topViewPlusScheduleVo) {
        TopViewSellingType sellingType = null;
        if (Objects.nonNull(topViewPlusScheduleVo.getSellingType())) {
            sellingType = TopViewSellingType.getByCode(topViewPlusScheduleVo.getSellingType());
        }
        if (Objects.isNull(sellingType)) {
            sellingType = TopViewSellingType.COMMON;
        }

        if (TopViewSellingType.FIRST_BRUSH == sellingType) {
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.SSA_SALES_TYPE.getCode())
                    .valueIds(Lists.newArrayList(SsaDisplayModeEnum.FIRST_BRUSH.getProphetCode()))
                    .build());
        }

        if (!CollectionUtils.isEmpty(topViewPlusScheduleVo.getCrowdPackIds())) {
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.CROW_PACK.getCode())
                    .valueIds(topViewPlusScheduleVo.getCrowdPackIds())
                    .build());
        }
        appendOsTargetRuleIfNecessary(targetRules);
    }

    private void appendOsTargetRuleIfNecessary(List<TargetRule> targetRules) {
        //TopView 只有iPhone和安卓，如果选择通投，此时会占据OTT、Pad的量，因此锁死设备定向为iPhone和安卓
        boolean hasOs = targetRules.stream().anyMatch(rule -> rule.getRuleType() == TargetType.OS.getCode());
        if (!hasOs) {
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.OS.getCode())
                    .valueIds(Lists.newArrayList(Constant.MOBILE_PLATFORM_TARGET))
                    .build());
        }
    }

    public TopViewPlusScheduleBo convert2TopViewPlusScheduleBo(TopViewCptPlusScheduleVo vo) {
        TopViewPlusScheduleBo result = this.doConvert2TopViewPlusScheduleBo(vo, ConvertContext.builder()
                .salesType(SalesType.TOP_VIEW_PLUS)
                .orderProduct(OrderProduct.TOP_VIEW_CPT_PLUS)
                .build());
        return result;
    }

    public TopViewPlusScheduleBo convert2TopViewPlusScheduleBo(TopViewGdPlusScheduleVo vo) {
        TopViewPlusScheduleBo result = this.doConvert2TopViewPlusScheduleBo(vo, ConvertContext.builder()
                .salesType(SalesType.TOP_VIEW_GD_PLUS)
                .orderProduct(OrderProduct.TOP_VIEW_GD_PLUS)
                .build());
        //append special properties
        SsaPlusScheduleBo ssaScheduleBo = result.getSsaScheduleBo();
        ssaScheduleBo.setTodaySchedule(vo.is_today_schedule());
        return result;
    }

    public TopViewPlusScheduleBo convert2TopViewPlusScheduleBo(TopViewCptPlusScheduleUpdateVo vo) {
        TopViewPlusScheduleBo result = this.doConvert2TopViewPlusScheduleBo(vo, ConvertContext.builder()
                .salesType(SalesType.TOP_VIEW_PLUS)
                .orderProduct(OrderProduct.TOP_VIEW_CPT_PLUS)
                .build());
        SsaPlusScheduleBo ssaScheduleBo = result.getSsaScheduleBo();
        SplitDaysImpressBo impressBo = ssaScheduleBo.getImpressBos().get(0);
        ssaScheduleBo.setLaunchDate(TimeUtils.getBeginOfDay(TimeUtil.isoTimeStr2Timestamp(impressBo.getBeginTime())));
        return result;
    }

    public TopViewPlusScheduleBo convert2TopViewPlusScheduleBo(TopViewGdPlusScheduleUpdateVo vo) {
        TopViewPlusScheduleBo result = this.doConvert2TopViewPlusScheduleBo(vo, ConvertContext.builder()
                .salesType(SalesType.TOP_VIEW_GD_PLUS)
                .orderProduct(OrderProduct.TOP_VIEW_GD_PLUS)
                .build());
        SsaPlusScheduleBo ssaScheduleBo = result.getSsaScheduleBo();
        SplitDaysImpressBo impressBo = ssaScheduleBo.getImpressBos().get(0);
        ssaScheduleBo.setLaunchDate(TimeUtils.getBeginOfDay(TimeUtil.isoTimeStr2Timestamp(impressBo.getBeginTime())));
        //处理分时
        //TOP_VIEW_GD支持分时包段
        LocalDateTime beginTime = Objects.requireNonNull(TimeUtil.isoTimeStr2Timestamp(impressBo.getBeginTime())).toLocalDateTime();
        LocalDateTime endTime = Objects.requireNonNull(TimeUtil.isoTimeStr2Timestamp(impressBo.getEndTime())).toLocalDateTime();
        Assert.isTrue(Objects.equals(beginTime.toLocalDate(), endTime.toLocalDate()), "开始时间和结束时间必须为同一天");
        if (endTime.getHour() - beginTime.getHour() != 23) {
            List<TargetRule> targetRules = ssaScheduleBo.getTargetRules();
            SsaScheduleTargetDto targetDto = ssaScheduleBo.getTargetDto();
            //有分时
            List<Integer> hours = NumberUtil.toList(beginTime.getHour(), endTime.getHour(), 1);
            //增加分时定向
            targetDto.setHour(hours);
            targetRules.add(TargetRule.builder()
                    .ruleType(TargetType.HOUR.getCode())
                    .valueIds(hours)
                    .build());
        }
        return result;
    }

    private TopViewPlusScheduleBo doConvert2TopViewPlusScheduleBo(TopViewPlusScheduleVo vo, ConvertContext context) {
        SsaPlusScheduleBo scheduleBo = new SsaPlusScheduleBo();
        BeanUtils.copyProperties(vo, scheduleBo);
        scheduleBo.setClickArea(vo.getMaterialClickArea());
        scheduleBo.setSalesType(context.getSalesType().getCode());
        scheduleBo.setOrderProduct(context.getOrderProduct().getCode());
        scheduleBo.setTransitionMode(vo.getTransitionMode());
//        scheduleBo.setFrequencyLimit(configService.getValueReturnInt(SystemConfigEnum.SSA_PLUS_DEFAULT_FREQUENCY_LIMIT.getCode()));
        Integer frequencyLimit = vo.getFrequencyLimit();
        scheduleBo.setFrequencyLimit(Objects.nonNull(frequencyLimit) ? frequencyLimit : DEFAULT_FREQUENCY_LIMIT);
        Optional.ofNullable(vo.getSplitDaysImpressVos()).ifPresent(imps ->
                scheduleBo.setImpressBos(vo.getSplitDaysImpressVos().stream()
                        .map(t -> {
                            SplitDaysImpressBo impressBo = new SplitDaysImpressBo();
                            BeanUtils.copyProperties(t, impressBo);
                            if (StringUtils.isEmpty(impressBo.getScheduleDate())) {
                                impressBo.setScheduleDate(TimeUtil.timestampToIsoDateStr(
                                        TimeUtil.isoTimeStr2Timestamp(impressBo.getBeginTime())));
                            }
                            return impressBo;
                        }).collect(Collectors.toList())));
        if (SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())) {
            scheduleBo.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }

        //分时的定向暂时不计算，因为它和具体的日期有关系，因此延迟到创建/编辑排期的时候计算
        List<TargetRule> targetRules = this.ssaPlusScheduleConvert.buildTargetRules(
                Objects.isNull(vo.getTarget()) ? SsaScheduleTargetDto.builder().build() : vo.getTarget().toDto());
        this.appendTargetRuleIfNecessary(targetRules, vo);
        scheduleBo.setTargetRules(targetRules);
        scheduleBo.setTargetDto(vo.getTarget().toDto());

        TopViewSellingType sellingType = TopViewSellingType.getByCode(vo.getSellingType());
        if (Objects.isNull(sellingType)) {
            sellingType = TopViewSellingType.COMMON;
        }
        TopViewPlusScheduleBo topViewPlusScheduleBo = new TopViewPlusScheduleBo();
        topViewPlusScheduleBo.setSellingType(sellingType);
        topViewPlusScheduleBo.setSsaScheduleBo(scheduleBo);
        return topViewPlusScheduleBo;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ConvertContext {
        private SalesType salesType;
        private OrderProduct orderProduct;
    }
}
