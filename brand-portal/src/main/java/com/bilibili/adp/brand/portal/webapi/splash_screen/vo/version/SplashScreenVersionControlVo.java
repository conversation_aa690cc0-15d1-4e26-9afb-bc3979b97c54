package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenVersionControlVo {

    /**
     * 平台ID: 1-iPhone 2-Android 3-iPad
     */
    @ApiModelProperty(value = "平台ID: 1-iPhone 2-Android 3-iPad")
    private Integer platform_type;

    /**
     * 平台ID: 1-iPhone 2-Android 3-iPad
     */
    @ApiModelProperty(value = "平台ID: 1-iPhone 2-Android 3-iPad")
    private String platform_type_desc;


    /**
     *  比较类型
     */
    @ApiModelProperty(value = "比较类型: 1-大于 2-等于 3-小于")
    private Integer compare_type;


    /**
     *  版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

}