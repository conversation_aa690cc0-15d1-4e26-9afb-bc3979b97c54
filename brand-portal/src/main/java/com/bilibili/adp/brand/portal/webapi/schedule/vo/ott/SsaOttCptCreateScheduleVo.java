/** 
* <AUTHOR> 
* @date  2018年3月7日
*/ 

package com.bilibili.adp.brand.portal.webapi.schedule.vo.ott;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.inventory.CptInventoryDetailVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.inventory.SsaOttTargetVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel("OTT_CPT排期信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SsaOttCptCreateScheduleVo extends SsaOttBaseScheduleVo {

	private List<CptInventoryDetailVo> reserveInventoryDetails;

	@JsonProperty("target")
	private SsaOttTargetVo targets;
}
