package com.bilibili.adp.brand.portal.service.stat;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.bilibili.adp.brand.portal.common.GroupType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.bilibili.adp.brand.portal.webapi.statistic.vo.AdvertiserDataVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.platform.report.api.dto.StatAccountDto;
import com.bilibili.brand.platform.report.api.service.IStatAccountService;

/**
 * Created by fanwenbin on 16/9/29.
 */
@Service
public class WebStatAccountService extends WebStatBaseService<AdvertiserDataVo> {

    @Autowired
    private IStatAccountService accountService;

    @Override
    protected List<AdvertiserDataVo> groupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return this.convertVosToDtos(accountService.getByAccountIdGroupByTime(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<AdvertiserDataVo> groupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return this.convertVosToDtos(accountService.getByAccountIdGroupByDay(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<AdvertiserDataVo> groupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return this.convertVosToDtos(accountService.getByAccountIdGroupByWeek(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<AdvertiserDataVo> groupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, Integer product) throws ServiceException {
        return this.convertVosToDtos(accountService.getByAccountIdGroupByMonth(accountId, fromTime, toTime, salesType));
    }

    @Override
    protected List<AdvertiserDataVo> groupByGroupTypeAndOrderIdList(Integer accountId, Timestamp fromTime, Timestamp toTime,
                                                  Integer salesType, GroupType groupType,
                                                                    List<Integer> orderIdList, Integer product) {
        return null;
    }

    private List<AdvertiserDataVo> convertVosToDtos(List<StatAccountDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        List<AdvertiserDataVo> result = new ArrayList<>(dtos.size());
        result.addAll(dtos.stream().map(dto -> AdvertiserDataVo.builder()
                .show_count(dto.getShowAccount())
                .cost_per_click(dto.getCostPerClick())
                .cost(dto.getCost())
                .average_cost_per_thousand(dto.getAverageCostPerThousand())
                .click_rate(dto.getClickRate())
                .click_count(dto.getClickCount())
                .date(dto.getDate())
                .build()).collect(Collectors.toList()));
        return result;
    }


}
