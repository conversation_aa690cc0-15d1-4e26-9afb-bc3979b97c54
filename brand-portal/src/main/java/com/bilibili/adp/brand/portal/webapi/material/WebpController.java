package com.bilibili.adp.brand.portal.webapi.material;

import com.bilibili.adp.brand.portal.convert.webp.WebpControllerConverter;
import com.bilibili.adp.brand.portal.webapi.material.vo.WebpProcessResultVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.material.IWebpService;
import com.bilibili.brand.api.material.bo.WebpProcessResultBo;
import com.bilibili.brand.api.webp.WebpGenerateRequestDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Objects;

@RestController
@RequestMapping("/web_api/v1/webp")
@Api(value = "/webp", description = "webp相关")
public class WebpController {

    @Autowired
    private IWebpService webpService;

    @PostMapping("/generate")
    @ApiOperation(value = "合成webp")
    public Response<WebpProcessResultVo> generateWebp(@RequestParam(name = "file", required = false) MultipartFile file,
                                                      @RequestParam(name = "deal_seq", required = false) String dealSeq,
                                                      @RequestParam(name = "enable_mixed", required = false) Boolean enableMixed,
                                                      @RequestParam(name = "quality_ratio", required = false) Integer qualityRatio,
                                                      @RequestParam(name = "frame_duration", required = false) Integer frameDuration,
                                                      @RequestParam(name = "frame_rate", required = false) Integer frameRate
    ) throws IOException {
        WebpGenerateRequestDto webpGenerateRequestDto = WebpControllerConverter.MAPPER.convertToWebpGenerateRequestDto(dealSeq, enableMixed, qualityRatio, frameDuration, frameRate);
        if (Objects.isNull(webpGenerateRequestDto)) {
            webpGenerateRequestDto = new WebpGenerateRequestDto();
        }
        WebpProcessResultBo webpProcessResultBo = webpService.generateWebpByImagesZip(file, webpGenerateRequestDto);
        WebpProcessResultVo webpProcessResultVo = WebpControllerConverter.MAPPER.convertToWebpProcessResultVo(webpProcessResultBo);
        return Response.SUCCESS(webpProcessResultVo);
    }

    @PostMapping("/upload")
    @ApiOperation(value = "上传webp")
    public Response<WebpProcessResultVo> uploadWebp(@RequestParam(name = "file") MultipartFile multipartFile) throws IOException, ServiceException {
        WebpProcessResultBo webpProcessResultBo = webpService.uploadWebpDirectly(multipartFile);
        WebpProcessResultVo webpProcessResultVo = WebpControllerConverter.MAPPER.convertToWebpProcessResultVo(webpProcessResultBo);
        return Response.SUCCESS(webpProcessResultVo);
    }
}