package com.bilibili.adp.brand.portal.webapi.mock.vo.ssa;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/14
 */
@Data
@Builder
public class SsaTwistButtonRes {

    @FieldDesc(summary = "闪屏id")
    private Integer splashScreenId;

    @FieldDesc(summary = "按钮对应平台")
    private String platform;

    @FieldDesc(summary = "调整以后的角度")
    private Float angel;

    @FieldDesc(summary = "调整以后的回返角度")
    private Float reverseAngle;

    @FieldDesc(summary = "调整以后的加速度")
    private Float speed;
}
