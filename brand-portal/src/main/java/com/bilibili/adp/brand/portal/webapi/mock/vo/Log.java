package com.bilibili.adp.brand.portal.webapi.mock.vo;

import com.bilibili.adp.brand.portal.webapi.mock.annotations.FieldDesc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/8/14 21:52
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Log {
    @FieldDesc(summary = "日志Id")
    private Long logId;
    @FieldDesc(summary = "业务Id")
    private Long objId;
    @FieldDesc(summary = "操作内容")
    private String value;
    @FieldDesc(summary = "操作人")
    private String operator;
    @FieldDesc(summary = "操作时间")
    private String ctime;
}
