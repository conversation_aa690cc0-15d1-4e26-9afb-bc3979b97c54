/**
 * <AUTHOR>
 * @date 2018年3月7日
 */

package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewScheduledRotationVo {

    @ApiModelProperty("投放时间")
    private Timestamp launch_date;

    @ApiModelProperty("投放轮数")
    private Integer rotation_num;

    @ApiModelProperty("此预约占整个轮数的比例,满轮是1000,半轮是500")
    private Integer booking_ratio;
}
