package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view;

import com.bilibili.adp.brand.portal.common.ProductLabelVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ImageVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ManuscriptInfoVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.MiniProgramVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenCustomizedVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenMiddlePageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenDynamicButtonVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version.SplashScreenVersionControlVo;
import com.bilibili.cpt.platform.portal.webapi.creative.vo.TemplateVo;
import com.bilibili.enums.GdJumpType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewCreativeDetailVo {

    @ApiModelProperty(value = "topView ID")
    private Integer top_view_id;

    @ApiModelProperty("创意名称")
    private String creative_name;

    @ApiModelProperty(value = "订单ID")
    private Integer order_id;

    @ApiModelProperty(value = "订单名称")
    private String order_name;

    @ApiModelProperty(value = "排期ID")
    private Integer schedule_id;

    @ApiModelProperty(value = "排期名称")
    private String schedule_name;

    @ApiModelProperty(value = "1-待审核 2-已驳回 3-待上线 4-上线中 5-已暂停 6-已完成 7-已删除")
    private Integer status;

    @ApiModelProperty(value = "状态描述")
    private String status_desc;

    @ApiModelProperty(value = "驳回原因")
    private String reason;

    @ApiModelProperty(value = "广告角标ID")
    private Integer cm_mark;

    @ApiModelProperty(value = "广告角标名称")
    private String cm_mark_name;

    @ApiModelProperty("时间定向: 0-禁用，1-启用")
    private Integer time_target;

    @ApiModelProperty("排期日期")
    private List<ScheduledRotationVo> schedule_dates;

    @ApiModelProperty("创建人名称")
    private String creator_name;

    @ApiModelProperty(value = "跳转类型: 1-链接 2-视频 3-游戏 4-番剧 5-直播 6-游戏中心")
    private Integer jump_type;

    @ApiModelProperty(value = "分享开关 0-关闭 1-打开")
    private Integer share_state;

    @ApiModelProperty(value = "分享标题")
    private String share_title;

    @ApiModelProperty(value = "分享副标题")
    private String share_sub_title;

    @ApiModelProperty(value = "分享图片URL")
    private String share_image_url;

    @ApiModelProperty(value = "分享图片hash")
    private String share_image_hash;

    @ApiModelProperty(value = "售卖类型：41-闪屏cpt")
    private Integer sales_type;

    @ApiModelProperty(value = "分端开关 0-开启 1-关闭")
    private Integer platform_switch;

    // 闪屏
    @ApiModelProperty("闪屏创意id")
    private Integer splash_screen_id;

    @ApiModelProperty(value = "闪屏标题")
    private String ssa_title;

    @ApiModelProperty(value = "闪屏视频")
    private TopViewVideoVo ssa_video;

    @ApiModelProperty(value = "彩蛋视频")
    private TopViewVideoVo egg_video;

    @ApiModelProperty("闪屏背景图片")
    private List<SplashScreenBaseImageVo> ssa_base_image_list;

    @ApiModelProperty(value = "是否可跳过: 0-否 1-是")
    private Integer ssa_is_skip;

    @ApiModelProperty(value = "下发时间: 1-过审下发 2-排期开始前2小时下发")
    private Integer ssa_issued_time;

    @ApiModelProperty("素材加密: 0-禁用，1-启用")
    private Integer ssa_encryption;

    @ApiModelProperty(value = "闪屏文案")
    private String ssa_copy_writing;

    //以下字段用于闪屏分端开关关闭的时候
    private SplashScreenJumpVo jump_vo;

    private List<SplashScreenDynamicButtonVo> dynamic_button_vos;

    //选择式按钮纯文字按钮唤起文案
    private String extra_scheme_copywriting;

    //3.选择式按钮纯文字按钮跳转文案")
    private String extra_guide_instructions;

    //互动闪屏是否支持按钮
    private Boolean is_support_button_to_interact;

    @ApiModelProperty("闪屏按钮颜色 0-默认白色 1-黑色")
    private Integer text_color_style;



    /**
     * 闪屏分端监控模型列表
     */
    @ApiModelProperty(value = "闪屏分端监控模型列表")
    private List<SplashScreenCustomizedVo> splash_screen_customized_vos;

    //以下字段用于闪屏分端开关关闭的时候
    @ApiModelProperty(value = "跳转链接")
    private String jump_link;

    @ApiModelProperty(value = "实际跳转链接")
    private String actual_jump_link;

    @ApiModelProperty(value = "是否唤起应用 1：否 2：是")
    private Integer is_call_app;

    @ApiModelProperty(value = "唤起url")
    private String scheme_url;

    @ApiModelProperty(value = "唤起文案")
    private String scheme_copywriting;

    @ApiModelProperty(value = "ios包名")
    private String ios_package_name;

    @ApiModelProperty(value = "android包名")
    private String android_package_name;

    @ApiModelProperty(value = "点击监控链接列表")
    private List<String> customized_click_url_list;

    @ApiModelProperty(value = "展示监控链接列表")
    private List<String> customized_imp_url_list;

    @ApiModelProperty(value = "中间页点击监控链接列表")
    private List<String> customized_middle_page_click_url_list;

    @ApiModelProperty(value = "自动进入中间页监控链接列表（参考友商，自动进入中间页算点击的 from 柠糕）")
    private List<String> customized_open_middle_page_click_url_list;

    @ApiModelProperty(value = "是否支持自定义引导文案 0-否 1-是")
    private Integer support_custom_guide;

    /**
     * 闪屏跳转模型列表
     */
    @ApiModelProperty(value = "闪屏分端跳转模型列表")
    private List<SplashScreenJumpVo> splash_screen_jump_vos;

    //新版首焦
    @ApiModelProperty("新版首焦创意id列表")
    private List<Long> new_hf_creative_ids;

    @ApiModelProperty("新版首焦模板ID")
    private Integer new_hf_template_id;

    @ApiModelProperty("新版首焦模板")
    private TemplateVo new_hf_template;

    @ApiModelProperty("新版首焦标题")
    private String new_hf_title;

    @ApiModelProperty(notes = "新版首焦图片")
    private List<ImageVo> new_hf_images;

    @ApiModelProperty(notes = "新版首焦视频")
    private TopViewVideoVo new_hf_video;

    @ApiModelProperty("是否自定义新版首焦品牌信息")
    private Integer is_customized_new_hf_brand_info;

    @ApiModelProperty("新版首焦品牌名称")
    private String new_hf_brand_name;

    @ApiModelProperty("新版首焦品牌头像url")
    private String new_hf_face_url;

    @ApiModelProperty("新版首焦品牌头像md5")
    private String new_hf_face_md5;

    @ApiModelProperty("新版首焦推广目的页")
    private String new_hf_promotion_purpose_content;

    /**
     * {@link GdJumpType}
     */
    @ApiModelProperty("新版首焦跳转类型")
    private Integer new_hf_jump_type;

    @ApiModelProperty("android首焦展示监控链接")
    private String hf_android_customized_imp_url;

    @ApiModelProperty("android首焦点击监控链接")
    private String hf_android_customized_click_url;

    @ApiModelProperty("首焦监控链接是否需要IDFA加密（默认0，0：不加密，1：加密）")
    private Integer hf_is_idfa_encrypted;

    @ApiModelProperty("ios首焦展示监控链接")
    private String hf_ios_customized_imp_url;

    @ApiModelProperty("ios首焦点击监控链接")
    private String hf_ios_customized_click_url;

    //以下字段用于闪屏分端开关关闭的时候
    @ApiModelProperty("首焦展示监控链接")
    private String hf_customized_imp_url;

    @ApiModelProperty("首焦点击监控链接")
    private String hf_customized_click_url;

    @ApiModelProperty("topView首焦ip视频id")
    private Integer hf_ip_video_id;

    @ApiModelProperty("topView首焦ip视频url")
    private String hf_ip_video_url;

    @ApiModelProperty("topView首焦稿件信息")
    private ManuscriptInfoVo manuscript_info;

    @ApiModelProperty("产品型号")
    private ProductLabelVo product_label;

    @ApiModelProperty("小程序")
    private MiniProgramVo mini_program;

    @ApiModelProperty("应用包信息")
    private List<Integer> app_package_ids;

    /**
     * 闪屏版本控制
     */
    @ApiModelProperty(value = "闪屏版本控制")
    private List<SplashScreenVersionControlVo> version_control;

    /**
     * 是否使用默认版本号: true-是 false-否
     */
    @ApiModelProperty(value = "是否使用默认版本号: true-是 false-否")
    private Boolean use_default_version;

    @ApiModelProperty("交互说明文案")
    private String interact_instructions;

    @ApiModelProperty("中间页")
    private SplashScreenMiddlePageVo middle_page;

    @ApiModelProperty("android首焦点击监控链接")
    private List<String> hf_android_customized_click_url_list;

    @ApiModelProperty("ios首焦点击监控链接")
    private List<String> hf_ios_customized_click_url_list;

    @ApiModelProperty("首焦点击监控链接列表")
    private List<String> hf_customized_click_url_list;

    @ApiModelProperty("是否启用直播间预约")
    private Boolean is_enable_live_booking;

    @ApiModelProperty("直播预约id")
    private Long live_booking_id;

    @ApiModelProperty("闪屏部分选择的唤起类型，注意：可能和排期是不一致的！！！")
    private Integer wake_app_type;

    @ApiModelProperty("是否自定义首焦跳转信息")
    private Integer is_customized_hf_jump;

    @ApiModelProperty("首焦跳转信息相关")
    private TopViewHfJumpVo hf_jump;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("排期时间信息")
    public static class ScheduledRotationVo {

        @ApiModelProperty("排期日期id")
        private Integer id;

        @ApiModelProperty(value = "投放开始时间")
        private String begin_time;

        @ApiModelProperty(value = "投放结束小时")
        private String end_time;

        @ApiModelProperty("轮数")
        private int rotation_num;

        @ApiModelProperty("目标展现量")
        private Integer impression;

        @ApiModelProperty("描述")
        private String schedule_date_desc;
    }
}
