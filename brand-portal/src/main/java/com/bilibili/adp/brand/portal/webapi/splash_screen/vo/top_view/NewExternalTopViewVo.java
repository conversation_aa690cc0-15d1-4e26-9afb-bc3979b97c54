package com.bilibili.adp.brand.portal.webapi.splash_screen.vo.top_view;

import com.bilibili.adp.brand.portal.common.ProductLabelVo;
import com.bilibili.adp.brand.portal.config.validator.URLCollection;
import com.bilibili.adp.brand.portal.webapi.launch.vo.ManuscriptInfoVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.MiniProgramVo;
import com.bilibili.adp.brand.portal.webapi.resource.vo.MgkVideoVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenCustomizedVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenJumpVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.SplashScreenMiddlePageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenBaseImageVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.image.SplashScreenDynamicButtonVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.schedule.SsaScheduleSelectVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.version.NewSplashScreenVersionControlVo;
import com.bilibili.adp.brand.portal.webapi.splash_screen.vo.video.SsaNewSplashScreenVideoVo;
import com.bilibili.enums.GdJumpType;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class NewExternalTopViewVo {

    @ApiModelProperty(value = "是否支持自定义引导文案 0-否 1-是")
    private Integer supportCustomGuide;

    /**
     * 闪屏动效按钮
     */
    @ApiModelProperty("闪屏动效按钮")
    private List<SplashScreenDynamicButtonVo> dynamicButtonVos;

    @ApiModelProperty("按钮纯文字按钮唤起文案")
    private String extraSchemeCopywriting;

    @ApiModelProperty("按钮纯文字按钮跳转文案")
    private String extraGuideInstructions;

    @ApiModelProperty("排期ID")
    private Integer scheduleId;

    @ApiModelProperty(value = "排期ID（批量创建创意时使用）")
    private List<Integer> scheduleIdList;

    @ApiModelProperty("创意名称")
    private String creativeName;

    @ApiModelProperty("时间定向: 0-禁用，1-启用")
    private Integer timeTarget;

    @ApiModelProperty("选择的排期")
    private List<SsaScheduleSelectVo> schedules;

    @ApiModelProperty(value = "广告角标(1:广告，2:推广)")
    private Integer cmMark;

    @ApiModelProperty(value = "分享开关 0-关闭 1-打开")
    private Integer shareState;

    @ApiModelProperty(value = "分享标题")
    private String shareTitle;

    @ApiModelProperty(value = "分享副标题")
    private String shareSubTitle;

    @ApiModelProperty(value = "分享图片URL")
    private String shareImageUrl;

    @ApiModelProperty(value = "分享图片hash")
    private String shareImageHash;

    /**
     * 跳转类型
     */
    @ApiModelProperty(value = "跳转类型: 1-链接 2-视频 3-番剧 4-直播 5-游戏中心")
    private Integer jumpType;

    @ApiModelProperty(value = "交互方式 0-点击交互 1-手势交互")
    private Integer interactStyle;

    @ApiModelProperty("闪屏分端跳转模型列表")
    private List<SplashScreenJumpVo> splashScreenJumpVos;

    @Valid
    @ApiModelProperty(value = "闪屏分端监控模型列表")
    private List<SplashScreenCustomizedVo> splashScreenCustomizedVos;

    @ApiModelProperty(value = "分端开关 0-分端 1-不分端")
    private Integer platformSwitch;

    //以下字段用于闪屏分端开关关闭的时候
    private List<SplashScreenJumpVo> jumpVos;

    @ApiModelProperty(value = "ios包名")
    private String iosPackageName;

    @ApiModelProperty(value = "android包名")
    private String androidPackageName;

    @ApiModelProperty(value = "点击监控链接列表")
    @URLCollection(message = "自定义点击监控链接列表不合法，请检查你的链接后再试")
    private List<String> customizedClickUrlList;

    @ApiModelProperty(value = "展示监控链接列表")
    @URLCollection(message = "自定义展示监控链接列表不合法，请检查你的链接后再试")
    private List<String> customizedImpUrlList;

    @ApiModelProperty(value = "中间页点击监控链接列表")
    private List<String> customizedMiddlePageClickUrlList;

    @ApiModelProperty(value = "自动进入中间页监控链接列表（参考友商，自动进入中间页算点击的 from 柠糕）")
    private List<String> customizedOpenMiddlePageClickUrlList;

    // 闪屏
    @ApiModelProperty(value = "是否可跳过: 0-否 1-是")
    private Integer ssaIsSkip;

    @ApiModelProperty(value = "下发时间: 1-过审下发 2-排期开始前2小时下发")
    private Integer ssaIssuedTime;

    @ApiModelProperty("闪屏视频")
    private SsaNewSplashScreenVideoVo ssaVideo;

    @ApiModelProperty("彩蛋视频")
    private MgkVideoVo eggVideo;

    @ApiModelProperty("过渡视频")
    private TransitionVideoVo transitionVideo;

    @ApiModelProperty("闪屏背景图片")
    private List<SplashScreenBaseImageVo> ssaBaseImageList;

    @ApiModelProperty("素材加密: 0-禁用，1-启用")
    private Integer ssaEncryption;

    @ApiModelProperty(value = "闪屏文案")
    private String ssaCopyWriting;

    //互动闪屏是否支持按钮
    private Boolean isSupportButtonToInteract;

    //闪屏按钮文字颜色 0-默认 1-黑色
    private Integer textColorStyle;


    // 新版首焦
    @ApiModelProperty("新版首焦推广目的页")
    private String newHfPromotionPurposeContent;

    /**
     * {@link GdJumpType}
     */
    @ApiModelProperty("新版首焦跳转类型")
    private Integer newHfJumpType;

    @ApiModelProperty("新版首焦模板ID")
    private Integer newHfTemplateId;

    @ApiModelProperty("新版首焦标题")
    private String newHfTitle;

    @ApiModelProperty("新版首焦大图URL")
    private String newHfImageUrl;

    @ApiModelProperty("新版首焦大图Hash")
    private String newHfImageHash;

    @ApiModelProperty("新版首焦大图跳转URL")
    private String newHfImageJumpUrl;
    /**
     * 新版首焦图片跳转类型
     * {@link GdJumpType}
     */
    @ApiModelProperty("新版首焦图片跳转类型")
    private Integer newHfImageJumpType;

    @ApiModelProperty("新版首焦小图URL")
    private String newHfExtImageUrl;

    @ApiModelProperty("新版首焦小图Hash")
    private String newHfExtImageHash;

    @ApiModelProperty("新版首焦视频")
    private SsaNewSplashScreenVideoVo newHfVideo;

    @ApiModelProperty("是否自定义首焦品牌信息")
    private Integer isCustomizedNewHfBrandInfo;

    @ApiModelProperty("新版首焦品牌名称")
    private String newHfBrandName;

    @ApiModelProperty("新版首焦品牌头像url")
    private String newHfFaceUrl;

    @ApiModelProperty("新版首焦品牌头像md5")
    private String newHfFaceMd5;

    //首焦公用信息
    @ApiModelProperty("android首焦展示监控链接")
    private String hfAndroidCustomizedImpUrl;

    @ApiModelProperty("android首焦点击监控链接")
    private String hfAndroidCustomizedClickUrl;

    @ApiModelProperty("首焦监控链接是否需要IDFA加密（默认0，0：不加密，1：加密）")
    private Integer hfIsIdfaEncrypted;

    @ApiModelProperty("ios首焦展示监控链接")
    private String hfIosCustomizedImpUrl;

    @ApiModelProperty("ios首焦点击监控链接")
    private String hfIosCustomizedClickUrl;

    //以下字段用于分端开关关闭的时候
    @ApiModelProperty("首焦展示监控链接")
    private String hfCustomizedImpUrl;

    @ApiModelProperty("首焦点击监控链接")
    private String hfCustomizedClickUrl;

    @ApiModelProperty("topView首焦ip视频id")
    private Integer hfIpVideoId;

    @ApiModelProperty("首焦稿件信息")
    private ManuscriptInfoVo manuscriptInfo;

    @ApiModelProperty("产品型号")
    private ProductLabelVo productLabel;

    @ApiModelProperty("小程序信息")
    private MiniProgramVo miniProgram;
    @ApiModelProperty("应用包id")
    private List<Integer> appPackageIds;

    /**
     * 是否使用默认版本号: true-是 false-否
     */
    @ApiModelProperty(value = "是否使用默认版本号: true-是 false-否")
    private Boolean useDefaultVersion;

    /**
     * 闪屏版本控制
     */
    @ApiModelProperty(value = "闪屏版本控制")
    private List<NewSplashScreenVersionControlVo> versionControl;

    @ApiModelProperty("交互说明文案")
    private String interactInstructions;

    @ApiModelProperty("中间页")
    private SplashScreenMiddlePageVo middlePage;

    @ApiModelProperty("android首焦点击监控链接")
    private List<String> hfAndroidCustomizedClickUrlList;

    @ApiModelProperty("ios首焦点击监控链接")
    private List<String> hfIosCustomizedClickUrlList;

    @ApiModelProperty("首焦点击监控链接列表")
    private List<String> hfCustomizedClickUrlList;

    @ApiModelProperty("是否启用直播间预约")
    private Boolean isEnableLiveBooking;

    @ApiModelProperty("直播预约id")
    private Long liveBookingId;

    @ApiModelProperty("是否自定义首焦跳转信息")
    private Integer isCustomizedHfJump;

    //闪屏实际唤起类型，主要方便前端切换tab
    //如果排期层级选择了无唤起，则该值应该也是无唤起
    //如果排期层级选择了唤起应用，则该值应该是唤起应用
    //如果排期层级选择了唤起小程序，则该值应该是唤起小程序
    //如果排期层级选择了唤起应用或小程序，则该值可能是唤起应用也可能是唤起小程序
    @ApiModelProperty("闪屏实际唤起类型")
    private Integer wakeAppType;

    @ApiModelProperty("首焦跳转链接信息")
    private TopViewHfJumpVo hfJump;
}
