package com.bilibili.adp.brand.portal.webapi.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.brand.portal.annotation.RateLimit;
import com.bilibili.adp.brand.portal.convert.schedule.SsaPlusScheduleConvert;
import com.bilibili.adp.brand.portal.convert.schedule.TopViewPlusScheduleConvert;
import com.bilibili.adp.brand.portal.webapi.common.convert.CreativeConverter;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.CptTemplateVo;
import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.creative.service.ITopViewCreativeService;
import com.bilibili.brand.api.stock.dto.QueryStockDto;
import com.bilibili.brand.api.schedule.dto.StockPriceDto;
import com.bilibili.brand.biz.cache.service.GdScheduleRedisService;
import com.bilibili.brand.biz.message.TopViewPlusMsgProducer;
import com.bilibili.brand.biz.schedule.dao.GdScheduleTempDao;
import com.bilibili.brand.biz.schedule.po.GdScheduleTempPo;
import com.bilibili.brand.biz.schedule.service.TopViewScheduleStockService;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.api.creative.dto.CptTemplateDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.TopViewPlusScheduleBo;
import com.bilibili.ssa.platform.api.schedule.service.ISsaCpmScheduleService;
import com.bilibili.ssa.platform.api.schedule.service.ITopViewPlusScheduleService;
import com.bilibili.ssa.platform.biz.service.schedule.TopViewPlusScheduleValidator;
import com.google.protobuf.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

@Slf4j
@Controller
@RequestMapping("/web_api/v1/schedules/top_view/plus")
@Api(value = "/schedules/top_view", description = "TopView排期相关")
public class TopViewPlusScheduleController extends BaseController {

    @Autowired
    private ISsaCpmScheduleService cpmScheduleService;

    @Autowired
    private SsaPlusScheduleConvert ssaPlusScheduleConvert;

    @Autowired
    private TopViewScheduleStockService topViewScheduleStockService;

    @Autowired
    private GdScheduleRedisService redisService;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private TopViewPlusScheduleConvert topViewPlusScheduleConvert;

    @Autowired
    private ITopViewPlusScheduleService scheduleService;

    @Autowired
    private GdScheduleTempDao gdScheduleTempDao;

    @Autowired
    private TopViewPlusMsgProducer topViewPlusMsgProducer;
    @Autowired
    private TopViewPlusScheduleValidator topViewPlusScheduleValidator;
    @Autowired
    private ITopViewCreativeService topViewCreativeService;
    @Autowired
    private CreativeConverter creativeConverter;

    @ApiOperation(value = "查询TopView-CPT可预约时间")
    @RequestMapping(value = "/booked/time", method = RequestMethod.POST)
    @ResponseBody
    public Response<SsaCptPlusStockVo> getRemainedTopViewBooking(
            @ApiIgnore Context context,
            @RequestBody SsaPlusStockTargetInfo stockTargetInfo) throws ServiceException {
        //获取处理序列
        String dealSeq = stockTargetInfo.getDealSeq();

        //查询缓存如果命中则直接返回
        if (dealSeq != null) {
            StockPriceDto stockPriceDto = redisService.getValue(dealSeq);
            if (stockPriceDto != null) {
                if (stockPriceDto.getIsSuccess()) {
                    return Response.SUCCESS(SsaPlusScheduleConvert.convert2SsaCptPlusStockVo(stockPriceDto));
                }
                throw new ServiceException(stockPriceDto.getErrorMsg());
            }
            return Response.SUCCESS(SsaCptPlusStockVo.builder().isDealFinish(false).dealSeq(dealSeq).build());
        } else {
            dealSeq = String.valueOf(snowflakeIdWorker.nextId());
        }

        //异步处理库存查询
        QueryStockDto stockDto = ssaPlusScheduleConvert.convert2QueryStockDto(stockTargetInfo, context);
        topViewScheduleStockService.queryStockAsync(stockDto, dealSeq, super.getOperator(context));

        return Response.SUCCESS(SsaCptPlusStockVo.builder().isDealFinish(false).dealSeq(dealSeq).build());
    }

    @ApiOperation("创意可用时间")
    @RequestMapping(value = "/date", method = RequestMethod.GET)
    @ResponseBody
    public Response<SchedulePlusAvailableTimeVo> scheduleDate(
            @ApiIgnore Context context,
            @RequestParam("schedule_id") Integer scheduleId) {

        SsaCpmScheduleDto scheduleDto = cpmScheduleService.getCpmScheduleByScheduleId(scheduleId);
        SalesType salesType = SalesType.getByCode(scheduleDto.getSalesType());
        String scheduleDateDesc = "";
        if (Objects.equals(salesType, SalesType.TOP_VIEW_GD_PLUS)) {
            scheduleDateDesc = String.format("%s ~ %s %d%s", TimeUtil.timestampToIsoTimeStr(scheduleDto.getBeginTime()),
                    TimeUtil.timestampToIsoTimeStr(scheduleDto.getEndTime()), scheduleDto.getTotalImpression(), "cpm");
        } else {
            scheduleDateDesc = String.format("%s %d%s", TimeUtil.timestampToIsoDateStr(scheduleDto.getBeginDate()),
                    scheduleDto.getRotationNum(), "轮");
        }

        return Response.SUCCESS(SchedulePlusAvailableTimeVo.builder().start_time(scheduleDto.getBeginTime().getTime())
                .end_time(scheduleDto.getEndTime().getTime())
                .schedule_date_desc(scheduleDateDesc).build());
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 30)
    @ApiOperation(value = "查询TopView-CPT+可预约轮数")
    @RequestMapping(value = "/cpt/stock", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<TopViewPlusStockVo> queryTopViewCptStock(@ApiIgnore Context context,
                                                             @RequestBody TopViewCptPlusStockTargetInfo stockTargetInfo)
            throws ServiceException {
        log.info("[TopViewPlusScheduleController] queryTopViewCptStock，request:{}", JSONObject.toJSONString(stockTargetInfo));
        if (Objects.nonNull(stockTargetInfo.getDealSeq())) {
            StockPriceDto stockPriceDto = redisService.getValue(stockTargetInfo.getDealSeq());
            if (Objects.nonNull(stockPriceDto)) {
                if (stockPriceDto.getIsSuccess()) {
                    return Response.SUCCESS(TopViewPlusScheduleConvert.convert2TopViewPlusStockVo(stockPriceDto));
                }
                log.warn("Failed to query stock for TopView-CPT, dealSeq:{}, targetInfo:{}",
                        stockTargetInfo.getDealSeq(), JSON.toJSONString(stockTargetInfo));
                throw new ServiceException(stockPriceDto.getErrorMsg());
            }
            return Response.SUCCESS(TopViewPlusStockVo.builder()
                    .isDealFinish(false)
                    .dealSeq(stockTargetInfo.getDealSeq())
                    .build());
        }
        stockTargetInfo.setDealSeq(String.valueOf(snowflakeIdWorker.nextId()));
        topViewScheduleStockService.queryTopViewCptStock(
                topViewPlusScheduleConvert.convert2QueryStockDto(stockTargetInfo, context), super.getOperator(context));
        return Response.SUCCESS(TopViewPlusStockVo.builder()
                .isDealFinish(false)
                .dealSeq(stockTargetInfo.getDealSeq())
                .build());
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 30)
    @ApiOperation(value = "查询TopView-Gd+可预约轮数")
    @RequestMapping(value = "/gd/stock", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public Response<TopViewPlusStockVo> queryTopViewGdStock(@ApiIgnore Context context,
                                                            @RequestBody TopViewGdPlusStockTargetInfo stockTargetInfo)
            throws ServiceException {
        log.info("[TopViewPlusScheduleController] queryTopViewGdStock，request:{}", JSONObject.toJSONString(stockTargetInfo));
        if (Objects.nonNull(stockTargetInfo.getDealSeq())) {
            StockPriceDto stockPriceDto = redisService.getValue(stockTargetInfo.getDealSeq());
            if (Objects.nonNull(stockPriceDto)) {
                if (stockPriceDto.getIsSuccess()) {
                    return Response.SUCCESS(TopViewPlusScheduleConvert.convert2TopViewPlusStockVo(stockPriceDto));
                }
                log.warn("Failed to query stock for TopView-GD, dealSeq:{}, targetInfo:{}",
                        stockTargetInfo.getDealSeq(), JSON.toJSONString(stockTargetInfo));
                throw new ServiceException(stockPriceDto.getErrorMsg());
            }
            return Response.SUCCESS(TopViewPlusStockVo.builder()
                    .isDealFinish(false)
                    .dealSeq(stockTargetInfo.getDealSeq())
                    .build());
        }
        stockTargetInfo.setDealSeq(String.valueOf(snowflakeIdWorker.nextId()));
        topViewScheduleStockService.queryTopViewGdStock(
                topViewPlusScheduleConvert.convert2QueryStockDto(stockTargetInfo, context),
                super.getOperator(context));
        return Response.SUCCESS(TopViewPlusStockVo.builder()
                .isDealFinish(false)
                .dealSeq(stockTargetInfo.getDealSeq())
                .build());
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 5)
    @ApiOperation(value = "新建TopView-CPT+排期")
    @RequestMapping(value = "/cpt/add", method = RequestMethod.POST)
    @ResponseBody
    public Response<Object> createTopViewCptPlusSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody TopViewCptPlusScheduleVo vo) throws Exception {
        log.info("[TopViewPlusScheduleController] createTopViewCptPlusSchedule，request:{}", JSONObject.toJSONString(vo));
        Operator operator = super.getOperator(context);
        TopViewPlusScheduleBo topViewPlusScheduleBo = topViewPlusScheduleConvert.convert2TopViewPlusScheduleBo(vo);
        topViewPlusScheduleBo.getSsaScheduleBo().setOperator(operator);
        this.topViewPlusScheduleValidator.validate(topViewPlusScheduleBo);
        List<GdScheduleTempPo> tempPos = ssaPlusScheduleConvert.convert2CptScheduleTempPo(
                topViewPlusScheduleBo.getSsaScheduleBo(), operator);
        gdScheduleTempDao.insertBatch(tempPos);
        topViewPlusMsgProducer.produce(topViewPlusScheduleBo);
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "编辑TopView-CPT+排期")
    @RequestMapping(value = "/cpt/update", method = {RequestMethod.POST, RequestMethod.PUT})
    @ResponseBody
    public Response<Object> updateTopViewCptPlusSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody TopViewCptPlusScheduleUpdateVo vo) throws Exception {
        log.info("[TopViewPlusScheduleController] updateTopViewCptPlusSchedule，request:{}", JSONObject.toJSONString(vo));
        TopViewPlusScheduleBo topViewPlusScheduleBo = topViewPlusScheduleConvert.convert2TopViewPlusScheduleBo(vo);
        topViewPlusScheduleBo.getSsaScheduleBo().setOperator(super.getOperator(context));
        this.topViewPlusScheduleValidator.validate(topViewPlusScheduleBo);
        scheduleService.updateTopViewCptPlusSchedule(topViewPlusScheduleBo);
        return Response.SUCCESS(null);
    }

    @RateLimit(mode = RateLimit.Mode.MINUTE, limit = 5)
    @ApiOperation(value = "新建TopView-GD+排期")
    @RequestMapping(value = "/gd/add", method = RequestMethod.POST)
    @ResponseBody
    public Response<Object> createTopViewGdPlusSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody TopViewGdPlusScheduleVo vo) throws Exception {
        log.info("[TopViewPlusScheduleController] createTopViewGdPlusSchedule，request:{}", JSONObject.toJSONString(vo));
        Operator operator = super.getOperator(context);
        TopViewPlusScheduleBo topViewPlusScheduleBo = topViewPlusScheduleConvert.convert2TopViewPlusScheduleBo(vo);
        topViewPlusScheduleBo.getSsaScheduleBo().setOperator(operator);
        this.topViewPlusScheduleValidator.validate(topViewPlusScheduleBo);
        List<GdScheduleTempPo> tempPos = ssaPlusScheduleConvert.convert2GdScheduleTempPo(
                topViewPlusScheduleBo.getSsaScheduleBo(), operator);
        gdScheduleTempDao.insertBatch(tempPos);
        topViewPlusMsgProducer.produce(topViewPlusScheduleBo);
        return Response.SUCCESS(true);
    }

    @ApiOperation(value = "编辑TopView-GD+排期")
    @RequestMapping(value = "/gd/update", method = {RequestMethod.POST, RequestMethod.PUT})
    @ResponseBody
    public Response<Object> updateTopViewGdPlusSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody TopViewGdPlusScheduleUpdateVo vo) throws Exception {
        log.info("[TopViewPlusScheduleController] updateTopViewGdPlusSchedule，request:{}", JSONObject.toJSONString(vo));
        TopViewPlusScheduleBo topViewPlusScheduleBo = topViewPlusScheduleConvert.convert2TopViewPlusScheduleBo(vo);
        topViewPlusScheduleBo.getSsaScheduleBo().setOperator(super.getOperator(context));
        this.topViewPlusScheduleValidator.validate(topViewPlusScheduleBo);
        scheduleService.updateTopViewGdPlusSchedule(topViewPlusScheduleBo);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "TopView+排期迁移")
    @RequestMapping(value = "/migrate", method = RequestMethod.POST)
    @ResponseBody
    public Response<Object> migrateTopViewSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody ScheduleMigrateInfoVo vo) throws Exception {
        log.info("[TopViewPlusScheduleController] migrateTopViewPlusSchedule，request:{}", JSONObject.toJSONString(vo));
        scheduleService.migrateTopViewSchedule(vo.getSchedule_id(), vo.getOrder_id(), getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "TopView+排期批量迁移")
    @RequestMapping(value = "/batch/migrate", method = RequestMethod.POST)
    @ResponseBody
    public Response<Void> batchMigrateTopViewSchedule(
            @ApiIgnore Context context,
            @Valid @RequestBody ScheduleMigrateInfoVo vo) throws Exception {
        scheduleService.batchMigrateTopViewSchedule(vo.getSchedule_id_list(), vo.getOrder_id(), getOperator(context));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "创意可用模板")
    @RequestMapping(value = "/templates", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<CptTemplateVo>> getTemplateList(
            @ApiIgnore Context context,
            @ApiParam("排期ID") @RequestParam(value = "schedule_id") Integer scheduleId) {
        List<CptTemplateDto> dtoList = topViewCreativeService.getTemplateListByScheduleId(scheduleId, getOperator(context));
        return Response.SUCCESS(creativeConverter.convertToCptTemplateVoList(dtoList));
    }
}
