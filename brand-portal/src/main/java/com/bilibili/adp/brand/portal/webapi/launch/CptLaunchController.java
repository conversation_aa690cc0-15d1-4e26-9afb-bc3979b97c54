package com.bilibili.adp.brand.portal.webapi.launch;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.cpt.platform.api.creative.dto.QueryCreativeTemplateParamDto;
import com.bilibili.cpt.platform.biz.enumerate.TemplateJumpType;
import com.bilibili.adp.brand.portal.convert.TimeDealConvert;
import com.bilibili.adp.brand.portal.convert.creative.GdCreativeConvert;
import com.bilibili.adp.brand.portal.webapi.common.convert.CreativeConverter;
import com.bilibili.adp.brand.portal.webapi.launch.vo.*;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.CptTemplateVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.NewCptCreativeVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.PeriodVo;
import com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.UpdateCptCreativeVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.common.enums.*;
import com.bilibili.brand.api.material.IImageService;
import com.bilibili.brand.api.material.IVideoService;
import com.bilibili.brand.api.material.bo.ImageBo;
import com.bilibili.cpt.platform.api.creative.dto.CptTemplateDto;
import com.bilibili.cpt.platform.api.creative.dto.NewMultiScheduleCptCreativeDto;
import com.bilibili.cpt.platform.api.creative.dto.PeriodDto;
import com.bilibili.cpt.platform.api.creative.service.ICptCreativeService;
import com.bilibili.cpt.platform.common.*;
import com.bilibili.enums.GdJumpType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/web_api/v1/cpt/launch")
@Api(value = "/launch", description = "cpt投放相关")
public class CptLaunchController extends BaseController {

    @Autowired
    private ICptCreativeService cptCreativeService;

    @Autowired
    private CreativeConverter creativeConverter;

    @Autowired
    private GdCreativeConvert gdCreativeConvert;

    @Autowired
    private IImageService imageService;

    @Autowired
    private IVideoService videoService;

    @ApiOperation(value = "批量新建cpt创意")
    @RequestMapping(value = "/cpt/multi", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Long> multiCreateCreative(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "creative data")
            @Valid @RequestBody NewCptCreativeVo newCreativeVo) throws ServiceException {
        cptCreativeService.createForMultiSchedule(super.getOperator(context), this.multiCreativeVo2Dto(newCreativeVo));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "编辑cpt创意")
    @RequestMapping(value = "/cpt", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateCreative(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "creative data")
            @Valid @RequestBody UpdateCptCreativeVo updateCreativeVo) {
        cptCreativeService.update(super.getOperator(context), GdCreativeConvert.updateCreativeVo2Dto(updateCreativeVo));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "【cpt】根据排期ID查询可用创意模板")
    @RequestMapping(value = "/templates/cpt", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<CptTemplateVo>> getTemplateList(
            @ApiIgnore Context context,
            @ApiParam("排期ID") @RequestParam(value = "schedule_id", required = false) Integer scheduleId,
            @ApiParam("排期ID列表") @RequestParam(value = "schedule_ids", required = false) String scheduleIds,
            @ApiParam("推广标志") @RequestParam(value = "bus_mark_id", required = false) Integer cmMark
    ) {
        Assert.hasText(scheduleIds,"排期id不能为空");

        List<CptTemplateDto> templateDtoList = cptCreativeService.getTemplates(super.getOperator(context),
                QueryCreativeTemplateParamDto.builder()
                        .busMarkId(cmMark)
                        .scheduleIdList(Utils.stringToList(scheduleIds, Integer::parseInt))
                        .build());
        return Response.SUCCESS(creativeConverter.convertToCptTemplateVoList(templateDtoList));
    }

    @ApiOperation(value = "根据排期ID查询可选时间段")
    @RequestMapping(value = "/dates/cpt", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<PeriodVo>> getDateList(
            @ApiIgnore Context context,
            @ApiParam("排期ID") @RequestParam(value = "schedule_id", required = false) Integer scheduleId,
            @ApiParam("排期ID列表") @RequestParam(value = "schedule_ids", required = false) String scheduleIds,
            @ApiParam("创意ID") @RequestParam(value = "creative_id", required = false, defaultValue = "0")
            String creativeId) {
        List<PeriodDto> periodDtoList;
        if (scheduleId != null) {
            // 单个排期
            periodDtoList = cptCreativeService.getOptionalPeriods(super.getOperator(context), scheduleId, Long.valueOf(creativeId));
        } else {
            // 多个排期
            periodDtoList = cptCreativeService.getMultiScheduleOptionalPeriods(super.getOperator(context), Utils.stringToList(scheduleIds, Integer::parseInt), SalesType.CPT.getCode());
        }

        return Response.SUCCESS(TimeDealConvert.periodDtoS2Vos4CPT(periodDtoList));
    }


    private NewMultiScheduleCptCreativeDto multiCreativeVo2Dto(NewCptCreativeVo vo) {
        return NewMultiScheduleCptCreativeDto.builder()
                .scheduleIdList(vo.getSchedule_ids())
                .newCptCreativeDto(GdCreativeConvert.newCreativeVo2Dto(vo))
                .build();
    }

    @ApiOperation(value = "新建cpt创意查询分享信息")
    @RequestMapping(value = "/create/get/share", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<ShareInfoVo> createGetShare(
            @ApiParam(required = true, value = "creative data")
            @RequestBody NewCptCreativeVo creativeVo) throws SystemException,
            ServiceException, IOException {

        if (!gdCreativeConvert.isSupportShare(creativeVo.getJump_type(),
                creativeVo.getPromotion_purpose_content())) {
            return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
        }

        if (!StringUtils.isEmpty(creativeVo.getImage_url())) {
            ImageBo imageBo = imageService.cutAndUpdateImage(creativeVo.getImage_url(), 0,
                    0, CutType.SHARE.getCode());
            String imageHash = imageService.buildHashCode(creativeVo.getTemplate_id(),
                    MaterialType.IMAGE, imageBo.getUrl(), imageBo.getMd5());
            return Response.SUCCESS(ShareInfoVo.builder().share_state(1)
                    .share_title(creativeVo.getTitle()).share_sub_title(creativeVo.getDescription())
                    .share_image_url(imageBo.getUrl()).share_image_hash(imageHash).build());
        }
        if (!StringUtils.isEmpty(creativeVo.getVideo_url())) {
            ImageBo videoImageBo = videoService.getCoverFromVideo(creativeVo.getVideo_url());
            ImageBo imageBo = imageService.cutAndUpdateImage(videoImageBo.getUrl(), 0,
                    0, CutType.SHARE.getCode());
            String imageHash = imageService.buildHashCode(creativeVo.getTemplate_id(),
                    MaterialType.IMAGE, imageBo.getUrl(), imageBo.getMd5());
            return Response.SUCCESS(ShareInfoVo.builder().share_state(1)
                    .share_title(creativeVo.getTitle())
                    .share_sub_title(creativeVo.getDescription())
                    .share_image_url(imageBo.getUrl()).share_image_hash(imageHash).build());

        }

        return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
    }

    @ApiOperation(value = "编辑cpt创意查询分享信息")
    @RequestMapping(value = "/update/get/share", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<ShareInfoVo> updateGetShare(
            @ApiIgnore Context context,
            @ApiParam(required = true, value = "creative data")
            @RequestBody UpdateCptCreativeVo creativeVo) throws SystemException, ServiceException, IOException {
        if (!gdCreativeConvert.isSupportShare(creativeVo.getJump_type(),
                creativeVo.getPromotion_purpose_content())) {
            return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
        }

        if (!StringUtils.isEmpty(creativeVo.getImage_url())) {
            ImageBo imageBo = imageService.cutAndUpdateImage(creativeVo.getImage_url(), 0,
                    0, CutType.SHARE.getCode());
            String imageHash = imageService.buildHashCode(creativeVo.getTemplate_id(),
                    MaterialType.IMAGE, imageBo.getUrl(), imageBo.getMd5());
            return Response.SUCCESS(ShareInfoVo.builder().share_state(1)
                    .share_title(creativeVo.getTitle()).share_sub_title(creativeVo.getDescription())
                    .share_image_url(imageBo.getUrl()).share_image_hash(imageHash).build());
        }
        if (!StringUtils.isEmpty(creativeVo.getVideo_url())) {
            ImageBo videoImageBo = videoService.getCoverFromVideo(creativeVo.getVideo_url());
            ImageBo imageBo = imageService.cutAndUpdateImage(videoImageBo.getUrl(), 0,
                    0, CutType.SHARE.getCode());
            String imageHash = imageService.buildHashCode(creativeVo.getTemplate_id(),
                    MaterialType.IMAGE, imageBo.getUrl(), imageBo.getMd5());
            return Response.SUCCESS(ShareInfoVo.builder().share_state(1)
                    .share_title(creativeVo.getTitle())
                    .share_sub_title(creativeVo.getDescription())
                    .share_image_url(imageBo.getUrl()).share_image_hash(imageHash).build());

        }

        return Response.SUCCESS(ShareInfoVo.builder().share_state(0).build());
    }
}
