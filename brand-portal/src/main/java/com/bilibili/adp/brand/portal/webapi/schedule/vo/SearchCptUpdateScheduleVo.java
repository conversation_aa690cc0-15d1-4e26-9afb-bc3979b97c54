package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.bilibili.adp.brand.portal.webapi.resource.vo.GameVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SearchCptUpdateScheduleVo {

    @ApiModelProperty("排期id")
    private Integer schedule_id;

    @ApiModelProperty("排期名")
    private String name;

    @ApiModelProperty("推广目的")
    private Integer promotion_purpose_type;

    @ApiModelProperty("关键词列表")
    private List<String> keywords;

    @ApiModelProperty("开始时间")
    private Long begin_time;

    @ApiModelProperty("结束时间")
    private Long end_time;

    @ApiModelProperty("uid")
    private String uid;

    @ApiModelProperty("APP包ID")
    private Integer app_package_id;

    @ApiModelProperty("唤起外部APP：0-无须唤起 1-需要唤起")
    private Integer need_wake_app;

    @ApiModelProperty("关键词词包")
    private List<Integer> keywords_packages;

    @ApiModelProperty("是否是二次确认")
    private boolean double_confirm;

    @ApiModelProperty("是否跳过检测")
    private boolean skip_check;

    @ApiModelProperty("刊例周期id")
    private Integer cycle_id;

    @ApiModelProperty("售卖类型：0、CPT 1、CPM")
    private Integer biz_sales_type;

    @ApiModelProperty("整体预期展示量")
    private Integer total_impression;
    //如果选择了安卓游戏下载，那么gameBaseId和channelId必传
    @ApiModelProperty("游戏信息")
    private GameVo game;

    @ApiModelProperty("唤起应用类型")
    private Integer wake_app_type;

}
