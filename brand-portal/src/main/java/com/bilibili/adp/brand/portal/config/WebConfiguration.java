package com.bilibili.adp.brand.portal.config;

import com.bilibili.adp.brand.portal.filter.MainSiteIdentifyAdapter;
import com.bilibili.adp.brand.portal.filter.RateLimitHandlerInterceptor;
import com.bilibili.adp.brand.portal.filter.RequestIdCheckHandlerInterceptor;
import com.bilibili.adp.web.framework.core.ContextWebArgumentResolver;
import com.bilibili.bjcom.disconf.cat.spring.CatInterceptor;
import com.bilibili.rbac.filter.interceptor.RbacInterceptor;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.ServletWebArgumentResolverAdapter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/7 17:01
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer, ApplicationContextAware {

    @Value("${rbac.tenantId}")
    private Integer tenantId;

    private ApplicationContext ctx;

    @Bean
    public RbacInterceptor registerRbacInterceptor() {
        RbacInterceptor rbacInterceptor = new RbacInterceptor();
        rbacInterceptor.setTenantId(tenantId);
        return rbacInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new CatInterceptor())
                .addPathPatterns("/**");
        registry.addInterceptor(this.ctx.getBean(RequestIdCheckHandlerInterceptor.class))
                .addPathPatterns("/web_api/v1/**");
        registry.addInterceptor(this.ctx.getBean(RbacInterceptor.class))
                .addPathPatterns("/web_api/v1/sale/**", "/web_api/v1/session/login");
        registry.addInterceptor(this.ctx.getBean(MainSiteIdentifyAdapter.class))
                .addPathPatterns("/open_api/v1/**");
        registry.addInterceptor(this.ctx.getBean(RateLimitHandlerInterceptor.class))
                .addPathPatterns("/web_api/v1/**");

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
        argumentResolvers.add(new ServletWebArgumentResolverAdapter(new ContextWebArgumentResolver()));
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }
}
