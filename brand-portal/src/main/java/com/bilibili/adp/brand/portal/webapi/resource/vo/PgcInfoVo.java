package com.bilibili.adp.brand.portal.webapi.resource.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PgcInfoVo {
    private String epId;

    private String seasonId;

    private String cid;

    private String mid;

    private String longTitle;

    private String showTitle;

    private String seasonTitle;

    private String cover;

    private String url;
}
