package com.bilibili.adp.brand.portal.convert.schedule;

import com.bilibili.adp.brand.portal.webapi.schedule.vo.*;
import com.bilibili.cpt.platform.util.TimeUtils;
import com.bilibili.ssa.platform.api.schedule.dto.SsaCpmScheduleDto;
import com.bilibili.ssa.platform.api.schedule.dto.SsaScheduleTargetDto;
import com.bilibili.ssa.platform.common.enums.SsaButtonStyle;
import com.bilibili.ssa.platform.common.enums.SsaClickAreaType;
import com.bilibili.ssa.platform.common.enums.SsaJumpAreaEffect;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.stream.Collectors;


/**
 * @Description ssa_cpm排期转换工具类
 * <AUTHOR>
 * @Date 2020.05.26 18:17
 */
public class SsaCpmScheduleConvert {

    private SsaCpmScheduleConvert(){
    }

    public static SsaCpmScheduleDto convertSsaCpmScheduleVo2Dto(SsaCpmScheduleAddVo vo) {
        SsaCpmScheduleDto dto = new SsaCpmScheduleDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setClickArea(vo.getMaterialClickArea());
        dto.setTarget(convert2ssaScheduleTargetDto(vo.getTarget()));
        dto.setBeginDate(TimeUtils.getBeginOfDay(vo.getBeginDate()));
        dto.setEndDate(TimeUtils.getBeginOfDay(vo.getEndDate()));
        if(SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())){
            dto.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }

        dto.setJumpAreaEffect(getActualJumpAreaEffect(vo.getButtonStyle(), vo.getJumpAreaEffect()));
        return dto;
    }

    /**
     * 由于滚动按钮产品不让和按钮类型放同一层级，所以前端只能通过这个字段去判断是否是扭一扭滚动按钮
     * 仅在vo层使用，到业务层以后转换为对应的按钮类型和无动效
     */
    private static int getActualJumpAreaEffect(Integer buttonStyle, Integer jumpAreaEffect) {
        if (SsaButtonStyle.TWIST_BRAND_SCROLL_BUTTON.getCode().equals(buttonStyle)) {
            return SsaJumpAreaEffect.NO.getCode();
        }else {
            return jumpAreaEffect;
        }
    }

    public static SsaCpmScheduleDto convertUpdateVo2SsaCpmSchedule(SsaCpmScheduleUpdateVo vo){
        SsaCpmScheduleDto dto = new SsaCpmScheduleDto();
        BeanUtils.copyProperties(vo, dto);
        dto.setTarget(convert2ssaScheduleTargetDto(vo.getTarget()));
        dto.setGdScheduleId(vo.getScheduleId());
        dto.setBeginDate(TimeUtils.getBeginOfDay(vo.getBeginDate()));
        dto.setEndDate(TimeUtils.getBeginOfDay(vo.getEndDate()));
        if(SsaClickAreaType.CLICK_AND_SLIDE.getCode().equals(vo.getMaterialClickArea())){
            dto.setButtonStyle(SsaButtonStyle.INTERACT_CLICK_BUTTON.getCode());
        }

        dto.setJumpAreaEffect(getActualJumpAreaEffect(vo.getButtonStyle(), vo.getJumpAreaEffect()));
        return dto;
    }

    private static SsaScheduleTargetDto convert2ssaScheduleTargetDto(SsaScheduleTargetVo vo){
        if(vo == null){
            return new SsaScheduleTargetDto();
        }
        return SsaScheduleTargetDto.builder().age(vo.getAge()).area(vo.getArea()).gender(vo.getGender()).build();
    }

    private static SsaScheduleTargetDto convertGd2SsaScheduleTargetDto(GdTargetVo vo){
        if(vo == null){
            return new SsaScheduleTargetDto();
        }
        return SsaScheduleTargetDto.builder().age(vo.getAge() != null ? vo.getAge().stream()
                .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .area(vo.getArea() != null ? vo.getArea().stream()
                        .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>())
                .gender(vo.getGender() != null ? vo.getGender().stream()
                        .map(GdTargetItemVo::getId).collect(Collectors.toList()) : new ArrayList<>()).build();
    }





}
