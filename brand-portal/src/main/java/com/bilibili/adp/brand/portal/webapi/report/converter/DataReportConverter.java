package com.bilibili.adp.brand.portal.webapi.report.converter;

import com.bilibili.adp.brand.portal.webapi.report.request.DataReportPageQueryRequest;
import com.bilibili.adp.brand.portal.webapi.report.vo.CreativeDataReportResultVo;
import com.bilibili.adp.brand.portal.webapi.report.vo.DataReportResultVo;
import com.bilibili.adp.brand.portal.webapi.report.vo.OrderDataReportResultVo;
import com.bilibili.adp.brand.portal.webapi.report.vo.OrderProductVo;
import com.bilibili.adp.brand.portal.webapi.report.vo.ScheduleDataReportResultVo;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.biz.report.dto.DataReportQueryDto;
import com.bilibili.brand.biz.report.dto.DataReportResultDto;
import com.bilibili.brand.biz.report.enums.AdDimensionEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;

@Mapper
public interface DataReportConverter {
    DataReportConverter MAPPER = Mappers.getMapper(DataReportConverter.class);

    OrderProductVo toOrderProductVo(OrderProduct orderProduct);

    DataReportQueryDto toDataReportQueryDto(Context context, DataReportPageQueryRequest request);

    OrderDataReportResultVo toOrderDataReportResultVo(DataReportResultDto dto);

    ScheduleDataReportResultVo toScheduleDataReportResultVo(DataReportResultDto dto);

    CreativeDataReportResultVo toCreativeDataReportResultVo(DataReportResultDto dto);

    default Timestamp toTimestamp(Long time) {
        return new Timestamp(time);
    }

    default DataReportResultVo toDataReportResultVo(DataReportQueryDto queryDto, DataReportResultDto resultDto) {
        AdDimensionEnum adDimensionEnum = AdDimensionEnum.getByCode(queryDto.getAdDimension());
        switch (adDimensionEnum) {
            case ORDER:
                return toOrderDataReportResultVo(resultDto);
            case SCHEDULE:
                return toScheduleDataReportResultVo(resultDto);
            case CREATIVE:
                return toCreativeDataReportResultVo(resultDto);
            default:
                return null;
        }
    }
}
