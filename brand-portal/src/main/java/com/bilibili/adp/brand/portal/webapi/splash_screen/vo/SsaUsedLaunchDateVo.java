package com.bilibili.adp.brand.portal.webapi.splash_screen.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SsaUsedLaunchDateVo {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增ID")
    private Integer id;


    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    private String start_date;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    private String end_date;



}