package com.bilibili.adp.brand.portal.webapi.schedule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/1 15:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("TopView库存查询返回信息")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TopViewPlusStockVo {
    @ApiModelProperty("处理序列号")
    private String dealSeq;

    @ApiModelProperty("分日库存信息")
    private List<SplitDaysStockVo> splitDaysStocks;

    @ApiModelProperty("是否处理完成")
    private Boolean isDealFinish;
}
