package com.bilibili.adp.brand.portal.webapi.launch.vo.cpt.live;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 直播cpt图片对象
 *
 * <AUTHOR>
 * @date 2022/3/8
 */
@ApiModel("直播cpt图片对象")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LiveCptImageVo {

    private Integer id;

    private String imageUrl;

    private String imageHash;

    @ApiModelProperty(notes = "图片类型 0-素材主图 1-背景图 2-头像 3-极致战队icon")
    private Integer imageStyle;
}
