package com.bilibili.cpt.platform.portal.webapi.schedule.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewScheduleDateVo {
    @NotNull
    @ApiModelProperty("开始日期")
    private Long begin_date;
    @NotNull
    @ApiModelProperty("结束日期")
    private Long end_date;
}
