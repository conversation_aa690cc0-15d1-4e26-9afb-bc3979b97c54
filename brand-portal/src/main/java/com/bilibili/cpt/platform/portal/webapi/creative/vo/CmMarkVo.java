package com.bilibili.cpt.platform.portal.webapi.creative.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年6月13日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class CmMarkVo {
	
	@ApiModelProperty("广告标识ID")
	private Integer id;
	@ApiModelProperty("广告标识名称")
    private String name;

}
