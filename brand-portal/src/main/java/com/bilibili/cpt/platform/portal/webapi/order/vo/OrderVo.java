package com.bilibili.cpt.platform.portal.webapi.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/12.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OrderVo {
    @ApiModelProperty(notes = "订单id")
    private Integer cpt_order_id;

    @ApiModelProperty(notes = "订单名")
    private String order_name;

    @ApiModelProperty(notes = "业务方id")
    private Integer business_side_id;

    @ApiModelProperty(notes = "业务方")
    private String business_side_name;

    @ApiModelProperty(notes = "合同号")
    private String crm_contract_number;

    @ApiModelProperty(notes = "合同id")
    private Integer crm_contract_id;

    @ApiModelProperty(notes = "合同名")
    private String contract_name;

    @ApiModelProperty("代理商名字")
    private String contract_agent_name;

    @ApiModelProperty("客户名")
    private String contract_account_name;

    @ApiModelProperty("合同开始时间")
    private String contract_begin_time;

    @ApiModelProperty("合同结束时间")
    private String contract_end_time;

    @ApiModelProperty("合同状态")
    private Integer contract_status;

    @ApiModelProperty("合同状态-展示")
    private String contract_status_desc;

    @ApiModelProperty(notes = "资源类型")
    private Integer resource_type;

    @ApiModelProperty(notes = "资源类型展示")
    private String resource_type_desc;

    @ApiModelProperty(notes = "订单状态")
    private Integer status;

    @ApiModelProperty(notes = "订单状态展示")
    private String status_desc;

    @ApiModelProperty(notes = "订单类型 0销售订单 1内部业务订单")
    private Integer type;

    @ApiModelProperty(notes = "排期数")
    private Integer schedule_count;

    @ApiModelProperty("视频id")
    private String avid;

    @ApiModelProperty("邀约广告-视频信息")
    private InvitationVideoVo video_info;

    @ApiModelProperty("广告开始时间")
    private String ad_start_time;

    @ApiModelProperty("广告结束时间")
    private String ad_end_time;
}
