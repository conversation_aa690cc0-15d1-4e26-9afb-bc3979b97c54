package com.bilibili.cpt.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/5/15.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractVo {
    @ApiModelProperty("合同id")
    private Integer crm_contract_id;
    @ApiModelProperty("合同号")
    private String contract_number;

    @ApiModelProperty("合同名")
    private String name;

    @ApiModelProperty("状态 0编辑中、1待审核、2已拒绝、3审核通过、4执行中、5可执行完成、6待收款、7已完成、8禁用")
    private Integer status;

    @ApiModelProperty("订单状态-展示值")
    private String status_desc;

    @ApiModelProperty("开始时间")
    private String begin_time;

    @ApiModelProperty("结束时间")
    private String end_time;

    @ApiModelProperty("客户id")
    private Integer account_id;


    @ApiModelProperty("客户名")
    private String username;

    @ApiModelProperty("代理商id")
    private Integer agent_id;

    @ApiModelProperty("代理商名称")
    private String agent_name;

}
