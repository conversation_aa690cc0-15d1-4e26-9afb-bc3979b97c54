package com.bilibili.cpt.platform.portal.webapi.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单简要信息
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OrderBriefVo {

    @ApiModelProperty(notes = "订单id")
    private Integer cpt_order_id;

    @ApiModelProperty(notes = "订单名称")
    private String order_name;
}
