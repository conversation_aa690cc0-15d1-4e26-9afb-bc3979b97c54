package com.bilibili.cpt.platform.portal.webapi.creative.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @time 2018/6/27 14:58
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ButtonCopyVo {

    private Integer id;
    private String content;
    private Integer type;

}
