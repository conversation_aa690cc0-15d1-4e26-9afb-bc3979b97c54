package com.bilibili.cpt.platform.api.creative.dto;

import com.bilibili.cpt.platform.api.external.SingleDynamicInfoDto;
import com.bilibili.cpt.platform.api.external.UpDynamicInfoDto;
import com.bilibili.cpt.platform.api.schedule.dto.KeywordsPackageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月21日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptCreativeAllInfoDto implements Serializable{

    private static final long serialVersionUID = -6232796296818490937L;

    private Long creativeId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台
     */
    private String platformName;

    /**
     * 页面id
     */
    private Integer locPageId;

    /**
     * 页面
     */
    private String locPageName;

    /**
     * 位置id
     */
    private Integer resourceId;

    /**
     * 位置
     */
    private String resourceName;

    /**
     * 广告位id
     */
    private Integer sourceId;

    /**
     * 位次
     */
    private String sourceName;

    /**
     * 业务方ID
     */
    private Integer businessSideId;
    private String businessSideName;

    /**
     * 订单ID
     */
    private Integer orderId;
    private String orderName;

    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 待下线的创意ID
     */
    private Integer disableCreativeId;

    /**
     * 投放开始时间,格式:2017-06-12 12:00:00
     */
    private Timestamp beginTime;

    /**
     * 投放结束时间,格式:2017-06-12 12:00:00
     */
    private Timestamp endTime;

    /**
     * 1-图片,2-视频,3-feeds图片,4-feeds视频(冗余字段，从模板中获取)
     */
    private Integer creativeType;

    /**
     * 创意名称
     */
    private String creativeName;

    /**
     * 跳转类型(1-链接 2-视频 3-游戏）
     */
    private Integer jumpType;

    /**
     * 推广目的内容
     */
    private String promotionPurposeContent;

    /**
     * 展示监控链接
     */
    private String customizedImpUrl;

    /**
     * 点击监控链接
     */
    private String customizedClickUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 扩展的描述(广告主名称等)
     */
    private String extDescription;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 图片的MD5值
     */
    private String imageMd5;

    /**
     * 视频ID(如AVID等)
     */
    private Long videoId;

    /**
     * 视频URL
     */
    private String videoUrl;

    /**
     * 扩展图片URL(缩略图、logo等的URL)
     */
    private String extImageUrl;

    /**
     * 扩展图片MD5值
     */
    private String extImageMd5;

    /**
     * 广告角标(默认为广告)
     */
    private Integer cmMark;

    /**
     * 商业标(用于搜索品专)
     */
    private Integer businessMark;

    /**
     * 商业标文案(用于搜索品专)
     */
    private String businessMarkCopywriting;

    /**
     * 按钮文案
     */
    private String buttonCopy;

    /**
     * 按钮文案ID
     */
    private Integer buttonCopyId;

    /**
     * 原因（审核拒绝时填写）
     */
    private String reason;

    /**
     * 创意模板ID
     */
    private Integer templateId;

    /**
     * 状态（1-待审核,2-审核通过,3-审核不通过, 4-删除）
     */
    private Integer cptCreativeStatus;

    /**
     * 版本号
     */
    private Integer version;
    
    /**
     * 轮播数
     */
    private Integer rotationNum;

    /**
     * 新/老创意状态提示信息
     */
    private CptCreativeTipsDto creativeTips;

    private String scheduleStartTime;

    private String scheduleEndTime;

    private Long businessNumber;
    private Long avid;
    private Integer type;

    private Long mgkPageId;

    /**
     * 创意分享信息
     */
    private CptCreativeShareDto cptCreativeShareDto;
    //唤起链接
    private String schemeUrl;
    //排期app包id
    private Integer appPackageId;
    //排期推广类型
    private Integer promotionPurposeType;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    private Integer auditStatus;

    //以下字段用于搜索品专审核后台查询
    private List<String> keywords;

    //稿件信息
    private List<NewManuscriptInfo> manuscriptInfos;

    private String brandName;

    //头像url
    private String faceUrl;

    //头像md5
    private String faceMd5;

    //品牌描述
    private String brandDescription;

    /**
     *投放主体
     */
    private Integer launcher;

    /**
     *投放主体
     */
    private Long mid;

    private List<ButtonDto> buttons;

    private List<ImageDto> images;

    //banner首焦视频化
    /**
     * 建站视频ID
     */
    private Integer mgkVideoId;

    /**
     * 建站视频URL
     */
    private String mgkVideoUrl;

    /**
     * 建站原始视频封面
     */
    private String mgkVideoCoverUrl;

    /**
     * 本地视频跳转类型
     */
    private Integer mgkJumpType;

    /**
     * 本地视频跳转链接
     */
    private String mgkJumpUrl;

    /**
     * 正常模式背景色
     */
    private String normalModeBackgroundColor;

    /**
     * 夜间模式背景色
     */
    private String nightModeBackgroundColor;

    /**
     * 商业标
     */
    private Integer busMarkId;

    /**
     * 搜索cpt创意类型，0：搜索品专，1：搜索原生，默认：0
     */
    private Integer searchCreativeType;

    /**
     * 搜索cpt创意类型描述，0：搜索品专，1：搜索原生，默认：0
     */
    private String searchCreativeTypeDesc;

    /**
     * 词包
     */
    private List<KeywordsPackageDto> keywordPackages;

    /**
     * 建站（本地）视频
     */
    private List<MgkVideoInfoDto> mgkVideos;

    /**
     * 创意时间（和关联表一致）
     */
    private List<CptCreativeDateDto> dates;

    /**
     * 动态信息
     */
    private SingleDynamicInfoDto dynamicInfo;

    /**
     * 品牌名称和{@link #brandName}应用场景不同，不要混淆
     */
    private String brandTitle;

    //排期开始时间
    private Timestamp scheduleStartTimeV2;
    //排期结束时间
    private Timestamp scheduleEndTimeV2;

    private List<Long> liveIdList;
}
