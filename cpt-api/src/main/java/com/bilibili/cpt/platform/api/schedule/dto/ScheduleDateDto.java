package com.bilibili.cpt.platform.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/19.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleDateDto {

    //用于直播cpt的按小时投放
    private Integer sourceId;

    //用于直播cpt的按小时投放
    private List<Integer> seqList;

    private Timestamp beginDate;

    private Timestamp endDate;
}
