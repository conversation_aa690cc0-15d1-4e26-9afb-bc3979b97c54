package com.bilibili.cpt.platform.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021.03.08 11:41
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ButtonDto implements Serializable {

    private static final long serialVersionUID = 4793352879868202603L;

    private Integer buttonCopyId;

    private Integer buttonType;

    private String buttonName;

    //顺序
    private Integer seq;

    //跳转类型
    private Integer jumpType;

    //跳转url
    private String jumpUrl;

    //唤起url
    private String schemeUrl;

    //按钮类型 0-文案选择式按钮 1-文案自定义式按钮
    private Integer buttonStyle;

    private Long boundId;

    private Integer category;

    private Long creativeId;

    //兼容数据
    private Integer imageId;

    private Long extremeTeamDataSourceId;

    //游戏用
    private Integer gameBaseId;
    //游戏用
    private String trackAdf;

    //监控链接
    private String customizedUrl;
}
