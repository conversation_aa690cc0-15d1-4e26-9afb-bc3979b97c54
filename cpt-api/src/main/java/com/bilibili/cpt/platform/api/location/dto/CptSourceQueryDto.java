package com.bilibili.cpt.platform.api.location.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-02-28
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CptSourceQueryDto implements Serializable {

    private static final long serialVersionUID = 1525183446915524423L;

    private Integer platformId;
    private List<Integer> pageIds;
    private List<Integer> resourceIds;
    private List<Integer> sourceIds;
    private Timestamp beginTime;
    private Timestamp endTime;

    private Integer cycleId;

    private Integer salesType;

    private Integer cardType;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 出价类型 0-日出价 1-周出价 2-月出价
     */
    private Integer priceType;

}
