package com.bilibili.cpt.platform.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewManuscriptInfo implements Serializable {

    private static final long serialVersionUID = 8108372412213986011L;

    /**
     * 自增id
     */
    private Integer id;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 序号
     */
    private Integer seq;

    /**
     * 视频aid
     */
    private Long aid;

    /**
     * 封面图
     */
    private String coverUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 跳转类型(1-链接 2-移动视频 3-游戏 4-Web视频 5-页面ID）
     */
    private Integer jumpType;

    /**
     * 推广目的
     */
    private String jumpUrl;

}