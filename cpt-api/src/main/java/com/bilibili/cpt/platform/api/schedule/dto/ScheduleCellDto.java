package com.bilibili.cpt.platform.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Map;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/20.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleCellDto {
    private Integer sourceId;
    private Integer scheduleId;
    private Integer gdOrderId;
    private String orderName;
    private Timestamp beginDate;
    private Timestamp endDate;
    private Integer businessSideId;
    private String logoColor;
    private String buSideName;
    private boolean isGd;
    private Map<Timestamp, String> cellInfoMap;
    private boolean isYours;

    private boolean cellEnable;
}
