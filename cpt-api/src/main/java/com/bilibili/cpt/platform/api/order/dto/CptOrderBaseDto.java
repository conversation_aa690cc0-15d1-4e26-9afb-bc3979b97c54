package com.bilibili.cpt.platform.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/28.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptOrderBaseDto implements Serializable{
    private static final long serialVersionUID = 90994449303798371L;
    private Integer id;
    /**
     * crm合同id
     */
    private Integer crmContractId;

    /**
     * crm订单id
     */
    private Integer crmOrderId;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 业务方id
     */
    private Integer businessSideId;

    /**
     * 状态（0待审核 1正式 2删除）
     */
    private Integer orderStatus;

    /**
     *  0其他 1内部、2售卖、3配送、4补量
     */
    private Integer resourceType;

    private Integer originTag;

    private Integer product;

    private Integer gdOrderType;

    private Integer accountId;

}
