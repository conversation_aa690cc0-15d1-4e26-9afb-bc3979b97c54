package com.bilibili.cpt.platform.api.business_side.dto;

import java.io.Serializable;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年6月15日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewBusinessSideDto implements Serializable{
    private static final long serialVersionUID = 6801984286903313728L;
    private String name;
    private Integer type;
    private String logoColor;
    private List<Integer> sourceIds;
    private Integer departmentId;
    private Integer accountId;
}
