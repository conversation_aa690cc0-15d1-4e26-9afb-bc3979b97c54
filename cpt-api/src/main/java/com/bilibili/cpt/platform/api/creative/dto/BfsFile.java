package com.bilibili.cpt.platform.api.creative.dto;

import java.io.File;
import java.io.InputStream;
import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BfsFile implements Serializable{
	
    private static final long serialVersionUID = 5435625905381491555L;
    private long size;
	private File convFile;
	private String mimeType;
	private String fileName;
	private byte[] bytes;
	private InputStream inputStream;
	
}
