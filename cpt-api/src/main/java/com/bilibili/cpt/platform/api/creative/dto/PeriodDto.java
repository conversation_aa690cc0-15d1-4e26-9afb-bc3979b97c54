package com.bilibili.cpt.platform.api.creative.dto;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年6月21日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PeriodDto implements Serializable{

    private static final long serialVersionUID = -7232204526761485000L;
    private Timestamp startTime;
    private Timestamp endTime;

}
