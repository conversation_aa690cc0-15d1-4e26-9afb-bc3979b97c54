package com.bilibili.cpt.platform.api.schedule.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.cpt.platform.api.schedule.dto.CptScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.NewSourceGroupScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.NewSourceScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.UpdateCptScheduleDto;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by fanwen<PERSON> on 2017/6/19.
 */
public interface ICptScheduleService {

    CptScheduleDto getScheduleById(Integer scheduleId) throws ServiceException;

    void updateCptSchedule(UpdateCptScheduleDto updateScheduleDto, Operator operator);

//    void createCptScheduleBySource(NewSourceScheduleDto newSourceScheduleDto, Operator operator);

    void createCptScheduleBySourceGroup(NewSourceGroupScheduleDto newSourceGroupScheduleDto, Operator operator);

    void deleteCptScheduleById(Integer id, Operator operator);

    void deleteCptScheduleByIds(List<Integer> ids, Operator operator);

    /**
     * 预约系统的自动释放，会反作用CPT排期，使排期自动删除
     * @param scheduleId
     * @param day
     */
    void deleteCptScheduleDayFromBooking(Integer scheduleId, Timestamp day);


    Map<Integer, CptScheduleDto> getScheduleBaseById(List<Integer> scheduleIds);


}
