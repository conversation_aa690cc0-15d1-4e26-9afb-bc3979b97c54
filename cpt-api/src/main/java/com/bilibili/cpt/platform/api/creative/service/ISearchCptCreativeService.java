package com.bilibili.cpt.platform.api.creative.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.cpt.platform.api.creative.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月20日
 */
public interface ISearchCptCreativeService {

    UrlHashDto uploadMaterial(Integer templateId, Integer materialType,
                              BfsFile bfsFile) throws ServiceException;

    void delete(Operator operator, Long gdCreativeId);

    Long create(Operator operator, SearchCptCreativeDto newCptCreativeDto);

    void multiCreate(Operator operator, MultiCreateSearchCptCreativeDto multiCreateSearchCptCreativeDto) throws Exception;

    void update(Operator operator, SearchCptCreativeDto update) throws ServiceException;

    List<CptTemplateDto> getTemplatesByScheduleIdList(Operator operator, List<Integer> scheduleIdList);

    SearchCptCreativeDto getByCreativeId(Long creativeId, Operator operator) throws ServiceException;

    PageResult<CptCreativeAllInfoDto> getCptCreatives(QueryCreativeParamDto queryCreativeParamDto,
                                                      Integer page, Integer size);

}
