package com.bilibili.cpt.platform.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/14.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderParamDto {
    private Integer gdOrderId;
    private String orderNameLike;
    private Integer businessSideId;
    private List<Integer> gdOrderStatusList;

    private List<Integer> businessSideIds;

    private Integer product;


}
