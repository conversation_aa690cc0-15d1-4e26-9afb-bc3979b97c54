package com.bilibili.cpt.platform.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageDto implements Serializable {

    private static final long serialVersionUID = -4084806454547616265L;

    private Integer id;

    private String imageUrl;

    private String imageMd5;

    private String hash;

    //图片类型 0-素材主图 1-背景图
    private Integer imageStyle;

    //跳转类型
    private Integer jumpType;

    //跳转url
    private String jumpUrl;

    //唤起url
    private String schemeUrl;

    //是否是gif
    private Boolean isGif;

    //封面url
    private String coverUrl;

    //封面url
    private String coverHash;

    //标题
    private String title;

    //描述
    private String description;

    //按钮
    private List<ButtonDto> buttons;

    //顺序
    private Integer seq;

    //创意ID
    private Long creativeId;
}
