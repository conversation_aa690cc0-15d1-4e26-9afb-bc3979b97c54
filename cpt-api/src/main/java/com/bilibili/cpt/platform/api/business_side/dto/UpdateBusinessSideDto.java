package com.bilibili.cpt.platform.api.business_side.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月15日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateBusinessSideDto implements Serializable{
    private static final long serialVersionUID = 3406987567452509466L;
    private Integer id;
    private String name;
    private String logoColor;
    private List<Integer> sourceIds;
    private Integer departmentId;
    private Integer accountId;
}
