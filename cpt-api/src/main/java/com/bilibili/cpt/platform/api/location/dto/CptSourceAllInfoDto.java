package com.bilibili.cpt.platform.api.location.dto;

import com.bilibili.brand.dto.cycle.PriceRaiseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月14日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptSourceAllInfoDto implements Serializable{
	private static final long serialVersionUID = -3039332201539201302L;
    private Integer id;
    private Integer cycleId;
    private Timestamp cycleBeginTime;
    private Timestamp cycleEndTime;
	private Integer platformId;
    private String platformName;
    private Integer pageId;
    private String pageName;
    private Integer resourceId;
    private String resourceName;
    private Integer sourceId;
    private String sourceName;
    
    private String templates;
    private String level;
    private Integer rotationNum;
    private Integer mFreqLimit;
    private BigDecimal externalPrice;
    private BigDecimal internalPrice;
    private Integer isDistribution;
    private Integer status;
    private BigDecimal internalCpmPrice;
    private Boolean statusPrice;

    //半小时价格，用于直播cpt售卖
    private BigDecimal hourExternalPrice;

    /**
     * 半小时外部增强版刊例价 主要用于直播cpt
     */
    private BigDecimal hourExternalPriceStrong;

    /**
     * cpt半小时热门时段刊例价
     */
    private BigDecimal hourHotExternalPrice;

    /**
     * 提前预订状态 0-关 1-开
     */
    private Integer preBookingStatus;

    private Integer salesType;

    private Integer priceType;

    private Integer templateId;

    private String templateName;

    private Integer cardType;

    private String cardName;

    //行业id
    private Integer industryId;

    //行业名称
    private String industryName;

    /**
     * 统一行业id
     */
    private Integer unitIndustryId;

    /**
     * 统一行业名称
     */
    private String unitIndustryName;

    /**
     * 周出价
     */
    private BigDecimal weekExternalPrice;

    //加收项
    private PriceRaiseDto raise;

    /**
     * cpm单价
     */
    private BigDecimal cpmPrice;

    /**
     * 相互推荐刊例价
     */
    private BigDecimal mutualRecommendPrice;

    /**
     * 粉丝量级价格
     */
    private List<UpFansPriceDto> upFansPriceDtoList;
}
