package com.bilibili.cpt.platform.api.location.service;

import java.util.List;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.cpt.platform.api.location.dto.CptSourceGroupDto;

/**
 * <AUTHOR>
 * @date 2017年6月15日
 */
public interface ICptSourceGroupService {
    public List<CptSourceGroupDto> getCptSourceGroupByStatus(Integer status);
    public Integer addGroup(Operator operator, String groupName);
    public void updateGroupName(Operator operator, Integer groupId, String groupName);
    public void deleteSourceFromGroup(Operator operator, Integer sourceId, Integer groupId);
    public void addSourceToGroup(Operator operator, Integer sourceId, Integer groupId);
    public void enableGroup(Operator operator, Integer groupId);
    public void disableGroup(Operator operator, Integer groupId);
    List<CptSourceGroupDto> getValidCptSourceGroupByBusinessSideId(Integer businessSideId);

    List<CptSourceGroupDto> getSearchCptSourceGroupByBusinessSideId(Integer businessSideId);
    List<Integer> querySourceIdsByGroupId(Integer groupId);
}
