package com.bilibili.cpt.platform.api.external;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DynamicMaterialInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long type;

    private Long rid;

    private String cover;

    private List<PictureInfoDto> pictureInfoList;

    public Long getAvid() {
        if (Objects.equals(this.type, 8L)) {
            return rid;
        }
        return null;
    }
}
