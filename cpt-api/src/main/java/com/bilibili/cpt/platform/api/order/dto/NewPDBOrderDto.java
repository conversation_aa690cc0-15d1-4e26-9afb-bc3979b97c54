package com.bilibili.cpt.platform.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2018/4/16 17:05
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewPDBOrderDto implements Serializable{
    private static final long serialVersionUID = 8408982362925671171L;
    private String orderName;
    private Long crmContractNumber;
    private Integer resourceType;

}
