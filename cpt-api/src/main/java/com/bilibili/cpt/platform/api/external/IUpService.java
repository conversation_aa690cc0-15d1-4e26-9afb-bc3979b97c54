package com.bilibili.cpt.platform.api.external;

import java.util.List;

public interface IUpService {

    Integer queryUpFansLevel(List<Long> upMidList);

    List<UpFansDto> queryUpFans(List<Long> upMidList);

    UpDynamicInfoDto queryUpDynamic(Long upMid, List<Long> typeList, Long limitTime);

    SingleDynamicInfoDto queryDynamicByDynamicId(Long mid, Long dynamicId);

    SingleDynamicInfoDto queryDynamicByAid(Long mid, Long aid);
}
