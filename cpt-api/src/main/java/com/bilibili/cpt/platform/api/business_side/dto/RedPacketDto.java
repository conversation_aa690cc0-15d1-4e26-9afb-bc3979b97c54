package com.bilibili.cpt.platform.api.business_side.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedPacketDto implements Serializable{
    private static final long serialVersionUID = 1636065405010521377L;
    private Integer businessSideId;
    //本月初始保障额度
    private Long initRedPacket;
    //下月月初始保障额度
    private Long nextMonthInitRedPacket;
}
