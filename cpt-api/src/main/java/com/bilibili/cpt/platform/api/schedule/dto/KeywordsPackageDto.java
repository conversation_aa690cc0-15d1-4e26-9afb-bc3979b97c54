package com.bilibili.cpt.platform.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 词包
 *
 * <AUTHOR>
 * @date 2022/10/31 10:09
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class KeywordsPackageDto implements Serializable {
    //词包id
    private Integer packageId;
    //词包名称
    private String packageName;
    //所关联的关键词
    private List<String> keywords;
}
