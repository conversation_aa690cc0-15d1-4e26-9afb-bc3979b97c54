package com.bilibili.cpt.platform.api.creative.dto;

import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaNewSplashScreenVideoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月21日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCptCreativeDto implements Serializable{

    private static final long serialVersionUID = -1205677129534812694L;
    private Long gdCreativeId;
    private Timestamp beginTime;
    private Timestamp endTime;
    private String creativeName;
    private Integer jumpType;
    private String promotionPurposeContent;
    private String customizedImpUrl;
    private String customizedClickUrl;
    private String title;
    private String description;
    private String extDescription;
    private String imageUrl;
    private String imageMd5;
    private Long videoId;
    private String videoUrl;
    private String extImageUrl;
    private String extImageMd5;
    private Integer cmMark;
    private Integer busMarkId;
    private String adMark;
    private String buttonCopy;
    private Integer buttonCopyId;
    
    private String imageHash;
    private String videoHash;
    private String extImageHash;
    private Long mgkPageId;
    private Integer adVersionControllId;

    private String schemeUrl;

    private Integer isIdfaEncrypted;

    /**
     * 分享开关 0-关闭 1-打开
     */
    private Integer shareState;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享副标题
     */
    private String shareSubTitle;

    /**
     * 分享图片URL
     */
    private String shareImageUrl;

    /**
     * 分享图片hash
     */
    private String shareImageHash;

    /**
     * topView首焦创意投放时间
     */
    private List<Timestamp> launchDateList;

    /**
     * topView首焦视频
     */
    private SsaNewSplashScreenVideoDto videoDto;

    //建站视频ID
    private Integer mgkVideoId;

    private List<CptCreativeMonitoringDto> creativeMonitoring;

    //可触发展示的开始时间（单位ms）（目前使用场景：扭一扭，其他场景可复用）
    private Integer eggStartTime;

    //可触发展示的结束时间（单位ms）（目前使用场景：扭一扭，其他场景可复用）
    private Integer eggEndTime;

    //彩蛋视频id（目前使用场景：扭一扭，其他场景可复用）
    private Integer eggVideoId;

    //引导图（目前使用场景：扭一扭，其他场景可复用）
    private String lottieUrl;

    //交互文案（目前使用场景：扭一扭，其他场景可复用）
    private String hint;

    //彩蛋hint的x轴坐标百分比0-100
    private Integer eggCoorX;

    //彩蛋hint的x轴坐标百分比0-100
    private Integer eggCoorY;

    //稿件
    private ManuscriptInfo manuscriptInfo;

    //产品型号
    private ProductLabelDto productLabel;

    //小程序
    private MiniProgramDto miniProgram;

    // 番剧id
    private Long seasonId;

    // 集数id
    private Long epId;

    // 投放类型：0、动态投放 1、稿件投放
    private Integer launchType;

    // up账号id
    private Long upMid;

    // up动态id
    private Long upDynamicId;

    // 模板id
    private Integer templateId;

    private Integer showStyle;

    private String brandTitle;
}
