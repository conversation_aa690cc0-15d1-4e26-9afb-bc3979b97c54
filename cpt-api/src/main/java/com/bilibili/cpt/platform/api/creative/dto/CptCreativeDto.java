package com.bilibili.cpt.platform.api.creative.dto;

import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.brand.dto.common.AppPackageDto;
import com.bilibili.cpt.platform.api.external.SingleDynamicInfoDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017年6月21日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptCreativeDto implements Serializable{

    private static final long serialVersionUID = -6232796296818490937L;
    private Long creativeId;
    private Integer platformId;
    private String platformName;
    private Integer LocPageId;
    private String locPageName;
    private Integer resourceId;
    private String resourceName;
    private Integer sourceId;
    private String sourceName;
    private Integer businessSideId;
    private Integer orderId;
    private Integer scheduleId;
    private Long disableCreativeId;
    private Timestamp beginTime;
    private Timestamp endTime;
    private Integer creativeType;
    private String creativeName;
    private Integer jumpType;
    private String promotionPurposeContent;
    private String customizedImpUrl;
    private String customizedClickUrl;
    private String title;
    private String description;
    private String extDescription;
    private String imageUrl;
    private String imageMd5;
    private Long videoId;
    private String videoUrl;
    private String extImageUrl;
    private String extImageMd5;
    private Integer cmMark;
    private String buttonCopy;
    private Integer buttonCopyId;
    private String reason;
    private Integer templateId;
    private Integer cptCreativeStatus;
    private Integer version;
    
    private String imageHash;
    private String videoHash;
    private String extImageHash;

    private Boolean isOnline;

    private Long invitationAvid;

    private Long mgkPageId;

    private String schemeUrl;
    private String adMark;

    private Integer isIdfaEncrypted;

    /**
     * 状态（1-有效，2-暂停, 3-删除 4-结束 5-修改待下线 6-已下线）
     */
    private Integer status;
    /**
     * 商业标id
     */
    private Integer busMarkId;

    private Integer orderProduct;

    private ProductLabelDto productLabel;

    private MiniProgramDto miniProgram;

    private List<AppPackageDto> appPackages;

    /**
     * 创意时间（和关联表一致）
     */
    private List<CptCreativeDateDto> dates;

    /**
     * up动态信息
     */
    private SingleDynamicInfoDto dynamicInfo;

    /**
     * 附加卡类型 0-无 1-蓝链 2-评论
     */
    private Integer additionalCardType;

    /**
     * 直播预约ID
     */
    private Long liveBookingId;

    /**
     * 直播开播时间
     */
    private String liveTime;

}
