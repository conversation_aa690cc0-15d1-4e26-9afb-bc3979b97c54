package com.bilibili.cpt.platform.api.location.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年6月14日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptSourceBusinessSideLimitDto implements Serializable{
	private static final long serialVersionUID = 323703608338745846L;
	private Integer limitId;
	private Integer cycleId;
	private Integer businessSideId;
    private String businessSideName;
    private Integer mRotationLimit;
}
