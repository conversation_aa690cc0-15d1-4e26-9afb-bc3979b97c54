package com.bilibili.cpt.platform.api.order.service;

import com.bilibili.cpt.platform.api.creative.dto.PeriodDto;
import com.bilibili.cpt.platform.api.order.dto.CptOrderBusinessOrderDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2018/5/21 22:08
 */
public interface ICptOrderBusinessOrderService {

    PeriodDto getPeriodByGdOrderId(Integer gdOrderId);

    CptOrderBusinessOrderDto getCptOrderBusinessOrderDtoByGdOrderId(Integer gdOrderId);

    void addMappingDto(CptOrderBusinessOrderDto businessOrderDto);

    void updateMappingDto(CptOrderBusinessOrderDto businessOrderDto);

    void updateMappingDtoAvid(CptOrderBusinessOrderDto businessOrderDto);

    void removeMappingDtoAvid(CptOrderBusinessOrderDto businessOrderDto);

    Map<Integer, CptOrderBusinessOrderDto> getCptOrderBusinessOrderDtoByGdOrderIds(List<Integer> gdOrderIds);
}
