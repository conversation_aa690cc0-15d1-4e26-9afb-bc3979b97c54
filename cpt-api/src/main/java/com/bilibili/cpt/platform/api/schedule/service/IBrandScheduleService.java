package com.bilibili.cpt.platform.api.schedule.service;


import com.bilibili.cpt.platform.api.schedule.dto.BrandScheduleDto;
import com.bilibili.cpt.platform.api.schedule.dto.NewBrandScheduleDto;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/19.
 */
public interface IBrandScheduleService {
    void batchCreateCptScheduleDto(List<NewBrandScheduleDto> newBrandScheduleDtos);

    Map<Integer,Map<Timestamp, List<BrandScheduleDto>>> getSourceBrandScheduleMap(List<Integer> sourceIds, Timestamp begin, Timestamp end);

    Map<Timestamp, List<BrandScheduleDto>> getSourceBrandScheduleMap(Integer sourceId, Timestamp begin, Timestamp end);

    void deleteByScheduleId(Integer gdScheduleId);

    Map<Integer,Map<Timestamp, List<BrandScheduleDto>>> getSourceBrandScheduleMap(List<Integer> sourceIds, Timestamp begin,
                                                                                  Timestamp end, Function<List<Integer>, List<Integer>> scheduleFilter);

}
