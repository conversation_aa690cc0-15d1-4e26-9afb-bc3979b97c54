package com.bilibili.cpt.platform.api.location.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SourceConfigDto implements Serializable{
    
	private static final long serialVersionUID = 6867002609794063912L;
    private Integer cycleId;
	private Integer sourceId;
	private String sourceName;
    private Integer isDistribution;
    private Integer rotationNum;
    private Integer mFreqLimit;
    private BigDecimal externalPrice;
    private BigDecimal internalPrice;
    private BigDecimal internalCpmPrice;
    /**
     * 半小时外部刊例价 主要用于直播cpt
     */
    private BigDecimal hourExternalPrice;

    /**
     * 半小时外部增强版刊例价 主要用于直播cpt
     */
    private BigDecimal hourExternalPriceStrong;

    /**
     * cpt半小时热门时段刊例价
     */
    private BigDecimal hourHotExternalPrice;

    private Integer preBookingStatus;

    //行业id
    private Integer industryId;

    //行业名称
    private String industryName;

    /**
     * 统一行业id
     */
    private Integer unitIndustryId;

    /**
     * 统一行业名称
     */
    private String unitIndustryName;

    /**
     * 周出价
     */
    private BigDecimal weekExternalPrice;

    /**
     * cpm单价
     */
    private BigDecimal cpmPrice;

    /**
     * 相互推荐刊例价
     */
    private BigDecimal mutualRecommendPrice;

    /**
     * up粉丝量级刊例价
     */
    private List<UpFansPriceDto> upFansPriceDtoList;
}
