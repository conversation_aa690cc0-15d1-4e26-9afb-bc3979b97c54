package com.bilibili.cpt.platform.api.external;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SingleDynamicInfoDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 动态ID
     */
    private Long dynamicId;

    /**
     * up主mid
     */
    private Long mid;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Long ctime;

    /**
     * 动态类型
     * <a href="https://info.bilibili.co/pages/viewpage.action?pageId=13358955">...</a>
     */
    private Long type;
}
