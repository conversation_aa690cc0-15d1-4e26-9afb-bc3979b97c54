package com.bilibili.brand.job.gd;

import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.ext.ILiveInfoService;
import com.bilibili.brand.job.AbstractJobHandler;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/*
* 刷新直播间链接
 */
@Slf4j
@Component
@JobHandler("RefreshLiveUrlJob")
public class RefreshLiveUrlJob extends AbstractJobHandler {

	private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());
	
	@Autowired
	private IGdCreativeService gdCreativeService;


	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		JOB_LOGGER.info("********  start job: RefreshLiveUrlJob....  ******");
		try {
			gdCreativeService.refreshGdLiveUrl();
		} catch (Exception e) {
			log.warn("refreshGdLiveUrl error" + e);
		}

		//try {
		//	ssaSplashScreenService.refreshSsaLiveInfo();
		//} catch (Exception e) {
		//	log.warn("refreshSsaLiveInfo error" + e);
		//}
// https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
// 直播预约失效引擎出兜底，不再更改创意状态
//
//		try {
//			liveInfoService.refreshCreativeStatus();
//		} catch (Exception e) {
//			log.warn("refreshCreativeStatus error" + e);
//		}
		JOB_LOGGER.info("********  finish job: RefreshLiveUrlJob!!!  ******\n\n");
		return ReturnT.SUCCESS;
	}

}
