package com.bilibili.brand.job.gd;

import com.bilibili.brand.api.schedule.service.IScheduleService;
import com.bilibili.ssa.platform.biz.service.schedule.delegate.SsaPlusScheduleServiceDelegate;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * GD跑量完成通知机器人
 * Created by buer
 */
@Slf4j
@Component
@JobHandler("GdFinishRobotJob")
public class GdFinishRobotJob extends IJobHandler {
    @Autowired
    private IScheduleService scheduleService;

    @Autowired
    private SsaPlusScheduleServiceDelegate delegate;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>GdFinishRobotJob start executing...");
        scheduleService.gdFinishInform();
        scheduleService.gdInlineInventory();
        delegate.informSsa();
        log.info("=====>>>>>>>>>>>>>>>GdFinishRobotJob end executing.");
        return ReturnT.SUCCESS;
    }
}
