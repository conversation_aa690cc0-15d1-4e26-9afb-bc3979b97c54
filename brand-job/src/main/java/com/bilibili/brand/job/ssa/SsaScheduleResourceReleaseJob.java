package com.bilibili.brand.job.ssa;

import com.alibaba.fastjson.JSON;
import com.bilibili.brand.job.AbstractJobHandler;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.cpt.platform.biz.handler.booking.BookingReleaseHandler;
import com.bilibili.cpt.platform.biz.handler.booking.SsaScheduleBookingReleaseHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 【品牌】闪屏资源-订单排期释放规则
 * <p>
 * 释放
 * <a href="https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887003091340">【品牌】闪屏资源-订单排期释放规则</a>
 *
 * <AUTHOR>
 * @date 2023/11/6 16:00
 */
@Slf4j
@JobHandler("SsaScheduleResourceReleaseJob")
@Component
public class SsaScheduleResourceReleaseJob extends AbstractJobHandler {

    @Resource(type = SsaScheduleBookingReleaseHandler.class)
    private BookingReleaseHandler releaseHandler;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        ReleaseRequestBean releaseRequest = JSON.parseObject(param, ReleaseRequestBean.class);
        releaseRequest.setTriggerType(ReleaseRequestBean.TriggerType.RELEASE);
        releaseRequest.setResourceType(ReleaseRequestBean.ResourceType.SSA);
        if (Objects.isNull(releaseRequest.getTriggerTime())) {
            releaseRequest.setTriggerTime(LocalDateTime.now());
        }
        releaseHandler.release(releaseRequest);
        return ReturnT.SUCCESS;
    }
}
