/**
 * <AUTHOR>
 * @date 2017年8月17日
 */

package com.bilibili.brand.job.common;

import com.bilibili.brand.api.creative.service.ICreativePreviewService;
import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.job.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Slf4j
@JobHandler("UpdateCreativePreviewJob")
@Component
public class UpdateCreativePreviewJob extends AbstractJobHandler {

    @Autowired
    private ICreativePreviewService creativePreviewService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        log.info("UpdateCreativePreviewJob start");
        creativePreviewService.updatePreviewStatus();
        log.info("UpdateCreativePreviewJob end");
        return ReturnT.SUCCESS;
    }
}
