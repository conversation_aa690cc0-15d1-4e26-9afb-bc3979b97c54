package com.bilibili.brand.job.cpt;

import com.amazonaws.util.StringInputStream;
import com.bilibili.adp.common.oss.BOSSUtils;
import com.bilibili.adp.common.util.Md5Util;
import com.bilibili.brand.api.common.enums.OrderProduct;
import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.api.schedule.service.IQueryScheduleService;
import com.bilibili.brand.job.AbstractJobHandler;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.api.schedule.dto.KeywordsPackageDto;
import com.bilibili.cpt.platform.biz.handler.schedule.SearchKeyWordHandler;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.bilibili.enums.PlatformType;
import com.google.gson.annotations.SerializedName;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜索Cache品专广告保护
 *
 * <AUTHOR>
 * @date 2024/1/2
 */
@Slf4j
@Component
@JobHandler("SearchCptWordProtectionJob")
public class SearchCptWordProtectionJob extends AbstractJobHandler {

    @Autowired
    private IQueryScheduleService queryScheduleService;
    @Autowired
    private SearchKeyWordHandler searchKeyWordHandler;
    @Value("${job.protect.search.word.path:search-cpt/}")
    private String path;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime twoHourLater = now.plusHours(2);

        List<ScheduleDto> searchCptSchedules = queryScheduleService.queryBaseSchedule(QueryScheduleDto.builder()
                .orderProducts(Collections.singletonList(OrderProduct.SEARCH_CPT.getCode()))
                .statusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                .gdBeginTime(TimeUtil.toTimestamp(now))
                .gdEndTime(TimeUtil.toTimestamp(twoHourLater))
                .build());
        if (CollectionUtils.isEmpty(searchCptSchedules)) {
            return new ReturnT<>("无搜索cpt排期，任务结束");
        }

        List<Integer> scheduleIds = searchCptSchedules.stream()
                .map(ScheduleDto::getScheduleId)
                .collect(Collectors.toList());
        Map<Integer, Pair<List<String>, List<KeywordsPackageDto>>> keywordsAndPackages = searchKeyWordHandler.getKeywordsAndPackages(scheduleIds, true);

        String text = searchCptSchedules.stream()
                .map(schedule -> {
                    Pair<List<String>, List<KeywordsPackageDto>> wordPair = keywordsAndPackages.get(schedule.getScheduleId());
                    List<String> words = wordPair.getFirst();
                    if (CollectionUtils.isEmpty(words)) {
                        words = new LinkedList<>();
                    }
                    if (!CollectionUtils.isEmpty(wordPair.getSecond())) {
                        List<String> packageWords = wordPair.getSecond().stream()
                                .map(KeywordsPackageDto::getKeywords)
                                .flatMap(Collection::stream)
                                .distinct()
                                .collect(Collectors.toList());
                        words.addAll(packageWords);
                    }
                    if (CollectionUtils.isEmpty(words)) {
                        return null;
                    }
                    KeyWordInfo wordInfo = KeyWordInfo.builder()
                            .startTime(TimeUtil.timestampToIsoTimeStr(schedule.getGdBeginTime()))
                            .endTime(TimeUtil.timestampToIsoTimeStr(schedule.getGdEndTime()))
                            .platform(Optional.ofNullable(PlatformType.getByCodeWithoutEx(schedule.getPlatformId())).orElse(PlatformType.IPHONE).getDesc())
                            .words(words)
                            .build();
                    return GsonUtils.toJson(wordInfo);
                })
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.joining("\n"));


        StringInputStream stringInputStream = new StringInputStream(text);
        BOSSUtils.putObject(path + "search-words", stringInputStream);

        String md5 = Md5Util.md5Hash(text);
        StringInputStream inputStream = new StringInputStream(md5);
        BOSSUtils.putObject(path + "search-words.md5", inputStream);

        return new ReturnT<>("任务处理完成");
    }

    @Data
    @Builder
    @EqualsAndHashCode
    private static class KeyWordInfo{
        @SerializedName("start_time")
        private String startTime;
        @SerializedName("end_time")
        private String endTime;
        @SerializedName("platform")
        private String platform;
        @SerializedName("search_word")
        private List<String> words;
    }
}
