package com.bilibili.brand.job.cpt;

import com.bilibili.brand.api.creative.service.IGdCreativeService;
import com.bilibili.brand.api.ext.IHotForbidService;
import com.bilibili.brand.api.ext.ILiveInfoService;
import com.bilibili.ssa.platform.api.splash_screen.service.ISsaSplashScreenService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/*
* 刷新直播间链接
 */
@Slf4j
@Component
public class RefreshForbidStatusJob extends QuartzJobBean {

	private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());

	@Autowired
	private IHotForbidService forbidService;

	@Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
		JOB_LOGGER.info("********  start job: RefreshForbidStatusJob....  ******");
		try {
			forbidService.refreshCreativeStatus();
		} catch (Exception e) {
			log.warn("RefreshForbidStatusJob error" + e);
		}

		JOB_LOGGER.info("********  finish job: RefreshForbidStatusJob!!!  ******\n\n");
	}

}
