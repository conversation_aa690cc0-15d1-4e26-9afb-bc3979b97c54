package com.bilibili.brand.job.gd;

import com.bilibili.brand.api.account.service.IQueryAccountService;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.MailUtils;
import com.bilibili.brand.api.launch.dto.EmptyLaunchScheduleDto;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * Created by many2023 on 2017/10/25.
 */
@Component
public class EmptyScheduleStatusJob extends QuartzJobBean {
    private final Logger LOGGER = LoggerFactory.getLogger("job");
    @Autowired
    private IMailService mailService;
    @Autowired
    private IQueryAccountService queryAccountService;

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        LOGGER.info("************   EmptyScheduleStatusJob start executing...");
		//TODO
//        try {
//        	List<EmptyLaunchScheduleDto> dtos = gdScheduleService.getEmptySchedules();
//
//        	LOGGER.info("EmptyScheduleStatusJob get empty schedules:{}", dtos);
//
//        	Map<Integer, List<EmptyLaunchScheduleDto>> scheduleMapByAccountId = dtos
//								.stream()
//								.collect(Collectors.groupingBy(EmptyLaunchScheduleDto::getAccountId, Collectors.toList()));
//
//        	List<Integer> accountIds = Lists.newArrayList(scheduleMapByAccountId.keySet());
//
//        	Map<Integer, List<String>> bilibiliUerMap = queryAccountService.getBiliUserMapByAccoungIds(accountIds);
//        	LOGGER.info("EmptyScheduleStatusJob bilibili users:{}", bilibiliUerMap);
//
//        	for(Entry<Integer, List<EmptyLaunchScheduleDto>> e: scheduleMapByAccountId.entrySet()) {
//        		sendMail(e.getValue(), bilibiliUerMap.get(e.getKey()));
//        	}
//
//		} catch (ServiceException e) {
//			LOGGER.info("************   EmptyScheduleStatusJob encounter an exception:" + Throwables.getStackTraceAsString(e));
//		}
        LOGGER.info("************   EmptyScheduleStatusJob end executing.");
    }
    
    private void sendMail(List<EmptyLaunchScheduleDto> dtos, List<String> bilibiliUsers) {
    	if(CollectionUtils.isEmpty(bilibiliUsers)) {
    		return;
    	}
    	
    	bilibiliUsers= bilibiliUsers.stream().filter(user -> !Strings.isNullOrEmpty(user)).collect(Collectors.toList());
    	
    	if(CollectionUtils.isEmpty(bilibiliUsers)) {
    		return;
    	}
    	
		List<String> sendList = bilibiliUsers.stream().map(user -> user + "@bilibili.com").collect(Collectors.toList());
		sendList.forEach(send -> {
			try {

				MailMessage mail = new MailMessage();

				mail.setTos(Lists.newArrayList(send));
				mail.setHasFile(false);
				mail.setSubject("GD排期空投放提醒");
				mail.setUseHtml(true);

				String text =  MailUtils.genHtmlTableString(dtos);
				String appendList = sendList.stream().filter(item -> !item.equals(send)).collect(Collectors.toList()).toString();
				StringBuilder apsb = new StringBuilder();
				apsb.append("<br/>");
				apsb.append("<p><b>此邮件已同步通知以下名单: </b></p>");
				apsb.append("<p><b>").append(appendList).append("</b></p>");
				text += apsb;
				mail.setText(text);

				LOGGER.info("EmptyScheduleStatusJob send mail:{}", mail);
				mailService.send(mail);
			} catch (Exception e) {
				LOGGER.error("EmptyScheduleStatusJob encounter an exception:" + Throwables.getStackTraceAsString(e));
			}

		});




    }
}
