package com.bilibili.brand.job.gd;

import com.bilibili.brand.api.creative.service.IGdCreativeService;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;

public class RefreshGdCreativeStatusByVideoIdJob extends QuartzJobBean {
	private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());
	
	@Autowired
	private IGdCreativeService gdCreativeService;

	@Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
		JOB_LOGGER.info("********  start job: RefreshGdCreativeStatusByVideoIdJob....  ******");
		gdCreativeService.refreshGdCreativeStatusByVideo();
		JOB_LOGGER.info("********  finish job: RefreshGdCreativeStatusByVideoIdJob!!!  ******\n\n");
	}

}
