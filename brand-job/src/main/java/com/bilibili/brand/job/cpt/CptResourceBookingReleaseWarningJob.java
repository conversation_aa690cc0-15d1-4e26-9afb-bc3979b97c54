package com.bilibili.brand.job.cpt;

import com.alibaba.fastjson.JSON;
import com.bilibili.brand.job.AbstractJobHandler;
import com.bilibili.cpt.platform.biz.bean.ReleaseRequestBean;
import com.bilibili.cpt.platform.biz.handler.booking.BookingReleaseHandler;
import com.bilibili.cpt.platform.biz.handler.booking.CptResourceBookingReleaseHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * CPT预定资源释放预警
 * <p>
 * CPT预定资源释放V2版本
 * <a href="https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002727323">【品牌】CPT资源预定释放逻辑调整</a>
 *
 * <AUTHOR>
 * @date 2023/3/24 17:07
 */
@Slf4j
@JobHandler("CptResourceBookingReleaseWarningJob")
@Component
public class CptResourceBookingReleaseWarningJob extends AbstractJobHandler {

    @Resource(type = CptResourceBookingReleaseHandler.class)
    private BookingReleaseHandler releaseHandler;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        ReleaseRequestBean releaseRequest = JSON.parseObject(param, ReleaseRequestBean.class);
        releaseRequest.setTriggerType(ReleaseRequestBean.TriggerType.WARNING);
        releaseRequest.setResourceType(ReleaseRequestBean.ResourceType.CPT);
        if (Objects.isNull(releaseRequest.getTriggerTime())) {
            releaseRequest.setTriggerTime(LocalDateTime.now());
        }
        releaseHandler.release(releaseRequest);
        return ReturnT.SUCCESS;
    }
}
