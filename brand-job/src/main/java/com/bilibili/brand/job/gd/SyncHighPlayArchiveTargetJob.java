package com.bilibili.brand.job.gd;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.enums.Status;
import com.bilibili.brand.api.resource.target_lau.IResTargetItemService;
import com.bilibili.brand.api.resource.target_lau.dto.NewTargetItemDto;
import com.bilibili.brand.api.resource.target_lau.dto.ResTargetItemDto;
import com.bilibili.brand.api.resource.targetmeta.ITargetService;
import com.bilibili.brand.api.resource.targetmeta.Target;
import com.bilibili.brand.api.resource.targetmeta.TargetType;
import com.bilibili.brand.biz.data.ProphetTargetDao;
import com.bilibili.brand.biz.resource.po.ProphetTargetPo;
import com.bilibili.brand.biz.resource.po.ProphetTargetPoExample;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 将高播放稿件定向信息同步到资源定向表
 *
 * <AUTHOR>
 * @date 2022/10/20 17:51
 */
@Component
@JobHandler("SyncHighPlayArchiveTargetJob")
public class SyncHighPlayArchiveTargetJob extends IJobHandler {
    @Autowired
    private ProphetTargetDao prophetTargetDao;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private ITargetService targetService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Operator operator = Operator.builder()
                .operatorName(OperatorType.SYSTEM.getName())
                .operatorType(OperatorType.SYSTEM)
                .build();
        this.checkHighPlayArchiveTargetType(operator);

        List<ProphetTargetPo> prophetTargets = this.queryHighPlayArchiveList();
        if (CollectionUtils.isEmpty(prophetTargets)) {
            return ReturnT.SUCCESS;
        }

        List<ResTargetItemDto> resTargetItems = resTargetItemService.getItemByTargetType(
                TargetType.HIGH_PLAY_ARCHIVE.getCode());

        Map<Integer, ResTargetItemDto> resTargetItemMap = resTargetItems.stream()
                .collect(Collectors.toMap(item -> Integer.valueOf(item.getMappingContent()), item -> item));
        //需要删除的
        prophetTargets.stream()
                .filter(prophet -> prophet.getIsDeleted() == IsDeleted.DELETED.getCode()
                        && resTargetItemMap.containsKey(prophet.getMappingContent()))
                .forEach(prophet -> {
                    ResTargetItemDto resTargetItemDto = resTargetItemMap.get(prophet.getMappingContent());
                    resTargetItemService.delete(operator, resTargetItemDto.getId());
                });
        //需要新增的
        prophetTargets.stream()
                .filter(prophet -> prophet.getIsDeleted() == IsDeleted.VALID.getCode()
                        && !resTargetItemMap.containsKey(prophet.getMappingContent()))
                .forEach(prophet -> {
                    NewTargetItemDto resTargetItemDto = NewTargetItemDto.builder()
                            .name(TargetType.HIGH_PLAY_ARCHIVE.getName())
                            .type(TargetType.HIGH_PLAY_ARCHIVE.getCode())
                            .mappingContent(String.valueOf(prophet.getMappingContent()))
                            .parentId(0)
                            .sortOrder(0)
                            .subType(0)
                            .build();
                    resTargetItemService.create(operator, resTargetItemDto);
                });
        return ReturnT.SUCCESS;
    }

    private List<ProphetTargetPo> queryHighPlayArchiveList() {
        //关于mapping_content说明：
        //0：低播放量定向
        //1：高播放量定向
        //目前只需要高播放量稿件定向，因此强制过滤1
        ProphetTargetPoExample example = new ProphetTargetPoExample();
        example.createCriteria()
                .andTargetTypeIdEqualTo(TargetType.HIGH_PLAY_ARCHIVE.getProphetCode())
                .andMappingContentEqualTo(1);
        return this.prophetTargetDao.selectByExample(example);
    }

    private void checkHighPlayArchiveTargetType(Operator operator) {
        Target targetType = this.targetService.getTargetByType(TargetType.HIGH_PLAY_ARCHIVE.getCode());
        if (targetType == null) {
            Target target = Target.builder()
                    .name(TargetType.HIGH_PLAY_ARCHIVE.getName())
                    .type(TargetType.HIGH_PLAY_ARCHIVE.getCode())
                    .status(Status.VALID.getCode())
                    .isInput(false)
                    .isMultiGroupSelected(false)
                    .hasMapping(false)
                    .mappingType(0)
                    .sortOrder(0)
                    .build();
            this.targetService.createTarget(target, operator);
        }
    }
}
