package com.bilibili.brand.job.common;

import com.bilibili.brand.api.common.enums.SwitchStatus;
import com.bilibili.brand.api.schedule.dto.QueryScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.brand.biz.order.service.syn_crm.SynOrderToCrmService;
import com.bilibili.brand.biz.schedule.service.QueryScheduleService;
import com.bilibili.brand.job.AbstractJobHandler;
import com.bilibili.brand.util.TimeUtil;
import com.bilibili.cpt.platform.util.GsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 排期创建后同步订单到crm任务
 *
 * <AUTHOR>
 * @date 2023/2/8
 */
@Slf4j
@JobHandler("SynOrderToCrmJob")
@Component
public class SynOrderToCrmJob extends AbstractJobHandler {

    @Autowired
    private SynOrderToCrmService synOrderToCrmService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private QueryScheduleService queryScheduleService;

    private final static String LAST_PROCESS_INFO_KEY = "syn_crm_job_info";

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        ProcessInfo processInfo = getLastProcessInfo();

        XxlJobLogger.log("当前查询到处理信息为:[{}]", processInfo);

        List<Integer> orderIds = getOrderIds(param, processInfo);

        XxlJobLogger.log("当前查询到需要同步到crm的排期id为:[{}]", orderIds);

        if (CollectionUtils.isEmpty(orderIds)) {
            return new ReturnT<>("暂无订单需要更新");
        }

        doSyn(processInfo, orderIds);

        return new ReturnT<>("订单同步crm完成");
    }

    private void doSyn(ProcessInfo processInfo, List<Integer> orderIds) {
        try {
            synOrderToCrmService.synCrm(orderIds);
            updateInfoWhenSuccess(processInfo);
            XxlJobLogger.log("所有订单成功同步");
        } catch (Exception e) {
            XxlJobLogger.log(e);
            throw new RuntimeException(e);
        }finally {
            saveProcessInfo(processInfo);
        }
    }

    private void updateInfoWhenSuccess(ProcessInfo processInfo) {
        processInfo.setLastSuccessTime(processInfo.getThisRoundBigestTime());
        processInfo.setThisRoundBigestTime(null);
        processInfo.setLastSuccessScheduleId(processInfo.getThisRoundLastOneScheduleId());
        processInfo.setThisRoundLastOneScheduleId(null);
    }

    private List<Integer> getOrderIds(String param, ProcessInfo processInfo) {

        if (StringUtils.isNotBlank(param)) {
            return Arrays.stream(param.split(",")).map(Integer::new).collect(Collectors.toList());
        }

        List<ScheduleDto> schedules = queryScheduleService.queryBaseSchedule(QueryScheduleDto.builder()
                .statusList(Arrays.asList(SwitchStatus.STARTED.getCode(), SwitchStatus.STOPED.getCode()))
                //多延长10分钟，保证时间区间内未提交的事务在后续任务中能够被处理到
                .mTime(TimeUtil.toTimestamp(processInfo.getLastSuccessTime().minusMinutes(10)))
                .build());

        if (CollectionUtils.isEmpty(schedules)) {
            return new LinkedList<>();
        }

        schedules = schedules.stream()
                .sorted(Comparator.comparing(ScheduleDto::getMtime))
                .collect(Collectors.toList());

        Integer thisRoundLastOneScheduleId = schedules.get(schedules.size() - 1).getScheduleId();

        boolean scheduleNotChange = processInfo.getLastSuccessScheduleId() != null
                && processInfo.getLastSuccessScheduleId().equals(thisRoundLastOneScheduleId);

        if (scheduleNotChange) {
            return new LinkedList<>();
        }

        Timestamp bigestTime = schedules.stream()
                .map(ScheduleDto::getMtime)
                .max(Comparator.comparing(Function.identity()))
                .get();

        processInfo.setThisRoundBigestTime(bigestTime.toLocalDateTime());

        processInfo.setThisRoundLastOneScheduleId(thisRoundLastOneScheduleId);

        return schedules.stream()
                .map(ScheduleDto::getOrderId)
                .distinct()
                .collect(Collectors.toList());
    }

    private ProcessInfo getLastProcessInfo(){
        RBucket<String> bucket = redissonClient.getBucket(LAST_PROCESS_INFO_KEY);
        if (bucket.isExists() && StringUtils.isNotBlank(bucket.get())) {
            ProcessInfo processInfo = GsonUtils.toObject(bucket.get(), ProcessInfo.class);
            if (processInfo.getLastSuccessTime() == null) {
                processInfo.setLastSuccessTime(LocalDate.now().atStartOfDay());
            }
            return processInfo;
        }

        return ProcessInfo.builder()
                .lastSuccessTime(LocalDate.now().atStartOfDay())
                .build();
    }

    private void saveProcessInfo(ProcessInfo processInfo) {
        RBucket<String> bucket = redissonClient.getBucket(LAST_PROCESS_INFO_KEY);
        bucket.set(GsonUtils.toJson(processInfo), 1, TimeUnit.DAYS);
    }

    @Data
    @Builder
    private static class ProcessInfo {

        private LocalDateTime lastSuccessTime;

        private LocalDateTime thisRoundBigestTime;

        private Integer lastSuccessScheduleId;

        private Integer thisRoundLastOneScheduleId;
    }
}
