package com.bilibili.brand.job.gd;

import com.bilibili.brand.api.schedule.service.IScheduleService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * cpt跑量完成通知机器人
 * Created by buer
 */
@Slf4j
@Component
public class CptRobotJob extends QuartzJobBean {

    @Autowired
    private IScheduleService scheduleService;

    @SneakyThrows
    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        log.info("=====>>>>>>>>>>>>>>>CptRobotJob start executing...");
        scheduleService.cptFinishInform();
        log.info("=====>>>>>>>>>>>>>>>CptRobotJob end executing.");
    }
}
