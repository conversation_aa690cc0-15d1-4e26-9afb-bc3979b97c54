package com.bilibili.brand.job.gd;

import org.quartz.*;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

/**
 * Created by fan<PERSON><PERSON> on 2019/3/8.
 */
@Component
public class UpdateAccountBilibiliInfoJob extends QuartzJobBean {
    @Override
    protected void executeInternal(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        Scheduler scheduler = jobExecutionContext.getScheduler();
        try {
            scheduler.deleteJob(JobKey.jobKey("UpdateAccountBilibiliInfoJobTrigger", "brand-order"));
        } catch (SchedulerException e) {
            e.printStackTrace();
        }
    }
}
