package com.bilibili.cpt.report.platform.api.creative.service;

import com.bilibili.cpt.report.platform.api.creative.dto.VideoPlayStatCreativeDto;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/12 14:17
 */
public interface IVideoPlayStatCreativeService {
    Map<Long, VideoPlayStatCreativeDto> getCreativeStat(List<Long> creativeIds);

    Map<Long, VideoPlayStatCreativeDto> getCreativeStat(List<Long> creativeIds, Timestamp fromTime, Timestamp toTime);
}
