package com.bilibili.cpt.report.platform.api.creative.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @time 2017/8/24 10:41
 */
@Data
public class ReportDayCptCreativeDto {
    private Integer id;
    private Timestamp date;
    private Integer orderId;
    private Integer appliedId;
    //private Integer srcId;
    private String sourceName;
    private String srcLevel;
    private Integer roleId;
    private String roleName;
    private Integer materialId;
    private String title;
    private Integer platform;
    private String page;
    private String position;
    private Integer offset;
    private Integer pv;
    private Integer visiblePv;
    private Integer click;
    private BigDecimal ctr1;
    private BigDecimal ctr2;
    private Integer uv;
    private Integer visibleUv;
    private Timestamp ctime;
    private Timestamp mtime;
    private Integer resourceType;
    private String creativeType;
    private String orderName;

    /*private String creativeName;
    private Long creativeId;
    private String sourceName;*/

    private Long gdCreativeId;
    private String gdCreativeName;
    private Integer gdScheduleId;
    private String gdScheduleName;
    private Integer gdOrderId;
    private String gdOrderName;

    private Long playNums;
    private Long playTime;
}
