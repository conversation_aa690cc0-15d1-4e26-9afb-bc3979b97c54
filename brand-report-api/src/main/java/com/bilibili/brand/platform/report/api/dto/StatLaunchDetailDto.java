/** 
* <AUTHOR> 
* @date  2017年10月13日
*/

package com.bilibili.brand.platform.report.api.dto;

import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatLaunchDetailDto {
	private Integer accountId;
	private Integer orderId;
	private Integer campaignId;
	private Integer unitId;
	private Integer creativeId;
	private Integer showAccount;
	private Integer clickCount;
	private BigDecimal cost;
	private String date;
	private BigDecimal clickRate;
	private BigDecimal costPerClick;
	private BigDecimal averageCostPerThousand;
}
