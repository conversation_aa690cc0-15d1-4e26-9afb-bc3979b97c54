package com.bilibili.brand.platform.report.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by fanwen<PERSON> on 16/9/23.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatCreativeDto implements Serializable {

    private static final long serialVersionUID = 1014213220711125961L;

    private Integer accountId;
    private Integer orderId;
    private Integer scheduleId;
    private Integer unitId;
    private Long creativeId;
    private Long showAccount;
    private Integer clickCount;
    private BigDecimal cost;
    private String date;
    private BigDecimal clickRate;
    private BigDecimal costPerClick;
    private BigDecimal averageCostPerThousand;

    /**
     * 稿件播放量
     */
    private Long playCount;

    /**
     * 稿件播放时长
     */
    private Long playTime;

    /**
     * 稿件播放成本
     */
    private BigDecimal playCost;
}
