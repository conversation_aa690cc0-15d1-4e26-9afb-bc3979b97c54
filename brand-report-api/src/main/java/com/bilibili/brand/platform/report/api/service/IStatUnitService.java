package com.bilibili.brand.platform.report.api.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.platform.report.api.dto.StatUnitDto;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/23.
 */
public interface IStatUnitService {
    List<StatUnitDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatUnitDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatUnitDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatUnitDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;


    List<StatUnitDto> getInUnitIdsGroupByTime(List<Integer> unitIds, Timestamp fromTime, Timestamp toTime) throws ServiceException;

    BigDecimal getSumCostByUnitId(Integer unitId, Timestamp fromTime, Timestamp toTime) throws ServiceException;

    List<StatUnitDto> getInAccountIdsGroupByTime(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatUnitDto> getInAccountIdsGroupByDay(List<Integer> accountIds, Timestamp fromTime,
            Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatUnitDto> getInAccountIdsGroupByWeek(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatUnitDto> getInAccountIdsGroupByMonth(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

}
