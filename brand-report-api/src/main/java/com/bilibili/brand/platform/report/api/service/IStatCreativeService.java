package com.bilibili.brand.platform.report.api.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.platform.report.api.dto.AdStatCreativeDayDto;
import com.bilibili.brand.platform.report.api.dto.StatCreativeDto;
import com.bilibili.brand.platform.report.api.dto.StatQueryBean;
import com.bilibili.cpt.platform.common.GROUP_TYPE;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/9/23.
 */
public interface IStatCreativeService {
    List<StatCreativeDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatCreativeDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatCreativeDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatCreativeDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatCreativeDto> getInCreativeIdsGroupByTime(List<Long> creativeIds, Timestamp fromTime, Timestamp toTime);

    List<StatCreativeDto> getInAccountIdsGroupByTime(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatCreativeDto> getInAccountIdsGroupByDay(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatCreativeDto> getInAccountIdsGroupByWeek(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatCreativeDto> getInAccountIdsGroupByMonth(List<Integer> accountIds,
            Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatCreativeDto> getInCreativeIdsGroupByType(StatQueryBean statQueryBean);

    List<AdStatCreativeDayDto> getStatCreative(StatQueryBean statQueryBean);

    List<StatCreativeDto> getByAccountIdGroupByTimeAndGroupType(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, GROUP_TYPE group_type, List<Integer> orderIdList) throws ServiceException;

}
