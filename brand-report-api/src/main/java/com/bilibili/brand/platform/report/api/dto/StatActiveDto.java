package com.bilibili.brand.platform.report.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by fan<PERSON><PERSON> on 16/9/26.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatActiveDto implements Serializable{
    private static final long serialVersionUID = 1806471421697339498L;
    private String date;
    private int accountId;
    private int activeCount;
    private int salesType;
}
