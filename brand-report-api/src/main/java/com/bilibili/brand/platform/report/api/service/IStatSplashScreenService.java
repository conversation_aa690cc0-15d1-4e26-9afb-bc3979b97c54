package com.bilibili.brand.platform.report.api.service;

import com.bilibili.brand.platform.report.api.dto.OttStatSplashScreenDto;
import com.bilibili.brand.platform.report.api.dto.StatQueryBean;
import com.bilibili.brand.platform.report.api.dto.StatSplashScreenDto;

import java.util.List;
import java.util.Map;

public interface IStatSplashScreenService {

	List<StatSplashScreenDto> getStatDataGroupByType(StatQueryBean query);

	Map<Integer, StatSplashScreenDto> getSplashScreenStatMapInScreenIds(List<Integer> creativeIds);

	Map<Integer, StatSplashScreenDto> getSplashScreenStatMapInScreenIds(List<Integer> screenIds,
																		Integer salesType);

	Map<Integer, OttStatSplashScreenDto> getOttSplashScreenStatMapInScreenIds(List<Integer> screenIds);

}
