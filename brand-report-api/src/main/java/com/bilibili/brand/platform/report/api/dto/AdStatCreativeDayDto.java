package com.bilibili.brand.platform.report.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdStatCreativeDayDto {

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 时间序列-日期
     */
    private Timestamp groupTime;

    /**
     * 曝光数量
     */
    private Long showCount;

    /**
     * 点击次数
     */
    private Integer clickCount;

}