package com.bilibili.brand.platform.report.api.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.platform.report.api.dto.StatOrderDto;
import com.bilibili.cpt.platform.common.GROUP_TYPE;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/12/9.
 */
public interface IStatOrderService {
    List<StatOrderDto> getByAccountIdGroupByTime(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatOrderDto> getByAccountIdGroupByDay(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatOrderDto> getByAccountIdGroupByWeek(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatOrderDto> getByAccountIdGroupByMonth(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException;

    List<StatOrderDto> getInOrderIdsGroupByTime(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime) throws ServiceException;

    Map<Integer, StatOrderDto> getMapInOrderIds(List<Integer> orderIds) throws ServiceException;

    List<StatOrderDto> getInAccountIdsGroupByTime(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatOrderDto> getInAccountIdsGroupByDay(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatOrderDto> getInAccountIdsGroupByWeek(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

    List<StatOrderDto> getInAccountIdsGroupByMonth(List<Integer> accountIds, Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException;

	List<StatOrderDto> getInOrderIdsGroupByDay(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime)
	        throws ServiceException;

	List<StatOrderDto> getInOrderIdsGroupByMonth(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime)
	        throws ServiceException;

	List<StatOrderDto> getInOrderIdsGroupByWeek(List<Integer> orderIds, Timestamp fromTime, Timestamp toTime)
	        throws ServiceException;

    List<StatOrderDto> getByAccountIdGroupByTimeAndGroupType(Integer accountId, Timestamp fromTime, Timestamp toTime, Integer salesType, GROUP_TYPE group_type, List<Integer> orderIdList) throws ServiceException;


}
