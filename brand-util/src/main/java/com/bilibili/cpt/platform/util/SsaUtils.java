/** 
* <AUTHOR> 
* @date  2018年3月7日
*/ 

package com.bilibili.cpt.platform.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class SsaUtils {
	private final static BigDecimal HUNDRED = BigDecimal.valueOf(100);
	private final static BigDecimal THOUSAND = BigDecimal.valueOf(1000);
	private final static String WEBP_SUFFIX = "@%dw_%dh.webp";

	public static int getDayOfMonth(Timestamp date) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(date.getTime());
		
		return c.get(Calendar.DAY_OF_MONTH);
	}
	
	public static List<Integer> getEachDayOfMonth(int month) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.MONTH, month - 1);
		
		List<Integer> days = new ArrayList<Integer>();
		
		int lastDayOfMonth = getMonthLastDay(month);
		
		for(int i = 1; i <= lastDayOfMonth; i++) {
			days.add(i);
		}
		
		return days;
	}
	
	public static List<Integer> getEachDayOfMonth(Timestamp month) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(month.getTime());
		
		List<Integer> days = new ArrayList<Integer>();
		
		int lastDayOfMonth = getMonthLastDay(month);
		
		for(int i = 1; i <= lastDayOfMonth; i++) {
			days.add(i);
		}
		
		return days;
	}
	
	public static int getMonthLastDay(int month) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.MONTH, month - 1);
		return c.getActualMaximum(Calendar.DAY_OF_MONTH);
	}
	
	public static int getMonthLastDay(Timestamp month) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(month.getTime());
		return c.getActualMaximum(Calendar.DAY_OF_MONTH);
	}
	
	public static int getMonthFirstDay(int month) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.MONTH, month - 1);
		return c.getActualMinimum(Calendar.DAY_OF_MONTH);
	}
	
	public static int getMonthFirstDay(Timestamp month) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(month.getTime());
		return c.getActualMinimum(Calendar.DAY_OF_MONTH);
	}
	
	public static Timestamp getMonthLastDayTimestamp(int month) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.MONTH, month - 1);
		c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static Timestamp getMonthFirstDayTimestamp(int month) {
		Calendar c = Calendar.getInstance();
		c.set(Calendar.MONTH, month - 1);
		c.set(Calendar.DAY_OF_MONTH, 1);
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static Timestamp getMonthLastDayTimestamp(Timestamp month) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(month.getTime());
		c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static Timestamp getMonthFirstDayTimestamp(Timestamp month) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(month.getTime());
		c.set(Calendar.DAY_OF_MONTH, 1);
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static int getDateToMonthEndSpace(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		int dayOfMonth = c.get(Calendar.DAY_OF_MONTH);
		int lastDayOfMonth = c.getActualMaximum(Calendar.DAY_OF_MONTH);
		
		return lastDayOfMonth - dayOfMonth + 1;
	}
	
	public static int getMonthDate(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		return c.get(Calendar.DAY_OF_MONTH);
	}
	
	public static int getMonth(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		return c.get(Calendar.MONTH) + 1;
	}
	
	public static Timestamp getEndOfMonth(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
		c.set(Calendar.HOUR_OF_DAY, 23);
		c.set(Calendar.MINUTE, 59);
		c.set(Calendar.SECOND, 59);
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static Timestamp getEndOfNextMonth(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		c.add(Calendar.MONTH, 1);
		c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
		c.set(Calendar.HOUR_OF_DAY, 23);
		c.set(Calendar.MINUTE, 59);
		c.set(Calendar.SECOND, 59);
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static Timestamp getBeginOfMonth(Timestamp time) {
		Calendar c = Calendar.getInstance();
		c.setTimeInMillis(time.getTime());
		c.set(Calendar.DAY_OF_MONTH, 1);
		c.set(Calendar.HOUR_OF_DAY, 0);
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		return new Timestamp(c.getTimeInMillis());
	}
	
	public static List<Integer> getEveryMonth(Timestamp startTime, Timestamp endTime) {
		int startMonth = getMonth(startTime);
		int endMonth = getMonth(endTime);
		
		List<Integer> months = new ArrayList<Integer>();
		
		for(int month = startMonth; month <= endMonth; month++) {
			months.add(month);
		}
		
		return months;
	}

	public static BigDecimal getVideoSecDuration(Integer durationMills) {
		return null == durationMills
				? BigDecimal.ZERO
				: BigDecimal.valueOf(durationMills).divide(THOUSAND, 3, RoundingMode.HALF_UP);
	}

	public static Integer getVideoMilliDuration(BigDecimal durationSec) {
		return null == durationSec ? Integer.valueOf(0) : Integer.valueOf(durationSec.multiply(THOUSAND).intValue());
	}

	public static BigDecimal fromFenToYuan(Long fen) {
		return null == fen
				? BigDecimal.ZERO
				: (BigDecimal.valueOf(fen.doubleValue())).divide(HUNDRED, 2, RoundingMode.HALF_UP);
	}

	public static BigDecimal fromFenToYuan(Integer fen) {
		return null == fen
				? BigDecimal.ZERO
				: (BigDecimal.valueOf(fen.doubleValue())).divide(HUNDRED, 2, RoundingMode.HALF_UP);
	}
	
	public static Integer fromYuanToFen(BigDecimal yuan) {
		return null == yuan ? Integer.valueOf(0) : Integer.valueOf(yuan.multiply(HUNDRED).intValue());
	}

	public static Long fromYuanToFenInLong(BigDecimal yuan) {
		return null == yuan ? 0L : yuan.multiply(HUNDRED).longValue();
	}

	public static String getWidthHeightRatio(int width, int height){
		int cd = getGreatestCommonDivisor(width, height);
		return String.format("%d:%d", width/cd, height/cd);
	}

	public static double getVideoRatio(int d1, int d2){
		return (new BigDecimal(d1).divide(new BigDecimal(d2), 2, BigDecimal.ROUND_HALF_UP)).doubleValue();
	}

	private static int getGreatestCommonDivisor(int num1, int num2){
		int d;
		if(num1<num2){
			d=num1;
		}else{
			d=num2;
		}
		while(d>=1){
			if(num1 % d == 0 && num2 % d == 0){
				break;
			}
			d--;
		}
		return d;
	}
	
	public static boolean isPositive(Integer i) {
		return i != null && i.compareTo(0) > 0;
	}
	
	public static boolean isPositive(Long l) {
		return l != null && l.compareTo(0L) > 0;
	}
	
	public static boolean isPositive(BigDecimal b) {
		return b != null && b.compareTo(BigDecimal.ZERO) > 0;
	}
	
	public static Integer getInteger(Integer i) {
		return i == null ? Integer.valueOf(0) : i;
	}
	
//	public static void main(String[] args) {
////		System.out.println(SsaUtils.fromFenToYuan(1500).multiply(BigDecimal.valueOf(1)).divide(BigDecimal.valueOf(Integer.valueOf(3)), 2, BigDecimal.ROUND_HALF_DOWN).toString());
//		System.out.println(getVideoRatio(16, 9));
//	}

	public static String getCompressImageSuffix(int width, int height){
		return String.format(WEBP_SUFFIX, width, height);
	}
}
