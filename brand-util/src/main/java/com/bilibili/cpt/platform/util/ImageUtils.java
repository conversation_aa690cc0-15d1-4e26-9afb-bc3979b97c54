package com.bilibili.cpt.platform.util;

import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

import javax.imageio.ImageIO;

import com.bilibili.cpt.platform.util.bean.ImageResolution;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @date 2016年10月1日
 */
@Slf4j
public class ImageUtils {

    /**
     * 这个方法不会关闭inputStream，这是调用者需要做的
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static ImageResolution getImageResolution(InputStream inputStream) throws IOException {
        BufferedImage imagePic = ImageIO.read(inputStream);
        return new ImageResolution(imagePic.getWidth(), imagePic.getHeight());
    }

    public static ImageResolution getImageResolution(File file) throws IOException {
        BufferedImage imagePic;
        try (InputStream input = new FileInputStream(file)) {
            imagePic = ImageIO.read(input);
        }
        return new ImageResolution(imagePic.getWidth(), imagePic.getHeight());
    }

    /**
     * 根据url获取图片大小
     * @param urlPath
     * @return
     */
    public static byte[] getImageFromURL(String urlPath){
        byte[] data = null;
        InputStream is = null;
        HttpURLConnection conn = null;
        try {
            trustAllHttpsCertificates();
            URL url = new URL(urlPath);
            conn = (HttpURLConnection) url.openConnection();
            conn.setDoInput(true);
            conn.setRequestMethod("GET");
            conn.setReadTimeout(10000);
            conn.setConnectTimeout(10000);
            is = conn.getInputStream();
            if (conn.getResponseCode() == 200) {
                data = readInputStream(is);
            }
        } catch (Exception e) {
            log.error("get image failed. [image_url={}]", urlPath, e);
        } finally {
            try {
                if(is != null){
                    is.close();
                }
            } catch (IOException e) {
                log.error("close stream failed. [image_url={}]", urlPath, e);
            }
            if (conn != null) {
                conn.disconnect();
            }
        }
        return data;
    }

    public static void main(String[] args) {
        System.out.println(ImageUtils.getImageFromURL("http://i1.hdslb.com/bfs/archive/5d9399abe14d0e98055fc0da56e3beed21a7fe8b.jpg").length);
    }

    static class miTM implements javax.net.ssl.TrustManager,
            javax.net.ssl.X509TrustManager {
        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        public boolean isServerTrusted(
                java.security.cert.X509Certificate[] certs) {
            return true;
        }

        public boolean isClientTrusted(
                java.security.cert.X509Certificate[] certs) {
            return true;
        }

        public void checkServerTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
                throws java.security.cert.CertificateException {
            return;
        }

        public void checkClientTrusted(
                java.security.cert.X509Certificate[] certs, String authType)
                throws java.security.cert.CertificateException {
            return;
        }
    }

    public static void trustAllHttpsCertificates() throws Exception {
        javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[1];
        javax.net.ssl.TrustManager tm = new miTM();
        trustAllCerts[0] = tm;
        javax.net.ssl.SSLContext sc = javax.net.ssl.SSLContext
                .getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        javax.net.ssl.HttpsURLConnection.setDefaultSSLSocketFactory(sc
                .getSocketFactory());
    }

    /**
     * 将流转换为字节
     * @param is
     * @return
     */
    public static byte[] readInputStream(InputStream is) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        try {
            while ((length = is.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            baos.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] data = baos.toByteArray();
        try {
            is.close();
            baos.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return data;
    }
    
}