package com.bilibili.cpt.platform.util;

import com.bilibili.adp.common.util.Md5Util;
import com.bilibili.adp.common.util.Utils;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

public final class OSSUtils {

    private final static String UPOS_URI_PREFIX = "upos://";

    private final static String SECRET = "1902_724b86e0b80a18f765386a5285e";

    private static final Map<String, String> URL_OS_MAP = new HashMap<>();
    static {
        URL_OS_MAP.put("http://upos-hz-uat.bilivideo.com", "upos");
        URL_OS_MAP.put("http://upos-sz-mirrorkodo.bilivideo.com", "kodobv");
        URL_OS_MAP.put("http://uposgate-vip.bilivideo.com:2280", "upos");
    }

    public static String getOssUrl(String ossBaseUrl, String key) {
        Assert.hasLength(key, "key不可为空");
        Assert.hasLength(ossBaseUrl, "基础url不可为空");
        String os = URL_OS_MAP.get(ossBaseUrl);
        Assert.notNull(os, "基础url找不到对应的os参数 " + ossBaseUrl);

        String actualKey = key.replace(UPOS_URI_PREFIX, "/");
        Map<String, String> paramMap = new LinkedHashMap<>(3);
        paramMap.put("deadline", String.valueOf(Utils.getDayAfterHours(Utils.getNow(), 2).getTime() / 1000));
        paramMap.put("gen", "ssa");
        paramMap.put("os", os);

        return ossBaseUrl + getUpOsSign(actualKey, paramMap) + "&uparams=" + String.join(",", paramMap.keySet());
    }

    private static String getUpOsSign(String key, Map<String, String> paramMap) {
        String body = paramMap.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        String toSign = String.format("path=%s&%s&secret=%s", key, body, SECRET);

        return String.format("%s?%s&upsig=%s", key, body, Md5Util.md5Hash(toSign));
    }
}
