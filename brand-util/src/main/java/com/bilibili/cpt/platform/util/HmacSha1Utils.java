package com.bilibili.cpt.platform.util;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Calendar;
import java.util.Formatter;
import java.util.GregorianCalendar;
import java.util.TimeZone;


public class HmacSha1Utils {
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";

    private static String toHexString(byte[] bytes) {
        Formatter formatter = new Formatter();

        for (byte b : bytes) {
            formatter.format("%02x", b);
        }

        return formatter.toString();
    }

    public static String calculateRFC2104HMAC(String data, String key)
            throws NoSuchAlgorithmException, InvalidKeyException
    {
        SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(), HMAC_SHA1_ALGORITHM);
        Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
        mac.init(signingKey);
        return toHexString(mac.doFinal(data.getBytes()));
    }

//    public static void main(String[] args) throws Exception {
//        String hmac = calculateRFC2104HMAC("data", "key");
//
//        System.out.println(hmac);
//        assert hmac.equals("104152c5bfdca07bc633eebd46199f0255c9f49d");
//    }

    public static String getSignature(String strAppKey, String strADID){
        String sig = "";
        if(strAppKey.length() != 32 || strADID.length() != 32) return sig;
        try
        {
            byte[] btSrc = new byte[24];
// Fill the first half of src bytes.
            {
                int len = strADID.length();
                byte[] data = new byte[len / 2 + (len % 2 == 0 ? 0 : 1)];
                for(int n = 0; n < data.length; n++)
                {
                    String tmp = strADID.substring(n * 2, Math.min(n * 2 + 2, len)); data[n] = (byte)Integer.parseInt(tmp, 16);
                }
                System.arraycopy(data, 0, btSrc, 0, 16);
            }
// Fill the last half of src bytes.
            {
                Calendar cal = new GregorianCalendar(TimeZone.getTimeZone("GMT+8")); cal.setTimeInMillis(Calendar.getInstance().getTimeInMillis());

                long min = cal.getTimeInMillis() / 1000 / 60;
                System.out.println("time is "+cal.getTime()+" minute is "+min);
                btSrc[16 + 0] = (byte)((min & 0x00000000000000FFL) >>> 0); btSrc[16 + 1] = (byte)((min & 0x000000000000FF00L) >>> 8); btSrc[16 + 2] = (byte)((min & 0x0000000000FF0000L) >>> 16); btSrc[16 + 3] = (byte)((min & 0x00000000FF000000L) >>> 24); btSrc[16 + 4] = (byte)((min & 0x000000FF00000000L) >>> 32); btSrc[16 + 5] = (byte)((min & 0x0000FF0000000000L) >>> 40); btSrc[16 + 6] = (byte)((min & 0x00FF000000000000L) >>> 48); btSrc[16 + 7] = (byte)((min & 0xFF00000000000000L) >>> 56);
            }
// Generate result bytes.
            byte[] btResult;
            {
                byte[] key;
                {
                    int len = strAppKey.length();

                    key = new byte[len / 2 + (len % 2 == 0 ? 0 : 1)];
                    for(int n = 0; n < key.length; n++)
                    {
                        String t = strAppKey.substring(n * 2, Math.min(n * 2 + 2, len)); key[n] = (byte)Integer.parseInt(t, 16);
                    }
                }
                Mac mac = Mac.getInstance("HmacSHA1"); mac.init(new SecretKeySpec(key, "HmacSHA1"));
                btResult = mac.doFinal(btSrc);
            }
// Translate to string.
            sig = String.format("%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
                    btResult[0], btResult[1], btResult[2], btResult[3], btResult[4], btResult[5], btResult[6], btResult[7], btResult[8], btResult[9], btResult[10], btResult[11], btResult[12], btResult[13], btResult[14], btResult[15], btResult[16], btResult[17], btResult[18], btResult[19]);
        }
        catch(Throwable t){}
        return sig;
    }

    /**
     * 产⽣签名字符串
     * @param strAppKey
     * @param strADID
     * @return
     */
    public static String getSignature(String strAppKey, String strADID, long min)
    {
        String sig = "";
        if(strAppKey.length() != 32 || strADID.length() != 32) return sig;
        try
        {
            byte[] btSrc = new byte[24];
// Fill the first half of src bytes.
            {
                int len = strADID.length();
                byte[] data = new byte[len / 2 + (len % 2 == 0 ? 0 : 1)];
                for(int n = 0; n < data.length; n++)
                {
                    String tmp = strADID.substring(n * 2, Math.min(n * 2 + 2, len)); data[n] = (byte)Integer.parseInt(tmp, 16);
                }
                System.arraycopy(data, 0, btSrc, 0, 16);
            }
// Fill the last half of src bytes.
            {
//                Calendar cal = new GregorianCalendar(TimeZone.getTimeZone("GMT+8")); cal.setTimeInMillis(Calendar.getInstance().getTimeInMillis());
//                long min = cal.getTimeInMillis() / 1000 / 60;
                btSrc[16 + 0] = (byte)((min & 0x00000000000000FFL) >>> 0); btSrc[16 + 1] = (byte)((min & 0x000000000000FF00L) >>> 8); btSrc[16 + 2] = (byte)((min & 0x0000000000FF0000L) >>> 16); btSrc[16 + 3] = (byte)((min & 0x00000000FF000000L) >>> 24); btSrc[16 + 4] = (byte)((min & 0x000000FF00000000L) >>> 32); btSrc[16 + 5] = (byte)((min & 0x0000FF0000000000L) >>> 40); btSrc[16 + 6] = (byte)((min & 0x00FF000000000000L) >>> 48); btSrc[16 + 7] = (byte)((min & 0xFF00000000000000L) >>> 56);
            }
// Generate result bytes.
            byte[] btResult;
            {
                byte[] key;
                {
                    int len = strAppKey.length();

                    key = new byte[len / 2 + (len % 2 == 0 ? 0 : 1)];
                    for(int n = 0; n < key.length; n++)
                    {
                        String t = strAppKey.substring(n * 2, Math.min(n * 2 + 2, len)); key[n] = (byte)Integer.parseInt(t, 16);
                    }
                }
                Mac mac = Mac.getInstance("HmacSHA1"); mac.init(new SecretKeySpec(key, "HmacSHA1"));
                btResult = mac.doFinal(btSrc);
            }
// Translate to string.
            sig = String.format("%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
                    btResult[0], btResult[1], btResult[2], btResult[3], btResult[4], btResult[5], btResult[6], btResult[7], btResult[8], btResult[9], btResult[10], btResult[11], btResult[12], btResult[13], btResult[14], btResult[15], btResult[16], btResult[17], btResult[18], btResult[19]);
        }
        catch(Throwable t){}
        return sig;
    }
}

