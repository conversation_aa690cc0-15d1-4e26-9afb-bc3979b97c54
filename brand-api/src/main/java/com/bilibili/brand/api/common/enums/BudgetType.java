package com.bilibili.brand.api.common.enums;

/**
 * <AUTHOR>
 * @date 2016年9月26日
 */
public enum BudgetType {
    DAILY(1, "日预算"),
    TOTAL(2, "总预算");

    private int code;
    private String name;

    private BudgetType(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static BudgetType getByCode(int code) {
        for (BudgetType budgetType: values()) {
            if (budgetType.code == code) {
                return budgetType;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
