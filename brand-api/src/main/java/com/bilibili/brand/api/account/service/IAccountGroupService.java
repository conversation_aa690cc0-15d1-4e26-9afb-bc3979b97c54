package com.bilibili.brand.api.account.service;

import java.util.List;
import java.util.Map;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.account.dto.AccountGroupBaseDto;
import com.bilibili.brand.api.account.dto.AccountGroupDto;
import com.bilibili.brand.api.account.dto.AccountGroupPrivilegeDto;

/**
 * <AUTHOR>
 * @date 2017年2月9日
 */
public interface IAccountGroupService {

    AccountGroupDto load(Integer accountGroupId) throws ServiceException;
    
    List<AccountGroupDto> getAccountGroupByStatus(Integer status) throws ServiceException;
    
    List<AccountGroupDto> getValidAccountGroupsInIds(List<Integer> accountGroupIds);

    Map<Integer, String> getValidAccountGroupId2NameMapInIds(List<Integer> accountGroupIds);

    Map<Integer, AccountGroupDto> getValidAccountGroupMapInIds(List<Integer> accountGroupIds) throws ServiceException;
    
    Map<Integer, List<AccountGroupDto>> getValidAccountGroupMapByAccountIds(List<Integer> accountIds);

    int create(Operator operator, AccountGroupDto dto) throws ServiceException;
    
    void update(Operator operator, AccountGroupDto dto) throws ServiceException;
    
	int updateStatus(Operator operator, Integer accountGroupsId, Integer status) throws ServiceException;

	int createAccountGroupPrivilege(AccountGroupPrivilegeDto dto, Operator operator);

	void saveOrUpdatePrivilege(AccountGroupPrivilegeDto dto, Operator operator);

	Map<Integer, AccountGroupPrivilegeDto> getAccountGroupPrivilegeMapInAccountGroupIds(List<Integer> accountGroupIds);
}
