package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-02-26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteResourceBookingDto {

    private Integer accountId;

    private Integer platformId;

    private Integer pageId;

    private Integer resourceId;

    private Integer sourceId;

    private List<Integer> ids;
}
