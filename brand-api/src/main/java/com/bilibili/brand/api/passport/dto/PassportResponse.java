package com.bilibili.brand.api.passport.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * copy from passport
 *
 * <AUTHOR>
 * @date 2024/1/3 17:19
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PassportResponse<T> {
    public final static Integer SUCCESS = 0;
    private Integer code;
    private T data;
    private String message;
}
