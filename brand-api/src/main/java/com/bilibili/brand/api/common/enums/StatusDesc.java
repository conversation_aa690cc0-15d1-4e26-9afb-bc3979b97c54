package com.bilibili.brand.api.common.enums;

/**
 * <AUTHOR>
 * @date 2016年10月17日
 */
public enum StatusDesc {
	UNKNOWN(0, "未知的状态"),
    VALID(1, "有效"),
    PAUSE(2, "已暂停"),
    DELETE(3, "已删除"),
    NO_START(4, "未开始"),
    OVER(5, "已结束"),
    BUDGET_LIMIT(6, "预算受限"),
    NOT_IN_LAUNCH_TIME(7, "不在投放时段"),
    PENDING(8, "待审核"),
    AUDIT_PASS(9, "审核通过"),
	AUDIT_REJECT(10, "审核拒绝"),
	COMPLETE(11, "已完成");

    private int code;
    private String name;

    private StatusDesc(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public static StatusDesc getByCode(int code) {
        for (StatusDesc budgetType: values()) {
            if (budgetType.code == code) {
                return budgetType;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
