package com.bilibili.brand.api.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/6/22
 */
@Getter
public enum GdPriceTypeEnum {
    CPM(1,"gd-cpm价格"),
    CPV(2,"gd-cpv价格"),
    TOP_VIEW(3,"gd-topView价格"),
    CPT(4, "gd-cpt价格"),
    ADX(5, "gd-adx价格"),
    INLINE_PD_CPM(6, "信息流pd-单个cpm价格"),
    ;

    private final int code;

    private final String desc;

    GdPriceTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
