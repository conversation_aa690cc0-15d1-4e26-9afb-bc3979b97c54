package com.bilibili.brand.api.resource.targetmeta;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by walker on 16/9/13.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TargetItem implements Serializable {

    private static final long serialVersionUID = -3676820995145730846L;

    private int id;

    private int type;
    
    private int subType;

    private String name;

    private int parentId;

    private String mappingContent;

    private int sortOrder;

    private int status;
    
    private int subtype;

}
