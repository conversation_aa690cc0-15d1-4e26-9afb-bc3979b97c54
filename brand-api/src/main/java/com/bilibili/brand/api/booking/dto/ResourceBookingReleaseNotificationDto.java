package com.bilibili.brand.api.booking.dto;

import com.bilibili.adp.common.annotation.MailTableDesc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/3/26 17:30
 **/
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResourceBookingReleaseNotificationDto implements Serializable {

    private static final long serialVersionUID = -784714415955628260L;
    private Integer sourceDayBookId;

    private Integer businessSideId;
    @MailTableDesc(value = "业务方", sort = 1)
    private String businessSideName;

    private Integer sourceId;

    @MailTableDesc(value = "资源位名称", sort = 2)
    private String sourceName;

    private LocalDateTime groupDate;

    @MailTableDesc(value = "资源时间(排期时间)", sort = 3)
    private String groupDateStr;

    private LocalDateTime releaseDate;

    @MailTableDesc(value = "资源释放时间", sort = 4)
    private String releaseDateStr;

    @MailTableDesc(value = "订单ID", sort = 5)
    private Integer orderId;

    @MailTableDesc(value = "订单名称", sort = 6)
    private String orderName;

    @MailTableDesc(value = "排期ID", sort = 7)
    private Integer scheduleId;

    private LocalDateTime sourceBookingTime;

    @MailTableDesc(value = "资源预定时间", sort = 8)
    private String sourceBookingTimeStr;

    @MailTableDesc(value = "资源预定人", sort = 9)
    private String operator;

    @MailTableDesc(value = "资源释放原因", sort = 10)
    private String releaseReason;
}
