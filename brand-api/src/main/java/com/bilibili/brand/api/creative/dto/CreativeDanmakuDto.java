package com.bilibili.brand.api.creative.dto;

import com.bilibili.brand.api.launch.dto.ImageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/11 17:44
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreativeDanmakuDto implements Serializable {
    private static final long serialVersionUID = -2568486968453144609L;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 氛围类型，0：自定义，1：七夕主题
     */
    private Integer atmosphereType;

    /**
     * 按钮动效类型，1：webp，2：lottie
     */
    private Integer btnAnimType;

    /**
     * 按钮动效链接
     */
    private String btnAnimUrl;

    /**
     * 按钮icon
     */
    private ImageDto btnIcon;

    /**
     * 按钮文案
     */
    private String btnText;

    /**
     * 弹幕头图
     */
    private ImageDto dmLeaderIcon;

    /**
     * 弹幕文案色值
     */
    private String dmTextColor;

    /**
     * 弹幕背景色值
     */
    private String dmBgColor;

    /**
     * 弹幕文案，JSON数组
     */
    private List<String> dmContents;
}
