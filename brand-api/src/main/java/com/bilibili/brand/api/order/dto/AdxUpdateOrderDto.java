package com.bilibili.brand.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2018/3/6.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdxUpdateOrderDto {
    private Integer id;
    private String orderName;
    private Long ecpm;
    private Integer premiumRatio;

    private Integer budget;

    private List<AdxOrderLaunchTimeDto> adxOrderLaunchTimes;
}
