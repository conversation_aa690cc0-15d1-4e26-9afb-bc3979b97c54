package com.bilibili.brand.api.common.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/10/13.
 */
public enum MoneyStatus {
    PENDING(0, "待审核") {
        @Override
        public boolean validateToStatus(MoneyStatus toStatus) {
            switch (toStatus) {
                case PENDING:
                    return true;
                case REJECT:
                    return true;
                case ADOPT:
                    return true;
                default:
                    return false;
            }
        }
    },
    REJECT(1, "拒绝") {
        @Override
        public boolean validateToStatus(MoneyStatus toStatus) {
            switch (toStatus) {
                case PENDING:
                    return true;
                default:
                    return false;
            }
        }
    },
    ADOPT(2, "通过") {
        @Override
        public boolean validateToStatus(MoneyStatus toStatus) {
            return false;
        }
    };

    private Integer code;
    private String desc;

    MoneyStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public abstract boolean validateToStatus(MoneyStatus toStatus);

    public static MoneyStatus getByCode(int code) {
        for (MoneyStatus moneyStatus : values()) {
            if (moneyStatus.getCode() == code) {
                return moneyStatus;
            }
        }
        throw new IllegalArgumentException("unknown code.");
    }
}
