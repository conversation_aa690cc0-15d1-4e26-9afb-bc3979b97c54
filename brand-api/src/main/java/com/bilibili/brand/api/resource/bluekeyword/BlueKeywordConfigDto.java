package com.bilibili.brand.api.resource.bluekeyword;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/5/10 15:21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlueKeywordConfigDto implements Serializable {
    private static final long serialVersionUID = -3828342434923501486L;
    /**
     * 配置id
     */
    private Long id;
    /**
     * 配置名称
     */
    private String name;
    /**
     * 开始时间
     */
    private Timestamp beginTime;
    /**
     * 结束时间
     */
    private Timestamp endTime;
    /**
     * 稿件数
     */
    private Integer archiveCount;
    /**
     * 关键词数
     */
    private Integer keywordCount;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 明细版本
     */
    private Integer itemVersion;

    /**
     * 业务场景
     */
    private String bizScene;
}
