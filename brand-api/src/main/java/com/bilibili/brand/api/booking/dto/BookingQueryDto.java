package com.bilibili.brand.api.booking.dto;

import com.bilibili.adp.common.bean.Operator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BookingQueryDto {

    /**
     * 该条件仅在 isMyResource 为 true 时生效
     */
    private Integer accountId;

    private Integer platformId;

    private Integer pageId;
    private List<Integer> pageIds;

    private Integer resourceId;
    private List<Integer> resourceIds;

    private Integer sourceId;
    private List<Integer> sourceIds;

    private Timestamp beginTime;

    private Timestamp endTime;

    private List<Timestamp> groupDates;

    private Operator operator;
    private List<Integer> status;
    private boolean isMyResource;

    private Integer scheduleId;

    private String operatorName;

    //1：天，2：半小时
    private Integer timeType;


}
