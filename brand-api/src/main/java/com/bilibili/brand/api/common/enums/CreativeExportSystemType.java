package com.bilibili.brand.api.common.enums;

/**
 * Created by many2023 on 2017/5/22.
 */
public enum CreativeExportSystemType {
    CPT("CPT", "CPT"),
    CPM("CPM", "CPM"),
    GD("GD", "GD");

    private String code;
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CreativeExportSystemType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CreativeExportSystemType getByCode(String code) {
        for (CreativeExportSystemType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code.");
    }
}
