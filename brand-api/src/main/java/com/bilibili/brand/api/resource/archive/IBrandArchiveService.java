package com.bilibili.brand.api.resource.archive;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/16 19:49
 */
public interface IBrandArchiveService {
    Long createBrandArchive(BrandArchiveDto brandArchiveDto, Operator operator);

    void updateBrandArchive(BrandArchiveDto notice);

    PageResult<BrandArchiveDto> queryBrandArchive(BrandArchiveQueryDto query);

    /**
     * 转码
     *
     * @param idList 可为空，为空的情况下，则全量转码，否则转码指定稿件
     */
    void transferBrandArchive(List<Long> idList);

    /**
     * 给指定的稿件打标
     *
     * @param idList        主键id
     * @param includeNoMark true:包含无需打标的记录（主要方便后续紧急情况下，可以针对无需打标的稿件进行打标），false:排除无需打标的记录
     */
    void addBrandArchiveMark(List<Long> idList, boolean includeNoMark);

    BrandArchiveDto getBrandArchiveByCid(Long cid);
}
