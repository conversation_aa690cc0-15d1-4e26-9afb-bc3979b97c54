package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/26 14:01
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateHourResourceBookingDto implements Serializable {

    private static final long serialVersionUID = 6649505807060074169L;

    private Integer dayBookingId;

    private Integer sourceId;

    private Timestamp groupDate;

    private List<Integer> seq;

    private Boolean isUniteBooking;
}
