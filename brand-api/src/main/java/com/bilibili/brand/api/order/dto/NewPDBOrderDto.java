package com.bilibili.brand.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by fan<PERSON><PERSON> on 2018/3/8.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewPDBOrderDto implements Serializable {

    private static final long serialVersionUID = 2684530636280055345L;

    private String orderName;

    private Long crmContractNumber;

    private Integer resourceType;
}
