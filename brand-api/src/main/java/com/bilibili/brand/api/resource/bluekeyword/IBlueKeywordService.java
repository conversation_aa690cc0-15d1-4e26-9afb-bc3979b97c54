package com.bilibili.brand.api.resource.bluekeyword;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.comment.CommentBlueKeywordQueryDto;

import java.util.List;
import java.util.Map;

/**
 * 小蓝词配置
 *
 * <AUTHOR>
 * @date 2024/5/10 15:18
 */
public interface IBlueKeywordService {
    //保存小蓝词配置
    Long saveBlueKeywordConfig(BlueKeywordConfigDto config, Operator operator) throws Exception;

    //启用小蓝词配置
    Long enableBlueKeywordConfig(Long id, Operator operator) throws Exception;

    //禁用小蓝词配置
    Long disableBlueKeywordConfig(Long id, Operator operator) throws Exception;

    //仅查询可见小蓝词配置
    BlueKeywordConfigDto getVisibleBlueKeywordConfig(Long id);

    PageResult<BlueKeywordConfigDto> getBlueKeywordConfigList(BlueKeywordConfigQueryDto query);

    //上传小蓝词配置明细（稿件和关键词）
    BlueKeywordConfigDto uploadBlueKeywordConfigItem(BlueKeywordConfigItemDto item, Operator operator) throws Exception;

    //上传小蓝词配置明细（稿件和关键词）
    Map<Long, BlueKeywordConfigItemDto> getBlueKeywordConfigItem(List<Long> configIdList);

    //根据稿件id查询对应的配置项
    //一个稿件可能配置在多个配置里
    Map<Long, List<BlueKeywordConfigDto>> getArchiveBlueKeywordConfig(List<Long> avidList);


    //刷新小蓝词配置
    //1、开始投放
    //1.1、添加到archive-extra服务中
    //1.2、构建缓存
    //2、结束投放
    //2.1、从archive-extra服务中移除
    //2.2、从缓存中移除
    void refreshBlueKeywordConfig() throws Exception;

    //一阶段创建小蓝词配置
    Long saveBlueKeywordConfigV2(BlueKeywordConfigDto config, BlueKeywordConfigItemDto item, Operator operator) throws Exception;

}
