package com.bilibili.brand.api.common.enums;

/**
 * Created by walker on 16/9/19.
 */
public enum FrequencyUnit {
    DAY(1, "每日"),
    WEEK(2, "每周"),
    MONTH(3, "每月"),
    THR_DAY(4, "三日");

    private int code;
    private String message;

    FrequencyUnit(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static FrequencyUnit getByCode(int code) {
        for (FrequencyUnit frequencyUnit : values())  {
            if (frequencyUnit.code == code) {
                return frequencyUnit;
            }
        }

        throw new IllegalArgumentException("can not find FrequencyUnit by code:" + code);
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
