package com.bilibili.brand.api.fb;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20
 **/

@AllArgsConstructor
@Getter
public enum FbResourceType {

    UNKNOWN(0, "未知类型"),
    FLOW_CPT(1, "流量非标-CPT"),
    FLOW_CPM(2, "流量非标-CPM"),
    CONTENT(3, "内容非标（包装权益/植入权益）"),
    PAGE(4, "页面非标"),
    OTHER(5, "其他"),
    CUSTOMIZE(6, "自定义"),
    ;

    private final Integer code;

    private final String desc;

    public static FbResourceType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(resourceType -> Objects.equals(resourceType.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(String.format("资源类型「%s」不存在", code)));
    }

    public static boolean needProjectLevel(Integer code) {
        return Objects.equals(CONTENT.getCode(), code)
                || Objects.equals(PAGE.getCode(), code);
    }

    public static FbResourceType getByCodeWithoutExp(Integer code) {
        return Arrays.stream(values())
                .filter(resourceType -> Objects.equals(resourceType.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public static final List<Integer> allowDeleted = Lists.newArrayList(
            FLOW_CPT.getCode(),
            FLOW_CPM.getCode(),
            CONTENT.getCode(),
            PAGE.getCode(),
            OTHER.getCode());

}
