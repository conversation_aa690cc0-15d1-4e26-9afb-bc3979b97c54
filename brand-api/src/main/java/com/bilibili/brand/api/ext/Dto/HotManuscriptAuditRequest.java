package com.bilibili.brand.api.ext.Dto;

import com.bilibili.brand.api.passport.dto.PassportBaseRequest;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
public class HotManuscriptAuditRequest extends PassportBaseRequest {

    private Long aid;

    private Integer reset;

    private Integer type;

    public HotManuscriptAuditRequest(Long aid, PassportBaseRequest passportBaseRequest) {
        this.setAid(aid);
        this.setReset(0);
        this.setType(43);
        this.setAppkey(passportBaseRequest.getAppkey());
        this.setSign(passportBaseRequest.getSign());
        this.setTs(passportBaseRequest.getTs());
    }

}
