package com.bilibili.brand.api.schedule.dto;

import com.bilibili.brand.api.resource.price.gd.PriceDto;
import com.bilibili.brand.api.stock.BrushStockDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by xiongyan on 2020/9/20.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
//库存查询返回信息
public class StockDto implements Serializable {

    private static final long serialVersionUID = 19701134460103103L;

    @ApiModelProperty("库存")
    private Long stockCpm;

    //刷次库存信息
    private List<BrushStockDto> brushStockDtoS;

}
