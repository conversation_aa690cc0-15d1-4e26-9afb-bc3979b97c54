package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020.11.16 19:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeiXinGdInfoDTO implements Serializable {

    private static final long serialVersionUID = -6015126055301664692L;

    private String msgtype;

    private WeiXinTextDTO markdown;
}
