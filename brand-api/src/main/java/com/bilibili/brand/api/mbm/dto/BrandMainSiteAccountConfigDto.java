package com.bilibili.brand.api.mbm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/24 11:58
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMainSiteAccountConfigDto implements Serializable {
    private static final long serialVersionUID = -1589338167969027038L;
    /**
     * id
     */
    private Long id;

    /**
     * mid
     */
    private Long mid;

    /**
     * 用户昵称
     */
    private String userName;

    /**
     * 权限组
     */
    private List<BrandMainSiteAccountPermissionDto> permissions;
}
