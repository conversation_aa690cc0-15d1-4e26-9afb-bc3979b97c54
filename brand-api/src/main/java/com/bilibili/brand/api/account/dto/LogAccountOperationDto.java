package com.bilibili.brand.api.account.dto;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年3月1日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogAccountOperationDto{

    private Integer id;
    private Integer accountId;
    private String userName;
    private Integer modifyType;
    private Integer operatorId;
    private String operatorName;
    private Integer systemType;
    private Integer operatorType;
    private String remark;
    private UpdateAccountDto accountInfo;
    private Date addTime;
    
}
