package com.bilibili.brand.api.ext.Dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UpActReserveRelationInfoDto implements Serializable {

    private static final long serialVersionUID = -6976357689236771787L;

    //预约id
    private Long sid;

    //预约标题
    private String name;

    //预约状态 100为有效状态 见https://info.bilibili.co/pages/viewpage.action?pageId=243887005
    private Long state;

    //计划开始时间
    private Long livePlanStartTime;
}
