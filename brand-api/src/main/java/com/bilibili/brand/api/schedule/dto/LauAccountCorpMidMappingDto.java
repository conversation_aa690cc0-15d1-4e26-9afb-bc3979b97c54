package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauAccountCorpMidMappingDto {

    private Integer accountId;

    private Integer id;

    private Long mid;

    private String corpFace;

    private String corpName;

    private String spaceUrl;
}

