package com.bilibili.brand.api.component;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/2/9 21:02
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComponentDto implements Serializable {
    private static final long serialVersionUID = 1062588302448127062L;
    /**
     * 组件id
     */
    private Long componentId;

    /**
     * 组件名称
     */
    private String componentName;

    /**
     * 组件类型，0：图片，默认0
     */
    private Integer componentType;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 图片md5
     */
    private String imageMd5;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 组件跳转链接
     */
    private String jumpUrl;

    /**
     * 组件唤起链接
     */
    private String schemaUrl;

    /**
     * 组件标链接，如果为空则表示无标
     */
    private String markUrl;

    /**
     * 视频id
     */
    private Integer videoId;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 视频md5
     */
    private String videoMd5;

    /**
     * 视频时长，单位ms
     */
    private Integer videoDuration;

    /**
     * 是否展示关闭按钮，0：不展示 1：展示
     */
    private Integer showCloseButton;
}
