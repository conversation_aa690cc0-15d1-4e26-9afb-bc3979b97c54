package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2019/5/7.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryScheduleDto implements Serializable {
    private static final long serialVersionUID = -4623169719355083171L;

    private Integer accountId;

    private List<Integer> notInAccountIds;

    private List<Integer> orderIds;

    private List<Integer> scheduleIds;

    private List<Integer> statusList;

    private List<Integer> salesTypes;

    private String nameLike;

    private Timestamp startTime;

    private Timestamp endTime;

    private String sourceNameLike;

    private String templateNameLike;

    private List<Integer> templateIds;

    private List<Integer> promotionPurposeTypeList;

    private List<Integer> sourceIds;

    //按钮类型
    private Integer buttonStyle;

    private Integer postType;

    private Long avid;

    private List<Integer> platformIds;

    //这个字段决定了gdBeginTime + gdEndTime的查询返回信息
    //如果timeContains等于true,那么db的数据必须完全在gdBeginTime～gdEndTime之间
    //如果timeContains等于false,那么db的数据只要和gdBeginTime～gdEndTime区间有交集就可以
    private Boolean timeContains = true;

    private Timestamp gdBeginTime;

    private Timestamp gdEndTime;

    private Timestamp beginDate;

    private Timestamp endDate;

    private List<Integer> orderProducts;

    private Timestamp mTime;

    private String scheduleName;

    private boolean withoutTempSchedule;

    private String orderBy;

    private Timestamp equalBeginDate;

    private Integer finishFlag;

    private Integer ssaVideoPlayMode;

    private Integer jumpAreaEffect;

    private Integer launcher;
    private Integer effectiveType;


}
