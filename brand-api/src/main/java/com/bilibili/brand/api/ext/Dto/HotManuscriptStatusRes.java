package com.bilibili.brand.api.ext.Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotManuscriptStatusRes implements Serializable {

    private static final long serialVersionUID = 7481698004778232887L;

    private Integer code;

    private String message;

    private Long ttl;

    private HotManuscriptStatusDto data;


}
