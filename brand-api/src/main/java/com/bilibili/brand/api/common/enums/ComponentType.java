package com.bilibili.brand.api.common.enums;

import com.bilibili.brand.api.component.ComponentDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2023/2/9 22:01
 */
@Getter
@AllArgsConstructor
public enum ComponentType {
    STORY_IMAGE_FLIP(0, "story图片翻转卡") {
        @Override
        public void check(ComponentDto component) {
            Assert.notNull(component, "component is null");
            Assert.hasText(component.getComponentName(), "组件名称必填");
            Assert.hasText(component.getImageUrl(), "组件图片必填");
            Assert.hasText(component.getImageMd5(), "组件图片（MD5）必填");
        }
    },
    STORY_BREAK_FRAME(1, "story破框展示") {
        @Override
        public void check(ComponentDto component) {
            Assert.notNull(component, "component不能为空");
            Assert.hasText(component.getComponentName(), "组件名称必填");
            Assert.hasText(component.getImageUrl(), "组件图片必填");
            Assert.hasText(component.getImageMd5(), "组件图片（MD5）必填");
            Assert.notNull(component.getVideoId(), "破框视频ID不能为空");
            Assert.hasText(component.getVideoUrl(), "破框视频URL不能空");
            Assert.hasText(component.getVideoMd5(), "破框视频MD5不能为空");
            Assert.notNull(component.getVideoDuration(), "破框视频时长不能为空");
            Assert.notNull(component.getShowCloseButton(), "是否展示关闭按钮不能为空");
        }
    },
    ;

    private final int code;

    private final String name;

    public abstract void check(ComponentDto component);

    public static ComponentType getByCode(int code) {
        for (ComponentType componentType : values()) {
            if (componentType.code == code) {
                return componentType;
            }
        }
        throw new IllegalArgumentException("unknown component type:" + code);
    }

    public static ComponentType getByCodeWithoutEx(int code) {
        for (ComponentType componentType : values()) {
            if (componentType.code == code) {
                return componentType;
            }
        }
        return null;
    }
}
