package com.bilibili.brand.api.resource.slot_group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewSlotGroupDto implements Serializable{

	private static final long serialVersionUID = 3882303547082934975L;
	private String slotGroupName;

	private Integer channelId;
	
	private List<Integer> sysTypes;

}
