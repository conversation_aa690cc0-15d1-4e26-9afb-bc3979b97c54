package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Creative3dModelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 原始文件名
     */
    private String fileName;

    /**
     * 模型类型
     */
    private String type;

    /**
     * 模型链接
     */
    private String url;

    /**
     * 模型md5
     */
    private String md5;
}
