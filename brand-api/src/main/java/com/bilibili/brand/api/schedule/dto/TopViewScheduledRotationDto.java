package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewScheduledRotationDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 投放时间
     */
    private Timestamp launchDate;

    /**
     * 投放轮数
     */
    private Integer rotationNum;

    /**
     * 投放轮数
     */
    private Integer bookingRatio;
}
