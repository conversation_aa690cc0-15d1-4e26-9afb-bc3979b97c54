package com.bilibili.brand.api.schedule.service;

import com.bilibili.brand.api.schedule.dto.NewScheduleDto;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
public interface IGdPlusScheduleValidate {

    void checkCreateScheduleBaseInfo(NewScheduleDto newScheduleDto);

    void validateQotaMargin(long expectTotalPrice, Integer accountId, List<Timestamp> dates);
}
