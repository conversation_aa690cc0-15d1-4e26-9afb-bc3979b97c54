/** 
* <AUTHOR> 
* @date  2018年6月6日
*/ 

package com.bilibili.brand.api.launch.dto;

import java.util.List;

import com.bilibili.adp.common.util.Page;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryAccountInfoDto {
	private Integer id;
	private List<Integer> ids;

    private Integer accountId;
    private List<Integer> accountIds;

    private String brandName;
    private String likeBrandName;
    
    private Page page;
    private String orderBy;
}
