package com.bilibili.brand.api.account.dto;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccAccountWalletDayLogAgentStatDto implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -8822706510037340539L;
	
	private Integer accountId;
	private String date;
	private Long charge;
	private Long redPacket;
	private Long turnIn;
	private Long turnOut;
	private Long cashConsume;
	private Long redPacketConsume;
	private Integer activeCustomerCount;
	
	public static AccAccountWalletDayLogAgentStatDto getEmpty() {
		return AccAccountWalletDayLogAgentStatDto
				.builder()
				.accountId(0)
				.redPacket(0L)
				.charge(0L)
				.turnIn(0L)
				.turnOut(0L)
				.cashConsume(0L)
				.redPacketConsume(0L)
				.activeCustomerCount(0)
				.build();
	}
}
