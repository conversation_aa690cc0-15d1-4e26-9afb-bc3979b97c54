package com.bilibili.brand.api.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/4/8 17:57
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimeChangeInfo implements Serializable {
    private static final long serialVersionUID = 4888602642242607714L;
    //修改前的日期
    private Set<Timestamp> oldDays;
    //修改后的日期
    private Set<Timestamp> newDays;
    //需要删除的日期
    private Set<Timestamp> deleteDays;
    //需要新增的日期
    private Set<Timestamp> addDays;
    //是否发生变化
    private boolean changed;
}
