package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description: 预约结果
 * @author: wangbin01
 * @create: 2019-01-29
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptScheduleSplitTimeDayDto {

    /**
     * 日期
     */
    private String day;

    private String sourceName;

    /**
     * 位次
     */
    private List<CptScheduleSplitTimeHourDto> hourDtoList;

}
