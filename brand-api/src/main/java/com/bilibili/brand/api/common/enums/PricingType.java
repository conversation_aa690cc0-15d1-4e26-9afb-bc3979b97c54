package com.bilibili.brand.api.common.enums;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/9/29.
 *
 * 定价类型
 */
public enum PricingType {

    NONE(0),            // 不影响定价
    BASE(1),            // 基价
    TARGET_PLUS_PERCENT(2),    // 定向百分比加价
    LIMIT_PLUS_PERCENT(3),
    ;    // 频次百分比加价

    private final int code;

    public static PricingType getByCode(int code) {
        for (PricingType pricingType : values()) {
            if (pricingType.code == code) {
                return pricingType;
            }
        }

        return NONE;
    }

    PricingType(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
