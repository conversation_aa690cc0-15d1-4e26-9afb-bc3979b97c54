package com.bilibili.brand.api.schedule.dto;

import com.bilibili.brand.dto.cycle.CycleDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * migrate from cpt-api
 *
 * <AUTHOR>
 * @date 2023/9/16 10:53
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptScheduleDto implements Serializable {
    private static final long serialVersionUID = -5792552996335525874L;
    private Integer id;

    private String name;

    /**
     * cpt订单id
     */
    private Integer gdOrderId;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台
     */
    private String platformName;

    /**
     * 页面id
     */
    private Integer pageId;

    /**
     * 页面
     */
    private String pageName;

    /**
     * 位置id
     */
    private Integer resourceId;

    /**
     * 位置
     */
    private String resourceName;

    /**
     * 广告位id
     */
    private Integer sourceId;

    /**
     * 位次
     */
    private String sourceName;

    private String level;

    private Integer status;

    /**
     * 业务方id
     */
    private Integer businessSideId;

    /**
     * 开始日期
     */
    private Timestamp beginDate;

    /**
     * 结束日期
     */
    private Timestamp endDate;

    /**
     * 外部价,单位(分)
     */
    private Long externalPrice;

    /**
     * 内部价,单位(分)
     */
    private Long internalPrice;

    private Integer cycleId;

    private Integer isDistribution;

    private Integer accountId;

    /**
     * 售卖类型: 21-GD 31-CPT
     */
    private Integer salesType;

    /**
     * 投放目的 APP(1, "APP推广"),LANDING_PAGE(2, "落地页"), VIDEO(3, "视频页");
     */
    private Integer promotionPurposeType;

    private Integer needWakeApp;

    /**
     * APP包ID
     */
    private Integer appPackageId;

    private List<CptScheduleDateDto> scheduleDateDtos;

    //人群包
    private List<Integer> crowdPackIds;

    //排除的人群包
    private List<Integer> excludeCrowdPackIds;

    //稿件定向 0-无定向 1-支持定向
    private Integer manuscriptTarget;

    //稿件定向视频项
    private String manuscriptTargetItems;

    /**
     * 投放主体 0-蓝v号 1-自定义 此字段仅用于搜索cpt
     */
    private Integer launcher;

    /**
     * 投放主体 0-蓝v号 1-自定义 此字段仅用于搜索cpt
     */
    private String launcherDesc;

    //
    private Long mid;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * 产品类型
     */
    private Integer orderProduct;

    /**
     * 版本类型 仅用于直播cpt 0-标准版 1-增强版
     */
    private Integer versionType;

    /**
     * 排期方式 0-按天 1-按小时
     */
    private Integer scheduleStyle;

    private Boolean launchInnerJump;

    /**
     * 展示优先级，0：常规展示，1：优先展示 2:优先限流展示
     */
    private Integer showPriority;


    /**
     * 词包
     */
    private List<KeywordsPackageDto> keywordPackages;

    //ogv信息，OGV-CPT/OGV-GD订单类型会存在
    private ScheduleOgvDto ogv;

    //唤起应用类型描述
    private String wakeAppTypeDesc;

    //刊例周期
    private CycleDto cycle;

    /**
     * 品专类型
     */
    private Integer bizSalesType;

    /**
     * 品专类型描述
     */
    private String bizSalesTypeDesc;

    /**
     * 目标展示量
     */
    private Integer targetCpm;

    /**
     * 实际展示量
     */
    private Integer actualCpm;

    /**
     * 实际差量
     */
    private Integer differenceCpm;
}
