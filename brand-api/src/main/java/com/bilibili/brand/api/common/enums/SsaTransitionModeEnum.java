package com.bilibili.brand.api.common.enums;

import lombok.Getter;

/**
 * 闪屏过渡形式枚举
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Getter
public enum SsaTransitionModeEnum {

    NORMAL(0, "普通过渡"),

    CUSTOM(1, "自定义过渡"),
    ;

    private final Integer code;

    private final String desc;

    SsaTransitionModeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SsaTransitionModeEnum getByCode(Integer code) {
        for (SsaTransitionModeEnum value : SsaTransitionModeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return NORMAL;
    }

    /**
     * 是否是自定义过渡
     */
    public static boolean isCustom(Integer code) {
        return CUSTOM.getCode().equals(code);
    }

    /**
     * 是否是普通过渡
     */
    public static boolean isNormal(Integer code) {
        return NORMAL.getCode().equals(code);
    }
}
