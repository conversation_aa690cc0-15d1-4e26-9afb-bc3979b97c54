package com.bilibili.brand.api.stock.dto.fly;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlyStockTargetInfo implements Serializable {
    private static final long serialVersionUID = -237057123180261274L;
    private Integer accountId;
    private List<Long> scheduleIds;
    private FlyTargetingInfo targetingInfo;
    private List<Timestamp> scheduleDate;
    private Integer templateId;
    private List<Integer> platformIds;
    private Integer frequencyUnit;
    private Integer frequencyLimit;
    private String dealSeq;
    private Integer areaGroupId;
    private boolean isToday;
    private Integer hour;
}
