package com.bilibili.brand.api.comment;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/13 20:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommentBlueKeywordRefreshDto implements Serializable {
    private static final long serialVersionUID = 7896077239480738734L;
    //小蓝词配置id
    private Long configId;
    //小蓝词配置投放开始时间，主要是做排序用
    private Timestamp configBeginTime;
    //稿件id
    private List<Long> avidList;
    //关键词
    private List<String> keywordList;
    //需要删除的稿件
    private List<Long> toRemoveAvidList;
}
