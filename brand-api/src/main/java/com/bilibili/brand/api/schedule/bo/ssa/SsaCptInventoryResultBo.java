package com.bilibili.brand.api.schedule.bo.ssa;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SsaCptInventoryResultBo {

    private boolean alreadyFinish;

    private List<SsaCptInventoryDetailBo> inventoryDetails;

}
