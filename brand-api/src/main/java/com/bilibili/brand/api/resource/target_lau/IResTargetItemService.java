package com.bilibili.brand.api.resource.target_lau;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.brand.api.resource.target_lau.dto.NewTargetItemDto;
import com.bilibili.brand.api.resource.target_lau.dto.QueryTargetItemParamDto;
import com.bilibili.brand.api.resource.target_lau.dto.ResTargetItemDto;
import com.bilibili.brand.api.resource.target_lau.dto.TargetItemTreeDto;
import com.bilibili.brand.api.resource.target_lau.dto.TargetTreeDto;
import com.bilibili.brand.api.resource.target_lau.dto.UpdateTargetItemDto;
import com.bilibili.brand.api.resource.targetmeta.TargetType;

import java.util.List;
import java.util.Map;

public interface IResTargetItemService {
    List<ResTargetItemDto> getItemByTargetType(Integer type);

    List<ResTargetItemDto> getItemByTargetTypeAndMapping(Integer type, List<String> mapping);

    List<ResTargetItemDto> getValidItemByTargetType(Integer type);
    List<Integer> getValidAreaTargetItemIdsInIds(List<Integer> cityIds);

    List<TargetTreeDto> getTargetTreeExcludeItemIds(List<TargetItemTreeDto> targetItemTreeList, List<Integer> excludeItemIds, List<Integer> selfBindedItemIds);

    List<ResTargetItemDto> getTargetItemsInIds(List<Integer> itemIds);

    List<ResTargetItemDto> getValidTargetItemsInIds(List<Integer> itemIds);
    Map<Integer, String> getTargetItemId2NameMapInIds(List<Integer> itemIds);

    String getTargetsString(List<Integer> targetIds);

    List<TargetItemTreeDto> getItemTreesByType(Integer targetType);

    List<TargetItemTreeDto> getValidItemTreesByType(Integer targetType, Integer orderProduct);


    Map<TargetType, List<TargetTreeDto>> getAllValidTarget2ItemTreeMap();

    Integer create(Operator operator, NewTargetItemDto newTargetItemDto);

    void update(Operator operator, UpdateTargetItemDto updateTargetItemDto) ;

    void enable(Operator operator, Integer itemId) ;

    void disable(Operator operator, Integer itemId) ;

	List<TargetRule> validateTarget(SalesType salesType, List<TargetRule> targetRules) ;

    List<ResTargetItemDto> getTargetItems(QueryTargetItemParamDto queryTargetItemParamDto);

    List<ResTargetItemDto> getTargetsByFuzzyName(Integer targetType, String targetName);

	Map<Integer, String> getAllItemNameMap();

    void delete(Operator operator, Integer itemId) ;

}
