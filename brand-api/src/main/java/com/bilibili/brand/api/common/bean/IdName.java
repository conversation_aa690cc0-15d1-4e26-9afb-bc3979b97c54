package com.bilibili.brand.api.common.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26 21:33
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IdName implements Serializable {
    private static final long serialVersionUID = 3988694505518133183L;
    private Long id;
    private String name;
    private List<IdName> child;
}
