package com.bilibili.brand.api.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/3/9.
 */
@AllArgsConstructor
public enum AdxOrderType {
    GD(0,"GD订单"),
    CPT(1, "CPT订单"),//brand cpt =2
    PD(2, "PD订单"),
    GD_CPM(3, "GD-CPM"),
    INVIT(4, "邀约广告"),
    SSA_CPT(5, "闪屏CPT"),
    TAIL_OF_PD(6, "尾量PD订单"),
    SSA_PD(7, "闪屏PD"),
    SSA_PDB(8,"闪屏PDB"),
    SSA_OTT_PD(9, "OTT闪屏PD订单"),
    SSA_OTT_PDB(10, "OTT闪屏PDB订单"),
    ;
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static AdxOrderType getByCode(int code) {
        for (AdxOrderType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code OrderType" + code);
    }
}
