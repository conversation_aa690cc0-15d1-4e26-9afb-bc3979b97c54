package com.bilibili.brand.api.stock.dto.fly;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlyTargetingInfo implements Serializable {
    private static final long serialVersionUID = -237057123180261274L;
    private List<Integer> area;
    private List<Integer> os;
    private List<Integer> gender;
    private List<Integer> age;
    private List<Integer> inlineSalesType;
}
