package com.bilibili.brand.api.common.enums;

/**
 * <AUTHOR>
 * @date 2016年10月1日
 */
public enum MaterialType {
	
    IMAGE(1, "image"), 
    EXT_IMAGE(2, "ext_image"), 
    AVID(3, "avid"),
    VIDEO(4, "video"),
    GIF(5, "gif");

    private Integer code;

    private String desc;

    MaterialType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getCode() {
        return code;
    }

    public final static MaterialType getByCode(Integer code) {
        for (MaterialType creativeType : MaterialType.values()) {
            if (creativeType.getCode().equals(code)) {
                return creativeType;
            }
        }
        return null;
    }
}
