package com.bilibili.brand.api.stock.dto.ssa;

import lombok.*;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QueryStockResponseDto implements Serializable {

    private static final long serialVersionUID = -5723982006985178797L;

    private String request_id;

    private String date;

    private List<StockTaskDto> tasks;

    private Integer code;

    private String err_msg;

}
