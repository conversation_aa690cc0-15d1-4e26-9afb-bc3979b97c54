package com.bilibili.brand.api.common.enums;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.cpt.platform.common.AdxBidMode;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 0 CPM 1 CPC 2 CPT 3 GD-CPM
 * Created by <PERSON><PERSON><PERSON> on 2016/12/12.
 */
@AllArgsConstructor
@Getter
public enum OrderProduct {

    CPM(0, "CPM", OrderType.ADVANCE_PAYMENT, SalesType.CPM, 0, "", null),
    CPC(1, "CPC", OrderType.ADVANCE_PAYMENT, SalesType.CPC, 0, "", null),
    CPT(2, "CPT", OrderType.AFTER_PAYMENT, SalesType.CPT, 1, "", null),
    GD_CPM(3, "GD-CPM", OrderType.AFTER_PAYMENT, SalesType.BRAND_AFTER_PAY_GD_PLUS, 4,
            SystemConfigEnum.GD_TEMPLATE_WHITE_LIST.getCode(), Arrays.asList(AdxBidMode.PDB.getCode(), AdxBidMode.PD_PDB.getCode())),
    INVIT(4, "邀约广告", OrderType.AFTER_PAYMENT, SalesType.CPT, 0, "", null),
    SSA_CPT(5, "闪屏CPT", OrderType.AFTER_PAYMENT, SalesType.SSA_CPT, 6, "", null),
    SSA_GD(6, "闪屏GD", OrderType.AFTER_PAYMENT, SalesType.SSA_GD, 8, "", null),
    TOP_VIEW_CPT(7, "TopView-CPT", OrderType.AFTER_PAYMENT, SalesType.TOP_VIEW_CPT,
            11, "", null),
    SEARCH_CPT(8, "SEARCH_CPT", OrderType.AFTER_PAYMENT, SalesType.CPT, 12, "", null),
    SSA_OTT_CPT(9, "SSA_TV_CPT", OrderType.AFTER_PAYMENT, SalesType.SSA_CPT_PLUS, CrmOrderType.OTT_TOP_VIEW.getCode(), "", null),
    OTT_GD(10, "TV_GD", OrderType.AFTER_PAYMENT, SalesType.BRAND_AFTER_PAY_GD_PLUS, 4,
            SystemConfigEnum.OTT_GD_TEMPLATE_WHITE_LIST.getCode(), null),
    TOP_FLOW_GD(11, "顶流", OrderType.AFTER_PAYMENT, SalesType.CPT, 4,
            SystemConfigEnum.TOP_FLOW_TEMPLATE_WHITE_LIST.getCode(), null),
    LIVE_CPT(12, "直播CPT", OrderType.AFTER_PAYMENT, SalesType.CPT, 14, "", null),
    COMIC_SSA_CPT(13, "漫画闪屏", OrderType.AFTER_PAYMENT, SalesType.SSA_CPT, 6, "", null),
    FLY_GD(14, "起飞GD", OrderType.AFTER_PAYMENT, SalesType.GD, 15,
            SystemConfigEnum.FLY_GD_TEMPLATE_WHITE_LIST.getCode(), null),
    SSA_CPM(15, "闪屏cpm", OrderType.AFTER_PAYMENT, SalesType.SSA_CPM, 16, "", null),
    FLY_GD_PLUS(16, "起飞GD+", OrderType.ADVANCE_PAYMENT, SalesType.FLY_PRE_PAY_GD_PLUS, 15, "", null),
    SSA_CPT_PLUS(17, "闪屏CPT+", OrderType.AFTER_PAYMENT, SalesType.SSA_CPT_PLUS, 6, "", null),
    SSA_GD_PLUS(18, "闪屏GD+", OrderType.AFTER_PAYMENT, SalesType.SSA_GD_PLUS, 8, "", Arrays.asList(AdxBidMode.SSA_PDB.getCode(), AdxBidMode.SSA.getCode())),
    TOP_VIEW_CPT_PLUS(19, "TopView-CPT+", OrderType.AFTER_PAYMENT, SalesType.TOP_VIEW_PLUS, 11, "", null),
    STANDARD_LIVE_CPT(20, "标准化直播CPT", OrderType.AFTER_PAYMENT, SalesType.CPT, 1, "", null),
    SSA_PD(21, "闪屏PD", OrderType.AFTER_PAYMENT, SalesType.ADX_SPLASH_CPM, CrmOrderType.SSA_PD.getCode(),
            "", Arrays.asList(AdxBidMode.SSA_PD.getCode(), AdxBidMode.SSA.getCode())),

    PD(22, "PD", OrderType.AFTER_PAYMENT, SalesType.PD_TAIL, CrmOrderType.PD.getCode(),
            SystemConfigEnum.PD_TEMPLATE_WHITE_LIST.getCode(), Arrays.asList(AdxBidMode.BRAND_PD.getCode(), AdxBidMode.PD_PDB.getCode())),

    PDB(23, "PDB", OrderType.AFTER_PAYMENT, SalesType.BRAND_AFTER_PAY_GD_PLUS, CrmOrderType.PDB.getCode(),
            SystemConfigEnum.PD_TEMPLATE_WHITE_LIST.getCode(), Arrays.asList(AdxBidMode.PDB.getCode(), AdxBidMode.PD_PDB.getCode())),

    SSA_PDB(24, "SSA_PDB", OrderType.AFTER_PAYMENT, SalesType.SSA_GD_PLUS, CrmOrderType.SSA_PDB.getCode(),
            "", Arrays.asList(AdxBidMode.SSA_PDB.getCode(), AdxBidMode.SSA.getCode())),

    SSA_OTT_GD(25, "TV_SSA_GD", OrderType.AFTER_PAYMENT, SalesType.SSA_GD_PLUS, CrmOrderType.OTT_SSA_GD.getCode(),
            "", Collections.singletonList(AdxBidMode.OTT_SSA_PD_PDB.getCode())),

    TOP_VIEW_GD_PLUS(26, "TopView-GD+", OrderType.AFTER_PAYMENT, SalesType.TOP_VIEW_GD_PLUS,
            CrmOrderType.TOP_VIEW_GD.getCode(), "", null),

    OGV_CPT(27, "OGV标板CPT", OrderType.AFTER_PAYMENT, SalesType.CPT,
            CrmOrderType.OGV_CPT.getCode(), SystemConfigEnum.OGV_TEMPLATE_WHITE_LIST.getCode(), Collections.emptyList()),
    OGV_GD(28, "OGV标板GD", OrderType.AFTER_PAYMENT, SalesType.BRAND_AFTER_PAY_GD_PLUS,
            CrmOrderType.OGV_GD.getCode(), SystemConfigEnum.OGV_TEMPLATE_WHITE_LIST.getCode(), Collections.emptyList()),

    SSA_OTT_PD(29, "TV_SSA_PD", OrderType.AFTER_PAYMENT, SalesType.ADX_SPLASH_CPM, CrmOrderType.SSA_PD.getCode(),
            "", Collections.singletonList(AdxBidMode.OTT_SSA_PD_PDB.getCode())),
    ;

    private final Integer code;

    private final String desc;

    private final OrderType orderType;

    private final SalesType salesType;

    private final Integer crmOrderType;

    private final String GdTemplateConfig;

    //Adx的DSP类型
    private final List<Integer> adxBidModes;

    //品牌投放端能投放的订单类型
    public final static List<Integer> orderList = Lists.newArrayList(OrderProduct.CPT.getCode(),
            OrderProduct.GD_CPM.getCode(),
            OrderProduct.SSA_CPT.getCode(),
            OrderProduct.SSA_GD.getCode(),
            OrderProduct.SEARCH_CPT.getCode(),
            OrderProduct.TOP_VIEW_CPT.getCode(),
            OrderProduct.SSA_OTT_CPT.getCode(),
            OrderProduct.SSA_OTT_GD.getCode(),
            OrderProduct.OTT_GD.getCode(),
            OrderProduct.TOP_FLOW_GD.getCode(),
            OrderProduct.LIVE_CPT.getCode(),
            OrderProduct.OTT_GD.getCode(),
            OrderProduct.COMIC_SSA_CPT.getCode(),
            OrderProduct.SSA_CPM.getCode(),
            OrderProduct.SSA_PD.getCode(),
            OrderProduct.FLY_GD.getCode(),
            OrderProduct.SSA_CPT_PLUS.getCode(),
            OrderProduct.SSA_GD_PLUS.getCode(),
            OrderProduct.TOP_VIEW_CPT_PLUS.getCode(),
            OrderProduct.STANDARD_LIVE_CPT.getCode(),
            OrderProduct.PD.getCode(),
            OrderProduct.TOP_VIEW_GD_PLUS.getCode(),
            OrderProduct.OGV_CPT.getCode(),
            OrderProduct.OGV_GD.getCode(),
            OrderProduct.SSA_OTT_PD.getCode()
    );

    //排期列表按照时间段展示
    public final static List<Integer> TimeSegmentProduct = Lists.newArrayList(OrderProduct.CPT.getCode(),
            OrderProduct.LIVE_CPT.getCode(),
            OrderProduct.GD_CPM.getCode(),
            OrderProduct.PD.getCode(),
            OrderProduct.SSA_GD_PLUS.getCode(),
            OrderProduct.TOP_VIEW_GD_PLUS.getCode(),
            OrderProduct.OGV_CPT.getCode(),
            OrderProduct.OGV_GD.getCode(),
            OrderProduct.SSA_PD.getCode(),
            OrderProduct.SSA_OTT_CPT.getCode(),
            OrderProduct.SSA_OTT_GD.getCode()
    );

    //不支持唤起的订单类型列表
    public final static List<Integer> NO_SCHEMA_PROUDCT = Lists
            .newArrayList(OrderProduct.FLY_GD.getCode(),
                    OrderProduct.OTT_GD.getCode(),
                    OrderProduct.SSA_OTT_CPT.getCode()
            );

    @Deprecated
    //adx类型订单中 不需要在订单纬度(一般在排期纬度)就同步到订单信息到adx的订单类型列表
    public final static List<Integer> NO_SYNC_ADX_WHEN_CREATE_ORDER = Lists
            .newArrayList(OrderProduct.SSA_PD.getCode(),
                    OrderProduct.SSA_GD_PLUS.getCode(),
                    OrderProduct.PD.getCode()
            );

    //允许按设备类型投放
    public final static List<Integer> ALLOW_OS_LAUNCH = Lists
            .newArrayList(OrderProduct.SSA_PD.getCode(),
                    OrderProduct.SSA_GD_PLUS.getCode(),
                    OrderProduct.SSA_CPT_PLUS.getCode(),
                    OrderProduct.TOP_VIEW_CPT_PLUS.getCode(),
                    OrderProduct.TOP_VIEW_GD_PLUS.getCode()
            );


    //TopView订单类型
    public final static Set<Integer> TOPVIEW_CODE_SET = Sets.newHashSet(
            OrderProduct.TOP_VIEW_CPT_PLUS.getCode(),
            OrderProduct.TOP_VIEW_GD_PLUS.getCode());

    //Ssa订单类型
    public final static Set<Integer> SSA_CODE_SET = Sets.newHashSet(
            OrderProduct.SSA_GD_PLUS.getCode(),
            OrderProduct.SSA_CPT_PLUS.getCode());

    public final static Set<Integer> SSA_GD_OR_CPT_SET = Sets.newHashSet(
            OrderProduct.SSA_CPT_PLUS.getCode(),
            OrderProduct.SSA_GD_PLUS.getCode());

    public final static Set<Integer> SSA_PRODUCT_SET = Stream.of(SSA_GD_PLUS.getCode(),
                    SSA_CPT_PLUS.getCode(),
                    SSA_OTT_GD.getCode(),
                    SSA_OTT_CPT.getCode(),
                    SSA_OTT_PD.getCode(),
                    SSA_CPM.getCode(),
                    SSA_PD.getCode(),
                    SSA_PDB.getCode(),
                    COMIC_SSA_CPT.getCode())
            .collect(Collectors.toSet());

    public static OrderProduct getByCode(int code) {
        for (OrderProduct orderProduct : values()) {
            if (orderProduct.getCode() == code) {
                return orderProduct;
            }
        }
        throw new IllegalArgumentException("unknown OrderProduct by code:" + code);
    }

    public static OrderProduct getBySalesType(int code) {
        for (OrderProduct orderProduct : values()) {
            if (orderProduct.getSalesType().getCode() == code) {
                return orderProduct;
            }
        }
        throw new IllegalArgumentException("unknown OrderProduct by salesType:" + code);
    }

    public static OrderProduct getByCodeWithoutEx(Integer code) {
        for (OrderProduct orderProduct : values()) {
            if (orderProduct.getCode().equals(code)) {
                return orderProduct;
            }
        }
        return null;
    }

    //是否是OGV订单类型
    public static boolean isOgv(Integer code) {
        return OGV_CPT.getCode().equals(code) || OGV_GD.getCode().equals(code);
    }

    //是否是闪屏OTT订单类型
    public static boolean isSsaOtt(Integer code) {
        return SSA_OTT_CPT.getCode().equals(code)
                || SSA_OTT_GD.getCode().equals(code)
                || SSA_OTT_PD.getCode().equals(code);
    }

    public static boolean isSsaOtt(OrderProduct orderProduct) {
        return isSsaOtt(orderProduct.getCode());
    }

    public static boolean isTopView(Integer code) {
        return OrderProduct.TOP_VIEW_GD_PLUS.getCode().equals(code) || OrderProduct.TOP_VIEW_CPT_PLUS.getCode().equals(code);
    }

    public static boolean isTopView(OrderProduct orderProduct) {
        return isTopView(orderProduct.getCode());
    }

    //是否是移动端闪屏
    public static boolean isSsa(Integer orderProduct) {
        return Objects.equals(orderProduct, SSA_GD_PLUS.getCode())
                || Objects.equals(orderProduct, SSA_CPT_PLUS.getCode());
    }
}
