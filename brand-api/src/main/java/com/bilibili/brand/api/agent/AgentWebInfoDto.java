package com.bilibili.brand.api.agent;

import com.bilibili.brand.enums.AgentStaticCertificationTypeEnum;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: HHViv
 * @description:
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(value = PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AgentWebInfoDto implements Serializable {

    private static final long serialVersionUID = 3890193552280008226L;

    private Long id;

    private Integer companyId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 服务地区
     */
    private String area;

    /**
     * 联系方式
     */
    private String contactInfo;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 业务范围
     */
    private List<String> businessScope;

    /**
     * 关联词条
     */
    private List<String> relateWord;

    /**
     * 代理资质
     */
    private List<AgentQualificationInfo> agentQualifications;

    /**
     * 服务认证
     */
    private List<AgentCertificationInfo> serviceCertifications;

    private String logoUrl;

    /**
     * 主营行业
     */
    private List<String> mainIndustry;

    private Integer version;

    private Integer isDeleted;
}
