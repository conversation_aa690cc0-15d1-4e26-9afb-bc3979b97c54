package com.bilibili.brand.api.schedule.soa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-08-23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdScheduleDailyDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer crmOrderId;

    /**
     * 排期名称
     */
    private String scheduleName;
    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 模板ID
     */
    private Integer templateId;
    /**
     * 广告位组ID
     */
    private Integer sourceId;
    /**
     * 广告位组名
     */
    private String sourceName;
    /**
     * 时间（天）  yyyy-MM-dd
     */
    private String launchDate;
    /**
     * 预估cpms
     */
    private Integer targetCpm;
    /**
     * 真实cpms
     */
    private Integer actualCpm;

    /**
     * 单个cpms价格
     */
    private Long costPrice;

    /**
     * 金额(单位分)
     */
    private Long amount;


}
