package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * Created by fan<PERSON><PERSON> on 2018/11/24.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GdCreativeDateDto implements Serializable{
    private static final long serialVersionUID = 1813017470119793528L;
    private Long creativeId;
    /**
     * 开始时间
     */
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    private Timestamp endTime;
}
