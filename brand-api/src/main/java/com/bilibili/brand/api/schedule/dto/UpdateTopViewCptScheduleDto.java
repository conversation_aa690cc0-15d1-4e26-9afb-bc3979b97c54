package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTopViewCptScheduleDto {

    /**
     * 排期id
     */
    private Integer scheduleId;

    /**
     * 排期名称
     */
    private String name;

    /**
     * 投放日期
     */
    private List<TopViewScheduledRotationDto> scheduleList;

    /**
     * 唤起外部APP：0-无须唤起 1-需要唤起
     */
    private Integer needWakeApp;

    /**
     * cpt预约的资源位个数
     */
    private Integer cptOrderSize;

    private Integer bookingRatio;

    private Integer targeItem;

    /**
     * 人群包（包含）
     */
    private List<Integer> crowdPackIds;

    /**
     * 人群包（排除）
     */
    private List<Integer> excludeCrowdPackIds;
}
