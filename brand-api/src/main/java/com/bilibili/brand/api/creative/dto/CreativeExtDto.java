package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/12/27 17:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreativeExtDto implements Serializable {
    private static final long serialVersionUID = 3984755692996944923L;
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 订单类型
     */
    private Integer orderProduct;

    /**
     * 一级产品型号
     */
    private Long firstProductLabelId;

    /**
     * 二级产品型号
     */
    private Long secondProductLabelId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;
}
