package com.bilibili.brand.api.schedule.dto;

import com.bilibili.adp.common.bean.TargetRule;
import com.mysema.commons.lang.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/9/25.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BestGroupStockParamDto {

    private Integer accountId;

    private Integer orderId;

    private Integer templateId;

    private List<Integer> platformIds;

    private List<ScheduleTargetDto> scheduleTargetDtos;

    private List<Timestamp> dates;

    private boolean isToday;

    private Integer hour;

    private String requestId;

    /**
     * 创意类型
     * 0-不限 1-静态图文 2-GIF图文 3-静态视频 4-广告位播放视频
     */
    private Integer creativeStyle;

    private Integer frequencyUnit;

    private Integer frequencyLimit;

    //是否分日预约
    private Integer splitDaysFlag;

    //地域组id
    private Integer areaGroupId;

    private List<Long> scheduleIds;

    private Boolean isDynamicTemplate;

    private Integer orderProduct;

    //包含人群包
    private List<Integer> crowdPackIds;

    //人群包（排除）
    private List<Integer> excludeCrowdPackIds;

    private Integer gdType;

    //开始时间+结束时间
    private List<Pair<Timestamp, Timestamp>> timeInfo;

    //排期时间 -》开始时间+结束时间
    private Map<Timestamp, Pair<Timestamp, Timestamp>> timeInfoMap;

    private Map<Pair<Timestamp, Timestamp>, List<TargetRule>> targetMap;

    //推送比例
    private Integer pushRatio;

    private Integer promotionPurposeType;

    private Boolean supportPlayLaunch;

    private boolean fistBrush;

    /**
     * 资源类型 0-其他 1-大卡 2-小卡 3-框下
     */
    private Integer resourceType;

    //此字段针对于起飞,表示是否会出现同一个avid预约多个资源位的情况,如果是1的话，archive_id必填
    //联合资源位预定开关，0：否，1：是")
    private Boolean supportSourceCombineLaunch;

    private Long avid;

    /**
     * @see com.bilibili.cpt.platform.common.GdSalesType
     */
    private Integer gdSalesType;

    private Integer cycleId;

    //优先展示类型
    private Integer showPriority;

    //是否包段
    private Boolean isBookingTimePeriod;

    private Integer wakeAppType;

    private Integer displayMode;

    //是否支持mock库存
    private boolean supportsMock;

    //不包含分时定向的定向即可
    private List<TargetRule> targetRules;
}
