package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15 14:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SplitDaysImpressHolder implements Serializable {
    private static final long serialVersionUID = -888808724728749905L;
    private List<SplitDaysImpressDto> daysImpress;
    //daysImpress中的min
    private Timestamp startTime;
    //daysImpress中的max
    private Timestamp endTime;
    //daysImpress中的sum(impressionCpm)
    private Long totalImpression;
    //daysImpress中的sum(rotationNum)
    private Integer totalRotationNum;
}
