package com.bilibili.brand.api.schedule.bo.information_flow;


import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdInventoryResultBo {

    private List<GdInventoryDetailBo> inventoryDetails;
}
