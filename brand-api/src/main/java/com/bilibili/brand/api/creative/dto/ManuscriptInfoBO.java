package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
//稿件信息
public class ManuscriptInfoBO implements Serializable {

    private static final long serialVersionUID = -5524366329751081429L;

    private Long aid;

    private String coverUrl;

    private String title;

    private Long cid;

    private Long mid;

    /**
     * @see com.bilibili.enums.ManuscriptLaunchSceneEnum
     */
    private Integer scene;

    /**
     * @see com.bilibili.enums.ManuscriptBizTypeEnum
     */
    private Integer bizType;


}
