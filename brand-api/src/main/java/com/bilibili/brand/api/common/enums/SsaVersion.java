package com.bilibili.brand.api.common.enums;

import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.common.enums.*;
import javafx.util.Pair;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/8/26 14:22
 */
public enum SsaVersion {
    ;
    //稿件闪屏版本
    public static final List<Pair<Integer, Integer>> SSA_ARCHIVE_VERSION = Arrays.asList(
            new Pair<>(1, 72700000),
            new Pair<>(2, 7280000));
    //程序化目前不支持ipad_hd，但是因为ipad_hd是跟随ipad的，为了保持代码统一，
    //选择通过逻辑可行性角度限定程序化的版本为最大，间接实现不支持ipad_hd，
    //这远比【程序化选择ipad时不绑定ipad_hd】方案可行性更好、复杂性更低
    public static final List<Pair<Integer, Integer>> SSA_ADX_IPAD_HD_VERSION = Arrays.asList(
            new Pair<>(14, SsaConstants.MAX_VERSION));

    // 点击彩蛋-空降惊喜版本
    public static final List<Pair<Integer, Integer>> CLICK_EGGS_LANDING = Arrays.asList(
            new Pair<>(1, 78100000),
            new Pair<>(2, 7810000),
            new Pair<>(3, 80000000),
            new Pair<>(14, 99999999),
            new Pair<>(15, 99999999));

    // 点击彩蛋-惊喜气球版本
    public static final List<Pair<Integer, Integer>> CLICK_EGGS_BALLOONS = Arrays.asList(
            new Pair<>(1, 80100000),
            new Pair<>(2, 8010000),
            new Pair<>(3, 80100000),
            new Pair<>(14, 99999999),
            new Pair<>(15, 99999999));

    public static final List<Pair<Integer, Integer>> SSA_CUSTOMIZED_GUIDE_WEBP_VERSION = Arrays.asList(
            new Pair<>(1, 81100000),
            new Pair<>(2, 8110000),
            new Pair<>(3, 81100000),
            new Pair<>(14, 999999999),
            new Pair<>(15, 999999999));

    public static final List<Pair<Integer, Integer>> TOP_VIEW_NEW_HF_CUSTOM_BRAND_INFO_VERSION = Arrays.asList(
            new Pair<>(1, 82300000),
            new Pair<>(2, 8230000)
    );

    // 支持topView图片闪频最小版本
    public static final List<Pair<Integer, Integer>> TOP_VIEW_IMAGE_SPLASH_VERSION = Arrays.asList(
            new Pair<>(1, 82200000),
            new Pair<>(2, 8220000)
    );

    public static final List<Pair<Integer, Integer>> SSA_LIVE_BOOKING_VERSION = Arrays.asList(
            new Pair<>(1, 82800000),
            new Pair<>(2, 8280000),
            new Pair<>(3, 82800000),
            new Pair<>(14, 999999999),
            new Pair<>(15, 999999999)
    );

    public static final List<Pair<Integer, Integer>> SSA_LIVE_BOOKING_VERSION_2 = Arrays.asList(
            new Pair<>(1, 83300000),
            new Pair<>(2, 8330000),
            new Pair<>(3, 83300000),
            new Pair<>(14, 999999999),
            new Pair<>(15, 999999999)
    );

    //获取闪屏对应的版本控制
    public static List<Pair<Integer, Integer>> parseVersion(VersionContext context) {
        SsaButtonStyle buttonStyle = SsaButtonStyle.getByCode(context.getButtonStyle());
        OrderProduct orderProduct = OrderProduct.getByCodeWithoutEx(context.getOrderProduct());
        List<Pair<Integer, Integer>> versions = buttonStyle.getVersions();
        //特殊case时的版本，有不同的版本则取其他的
        List<Pair<Integer, Integer>> specialVersions = new ArrayList<>();
        if (Objects.equals(OrderProduct.SSA_CPM, orderProduct)
                || Objects.equals(OrderProduct.SSA_PD, orderProduct)) {
            specialVersions = mergeVersion(specialVersions, SsaButtonStyle.SSA_CPM_BUTTON.getVersions());
        }
        if (SsaVideoPlayModeEnum.isArchive(context.getVideoPlayMode())) {
            specialVersions = mergeVersion(specialVersions, SSA_ARCHIVE_VERSION);
        }
        if (context.isAdx()) {
            specialVersions = mergeVersion(specialVersions, SSA_ADX_IPAD_HD_VERSION);
        }
        boolean isTopCountDown = Optional.ofNullable(context.getComponentTypes())
                .map(cts -> cts.contains(SsaComponentTypeEnum.TOP_COUNT_DOWN.getCode()))
                .orElse(false);
        if (isTopCountDown) {
            specialVersions = mergeVersion(specialVersions, SsaButtonStyle.TOP_COUNT_DOWN_COMPONENT.getVersions());
        }
        if (SsaButtonStyle.isEggButton(context.getButtonStyle())) {
            SsaJumpAreaEffect ssaButtonStyle = SsaJumpAreaEffect.getByCode(context.getSsaJumpAreaEffect());
            if (Objects.equals(SsaJumpAreaEffect.EGGS_LANDING, ssaButtonStyle)) {
                // 空降惊喜
                specialVersions = mergeVersion(specialVersions, CLICK_EGGS_LANDING);
            } else if (Objects.equals(SsaJumpAreaEffect.HEARTBEAT_BALLOONS, ssaButtonStyle)) {
                // 惊喜气球
                specialVersions = mergeVersion(specialVersions, CLICK_EGGS_BALLOONS);
            }
        }
        //自定义webp
        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004312757
        if (Objects.equals(context.getGuideMaterialType(), SsaGuideMaterialTypeEnum.IMAGE.getCode())) {
            specialVersions = mergeVersion(specialVersions, SSA_CUSTOMIZED_GUIDE_WEBP_VERSION);
        }

        //【品牌】闪屏转化能力升级支持直播预约
        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004452040
        if (BooleanUtils.isTrue(context.getIsEnableLiveBooking())) {
            specialVersions = mergeVersion(specialVersions, SSA_LIVE_BOOKING_VERSION);
        }

        //https://www.tapd.cn/67874887/prong/stories/view/1167874887004448078
        //【品牌】闪屏交互彩蛋-CNY节点营销
        //按钮不变，其他交互效果导致版本升级
        if (Objects.equals(context.getEffectiveType(), SsaEffectiveType.INTERACT_EGG.getCode())) {
            specialVersions = mergeVersion(specialVersions, SsaButtonStyle.INTERACT_EGG_TEXT_BUTTON.getVersions());

            // https://www.tapd.cn/67874887/prong/stories/view/1167874887004507824
            // 【品牌】闪屏交互彩蛋支持直播预约
            if (BooleanUtils.isTrue(context.getIsEnableLiveBooking())) {
                specialVersions = mergeVersion(specialVersions, SSA_LIVE_BOOKING_VERSION_2);
            }
        }

        Map<Integer, Integer> topVersion = specialVersions.stream()
                .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        return versions.stream()
                .filter(p -> Objects.isNull(context.getPlatforms())
                        || context.getPlatforms().isEmpty()
                        || context.getPlatforms().contains(p.getKey()))
                .map(pair -> new Pair<>(pair.getKey(), Math.max(pair.getValue(),
                        topVersion.getOrDefault(pair.getKey(), 0))))
                .collect(Collectors.toList());
    }

    //1、不同场景，同一个平台的版本可能不同，因此如果出现相同平台则取版本最大者！！！
    //2、每个元素都是独立，确保不会修改versions中的元素，隔离！！！
    //3、请使用方法返回之后的结果集，而不是参数result！！！
    private static List<Pair<Integer, Integer>> mergeVersion(List<Pair<Integer, Integer>> result,
                                                             List<Pair<Integer, Integer>> versions) {
        Map<Integer, Integer> resultVersion = result.stream().collect(Collectors.toMap(Pair::getKey, Pair::getValue));
        for (Pair<Integer, Integer> version : versions) {
            resultVersion.put(version.getKey(),
                    Math.max(version.getValue(), resultVersion.getOrDefault(version.getKey(), 0)));
        }
        return resultVersion.entrySet().stream()
                .map(entry -> new Pair<>(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class VersionContext {
        /**
         * @see OrderProduct
         */
        private Integer orderProduct;
        @NotNull
        /**
         * @see SsaButtonStyle
         */
        private Integer buttonStyle;
        /**
         * @see SsaVideoPlayModeEnum
         */
        private Integer videoPlayMode;

        /**
         * @see PlatformType
         */
        private List<Integer> platforms;
        /**
         * 是否是程序化
         */
        private boolean adx;

        /**
         * @see SsaComponentTypeEnum
         */
        private List<Integer> componentTypes;

        /**
         * @see com.bilibili.ssa.platform.common.enums.SsaJumpAreaEffect
         */
        private Integer ssaJumpAreaEffect;

        /**
         * @see com.bilibili.ssa.platform.common.enums.SsaGuideMaterialTypeEnum
         */
        private Integer guideMaterialType;

        /**
         * 是否启用闪屏直播预约
         */
        private Boolean isEnableLiveBooking;

        /**
         * @see  SsaEffectiveType
         */
        private Integer effectiveType;
    }
}
