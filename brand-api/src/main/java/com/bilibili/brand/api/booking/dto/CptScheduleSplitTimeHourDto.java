package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 预约结果
 * @author: wangbin01
 * @create: 2019-01-29
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptScheduleSplitTimeHourDto {

    /**
     * 日期
     */
    private Integer seq;

    /**
     * 日期
     */
    private Long begin_time;

    /**
     * 日期
     */
    private Long end_time;

    private String sourceName;

    private Integer cptScheduleId;

}
