/** 
* <AUTHOR> 
* @date  2017年10月17日
*/ 

package com.bilibili.brand.api.common.enums;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@AllArgsConstructor
public enum DateGroupType {
    DEFAULT (0, "全部汇总"){
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            return getDate(fromTime.getTime()) + " ~ " + getDate(toTime.getTime());
        }

        @Override
        public Timestamp getFromTime(Timestamp fromTime) {
            return fromTime;
        }

        @Override
        public Timestamp getToTime(Timestamp toTime) {
            return toTime;
        }
    },
    DAY (1, "分天"){
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            return getDate(time);
        }

        @Override
        public Timestamp getFromTime(Timestamp fromTime) {
            return Utils.getBeginOfDay(fromTime);
        }

        @Override
        public Timestamp getToTime(Timestamp toTime) {
            return Utils.getEndSecondOfDay(toTime);
        }
    },
    WEEK (2, "分周"){
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            calendar.setTimeInMillis(time);
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            StringBuilder key = new StringBuilder();
            key.append(getDate(calendar.getTimeInMillis()));
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
            key.append(" ~ ");
            key.append(getDate(calendar.getTimeInMillis()));
            return key.toString();
        }

        @Override
        public Timestamp getFromTime(Timestamp fromTime) {
            return Utils.getBeginDayOfWeek(fromTime);
        }

        @Override
        public Timestamp getToTime(Timestamp toTime) {
            return Utils.getEndDayOfWeek(toTime);
        }
    }, MONTH (3, "分月"){
        @Override
        public String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime) {
            calendar.setTimeInMillis(time);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            StringBuilder key = new StringBuilder();
            key.append(getDate(calendar.getTimeInMillis()));
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            key.append(" ~ ");
            key.append(getDate(calendar.getTimeInMillis()));
            return key.toString();
        }

        @Override
        public Timestamp getFromTime(Timestamp fromTime) {
            return Utils.getBeginDayOfMonth(fromTime);
        }

        @Override
        public Timestamp getToTime(Timestamp toTime) {
            return Utils.getEndDayOfMonth(toTime);
        }
    };

    @Getter
    private Integer code;

    @Getter
    private String desc;

    public final static DateGroupType getByCode(Integer code) throws ServiceException {
        for (DateGroupType groupType : DateGroupType.values()) {
            if (groupType.getCode().equals(code)) {
                return groupType;
            }
        }
        throw new IllegalArgumentException("unknown code DateGroupType " + code);
    }

    public abstract String generateTimeKey(Calendar calendar, Long time, Timestamp fromTime, Timestamp toTime);

    public abstract Timestamp getFromTime(Timestamp fromTime);

    public abstract Timestamp getToTime(Timestamp toTime);
    
    private static String getDate(Long timeMiles) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(timeMiles);
    }
}
