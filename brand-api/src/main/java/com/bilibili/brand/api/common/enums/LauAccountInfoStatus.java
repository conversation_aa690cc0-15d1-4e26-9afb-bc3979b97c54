/** 
* <AUTHOR> 
* @date  2018年6月6日
*/ 

package com.bilibili.brand.api.common.enums;

public enum LauAccountInfoStatus {
    INIT(1, "待审核"),
    AUDITED(2, "审核通过"),
    REJECTED(3, "审核驳回");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    LauAccountInfoStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LauAccountInfoStatus getByCode(int code) {
        for (LauAccountInfoStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown LauAccountInfoStatus code.");
    }
}

