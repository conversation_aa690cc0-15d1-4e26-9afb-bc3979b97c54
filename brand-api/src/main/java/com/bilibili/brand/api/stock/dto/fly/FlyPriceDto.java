package com.bilibili.brand.api.stock.dto.fly;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlyPriceDto implements Serializable{
    private static final long serialVersionUID = -237057123180261274L;
    private Integer platformId;
    private String platformName;
    private BigDecimal price;
}
