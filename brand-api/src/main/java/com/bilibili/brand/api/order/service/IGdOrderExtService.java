package com.bilibili.brand.api.order.service;

import com.bilibili.brand.api.order.dto.GdOrderExtDto;
import com.bilibili.brand.api.order.dto.UpdateOrderDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/19
 */
public interface IGdOrderExtService {

    void saveGdOrderExtInfo(int orderId, GdOrderExtDto gdOrderExtDto);

    GdOrderExtDto getGdOrderExtInfo(int orderId);

    void checkGdOrderExtCreateInfo(Integer orderProduct, GdOrderExtDto gdOrderExtDto);

    void checkGdOrderUpdateInfo(Integer orderProduct, GdOrderExtDto oldOrderExt, UpdateOrderDto updateOrderDto);

    boolean isFlyGdRangeOrder(GdOrderExtDto gdOrderExtDto);

    boolean isFlyGdOrder(GdOrderExtDto gdOrderExtDto);

    boolean isTopFlowOrder(GdOrderExtDto gdOrderExtDto);

    boolean isFlyGdOrder(int orderId);

    boolean isCycleFrequencyOrder(GdOrderExtDto gdOrderExtDto);

    List<GdOrderExtDto> getGdOrderExtInfoList(List<Integer> orderIds);
}
