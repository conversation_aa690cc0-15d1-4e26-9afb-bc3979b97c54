/** 
* <AUTHOR> 
* @date  2017年6月13日
*/

package com.bilibili.brand.api.account.service;

import java.util.List;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.brand.api.account.dto.AccountDto;
import com.bilibili.brand.api.account.dto.AccountGdQuotaDto;
import com.bilibili.brand.api.account.dto.QueryAccountGdQuotaDto;

public interface IAccountGdQuotaService {

	PageResult<QueryAccountGdQuotaDto> queryByPage(Page page) throws ServiceException;

	AccountGdQuotaDto load(Integer id) throws ServiceException;

	int create(AccountGdQuotaDto accountGdQuotaDto, Operator operator) throws ServiceException;

	void update(AccountGdQuotaDto accountGdQuota<PERSON><PERSON>, Operator operator) throws ServiceException;

	void delete(Integer id, Operator operator);

	AccountGdQuotaDto getByAccountId(Integer accountId);
	
	List<QueryAccountGdQuotaDto> getInAccountIds(List<Integer> accountIds, List<AccountDto> accountDtos);
}
