package com.bilibili.brand.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2017年2月9日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountGroupBaseDto implements Serializable{


    private static final long serialVersionUID = 5629428955432738767L;
    private Integer id;
    
    private String name;

    private String description;

    private Integer status;
    
}
