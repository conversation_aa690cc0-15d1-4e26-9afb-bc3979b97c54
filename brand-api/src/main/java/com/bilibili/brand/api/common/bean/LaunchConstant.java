package com.bilibili.brand.api.common.bean;

import com.google.common.collect.Sets;

import java.util.Set;
import java.util.regex.Pattern;

public class LaunchConstant {

	public final static String MTIME_ASC = "mtime ASC";
	public final static String MTIME_DESC = "mtime DESC";
	public final static String CTIME_DESC = "ctime DESC";
	public final static String ID_ASC = "id ASC";
	public static final Integer PC_CHANNEL_ID = 10010000;
	public static final Integer MOB_CHANNEL_ID = 10020000;

	public static final String YYYY_MM_DD = "yyyy-MM-dd";
	public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public static final int LAUNCH_TIME_LENGTH = 7 * 24;

    public static final int MAX_CAMPAIGN_NAME_LENGTH = 40;
    public static final long MIN_CAMPAIGN_BUDGET = 10000;//单位(分)
    
    public static final int MAX_UNIT_NAME_LENGTH = 40;
    public static final long MIN_UNIT_BUDGET = 10000;

	public final static Pattern LAUNCH_TIME_PATTERN = Pattern.compile("[10]*");

	public static final String VIDEO_PREFIX = "bilibili://video/";

    public static final String LAUNCH_TIME = "111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111";
    public static final String DEFAULT_UNIT_NAME = "GD-UNIT";
    public static final String DEFAULT_SSA_UNIT_NAME = "SSA-GD-UNIT";
	public static final String DEFAULT_BUTTON_COPY = "查看详情";
    public static final String SCHEDULE_LOCK_SUFFIX = ":schedule-lock:brand-portal";
	public static final String BRAND_CAMPAIGN_LOCK_SUFFIX = ":campaign-lock:brand-portal";
	public static final String BRAND_UNIT_LOCK_SUFFIX = ":unit-lock:brand-portal";
	public static final String BRAND_CREATIVE_LOCK_SUFFIX = ":creative-lock:brand-portal";
	public static final String ORDER_LOCK_SUFFIX = ":order-lock:brand-portal";
	public static final String FLY_UNIT_LOCK_SUFFIX = ":fly-unit-lock:brand-portal";
	public static final String CAMPAIGN_BUDGET_LOCK_SUFFIX = ":campaign-budget-lock:cpm-adp";
	public static final String UNIT_BUDGET_LOCK_SUFFIX = ":unit-budget-lock:cpm-adp";
	public static final String UNIT_LOCK_SUFFIX = ":unit-lock:cpm-adp";
	public static final String SSA_PLUS_LOCK_SUFFIX = ":ssa-lock:plus";
	public static final String TOPVIEW_PLUS_LOCK_SUFFIX = ":ssa-lock:plus";


	public static final String ACCOUNT_LOCK_SUFFIX = ":account-lock:ad";
	
	public final static String BILIBILI_SPACE_PREFIX = "https://m.bilibili.com/space/";

	public static final String BILIBILI_USER_SPACE_SCHEMA = "bilibili://space/";
	public static final String BILIBILI_USER_FACE_DEFAULT_URL_SUFFIX = "noface.gif";

	public final static String BILIBILI_GAME_URL = "https://app.biligame.com/detail?id=%s&sourceType=adPut";

	public static final int MAX_CREATIVE_MONITORING_URL_LENGTH = 1024;

	public static boolean IS_PING_URL = true;
    public static boolean IS_VALIDATE_VIDEO = true;

	//动态配置
    public static int MAX_CAMPAIGN_NUM=100000;
    public static int MAX_UNIT_NUM=100000;
    public static int MAX_CREATIVE_NUM=100000;
    public static int MIN_CPC_BID=100;//单位(分)
    public static int MAX_CPC_TARGET_COUNT=2;
	public static int BUDGET_PERCENTAGE = 20;
	public static long CAMPAIGN_BUDGET_UPDATE_LIMIT = 3l;
	public static long UNIT_BUDGET_UPDATE_LIMIT = 3l;
	public static int UNIT_MAX_REPEAT_NUM = 50;

	public static final String WAKE_UP_BAR_SCHEMA_CONTENT = "bilibili%3A%2F%2Fblank%3Fcm_mark";
	public static final String BILI_WAKE_UP_BAR_SCHEMA_CONTENT = "bilibili://blank?cm_mark";

	//允许gd发送消息的时间
	public static final Set<Integer> ENABLE_INFORM_HOUR = Sets.newHashSet(10,13,16,19,20,21,22,23);

	//允许cpt发送消息的时间
	public static final Set<Integer> ENABLE_CPT_INFORM_HOUR = Sets.newHashSet(1,22);


	//小卡资源位
	public static final Set<Integer> SMALL_CARD_SOURCE = Sets.newHashSet(1892,1893,1894,1895,1896,
			1981,1982,1983,1984,1899,1900,1901,1902,1903,1989,1990,1991,1992);

	//inline资源位
	public static final Set<Integer> BIG_CARD_SOURCE = Sets.newHashSet(1891,1898);

	//所有大小卡资源位
	public static final Set<Integer> All_BIG_SMALL_CARD_SOURCE = Sets.newHashSet(1891,1898,1892,1893,1894,1895,1896,
			1981,1982,1983,1984,1899,1900,1901,1902,1903,1989,1990,1991,1992);

	public static int getUNIT_MAX_REPEAT_NUM() {
		return UNIT_MAX_REPEAT_NUM;
	}
	public static void setUNIT_MAX_REPEAT_NUM(int unitMaxRepeatNum) {
		UNIT_MAX_REPEAT_NUM = unitMaxRepeatNum;
	}

	public static long getCAMPAIGN_BUDGET_UPDATE_LIMIT() {
		return CAMPAIGN_BUDGET_UPDATE_LIMIT;
	}

	public static long getUNIT_BUDGET_UPDATE_LIMIT() {
		return UNIT_BUDGET_UPDATE_LIMIT;
	}

	public static void setUNIT_BUDGET_UPDATE_LIMIT(long unitBudgetUpdateLimit) {
		UNIT_BUDGET_UPDATE_LIMIT = unitBudgetUpdateLimit;
	}

	public static void setCAMPAIGN_BUDGET_UPDATE_LIMIT(long campaignBudgetUpdateLimit) {
		CAMPAIGN_BUDGET_UPDATE_LIMIT = campaignBudgetUpdateLimit;
	}

	public static int getBUDGET_PERCENTAGE() {
		return BUDGET_PERCENTAGE;
	}

	public static void setBUDGET_PERCENTAGE(int budgetPercentage) {
		BUDGET_PERCENTAGE = budgetPercentage;
	}

	public static int getMIN_CPC_BID() {
        return MIN_CPC_BID;
    }
    public static void setMIN_CPC_BID(int mIN_CPC_BID) {
        MIN_CPC_BID = mIN_CPC_BID;
    }
    public static int getMAX_CPC_TARGET_COUNT() {
        return MAX_CPC_TARGET_COUNT;
    }
    public static void setMAX_CPC_TARGET_COUNT(int mAX_CPC_TARGET_COUNT) {
        MAX_CPC_TARGET_COUNT = mAX_CPC_TARGET_COUNT;
    }
    public static int getMAX_CAMPAIGN_NUM() {
		return MAX_CAMPAIGN_NUM;
	}
	public static void setMAX_CAMPAIGN_NUM(int mAX_CAMPAIGN_NUM) {
		MAX_CAMPAIGN_NUM = mAX_CAMPAIGN_NUM;
	}
	public static int getMAX_UNIT_NUM() {
		return MAX_UNIT_NUM;
	}
	public static void setMAX_UNIT_NUM(int mAX_UNIT_NUM) {
		MAX_UNIT_NUM = mAX_UNIT_NUM;
	}
	public static int getMAX_CREATIVE_NUM() {
		return MAX_CREATIVE_NUM;
	}
	public static void setMAX_CREATIVE_NUM(int mAX_CREATIVE_NUM) {
		MAX_CREATIVE_NUM = mAX_CREATIVE_NUM;
	}
	public static boolean isIS_PING_URL() {
		return IS_PING_URL;
	}
	public static void setIS_PING_URL(boolean iS_PING_URL) {
		IS_PING_URL = iS_PING_URL;
	}
	public static boolean isIS_VALIDATE_VIDEO() {
		return IS_VALIDATE_VIDEO;
	}
	public static void setIS_VALIDATE_VIDEO(boolean iS_VALIDATE_VIDEO) {
		IS_VALIDATE_VIDEO = iS_VALIDATE_VIDEO;
	}

	public final static String MGK_PAGE_MICRO_PARAM = "buvid=__BUVID__&mid=__MID__&imei=__IMEI__&duid=__DUID__&idfa=__IDFA__&android_id=__ANDROIDID__&os=__OS__&request_id=__REQUESTID__&source_id=__SOURCEID__&track_id=__TRACKID__&creative_id=__CREATIVEID__&adtype=__ADTYPE__";

	public final static String MANUSCRIPT_MICRO_PARAM = "source_id=__SOURCEID__&resource_id=__RESOURCEID__&creative_id=__CREATIVEID__&linked_creative_id=$creative_id&track_id=__TRACKID__&request_id=__REQUESTID__&caid=__CAID__&biz_extra=%7B%22ad_play_page%22%3A1%7D";

	public final static String MANUSCRIPT_MICRO_REPLACE_PARAM = "$creative_id";
}
