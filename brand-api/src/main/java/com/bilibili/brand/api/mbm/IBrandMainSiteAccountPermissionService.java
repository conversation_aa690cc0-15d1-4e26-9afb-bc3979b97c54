package com.bilibili.brand.api.mbm;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.mbm.dto.BrandMainSiteAccountConfigDto;
import com.bilibili.brand.api.mbm.dto.BrandMainSiteAccountConfigQueryDto;

/**
 * 品牌主站账号权限配置管理
 *
 * <AUTHOR>
 * @date 2024/10/24 11:57
 */
public interface IBrandMainSiteAccountPermissionService {
    /**
     * 查询品牌主站账号配置列表
     *
     * @return
     */
    PageResult<BrandMainSiteAccountConfigDto> getMainSiteAccountConfigList(BrandMainSiteAccountConfigQueryDto query);

    /**
     * 保存品牌主站账号权限配置
     *
     * @param config，config#id大于0则更新，否则新增
     * @return 配置id
     */
    Long saveMainSiteAccountConfig(BrandMainSiteAccountConfigDto config, Operator operator) throws Exception;


    /**
     * 删除品牌主站账号权限配置
     *
     * @param id
     */
    void deleteMainSiteAccountConfig(Long id, Operator operator) throws Exception;

}
