/** 
* <AUTHOR> 
* @date  2017年5月22日
*/

package com.bilibili.brand.api.log.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.log.bean.LogOperatorBean;
import com.bilibili.brand.api.log.dto.LogOperatorDto;
import com.bilibili.brand.api.log.dto.QueryLogOperatorDto;


public interface ILogOperatorService {

	void insertLog(Operator operator, LogOperatorBean newLogOperatorDto);
}
