package com.bilibili.brand.api.fb;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/20
 **/

@AllArgsConstructor
@Getter
public enum FbProjectType {

    UNKNOWN(0, "未知类型", Lists.newArrayList()),
    INTEGRATED_NON_GAME(1, "整合营销-非游",
            Lists.newArrayList(
                    FbMarketingType.PARTITION_ECOSYSTEM,
                    FbMarketingType.NODE_MARKETING,
                    FbMarketingType.INDUSTRY_IP,
                    FbMarketingType.BUSINESS_CUSTOMIZATION,
                    FbMarketingType.MUSIC
            )
    ),
    INTEGRATED_GAME(2, "整合营销-游戏",
            Lists.newArrayList(
                    FbMarketingType.EVENT_SPONSORSHIP,
                    FbMarketingType.GAME_ECOSYSTEM,
                    FbMarketingType.GAME_MARKETING,
                    FbMarketingType.EVENT_ECOSYSTEM,
                    FbMarketingType.EVENT_MARKETING
            )
    ),
    INTEGRATED_INTERACTIVE(3, "整合营销-互动营销",
            Lists.newArrayList(
                    FbMarketingType.CONSCRIPTION
            )
    ),
    RETAIL_GAME(4, "零卖-游戏",
            Lists.newArrayList(
                    FbMarketingType.LIVE_BUSINESS_ORDER,
                    FbMarketingType.LIVE_ADVERTISEMENT,
                    FbMarketingType.POSTPAYMENT_BUSINESS_ORDER,
                    FbMarketingType.AGENCY_PREPAYMENT_BUSINESS_ORDER
            )
    ),
    RETAIL_NON_GAME(5, "零卖-非游",
            Lists.newArrayList(
                    FbMarketingType.LIVE_BUSINESS_ORDER_2,
                    FbMarketingType.LIVE_ADVERTISEMENT_2,
                    FbMarketingType.VENUE_IP,
                    FbMarketingType.LIVE_ROOM_OPERATION,
                    FbMarketingType.POSTPAYMENT_BUSINESS_ORDER_2,
                    FbMarketingType.AGENCY_PREPAYMENT_BUSINESS_ORDER_2
            )
    ),
    OGV(6, "OGV",
            Lists.newArrayList(
                    FbMarketingType.FUEL_PACKAGE,
                    FbMarketingType.FILM_AND_TELEVISION,
                    FbMarketingType.VIRTUAL_IDOL,
                    FbMarketingType.OGV_BML_BW,
                    FbMarketingType.OGV_NEW_YEAR,
                    FbMarketingType.OGV_CREATION,
                    FbMarketingType.OGV_DOCUMENTARY,
                    FbMarketingType.OGV_VARIETY_SHOW,
                    FbMarketingType.OGV_BRAND_PLANNING,
                    FbMarketingType.OGV_MARKET_SPONSORSHIP,
                    FbMarketingType.OGV_IP_AUTHORIZATION
            )
    );

    private final Integer code;
    private final String desc;
    private final List<FbMarketingType> children;

    public static FbProjectType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(String.format("项目类型「%s」不存在", code)));
    }

    public static FbProjectType getByCodeWithoutExp(Integer code) {
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public ProjectTree buildTree() {
        return ProjectTree.builder()
                .code(code)
                .value(desc)
                .children(children.stream().map(FbMarketingType::buildTree).collect(Collectors.toList()))
                .build();
    }
}
