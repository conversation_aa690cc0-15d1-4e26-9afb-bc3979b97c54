package com.bilibili.brand.api.resource.slot_group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResSlotGroupDto implements Serializable{

	private static final long serialVersionUID = -5245145168287379373L;
	private Integer id;
	private String slotroupName;
	private Integer channeld;
	private String channelName;
	private List<ResSlotDto> slots;
	private List<ResAccountGroupDto> accountGroups;
	private Integer status;
	private List<Integer> sysTypes;
	private String commonTemplates;

}
