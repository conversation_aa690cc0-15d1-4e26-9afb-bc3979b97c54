package com.bilibili.brand.api.stock.dto.fly;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlyPriceQueryDto implements Serializable {
    private static final long serialVersionUID = -237057123180261274L;

    private String dealSeq;

    //分日预约库存
    private Map<Timestamp,Long> dayImpression;

    //并标记哪些新增修改的,vluae>0表示维持原量
    private Map<Timestamp, Long> dayFlag;

    private boolean isToday;
    private Integer hour;
}
