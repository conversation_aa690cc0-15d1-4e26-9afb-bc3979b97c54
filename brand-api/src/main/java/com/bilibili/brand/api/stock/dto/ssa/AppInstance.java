/*
 * Copyright (c) 2015-2018 BiliBili Inc.
 */

package com.bilibili.brand.api.stock.dto.ssa;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AppInstance {
    /**
     * 服务唯一标示。【业务标识.服务标识[.子服务标识]】 全局唯一，禁止修改.
     */
    private String appId;

    /**
     * instance主机标示.
     */
    private String hostName;

    /**
     * 服务地址 格式为 scheme://ip:port,支持多个协议地址。如 "grpc://127.0.0.1:8888, http://127.0.0.1:8887".
     */
    private List<String> addrs;

    /**
     * 环境信息，(fat1,uat ,pre ,prod)分别对应fat环境 集成环境，预发布和线上.
     */
    private String env;

    /**
     * 机房地区.
     */
    private String region;

    /**
     * 机房服务地区标识，用于多机房部署区分数据中心.
     */
    private String zone;

    /**
     * 状态，1表示接收流量，2表示不接收.
     */
    private int status;


    /**
     * 服务版本号信息.
     */
    private String version;

    /**
     * 服务自定义扩展元数据，格式为key1=value1&key2=value2，可以用于传递权重，负载等信息.
     * 使用url query格式传递。多个值时为key1=a&key1=b&key2=c
     * 通用字段:cluster weight
     */
    private Map<String, Object> metadata;
}
