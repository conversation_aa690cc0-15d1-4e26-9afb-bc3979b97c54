package com.bilibili.brand.api.ssa;

import com.bilibili.adp.common.annotation.MailTableDesc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/11/6 20:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SsaScheduleReleasedNotificationDto implements Serializable {
    private static final long serialVersionUID = 4698053240814154325L;
    @MailTableDesc(value = "账号id", sort = 1)
    private Integer accountId;
    @MailTableDesc(value = "账号名称", sort = 2)
    private String accountName;
    @MailTableDesc(value = "资源位名称", sort = 3)
    private String sourceName;
    @MailTableDesc(value = "排期时间", sort = 4)
    private String launchDateStr;
    @MailTableDesc(value = "释放时间", sort = 5)
    private String releaseDateStr;
    @MailTableDesc(value = "订单ID", sort = 6)
    private Integer orderId;
    @MailTableDesc(value = "订单名称", sort = 7)
    private String orderName;
    @MailTableDesc(value = "排期ID", sort = 8)
    private Integer scheduleId;
    @MailTableDesc(value = "排期名称", sort = 9)
    private String scheduleName;
    @MailTableDesc(value = "资源预定时间", sort = 10)
    private String sourceBookingTimeStr;
    @MailTableDesc(value = "资源预定人", sort = 11)
    private String operator;
}
