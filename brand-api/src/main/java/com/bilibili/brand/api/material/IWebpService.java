package com.bilibili.brand.api.material;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.material.bo.WebpProcessResultBo;
import com.bilibili.brand.api.webp.WebpGenerateRequestDto;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface IWebpService {
    WebpProcessResultBo generateWebpByImagesZip(MultipartFile multipartFile, WebpGenerateRequestDto webpGenerateRequestDto) throws IOException;

    WebpProcessResultBo uploadWebpDirectly(MultipartFile multipartFile) throws IOException, ServiceException;
}
