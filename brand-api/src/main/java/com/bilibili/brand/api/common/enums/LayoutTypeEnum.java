package com.bilibili.brand.api.common.enums;

/**
 * <AUTHOR>
 * @date 2016年9月26日
 */
public enum LayoutTypeEnum {

    IOS(1, "Iphone端布局"),

    ANDROID(2, "Android端布局");

    private int code;
    private String desc;

    LayoutTypeEnum(int code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public static LayoutTypeEnum getByCode(int code) {
        for (LayoutTypeEnum layoutTypeEnum: values()) {
            if (layoutTypeEnum.code == code) {
                return layoutTypeEnum;
            }
        }

        throw new IllegalArgumentException("unknown code LocLogFlag " + code);
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
