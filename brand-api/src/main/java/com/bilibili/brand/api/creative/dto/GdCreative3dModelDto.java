package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GdCreative3dModelDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 业务场景，用于表示该模型正在被哪种业务使用，0、story天降礼盒
     */
    private Integer bizScene;

    /**
     * 跳转时机，特效起播后多长时间进行跳转，单位ms
     */
    private Integer jumpDelay;

    /**
     * 模型列表，包含name、url、md5
     */
    private List<Creative3dModelDto> modelList;

    /**
     * 模型参数，包含generation_duration、falling_duration、removal_delay、gravity_factor、box_count、gravity_affected、collider_box（width、height、depth）
     */
    private Param params;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Param {
        // 盒子生成并自由下落的时间，单位毫秒
        private Long generationDuration;
        // 不生成，等待所有盒子自由下落到底部的时间，单位毫秒
        private Long fallingDuration;
        // 将底部抽掉后，等待多久将视图关闭的时间，单位毫秒
        private Long removalDelay;
        // 重力因数 (可选，默认为 1.0)，浮点数转字符串
        private String gravityFactor;
        // 盒子数量，下限为 1
        private Integer boxCount;
        // 重力方向是否受手机姿态影响
        private Boolean gravityAffected;
        // 碰撞体积, 单位米，浮点数转字符串
        private ColliderBox colliderBox;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class ColliderBox {
            private String width;
            private String height;
            private String depth;
        }
    }
}
