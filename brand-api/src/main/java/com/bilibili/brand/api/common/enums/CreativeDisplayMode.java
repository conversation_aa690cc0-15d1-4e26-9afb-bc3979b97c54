package com.bilibili.brand.api.common.enums;

/**
 * Created by walker on 16/9/19.
 */
public enum CreativeDisplayMode {
    OPTIMIZATION(1, "优播模式"),
    ROTATION(2, "轮播模式");

    private int code;
    private String message;

    private CreativeDisplayMode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static CreativeDisplayMode getByCode(int code) {

        for (CreativeDisplayMode creativeDisplayMode : values()) {
            if (creativeDisplayMode.code == code) {
                return creativeDisplayMode;
            }
        }

        return null;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
