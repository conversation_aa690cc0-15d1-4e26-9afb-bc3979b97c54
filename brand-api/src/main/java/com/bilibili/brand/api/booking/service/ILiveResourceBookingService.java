package com.bilibili.brand.api.booking.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.booking.dto.*;

import java.sql.Timestamp;
import java.util.List;

public interface ILiveResourceBookingService {

    List<BookingItemDto> queryResourceBooking(BookingQueryDto queryDto);

    List<LiveHourBookingItemDto> queryLiveHourBooking(Integer dayBookingId, Integer accountId);

    /**
     * 根据排期ID和最大时间(不包含最大时间本身)，查找所有匹配的预约项
     * @param scheduleId
     * @param maxTime
     * @return
     */
    List<BookingItemDto> queryResourceBooking(Integer scheduleId, Timestamp maxTime);

    List<BookingItemDetailDto> convertToDetailDto(List<BookingItemDto> list);

    BookingItemDto queryResourceBookingById(Integer id);

    Integer deleteResourceBookingById(Integer id, Operator operator);

    Integer deleteHourResourceBookingById(Integer dayBookingId, List<Integer> ids, Operator operator);

    Integer deleteResourceBooking(BookingItemDto dto,  Operator operator);

    Integer deleteResourceBookingWithoutValidate(BookingItemDto dto,  Operator operator);

    List<String> addResourceBooking(NewResourceBookingDto dto, Operator operator);

    Integer bookingHour(LiveNewResourceBookingDto dto, Operator operator);

    List<BookingItemResultDto> addMultiResourceBooking(List<NewResourceBookingDto> dtoList, Operator operator);

    List<BookingItemResultDto> deleteMultiResourceBooking(List<DeleteResourceBookingDto> dtoList, Operator operator);

    /**
     * cpt在创建排期的时候，相应的更新预约的状态及相关属性
     * @return
     */
    Integer scheduleTheResourceBooking(UpdateScheduleBookingDto updateDto);

    /**
     * cpt在创建排期的时候，相应的更新预约的状态及相关属性
     * @return
     */
    Integer scheduleTheResourceHourBooking(UpdateScheduleBookingDto updateDto);

    /**
     * cpt在删除排期的时候，相应的更新预约的状态及相关属性
     * @param updateDto
     * @return
     */
    Integer unScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto);

    /**
     * cpt在删除排期的时候，相应的更新预约的状态及相关属性
     * @param updateDto
     * @return
     */
    Integer unScheduleTheHourResourceBooking(UpdateScheduleBookingDto updateDto);

    /**
     * 查询位次下所有已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp> queryAllBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 查询位次下所有我已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp> queryAllMyBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 查询多个位次下所有我已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp> multiQueryMyBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 查询多个位次下可用的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<CptScheduleSplitTimeDto> multiQueryHourTime(BookingTimeQueryDto queryDto, Operator operator);


    long countBookingItem(BookingItemCountQueryDto countQueryDto);

    List<BookingItemDto> queryResourceBooking(Integer cycleId, Integer sourceId);

    void addValidateAndDecorate(NewResourceBookingDto dto, Operator operator);

    /**
     * 预约资源自动释放
     */
    void bookingAutoRelease();

}
