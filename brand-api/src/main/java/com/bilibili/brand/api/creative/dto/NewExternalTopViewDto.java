package com.bilibili.brand.api.creative.dto;

import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.brand.dto.creative.MiniProgramDto;
import com.bilibili.cpt.platform.api.creative.dto.CptJumpDTO;
import com.bilibili.cpt.platform.api.schedule.dto.ScheduleDateDto;
import com.bilibili.enums.GdJumpType;
import com.bilibili.ssa.platform.api.splash_screen.dto.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewExternalTopViewDto {

    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 排期ID（批量创建创意时使用）
     */
    private List<Integer> scheduleIdList;

    /**
     * 创意名称
     */
    private String creativeName;

    /**
     * 时间定向
     */
    private Integer timeTarget;

    /**
     * 闪屏排期
     */
    private List<SsaNewScheduleSplashScreenMappingDto> ssaNewScheduleSplashScreenMappingDtos;

    /**
     * 广告角标(1:广告，2:推广)
     */
    private Integer cmMark;

    /**
     * 分享开关 0-关闭 1-打开
     */
    private Integer shareState;

    /**
     * 分享标题
     */
    private String shareTitle;

    /**
     * 分享副标题
     */
    private String shareSubTitle;

    /**
     * 分享图片URL
     */
    private String shareImageUrl;

    /**
     * 分享图片hash
     */
    private String shareImageHash;

    // 闪屏
    /**
     * 是否可跳过: 0-否 1-是
     */
    private Integer ssaIsSkip;

    /**
     * 下发时间: 1-过审下发 2-排期开始前2小时下发、
     *
     */
    private Integer ssaIssuedTime;

    /**
     * 闪屏视频
     */
    private SsaNewSplashScreenVideoDto ssaVideo;

    /**
     * 彩蛋视频
     */
    private SsaNewSplashScreenVideoDto eggVideo;

    /**
     * 过渡视频
     */
    private TransitionVideoDto transitionVideo;

    /**
     * 闪屏背景图片
     */
    private List<SsaNewSplashScreenBaseImageDto> ssaBaseImageList;

    /**
     * 素材加密: 0-禁用，1-启用
     */
    private Integer ssaEncryption;

    /**
     * 闪屏文案
     */
    private String ssaCopyWriting;

    //"跳转类型: 1-链接 2-视频 3-番剧 4-直播 5-游戏中心")
    private Integer jumpType;

    /**
     * 闪屏跳转模型列表
     */
    private List<SplashScreenJumpDTO> splashScreenJumpDTOS;

    /**
     * 闪屏监控模型列表
     */
    private List<SplashScreenCustomizedDTO> ssaCustomizedDTOS;

    /**
     * 分端开关 0-分端 1-不分端
     */
    private Integer platformSwitch;

    /**
     * 是否支持自定义按钮文案 0-否 1-是
     */
    private Integer supportCustomGuide;

    /**
     * 按钮信息
     */
    private List<SplashScreenDynamicButtonBO> buttonBOS;


    // 新版首焦
    /**
     * 新版首焦模板ID
     */
    private Integer newHfTemplateId;

    /**
     * 新版首焦标题
     */
    private String newHfTitle;

    /**
     * 新版首焦大图URL
     */
    private String newHfImageUrl;

    /**
     * 新版首焦大图Hash
     */
    private String newHfImageHash;

    /**
     * 新版首焦大图跳转URL
     */
    private String newHfImageJumpUrl;

    /**
     * 新版首焦图片跳转类型
     * {@link GdJumpType}
     */
    private Integer newHfImageJumpType;

    /**
     * 新版首焦小图URL
     */
    private String newHfExtImageUrl;

    /**
     * 新版首焦小图Hash
     */
    private String newHfExtImageHash;

    /**
     * 新版首焦视频
     */
    private SsaNewSplashScreenVideoDto newHfVideo;

    /**
     * 是否自定义首焦品牌信息
     */
    private Integer isCustomizedNewHfBrandInfo;

    /**
     * 新版首焦品牌名称
     */
    private String newHfBrandName;

    /**
     * 新版首焦品牌头像url
     */
    private String newHfFaceUrl;

    /**
     * 新版首焦品牌头像md5
     */
    private String newHfFaceMd5;

    /**
     * 首焦安卓展示监控链接
     */
    private String hfAndroidCustomizedImpUrl;

    /**
     * 首焦安卓点击监控链接
     */
    private String hfAndroidCustomizedClickUrl;


    /**
     * 首焦IOS展示监控链接
     */
    private String hfIosCustomizedImpUrl;

    /**
     * 首焦点击监控链接
     */
    private String hfIosCustomizedClickUrl;

    /**
     * 首焦监控链接是否需要IDFA加密（默认0，0：不加密，1：加密）
     */
    private Integer hfIsIdfaEncrypted;

    /**
     * cpt排期
     */
    private List<ScheduleDateDto> hfSchedules;

    //选择式按钮纯文字按钮唤起文案
    private String extraSchemeCopywriting;

    //选择式按钮纯文字按钮跳转文案
    private String extraGuideInstructions;

    private List<CptJumpDTO> cptJumpDTOS;

    //互动闪屏是否支持按钮
    private Boolean isSupportButtonToInteract;

    //闪屏按钮文字颜色 0-默认 1-黑色
    private Integer textColorStyle;

    /**
     * topView首焦ip视频id
     */
    private Integer hfIpVideoId;

    private ManuscriptInfoBO manuscriptInfo;

    private ProductLabelDto productLabel;

    private MiniProgramDto miniProgram;

    private List<Integer> appPackageIds;

    /**
     * 是否使用默认版本号: true-是 false-否
     */
    private Boolean useDefaultVersion;

    /**
     * 版本控制信息
     */
    private List<SsaNewSplashScreenVersionControlDto> ssaNewSplashScreenVersionControlDtos;

    //交互说明文案
    private String interactInstructions;

    private SsaSplashScreenMiddlePageDto middlePage;

    private List<String> hfAndroidCustomizedClickUrlList;

    private List<String> hfIosCustomizedClickUrlList;

    private List<String> hfCustomizedClickUrlList;

    // 是否启用直播间预约
    private Boolean isEnableLiveBooking;

    // 直播预约id
    private Long liveBookingId;

    //闪屏实际唤起类型
    private Integer wakeAppType;

    //首焦跳转信息相关
    private TopViewHfJumpDto hfJump;
}
