package com.bilibili.brand.api.resource.slot_group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResAccountGroupDto implements Serializable{

	private static final long serialVersionUID = -6862749526868336304L;
	private Integer id;

	private String name;

	private Integer status;

}
