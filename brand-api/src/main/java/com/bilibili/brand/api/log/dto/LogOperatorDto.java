/** 
* <AUTHOR> 
* @date  2017年5月24日
*/

package com.bilibili.brand.api.log.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LogOperatorDto {
	private Long id;
	private Integer objId;
	private Integer moduleType;
	private String modifyType;
	private Integer operatorId;
	private String operatorName;
	private String content;
	private Long ip;

	private Timestamp ctime;
}
