package com.bilibili.brand.api.creative.handler;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.creative.dto.CreativePreviewContext;
import com.bilibili.brand.api.creative.dto.CreativePreviewDto;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface CreativePreviewHandler {

    long openPreview(Operator operator, Long creativeId, CreativePreviewContext context);

    void cancelPreview(Operator operator, Long creativeId);

    Map<Long, Timestamp> getCreative2PreviewTimeMap(List<CreativePreviewDto> previewDtoList);

    boolean validateCreativeStatus(Integer status);

    boolean validateCreativeAuditStatus(Integer status);

    boolean validateScheduleDate(Timestamp beginDate, Timestamp endDate);

    boolean match(int product);
}
