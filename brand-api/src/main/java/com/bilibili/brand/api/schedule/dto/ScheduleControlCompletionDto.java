package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/8/30 15:44
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleControlCompletionDto implements Serializable {
    private static final long serialVersionUID = -188657438135550893L;
    private Integer scheduleId;
    private Float expectCompletionRatio;
}