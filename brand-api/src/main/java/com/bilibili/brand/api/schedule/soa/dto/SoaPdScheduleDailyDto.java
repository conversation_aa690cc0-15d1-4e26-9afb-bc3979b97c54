package com.bilibili.brand.api.schedule.soa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-08-23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SoaPdScheduleDailyDto implements Serializable {

    private static final long serialVersionUID = -1760807797265178700L;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 开始时间
     */
    private String endTime;

    /**
     * 真实曝光，单位次
     */
    private Integer actualShow;


}
