package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/8/30 15:44
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleControlPriorityDto implements Serializable {
    private static final long serialVersionUID = -7350224633895474445L;
    private Integer scheduleId;
    private Timestamp beginTime;
    private Timestamp endTime;
    private Float speedRatio;
    private Float weightRatio;
}