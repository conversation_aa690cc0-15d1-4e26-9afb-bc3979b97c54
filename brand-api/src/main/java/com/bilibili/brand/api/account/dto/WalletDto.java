package com.bilibili.brand.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by fan<PERSON><PERSON> on 2016/10/14.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WalletDto {
    private Integer accountId;
    private BigDecimal cash;
    private BigDecimal redPacket;
    private BigDecimal totalCashRecharge;
    private BigDecimal totalCashConsume;
    private BigDecimal totalRedPacketRecharge;
    private BigDecimal totalRedPacketConsume;
}
