package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * migrate from cpt-api
 *
 * <AUTHOR>
 * @date 2023/9/16 10:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptScheduleDateDto implements Serializable {

    private static final long serialVersionUID = 338929935911427645L;
    private Integer id;
    private Integer scheduleId;
    private Integer campaignId;
    private Integer orderId;
    private Integer accountId;
    private Timestamp scheduleDate;
    private Integer rotationNum;
}
