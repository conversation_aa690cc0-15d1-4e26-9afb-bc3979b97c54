package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleSplitInfoDto implements Serializable {

    /**
     * 排期ID
     */
    private Integer scheduleId;

    /**
     * 拆分排期详情
     */
    private List<DetailDto> splitScheduleInfoList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DetailDto implements Serializable {

        /**
         * 目标展示量
         */
        private Integer totalImpression;
    }
}
