package com.bilibili.brand.api.creative.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.bilibili.brand.api.creative.dto.CreativeDanmakuDto;
import com.bilibili.brand.api.creative.dto.CreativeLotteryDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreativeProductCarouselDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 创意id
     */
    private Long creativeId;

    /**
     * 轮播图片列表 1到3个
     */
    private List<ProductCarouselImage> images;

    /**
     * 主题色
     */
    private String themeColor;

    /**
     * 引导图片
     */
    private ProductCarouselImage guideImage;

    /**
     * 可触发开始时间
     */
    private Integer triggerStartTime;

    public void validate() {
        // 轮播图片不能为空
        Assert.notEmpty(images, "轮播图片不能为空");
        // 引导图片不能为空
        Assert.notNull(guideImage, "引导图片不能为空");
        // 主题色不能为空
        Assert.hasText(themeColor, "主题色不能为空");
        // 可触发时间不能为空
        Assert.notNull(triggerStartTime, "可触发时间不能为空");
        // 轮播图片个数在1-3之间
        Assert.isTrue(images.size() <= 3, "轮播图片个数在1-3之间");
        images.forEach(image -> Assert.isTrue(image.isValid(), "轮播图片缺少必要信息"));
        Assert.isTrue(guideImage.isValid(), "引导图片缺少必要信息");
        Assert.isTrue(triggerStartTime >= 0, "可触发时间必须大于等于0");
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductCarouselImage implements Serializable {
        @JSONField(serialize = false)
        private static final long serialVersionUID = 1L;
        private String url;
        private String md5;
        //不序列化
        @JSONField(serialize = false)
        private String hash;


        @JSONField(serialize = false)
        public boolean isValid() {
            return Objects.nonNull(url) && !url.isEmpty() && Objects.nonNull(md5) && !md5.isEmpty();
        }
    }
}
