package com.bilibili.brand.api.resource.channel;

import java.util.List;
import java.util.Map;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;

/**
 * Created by walker on 16/9/13.
 */
public interface IChannelService {

    List<Channel> getInnerCpmChannels();
    
    Channel load(Integer channelId) throws ServiceException;
    
    List<Channel> getAll();
    

}
