package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-02-26
 **/
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookingTimeQueryDto {

    private Integer accountId;

    private Integer sourceId;

    private Integer scheduleId;

    private List<Integer> sourceIds;

    private Timestamp startTime;

    private Timestamp endTime;

    private List<Integer> status;

    private List<Integer> scheduleIdList;

    private boolean excludesPastDate;

    private List<Timestamp> groupDates;
}
