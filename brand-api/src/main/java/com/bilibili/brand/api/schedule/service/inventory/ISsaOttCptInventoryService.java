package com.bilibili.brand.api.schedule.service.inventory;

import com.bilibili.brand.api.schedule.bo.ssa.SsaInventoryContext;
import com.bilibili.brand.api.schedule.bo.ssa.SsaOttQueryInventoryBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.SsaOttCptInventoryResultBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttCptCreateSingleScheduleBo;

/**
 * <AUTHOR>
 * @date 2023/10/8
 */
public interface ISsaOttCptInventoryService {

    SsaOttCptInventoryResultBo queryInventory(SsaOttQueryInventoryBo queryInventoryBo);

    void lockInventory(SsaOttCptCreateSingleScheduleBo scheduleBo, SsaInventoryContext ctx);

    void calculateLockInventory(SsaOttCptCreateSingleScheduleBo scheduleBo);
}
