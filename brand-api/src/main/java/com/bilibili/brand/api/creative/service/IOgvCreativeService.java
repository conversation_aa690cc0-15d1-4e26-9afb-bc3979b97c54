package com.bilibili.brand.api.creative.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.brand.api.creative.dto.OgvCreativeDto;
import com.bilibili.brand.api.creative.dto.OgvCreativeQueryDto;
import com.bilibili.brand.api.creative.dto.OgvCreativeSaveDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/19 21:02
 */
public interface IOgvCreativeService {

    //创建OGV创意
    List<Long> createCreative(OgvCreativeSaveDto dto, Operator operator) throws Exception;

    //编辑OGV创意
    void updateCreative(OgvCreativeSaveDto dto, Operator operator) throws Exception;

    PageResult<OgvCreativeDto> getOgvCreativeList(OgvCreativeQueryDto query);

    OgvCreativeDto getOgvCreativeDetail(Long creativeId);

}
