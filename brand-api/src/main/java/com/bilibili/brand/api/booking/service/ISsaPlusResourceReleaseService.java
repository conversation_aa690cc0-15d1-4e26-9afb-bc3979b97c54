package com.bilibili.brand.api.booking.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.brand.api.booking.dto.*;

import java.sql.Timestamp;
import java.util.List;

public interface ISsaPlusResourceReleaseService {

    long countBookingItemByCycleId(Integer cycleId);

    List<BookingItemDto> queryResourceBooking(BookingQueryDto queryDto);

    /**
     * 根据排期ID和最大时间(不包含最大时间本身)，查找所有匹配的预约项
     * @param scheduleId
     * @param maxTime
     * @return
     */
    List<BookingItemDto> queryResourceBooking(Integer scheduleId, Timestamp maxTime);

    List<BookingItemDetailDto> convertToDetailDto(List<BookingItemDto> list);

    BookingItemDto queryResourceBookingById(Integer id);

    List<BookingItemDto> queryResourceBookingByIds(List<Integer> ids);

    Integer deleteResourceBookingById(Integer id, Operator operator);

    Integer deleteResourceBookingByIdWithoutValidate(Integer id, Operator operator);

    Integer deleteResourceBooking(BookingItemDto dto,  Operator operator);

    Integer deleteResourceBookingWithoutValidate(BookingItemDto dto,  Operator operator);

    List<String> addResourceBooking(NewResourceBookingDto dto, Operator operator);

    List<BookingItemResultDto> addMultiResourceBooking(List<NewResourceBookingDto> dtoList, Operator operator);

    List<BookingItemResultDto> deleteMultiResourceBooking(List<DeleteResourceBookingDto> dtoList, Operator operator);

    /**
     *  验证  cpt在创建排期的时候
     * @param updateDto
     */
    void validateScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto);
    /**
     * cpt在创建排期的时候，相应的更新预约的状态及相关属性
     * @return
     */
    Integer scheduleTheResourceBooking(UpdateScheduleBookingDto updateDto);

    /**
     * cpt在删除排期的时候，相应的更新预约的状态及相关属性
     * @param updateDto
     * @return
     */
    Integer unScheduleTheResourceBooking(UpdateScheduleBookingDto updateDto);

    /**
     * 查询位次下所有已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp> queryAllBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 查询位次下所有我已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp> queryAllMyBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 查询多个位次下所有我已排期的时间
     * @param queryDto
     * @param operator
     * @return
     */
    List<Timestamp>  multiQueryMyBookedTime(BookingTimeQueryDto queryDto, Operator operator);

    /**
     * 预约资源自动释放
     */
    void bookingAutoRelease(List<TopViewSourceReleaseDto> willReleaseTopViewSourceList,
                            List<Integer> releasedTopViewSourceIdList);

    /**
     * 当日预约资源自动释放
     */
    void bookingAutoTodayRelease(List<TopViewSourceReleaseDto> willReleaseTopViewSourceList,
                            List<Integer> releasedTopViewSourceIdList);

    /**
     * 只用于预约页面的第一次上线
     *
     * 上线之前，手动将老的排期数据刷到预约表里
     */
//    void refreshCptSchedule2BookingResource();

    void clearAllBookingResource();

    /**
     * 清空booking表中的脏数据
     */
    void clearBookingDatasNotMappingWithSchedule();

    /**
     * 获取即将释放的预约，与 bookingAutoRelease() 里逻辑一致
     */
    List<ResourceBookingNotificationDto> getReleasingBookings(List<BookingItemDto> bookingItemDtos, Operator operator);

    long countBookingItem(BookingItemCountQueryDto countQueryDto);

    List<BookingItemDto> queryResourceBooking(Integer cycleId, Integer sourceId);

    List<ResourceBookingNotificationDto> releaseAccountBooking(Integer accountId, Timestamp groupDate);
    void addValidateAndDecorate(NewResourceBookingDto dto, Operator operator);

    void scheduleForTopView(List<Integer> topViewSourceIdList, Integer gdOrderId, Integer gdScheduleId, Operator operator);

    void scheduleForTopViewPlus(List<Integer> ids, Integer gdOrderId, Integer gdScheduleId, Operator operator);

    void bookingForTopView(SingleResourceBookingDto singleDto, Integer rotationNum, Operator operator);

    void deleteForTopView(Integer topViewSourceId, Operator operator);
}
