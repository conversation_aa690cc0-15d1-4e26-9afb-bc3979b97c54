package com.bilibili.brand.api.stock.dto.ssa;

import lombok.*;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class QueryStockRequestDto implements Serializable {

    private static final long serialVersionUID = 7556246713265992575L;

    private String request_id;

    /**
     * 格式yyyyMMdd
     */
    private String date;

    private List<StockTaskDto> tasks;

    private Integer scene;

}
