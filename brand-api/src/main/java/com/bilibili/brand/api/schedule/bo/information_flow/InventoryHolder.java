package com.bilibili.brand.api.schedule.bo.information_flow;

import com.bilibili.brand.api.stock.dto.ssa.QueryStockResponseDto;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.QueryInventoryTimeInfoBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryHolder {

    private QueryInventoryTimeInfoBo time;

    private QueryStockResponseDto response;
}
