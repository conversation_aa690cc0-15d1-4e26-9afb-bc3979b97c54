package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/9/25.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PlatformCtrQueryParamDto {
    private List<Integer> area;
    private List<Integer> gender;
    private List<Integer> age;
    private List<Integer> inlineSalesType;
    private Integer templateId;
    private Integer platformId;
    private Integer orderId;
}
