package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 过渡视频DTO
 *
 * <AUTHOR> Assistant
 * @date 2024/12/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransitionVideoDto implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 视频URL
     */
    private String url;
    
    /**
     * 业务ID（IP视频库ID）
     */
    private Integer bizId;
    
    /**
     * 视频MD5
     */
    private String md5;
    
    /**
     * 视频宽度
     */
    private Integer width;
    
    /**
     * 视频高度
     */
    private Integer height;
    
    /**
     * 视频时长（毫秒）
     */
    private Long duration;
    
    /**
     * 视频大小（字节）
     */
    private Long size;
}
