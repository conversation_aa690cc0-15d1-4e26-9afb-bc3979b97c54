package com.bilibili.brand.api.ext.Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpActReserveRelationResList implements Serializable {

    private static final long serialVersionUID = 1613370825159696309L;

    private Map<Long, UpActReserveRelationInfoDto> list;
}
