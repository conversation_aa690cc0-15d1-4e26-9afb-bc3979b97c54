package com.bilibili.brand.api.resource.slot_group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResSlotDto implements Serializable{

	private static final long serialVersionUID = 3792160124546678532L;
	private Integer id;

	private String name;

}
