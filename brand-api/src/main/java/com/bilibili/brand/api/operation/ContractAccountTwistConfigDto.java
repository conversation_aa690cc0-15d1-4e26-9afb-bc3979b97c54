package com.bilibili.brand.api.operation;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/8
 */
@Data
public class ContractAccountTwistConfigDto {

    /**
     * 类型，0：闪屏，1：inline，2：story
     */
    private Integer type;

    /**
     * 平台，1：ios，2：android
     */
    private Integer platform;

    /**
     * 扭一扭角度
     */
    private Integer angle;

    /**
     * 扭一扭加速度
     */
    private Float speed;

    /**
     * 扭一扭反向角度
     */
    private Float reverseAngle;
}
