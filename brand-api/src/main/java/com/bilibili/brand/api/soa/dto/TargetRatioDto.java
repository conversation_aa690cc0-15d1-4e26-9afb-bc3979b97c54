package com.bilibili.brand.api.soa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by fanwen<PERSON> on 2019/10/23.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TargetRatioDto implements Serializable {
    private static final long serialVersionUID = -7310652305596765366L;
    private Integer targetKeyId;
    private Integer ratio;
}
