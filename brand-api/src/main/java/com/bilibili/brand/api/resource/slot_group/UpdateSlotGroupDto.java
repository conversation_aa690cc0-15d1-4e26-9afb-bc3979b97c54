package com.bilibili.brand.api.resource.slot_group;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateSlotGroupDto implements Serializable{

	private static final long serialVersionUID = -2599252651614299170L;
	private Integer id;
	
	private String slotGroupName;

	private Integer channelId;

	private List<Integer> sysTypes;

}
