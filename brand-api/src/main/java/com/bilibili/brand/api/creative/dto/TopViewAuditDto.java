package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TopViewAuditDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer topViewId;
    private Integer version;
    private String reason;
}
