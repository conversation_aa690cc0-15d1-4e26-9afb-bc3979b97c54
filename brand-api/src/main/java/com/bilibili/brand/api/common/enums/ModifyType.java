/**
 * <AUTHOR>
 * @date 2017年5月25日
 */

package com.bilibili.brand.api.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum ModifyType {
    ADD_ORDER("添加订单"),
    UPDATE_ORDER("更新订单"),
    DELETE_ORDER("删除订单"),
    ADD_BIDDER_CONFIG("添加DSP配置"),
    UPDATE_BIDDER_CONFIG("修改DSP配置"),
    ENABLE_BIDDER_CONFIG("启用DSP配置"),
    PAUSE_BIDDER_CONFIG("禁用DSP配置"),
    UPDATE_DOCUMENT_BLACKLIST("修改文案黑名单"),
    UPDATE_WEBSITES_CONFIG("修改网址黑名单"),
    ADD_APP_PACKAGE_CONFIG("添加应用包配置"),
    DELETE_APP_PACKAGE_CONFIG("删除应用包配置"),
    UPDATE_APP_PACKAGE_CONFIG("修改应用包配置");

    @Getter
    private String value;
}