package com.bilibili.brand.api.common.enums;

import java.util.Objects;

/**
 * Created by walker on 16/8/30.
 */
public enum PromotionPurposeType {
    DEFAULT(0, ""),
    APP(1, "APP推广"),
    LANDING_PAGE(2, "落地页"),
    VIDEO(3, "视频页"),
    APP_DOWNLOAD(4, "应用下载"),
    LIVE(8, "直播"),
    ARCHIVE_CONTENT(7, "投稿内容"),
    BRAND_VIDEO_PROMOTION(10, "bilibili视频"),


    OGV(20, "OGV视频"),
    INTEGRATED_MARKETING(22, "整合营销"),
    OGV_STANDARD(23, "OGV标版广告"),
    ANDROID_GAME_DOWNLOAD(24, "安卓游戏下载"),
    ;

    private final Integer code;

    private final String desc;

    PromotionPurposeType(int code, String desc) {

        this.code = code;
        this.desc = desc;
    }

    public static PromotionPurposeType getByCode(Integer code) {
        for (PromotionPurposeType promotionPurposeType : values()) {
            if (Objects.equals(promotionPurposeType.code, code)) {
                return promotionPurposeType;
            }
        }

        throw new IllegalArgumentException("unknown promotion purpose type code:" + code);
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static PromotionPurposeType getByCodeWithoutEx(Integer code) {
        for (PromotionPurposeType promotionPurposeType : values()) {
            if (Objects.equals(promotionPurposeType.code, code)) {
                return promotionPurposeType;
            }
        }

        return DEFAULT;
    }
}
