package com.bilibili.brand.api.common.enums;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

public enum ShowPriorityType {

    NORMAL(0, "常规展示"),
    //全量包段（crm那边用cpt计费,约量的时候按照预约时间段的所有流量的3/2约量）逻辑售卖,
    //创意上如果开启直播出直播间,直播间未开启的时候优先兜底创意
    BUY_OUT_AND_SHOW_BY_LIVE_STATUS(1, "按全量包段售卖且根据直播间的状态展示创意"),

    //按配置包段（crm那边用cpt计费,约量的时候按照资管配置的预约量，比如每小时2000cpm）逻辑售卖,
    //创意上如果开启直播出直播间,直播间未开启的时候优先出兜底创意
    BUY_USE_CONFIG_AND_SHOW_BY_LIVE_STATUS(2, "按包段的配置售卖且根据直播间的状态展示创意"),

    //按GD逻辑售卖,
    //创意上如果开启直播出直播间,直播间未开启的时候优先出兜底创意
    BUY_USE_GD_AND_SHOW_BY_LIVE_STATUS(3, "GD售卖且根据直播间的状态展示创意"),

//    //全量包段逻辑售卖,和ShowPriorityType中的Priority没必然关系
//    //没有所谓的兜底创意，就是在选择的时间段内直接包段售卖
//    BUY_OUT_AND_SHOW_BY_NORMAL(4, "按全量包段售卖"),
    ;

    private final Integer code;
    private final String desc;

    public static final List<Integer> PRIORITY_TYPES = Lists.newArrayList(
            BUY_OUT_AND_SHOW_BY_LIVE_STATUS.code
//https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002918552
//            BUY_USE_CONFIG_AND_SHOW_BY_LIVE_STATUS.code
    );

    //可能产生包段的优先级
    public static final Set<Integer> POSSIBLE_TIME_PERIOD_PRIORITY_TYPES = Sets.newHashSet(
            BUY_OUT_AND_SHOW_BY_LIVE_STATUS.code);

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ShowPriorityType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ShowPriorityType getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ShowPriorityType bean : values()) {
            if (Objects.equals(bean.getCode(), code)) {
                return bean;
            }
        }
        return null;
    }


    public static Optional<ShowPriorityType> getByCodeOptional(Integer code) {
        if (code == null) {
            return Optional.empty();
        }
        for (ShowPriorityType showPriorityType : values()) {
            if (showPriorityType.code.equals(code)) {
                return Optional.of(showPriorityType);
            }
        }
        return Optional.empty();
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ShowPriorityType showPriorityType : values()) {
            if (showPriorityType.code.equals(code)) {
                return showPriorityType.getDesc();
            }
        }
        return null;
    }
}
