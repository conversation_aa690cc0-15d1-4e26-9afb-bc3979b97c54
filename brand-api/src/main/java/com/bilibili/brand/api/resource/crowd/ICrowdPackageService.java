package com.bilibili.brand.api.resource.crowd;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.brand.api.dmp.dto.CrowdPackDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/12/11
 */
public interface ICrowdPackageService {

    Page<BrandCrowdPackageDto> queryCrowdPackages(String name,
                                                  long startTime,
                                                  long endTime,
                                                  Integer scene,
                                                  int page,
                                                  int size);

    Page<BrandCrowdPackageDto> queryCrowdPackages(String packageName,
                                                  List<Long> packageIds,
                                                  long startTime,
                                                  long endTime,
                                                  List<Integer> scene,
                                                  int page,
                                                  int size);

    @Deprecated
    Map<Integer, String> queryPackageName(List<Integer> crowdPackageIds, List<Integer> excludeCrowdPackageIds, Integer product);

    void createCrowdPackage(long packageId,
                            long startTime,
                            long endTime,
                            int scene,
                            Operator operator);

    void batchCreateCrowdPackage(List<Long> packageIds,
                            long startTime,
                            long endTime,
                            List<Integer> scenes,
                            Operator operator);


    void updateCrowdPackage(long id,
                            long startTime,
                            long endTime,
                            Operator operator);

    void deleteCrowdPackage(long id, Operator operator);

    List<Integer> getSceneCode (Integer templateId, Integer orderProduct);

    Map<Integer, BrandCrowdPackageDto> queryPackage(List<Integer> crowdPackageIds, List<Integer> excludeCrowdPackageIds, Integer product);
}
