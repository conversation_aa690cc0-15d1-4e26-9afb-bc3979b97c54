package com.bilibili.brand.api.resource.price.gd;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/6/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GdPriceResultDto {

    /**
     * 价格类型
     * @see com.bilibili.brand.api.common.enums.GdPriceTypeEnum
     */
    private Integer priceType;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 总价
     */
    private Long totalPrice;

    /**
     * 当前价格对应的刊例周期
     */
    private Integer cycleId;

    /**
     * 单价组成描述
     */
    private String unitPriceDesc;
}
