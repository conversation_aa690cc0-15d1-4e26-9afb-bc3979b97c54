package com.bilibili.brand.api.schedule.service;

import com.bilibili.brand.api.schedule.bo.TempScheduleBo;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2023/2/13
 */
public interface IGdTempScheduleService {

    int saveTempSchedule(TempScheduleBo tempSchedule);

    void updateTempScheduleToFail(int id, String errorMsg);

    void updateTempScheduleToFail(Long dealSeq, Timestamp beginTime, String errorMsg);

    void deleteSchedule(Long dealSeq);

    void deleteSchedule(int id);
}
