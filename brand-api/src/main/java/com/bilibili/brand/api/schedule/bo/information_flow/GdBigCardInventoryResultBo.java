package com.bilibili.brand.api.schedule.bo.information_flow;

import com.bilibili.ssa.platform.api.schedule.dto.inventory.GdInventoryDetailBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/16
 */
@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class GdBigCardInventoryResultBo extends GdInventoryDetailBo {

    /**
     * 按照资源位(GdColdBootInventoryBo::sourceProphetTargetId)分组
     */
    private Map<Integer, List<GdColdBootInventoryBo>> coldBootInventoryMap;

}
