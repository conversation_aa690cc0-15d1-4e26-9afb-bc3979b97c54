package com.bilibili.brand.api.common.enums;

/**
 * Created by many2023 on 2017/5/22.
 */
public enum CreativeExportType {
    VIDEO(0, "视频"),
    PICTURE(1, "图片"),
    NORMAL(3, "普通");

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CreativeExportType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CreativeExportType getByCode(int code) {
        for (CreativeExportType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code.");
    }
}
