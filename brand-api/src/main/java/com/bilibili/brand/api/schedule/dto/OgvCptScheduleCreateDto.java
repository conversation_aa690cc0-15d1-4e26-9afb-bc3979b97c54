package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/12 20:01
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OgvCptScheduleCreateDto extends OgvScheduleCreateDto implements Serializable {
    private static final long serialVersionUID = -1790652024931265822L;
    private Integer rotationNum;
}
