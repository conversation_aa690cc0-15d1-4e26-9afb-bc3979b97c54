package com.bilibili.brand.api.material;


import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.material.bo.IPVideoBo;
import com.bilibili.brand.api.material.bo.VideoCompoundBo;

import java.util.List;

public interface IIPVideoService {

    /**
     * @param name      视频名称
     * @param videoTypeList
     * @param strictMatch 是否严格匹配宽高
     * @param maxDuration 最大时长(ms)
     * @return IP视频列表
     * <AUTHOR>
     * @Description 根据视频名称获取IP视频列表
     **/
    PageResult<IPVideoBo> getIPVideoBoList(String name, Integer page, Integer size, Integer dealStatus,
                                           Integer width, Integer height, List<Integer> videoTypeList, Long maxSize,
                                           Long maxDuration, boolean strictMatch);

    /**
     * <AUTHOR>
     * @Description 合成IP视频
     **/
    void compoundIPVideo(VideoCompoundBo videoCompoundBo);

    /**
     * <AUTHOR>
     * @Description 删除IP视频
     * @Param id 自增id
     **/
    void removeIPVideo(Integer id) throws ServiceException;

    IPVideoBo getIPVideoById(Integer id);

}
