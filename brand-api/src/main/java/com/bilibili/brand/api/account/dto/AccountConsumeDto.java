package com.bilibili.brand.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountConsumeDto implements Serializable {
    private static final long serialVersionUID = -8638050542896214064L;
    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 现金
     */
    private Long cash;

    /**
     * 红包
     */
    private Long redPacket;

    /**
     * 日期
     */
    private Timestamp date;

    public AccountConsumeDto sum(AccountConsumeDto dto){
        if (date == null) {
            this.date = dto.getDate();
        }
        if (accountId == null) {
            this.accountId = dto.getAccountId();
        }
        if (cash == null) {
            this.cash = 0L;
        }
        if (redPacket == null) {
            this.redPacket = 0L;
        }

        this.cash += dto.getCash();
        this.redPacket += dto.getRedPacket();

        return this;
    }


}