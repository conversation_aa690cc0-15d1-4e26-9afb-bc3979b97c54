package com.bilibili.brand.api.schedule.service.inventory;

import com.bilibili.brand.api.schedule.bo.ssa.SsaInventoryContext;
import com.bilibili.brand.api.schedule.bo.ssa.SsaOttQueryInventoryBo;
import com.bilibili.ssa.platform.api.schedule.dto.inventory.SsaOttGdInventoryResultBo;
import com.bilibili.ssa.platform.api.schedule.dto.ott.SsaOttGdCreateSingleScheduleBo;

/**
 * <AUTHOR>
 * @date 2023/10/8
 */
public interface ISsaOttGdInventoryService {

    SsaOttGdInventoryResultBo queryInventory(SsaOttQueryInventoryBo queryInventoryBo);

    void lockInventory(SsaOttGdCreateSingleScheduleBo scheduleBo, SsaInventoryContext ctx);
}
