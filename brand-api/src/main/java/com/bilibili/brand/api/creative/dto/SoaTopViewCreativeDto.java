package com.bilibili.brand.api.creative.dto;

import com.bilibili.brand.api.schedule.dto.TopViewScheduledRotationDto;
import com.bilibili.brand.dto.bdata.ProductLabelDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenCustomizedDTO;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenDynamicButtonBO;
import com.bilibili.ssa.platform.api.splash_screen.dto.SplashScreenJumpDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.annotation.Nullable;
import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SoaTopViewCreativeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer topViewId;
    private Long ssaCreativeId;
    private Long hfIosCreativeId;
    private Long hfAndroidCreativeId;

    private Long newHfIosCreativeId;
    private Long newHfAndroidCreativeId;

    private Integer businessSideId;
    private String businessSideName;
    private Integer orderId;
    private String orderName;
    private Integer scheduleId;
    private String scheduleName;
    private Integer sellingType;
    private String sellingTypeName;

    private String ssaTitle;
    private String ssaImageUrl;
    private String ssaVideoUrl;
    private String eggVideoUrl;
    private Integer showTime;

    private String cptTitle;
    private String cptImageUrl;
    private String cptVideoUrl;
    private List<String> sourceNameList;

    //新版首焦16:9
    private String newCptTitle;
    private String newCptImageUrl;
    private String newCptVideoUrl;
    private List<String> newSourceNameList;
    private List<GdCreativeImageDto> newHfImageList;
    private Integer newHfJumpType;
    private String newHfPromotionPurposeContent;//用户输入保持一致
    private String newHfBrandName;
    private String newHfFaceUrl;
    private String newHfFaceMd5;
    private String actualNewHfPromotionPurposeContent;//可跳转链接

    private String creativeName;
    private Timestamp beginDate;
    private Timestamp endDate;
    private List<TopViewScheduledRotationDto> launchDateList;
    private Integer cmMark;
    private String cmMarkDesc;
    private Integer jumpType;
    private String jumpTypeDesc;
    private String promotionPurposeContent;
    private String schemeUrl;
    private Integer status;
    private String statusDesc;
    private Integer sourceType;
    private String sourceTypeDesc;
    private Integer showStyle;
    private String showStyleDesc;
    private Integer ssaScreenStyle;
    private String ssaScreenStyleDesc;
    private Integer hfAdType;
    private String hfAdTypeDesc;
    private Integer version;
    private String reason;
    private Integer promotionPurposeType;
    private String promotionPurposeTypeDesc;

    /**
     * 首焦安卓展示监控链接
     */
    private String hfAndroidCustomizedImpUrl;

    /**
     * 首焦安卓点击监控链接
     */
    private String hfAndroidCustomizedClickUrl;


    /**
     * 首焦IOS展示监控链接
     */
    private String hfIOSCustomizedImpUrl;

    /**
     * 首焦点击监控链接
     */
    private String hfIOSCustomizedClickUrl;


    /**
     * 闪屏跳转模型列表
     */
    private List<SplashScreenJumpDTO> splashScreenJumpDTOS;

    /**
     * 闪屏监控模型列表
     */
    private List<SplashScreenCustomizedDTO> ssaCustomizedDTOS;

    /**
     * ssaButtonStyle
     */
    private Integer buttonStyle;

    /**
     * 按钮信息
     */
    private List<SplashScreenDynamicButtonBO> buttonBOs;

    //选择式按钮纯文字按钮唤起文案
    private String selectSchemeCopywriting;

    //选择式按钮纯文字按钮跳转文案
    private String selectGuideInstructions;

    //产品型号
    //目前始终为null
    @Nullable
    private ProductLabelDto productLabel;

    /**
     * 首焦IOS点击监控链接列表
     */
    private List<String> hfIOSCustomizedClickUrlList;

    /**
     * 首焦安卓点击监控链接列表
     */
    private List<String> hfAndroidCustomizedClickUrlList;

    // 直播预约id
    private Long liveBookingId;

    //首焦跳转信
    private SoaTopViewHfJumpDto hfJump;
}
