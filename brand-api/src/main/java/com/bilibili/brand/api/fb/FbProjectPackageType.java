package com.bilibili.brand.api.fb;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/6/26
 **/

@AllArgsConstructor
@Getter
public enum FbProjectPackageType {
    UNKNOWN(0, "未知包类型"),
    GENERAL(1, "项目非标通包"),
    CUSTOMER(2, "项目非标客户包"),
    ;

    private final Integer code;

    private final String desc;

    public static FbProjectPackageType getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getCode(), code))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(String.format("包类型「%s」不存在", code)));
    }

    public static FbProjectPackageType getByCodeWithoutExp(Integer code) {
        return Arrays.stream(values())
                .filter(type -> Objects.equals(type.getCode(), code))
                .findFirst()
                .orElse(UNKNOWN);
    }

}
