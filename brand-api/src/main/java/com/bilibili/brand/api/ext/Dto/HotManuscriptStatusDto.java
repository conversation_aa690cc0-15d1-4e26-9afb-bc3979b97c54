package com.bilibili.brand.api.ext.Dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotManuscriptStatusDto implements Serializable {

    private static final long serialVersionUID = 8051880313021845846L;

    private List<HotManuscriptStatusItem> items;

 
}
