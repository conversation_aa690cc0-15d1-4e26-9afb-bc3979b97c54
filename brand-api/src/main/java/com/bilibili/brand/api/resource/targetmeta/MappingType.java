package com.bilibili.brand.api.resource.targetmeta;

/**
 * Created by walker on 16/9/13.
 */
public enum MappingType {

    NONE(0, "无映射"),
    RANGE(1, "区间映射"),
    ID_SET(2, "ID映射"),
    NAME_SET(3, "名称映射");

    private int code;

    private String name;

    private MappingType(int code, String name) {

        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public final static MappingType getByCode(Integer code) {
        for (MappingType mappingType : MappingType.values()) {
            if (mappingType.getCode() == code) {
                return mappingType;
            }
        }
        return NONE;
    }
}
