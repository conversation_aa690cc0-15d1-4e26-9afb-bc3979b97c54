package com.bilibili.brand.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/3/6.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdxOrderDto {
    /**
     * id
     */
    private Integer id;

    /**
     * deal id
     */
    private Long dealId;

    /**
     * bidder id
     */
    private Integer bidderId;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 0GD订单 1CPT订单
     */
    private Integer orderType;

    /**
     * ecpm价格
     */
    private Long ecpm;

    /**
     * 合同号
     */
    private Long contractNumber;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 0其他 1内部、2售卖、3配送、4补量
     */
    private Integer resourceType;

    /**
     * CRM订单id
     */
    private Integer crmOrderId;

    /**
     * 关联其他系统订单id
     */
    private Integer mappingOrderId;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private Integer premiumRatio;

    private Integer budget;

    private List<AdxOrderLaunchTimeDto> adxOrderLaunchTimes;

    /**
     * 闪屏日下发量，单位次
     */
    private Long dayDeliverImpressionLimit;

    /**
     * 闪屏展示样式:1-全屏图片 2-半屏图片  3-半屏横屏视频  4-半屏竖屏视频 5-全屏竖屏视频
     */
    private Integer ssaShowStyle;

    /**
     * 映射id
     */
    private Long mappingId;

    /**
     * deal id组
     */
    private Long dealGroupId;


    /**
     * 目标展现量,单位cpm
     */
    private Integer totalImpression;

    /**
     * 模板id
     */
    private Integer templateId;

    /**
     * dsp的资源位ID
     */
    private String bidderSourceId;

    /**
     * 投放开始时间
     */
    private Timestamp beginTime;

    /**
     * 投放结束时间
     */
    private Timestamp endTime;
}
