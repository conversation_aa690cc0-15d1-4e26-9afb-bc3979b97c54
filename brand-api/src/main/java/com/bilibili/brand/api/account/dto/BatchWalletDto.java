package com.bilibili.brand.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * Created by fan<PERSON><PERSON> on 2016/10/21.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchWalletDto {
    private Integer accountId;
    private Long money;
    private Timestamp date;
}
