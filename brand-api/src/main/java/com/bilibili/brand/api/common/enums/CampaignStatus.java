/** 
* <AUTHOR> 
* @date  2017年8月9日
*/ 

package com.bilibili.brand.api.common.enums;

/*
+----------------------------+
| +--------+    +---------+  |
| | VALID  +---->BUDGET_EX|  |
| |        <----+CEED     |  |
| +--------+    +---------+  |
|            +^              |
+-+--------------------+-----+
  |          ||        |
  |          ||        |
  |      +---v---+     |
  +------+PAUSED +-----+
  |      +---+---+     |
  |          |         |
  |      +---v---+     |
  +------+FINISHED<----+
  |      +---+---+
  |          |
  |      +---v---+
  +------>DELETED|
         +-------+
 */

public enum CampaignStatus {
	VALID(1, "有效"),
	PAUSED(2, "已暂停"),
	FINISHED(3, "已结束"),
	DELETED(4, "已删除"),
	BUDGET_EXCEED(5, "预算超限");
	
	private int code;
	private String desc;
	
	CampaignStatus(int code, String desc) {
		this.code = code;
		this.desc = desc;
	}
	
	public static CampaignStatus getByCode(int code) {
		for(CampaignStatus cs: values()) {
			if(cs.code == code) {
				return cs;
			}
		}
		
		return null;
	}

	public int getCode() {
		return code;
	}

	public String getDesc() {
		return desc;
	}
}
