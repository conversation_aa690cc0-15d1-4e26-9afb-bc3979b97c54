package com.bilibili.brand.api.common.enums;

/**
 * 1-展示监控 2-点击监控 3-播放0s监控 4-播放3s监控 " +
 *             "5-播放5s监控 6-游戏点击监测 7-自定义监测
 */
public enum MonitorType {
    SHOW(1, "展示监控"),
    CLICK(2, "点击监控"),
    ZERO_S(3, "播放0s监控"),
    THREE_S(4, "播放3s监控"),
    FIVE_S(5, "播放5s监控"),
    GAME_CLICK(6, "游戏点击监测"),
    PLAY_CUSTOMIZE(7, "播放自定义监测"),
    TEN_S(9, "播放10s监控"),
    FIFTEEN_S(10, "播放15s监控"),
    ;

    private final String desc;
    private final Integer code;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    MonitorType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MonitorType getByCode(int code) {
        for (MonitorType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("未知的监控链接类型！");
    }
}
