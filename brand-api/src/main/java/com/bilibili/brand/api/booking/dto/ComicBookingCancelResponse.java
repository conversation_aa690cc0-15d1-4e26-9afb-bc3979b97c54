package com.bilibili.brand.api.booking.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2019-01-28
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ComicBookingCancelResponse implements Serializable {

    private static final long serialVersionUID = 4897158488400299717L;

    private Integer code;

    private String message;

}
