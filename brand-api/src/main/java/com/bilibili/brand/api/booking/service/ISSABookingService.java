package com.bilibili.brand.api.booking.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.booking.dto.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by fan<PERSON><PERSON> on 2019/5/21.
 */
public interface ISSABookingService {
    Map<Integer, Long> getBookingCellCreativeCountMap(List<CptSsaDayBookingCellDto> bookingCellDtos);

    List<List<CptSsaDayBookingCellDto>> queryTable(Timestamp beginTime, Timestamp endTime, Integer salesType);

    Integer create(CptSsaDayBookingDto cptSsaDayBookingDto, Operator operator);

    void delete(Integer id, Operator operator);

    void schedule(SsaScheduleBookDto ssaScheduleBookDto, Operator operator);

    void bookedByGd(Integer orderId, Integer scheduleId, List<Timestamp> groupDates, Operator operator, Integer salesType);

    void releaseSchedule(Integer scheduleId, Timestamp date, Operator operator);

    void validateSchedule(List<SsaBookDateDto> bookDateDtos, Operator operator, Integer bookingRatio);

    List<CptSsaDayBookingDto> queryByAccountId(Timestamp beginTime, Integer accountId, Integer salesType,
                                               Integer bookingRatio);

    Map<Timestamp, List<CptSsaDayBookingDto>> queryAllNotDeletedMapByAccountId(List<Timestamp> groupDates, Integer salesType);

    Map<Timestamp, Integer> getRemainedRotationNumPerDayNew(Timestamp date);

    void releaseJob(Timestamp today, List<Integer> needCheckTopViewSourceIdList,
                    List<TopViewSourceReleaseDto> finalDeleteTopViewSourceList, Integer salesType) throws ServiceException;

    void alertJob(Timestamp today, List<TopViewSourceReleaseDto> willReleaseTopViewSourceList,
                  List<TopViewSourceReleaseDto> finalAlertTopViewSourceList, Integer salesType) throws ServiceException;

    Integer getRotationNum(Timestamp beginTime, Timestamp endTime, Integer orderProduct);

    List<CptSsaDayBookingCellDto> getAlertCptSsaDayBookingDtos(Timestamp today, List<Integer> bookingIds,
                                                               List<Integer> needCheckTopViewSourceIdList, Integer salesType);

    void releaseSsaGdSchedule(Integer scheduleId, Operator operator);

    List<CptSsaDayBookingCellDto> releaseAccountBooking(Integer accountId, Timestamp groupDate);

    void scheduleForTopView(List<Integer> topViewSourceIdList, Integer gdOrderId, Integer gdScheduleId, Operator operator);

    List<CptSsaDayBookingDto> getByTime(Timestamp beginTime, Timestamp endTime, Integer salesType);

    void bookingForTopView(Timestamp groupDate, Integer rotationNum, Integer cycleId, Integer topViewSourceId,
                                  Integer topViewType, Operator operator, Integer bookingRatio, Long relatedId);

    void deleteForTopView(Integer topViewSourceId, Operator operator);
}
