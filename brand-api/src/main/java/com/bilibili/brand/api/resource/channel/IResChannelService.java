package com.bilibili.brand.api.resource.channel;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;

import java.util.List;
import java.util.Map;

public interface IResChannelService {

    ResChannelDto getChannelById(Integer channelId);

    List<ResChannelDto> getChannelsInIds(List<Integer> channelIds);

    Map<Integer, String> getChannelId2NameMapInIds(List<Integer> channelIds);

    List<ResChannelDto> getAll();

}
