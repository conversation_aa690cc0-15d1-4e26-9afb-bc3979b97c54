/**
 * <AUTHOR>
 * @date 2018年1月3日
 */

package com.bilibili.brand.api.soa.service;

import java.util.List;
import java.util.Map;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.order.dto.*;

public interface ISoaGdOrderService {
    NewPDBOrderResult createPDBOrder(NewPDBOrderDto newPDBOrderDto, Operator operator) throws ServiceException;

    void updatePDBOrder(UpdatePDBOrderDto updatePDBOrderDto, Operator operator);

    List<GdOrderDto> queryOrders(QueryOrderParamDto queryOrderParamDto, Operator operator);

    GdOrderDto getOrderById(Integer id, Operator operator);

    GdOrderDto getOrderByCrmOrderId(Integer crmOrderId);

    Map<Integer, GdOrderDto> getOrderMapInOrderIds(List<Integer> orderIds);

    List<GdOrderDto> queryOrders(QueryOrderParamDto queryOrderParamDto);

}
