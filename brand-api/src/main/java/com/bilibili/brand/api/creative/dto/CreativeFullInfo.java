package com.bilibili.brand.api.creative.dto;

import com.bilibili.brand.api.component.ComponentDto;
import com.bilibili.brand.api.order.dto.GdOrderDto;
import com.bilibili.brand.api.schedule.dto.GdTopViewScheduleDto;
import com.bilibili.brand.api.schedule.dto.ScheduleDto;
import com.bilibili.ssa.platform.api.splash_screen.dto.SsaSplashScreenDetailDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/5/20
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreativeFullInfo {
    private Integer orderProduct;
    // 创意信息
    private GdCreativeDto creativeBaseInfo;

    // 排期信息
    private ScheduleDto scheduleInfo;

    // 订单信息
    private GdOrderDto orderInfo;

    // 闪频信息
    private SsaSplashScreenDetailDto splashScreeInfo;

    // TopView排期信息
    private GdTopViewScheduleDto topViewScheduleInfo;

    private List<Integer> searchCptTemplateIds;

    private List<GdCreativeButtonCopyDto> buttonCopyInfos;

    private CreativeTwistDto twist;

    // 天马inline0刷模板
    private List<Integer> zeroFlushTemplateIds;
}
