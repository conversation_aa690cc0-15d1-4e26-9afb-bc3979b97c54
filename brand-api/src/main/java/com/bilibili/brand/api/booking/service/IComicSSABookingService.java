package com.bilibili.brand.api.booking.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.booking.dto.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON>iongyan on 2021/5/30.
 */
public interface IComicSSABookingService {

    //查询漫画闪屏资源
    List<CptSsaDayBookingCellDto> queryTable(Timestamp beginTime, Timestamp endTime) throws ServiceException;

    //查询当前预约包含的创意个数
    Map<Integer, Long> getBookingCellCreativeCountMap(List<CptSsaDayBookingCellDto> bookingCellDtoS);

    //创建预约
    Integer create(CptSsaDayBookingDto cptSsaDayBookingDto, Operator operator) throws ServiceException;

    //取消预约
    void delete(Integer id, Operator operator) throws ServiceException;

    //释放资源位定时任务
    void releaseJob(Timestamp today) throws ServiceException;

    //预警定时任务
    void alertJob(Timestamp today) throws ServiceException;

    //查询即将释放的漫画闪屏
    List<CptSsaDayBookingCellDto> getAlertCptSsaDayBookingDtoS(Timestamp today, List<Integer> bookingIds);

    //释放入参账户下的预约-用于账户余额不足时资源的释放
    List<CptSsaDayBookingCellDto> releaseAccountBooking(Integer accountId, Timestamp groupDate);

    //******以下接口用于排期*******

    //排期占用预约资源
    void schedule(SsaScheduleBookDto ssaScheduleBookDto, Operator operator);

    //释放排期占用的预约
    void releaseSchedule(Integer scheduleId, Timestamp date, Operator operator);

    //校验当前账号是否能占用当前预约,用于排期占用预约资源
    void validateSchedule(List<SsaBookDateDto> bookDateDtoS, Operator operator);

    //查询当前帐号可预约的闪屏信息，主要用于排期
    List<CptSsaDayBookingDto> queryByAccountId(Timestamp beginTime, Integer accountId);

}
