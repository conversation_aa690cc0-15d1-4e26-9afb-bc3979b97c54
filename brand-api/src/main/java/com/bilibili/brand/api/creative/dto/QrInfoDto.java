package com.bilibili.brand.api.creative.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/2
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QrInfoDto implements Serializable {

    /**
     * 二维码边框地址
     */
    private String qrFrameUrl;

    /**
     * 二维码动效地址
     */
    private String qrAnimationUrl;

}
