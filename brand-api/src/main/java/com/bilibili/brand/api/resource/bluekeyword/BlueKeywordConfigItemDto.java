package com.bilibili.brand.api.resource.bluekeyword;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/11 20:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlueKeywordConfigItemDto implements Serializable {
    private static final long serialVersionUID = 1964860497027622507L;

    //小蓝词配置id
    private Long configId;

    //稿件
    private List<Long> avidList;

    //关键词
    private List<String> keywordList;
}
