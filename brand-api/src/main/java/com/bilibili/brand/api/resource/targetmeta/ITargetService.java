package com.bilibili.brand.api.resource.targetmeta;

import com.bilibili.adp.common.bean.Operator;
import java.util.List;
import java.util.Map;

/**
 * Created by walker on 16/9/13.
 */
public interface ITargetService {

    Target getTargetByType(Integer type);

    List<Target> getAll();

    List<Target> getAllValid();

    Map<TargetType, Boolean> getTargetType2Status();

    void createTarget(Target target, Operator operator);

    void updateTarget(Target target, Operator operator);

    void enableTarget(Integer type, Operator operator);

    void disableTarget(Integer type, Operator operator);

}
