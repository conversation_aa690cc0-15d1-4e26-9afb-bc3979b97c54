package com.bilibili.brand.api.resource.price;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhongyuan on 2016/9/29.
 *
 * 定向定价模式信息
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CpmPricing {

    /**
     * ID
     */
    private Integer id;

    /**
     * 定向类型
     */
    private Integer targetType;

    /**
     * 定价类型
     * @see com.bilibili.brand.api.common.enums.PricingType
     */
    private Integer pricingType;

    /**
     * 加价百分比
     */
    private Integer ratio;

    /**
     * 缺省基价
     */
    private Integer defaultBase;

    /**
     * 状态
     * @see com.bilibili.brand.api.common.enums.Status
     */
    private Integer status;
}
