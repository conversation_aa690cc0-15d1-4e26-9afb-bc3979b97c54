package com.bilibili.brand.api.stock.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/9/21.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryCtrParamDto {
    private List<Integer> area;
    private List<Integer> gender;
    private List<Integer> age;
    private List<Integer> inlineSalesType;
    private List<Integer> tags;
    private Integer templateId;
    private Integer platformId;
    private Integer categoryFirstId;
    private Integer categorySecondId;
}
