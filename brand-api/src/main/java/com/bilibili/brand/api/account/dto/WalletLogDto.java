package com.bilibili.brand.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created by fanwen<PERSON> on 16/9/30.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WalletLogDto {
    private Timestamp ctime;
    private BigDecimal money;
}
