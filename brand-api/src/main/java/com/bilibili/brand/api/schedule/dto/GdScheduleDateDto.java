/** 
* <AUTHOR> 
* @date  2017年10月24日
*/

package com.bilibili.brand.api.schedule.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GdScheduleDateDto implements Serializable{
	private static final long serialVersionUID = 1063173496673375064L;
	private Integer id;
	private Integer scheduleId;
	private Integer campaignId;
	private Integer orderId;
	private Integer accountId;
	private Timestamp scheduleDate;
	private Integer rotationNum;

	/**
	 * 外部价,单位(分)
	 */
	private Long externalPrice;

	/**
	 * 内部价,单位(分)
	 */
	private Long internalPrice;

	private Integer impression;

	private Integer gdType;

	/**
	 * 广告位ID
	 */
	private Integer source;

	/**
	 * 开始时间
	 */
	private Timestamp beginTime;

	/**
	 * 结束时间
	 */
	private Timestamp endTime;

	/**
	 * 当前预订的库存
	 * PS：
	 * 单独定义该字段而没直接使用impression字段的原因：
	 * 因为impression有了其他语义（从gd_flow_allocation查出来的，它是总量【参考写入的代码，比如PD】，即使是在分天的情况），
	 * 而我们需要一个每天的预定量，因此单独一个字段
	 */
	private Integer dayImpression;

	private Integer resourceType;
}
