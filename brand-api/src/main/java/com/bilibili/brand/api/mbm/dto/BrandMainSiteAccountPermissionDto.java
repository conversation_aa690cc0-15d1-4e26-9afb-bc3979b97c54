package com.bilibili.brand.api.mbm.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2024/10/24 11:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BrandMainSiteAccountPermissionDto implements Serializable {
    private static final long serialVersionUID = -1307686685085384584L;
    /**
     * id
     */
    private Long id;

    /**
     * 账号配置id
     */
    private Long configId;

    /**
     * 权限类型，1：空间隐藏
     * @see com.bilibili.enums.BrandMainSiteAccountPermissionEnum
     */
    private Integer code;

    /**
     * 开始时间
     */
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    private Timestamp endTime;

    /**
     * 状态，0：未启用，1：启用
     */
    private Integer status;
}
