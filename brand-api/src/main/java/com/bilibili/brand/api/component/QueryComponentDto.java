package com.bilibili.brand.api.component;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/9 21:24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryComponentDto implements Serializable {
    private static final long serialVersionUID = -8039475776139185737L;
    private Integer componentType;
    private List<Long> componentIds;
    private String componentName;
    private Integer page;
    private Integer size;
}
