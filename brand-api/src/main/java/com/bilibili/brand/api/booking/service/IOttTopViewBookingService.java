package com.bilibili.brand.api.booking.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.api.booking.dto.*;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * Created by fan<PERSON><PERSON> on 2019/5/21.
 */
public interface IOttTopViewBookingService {
    Map<Integer, Long> getBookingCellCreativeCountMap(List<CptSsaDayBookingCellDto> bookingCellDtos);

    List<List<CptSsaDayBookingCellDto>> queryTable(Timestamp beginTime, Timestamp endTime);

    Integer create(CptSsaDayBookingDto cptSsaDayBookingDto, Operator operator);

    void delete(Integer id, Operator operator);

    void schedule(SsaScheduleBookDto ssaScheduleBookDto, Operator operator);

    List<CptSsaDayBookingCellDto> getAlertCptSsaDayBookingDtos(Timestamp today, List<Integer> bookingIds,
                                                               List<Integer> needCheckTopViewSourceIdList);

    void releaseSchedule(Integer scheduleId, Operator operator);

    List<CptSsaDayBookingCellDto> releaseAccountBooking(Integer accountId, Timestamp groupDate);

    void scheduleForTopView(List<Integer> topViewSourceIdList, Integer gdOrderId, Integer gdScheduleId, Operator operator);

    List<CptSsaDayBookingDto> getByTime(Timestamp beginTime, Timestamp endTime);

    void bookingForTopView(Timestamp groupDate, Integer rotationNum, Integer cycleId, Integer topViewSourceId,
                                  Integer topViewType, Operator operator);

    void deleteForTopView(Integer topViewSourceId, Operator operator);

    long countMonthBookingByDayAndAccountId(Timestamp date, Integer accountId);

    long countOperatorDayBookedNum(Timestamp date, Operator operator);
}
