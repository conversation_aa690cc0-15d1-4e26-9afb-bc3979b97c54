package com.bilibili.brand.api.schedule.dto;

import com.bilibili.adp.common.annotation.DatabaseColumnName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * Created by fanwenbin on 2016/12/19.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateScheduleDto {
    private Integer scheduleId;

    @DatabaseColumnName("排期名称")
    private String name;

    @DatabaseColumnName("目标展现")
    private Long totalImpression;

    @DatabaseColumnName("投放日期")
    private List<Timestamp> scheduleDates;

    private List<LocalTime> hourRange;

    private Integer speedMode;

    @DatabaseColumnName("投放时间")
    private Integer hour;

    @DatabaseColumnName("频次限制类型")
    private Integer frequencyUnit;

    @DatabaseColumnName("频次限制")
    private Integer frequencyLimit;

    private String requestId;

    @DatabaseColumnName("唤起外部APP")
    private Integer needWakeApp;
    @DatabaseColumnName("创意形态")
    private Integer creativeStyle;

    private Integer appPackageId;

    //是否分日预约
    private Integer splitDaysFlag;

    //分日预约库存
    private Map<LocalDate, Long> dayImpression;

    private Integer showPriority;

    //以下两个字段用于ott-gd
    //投放主体: 0-蓝V号 1-自建主体
    private Integer launcher;


    private Long mid;

    private Integer openEnterpriseSpace;

    private List<Integer> crowdPackIds;

    //是否投放内链
    private Boolean launchInnerJump;

    private Long avid;

    /**
     * 起飞gd优化目标
     */
    private Integer ocpxTarget;

    private Integer wakeAppType;

    private Boolean isBookingTimePeriod;

    private Integer promotionPurposeType;

    private Integer templateId;


}
