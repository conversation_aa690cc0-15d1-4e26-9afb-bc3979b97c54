package com.bilibili.brand.api.resource.account.group;

import java.util.List;
import java.util.Map;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceException;

/**
 * <AUTHOR>
 * @date 2017年2月9日
 */
public interface IAccountSlotGroupService {

    List<AccountSlotGroupDto> getAccountSlotGroupInSlotGroupIds(List<Integer> slotGroupIds);

    List<Integer> getAllSettedSlotGroupIds();

    List<Integer> getSlotGroupIdsInAccountGroupIds(List<Integer> accountGroupIds);

    Map<Integer, List<Integer>> getSlotGroupId2AccountGroupIdsMapInSlotGroupIds(List<Integer> slotGroupIds);
    
    void batchUpdate(Operator operator, Integer slotGroupId, List<Integer> accountGroupIds) throws ServiceException;
	
}
