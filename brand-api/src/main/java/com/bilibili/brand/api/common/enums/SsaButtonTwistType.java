package com.bilibili.brand.api.common.enums;

import lombok.Getter;

/**
 * 闪屏扭一扭限定扭动方式
 *
 * <AUTHOR>
 * @date 2023/3/9
 */
@Getter
public enum SsaButtonTwistType {

    IGNORE(-1, "忽略当前轴"),
    ANY(0, "任意角度满足即可触发"),
    LEFT(1, "左扭"),
    RIGHT(2, "右扭"),
    ;

    private final int code;

    private final String desc;

    SsaButtonTwistType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static SsaButtonTwistType getByCode(int code) {
        for (SsaButtonTwistType value : SsaButtonTwistType.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

}
