/** Copyright bilibili.com 2016. All rights reversed.
 *
 * @Description:
 * @date 2016年11月3日
 */

package com.bilibili.brand.common;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class JsonManagerImpl implements JsonManager {
    private static final Logger logger = LoggerFactory.getLogger(JsonManagerImpl.class);

    private ObjectMapper objectmapper;

    public JsonManagerImpl() {
        objectmapper = new ObjectMapper();
    }

    @Override
    public <T> T deserilize(String json, Class<T> t) {
        T instance = null;
        try {
            instance = objectmapper.readValue(json, t);
        } catch (Exception e) {
            logger.error(new LogMessageBuilder()
                    .withMessage("Failed to deserilize json")
                    .withParameter("json", json)
                    .withParameter("objectTyle", t)
                    .build(), e);
        }
        return instance;
    }

    @Override
    public <T> String serilize(T object) {
        String json = null;
        try {
            json = objectmapper.writeValueAsString(object);
        } catch (Exception e) {
            logger.error(new LogMessageBuilder()
                    .withMessage("Failed to serilize object")
                    .withParameter("object", object)
                    .build(), e);
        }
        return json;
    }

}
