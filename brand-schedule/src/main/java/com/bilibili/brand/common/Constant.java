package com.bilibili.brand.common;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.enums.PlatformType;
import com.bilibili.ssa.platform.common.enums.OrderProduct;
import com.bilibili.ssa.platform.common.enums.SystemConfigEnum;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.io.LineIterator;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Tianpeng Li on 2017/5/9.
 */
@Component
@Slf4j
public class Constant {

    public static Map<Integer, Integer> FeedSource = Maps.newHashMap();

    public static final String  FROM_SPMID = "from_spmid";

    public static final String  __FROMSPMID__ = "__FROMSPMID__";

    @Deprecated
    public static final int GD_FIRST_BRUSH_PROPHET_MAPPING_CONTENT = 1;

    public static final String YELLOW_SHOW_ = "yellowShow_";

    public static final String  TRACKID = "trackid";

    public static final List<Integer> OTT_INLINE_SOURCE_ID = Lists.newArrayList(4425);
    /**
     * 由于当前shouStyle的设计，考虑直接加常量
     */
    public static final int SSA_ARCHIVE_CARD = 109;

    public static final Set<Integer> SplashSource = Sets.newHashSet(928, 929, 930);

    public static final List<Integer> GD_BIG_CARD_SOURCE = Lists.newArrayList(1891, 1898);

    //gd小卡能预约的位置
    public static final List<Integer> GD_SMALL_CARD_SOURCE = Lists.newArrayList(
            1892,1893,1894,1895,1896,1981,1982,1983,1984,
            1899,1900,1901,1902,1903,1989,1990,1991,1992);

    /**
     * 所有在prophet表中的source，记录的是mapping_content的值
     * 后续改成查表初始化
     */
    public static final Set<Integer> ALL_SOURCE_PROPHET_MAPPING_CONTENT = Sets.newHashSet(
            1, 2, 3, 4, 5, 6, 7, 8, 1892, 1893, 1894, 1896, 1981, 1899, 1900, 1901, 1903, 1989, 4422, 4353, 4356, 2030, 2031, 4490, 5887, 5888);

    public static Map<Integer, Integer> PLATFORM2SOURCE = new HashMap<Integer, Integer>() {
        private static final long serialVersionUID = -2457581679892233229L;

        {
            put(1, 1891);
            put(2, 1898);
            put(10, OTT_INLINE_SOURCE_ID.get(0));
    }};

    public static Map<Integer, Integer> PLATFORM_2_FIFTH_SOURCE = new HashMap<Integer, Integer>() {


        private static final long serialVersionUID = 3121367960177700795L;

        {
            put(1, 1896);
            put(2, 1903);
            put(10, 4425);
        }};

    public final static Map<Integer, Integer> PLATFORM_2_UNDER_BOX_SOURCE = new HashMap<Integer, Integer>() {
        private static final long serialVersionUID = 3121367960177700795L;

        {
        //ios对应2336source位
        put(1, 2336);
        //android对应2338source位
        put(2, 2338);
    }};

    public final static Map<Integer, List<Integer>> PLATFORM_2_SMALL_SOURCE = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 8354911899795413122L;

        {
            put(1, Lists.newArrayList(1964, 1892, 1893, 1894, 1895, 1896, 1981, 1982, 1983, 1984));
            put(2, Lists.newArrayList(1965, 1899, 1900, 1901, 1902, 1903, 1989, 1990, 1991, 1992));
    }};

    public final static Map<Integer, List<Integer>> PLATFORM_2_STORY_SOURCE = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 1L;

        {
            put(1, Lists.newArrayList(4353));
            put(2, Lists.newArrayList(4356));
        }};

    public final static Map<Integer, List<Integer>> PLATFORM_2_PLAYER_DETAIL_SOURCE = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 1L;

        {
            put(1, Lists.newArrayList(2030));
            put(2, Lists.newArrayList(2031));
            put(3, Lists.newArrayList(4490));
        }
    };


    public final static Map<Integer, List<Integer>> PLATFORM_2_OTT_PAUSE_SOURCE = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 2L;

        {
            put(10, Lists.newArrayList(5719));
        }
    };

    public final static Map<Integer, List<Integer>> PLATFORM_2_ZERO_SOURCE = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 2L;

        {
            put(1, Lists.newArrayList(5887));
            put(2, Lists.newArrayList(5888));
        }
    };

    public final static Map<Integer, Integer> SSA_ARCHIVE_STORY_PLATFORM_SOURCE =
            new HashMap<Integer, Integer>() {
                private static final long serialVersionUID = 1L;

                {
                    put(1, 5725);
                    put(2, 5727);
                }
            };

    //前端的设备定向代码未统一，有的时候传os（设备定向）有的时候传platformIds
    //平台到定向信息的映射关系，key为平台值或者os值，数据底表：prophet_target的mapping_content
    public final static Map<Integer, List<Integer>> PLATFORM2MAPPING = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 8354911899795413122L;

        {
        put(1,   Lists.newArrayList(4));
        put(2,   Lists.newArrayList(5));
        put(10,  Lists.newArrayList(6));
        put(398, Lists.newArrayList(4));
        put(399, Lists.newArrayList(5));
        put(812, Lists.newArrayList(6));
    }};


    //框下的定向映射map，key为平台值或者os值，映射到：prophet_target的mapping_content
    public final static Map<Integer, List<Integer>> PLATFORM_2_UNDER_BOX_TARGET = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 610975771892387405L;

        {
            put(1, Lists.newArrayList(7));
            put(2, Lists.newArrayList(8));
            put(398, Lists.newArrayList(7));
            put(399, Lists.newArrayList(8));
        }
    };

    //播放详情页定向映射map，key为平台值或者os值，映射到：prophet_target的mapping_content
    public final static Map<Integer, List<Integer>> PLATFORM_2_PLAYER_DETAIL_TARGET = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 610975771892385406L;

        {
            put(1, Lists.newArrayList(2030));
            put(2, Lists.newArrayList(2031));
            put(3, Lists.newArrayList(4490));
            put(398, Lists.newArrayList(2030));
            put(399, Lists.newArrayList(2031));
            put(421, Lists.newArrayList(4490));
        }
    };

    //小卡的定向映射map，key为平台值或者os值，映射到：prophet_target的mapping_content
    public final static Map<Integer, List<Integer>> PLATFORM_2_SMALL_CARD_TARGET = new HashMap<Integer, List<Integer>>() {
        private static final long serialVersionUID = 610975771892387406L;

        {
            put(1,    Lists.newArrayList(1892,1893,1894,1896,1981));
            put(2,    Lists.newArrayList(1899,1900,1901,1903,1989));
            put(398,  Lists.newArrayList(1892,1893,1894,1896,1981));
            put(399,  Lists.newArrayList(1899,1900,1901,1903,1989));
        }};

    //平台到定向信息的映射关系，key为平台值或者os值，数据底表：prophet_target的mapping_content
    //inline五刷
    public final static Map<Integer, List<Integer>> PLATFORM_2_FIFTH_MAPPING = new HashMap<Integer, List<Integer>>() {

        private static final long serialVersionUID = 8324516371981859438L;

        {
            put(1,   Lists.newArrayList(1896));
            put(2,   Lists.newArrayList(1903));
            put(398, Lists.newArrayList(1896));
            put(399, Lists.newArrayList(1903));
        }};


    //小卡的source(映射到：prophet_target的target_id)对应的平台
    public final static Map<Integer, Integer> PROPHET_SMALL_CARD_SOURCE_2_PLATFORM = new HashMap<Integer, Integer>() {

        private static final long serialVersionUID = 8324516371981859438L;

        {
            put(624,    1);
            put(625,    1);
            put(626,    1);
            put(627,    1);
            put(628,    1);
            put(629,    2);
            put(630,    2);
            put(631,    2);
            put(632,    2);
            put(633,    2);
        }};
    //起飞gd每个平台占总库存的比例
    public static Map<Integer, Integer> FLY_PLATFORM_IMPRESSION_RATIO = new HashMap<Integer, Integer>(){
        {
            put(1, 25);
            put(2, 75);
        }
    };

    public static final List<Integer> SHOW_TIME_SALES_TYPE = Lists
            .newArrayList(SalesType.SSA_GD_PLUS.getCode(),
                    SalesType.FLY_PRE_PAY_GD_PLUS.getCode(),
                    SalesType.FLY_PRE_PAY_GD_PLUS.getCode());

    public static final String GD_MESSAGE_KEY = ":gd:msg:";

    public static final String GD_MSG_BEGIN = "2022-01-01";

    public static final Set<Integer> VipCity = Sets.newHashSet(
            11,
            33);

    public static final Set<Long> innerAcc = Sets.newHashSet(
            26L,
            27L,
            34L
    );

    public final static Map<Integer, List<Integer>> PD_PLATFORM_2_SMALL_SOURCE = new HashMap<Integer, List<Integer>>() {

        private static final long serialVersionUID = 6725679250087133763L;

        {
            put(1, Lists.newArrayList(1891,1892,1893,1894,1895,1896,1981,1982,1983,1984));
            put(2, Lists.newArrayList(1898,1899,1900,1901,1902,1903,1989,1990,1991,1992));
            put(3, Lists.newArrayList(2003,2004,2005,2006,2007,2008,2009,2010,2011,2012));
        }};

    public Constant() {
        try {
            buildSourceMap();
        } catch (Exception e) {
            log.error("buildSourceMap hit error: ", e);
        }
    }

    private void buildSourceMap() throws IOException {
        InputStream stream = this.getClass().getClassLoader().getResourceAsStream("sourceInfo.txt");
        assert stream != null;
        LineIterator it = IOUtils.lineIterator(stream, "UTF-8");
        Splitter splitter = Splitter.on(',');
        try {
            while (it.hasNext()) {
                String line = it.nextLine();
                List<Integer> sources =
                        splitter.splitToList(line)
                                .stream()
                                .map(Integer::parseInt)
                                .collect(Collectors.toList());
                int source = sources.get(0);
                int index = sources.get(1);
                FeedSource.put(source, index);
            }
        } finally {
            LineIterator.closeQuietly(it);
        }
    }


    //Story GD的定向映射map，key为平台值或者os值，映射到：prophet_target的mapping_content
    public final static Map<Integer, List<Integer>> PLATFORM_2_STORY_TARGET = new HashMap<Integer, List<Integer>>() {
        {
            put(1,   Lists.newArrayList(4353));
            put(2,   Lists.newArrayList(4356));
            put(398, Lists.newArrayList(4353));
            put(399, Lists.newArrayList(4356));
        }};

    // 天马0刷inline新增target_id
    // https://www.tapd.cn/67874887/prong/stories/view/1167874887004395992
    public final static Map<Integer, List<Integer>> PLATFORM_2_ZERO_TARGET = new HashMap<Integer, List<Integer>>() {
        {
            put(1,   Lists.newArrayList(5887));
            put(2,   Lists.newArrayList(5888));
        }};



    //两种设备枚举值之间的映射关系(闪屏用)
    public final static Map<Integer, Integer> SSA_PLATFORM_MAP = new HashMap<Integer, Integer>() {
        {
            put(398, 1);//iphone
            put(399, 2);//android
            put(421, 3);//ipad
//            put(825, 14);//ipad_hd
            put(826, 15);//安卓pad
        }
    };



    //两种设备枚举值之间的映射关系
    //只作为映射词典辅助使用，不能作为数据源！！！，因为这个map变化频繁，和业务无关，容易出问题
    public final static Map<Integer, Integer> PLATFORM_MAP = new HashMap<Integer, Integer>() {
        {
            put(398, 1);//iphone
            put(399, 2);//android
            put(421, 3);//ipad
//            put(825, 14);//ipad_hd
            put(826, 15);//安卓pad
            put(827, 0);//web
            put(812, 10);//ott
            put(824, 10);//线上，ott闪屏---好乱
            put(981, 10);//测试，ott闪屏---好乱，幸好线上已经不会出现981的id了
        }
    };

    //827 WEB
    public final static Integer WEB_TARGET_ID = 827;

    //812 OTT
    public final static Integer OTT_TARGET_ID = 812;

    //826 安卓pad
    public final static Set<Integer> ANDROID_PAD_SET = Sets.newHashSet(826);

    //421 IPad
    //421+825，825没开放，暂时没有
    public final static Set<Integer> IPAD_SET = Sets.newHashSet(421);

    //pad 系列
    public final static Set<Integer> PAD_SET = new HashSet<Integer>(){{addAll(ANDROID_PAD_SET);addAll(IPAD_SET);}};

    //iphone和android定向
    public final static List<Integer> MOBILE_PLATFORM_TARGET = Lists.newArrayList(398, 399);


    static {
        FLY_PLATFORM_IMPRESSION_RATIO.put(1, 25);
        FLY_PLATFORM_IMPRESSION_RATIO.put(2, 75);
    }

    //闪屏城市加收
    public final static Map<Integer, String> SSA_AREA_GROUP_PRICE_CONFIG_MAP = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.SSA_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.SSA_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.SSA_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.SSA_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.SSA_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.SSA_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            //一线城市
            put(620, SystemConfigEnum.SSA_PRICE_TIER_CITY_RAISE_RATIO.getCode());
        }
    };
    //闪屏PD城市加收
    public final static Map<Integer, String> SSA_PD_AREA_GROUP_PRICE_CONFIG_MAP = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.SSA_PD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.SSA_PD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.SSA_PD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.SSA_PD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.SSA_PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.SSA_PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            //811和978都是闪屏PD可选城市
            put(811, SystemConfigEnum.SSA_PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());//闪屏pd可选城市加收，和其他城市省份加收一致
            put(978, SystemConfigEnum.SSA_PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());//闪屏pd可选城市加收，和其他城市省份加收一致
        }
    };
    //闪屏CPM城市加收
    public final static Map<Integer, String> SSA_CPM_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.SSA_CPM_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.SSA_CPM_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.SSA_CPM_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.SSA_CPM_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.SSA_CPM_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.SSA_CPM_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
        }
    };

    //TOPVIEW城市加收
    public final static Map<Integer, String> TOP_VIEW_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.TOPVIEW_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.TOPVIEW_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.TOPVIEW_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.TOPVIEW_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.TOPVIEW_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.TOPVIEW_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
        }
    };

    //GD城市加收
    public final static Map<Integer, String> GD_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
        }
    };

    @Deprecated
    //FLY_GD城市加收
    public final static Map<Integer, String> FLY_GD_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.FLY_GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.FLY_GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.FLY_GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.FLY_GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.FLY_GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.FLY_GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
        }
    };

    @Deprecated
    //OTT-GD城市加收
    public final static Map<Integer, String> OTT_GD_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.TV_GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.TV_GD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.TV_GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.TV_GD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.TV_GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.TV_GD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
        }
    };

    //PD-GD城市加收
    public final static Map<Integer, String> PD_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, String>(){
        {
            //1和976都是重点城市
            put(1, SystemConfigEnum.PD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            put(976, SystemConfigEnum.PD_PRICE_MAJOR_CITY_RAISE_RATIO.getCode());
            //2和975都是核心城市
            put(2, SystemConfigEnum.PD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            put(975, SystemConfigEnum.PD_PRICE_CORE_CITY_RAISE_RATIO.getCode());
            //3和977都是其他省市
            put(3, SystemConfigEnum.PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            put(977, SystemConfigEnum.PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());
            //974和979都是PD可选城市
            put(974, SystemConfigEnum.PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());//pd可选城市加收，和其他城市省份加收一致
            put(979, SystemConfigEnum.PD_PRICE_OTHER_CITY_RAISE_RATIO.getCode());//pd可选城市加收，和其他城市省份加收一致
        }
    };

    //订单类型和城市加收关系
    public final static Map<Integer, Map<Integer, String>> ORDER_AREA_GROUP_PRICE_CONFIG_MAP  = new HashMap<Integer, Map<Integer, String>>(){
        {
            put(0, SSA_AREA_GROUP_PRICE_CONFIG_MAP);//0初步默认所有闪屏，后续有需要分开的话再调整
            put(OrderProduct.SSA_PD.getCode(), SSA_PD_AREA_GROUP_PRICE_CONFIG_MAP);
            put(OrderProduct.SSA_CPM.getCode(), SSA_CPM_AREA_GROUP_PRICE_CONFIG_MAP);
            put(OrderProduct.TOP_VIEW_CPT_PLUS.getCode(), TOP_VIEW_AREA_GROUP_PRICE_CONFIG_MAP);
            put(OrderProduct.TOP_VIEW_GD_PLUS.getCode(), TOP_VIEW_AREA_GROUP_PRICE_CONFIG_MAP);
            put(OrderProduct.OTT_GD.getCode(), OTT_GD_AREA_GROUP_PRICE_CONFIG_MAP);//应该没有了。。。
            put(OrderProduct.FLY_GD.getCode(), FLY_GD_AREA_GROUP_PRICE_CONFIG_MAP);//应该没有了。。。
            put(OrderProduct.PD.getCode(), PD_AREA_GROUP_PRICE_CONFIG_MAP);
            put(OrderProduct.GD_CPM.getCode(), GD_AREA_GROUP_PRICE_CONFIG_MAP);
        }
    };

    public final static List<Integer> OGV_DEFAULT_PLATFORM_ID_LIST = Lists.newArrayList(
            PlatformType.WEB.getCode(),
            PlatformType.IPHONE.getCode(),
            PlatformType.ANDROID.getCode());


    public final static Map<Integer, Integer> PLAYER_DETAIL_PLATFORM_SOURCE_MAP  = new HashMap<Integer, Integer>(){
        {
            put(PlatformType.IPHONE.getCode(), 2030);
            put(PlatformType.ANDROID.getCode(), 2031);
            put(PlatformType.IPAD.getCode(), 4490);
        }
    };

    public final static Map<Integer, Integer> PLAYER_DETAIL_SOURCE_PLATFORM_MAP  = new HashMap<Integer, Integer>(){
        {
            put(2030, PlatformType.IPHONE.getCode());
            put(2031, PlatformType.ANDROID.getCode());
            put(4490, PlatformType.IPAD.getCode());
        }
    };

    //直播间右下角，卡片资源位
    public final static Map<Integer, Integer> LIVE_ROOM_RIGHT_DOWN_CARD_PLATFORM_SOURCE_MAP  = new HashMap<Integer, Integer>(){
        {
            put(PlatformType.IPHONE.getCode(), 5963);
            put(PlatformType.ANDROID.getCode(), 5965);
        }
    };

    //直播间右下角，卡片资源位
    public final static Map<Integer, Integer> LIVE_ROOM_RIGHT_DOWN_CARD_SOURCE_PLATFORM_MAP  = new HashMap<Integer, Integer>(){
        {
            put(5963, PlatformType.IPHONE.getCode());
            put(5965, PlatformType.ANDROID.getCode());
        }
    };
}
