package com.bilibili.brand.util;

import com.bilibili.brand.common.GdsOperation;
import com.bilibili.brand.schedule.GdScheduleRequest;
import com.google.common.collect.Lists;

import java.time.LocalDate;

/**
 * Created by Tianpeng Li on 2017/9/13.
 */
public class RequestUtil {

    //todo: better confirm integrity (LaunchDates)
    public static GdScheduleRequest mockPerDay(GdScheduleRequest gdScheduleRequest, LocalDate localDate, long cpms) {
        GdScheduleRequest rst = cloneRequest(gdScheduleRequest);
        rst.setLaunchDates(Lists.newArrayList(localDate));
        rst.setCpms(cpms);
        return rst;
    }

    public static GdScheduleRequest cloneRequest(GdScheduleRequest gdScheduleRequest) {
        GdScheduleRequest rst = GdScheduleRequest.builder()
                .requestId(gdScheduleRequest.getRequestId())
                .accountId(gdScheduleRequest.getAccountId())
                .scheduleId(gdScheduleRequest.getScheduleId())
                .scheduleIds(gdScheduleRequest.getScheduleIds())
                .cpms(gdScheduleRequest.getCpms())
                .sourceId(gdScheduleRequest.getSourceId())
                .slotGroupId(gdScheduleRequest.getSlotGroupId())
                .putLocation(gdScheduleRequest.getPutLocation())
                .operationType(gdScheduleRequest.getOperationType())
                .country(gdScheduleRequest.getCountry())
                .province(gdScheduleRequest.getProvince())
                .city(gdScheduleRequest.getCity())
                .age(gdScheduleRequest.getAge())
                .gender(gdScheduleRequest.getGender())
                .network(gdScheduleRequest.getNetwork())
                .os(gdScheduleRequest.getOs())
                .category(gdScheduleRequest.getCategory())
                .keyword(gdScheduleRequest.getKeyword())
                .tag(gdScheduleRequest.getTag())
                .inlineSalesType(gdScheduleRequest.getInlineSalesType())
                .launchDate(gdScheduleRequest.getLaunchDate())
                .hour(gdScheduleRequest.getHour())
                .launchDates(gdScheduleRequest.getLaunchDates())
                .orderToday(gdScheduleRequest.isOrderToday())
                .frequencyLimit(gdScheduleRequest.getFrequencyLimit())
                .build();
        return rst;
    }

    public static GdScheduleRequest genQueryRequest(int slotGroup, LocalDate localDate) {
        return GdScheduleRequest.builder()
                .requestId("bilibili")
                .scheduleId(100000000L)
                .slotGroupId(slotGroup)
                .putLocation(0)//feed
                .operationType(GdsOperation.QUERY.getOperation())
                .launchDate(Lists.newArrayList(TimeUtil.localDateToBasicStr(localDate)))
                .build();
    }

}
