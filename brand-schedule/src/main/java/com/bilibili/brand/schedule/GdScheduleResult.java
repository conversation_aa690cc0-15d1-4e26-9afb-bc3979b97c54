package com.bilibili.brand.schedule;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class GdScheduleResult {

    /**
     * 0-ok, -1-库存不足 -2
     */
    private Integer status = 0;

    private Long cpms;

    private Map<LocalDate, Long> detail;

    public GdScheduleResult(Integer status, Long cpms) {
        this.status = status;
        this.cpms = cpms;
    }
}
