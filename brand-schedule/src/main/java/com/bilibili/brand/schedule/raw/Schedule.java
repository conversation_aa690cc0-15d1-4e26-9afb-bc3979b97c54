package com.bilibili.brand.schedule.raw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

/**
 * Created by Tianpeng Li on 2017/6/27.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Schedule {

    private long id;

    private long accountId;

    private long campaignId;

    //aggregated targets.
    private Target target = new Target();

    private Set<LocalDate> dates = new HashSet<>(50);

    //source-allocation
    private Map<Integer, Allocation> allocations = new HashMap<>(50);

    private int useSlot;

    private int location;

    private List<Integer> locations;

    //time the schedule is created.
    private LocalDateTime ctime = LocalDateTime.now();

    private boolean orderToday = false;

    private Integer frequencyLimit;

//    public Schedule(Schedule schedule) {
//        id = schedule.getId();
//        accountId = schedule.getAccountId();
//        campaignId = schedule.getCampaignId();
//        target = schedule.getTarget();
//        dates = schedule.getDates();
//        useSlot = schedule.getUseSlot();
//        location = schedule.getLocation();
//        ctime = schedule.getCtime();
//        orderToday = schedule.isOrderToday();
//        for (Map.Entry<Integer, Allocation> entry : schedule.getAllocations().entrySet()) {
//            allocations.put(entry.getKey(), entry.getValue());
//        }
//        frequencyLimit = schedule.getFrequencyLimit();
//    }

}
