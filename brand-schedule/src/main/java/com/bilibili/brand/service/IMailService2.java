package com.bilibili.brand.service;

/**
 * Created by user on 2017/3/31.
 */
public interface IMailService2 {

    /**
     * Send emails without attachment.
     *
     * @param subject The subject of the email.
     * @param message The message content of the email.
     * @param toList The list of email addresses the email being send to.
     * @return Whether it is successful or not.
     */
    boolean sendMailWithoutAttachment(String subject, String message, String[] toList);

}
