from rediscluster import StrictRedisCluster

redis_nodes = [{'host': '*************', 'port': 6832},
               {'host': '*************', 'port': 6829},
               {'host': '*************', 'port': 6833}
               ]

redisconn = StrictRedisCluster(startup_nodes=redis_nodes)


def sum_invent(src, dt):
    key = str(src) + "_" + dt
    print key
    total = redisconn.hgetall(key)
    sum_target = 0
    for key in total:
        sum_target += int(total[key])
    print sum_target


cmd = ''
while cmd is not None:
    cmd = raw_input("Please Input Your Cmd: ")
    param = cmd.split(' ')
    if len(param) != 3:
        print "Input Not Valid"
        continue
    src = param[1]
    date = param[2]
    sum_invent(src, date)

# sources = [1898, ]
# dates = ["2017-09-08"]


# for src in sources:
#     for dt in dates:
#         key = str(src) + "_" + dt
#         print key
#         total = redisconn.hgetall(key)
#         sum_target = 0
#         for key in total:
#             sum_target += int(total[key])
#         print sum_target

