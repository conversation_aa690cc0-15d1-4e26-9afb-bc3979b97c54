<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                http://www.springframework.org/schema/beans/spring-beans.xsd
                http://www.springframework.org/schema/context
                http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:component-scan base-package="com.bilibili.brand" />

    <bean id="configMgrBean" class="com.bilibili.config.ConfigInit" destroy-method="destroy"></bean>

    <context:property-placeholder location="classpath:spring-redis-template.properties" ignore-unresolvable="true"/>
    <context:property-placeholder location="classpath:database.properties" ignore-unresolvable="true"/>
    <context:property-placeholder location="classpath:es.properties" ignore-unresolvable="true"/>

    <!--<bean id="propertyConfigurer"
          class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="locations">
            <list>
                <value>classpath:/spring-redis-template.properties</value>
                <value>classpath:/database.properties</value>
            </list>
        </property>
        <property name="ignoreUnresolvablePlaceholders" value="true" />
    </bean>-->

    <import resource="classpath:brand-schedule.xml"/>
</beans>