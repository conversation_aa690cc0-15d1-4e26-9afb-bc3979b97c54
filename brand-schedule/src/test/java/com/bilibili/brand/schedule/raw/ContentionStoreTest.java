package com.bilibili.brand.schedule.raw;

import com.bilibili.brand.AbstractMockitoTest;
import com.bilibili.brand.schedule.GdScheduleRequest;
import com.bilibili.brand.schedule.data.ScheduleDataManager;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDate;
import java.util.*;


/**
 * Created by dailuwei on 2020/3/11 11:17
 *
 * <AUTHOR>
 */
public class ContentionStoreTest extends AbstractMockitoTest {
    @Mock
    private DirectoryStore directoryStore;

    @Mock
    private ScheduleDataManager scheduleDataManager;

    @InjectMocks
    public ContentionStore contentionStore;

    @Test
    public void loadSchedule() {
        contentionStore.loadSchedule(1L, Sets.newHashSet(1),
                Sets.newHashSet(1), Sets.newHashSet(LocalDate.ofYearDay(2020, 20)),
                true, GdScheduleRequest.builder().requestId("515").build(),null);
    }

    @Test
    public void writeBack() {
        Allocation allocation = new Allocation();
        allocation.setScheduleId(1);
        Target target = new Target();
        allocation.setTarget(target);
        allocation.setLocation(1898);
        allocation.setCampaignId(1l);
        allocation.setDate(LocalDate.ofYearDay(2020, 1));
        allocation.setAccountId(1);
        Map<Target, Long> targetCount = new HashMap<>();
        targetCount.put(target,100l);
        allocation.setTargetCount(targetCount);
        Map<LocalDate, InlineUse> inlineUseMap = new HashMap<>();
        inlineUseMap.put(LocalDate.ofYearDay(2020, 1), InlineUse.builder().normalUse(0L).firstUse(0L).build());
        contentionStore.writeBack(Lists.newArrayList(allocation), Lists.newArrayList(allocation),
                Lists.newArrayList(allocation), Sets.newHashSet(182), false,
                inlineUseMap);
    }

}