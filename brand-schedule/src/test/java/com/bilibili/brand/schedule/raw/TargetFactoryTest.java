package com.bilibili.brand.schedule.raw;

import com.bilibili.brand.AbstractMockitoTest;
import com.bilibili.brand.common.JsonManager;
import com.bilibili.brand.schedule.GdScheduleRequest;
import com.bilibili.brand.schedule.data.ScheduleDataManager;
import com.bilibili.brand.schedule.data.ScheduleTargetGroupInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;


/**
 * Created by dailuwei on 2020/3/11 11:17
 *
 * <AUTHOR>
 */
public class TargetFactoryTest extends AbstractMockitoTest {
    @Mock
    private JsonManager jsonManager;

    @InjectMocks
    public TargetFactory targetFactory;

    @Test
    public void genAllTargets() {
        targetFactory.genAllTargets();
    }

    @Test
    public void genEsRequest() {
        Target target = new Target();
        TargetFactory.genEsRequest(target, 1981, LocalDate.ofYearDay(2020,7));
    }

    @Test
    public void loadProvinceMapping() {
        targetFactory.loadProvinceMapping();
    }

}