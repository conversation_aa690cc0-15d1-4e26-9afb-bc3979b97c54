package com.bilibili.brand.schedule.raw;

import com.bilibili.brand.AbstractMockitoTest;
import com.bilibili.brand.common.Constant;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by da<PERSON><PERSON><PERSON> on 2020/3/11 11:17
 *
 * <AUTHOR>
 */
public class ConstantTest{
        //extends AbstractMockitoTest {

    @InjectMocks
    public Constant constant;

    @Test
    public void constant() {
        Constant constant = new Constant();
    }

    @Test
    public void testReplace(){

        List<String> list = Lists.newArrayList("23422NBSP", "huahua adsdfa", "huahua ").stream()
                .map(it -> StringUtils.replace(it, " ", " ").trim())
                //.map(it -> it.trim())
                .collect(Collectors.toList());
        System.out.println("d");
    }


}