package com.bilibili.brand.furion.biz.common;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/11/23.
 */
public enum EsType {
    TYPE_SHOW_FLOW(EsIndex.INDEX_SHOW_FLOW, EsConst.TYPE_SHOW_FLOW),
    TYPE_HOUR_SHOW_FLOW(EsIndex.INDEX_HOUR_SHOW_FLOW, EsConst.TYPE_HOUR_SHOW_FLOW);
    private EsIndex index;
    private String type;

    EsType(EsIndex index, String type) {
        this.index = index;
        this.type = type;
    }

    public EsIndex getIndex() {
        return index;
    }

    public String getType() {
        return type;
    }
}
