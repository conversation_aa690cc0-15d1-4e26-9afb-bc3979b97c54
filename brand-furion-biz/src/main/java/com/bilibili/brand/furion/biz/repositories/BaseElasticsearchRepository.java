package com.bilibili.brand.furion.biz.repositories;

import com.bilibili.brand.furion.biz.common.EsIndex;
import com.bilibili.brand.furion.biz.common.EsType;
import org.elasticsearch.action.search.MultiSearchRequestBuilder;
import org.elasticsearch.action.search.MultiSearchResponse;
import org.elasticsearch.action.search.SearchRequestBuilder;
import org.elasticsearch.client.Requests;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.repository.support.AbstractElasticsearchRepository;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by fanwenbin on 2019/6/28.
 */
@Component
public class BaseElasticsearchRepository<T, ID> extends AbstractElasticsearchRepository {
    protected final Logger log = LoggerFactory.getLogger(this.getClass());
    @Autowired
    protected ElasticsearchTemplate template;

    public <S extends T> List<S> save(List<S> entities, EsIndex esIndex, String suffixDateField, EsType type) {
        Assert.notNull(entities, "Cannot insert \'null\' as a List.");
        Assert.notEmpty(entities, "Cannot insert empty List.");
        ArrayList queries = new ArrayList();
        Iterator var3 = entities.iterator();
        Set<String> indexNameSet = new HashSet<>();
        while (var3.hasNext()) {
            Object s = var3.next();
            String indexName = this.buildIndexName(esIndex.getName(), suffixDateField, s);
            indexNameSet.add(indexName);
            queries.add(this.createIndexQuery((T) s, indexName, type.getType()));
        }

        template.bulkIndex(queries);
        String[] indexNames = new String[indexNameSet.size()];
        indexNameSet.toArray(indexNames);
        this.refresh(indexNames);
        return entities;
    }

    public void refresh(String[] indexNames) {
        Assert.notNull(indexNames, "No index defined for refresh()");
        template.getClient().admin().indices().refresh(Requests.refreshRequest(indexNames)).actionGet();
    }


    private String buildIndexName(String prefix, String suffixField, Object t) {
        if (StringUtils.isEmpty(suffixField)) {
            return prefix;
        }
        try {
            Field field = t.getClass().getDeclaredField(suffixField);
            field.setAccessible(true);
            Timestamp timestamp = (Timestamp) field.get(t);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy_MM_dd");
            return prefix + "_" + sdf.format(timestamp);
        } catch (Exception e) {
            log.error("buildIndexName.error", e);
            return null;
        }
    }

    private IndexQuery createIndexQuery(T entity, String indexName, String type) {
        IndexQuery query = new IndexQuery();
        query.setObject(entity);
        query.setId(getId(entity));
        query.setVersion(this.extractVersionFromBean(entity));
        query.setParentId(this.extractParentIdFromBean(entity));
        query.setIndexName(indexName);
        query.setType(type);
        return query;
    }

    private String getId(T t) {
        Field[] fields = t.getClass().getFields();
        for (Field field : fields) {
            if (field.isAnnotationPresent(org.springframework.data.annotation.Id.class)) {
                field.setAccessible(true);
                try {
                    return String.valueOf(field.get(t));
                } catch (IllegalAccessException e) {
                    return null;
                }
            }
        }
        return null;
    }

    private Long extractVersionFromBean(T entity) {
        return this.entityInformation != null ? this.entityInformation.getVersion(entity) : null;
    }

    private String extractParentIdFromBean(T entity) {
        return this.entityInformation != null ? this.entityInformation.getParentId(entity) : null;
    }

    public List<MultiSearchResponse.Item> multiSearch(List<SearchRequestBuilder> searchRequestBuilders) {
        if (CollectionUtils.isEmpty(searchRequestBuilders)) return Collections.emptyList();
        MultiSearchRequestBuilder prepareMultiSearch = template.getClient().prepareMultiSearch();
        searchRequestBuilders.forEach(prepareMultiSearch::add);
        MultiSearchResponse multiSearchResponse = prepareMultiSearch.execute().actionGet();
        if (null == multiSearchResponse) return Collections.emptyList();
        return Arrays.asList(multiSearchResponse.getResponses());
    }

    @Override
    protected String stringIdRepresentation(Serializable serializable) {
        return String.valueOf(serializable);
    }
}
