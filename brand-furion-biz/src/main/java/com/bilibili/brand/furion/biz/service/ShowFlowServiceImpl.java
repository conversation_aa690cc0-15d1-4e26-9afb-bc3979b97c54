package com.bilibili.brand.furion.biz.service;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.brand.furion.api.dto.HourShowFlowDto;
import com.bilibili.brand.furion.api.dto.ShowFlowDto;
import com.bilibili.brand.furion.api.dto.ShowFlowSumValueDto;
import com.bilibili.brand.furion.api.query.ShowFlowQueryBuilder;
import com.bilibili.brand.furion.api.service.IShowFlowService;
import com.bilibili.brand.furion.biz.common.EsIndex;
import com.bilibili.brand.furion.biz.common.EsType;
import com.bilibili.brand.furion.biz.common.FurionBizExceptionCode;
import com.bilibili.brand.furion.biz.common.GroupSumBean;
import com.bilibili.brand.furion.biz.handler.ShowFlowHandler;
import com.bilibili.brand.furion.biz.po.ShowFlowPo;
import com.bilibili.brand.furion.biz.query.HourShowFlowQuery;
import com.bilibili.brand.furion.biz.query.HourShowFlowResult;
import com.bilibili.brand.furion.biz.query.ShowFlowQuery;
import com.bilibili.brand.furion.biz.repositories.ShowFlowRepository;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2019/6/28.
 */
@Service
public class ShowFlowServiceImpl implements IShowFlowService{
    @Autowired
    private ShowFlowHandler showFlowHandler;
    @Autowired
    private ShowFlowRepository repository;

    @Override
    public List<ShowFlowSumValueDto> sumCpmCount(ShowFlowQueryBuilder builder) {
        this.validateBuilder(builder);
        ShowFlowQuery query = new ShowFlowQuery();
        BeanUtils.copyProperties(builder, query);
        List<GroupSumBean> beanList = showFlowHandler.sumCpmCount(query);
        List<ShowFlowSumValueDto> list = new ArrayList<>(beanList.size());
        list.addAll(beanList.stream().map(groupSumBean -> ShowFlowSumValueDto.builder().key(groupSumBean.getKey()).sumValue(groupSumBean.getValue()).build()).collect(Collectors.toList()));
        return list;
    }

    @Override
    public List<List<ShowFlowSumValueDto>> multiSumCpmCount(List<ShowFlowQueryBuilder> builders) {
        Assert.notEmpty(builders, "查询参数不可为空");
        for (ShowFlowQueryBuilder builder : builders) {
            this.validateBuilder(builder);
        }
        List<ShowFlowQuery> queries = new ArrayList<>(builders.size());
        for (ShowFlowQueryBuilder builder : builders) {
            ShowFlowQuery query = new ShowFlowQuery();
            BeanUtils.copyProperties(builder, query);
            queries.add(query);
        }
        List<List<GroupSumBean>> groupList = showFlowHandler.multiSumCpmCount(queries);
        List<List<ShowFlowSumValueDto>> result = new ArrayList<>(groupList.size());
        for (List<GroupSumBean> beanList : groupList) {
            List<ShowFlowSumValueDto> list = new ArrayList<>(beanList.size());
            list.addAll(beanList.stream().map(groupSumBean -> ShowFlowSumValueDto.builder().key(groupSumBean.getKey()).sumValue(groupSumBean.getValue()).build()).collect(Collectors.toList()));
            result.add(list);
        }
        return result;
    }

    private void validateBuilder(ShowFlowQueryBuilder builder) {
        Assert.notNull(builder, "查询参数不可为空");
        Assert.notNull(builder.getFromTime(), "查询开始时间不可为空");
        Assert.notNull(builder.getToTime(), "查询结束时间不可为空");
    }

    @Override
    public void batchInsert(Integer tableIndex, List<ShowFlowDto> dtoList) throws ServiceException {
        if (CollectionUtils.isEmpty(dtoList) || null == tableIndex) {
            throw new ServiceException(FurionBizExceptionCode.REQUIRED_PARAM);
        }
        tableIndex = 1 + tableIndex;
        List<ShowFlowPo> pos = new ArrayList<>(dtoList.size());
        for (ShowFlowDto dto : dtoList) {
            pos.add(this.getPoByDto(tableIndex, dto));
        }
        repository.save(pos, EsIndex.INDEX_SHOW_FLOW, "date", EsType.TYPE_SHOW_FLOW);
    }

    @Override
    public List<HourShowFlowDto> getHourShowFlow(Integer sourceId, LocalDate date) {
        List<HourShowFlowResult> hourShowFlowResultList = showFlowHandler.getHourShowFlowResultList(HourShowFlowQuery.builder()
                .date(date)
                .source(Arrays.asList(sourceId))
                .build());

        return CollectionUtils.isEmpty(hourShowFlowResultList) ? Collections.emptyList() :
                hourShowFlowResultList.stream().map(hourShowFlowResult -> {
                    HourShowFlowDto hourShowFlowDto = new HourShowFlowDto();
                    BeanUtils.copyProperties(hourShowFlowResult, hourShowFlowDto);
                    return hourShowFlowDto;
                }).collect(Collectors.toList());
    }

    private ShowFlowPo getPoByDto(Integer tableIndex, ShowFlowDto dto) {
        return ShowFlowPo.builder()
                .key(tableIndex.toString() +"_"+ dto.getDbKey())
                .country(dto.getCountry())
                .province(dto.getProvince())
                .city(dto.getCity())
                .network(dto.getNetwork())
                .os(dto.getOs())
                .source(dto.getSource())
                .gender(dto.getGender())
                .age(dto.getAge())
                .category(dto.getCategory())
                .keyword(dto.getKeyword())
                .tag(dto.getTag())
                .hour(dto.getHour())
                .count(dto.getCount())
                .isActual(dto.getIsActual())
                .date(dto.getDate())
                .ctime(dto.getCtime())
                .mtime(dto.getMtime())
                .build();
    }
}
