package com.bilibili.brand.furion.biz.common;

import com.bilibili.adp.common.exception.IExceptionCode;

/**
 * Created by <PERSON><PERSON><PERSON> on 2016/11/23.
 */
public enum FurionBizExceptionCode implements IExceptionCode {
    REQUIRED_PARAM(90001, "参数不为空");

    private int code;
    private String message;

    private FurionBizExceptionCode(int code, String message) {

        this.code = code;
        this.message = message;
    }


    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
