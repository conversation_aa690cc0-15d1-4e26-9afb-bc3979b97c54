CREATE TABLE `gd_schedule` (
  `schedule_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'GD排期ID',
  `name` varchar(64) NOT NULL DEFAULT '0' COMMENT '排期名称',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单ID',
  `account_id` int(11) NOT NULL DEFAULT '0' COMMENT '账号ID',
  `campaign_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划ID',
  `slot_id` int(11) NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `complemented_id` int(11) NOT NULL DEFAULT '0' COMMENT '补量目标排期ID',
  `schedule_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0-新建  1-补量',
  `is_complete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否补量完成: 0-否  1-是',
  `cost_price` int(11) NOT NULL DEFAULT '0' COMMENT '刊例单价',
  `total_impression` int(11) NOT NULL DEFAULT '0' COMMENT '目标展现量',
  `promotion_purpose_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '投放目的 APP 1:APP推广,LANDING_PAGE 2:落地页, VIDEO 3:视频页',
  `speed_mode` tinyint(4) NOT NULL DEFAULT '0' COMMENT '投放目的 SMOOTH 1:匀速 ,SPEED_UP 2:加速',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态 1-有效 2-无效 3-用户删除',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除 0-有效 1-删除',
  PRIMARY KEY (`schedule_id`),
  UNIQUE KEY `uk_campaign_id` (`campaign_id`),
  KEY `ix_order_id_ctime` (`order_id`,`ctime`),
  KEY `ix_account_id_ctime` (`account_id`,`ctime`),
  KEY `ix_complemented_id` (`complemented_id`),
  KEY `ix_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='GD排期表';

CREATE TABLE `gd_schedule_target` (
  `schedule_target_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `schedule_id` int(11) NOT NULL DEFAULT '0' COMMENT 'GD排期ID',
  `campaign_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划ID, gd_schedule.campaign_id',
  `target_type` int(11) NOT NULL DEFAULT '0' COMMENT '定向类型',
  `target_item_ids` varchar(255) NOT NULL DEFAULT '' COMMENT '定向项ID列表, 逗号分隔',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除 0-有效 1-删除',
  PRIMARY KEY (`schedule_target_id`),
  UNIQUE KEY `uk_schedule_id_target_type` (`schedule_id`,`target_type`),
  KEY `ix_campaign_id` (`campaign_id`),
  KEY `ix_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='GD排期定向信息';

CREATE TABLE `gd_schedule_date` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `schedule_id` int(11) NOT NULL DEFAULT '0' COMMENT '排期ID',
  `campaign_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划id',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
  `account_id` int(11) NOT NULL DEFAULT '0' COMMENT '账户id',
  `schedule_date` date NOT NULL COMMENT '排期日期YYYY-MM-DD',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除0否 1是',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`),
  KEY `ix_schedule_id` (`schedule_id`),
  KEY `ix_campaign_id` (`campaign_id`),
  KEY `ix_order_id` (`order_id`),
  KEY `ix_account_id` (`account_id`),
  KEY `ix_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='排期时间表';

CREATE TABLE `gd_schedule_target_ratio` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增',
  `plan_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划ID',
  `source` int(11) NOT NULL DEFAULT '0' COMMENT '资源id',
  `target_key_id` int(11) NOT NULL DEFAULT '0' COMMENT '定向',
  `ratio` int(11) NOT NULL DEFAULT '0' COMMENT '比率',
  `launch_day` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '时间序列,如2016-10-26 00:00:00',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除，0是有效，1是删除',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_target_key_id_source` (`plan_id`,`target_key_id`,`launch_day`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='GD排期定向条件表';

ALTER TABLE `fc_order`
ADD COLUMN `product` TINYINT(4) NOT NULL DEFAULT 0 COMMENT '0 CPM 1 CPC 2 CPT 3 GD-CPT' AFTER `is_deleted`;

ALTER TABLE lau_campaign
ADD COLUMN `sales_type` tinyint(4) NOT NULL DEFAULT '11' COMMENT '售卖类型 11-CPM, 21-GD',
ADD COLUMN `order_id` int(11) NOT NULL DEFAULT '0' COMMENT 'fc_order.id GD订单ID, 对GD广告有效';

ALTER TABLE lau_unit
ADD COLUMN `sales_type` tinyint(4) NOT NULL DEFAULT '11' COMMENT '售卖类型 11-CPM, 21-GD',
ADD COLUMN `slot_id` int(11) NOT NULL DEFAULT '0' COMMENT '广告位ID res_slot.slot_id, 对GD广告有效';

ALTER TABLE lau_unit_creative
ADD COLUMN `sales_type` tinyint(4) NOT NULL DEFAULT '11' COMMENT '售卖类型 11-CPM, 21-GD',
ADD COLUMN `order_id` int(11) NOT NULL DEFAULT '0' COMMENT 'fc_order.id GD订单ID, 对GD广告有效';

CREATE TABLE `lau_unit_date` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `unit_id` int(11) NOT NULL DEFAULT '0' COMMENT '单元ID',
  `campaign_id` int(11) NOT NULL DEFAULT '0' COMMENT '计划id',
  `order_id` int(11) NOT NULL DEFAULT '0' COMMENT '订单id',
  `account_id` int(11) NOT NULL DEFAULT '0' COMMENT '账户id',
  `unit_date` date NOT NULL COMMENT '单元投放日期YYYY-MM-DD',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除0否 1是',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`),
  KEY `ix_unit_id` (`unit_id`),
  KEY `ix_campaign_id` (`campaign_id`),
  KEY `ix_order_id` (`order_id`),
  KEY `ix_account_id` (`account_id`),
  KEY `ix_ctime` (`ctime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='单元投放时间表';

CREATE TABLE `res_gd_price` (
  `price_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '价格ID',
  `slot_id` int(11) NOT NULL DEFAULT '0' COMMENT '广告位ID',
  `target_item_id` int(11) NOT NULL DEFAULT '0' COMMENT '定向ID',
  `target_type` int(11) NOT NULL DEFAULT '0' COMMENT '定向类型',
  `price` int(11) NOT NULL DEFAULT '0' COMMENT '价格（单位分）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态（1-有效，2-无效）',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除，0是有效，1是删除',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`price_id`),
  UNIQUE KEY `uk_slot_id_target_item_id` (`slot_id`,`target_type`,`target_item_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='GD定价';

CREATE TABLE `rpc_request_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `method_name` varchar(16) NOT NULL DEFAULT '' COMMENT '方法名',
  `method_parameter` varchar(512) NOT NULL DEFAULT '' COMMENT '方法参数：格式jsonobject',
  `request_count` int(11) NOT NULL DEFAULT '1' COMMENT '请求次数',
  `request_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '请求状态-1fail  1success',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `ix_ctime` (`ctime`),
  KEY `ix_method_name` (`method_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='rpc请求记录';

CREATE TABLE `system_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `item` varchar(32) NOT NULL DEFAULT '' COMMENT '配置项',
  `value` varchar(128) NOT NULL DEFAULT '' COMMENT '配置项的值',
  `desc` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '软删除，0是有效，1是删除',
  `ctime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `mtime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '变更时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item` (`item`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统配置表';

INSERT INTO `system_config` (`item`, `value`, `desc`, `is_deleted`, `ctime`, `mtime`) VALUES ( 'GD_SCHEDULE_MIN_STEP_SIZE', '2', 'GD排期最小步长', '0', '2016-12-30 10:57:17', '2017-01-03 16:02:38');
INSERT INTO `system_config` (`item`, `value`, `desc`, `is_deleted`, `ctime`, `mtime`) VALUES ( 'GD_SCHEDULE_MAX_STEP_SIZE', '15', 'GD排期最大步长', '0', '2016-12-30 10:57:34', '2017-01-03 16:02:38');
INSERT INTO `system_config` (`item`, `value`, `desc`, `is_deleted`, `ctime`, `mtime`) VALUES ( 'GD_TARGET_CONFIG', '[1,2,3,4,5,6]', 'GD定向条件配置', '0', '2016-12-30 10:57:52', '2017-01-03 16:02:38');

UPDATE res_target SET name='设备定向' WHERE id=4;
UPDATE res_target SET name='频道定向' WHERE id=6;