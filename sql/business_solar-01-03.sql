alter table schedule
add column gd_id int(11) not null default 0 comment 'gd关联id',
ADD INDEX `ix_gd_id` (`gd_id`);

begin;

-- GD审核管理

set @catid = (select id from auth_item where name='CPM审核管理' and `type`=2 and rule_id=3);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD查看创意列表',1,'', 3, 'cpm_gd_view_creative_list', now(), now());
set @pointid = last_insert_id();

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD审核创意',1,'', 3, 'cpm_gd_audit_creative', now(), now());
set @pointid = last_insert_id(); 

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

-- GD排期配置管理

set @catid = (select id from auth_item where name='CPM资源管理' and `type`=2 and rule_id=3);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD查询排期配置',1,'', 3, 'cpm_gd_view_schedule_config', now(), now());
set @pointid = last_insert_id();

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD启用/暂停定向',1,'', 3, 'cpm_gd_enable_disable_target', now(), now());
set @pointid = last_insert_id(); 

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD编辑排期步长',1,'', 3, 'cpm_gd_modify_schedule_step', now(), now());

set @pointid = last_insert_id(); 
insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

-- GD广告位管理
INSERT into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD广告位管理', 2, '', 3, '', now(), now());
set @catid = last_insert_id();

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD查看广告位基价',1,'', 3, 'cpm_gd_view_slot_base_price', now(), now());
set @pointid = last_insert_id();

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

insert into auth_item(name, type, description, rule_id, data, ctime, mtime) VALUES ('CPM_GD编辑广告位基价',1,'', 3, 'cpm_gd_modify_slot_base_price', now(), now());
set @pointid = last_insert_id(); 

insert into auth_item_child(parent, child) VALUES (@catid, @pointid);

commit;